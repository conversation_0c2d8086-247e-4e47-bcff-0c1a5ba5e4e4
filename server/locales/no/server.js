const translations = {
  common: {
    thinking: "Tenker...",
  },
  errors: {
    ai: {
      ollama: {
        "connection-failed":
          "Din Ollama-instans kunne ikke nås eller svarer ikke. Sørg for at API-serveren kjører og at tilkoblingsinformasjonen din er korrekt.",
        "empty-response": "Ollama tekstsvar var tomt.",
        "stream-failed": "Kunne ikke strømme chatten. {{message}}",
        "no-endpoint": "Ingen Ollama API-endepunkt er satt.",
        "no-token-limit": "Ingen Ollama-token-kontekstgrense er satt.",
        "provider-not-responding":
          "Den lokale AI-leverandøren svarer ikke, enten fordi serveren er offline eller overbelastet.",
        "provider-unavailable":
          "Den lokale AI-serveren er for øyeblikket utilgjengelig. Vennligst sjekk installasjonen og sørg for at den kjører og ikke er overbelastet.",
      },
      azure: {
        "no-endpoint": "Ingen Azure API-endepunkt er satt.",
        "no-key": "Ingen Azure API-nøkkel er satt.",
        "no-model": "Ingen Azure OpenAI-modell er satt.",
        "no-model-pref":
          "Ingen OPEN_MODEL_PREF ENV definert. Dette må være navnet på en distribusjon på din Azure-konto for en LLM-chattemodell som GPT-3.5.",
      },
      openai: {
        "no-key": "Ingen OpenAI API-nøkkel er satt.",
      },
      mistral: {
        "no-key": "Ingen Mistral API-nøkkel er satt.",
      },
      litellm: {
        "no-base-path": "LiteLLM må ha en gyldig basesti for å bruke API-et.",
        "no-model": "LiteLLM må ha en gyldig modell satt.",
        "no-token-limit": "Ingen token-kontekstgrense er satt.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI må ha en gyldig basesti for å bruke API-et.",
        "no-token-limit": "Ingen token-kontekstgrense er satt.",
      },
      localai: {
        "no-token-limit": "Ingen LocalAi-token-kontekstgrense er satt.",
      },
      lmstudio: {
        "no-token-limit": "Ingen LMStudio-token-kontekstgrense er satt.",
      },
      fireworks: {
        "invalid-model":
          "FireworksAI chat: {{model}} er ikke gyldig for chattkomplettering!",
      },
      context: {
        header: "Kilde(r)",
      },
    },
    system: {
      "ssl-boot-failed":
        "SSL-oppstart mislyktes: {{message}} - går tilbake til HTTP-oppstart.",
      "no-app": 'Ingen "app" definert - krasjer!',
      validation: {
        "invalid-transcription-provider":
          "{{input}} er ikke en gyldig transkripsjonmodell-leverandør.",
        "invalid-safety-setting":
          "Ugyldig sikkerhetsinnstilling. Må være en av {{modes}}.",
        "invalid-model-type": "Ugyldig modelltype. Må være en av {{models}}.",
        "invalid-embedding-model":
          "Ugyldig innbyggingsmodelltype. Må være en av {{supported}}.",
      },
    },
    document: {
      "sync-failed":
        "Kunne ikke synkronisere dokument {{filename}} etter {{attempts}} forsøk.",
      "invalid-metadata":
        "Dokument {{filename}} har ugyldig eller manglende metadata.",
      removed:
        "Dokument {{filename}} har blitt fjernet fra settet med overvåkede dokumenter.",
    },
    validation: {
      "value-not-number": "Verdien er ikke et tall.",
      "value-must-be-nonzero": "Verdien må være forskjellig fra null.",
      "value-cannot-be-negative": "Verdien kan ikke være mindre enn 0.",
      "invalid-serp-provider": "Ugyldig SERP-leverandør.",
      "max-rerank-limit-min": "Maksimal rerangering grense må være minst 10.",
      "max-rerank-limit-max":
        "Maksimal rerangering grense kan ikke overstige 200.",
    },
    embedding: {
      "lmstudio-failed": "LMStudio-innbygging mislyktes: {{errors}}",
      "jina-failed": "Jina-innbygging mislyktes: {{errors}}",
    },
    env: {
      "jina-api-key-error": "Jina API-nøkkel må starte med jina_",
      "empty-value": "Verdien kan ikke være tom",
      "non-zero-required": "Verdien må være større enn null",
      "must-be-number": "Verdien må være et tall",
      "invalid-url": "URL-en er ikke en gyldig URL",
      "openai-key-format": "OpenAI-nøkkelen må starte med sk-",
      "contextual-prompt-format":
        "Kontekstuell brukerforespørsel må inneholde {file} & {chunk}",
      "anthropic-key-format": "Anthropic-nøkkelen må starte med sk-ant-",
      "external-llm-url-format": "URL-en må inneholde /v1",
      "url-no-trailing-slash": "URL-en kan ikke slutte med en skråstrek",
      "invalid-tts-provider": "{{input}} er ikke en gyldig TTS-leverandør",
      "invalid-whisper-model":
        "{{input}} er ikke et gyldig valg av Whisper-modell",
      "invalid-llm-provider": "{{input}} er ikke en gyldig LLM-leverandør",
    },
    // Workspace sharing errors
    workspace: {
      onlyOwnerShare: "Kun arbeidsområdeeiere kan dele arbeidsområder",
      onlyOwnerRevoke:
        "Kun arbeidsområdeeiere kan fjerne deling av arbeidsområder",
      shareFailed: "Kunne ikke dele arbeidsområdet",
      revokeFailed: "Kunne ikke fjerne deling av arbeidsområdet",
      getStatusFailed: "Kunne ikke hente delingsstatus for arbeidsområdet",
    },
    // Thread sharing errors
    thread: {
      onlyOwnerShare: "Kun trådeiere kan dele tråder",
      onlyOwnerRevoke: "Kun trådeiere kan fjerne deling av tråder",
      shareFailed: "Kunne ikke dele tråden",
      revokeFailed: "Kunne ikke fjerne deling av tråden",
      getStatusFailed: "Kunne ikke hente delingsstatus for tråden",
    },
    auth: {
      "authentication-required": "Autentisering påkrevd",
      "insufficient-permissions": "Utilstrekkelige tillatelser",
    },
  },
  validation: {
    responseHeader: "Generert Svar",
    contextHeader: "Opprinnelig Kontekst og Kilder",
    content: {
      "user-question-sources-header": "BRUKERSPØRSMÅL OG KILDER",
      "system-response-header": "SYSTEMSVAR",
      "not-provided": "Ikke oppgitt",
    },
  },
  manualWorkEstimator: {
    content: {
      "user-question-header": "BRUKERSPØRSMÅL",
      "system-response-header": "SYSTEMSVAR",
    },
  },
  notifications: {
    document: {
      invalid:
        "Dokument {{filename}} har ingen metadata, er ødelagt eller ugyldig og har blitt fjernet.",
      removed:
        "Dokument {{filename}} har blitt fjernet fra settet med overvåkede dokumenter.",
    },
  },
  docx: {
    title: "Chat-eksport",
    exportedOn: "Eksportert",
    downloadedOn: "Nedlastet",
    keywords: "chat, eksport",
    description: "Eksportert chat-melding",
    tokenCount: "Antall tokens",
    canvasDocumentTitle: "Canvas-dokument",
    errors: {
      contentRequired: "Innhold er påkrevd",
      writingFile: "Feil ved skriving av midlertidig fil",
      generating: "Feil ved generering av dokument",
    },
  },
  deep_search: {
    instruction:
      "Hvis noen DeepSearch-kilder er relevante for brukerforespørselen og brukt i svaret, oppgi klikkbare lenker til disse kildene på slutten av meldingen i nøyaktig dette formatet: 'Kilder: [Kildetittel 1](URL1), [Kildetittel 2](URL2)'. Den klikkbare teksten for hver lenke må være en kort beskrivelse av kildens innhold (f.eks. sidetittel eller sammendrag). Referer aldri til DeepSearch-kilder med tall i teksten (som [1] eller [Kilde 1]); henvis alltid til kilden direkte med dens navn eller innhold. Ikke bruk akademiske siteringsformater.",
  },
};

module.exports = translations;
