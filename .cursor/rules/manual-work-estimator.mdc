---
description:
globs:
alwaysApply: false
---
# Manual Work Estimator Guidelines

## Overview

The Manual Work Estimator is a feature that allows managers and administrators to estimate the amount of manual work time required to produce AI-generated responses in the Legal Q&A module. This feature helps with billing, project planning, and understanding the value provided by the AI system.

## Core Implementation

### Frontend Component Structure
- **Main Component**: `frontend/src/components/WorkspaceChat/ChatContainer/ChatHistory/HistoricalMessage/Actions/ManualWorkEstimator/index.jsx`
- **Integration Point**: `frontend/src/components/WorkspaceChat/ChatContainer/ChatHistory/HistoricalMessage/Actions/index.jsx`
- **API Model**: `frontend/src/models/system.js` (estimateManualWork method)

### Backend Implementation
- **API Endpoint**: `POST /system/manual-work-estimate` in `server/endpoints/system.js`
- **Core Function**: `server/utils/chats/helpers/EstimateManualWork.js`
- **Prompt**: Hardcoded prompt optimized for legal work time estimation

## Access Control Rules

### Role-Based Visibility
```javascript
// Button visibility condition
role === "assistant" && isLegalQA && (user?.role === "admin" || user?.role === "manager")

// Prompt details visibility (admin only)
const canViewPrompt = user?.role === "admin";
```

### User Access Levels
- **Manager**: Can use time estimation, cannot view prompt details
- **Admin**: Can use time estimation and view prompt details
- **Regular Users**: No access to the feature

## UI/UX Guidelines

### Button Design
- Use stopwatch icon to represent time estimation
- Short button text: "Time estimate" (English), "Tidsbedömning" (Swedish)
- Show loading state during API calls
- Integrate seamlessly with other action buttons

### Modal Interface
- Display estimation results in a modal
- Show prompt details only for admin users
- Include collapsible prompt information section
- Use markdown rendering for formatted output

## Internationalization Requirements

### Translation Structure
All user-facing text must be internationalized using the following keys in `frontend/src/locales/[lang]/common.js`:

```javascript
"manual-work-estimator": {
  title: "Manual Work Estimation",
  button: "Time estimate",
  "show-prompt": "Show prompt",
  "hide-prompt": "Hide prompt",
  "prompt-title": "Prompt used for estimation",
  "system-prompt": "System prompt",
  "user-content": "User content",
  "provider-info": "Provider information",
  model: "Model",
  provider: "Provider"
}
```

### Error Messages
Error messages should be added to `frontend/src/locales/[lang]/showToast.js`:
```javascript
"manual-work-estimate-error": "Manual work estimation failed: "
```

### Supported Languages
- English (en)
- Swedish (sv)
- French (fr)
- German (de)
- Norwegian (no)
- Polish (pl)
- Kinyarwanda (rw)

## API Design Patterns

### Request Format
```javascript
{
  question: string,  // User's original question
  answer: string     // AI's response to estimate
}
```

### Response Format
```javascript
{
  success: boolean,
  result: {
    textResponse: string,  // LLM's time estimation
    prompt?: {             // Optional prompt details (for debugging)
      systemPrompt: string,
      userContent: string,
      provider: string,
      model: string
    }
  },
  error?: string          // Error message if success is false
}
```

### Error Handling
- Validate required fields (question and answer)
- Handle LLM service unavailability gracefully
- Return appropriate HTTP status codes (400 for validation, 500 for server errors)
- Log errors for debugging while showing user-friendly messages

## Testing Requirements

### Frontend Testing
- Test component rendering for different user roles
- Test button click functionality and API calls
- Test modal opening/closing behavior
- Test role-based prompt details visibility
- Test error handling and loading states

### Backend Testing
- Test API endpoint with valid and invalid inputs
- Test error handling for missing parameters
- Test LLM service failure scenarios
- Test response format consistency

### Test File Locations
- Frontend: `frontend/src/components/WorkspaceChat/ChatContainer/ChatHistory/HistoricalMessage/Actions/ManualWorkEstimator/__tests__/`
- Backend: `server/tests/unit/endpoints/system/manual-work-estimate.test.js`

## Security Considerations

### Authentication
- Feature inherits authentication from system endpoints
- No additional authentication required beyond user session

### Authorization
- Role-based access control enforced in frontend
- Backend should validate user permissions if needed
- Prompt details restricted to admin users only

### Data Privacy
- No sensitive data stored permanently
- Estimation requests are processed in real-time
- No logging of user questions/answers in estimation context

## Performance Guidelines

### Frontend Performance
- Use React.memo for component optimization if needed
- Implement proper loading states to prevent multiple requests
- Cache estimation results temporarily if appropriate

### Backend Performance
- Keep estimation prompts concise but effective
- Implement timeout handling for LLM requests
- Consider rate limiting for estimation requests

## Maintenance Guidelines

### Code Organization
- Keep estimation logic in dedicated utility functions
- Separate UI components from business logic
- Use consistent naming conventions across files

### Prompt Management
- Document prompt changes and rationale
- Test prompt effectiveness with various input types
- Consider making prompts configurable in the future

### Monitoring
- Log estimation request frequency and success rates
- Monitor LLM response times for estimation requests
- Track user adoption by role (manager vs admin usage)

## Integration Points

### Legal Q&A Module
- Feature only appears in Legal Q&A conversations
- Integrates with existing message action buttons
- Respects module-specific permissions and settings

### System LLM
- Uses the same LLM configuration as other system features
- Inherits LLM provider settings and credentials
- Falls back gracefully if LLM is unavailable

### User Management
- Integrates with existing role-based access control
- Respects user suspension and permission changes
- Works with multi-tenant workspace architecture

## Future Enhancement Considerations

### Potential Improvements
- Configurable estimation prompts via admin interface
- Historical estimation data and analytics
- Integration with billing/invoicing systems
- Batch estimation for multiple responses
- Custom estimation models per legal practice area

### Extensibility
- Design components to be reusable for other modules
- Keep API flexible for additional estimation parameters
- Consider plugin architecture for custom estimation logic
