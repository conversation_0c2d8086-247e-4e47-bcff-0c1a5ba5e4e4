import { useEffect, useState, useRef } from "react";
import Sidebar from "@/components/SettingsSidebar";
import showToast from "@/utils/toast";
import System from "@/models/system";
import paths from "@/utils/paths";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ArrowLeft } from "@phosphor-icons/react";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { Button } from "@/components/Button";
import Modal from "@/components/ui/Modal";

export default function DefaultSettings(workspace) {
  const [hasChanges, setHasChanges] = useState(false);
  const [saving, setSaving] = useState(false);
  const [applying, setApplying] = useState(false);
  const [applyingVectorSearch, setApplyingVectorSearch] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showVectorSearchConfirmDialog, setShowVectorSearchConfirmDialog] =
    useState(false);
  const { t } = useTranslation();
  const formDefaultPrompt = useRef(null);
  const [prompt, setPrompt] = useState("");
  const [vectorSearchTopN, setVectorSearchTopN] = useState("");
  const [validationPrompt, setValidationPrompt] = useState("");
  const [canvasSystemPrompt, setCanvasSystemPrompt] = useState("");
  const [canvasUploadSystemPrompt, setCanvasUploadSystemPrompt] = useState("");
  const [manualWorkEstimatorPrompt, setManualWorkEstimatorPrompt] =
    useState("");
  const [styleGenerationPrompt, setStyleGenerationPrompt] = useState("");
  const promptTextareaRef = useRef(null);
  const validationTextareaRef = useRef(null);
  const canvasPromptTextareaRef = useRef(null);
  const canvasUploadPromptTextareaRef = useRef(null);
  const manualWorkEstimatorPromptTextareaRef = useRef(null);
  const styleGenerationPromptTextareaRef = useRef(null);

  const autoResizeTextarea = (e) => {
    const textarea = e.target;
    textarea.style.height = "auto";
    const lineHeight = parseInt(getComputedStyle(textarea).lineHeight);
    const maxRows = 20;
    const maxHeight = lineHeight * maxRows;
    const shouldShowScrollbar = textarea.scrollHeight > maxHeight;
    const newHeight = Math.min(textarea.scrollHeight, maxHeight);

    textarea.style.height = `${newHeight}px`;
    textarea.style.overflowY = shouldShowScrollbar ? "auto" : "hidden";
  };

  // Store default placeholders
  const [defaultPlaceholders, setDefaultPlaceholders] = useState({
    systemPrompt: "",
    vectorSearchTopN: "",
    validationPrompt: "",
    canvasSystemPrompt: "",
    canvasUploadSystemPrompt: "",
    manualWorkEstimatorPrompt: "",
    styleGenerationPrompt: "",
  });

  useEffect(() => {
    const fetchPrompt = async () => {
      const {
        // Actual values
        systemPrompt,
        vectorSearchTopN,
        validationPrompt,
        canvasSystemPrompt,
        canvasUploadSystemPrompt,
        manualWorkEstimatorPrompt,
        styleGenerationPrompt,

        // Default placeholder values
        defaultSystemPrompt,
        defaultVectorSearchTopN,
        defaultValidationPrompt,
        defaultCanvasSystemPrompt,
        defaultCanvasUploadSystemPrompt,
        defaultManualWorkEstimatorPrompt,
        defaultStyleGenerationPrompt,
      } = await System.getDefaultSettings();

      // Set actual values
      setPrompt(systemPrompt);
      setVectorSearchTopN(vectorSearchTopN);
      setValidationPrompt(validationPrompt);
      setCanvasSystemPrompt(canvasSystemPrompt);
      setCanvasUploadSystemPrompt(canvasUploadSystemPrompt);
      setManualWorkEstimatorPrompt(manualWorkEstimatorPrompt);
      setStyleGenerationPrompt(styleGenerationPrompt);

      // Set default placeholders
      setDefaultPlaceholders({
        systemPrompt: defaultSystemPrompt,
        vectorSearchTopN: defaultVectorSearchTopN,
        validationPrompt: defaultValidationPrompt,
        canvasSystemPrompt: defaultCanvasSystemPrompt,
        canvasUploadSystemPrompt: defaultCanvasUploadSystemPrompt,
        manualWorkEstimatorPrompt: defaultManualWorkEstimatorPrompt,
        styleGenerationPrompt: defaultStyleGenerationPrompt,
      });
    };

    fetchPrompt();
  }, []);

  // Initialize textarea heights when component mounts
  useEffect(() => {
    if (promptTextareaRef.current) {
      const evt = new Event("input", { bubbles: true });
      promptTextareaRef.current.dispatchEvent(evt);
    }
    if (validationTextareaRef.current) {
      const evt = new Event("input", { bubbles: true });
      validationTextareaRef.current.dispatchEvent(evt);
    }
    if (canvasPromptTextareaRef.current) {
      const evt = new Event("input", { bubbles: true });
      canvasPromptTextareaRef.current.dispatchEvent(evt);
    }
    if (canvasUploadPromptTextareaRef.current) {
      const evt = new Event("input", { bubbles: true });
      canvasUploadPromptTextareaRef.current.dispatchEvent(evt);
    }
    if (manualWorkEstimatorPromptTextareaRef.current) {
      const evt = new Event("input", { bubbles: true });
      manualWorkEstimatorPromptTextareaRef.current.dispatchEvent(evt);
    }
    if (styleGenerationPromptTextareaRef.current) {
      const evt = new Event("input", { bubbles: true });
      styleGenerationPromptTextareaRef.current.dispatchEvent(evt);
    }
  }, []);

  // Re-trigger auto-expanding when placeholder text is loaded
  useEffect(() => {
    // Check if any placeholder has been loaded (not empty)
    const hasPlaceholders = Object.values(defaultPlaceholders).some(
      (placeholder) => placeholder && placeholder.trim() !== ""
    );

    if (hasPlaceholders) {
      // Small delay to ensure DOM is updated with placeholder
      setTimeout(() => {
        // Trigger auto-expanding for all textareas
        const textareaRefs = [
          promptTextareaRef,
          validationTextareaRef,
          canvasPromptTextareaRef,
          canvasUploadPromptTextareaRef,
          styleGenerationPromptTextareaRef,
        ];

        textareaRefs.forEach((ref) => {
          if (ref.current) {
            const evt = new Event("input", { bubbles: true });
            ref.current.dispatchEvent(evt);
          }
        });
      }, 100);
    }
  }, [defaultPlaceholders]);

  const handleSubmit = async (event) => {
    setSaving(true);
    event.preventDefault();

    const textareaValue = document.getElementById("default-prompt").value;
    const vectorSearchTopN = document.getElementById(
      "vector-search-top-n"
    ).value;
    const validationPromptValue =
      document.getElementById("validation-prompt").value;

    const canvasSystemPromptValue = document.getElementById(
      "canvas-system-prompt"
    ).value;
    const canvasUploadSystemPromptValue = document.getElementById(
      "canvas-upload-system-prompt"
    ).value;
    const manualWorkEstimatorPromptValue = document.getElementById(
      "manual-work-estimator-prompt"
    ).value;
    const styleGenerationPromptValue = document.getElementById(
      "style-generation-prompt"
    ).value;
    const response = await System.setDefaultSettings({
      systemPrompt: textareaValue,
      vectorSearch: vectorSearchTopN ? Number(vectorSearchTopN) : null,
      validationPrompt: validationPromptValue,
      canvasSystemPrompt: canvasSystemPromptValue,
      canvasUploadSystemPrompt: canvasUploadSystemPromptValue,
      manualWorkEstimatorPrompt: manualWorkEstimatorPromptValue,
      styleGenerationPrompt: styleGenerationPromptValue,
    });

    if (response.success) {
      showToast(t("default-settings.toast-success"), "success");
    } else {
      showToast(t("default-settings.toast-fail"), "error");
    }
    setSaving(false);
    setHasChanges(false);
  };

  const confirmApplyToAllWorkspaces = () => {
    setShowConfirmDialog(true);
  };

  const cancelApplyToAllWorkspaces = () => {
    setShowConfirmDialog(false);
  };

  const handleApplyToAllWorkspaces = async () => {
    setApplying(true);
    setShowConfirmDialog(false);

    const textareaValue = document.getElementById("default-prompt").value;
    const response =
      await System.applyDefaultPromptToAllWorkspaces(textareaValue);

    if (response.success) {
      showToast(
        t("default-settings.toast-apply-success", {
          count: response.updatedCount,
        }) || `Default prompt applied to ${response.updatedCount} workspaces`,
        "success"
      );
    } else {
      showToast(
        t("default-settings.toast-apply-fail") ||
          "Failed to apply default prompt to workspaces",
        "error"
      );
    }

    setApplying(false);
  };

  const confirmApplyVectorSearchToAllWorkspaces = () => {
    setShowVectorSearchConfirmDialog(true);
  };

  const cancelApplyVectorSearchToAllWorkspaces = () => {
    setShowVectorSearchConfirmDialog(false);
  };

  const handleApplyVectorSearchToAllWorkspaces = async () => {
    setApplyingVectorSearch(true);
    setShowVectorSearchConfirmDialog(false);

    const vectorSearchValue = document.getElementById(
      "vector-search-top-n"
    ).value;
    const response =
      await System.applyVectorSearchToAllWorkspaces(vectorSearchValue);

    if (response.success) {
      showToast(
        t("default-settings.toast-vector-search-apply-success", {
          count: response.updatedCount,
        }) ||
          `Vector search setting applied to ${response.updatedCount} workspaces`,
        "success"
      );
    } else {
      showToast(
        t("default-settings.toast-vector-search-apply-fail") ||
          "Failed to apply vector search setting to workspaces",
        "error"
      );
    }

    setApplyingVectorSearch(false);
  };

  if (!workspace) return null;

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <form
          ref={formDefaultPrompt}
          onSubmit={handleSubmit}
          className="relative rounded-md w-full h-full overflow-y-scroll"
        >
          <div className="flex flex-col w-full md:pl-6 md:pr-[50px] md:py-4">
            <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-3">
              <Link to={paths.home()}>
                <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
              </Link>
              <div className="flex flex-col">
                <div className="items-center">
                  <p className="text-lg leading-6 font-bold text-foreground">
                    {t("default-settings.title")}
                  </p>
                </div>
                <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                  {t("default-settings.default-desc")}
                </p>
              </div>
            </div>
            <div className="flex flex-col border-top pt-3">
              <label
                htmlFor="name"
                className="block input-label text-foreground"
              >
                {t("default-settings.prompt")}
              </label>
              <p className="text-foreground text-opacity-60 text-xs font-medium py-1.5">
                {t("default-settings.prompt-desc")}
              </p>
            </div>
            <textarea
              ref={promptTextareaRef}
              name="draftingPrompt"
              rows={3}
              id="default-prompt"
              defaultValue={prompt}
              placeholder={
                defaultPlaceholders.systemPrompt ||
                t("default-settings.prompt-placeholder")
              }
              required={true}
              wrap="soft"
              autoComplete="off"
              onChange={(e) => {
                setHasChanges(true);
                autoResizeTextarea(e);
              }}
              onInput={autoResizeTextarea}
              className="dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2 min-h-[66px] resize-none transition-all duration-200"
            />

            <Button
              onClick={confirmApplyToAllWorkspaces}
              disabled={applying}
              className="mt-4 w-fit"
            >
              {applying
                ? t("default-settings.applying") || "Applying..."
                : t("default-settings.apply-to-all") ||
                  "Apply to all existing Legal Q&A workspaces"}
            </Button>

            <hr className="my-6 border-border" />

            <div className="flex flex-col mt-6">
              <div className="flex flex-col">
                <label
                  htmlFor="vector-search-top-n"
                  className="block input-label text-foreground"
                >
                  {t("default-settings.snippets.title")}
                </label>
                <p className="text-foreground text-opacity-60 text-xs font-medium py-1.5">
                  {t("default-settings.snippets.description")}
                  <br />
                  <i>{t("default-settings.snippets.recommend")}</i>
                </p>
              </div>
              <input
                name="vector-search-top-n"
                type="number"
                id="vector-search-top-n"
                min={1}
                max={200}
                step={1}
                onWheel={(e) => e.target.blur()}
                defaultValue={vectorSearchTopN}
                placeholder={defaultPlaceholders.vectorSearchTopN || "30"}
                required={true}
                autoComplete="off"
                onChange={() => setHasChanges(true)}
                className="dark-input-mdl w-32 text-foreground text-sm rounded-lg block p-2"
              />
              <Button
                type="button"
                onClick={confirmApplyVectorSearchToAllWorkspaces}
                disabled={applyingVectorSearch}
                className="mt-4 w-fit"
              >
                {applyingVectorSearch
                  ? t("default-settings.applying") || "Applying..."
                  : t("default-settings.apply-vector-search-to-all") ||
                    "Apply to all existing Legal Q&A workspaces"}
              </Button>
            </div>

            <hr className="my-6 border-border" />

            <div className="flex flex-col mt-6">
              <div className="flex flex-col">
                <label
                  htmlFor="validation-prompt"
                  className="block input-label text-foreground"
                >
                  {t("default-settings.validation-prompt.title")}
                </label>
                <p className="text-foreground text-opacity-60 text-xs font-medium py-1.5">
                  {t("default-settings.validation-prompt.description")}
                </p>
              </div>
              <textarea
                ref={validationTextareaRef}
                name="validationPrompt"
                rows={3}
                id="validation-prompt"
                defaultValue={validationPrompt}
                placeholder={
                  defaultPlaceholders.validationPrompt ||
                  t("default-settings.validation-prompt.placeholder")
                }
                required={true}
                wrap="soft"
                autoComplete="off"
                onChange={(e) => {
                  setHasChanges(true);
                  autoResizeTextarea(e);
                }}
                onInput={autoResizeTextarea}
                className="dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2 min-h-[66px] resize-none transition-all duration-200"
              />
            </div>

            {/* Canvas System Prompt setting */}
            <div className="flex flex-col border-top pt-3 mt-6">
              <label
                htmlFor="canvas-system-prompt"
                className="block input-label text-foreground"
              >
                {t("default-settings.canvas-prompt")}
              </label>
              <p className="text-foreground text-opacity-60 text-xs font-medium py-1.5">
                {t("default-settings.canvas-prompt-desc")}
              </p>
            </div>
            <textarea
              ref={canvasPromptTextareaRef}
              name="canvasSystemPrompt"
              rows={3}
              id="canvas-system-prompt"
              defaultValue={canvasSystemPrompt}
              placeholder={defaultPlaceholders.canvasSystemPrompt}
              wrap="soft"
              autoComplete="off"
              onChange={(e) => {
                setHasChanges(true);
                autoResizeTextarea(e);
              }}
              onInput={autoResizeTextarea}
              className="dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2 min-h-[66px] resize-none transition-all duration-200"
            />

            {/* Canvas Upload System Prompt setting */}

            <div className="flex flex-col border-top pt-3 mt-6">
              <label
                htmlFor="canvas-upload-system-prompt"
                className="block input-label text-foreground"
              >
                {t("default-settings.canvas-upload-prompt")}
              </label>
              <p className="text-foreground text-opacity-60 text-xs font-medium py-1.5">
                {t("default-settings.canvas-upload-prompt-desc")}
              </p>
            </div>
            <textarea
              ref={canvasUploadPromptTextareaRef}
              name="canvasUploadSystemPrompt"
              rows={3}
              id="canvas-upload-system-prompt"
              defaultValue={canvasUploadSystemPrompt}
              placeholder={defaultPlaceholders.canvasUploadSystemPrompt}
              wrap="soft"
              autoComplete="off"
              onChange={(e) => {
                setCanvasUploadSystemPrompt(e.target.value);
                setHasChanges(true);
                autoResizeTextarea(e);
              }}
              onInput={autoResizeTextarea}
              className="dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2 min-h-[66px] resize-none transition-all duration-200"
            />

            {/* Manual Work Estimator Prompt setting */}
            <div className="flex flex-col border-top pt-3 mt-6">
              <label
                htmlFor="manual-work-estimator-prompt"
                className="block input-label text-foreground"
              >
                {t("default-settings.manual-work-estimator-prompt")}
              </label>
              <p className="text-foreground text-opacity-60 text-xs font-medium py-1.5">
                {t("default-settings.manual-work-estimator-prompt-desc")}
              </p>
            </div>
            <textarea
              ref={manualWorkEstimatorPromptTextareaRef}
              name="manualWorkEstimatorPrompt"
              rows={3}
              id="manual-work-estimator-prompt"
              defaultValue={manualWorkEstimatorPrompt}
              placeholder={defaultPlaceholders.manualWorkEstimatorPrompt}
              wrap="soft"
              autoComplete="off"
              onChange={(e) => {
                setManualWorkEstimatorPrompt(e.target.value);
                setHasChanges(true);
                autoResizeTextarea(e);
              }}
              onInput={autoResizeTextarea}
              className="dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2 min-h-[66px] resize-none transition-all duration-200"
            />

            {/* Style Generation Prompt setting */}
            <div className="flex flex-col border-top pt-3 mt-6">
              <label
                htmlFor="style-generation-prompt"
                className="block input-label text-foreground"
              >
                {t("default-settings.style-generation-prompt")}
              </label>
              <p className="text-foreground text-opacity-60 text-xs font-medium py-1.5">
                {t("default-settings.style-generation-prompt-desc")}
              </p>
            </div>
            <textarea
              ref={styleGenerationPromptTextareaRef}
              name="styleGenerationPrompt"
              rows={3}
              id="style-generation-prompt"
              defaultValue={styleGenerationPrompt}
              placeholder={defaultPlaceholders.styleGenerationPrompt}
              wrap="soft"
              autoComplete="off"
              onChange={(e) => {
                setStyleGenerationPrompt(e.target.value);
                setHasChanges(true);
                autoResizeTextarea(e);
              }}
              onInput={autoResizeTextarea}
              className="dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2 min-h-[66px] resize-none transition-all duration-200"
            />

            {hasChanges && (
              <Button type="submit" className="mt-6 w-fit">
                {saving
                  ? t("document-drafting.saving") || "Saving..."
                  : t("document-drafting.save") || "Save changes"}
              </Button>
            )}
          </div>

          <Modal
            isOpen={showConfirmDialog}
            onClose={cancelApplyToAllWorkspaces}
          >
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4">
                {t("common.confirm")}
              </h2>
              <p className="mb-6">{t("default-settings.apply-all-confirm")}</p>
              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  onClick={cancelApplyToAllWorkspaces}
                  variant="secondary"
                >
                  {t("common.cancel")}
                </Button>
                <Button
                  type="button"
                  onClick={handleApplyToAllWorkspaces}
                  className="bg-[#3C8BB1] hover:bg-[#357c9e] text-white"
                >
                  {t("common.confirm")}
                </Button>
              </div>
            </div>
          </Modal>

          <Modal
            isOpen={showVectorSearchConfirmDialog}
            onClose={cancelApplyVectorSearchToAllWorkspaces}
          >
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4">
                {t("common.confirm")}
              </h2>
              <p className="mb-6">
                {t("default-settings.apply-vector-search-all-confirm")}
              </p>
              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  onClick={cancelApplyVectorSearchToAllWorkspaces}
                  variant="secondary"
                >
                  {t("common.cancel")}
                </Button>
                <Button
                  type="button"
                  onClick={handleApplyVectorSearchToAllWorkspaces}
                  className="bg-[#3C8BB1] hover:bg-[#357c9e] text-white"
                >
                  {t("common.confirm")}
                </Button>
              </div>
            </div>
          </Modal>
        </form>
      </div>
    </div>
  );
}
