/**
 * Validation utilities for user inputs to prevent injection attacks
 */

/**
 * Validates if a string is a valid URL
 * @param {string} url - The URL to validate
 * @returns {boolean} - True if valid URL, false otherwise
 */
export function isValidUrl(url) {
  if (!url || typeof url !== "string") {
    return false;
  }

  // Remove leading/trailing whitespace
  const trimmedUrl = url.trim();

  // Check for minimum length
  if (trimmedUrl.length < 3) {
    return false;
  }

  // Check for dangerous protocols
  const dangerousProtocols = [
    "javascript:",
    "data:",
    "vbscript:",
    "file:",
    "about:",
  ];
  const lowerUrl = trimmedUrl.toLowerCase();

  for (const protocol of dangerousProtocols) {
    if (lowerUrl.startsWith(protocol)) {
      return false;
    }
  }

  try {
    const urlObj = new URL(trimmedUrl);

    // Only allow http, https, mailto, tel, and ftp protocols
    const allowedProtocols = ["http:", "https:", "mailto:", "tel:", "ftp:"];
    if (!allowedProtocols.includes(urlObj.protocol)) {
      return false;
    }

    // Additional checks for suspicious patterns
    if (urlObj.hostname && urlObj.hostname.includes("..")) {
      return false;
    }

    return true;
  } catch (error) {
    // If URL constructor throws, it's not a valid URL
    return false;
  }
}

let _testElement = null;
if (typeof document !== "undefined") {
  _testElement = document.createElement("div");
}

/**
 * Validates if a string is a valid CSS color
 * @param {string} color - The color to validate
 * @returns {boolean} - True if valid CSS color, false otherwise
 */
export function isValidCssColor(color) {
  if (!color || typeof color !== "string") {
    return false;
  }

  const trimmedColor = color.trim();

  // Check for minimum length
  if (trimmedColor.length < 3) {
    return false;
  }

  // Check for dangerous patterns that could be used for injection
  const dangerousPatterns = [
    /javascript:/i,
    /expression\s*\(/i,
    /url\s*\(/i,
    /@import/i,
    /behavior\s*:/i,
    /binding\s*:/i,
    /<script/i,
    /<\/script/i,
    /&lt;script/i,
    /&lt;\/script/i,
  ];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(trimmedColor)) {
      return false;
    }
  }

  // Test if it's a valid CSS color
  try {
    let isValid;
    if (typeof document !== "undefined" && _testElement) {
      _testElement.style.color = ""; // Reset style before use
      _testElement.style.color = trimmedColor;
      isValid = _testElement.style.color !== "";
    } else {
      // Fallback for SSR – rely on CSS.supports if available, else mark as invalid
      isValid =
        typeof CSS !== "undefined" && CSS.supports("color", trimmedColor);
    }

    // Additional validation for common color formats
    const colorPatterns = [
      /^#([0-9A-Fa-f]{3}){1,2}$/, // Hex colors (#fff, #ffffff)
      /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/, // RGB colors
      /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[0-1]?\.?\d*\s*\)$/, // RGBA colors
      /^hsl\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)$/, // HSL colors
      /^hsla\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*,\s*[0-1]?\.?\d*\s*\)$/, // HSLA colors
    ];

    // Check if it matches common color patterns or is a named color
    const matchesPattern = colorPatterns.some((pattern) =>
      pattern.test(trimmedColor)
    );
    const isNamedColor = isValid && !matchesPattern;

    return isValid && (matchesPattern || isNamedColor);
  } catch (error) {
    return false;
  }
}

/**
 * Sanitizes a URL by validating it and returning a safe version
 * @param {string} url - The URL to sanitize
 * @returns {string|null} - Sanitized URL or null if invalid
 */
export function sanitizeUrl(url) {
  if (!url || typeof url !== "string") {
    return null;
  }

  const trimmedUrl = url.trim();

  // Check for minimum length
  if (trimmedUrl.length < 3) {
    return null;
  }

  // If it doesn't start with a protocol, assume https and validate
  let urlToValidate = trimmedUrl;
  if (!trimmedUrl.match(/^[a-zA-Z][a-zA-Z0-9+.-]*:/)) {
    urlToValidate = `https://${trimmedUrl}`;
  }

  // Now validate the URL with protocol
  if (!isValidUrl(urlToValidate)) {
    return null;
  }

  return urlToValidate;
}

/**
 * Sanitizes a CSS color by validating it and returning a safe version
 * @param {string} color - The color to sanitize
 * @returns {string|null} - Sanitized color or null if invalid
 */
export function sanitizeCssColor(color) {
  if (!isValidCssColor(color)) {
    return null;
  }

  return color.trim();
}

/**
 * Predefined safe color palette for text colors
 */
export const SAFE_TEXT_COLORS = [
  "#000000",
  "#374151",
  "#6B7280",
  "#9CA3AF",
  "#EF4444",
  "#F97316",
  "#F59E0B",
  "#FFDD00",
  "#84CC16",
  "#22C55E",
  "#10B981",
  "#14B8A6",
  "#06B6D4",
  "#0EA5E9",
  "#3B82F6",
  "#6366F1",
  "#8B5CF6",
  "#A855F7",
  "#D946EF",
  "#EC4899",
  "#F43F5E",
];

/**
 * Predefined safe color palette for highlight colors
 */
export const SAFE_HIGHLIGHT_COLORS = [
  "#FFFF99",
  "#BBF7D0",
  "#BFDBFE",
  "#DDD6FE",
  "#FBCFE8",
  "#FED7AA",
  "#FECACA",
  "#E5E7EB",
];

/**
 * Checks if a color is in the predefined safe palette
 * @param {string} color - The color to check
 * @param {string} type - The type of color ('text' or 'highlight')
 * @returns {boolean} - True if color is in safe palette
 */
export function isColorInSafePalette(color, type = "text") {
  const palette =
    type === "highlight" ? SAFE_HIGHLIGHT_COLORS : SAFE_TEXT_COLORS;
  return palette.includes(color);
}
