-- CreateTable
CREATE TABLE IF NOT EXISTS "WorkspaceShare" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "workspaceId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "WorkspaceShare_workspaceId_fkey" FOREIGN KEY ("workspaceId") REFERENCES "workspaces" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "WorkspaceShare_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE IF NOT EXISTS "ThreadShare" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "threadId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "ThreadShare_threadId_fkey" FOREIGN KEY ("threadId") REFERENCES "workspace_threads" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "ThreadShare_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE IF NOT EXISTS "new_workspace_threads" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "workspace_id" INTEGER NOT NULL,
    "user_id" INTEGER,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastUpdatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "sharedWithOrg" BOOLEAN NOT NULL DEFAULT false,
    CONSTRAINT "workspace_threads_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "workspace_threads_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO "new_workspace_threads" ("createdAt", "id", "lastUpdatedAt", "name", "slug", "user_id", "workspace_id") SELECT "createdAt", "id", "lastUpdatedAt", "name", "slug", "user_id", "workspace_id" FROM "workspace_threads";
DROP TABLE "workspace_threads";
ALTER TABLE "new_workspace_threads" RENAME TO "workspace_threads";
CREATE UNIQUE INDEX IF NOT EXISTS "workspace_threads_slug_key" ON "workspace_threads"("slug");
CREATE INDEX IF NOT EXISTS "workspace_threads_workspace_id_idx" ON "workspace_threads"("workspace_id");
CREATE INDEX IF NOT EXISTS "workspace_threads_user_id_idx" ON "workspace_threads"("user_id");
CREATE TABLE IF NOT EXISTS "new_workspaces" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "vectorTag" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "openAiTemp" REAL,
    "openAiHistory" INTEGER NOT NULL DEFAULT 20,
    "lastUpdatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "openAiPrompt" TEXT,
    "similarityThreshold" REAL DEFAULT 0.25,
    "chatProvider" TEXT,
    "chatModel" TEXT,
    "embeddingProvider" TEXT,
    "embeddingModel" TEXT,
    "topN" INTEGER DEFAULT 4,
    "type" TEXT DEFAULT '',
    "chatMode" TEXT DEFAULT 'chat',
    "chatType" TEXT DEFAULT 'private',
    "pfpFilename" TEXT,
    "agentProvider" TEXT,
    "agentModel" TEXT,
    "queryRefusalResponse" TEXT,
    "vectorSearchMode" TEXT,
    "user_id" INTEGER NOT NULL DEFAULT 0,
    "pdr" BOOLEAN DEFAULT false,
    "hasMessages" BOOLEAN DEFAULT false,
    "order" INTEGER NOT NULL DEFAULT 0,
    "sharedWithOrg" BOOLEAN NOT NULL DEFAULT false
);
INSERT INTO "new_workspaces" ("agentModel", "agentProvider", "chatMode", "chatModel", "chatProvider", "chatType", "createdAt", "embeddingModel", "embeddingProvider", "hasMessages", "id", "lastUpdatedAt", "name", "openAiHistory", "openAiPrompt", "openAiTemp", "order", "pdr", "pfpFilename", "queryRefusalResponse", "similarityThreshold", "slug", "topN", "type", "user_id", "vectorSearchMode", "vectorTag") SELECT "agentModel", "agentProvider", "chatMode", "chatModel", "chatProvider", "chatType", "createdAt", "embeddingModel", "embeddingProvider", "hasMessages", "id", "lastUpdatedAt", "name", "openAiHistory", "openAiPrompt", "openAiTemp", "order", "pdr", "pfpFilename", "queryRefusalResponse", "similarityThreshold", "slug", "topN", "type", "user_id", "vectorSearchMode", "vectorTag" FROM "workspaces";
DROP TABLE "workspaces";
ALTER TABLE "new_workspaces" RENAME TO "workspaces";
CREATE UNIQUE INDEX IF NOT EXISTS "workspaces_slug_key" ON "workspaces"("slug");
CREATE INDEX IF NOT EXISTS "workspaces_user_id_idx" ON "workspaces"("user_id");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE INDEX IF NOT EXISTS "WorkspaceShare_workspaceId_idx" ON "WorkspaceShare"("workspaceId");

-- CreateIndex
CREATE INDEX IF NOT EXISTS "WorkspaceShare_userId_idx" ON "WorkspaceShare"("userId");

-- CreateIndex
CREATE UNIQUE INDEX IF NOT EXISTS "WorkspaceShare_workspaceId_userId_key" ON "WorkspaceShare"("workspaceId", "userId");

-- CreateIndex
CREATE INDEX IF NOT EXISTS "ThreadShare_threadId_idx" ON "ThreadShare"("threadId");

-- CreateIndex
CREATE INDEX IF NOT EXISTS "ThreadShare_userId_idx" ON "ThreadShare"("userId");

-- CreateIndex
CREATE UNIQUE INDEX IF NOT EXISTS "ThreadShare_threadId_userId_key" ON "ThreadShare"("threadId", "userId");
