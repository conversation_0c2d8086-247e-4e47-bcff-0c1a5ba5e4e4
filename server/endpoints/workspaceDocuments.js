const { reqBody } = require("../utils/http");
const { Document } = require("../models/documents");
const { Workspace } = require("../models/workspace");
const {
  isDocumentProperlyVectorized,
  reVectorizeDocument,
} = require("../utils/helpers/vectorizationCheck");

function workspaceDocumentEndpoints(app) {
  app.post("/workspace/:slug/re-vectorize", async (request, response) => {
    try {
      const { slug = null } = request.params;
      const { docId } = reqBody(request);
      const workspace = await Workspace.get({ slug });

      if (!docId) {
        return response
          .status(400)
          .json({ success: false, message: "Document ID is required" })
          .end();
      }

      console.log(
        `[RE-VECTORIZE] Processing request for document ${docId} in workspace ${workspace.id}`
      );

      // Get the document from the database
      const document = await Document.get({
        workspaceId: workspace.id,
        docId,
      });

      if (!document) {
        return response
          .status(404)
          .json({ success: false, message: "Document not found" })
          .end();
      }

      // Perform re-vectorization
      const success = await reVectorizeDocument(
        workspace.slug,
        docId,
        document
      );
      if (success) {
        return response
          .status(200)
          .json({
            success: true,
            message: "Document re-vectorized successfully",
            docId,
            docPath: document.docpath,
          })
          .end();
      } else {
        return response
          .status(500)
          .json({
            success: false,
            message: "Failed to re-vectorize document",
            docId,
            docPath: document.docpath,
          })
          .end();
      }
    } catch (error) {
      console.error("Error updating document:", error);
      return response
        .status(500)
        .json({
          success: false,
          message: error.message,
        })
        .end();
    }
  });

  app.post("/workspace/:slug/update-star", async (request, response) => {
    // --- NEW LOGGING ---
    const incomingDocPath = reqBody(request)?.docPath;
    console.log(
      `\n\n[HANDLER HIT] /update-star in workspaceDocuments.js received request. Incoming docPath: '${incomingDocPath}'\n\n`
    );
    // --- END LOGGING ---
    try {
      const { slug = null } = request.params;
      let {
        docPath,
        starStatus = false,
        isFolder = false,
        forceUpdate = false, // New parameter to force update even if not properly vectorized
      } = reqBody(request);

      // Ensure docPath is a string and normalize it
      if (docPath) {
        docPath = String(docPath).trim();
      } else {
        return response
          .status(400)
          .json({
            message: "Invalid document path",
            error: "Document path is required",
          })
          .end();
      }
      const workspace = await Workspace.get({ slug });

      // Debug log to help diagnose document path issues
      console.log(`[UPDATE-STAR] Request details:`, {
        workspace: workspace?.id,
        workspaceSlug: slug,
        docPath,
        docPathType: typeof docPath,
        starStatus,
        isFolder,
        forceUpdate,
      });

      console.log(`[UPDATE-STAR] Processing request:`, {
        workspace: workspace.id,
        docPath,
        starStatus,
        isFolder,
        forceUpdate,
      });

      if (isFolder) {
        // If it's a folder, update all documents in that folder
        const folderPath = docPath;
        console.log(`[UPDATE-STAR] Folder path: ${folderPath}`);

        // Use a more flexible pattern matching for folder paths
        const documents = await Document.where({
          workspaceId: workspace.id,
          docpath: { contains: folderPath },
        });

        console.log(
          `[UPDATE-STAR] Found ${documents.length} documents in folder ${folderPath}`
        );

        if (documents.length === 0) {
          // Try with a different pattern if no documents found
          console.log(
            `[UPDATE-STAR] Trying alternative pattern for folder path`
          );
          const altDocuments = await Document.where({
            workspaceId: workspace.id,
            docpath: { startsWith: folderPath },
          });

          console.log(
            `[UPDATE-STAR] Alternative search found ${altDocuments.length} documents`
          );

          if (altDocuments.length === 0) {
            return response
              .status(404)
              .json({ message: "No documents found in folder" })
              .end();
          }

          // Use the alternative documents if found
          const updatedDocs = [];
          const skippedDocs = [];

          for (const document of altDocuments) {
            // Check if the document is properly vectorized, but only if we're star-marking (not un-marking)
            console.log(
              `[UPDATE-STAR] Folder check - document: ${document.docpath}, forceUpdate=${forceUpdate}, starStatus=${starStatus}`
            );

            let isVectorized = false;
            if (forceUpdate) {
              console.log(
                `[UPDATE-STAR] Folder check - Skipping vectorization check due to forceUpdate=true`
              );
              isVectorized = true;
            } else if (!starStatus) {
              console.log(
                `[UPDATE-STAR] Folder check - Skipping vectorization check for un-marking operation`
              );
              isVectorized = true;
            } else {
              console.log(
                `[UPDATE-STAR] Folder check - Performing vectorization check for star-marking operation`
              );
              isVectorized = await isDocumentProperlyVectorized(
                workspace.slug,
                document.docId
              );
              console.log(
                `[UPDATE-STAR] Folder check - Vectorization check result: ${isVectorized}`
              );
            }

            if (isVectorized) {
              console.log(
                `[UPDATE-STAR] Updating document: ${document.docpath} (ID: ${document.docId})`
              );
              await Document.update(document.id, { starred: starStatus });
              updatedDocs.push(document.docpath);
            } else {
              console.log(
                `[UPDATE-STAR] Skipping document that is not properly vectorized: ${document.docpath}`
              );
              skippedDocs.push({
                docPath: document.docpath,
                docId: document.docId,
              });
            }
          }

          return response
            .status(200)
            .json({
              message: "Star status updated for eligible documents in folder",
              updated: updatedDocs,
              skipped: skippedDocs,
              requiresReVectorization: skippedDocs.length > 0,
              requiresMetadataUpdate: skippedDocs.length > 0,
            })
            .end();
        } else {
          // Update the documents found with the original pattern
          const updatedDocs = [];
          const skippedDocs = [];

          for (const document of documents) {
            // Check if the document is properly vectorized, but only if we're star-marking (not un-marking)
            console.log(
              `[UPDATE-STAR] Folder check - document: ${document.docpath}, forceUpdate=${forceUpdate}, starStatus=${starStatus}`
            );

            let isVectorized = false;
            if (forceUpdate) {
              console.log(
                `[UPDATE-STAR] Folder check - Skipping vectorization check due to forceUpdate=true`
              );
              isVectorized = true;
            } else if (!starStatus) {
              console.log(
                `[UPDATE-STAR] Folder check - Skipping vectorization check for un-marking operation`
              );
              isVectorized = true;
            } else {
              console.log(
                `[UPDATE-STAR] Folder check - Performing vectorization check for star-marking operation`
              );
              isVectorized = await isDocumentProperlyVectorized(
                workspace.slug,
                document.docId
              );
              console.log(
                `[UPDATE-STAR] Folder check - Vectorization check result: ${isVectorized}`
              );
            }

            if (isVectorized) {
              console.log(
                `[UPDATE-STAR] Updating document: ${document.docpath} (ID: ${document.docId})`
              );
              await Document.update(document.id, { starred: starStatus });
              updatedDocs.push(document.docpath);
            } else {
              console.log(
                `[UPDATE-STAR] Skipping document that is not properly vectorized: ${document.docpath}`
              );
              skippedDocs.push({
                docPath: document.docpath,
                docId: document.docId,
              });
            }
          }

          return response
            .status(200)
            .json({
              message: "Star status updated for eligible documents in folder",
              updated: updatedDocs,
              skipped: skippedDocs,
              requiresReVectorization: skippedDocs.length > 0,
              requiresMetadataUpdate: skippedDocs.length > 0,
            })
            .end();
        }
      } else {
        // Regular document update
        console.log(`[UPDATE-STAR] Looking for document with path: ${docPath}`);

        // First try to find the document by exact path
        const exactPathClause = {
          workspaceId: workspace.id,
          docpath: docPath,
        };
        let document = await Document.get(exactPathClause);

        // If not found, try to find by filename (which is more reliable for newly embedded files)
        if (!document) {
          const filename = docPath.split("/").pop();
          console.log(`[UPDATE-STAR] Trying to find by filename: ${filename}`);

          if (filename) {
            const filenameClause = {
              workspaceId: workspace.id,
              filename: filename,
            };
            document = await Document.get(filenameClause);

            if (document) {
              console.log(
                `[UPDATE-STAR] Found document by filename: ${document.docpath}`
              );
            }
          }
        }

        //-- Add Retry Logic Start --
        // If still not found after initial attempts, wait briefly and retry
        if (!document) {
          console.log(
            `[UPDATE-STAR] Document not found on initial attempts. Retrying after delay...`
          );

          // Retry exact path match
          console.log(`[UPDATE-STAR] Retrying exact path: ${docPath}`);
          const retryExactPathClause = {
            workspaceId: workspace.id,
            docpath: docPath,
          };
          document = await Document.get(retryExactPathClause);

          // Retry filename match if exact path still fails
          if (!document) {
            const filename = docPath.split("/").pop();
            console.log(`[UPDATE-STAR] Retrying filename: ${filename}`);
            if (filename) {
              const retryFilenameClause = {
                workspaceId: workspace.id,
                filename: filename,
              };
              document = await Document.get(retryFilenameClause);
              if (document) {
                console.log(
                  `[UPDATE-STAR] Found document by filename on retry: ${document.docpath}`
                );
              }
            }
          }
          if (document) {
            console.log(`[UPDATE-STAR] Found document on retry.`);
          } else {
            console.log(`[UPDATE-STAR] Document still not found after retry.`);
          }
        }
        //-- Add Retry Logic End --

        // --- Move Not Found check AFTER retry ---
        if (!document) {
          console.log(
            `[UPDATE-STAR] Document not found after retries, trying partial match`
          );
          // Try a partial match if exact match fails
          const partialMatchClause = {
            workspaceId: workspace.id,
            docpath: { contains: docPath },
          };
          const documents = await Document.where(partialMatchClause);

          // If still not found even with partial match, return 404
          if (documents.length === 0) {
            console.error(
              `[UPDATE-STAR] Document definitively not found for path: ${docPath}`
            );
            return response
              .status(404)
              .json({
                message: "Document not found",
                error:
                  "Document not found in workspace. It may have been deleted or not properly embedded.",
                docPath,
              })
              .end();
          }

          // If found via partial match, use the first result
          document = documents[0];
          console.log(
            `[UPDATE-STAR] Found document by partial match: ${document.docpath} (ID: ${document.docId})`
          );
        }

        // --- Proceed with the found document (either from initial lookup, retry, or partial match) ---
        console.log(
          `[UPDATE-STAR] Processing found document: ${document.docpath} (ID: ${document.docId})`
        );

        // Check if the document is properly vectorized, but only if we're star-marking (not un-marking)
        // If we're un-marking a document, we don't need to check if it's properly vectorized
        console.log(
          `[UPDATE-STAR] Checking vectorization: forceUpdate=${forceUpdate}, starStatus=${starStatus}`
        );

        let isVectorized = false;
        if (forceUpdate) {
          console.log(
            `[UPDATE-STAR] Skipping vectorization check due to forceUpdate=true`
          );
          isVectorized = true;
        } else if (!starStatus) {
          console.log(
            `[UPDATE-STAR] Skipping vectorization check for un-marking operation`
          );
          isVectorized = true;
        } else {
          console.log(
            `[UPDATE-STAR] Performing vectorization check for star-marking operation`
          );
          isVectorized = await isDocumentProperlyVectorized(
            workspace.slug,
            document.docId
          );
          console.log(
            `[UPDATE-STAR] Vectorization check result: ${isVectorized}`
          );
        }

        if (isVectorized) {
          await Document.update(document.id, { starred: starStatus });
          // Prepare and log success payload for regular document update
          const successPayload = {
            message: "Star status updated successfully",
            requiresReVectorization: false,
            requiresMetadataUpdate: false,
          };
          console.log(
            `[UPDATE-STAR] Response payload (success):`,
            successPayload
          );
          return response.status(200).json(successPayload).end();
        } else {
          // Prepare and log failure payload for re-vectorization required
          const failurePayload = {
            message:
              "Document requires re-vectorization before it can be starred",
            requiresReVectorization: true,
            requiresMetadataUpdate: true,
            docId: document.docId,
            docPath: document.docpath,
          };
          console.log(
            `[UPDATE-STAR] Response payload (needs re-vectorization):`,
            failurePayload
          );
          return response.status(200).json(failurePayload).end();
        }
      }
    } catch (error) {
      console.error("Error processing the star status update:", error);
      return response.status(500).json({ message: error.message }).end();
    }
  });
}

module.exports = { workspaceDocumentEndpoints };
