import { API_BASE } from "@/utils/constants";
import { create } from "zustand";
import { baseHeaders } from "@/utils/request";
import { persist } from "zustand/middleware";

const ENDPOINTS = {
  "tab-names": {
    endpoint: "/get-tab-names",
    updateEndpoint: "/system/custom-tab-names",
    defaultValue: { tabName1: "", tabName2: "", tabName3: "" },
  },
  "force-invoice-logging": {
    endpoint: "/system/force-invoice-logging",
    updateEndpoint: "/system/force-invoice-logging",
    defaultValue: false,
  },
  "invoice-logging": {
    endpoint: "/system/invoice-logging",
    updateEndpoint: "/system/invoice-logging",
    defaultValue: false,
  },
  "rexor-linkage": {
    endpoint: "/system/rexor-linkage",
    updateEndpoint: "/system/rexor-linkage",
    defaultValue: false,
  },
  "feedback-enabled": {
    endpoint: "/system/feedback-enabled",
    updateEndpoint: "/system/feedback-enabled",
    defaultValue: {
      enabled: false,
    },
  },
  "document-drafting": {
    endpoint: "/system/document-drafting",
    updateEndpoint: "/system/set-document-drafting",
    defaultValue: {
      isDocumentDrafting: false,
      isDocumentDraftingLinking: false,
    },
  },
  "request-legal-assistance": {
    endpoint: "/system/request-legal-assistance",
    updateEndpoint: "/system/request-legal-assistance",
    defaultValue: {
      enabled: false,
      lawFirmName: "",
      email: "",
    },
  },
  "color-palette": {
    endpoint: "/admin/system-preferences",
    updateEndpoint: "/admin/system-preferences",
    defaultValue: "",
  },
  "university-mode": {
    endpoint: "/system/preferences?labels=university_mode",
    updateEndpoint: "/system/preferences",
    defaultValue: false,
  },
  "system-language": {
    endpoint: "/setup-complete",
    updateEndpoint: null, // System language is read-only from this store
    defaultValue: "en",
  },
};

const DEFAULT_REFRESH_INTERVAL = 12 * 60 * 60 * 1000;

const useSystemSettingsStore = create(
  persist(
    (set, get) => ({
      values: Object.fromEntries(
        Object.entries(ENDPOINTS).map(([key, config]) => [
          key,
          config.defaultValue,
        ])
      ),
      lastFetched: {},
      loading: {},
      errors: {},
      refreshInterval: DEFAULT_REFRESH_INTERVAL,

      fetchAllSettings: async (force = false) => {
        const keys = Object.keys(ENDPOINTS);
        const { lastFetched, refreshInterval } = get();
        const now = Date.now();

        const keysToFetch = force
          ? keys
          : keys.filter((key) => {
              const timestamp = lastFetched[key] || 0;
              return !timestamp || now - timestamp > refreshInterval;
            });

        if (keysToFetch.length === 0) return get().values;

        set((state) => {
          const newLoading = { ...state.loading };
          keysToFetch.forEach((key) => {
            newLoading[key] = true;
          });
          return { loading: newLoading };
        });

        const results = await Promise.all(
          keysToFetch.map(async (key) => {
            try {
              const { endpoint } = ENDPOINTS[key];
              const res = await fetch(`${API_BASE}${endpoint}`, {
                method: "GET",
                headers: baseHeaders(),
              });

              if (!res.ok) {
                throw new Error(`Failed to fetch ${key}: ${res.statusText}`);
              }

              const data = await res.json();
              return { key, data, success: true };
            } catch (error) {
              console.error(`Error fetching setting ${key}:`, error);
              return { key, error: error.message, success: false };
            }
          })
        );

        set((state) => {
          const newValues = { ...state.values };
          const newLoading = { ...state.loading };
          const newErrors = { ...state.errors };
          const newLastFetched = { ...state.lastFetched };

          results.forEach(({ key, data, error, success }) => {
            newLoading[key] = false;

            if (success) {
              if (key === "color-palette" && data && data.settings) {
                newValues[key] =
                  data.settings.palette ?? ENDPOINTS[key].defaultValue;
              } else if (key === "university-mode" && data && data.settings) {
                newValues[key] = data.settings.university_mode === "true";
              } else if (key === "system-language" && data && data.results) {
                newValues[key] =
                  data.results.language ?? ENDPOINTS[key].defaultValue;
              } else if (key === "invoice-logging" && data) {
                newValues[key] = data.invoice ?? ENDPOINTS[key].defaultValue;
              } else if (key === "force-invoice-logging" && data) {
                newValues[key] =
                  data.isForcedinvoiceLogging ?? ENDPOINTS[key].defaultValue;
              } else if (key === "rexor-linkage" && data) {
                newValues[key] =
                  data.rexorLinkage ?? ENDPOINTS[key].defaultValue;
              } else {
                newValues[key] = data;
              }
              newLastFetched[key] = now;
              newErrors[key] = null;
            } else {
              newErrors[key] = error;
            }
          });

          return {
            values: newValues,
            loading: newLoading,
            errors: newErrors,
            lastFetched: newLastFetched,
          };
        });

        return get().values;
      },

      getSetting: (key) => {
        if (!ENDPOINTS[key]) {
          console.error(`Unknown setting key: ${key}`);
          return null;
        }
        return get().values[key] ?? ENDPOINTS[key].defaultValue;
      },

      updateSetting: async (key, data) => {
        if (!ENDPOINTS[key]) {
          console.error(`Unknown setting key: ${key}`);
          return false;
        }

        set((state) => ({
          loading: { ...state.loading, [key]: true },
          errors: { ...state.errors, [key]: null },
        }));

        try {
          const endpoint =
            ENDPOINTS[key].updateEndpoint || ENDPOINTS[key].endpoint;
          let payload = data;

          if (key === "document-drafting") {
            payload = {
              document_drafting: data?.isDocumentDrafting ?? false,
              document_drafting_linking:
                data?.isDocumentDraftingLinking ?? false,
            };
          }

          if (key === "color-palette") {
            payload = { palette: data };
          }

          if (key === "university-mode") {
            payload = { university_mode: String(data) };
          }

          // Transform payload for boolean settings to match backend expectations
          if (key === "invoice-logging") {
            // Handle both raw boolean and object format from System page
            payload = data?.invoice !== undefined ? data : { invoice: data };
          }

          if (key === "force-invoice-logging") {
            // Handle both raw boolean and object format from System page
            payload =
              data?.isForcedinvoiceLogging !== undefined
                ? data
                : { isForcedinvoiceLogging: data };
          }

          if (key === "rexor-linkage") {
            // Handle both raw boolean and object format from System page
            payload =
              data?.rexorLinkage !== undefined ? data : { rexorLinkage: data };
          }

          const res = await fetch(`${API_BASE}${endpoint}`, {
            method: "POST",
            headers: baseHeaders(),
            body: JSON.stringify(payload),
          });

          if (!res.ok) {
            throw new Error(`Failed to update ${key}: ${res.statusText}`);
          }

          // Extract the correct value for local state based on the setting type
          let localValue = data;
          if (key === "invoice-logging") {
            localValue = data?.invoice !== undefined ? data.invoice : data;
          } else if (key === "force-invoice-logging") {
            localValue =
              data?.isForcedinvoiceLogging !== undefined
                ? data.isForcedinvoiceLogging
                : data;
          } else if (key === "rexor-linkage") {
            localValue =
              data?.rexorLinkage !== undefined ? data.rexorLinkage : data;
          }

          set((state) => ({
            values: { ...state.values, [key]: localValue },
            lastFetched: { ...state.lastFetched, [key]: Date.now() },
            loading: { ...state.loading, [key]: false },
            errors: { ...state.errors, [key]: null },
          }));

          return true;
        } catch (error) {
          console.error(`Error updating setting ${key}:`, error);

          set((state) => ({
            loading: { ...state.loading, [key]: false },
            errors: { ...state.errors, [key]: error.message },
          }));

          return false;
        }
      },

      isLoading: (key) => get().loading[key] || false,

      getError: (key) => get().errors[key] || null,

      initializeSetting: (key, value) => {
        if (!ENDPOINTS[key]) {
          console.warn(`Attempted to initialize unknown setting key: ${key}`);
          return;
        }
        set((state) => ({
          values: { ...state.values, [key]: value },
          lastFetched: { ...state.lastFetched, [key]: Date.now() },
          errors: { ...state.errors, [key]: null },
          loading: { ...state.loading, [key]: false },
        }));
      },

      clearAllSettings: () => {
        set({
          values: Object.fromEntries(
            Object.entries(ENDPOINTS).map(([key, config]) => [
              key,
              config.defaultValue,
            ])
          ),
          lastFetched: {},
          loading: {},
          errors: {},
        });
      },
    }),
    {
      name: "system-settings",
      partialize: (state) => ({
        values: state.values,
        lastFetched: state.lastFetched,
        refreshInterval: state.refreshInterval,
      }),
    }
  )
);

export const useTabNames = () =>
  useSystemSettingsStore((state) => state.getSetting("tab-names"));

export const useForceInvoiceLogging = () =>
  useSystemSettingsStore((state) => state.getSetting("force-invoice-logging"));

export const useInvoiceLogging = () =>
  useSystemSettingsStore((state) => state.getSetting("invoice-logging"));

export const useRexorLinkage = () =>
  useSystemSettingsStore((state) => state.getSetting("rexor-linkage"));

export const useDocumentDraftingEnabled = () =>
  useSystemSettingsStore((state) => state.getSetting("document-drafting"));

export const useRequestLegalAssistance = () =>
  useSystemSettingsStore((state) =>
    state.getSetting("request-legal-assistance")
  );

export const useColorPalette = () =>
  useSystemSettingsStore((state) => state.getSetting("color-palette"));

export const useFeedbackEnabled = () =>
  useSystemSettingsStore((state) => state.getSetting("feedback-enabled"));

export const useUniversityMode = () =>
  useSystemSettingsStore((state) => state.getSetting("university-mode"));

export const useSystemLanguage = () =>
  useSystemSettingsStore((state) => state.getSetting("system-language"));

export default useSystemSettingsStore;
