---
description:
globs:
alwaysApply: false
---
# Vector Search Overview

This document provides a high-level overview of how vector search and context retrieval work in the system. For detailed implementation specifics, see [vector-context-implementation.mdc](mdc:.cursor/rules/vector-context-implementation.mdc).

## Vector Database Providers

The system supports multiple vector database providers, implemented in `server/utils/vectorDbProviders/`. While multiple options exist, **LanceDB (`lance/`) is the primary and recommended vector database for new deployments** due to its performance and ease of local setup.

- `lance/`: LanceDB vector database (Primary)
- `pinecone/`: Pinecone vector database
- `qdrant/`: Qdrant vector database
- `weaviate/`: Weaviate vector database
- `chroma/`: ChromaDB vector database
- `milvus/`: Milvus vector database
- `astra/`: AstraDB vector database
- `zilliz/`: Zilliz vector database

## Source Management Priority

The `server/utils/helpers/chat/index.js` file manages source context with the following priority:

1.  **Pinned Documents**: Always included first, cannot be displaced.
2.  **Vector Search Results**: Added after pinned documents, ranked by relevance.
3.  **Backfilled History Sources**: Added if vector results are insufficient (See [context-backfilling.mdc](mdc:.cursor/rules/context-backfilling.mdc)).

## Context Window Management Concept

The system manages the context window to stay within LLM token limits:

1.  Calculate available token space based on model limits and dynamic settings.
2.  Allocate tokens for system prompt, user messages, history, and response buffer.
3.  Fill remaining space with sources based on priority (Pinned > Vector > Backfilled).
4.  Truncate or compress sources if necessary.

## Basic Vector Search Process

1.  User query is vectorized using the selected embedding model.
2.  Vector database is queried for similar vectors based on workspace settings (threshold, topN, rerank).
3.  Results are ranked by similarity.
4.  Top results (filtered for duplicates with pinned docs) are included in the context window.
5.  Context is sent to the LLM for response generation.

## Best Practices

- Follow established patterns for vector DB integration.
- Use appropriate error handling.
- Consider performance implications.
- Document complex logic.
- Test thoroughly.
- Implement proper security checks.
