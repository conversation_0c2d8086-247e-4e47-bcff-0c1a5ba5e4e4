import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import ExamplePromptCard from "../index";

// Mock the icons utility
jest.mock("@/utils/examplePromptIcons", () => ({
  icons: [
    {
      name: "test-icon",
      icon: () => <div data-testid="test-icon">Test Icon</div>,
    },
    {
      name: "document-icon",
      icon: () => <div data-testid="document-icon">Document Icon</div>,
    },
  ],
}));

describe("ExamplePromptCard", () => {
  const mockOnClick = jest.fn();

  const defaultExample = {
    id: 1,
    title: "Test Example Title",
    area: "Test Area",
    prompt: "This is a test prompt",
    icon: "test-icon",
    workspaceSlug: "test-workspace",
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("handles click events properly", () => {
    render(
      <ExamplePromptCard example={defaultExample} onClick={mockOnClick} />
    );

    const card = screen.getByRole("button");
    fireEvent.click(card);

    expect(mockOnClick).toHaveBeenCalledWith(defaultExample);
  });

  test("shows loading state correctly", () => {
    render(
      <ExamplePromptCard
        example={defaultExample}
        onClick={mockOnClick}
        isLoading={true}
      />
    );

    // Should show loading spinner in desktop area (but mobile icon still shows)
    expect(screen.getByText("Test Icon")).toBeInTheDocument(); // Mobile icon still shows

    // Button should be disabled
    const card = screen.getByRole("button");
    expect(card).toBeDisabled();
    expect(card).toHaveClass("opacity-60");
  });

  test("renders icon correctly when not loading", () => {
    render(
      <ExamplePromptCard example={defaultExample} onClick={mockOnClick} />
    );

    // Should show the test icon in both mobile and desktop areas
    const icons = screen.getAllByTestId("test-icon");
    expect(icons).toHaveLength(2); // One for desktop, one for mobile
  });

  test("handles missing icon gracefully", () => {
    const exampleWithoutIcon = {
      ...defaultExample,
      icon: "non-existent-icon",
    };

    render(
      <ExamplePromptCard example={exampleWithoutIcon} onClick={mockOnClick} />
    );

    // Should not crash and should still render title and area
    expect(screen.getByText("Test Example Title")).toBeInTheDocument();
    expect(screen.getByText("Test Area")).toBeInTheDocument();
  });

  test("mobile icon area has proper flex classes to prevent shrinking", () => {
    render(
      <ExamplePromptCard example={defaultExample} onClick={mockOnClick} />
    );

    // Check that the area text section exists and has proper structure
    const areaText = screen.getByText("Test Area");
    expect(areaText).toHaveClass("truncate");

    // The container should exist and be properly structured
    const areaContainer = areaText.parentElement;
    expect(areaContainer).toHaveClass("flex", "items-center", "gap-2");
  });
});
