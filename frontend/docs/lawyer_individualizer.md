# Lawyer Individualizer - Frontend Documentation

## Overview

The Lawyer Individualizer frontend provides a user interface for uploading document samples, generating personalized style instructions, and managing style profiles. This documentation covers the frontend implementation details, component architecture, and integration points.

## Component Architecture

### StyleAlignmentModal Component

**Location**: `frontend/src/components/UserMenu/StyleAlignmentModal/index.jsx`

The main modal component that handles all user interactions for style alignment functionality.

#### Component Structure

```jsx
export default function StyleAlignmentModal({ isOpen, onClose }) {
  // State management
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedInstructions, setGeneratedInstructions] = useState("");
  const [profileName, setProfileName] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  // Zustand store hooks
  const styleProfiles = useStyleProfiles();
  const styleAlignmentEnabled = useStyleAlignmentEnabled();
  const activeStyleProfileId = useActiveStyleProfileId();

  // Component methods...
}
```

#### Key Features

1. **File Upload Interface**
   - Drag-and-drop support for DOCX files
   - File validation and error handling
   - Visual feedback for upload status

2. **Style Generation**
   - Integration with backend style analysis API
   - Loading states and progress indicators
   - Error handling and user feedback

3. **Profile Management**
   - Create new style profiles
   - Activate/deactivate profiles
   - Delete existing profiles
   - Profile name validation

4. **Settings Toggle**
   - Enable/disable style alignment globally
   - Visual indicators for current state

#### Props

| Prop | Type | Description |
|------|------|-------------|
| `isOpen` | `boolean` | Controls modal visibility |
| `onClose` | `function` | Callback when modal is closed |

#### State Management

The component uses local state for UI interactions and Zustand store for persistent data:

```jsx
// Local state for UI
const [uploadedFile, setUploadedFile] = useState(null);
const [isGenerating, setIsGenerating] = useState(false);
const [generatedInstructions, setGeneratedInstructions] = useState("");

// Zustand store for persistent data
const styleProfiles = useStyleProfiles();
const addStyleProfile = useAddStyleProfile();
const activateStyleProfile = useActivateStyleProfile();
```

### User Menu Integration

**Location**: `frontend/src/components/UserMenu/HeaderWorkspace/index.jsx`

The StyleAlignmentModal is integrated into the user dropdown menu:

```jsx
// In HeaderWorkspace component
const [showStyleModal, setShowStyleModal] = useState(false);

// Menu item
<div
  onClick={() => setShowStyleModal(true)}
  className="flex items-center gap-x-2 p-2 hover:bg-theme-bg-secondary rounded-md cursor-pointer"
>
  <User className="h-4 w-4" />
  <p className="text-sm">{t("user-menu.style-alignment")}</p>
</div>

// Modal component
<StyleAlignmentModal
  isOpen={showStyleModal}
  onClose={() => setShowStyleModal(false)}
/>
```

## State Management

### Zustand Store

**Location**: `frontend/src/stores/userStore.js`

The user store manages style alignment state using Zustand:

```javascript
export const useUserStore = create(
  persist(
    (set, get) => ({
      // Style alignment state
      styleAlignmentEnabled: false,
      styleProfiles: [],
      activeStyleProfileId: null,

      // Actions
      setStyleAlignmentEnabled: (enabled) =>
        set({ styleAlignmentEnabled: enabled }),

      setStyleProfiles: (profiles) =>
        set({ styleProfiles: profiles }),

      addStyleProfile: (profile) =>
        set((state) => ({
          styleProfiles: [...state.styleProfiles, profile]
        })),

      activateStyleProfile: (profileId) =>
        set((state) => ({
          activeStyleProfileId: profileId,
          styleProfiles: state.styleProfiles.map(profile => ({
            ...profile,
            isActive: profile.id === profileId
          }))
        })),

      deleteStyleProfile: (profileId) =>
        set((state) => ({
          styleProfiles: state.styleProfiles.filter(p => p.id !== profileId),
          activeStyleProfileId: state.activeStyleProfileId === profileId
            ? null
            : state.activeStyleProfileId
        })),
    }),
    {
      name: "user-storage",
      partialize: (state) => ({
        styleAlignmentEnabled: state.styleAlignmentEnabled,
        styleProfiles: state.styleProfiles,
        activeStyleProfileId: state.activeStyleProfileId,
      }),
    }
  )
);
```

### Selector Hooks

For better performance and cleaner code, the store exports selector hooks:

```javascript
// Selector hooks for style alignment
export const useStyleAlignmentEnabled = () =>
  useUserStore((state) => state.styleAlignmentEnabled);

export const useStyleProfiles = () =>
  useUserStore((state) => state.styleProfiles);

export const useActiveStyleProfileId = () =>
  useUserStore((state) => state.activeStyleProfileId);

export const useActiveStyleProfile = () =>
  useUserStore((state) =>
    state.styleProfiles.find(p => p.id === state.activeStyleProfileId)
  );

// Action hooks
export const useSetStyleAlignmentEnabled = () =>
  useUserStore((state) => state.setStyleAlignmentEnabled);

export const useAddStyleProfile = () =>
  useUserStore((state) => state.addStyleProfile);

export const useActivateStyleProfile = () =>
  useUserStore((state) => state.activateStyleProfile);

export const useDeleteStyleProfile = () =>
  useUserStore((state) => state.deleteStyleProfile);
```

## API Integration

### System API

**Location**: `frontend/src/models/system.js`

The System model provides methods for interacting with the style generation API:

```javascript
const System = {
  // Generate style profile from document content
  generateStyleProfile: async (documentContent) => {
    return await fetch(`${API_BASE}/system/generate-style-profile`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ documentContent }),
    })
    .then((res) => res.json())
    .catch((e) => {
      console.error(e);
      return { success: false, error: e.message };
    });
  },
};
```

### Workspace API Integration

**Location**: `frontend/src/models/workspace.js`

The workspace model is updated to send style alignment data with chat requests:

```javascript
// In streamChatWithWorkspace function
const chatBody = {
  message,
  mode,
  // ... other parameters
  styleAlignment: {
    enabled: styleAlignmentEnabled,
    instructions: activeProfile?.instructions || null,
  },
};
```

## File Upload Handling

### Document Processing

The component handles DOCX file uploads and processes them for style analysis:

```jsx
const handleFileUpload = async (file) => {
  if (!file || file.type !== "application/vnd.openxmlformats-officedocument.wordprocessingml.document") {
    showToast("Please upload a valid DOCX file", "error");
    return;
  }

  setUploadedFile(file);

  // Process file through existing document pipeline
  const formData = new FormData();
  formData.append("file", file);

  try {
    const response = await fetch("/api/document/upload-document", {
      method: "POST",
      headers: baseHeaders(),
      body: formData,
    });

    const result = await response.json();
    if (result.success) {
      // File processed successfully
      setDocumentContent(result.content);
    }
  } catch (error) {
    showToast("Failed to process document", "error");
  }
};
```

### Drag and Drop Support

The component includes drag-and-drop functionality:

```jsx
const handleDragOver = (e) => {
  e.preventDefault();
  setIsDragOver(true);
};

const handleDragLeave = (e) => {
  e.preventDefault();
  setIsDragOver(false);
};

const handleDrop = (e) => {
  e.preventDefault();
  setIsDragOver(false);

  const files = Array.from(e.dataTransfer.files);
  if (files.length > 0) {
    handleFileUpload(files[0]);
  }
};
```

## Internationalization

### Translation Keys

**Location**: `frontend/src/locales/en/modalRelatedKeys.js`

All user-facing text is internationalized using translation keys:

```javascript
"user-menu": {
  "style-alignment": "Personal Style Alignment",
  "style-upload": "Style Alignment",
  "style-generate": "Style Setting Generator",
  "style-enabled": "Enable Personal Style",
  "style-profile": "Style Profile",
  "style-generated-success": "Style profile generated successfully",
  "style-generation-failed": "Failed to generate style profile",
}
```

### Multi-language Support

The feature supports all system languages:
- English (en)
- French (fr)
- Swedish (sv)
- German (de)
- Norwegian (no)
- Polish (pl)
- Kinyarwanda (rw)

Translation keys are maintained in corresponding locale files for each language.

## Error Handling

### User Feedback

The component provides comprehensive error handling with user-friendly messages:

```jsx
const handleStyleGeneration = async () => {
  try {
    setIsGenerating(true);

    const result = await System.generateStyleProfile(documentContent);

    if (result.success) {
      setGeneratedInstructions(result.styleInstructions);
      showToast(t("user-menu.style-generated-success"), "success");
    } else {
      showToast(result.error || t("user-menu.style-generation-failed"), "error");
    }
  } catch (error) {
    console.error("Style generation error:", error);
    showToast(t("user-menu.style-generation-failed"), "error");
  } finally {
    setIsGenerating(false);
  }
};
```

### Validation

Input validation ensures data integrity:

```jsx
const validateProfileName = (name) => {
  if (!name || name.trim().length === 0) {
    return "Profile name is required";
  }
  if (name.length > 255) {
    return "Profile name must be less than 255 characters";
  }
  if (styleProfiles.some(p => p.name === name.trim())) {
    return "A profile with this name already exists";
  }
  return null;
};
```

## Styling and UI

### CSS Classes

The component uses Tailwind CSS for styling:

```jsx
<div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
  <div className="bg-theme-bg-primary rounded-lg shadow-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
    <div className="p-6">
      {/* Modal content */}
    </div>
  </div>
</div>
```

### Theme Integration

The component respects the application's theme system:
- Uses theme variables for colors
- Supports light/dark mode
- Consistent with application design patterns

### Responsive Design

The modal is responsive and works on different screen sizes:

```jsx
<div className="max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
  {/* Content adapts to screen size */}
</div>
```

## Testing

### Component Tests

**Location**: `frontend/src/components/UserMenu/StyleAlignmentModal/__tests__/StyleAlignmentModal.test.jsx`

Comprehensive test suite covering:

```javascript
describe("StyleAlignmentModal", () => {
  test("renders modal when open", () => {
    // Test modal rendering
  });

  test("handles file upload", () => {
    // Test file upload functionality
  });

  test("generates style profile", () => {
    // Test style generation
  });

  test("manages style profiles", () => {
    // Test profile CRUD operations
  });

  test("toggles style alignment", () => {
    // Test enable/disable functionality
  });
});
```

### Test Utilities

Mock functions for testing:

```javascript
// Mock the stores
const mockUseStyleProfiles = jest.fn(() => []);
const mockUseAddStyleProfile = jest.fn();
const mockUseActivateStyleProfile = jest.fn();

// Mock API calls
jest.mock("../../../models/system", () => ({
  generateStyleProfile: jest.fn(),
}));
```

## Performance Considerations

### Optimization Strategies

1. **Lazy Loading**: Modal component only renders when needed
2. **Memoization**: Use React.memo for expensive components
3. **Debouncing**: Debounce file upload and API calls
4. **State Management**: Efficient Zustand selectors prevent unnecessary re-renders

### Bundle Size

The feature adds minimal bundle size:
- Reuses existing UI components
- Leverages existing file upload infrastructure
- No additional heavy dependencies

## Accessibility

### ARIA Support

The component includes proper ARIA attributes:

```jsx
<div
  role="dialog"
  aria-labelledby="modal-title"
  aria-describedby="modal-description"
>
  <h2 id="modal-title">{t("user-menu.style-alignment")}</h2>
  <div id="modal-description">
    {/* Modal description */}
  </div>
</div>
```

### Keyboard Navigation

- Modal can be closed with Escape key
- Tab navigation works properly
- Focus management when modal opens/closes

### Screen Reader Support

- Proper heading structure
- Descriptive labels for form elements
- Status announcements for loading states

## Integration Points

### Chat System Integration

The frontend sends style alignment data with chat requests:

```javascript
// In workspace.js
const styleAlignment = {
  enabled: useStyleAlignmentEnabled(),
  instructions: useActiveStyleProfile()?.instructions || null,
};

// Include in chat request
const chatBody = {
  message,
  mode,
  styleAlignment,
  // ... other parameters
};
```

### Document Upload Integration

Leverages existing document processing pipeline:
- Uses same upload endpoints
- Reuses file validation logic
- Integrates with collector service

## Troubleshooting

### Common Frontend Issues

1. **Modal Not Opening**
   - Check if `isOpen` prop is being set correctly
   - Verify no JavaScript errors in console
   - Ensure proper event handlers are attached

2. **File Upload Fails**
   - Verify file is DOCX format
   - Check network connectivity
   - Ensure collector service is running

3. **Style Generation Errors**
   - Check browser console for API errors
   - Verify LLM provider configuration
   - Ensure document content is not empty

### Debug Tools

Enable debug logging:

```javascript
// In browser console
localStorage.setItem('debug', 'style-alignment');

// Check store state
console.log(useUserStore.getState());
```

## Future Enhancements

### Planned Improvements

1. **Batch Upload**: Support multiple document analysis
2. **Style Preview**: Preview how style affects generated content
3. **Style Comparison**: Compare different style profiles
4. **Export/Import**: Share style profiles between users
5. **Advanced Analytics**: Style consistency metrics

### Technical Debt

1. **Component Splitting**: Break down large modal into smaller components
2. **Custom Hooks**: Extract reusable logic into custom hooks
3. **Type Safety**: Add TypeScript support for better type safety
4. **Performance**: Implement virtual scrolling for large profile lists

## Conclusion

The Lawyer Individualizer frontend provides a comprehensive and user-friendly interface for managing personalized writing styles. The implementation follows React best practices, integrates seamlessly with the existing application architecture, and provides a solid foundation for future enhancements.
