# This Github action is for publishing of the DEVELOP image for IST Legal
name: Publish DEVELOP Docker image

concurrency:
  group: build-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches: ["main-dev"]
    paths-ignore:
      - "**.md"
      - "cloud-deployments/**/*"
      - "images/**/*"
      - ".vscode/**/*"
      - "**/.env.example"
      - ".github/ISSUE_TEMPLATE/**/*"
      - ".devcontainer/**/*"
      - "embed/**/*" # Embed is submodule
      - "browser-extension/**/*" # Chrome extension is submodule
      - "server/utils/agents/aibitat/example/**/*" # Do not push new image for local dev testing of new aibitat images.
      - "docker/vex/*" # CVE exceptions we know are not in risk

  workflow_dispatch:

jobs:
  push_to_dev:
    name: Push DEVELOP Docker image to registry
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main-dev'
    permissions:
      packages: write
      contents: read
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@65b78e6e13532edd9afa3aa52ac7964289d1a9c1
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.ISTLEGAL_GITHUB_TOKEN }}

      - name: Get lowercase repository name
        id: repo_lc
        run: echo "name=$(echo '${{ github.repository }}' | tr '[:upper:]' '[:lower:]')" >> $GITHUB_OUTPUT

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@9ec57ed1fcdbf14dcef7dfbe97b2010124a938b7
        with:
          images: |
            ghcr.io/${{ github.repository }}
          tags: |
            type=raw,value=dev
            type=ref,event=branch
            type=ref,event=tag
            type=ref,event=pr

      - name: Build and push multi-platform Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/Dockerfile
          sbom: true
          provenance: mode=max
          push: true
          # platforms: linux/amd64,linux/arm64
          platforms: linux/amd64
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=registry,ref=ghcr.io/${{ steps.repo_lc.outputs.name }}:buildcache-main-dev
          cache-to: type=registry,ref=ghcr.io/${{ steps.repo_lc.outputs.name }}:buildcache-main-dev,mode=max
#      - name: Deploy to AWS
#        uses: appleboy/ssh-action@master
#        with:
#          host: ${{ secrets.AWS_HOST_DEV || '***********' }}
#          username: ${{ secrets.AWS_USERNAME_DEV || 'ec2-user' }}
#          key: ${{ secrets.AWS_SSH_KEY || '' }}
#          script: |
#            sudo docker stop istlegal || true
#            sudo docker rm istlegal || true
#            sudo docker pull ghcr.io/rahswe/istlegal:test
#            sudo docker run -d -p 80:3001 -p 443:3001 -p 3001:3001 --cap-add SYS_ADMIN --name istlegal -v /home/<USER>/ISTLegal:/app/server/storage -v /home/<USER>/ISTLegal/.env:/app/server/.env -e STORAGE_DIR=/app/server/storage ghcr.io/rahswe/istlegal:develop
#            ! lsof -i :8080 > /dev/null && cd mkdir -p /home/<USER>/istlegal/hotdir && cd /home/<USER>/istlegal/hotdir && python3 -m http.server 8080

