---
description: Guidelines for the Collector extension system
globs: collector/extensions/**/*.js
alwaysApply: false
---

# Collector Extension System

## Overview

The extension system in the Collector service provides specialized connectors for different data sources, enabling the processing of content from various platforms and services. This rule provides guidelines for working with and extending the extension system.

## Architecture

The extension system is organized as follows:

```
extensions/
├── index.js                 # Extension registration and routing
├── github/                  # GitHub extension
│   ├── index.js             # Main GitHub extension logic
│   ├── api.js               # GitHub API integration
│   └── processor.js         # GitHub content processor
├── youtube/                 # YouTube extension
│   ├── index.js             # Main YouTube extension logic
│   ├── api.js               # YouTube API integration
│   └── processor.js         # YouTube content processor
├── confluence/              # Confluence extension
│   ├── index.js             # Main Confluence extension logic
│   ├── api.js               # Confluence API integration
│   └── processor.js         # Confluence content processor
├── resync/                  # Resync extension
│   ├── index.js             # Main resync extension logic
│   └── scheduler.js         # Resync scheduling logic
└── utils/                   # Shared extension utilities
    ├── auth.js              # Authentication utilities
    ├── cache.js             # Caching utilities
    └── rate-limiter.js      # Rate limiting utilities
```

## Extension Registration

Extensions are registered in the main `extensions/index.js` file:

```javascript
// extensions/index.js
const express = require('express');
const router = express.Router();

// Import extensions
const githubExtension = require('./github');
const youtubeExtension = require('./youtube');
const confluenceExtension = require('./confluence');
const resyncExtension = require('./resync');

// Register extensions
router.use('/github', githubExtension);
router.use('/youtube', youtubeExtension);
router.use('/confluence', confluenceExtension);
router.use('/resync', resyncExtension);

// Export router
module.exports = router;
```

## Standard Extension Structure

Each extension should follow this standard structure:

### 1. Route Handler (index.js)

```javascript
// extensions/example/index.js
const express = require('express');
const router = express.Router();
const processor = require('./processor');

// Define routes
router.post('/process', async (req, res) => {
  try {
    const result = await processor.process(req.body);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
```

### 2. Processor (processor.js)

```javascript
// extensions/example/processor.js
const api = require('./api');

async function process(params) {
  // Validate parameters
  if (!params.url) {
    throw new Error('URL is required');
  }

  // Fetch content
  const content = await api.fetchContent(params.url);

  // Process content
  const processedContent = processContent(content);

  // Return result
  return {
    metadata: {
      source_url: params.url,
      processed_at: new Date().toISOString(),
      // Additional metadata...
    },
    chunks: processedContent.chunks,
  };
}

function processContent(content) {
  // Process content and split into chunks
  // ...
  return { chunks: [] };
}

module.exports = {
  process,
};
```

### 3. API Integration (api.js)

```javascript
// extensions/example/api.js
const axios = require('axios');
const cache = require('../utils/cache');
const rateLimiter = require('../utils/rate-limiter');

async function fetchContent(url) {
  // Check cache
  const cachedContent = await cache.get(url);
  if (cachedContent) {
    return cachedContent;
  }

  // Apply rate limiting
  await rateLimiter.acquire();

  try {
    // Fetch content
    const response = await axios.get(url);
    const content = response.data;

    // Cache content
    await cache.set(url, content);

    return content;
  } finally {
    // Release rate limiter
    rateLimiter.release();
  }
}

module.exports = {
  fetchContent,
};
```

## Core Extensions

### GitHub Extension

The GitHub extension processes content from GitHub repositories:

- **Capabilities**:
  - Clone entire repositories
  - Process individual files or directories
  - Handle different branches and tags
  - Process README files and documentation
  - Extract code with proper syntax highlighting

- **Endpoints**:
  - `POST /extensions/github/repo`: Process a GitHub repository
  - `POST /extensions/github/file`: Process a specific file from GitHub
  - `POST /extensions/github/directory`: Process a specific directory from GitHub

### YouTube Extension

The YouTube extension processes content from YouTube videos:

- **Capabilities**:
  - Extract video metadata (title, description, etc.)
  - Retrieve video transcripts
  - Process transcript with timestamp information
  - Handle multiple languages
  - Support for playlists and channels

- **Endpoints**:
  - `POST /extensions/youtube/video`: Process a YouTube video
  - `POST /extensions/youtube/playlist`: Process a YouTube playlist
  - `POST /extensions/youtube/channel`: Process a YouTube channel

### Confluence Extension

The Confluence extension processes content from Confluence pages and spaces:

- **Capabilities**:
  - Extract page content and metadata
  - Process attachments and embedded content
  - Handle page hierarchies and relationships
  - Support for spaces and collections
  - Extract comments and page history

- **Endpoints**:
  - `POST /extensions/confluence/page`: Process a Confluence page
  - `POST /extensions/confluence/space`: Process a Confluence space
  - `POST /extensions/confluence/search`: Process Confluence search results

### Resync Extension

The resync extension handles document resyncing for watched sources:

- **Capabilities**:
  - Check for updates to previously processed sources
  - Process only changed content
  - Maintain version history
  - Support for scheduled resyncing
  - Handle different source types (GitHub, YouTube, Confluence, etc.)

- **Endpoints**:
  - `POST /extensions/resync/check`: Check for updates to a source
  - `POST /extensions/resync/process`: Process updates to a source
  - `POST /extensions/resync/schedule`: Schedule regular resyncing for a source

## Implementing New Extensions

To implement a new extension:

### 1. Create Extension Directory

Create a new directory in the `extensions` folder:

```
extensions/
└── new-extension/
    ├── index.js             # Main extension logic
    ├── api.js               # API integration
    └── processor.js         # Content processor
```

### 2. Implement Extension Logic

Implement the extension logic in the files:

- `index.js`: Define routes and handle requests
- `api.js`: Integrate with external APIs
- `processor.js`: Process content and generate output

### 3. Register Extension

Register the extension in `extensions/index.js`:

```javascript
const newExtension = require('./new-extension');
router.use('/new-extension', newExtension);
```

## Best Practices

### Security

- Securely store API keys and tokens
- Use environment variables for sensitive information
- Implement proper token rotation and management
- Use the principle of least privilege
- Validate all input parameters
- Sanitize URLs and other inputs
- Implement proper error handling
- Prevent injection attacks

### Performance

- Respect API rate limits
- Implement backoff strategies
- Cache results when appropriate
- Monitor usage and quotas
- Use streaming for large content
- Process content in chunks
- Optimize resource usage
- Clean up temporary files

### Error Handling

- Implement comprehensive error handling
- Provide meaningful error messages
- Log errors for debugging
- Fail gracefully when possible
- Handle transient errors with retries
- Implement circuit breakers for external services

### Documentation

- Document extension capabilities and limitations
- Provide usage examples
- Document configuration options
- Keep documentation up-to-date
- Document API endpoints and parameters
- Include error codes and handling

## Testing

- Test with various input types and sizes
- Test error handling and edge cases
- Test rate limiting and caching
- Test authentication and authorization
- Test with real-world data
- Implement integration tests
- Monitor performance and resource usage
