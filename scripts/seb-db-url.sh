#!/bin/bash
# This script checks for the legacy DB file and sets DATABASE_URL accordingly.
# It assumes it is run from the project root directory.

DB_PATH_LEGACY="server/storage/anythingllm.db"
DB_PATH_NEW="server/storage/istlegal.db"
DEFAULT_DB_URL="file:../storage/istlegal.db" # Prisma URL relative to server/prisma or server/
LEGACY_DB_URL="file:../storage/anythingllm.db"

# Check if the legacy DB file exists
if [ -f "$DB_PATH_LEGACY" ]; then
  export DATABASE_URL="$LEGACY_DB_URL"
  echo "[DB Setup] Legacy database 'anythingllm.db' found. Using it: $DATABASE_URL"
else
  export DATABASE_URL="$DEFAULT_DB_URL"
  echo "[DB Setup] Using default database 'istlegal.db': $DATABASE_URL"
fi