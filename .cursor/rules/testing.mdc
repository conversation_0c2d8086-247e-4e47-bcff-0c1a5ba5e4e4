---
description: Guidelines for testing in the frontend
globs: "frontend/**/*.{js,jsx,test.js}"
alwaysApply: false
---
# Testing Guidelines

## Overview

Testing is an essential part of the development process. This document provides guidelines for writing and running tests in the ISTLegal frontend application.

## Test Structure

1. **Place tests in `__tests__` directories**: Tests should be placed in a `__tests__` directory adjacent to the code they're testing.

```
src/
├── stores/
│   ├── __tests__/
│   │   └── attachmentStore.test.js
│   └── attachmentStore.js
```

2. **Use descriptive test names**: Test names should clearly describe what is being tested.

```javascript
// Good
it('should add an attachment to the store', () => {});

// Bad
it('test attachment', () => {});
```

3. **Follow the AAA pattern**: Arrange, Act, Assert.

```javascript
it('should add an attachment', () => {
  // Arrange
  const attachment = { id: '1', name: 'test.pdf' };

  // Act
  act(() => {
    useAttachmentStore.getState().addAttachment(attachment);
  });

  // Assert
  expect(useAttachmentStore.getState().attachments).toEqual([attachment]);
});
```

## Testing Zustand Stores

1. **Reset the store before each test**: Use `beforeEach` to reset the store to its initial state.

```javascript
beforeEach(() => {
  act(() => {
    useAttachmentStore.getState().clearAttachments();
  });
});
```

2. **Use `act` for state updates**: Wrap state updates in `act()` to ensure all updates are processed before assertions.

```javascript
act(() => {
  useAttachmentStore.getState().addAttachment(attachment);
});
```

3. **Test selectors and actions**: Test both the store state and the selector hooks.

```javascript
// Test store state
expect(useAttachmentStore.getState().attachments).toEqual([attachment]);

// Test selector hooks
const { result } = renderHook(() => useAttachments());
expect(result.current).toEqual([attachment]);
```

### 4. Testing Selectors with Equality Checks

Testing Zustand selectors that use custom equality functions (the second argument to `useStore`) with `@testing-library/react`'s `renderHook` can sometimes be problematic.

-   **Potential Issue**: You might encounter "Maximum update depth exceeded" errors, especially if the selector returns objects or arrays and the equality check logic interacts unexpectedly with the testing environment's rendering cycle. This seemed to occur even when the equality check logic (e.g., comparing timestamps or shallow properties) appeared correct.
-   **Symptom**: The error often originates directly from the `renderHook` call for the specific selector being tested.
-   **Alternative Patterns**:
    -   **Test state directly**: Instead of rendering the problematic selector hook separately, render the base store hook (`useAttachmentStore` in our example) and assert the relevant state properties (`lastAction`, `lastActionData`) directly on its result after performing actions within `act()`. This proved more reliable in `attachmentStore.test.js`.
    -   **Use `waitFor`**: While it didn't resolve the issue in our specific case, using `waitFor` after an action to wait for the hook's result to meet an expectation can sometimes help with timing issues, though it might not fix the root cause if it's related to the `renderHook` interaction itself.
-   **Last Resort**: If a specific selector test remains persistently problematic despite trying these patterns, consider skipping it (`it.skip(...)`) if other tests provide sufficient confidence in the store's overall functionality.

## Running Tests

> Run frontend tests from the `frontend` directory.

```bash
# Run all frontend tests
cd frontend && npm test

# Run tests in watch mode
cd frontend && npm run test:watch

# Run tests with coverage
cd frontend && npm run test:coverage
```

## Coverage Requirements

- Aim for at least 80% coverage for all new code
- Ensure all critical paths are covered
- Focus on testing behavior, not implementation details

## Documentation

For more detailed information, see the [Testing Documentation](mdc:frontend/docs/testing.md).

## UI Component Testing

When creating or updating UI components (e.g., Button, Input, Switch, Modal, Checkbox, Slider, Tab):
- Place unit tests under a `__tests__` folder adjacent to the component file.
- Use React Testing Library to render components and simulate user interactions.
- Cover both controlled and uncontrolled patterns (if the component supports both).
- Verify keyboard accessibility: focus behavior and activation via Space/Enter.
- Assert correct ARIA attributes (e.g., `aria-checked`, `aria-labelledby`, `aria-describedby`).
- Test all component variants (sizes, disabled state, label positions, themes).

## Backend Prisma Testing

When running Jest tests that import `@prisma/client` a native query-engine binary is normally loaded.  On CI or developer workstations where the binary may be missing (especially on Apple-silicon), this can cause `PrismaClientInitializationError` failures and slow test start-up times.

### Default Behaviour (Unit Tests)
1. A manual Jest mock lives in `server/__mocks__/@prisma/client.js`.
2. It provides a lightweight stub `PrismaClient` class with no-op methods and **no native engine**.
3. All server-side unit tests therefore use the stub automatically and stay fast & deterministic.

```js
// server/__mocks__/@prisma/client.js (excerpt)
class PrismaClient {
  async $connect() {}
  async $disconnect() {}
  $use() {}
  $on() {}
  // add model stubs as needed e.g.
  // this.user = { findMany: jest.fn() };
}
module.exports = { PrismaClient };
```

### Writing Integration / Query-Level Tests
If a particular test needs real Prisma behaviour:

```js
// myIntegration.test.js
jest.unmock("@prisma/client");      // disable manual mock for this file
const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
```

or load the actual implementation alongside the mock:

```js
const { PrismaClient: RealPrisma } = jest.requireActual("@prisma/client");
```

### Extending the Stub
For tests that only need to assert that a model method was *called* (not executed):

```js
// inside a test file – extend stub on the fly
const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
prisma.user = { findMany: jest.fn() };
...
expect(prisma.user.findMany).toHaveBeenCalledWith({ where: {...} });
```

### Summary
• **Unit tests** – rely on the stub, no native binary required.
• **Integration tests** – call `jest.unmock('@prisma/client')` to get the real client.
• **Selective stubbing** – add or spy on model methods as needed inside the stub.
