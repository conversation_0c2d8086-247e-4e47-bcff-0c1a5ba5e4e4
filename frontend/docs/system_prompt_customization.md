# System Prompt Customization

## Overview

The system prompt customization feature allows users to override the default system prompt with their own custom prompt. This feature provides a hierarchy of prompts to ensure users understand what prompt is being used.

## Feature Location

The system prompt edit functionality is accessible through:

- **Location**: Chat interface prompt actions
- **Component**: `SystemPromptEditButton` in `PromptActions`
- **Modal**: `SystemPromptEditModal`

## Prompt Hierarchy

The system uses a hierarchical approach to determine which prompt to use:

1. **User Custom Prompt** (highest priority) - User's personal override
2. **Workspace Prompt** (medium priority) - Workspace-specific prompt
3. **System Default Prompt** (lowest priority) - Application default

## User Interface

### System Prompt Edit Button

- **Icon**: Gear/settings icon
- **Tooltip**: "Customize your system prompt"
- **Location**: In the prompt actions area of the chat interface

### System Prompt Edit Modal

The modal displays:

1. **Current System Prompt Section**:

   - Shows the base prompt that would be used without user customization
   - Displays workspace prompt if different from default
   - Read-only display for context

2. **Status Indicators**:

   - Blue text: "Your custom prompt below overrides this base prompt" (when user has custom prompt)
   - Green text: "Using workspace prompt" (when workspace has custom prompt)
   - Gray text: "Using system default prompt" (when using default)

3. **Custom Prompt Section**:

   - Textarea for user's custom prompt
   - Character limit: 10,000 characters
   - Placeholder text guides user input

4. **Action Buttons**:
   - **Save Custom Prompt**: Saves the user's custom prompt
   - **Clear Custom Prompt**: Removes user's custom prompt
   - **Cancel**: Closes modal without changes

## Technical Implementation

### Backend Endpoints

- **GET** `/api/user/custom-system-prompt`: Retrieves user's custom prompt and default prompt
- **POST** `/api/user/custom-system-prompt`: Saves user's custom prompt
- **DELETE** `/api/user/custom-system-prompt`: Clears user's custom prompt

### Database Schema

The feature uses the `custom_system_prompt` field in the `users` table:

- **Type**: TEXT (nullable)
- **Purpose**: Stores user's custom system prompt
- **Default**: NULL (no custom prompt)

### Frontend Components

1. **SystemPromptEditButton** (`frontend/src/components/WorkspaceChat/ChatContainer/PromptInput/PromptActions/SystemPromptEditButton/index.jsx`)

   - Renders the edit button
   - Handles modal opening
   - Receives workspace prop for context

2. **SystemPromptEditModal** (`frontend/src/components/WorkspaceChat/ChatContainer/PromptInput/PromptActions/SystemPromptEditModal/index.jsx`)
   - Main modal component
   - Handles prompt loading, saving, and clearing
   - Displays prompt hierarchy
   - Manages form state and validation

### State Management

- Uses `useUser` hook for user state management
- Modal state managed with `useModal` hook
- Form state managed with local React state
- Updates user state after successful operations

## User Experience

### Workflow

1. **Opening the Modal**:

   - User clicks the system prompt edit button
   - Modal loads current prompts and user's custom prompt (if any)

2. **Viewing Current Prompt**:

   - User sees the base prompt that would be used
   - Clear indication of prompt hierarchy
   - Context about workspace vs. default prompts

3. **Customizing Prompt**:

   - User enters their custom prompt in the textarea
   - Real-time character count validation
   - Clear indication that custom prompt overrides base prompt

4. **Saving Changes**:

   - User clicks "Save Custom Prompt"
   - Success message confirms save
   - Modal closes and user state updates

5. **Clearing Custom Prompt**:
   - User clicks "Clear Custom Prompt"
   - Confirmation and success messaging
   - Reverts to base prompt (workspace or default)

### Visual Feedback

- **Loading States**: Spinner during API calls
- **Success Messages**: Toast notifications for successful operations
- **Error Handling**: Error messages for failed operations
- **Character Limit**: Visual indicator of character count and limit

## Internationalization

The feature is fully internationalized with translation keys in the `system-prompt-edit` namespace:

- Button labels and tooltips
- Modal title and section headers
- Status messages and indicators
- Success and error messages
- Placeholder text and help text

Supported languages: English, Swedish, French, German, Norwegian, Polish, Kinyarwanda

## Security Considerations

- **Authentication**: All endpoints require valid JWT authentication
- **Authorization**: Users can only modify their own custom prompts
- **Input Validation**: Character limits and content validation
- **XSS Prevention**: Proper input sanitization and output encoding

## Error Handling

The feature includes comprehensive error handling:

- **Network Errors**: Graceful handling of API failures
- **Validation Errors**: Character limit enforcement
- **Authentication Errors**: Proper error messages for auth failures
- **Server Errors**: Generic error messages for internal server errors

## Testing

### Manual Testing Checklist

1. **Modal Functionality**:

   - Modal opens and closes correctly
   - Scrolling works for long content
   - Form validation works

2. **Prompt Hierarchy**:

   - Correct display of workspace vs. default prompts
   - Proper status indicators
   - Accurate prompt precedence

3. **CRUD Operations**:

   - Save custom prompt works
   - Clear custom prompt works
   - Load existing custom prompt works

4. **Error Scenarios**:
   - Network failures handled gracefully
   - Invalid input rejected
   - Authentication errors handled

### Integration Testing

- Test with different user roles
- Test with workspaces that have custom prompts
- Test character limit enforcement
- Test internationalization in different languages

## Related Files

### Frontend

- `frontend/src/components/WorkspaceChat/ChatContainer/PromptInput/PromptActions/SystemPromptEditButton/index.jsx`
- `frontend/src/components/WorkspaceChat/ChatContainer/PromptInput/PromptActions/SystemPromptEditModal/index.jsx`
- `frontend/src/hooks/useUser.js`
- `frontend/src/locales/*/common.js` (translation files)

### Backend

- `server/endpoints/userCustomSystemPrompt.js`
- `server/models/user.js`
- `server/utils/http/index.js`
- `server/utils/middleware/validatedRequest.js`

### Database

- `server/prisma/schema.prisma` (users table with custom_system_prompt field)

## Future Enhancements

Potential improvements for the feature:

1. **Prompt Templates**: Pre-defined prompt templates for common use cases
2. **Prompt History**: Version history of user's custom prompts
3. **Prompt Sharing**: Ability to share custom prompts with team members
4. **Advanced Validation**: More sophisticated prompt validation and suggestions
5. **Prompt Testing**: Preview functionality to test prompts before saving
