export default {
  // =========================
  // PRIVACY & DATA-HANDLING
  // =========================
  privacy: {
    title: "Ubwiru & Gukorana n'Amakuru",
    description:
      "Aya ni amagenamiterere yawe ku buryo abatanga serivisi n'iyi platform bakorana n'amakuru yawe.",
    llm: "Guhitamo LLM",
    embedding: "Ihitamo rya Embedding",
    vector: "Database ya Vectors",
    anonymous: "Telemetry y'Ubwiru yashyizweho",
    "desc-event":
      "Ibikorwa byose ntibizandika aderesi ya IP kandi ntibikubiyemo",
    "desc-id": "amakuru atagaragara",
    "desc-cont":
      "ibiri mu biganiro, settings, ibiganiro, cyangwa andi makuru atari ay'ikoreshwa. Reba urutonde rw'ibikorwa bikusanywa kuri",
    "desc-git": "Github hano",
    "desc-end":
      "Niba uhagaritse telemetry, turagusaba kuduha ibitekerezo kugirango dukomeze kunoza platform.",
  },
  // =========================
  // LLM SELECTION PRIVACY
  // =========================
  "llm-selection-privacy": {
    // We can add short translations or keep them minimal if previously missing.
    openai: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Ibyifuzo byawe n'inyandiko zawe zikoreshwa mu gusubiza bikagaragara kuri OpenAI",
      ],
    },
    azure: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Inyandiko n'ibyo winjiza ntibigaragara kuri OpenAI cyangwa Microsoft",
      ],
    },
    anthropic: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Ibyifuzo byawe n'inyandiko zawe zikoreshwa mu gusubiza bikagaragara kuri Anthropic",
      ],
    },
    gemini: {
      description: [
        "Ibiganiro byawe byavanwemo imyirondoro kandi bikaba bikoreshwa mu mahugurwa",
        "Ibyifuzo byawe n'inyandiko zawe bikagaragara kuri Google",
      ],
    },
    lmstudio: {
      description: [
        "Modeli yawe n'ibiganiro byawe biboneka kuri server iriho LMStudio",
      ],
    },
    localai: {
      description: [
        "Modeli yawe n'ibiganiro byawe biboneka kuri server ikoresha LocalAI",
      ],
    },
    ollama: {
      description: [
        "Modeli yawe n'ibiganiro byawe biboneka kuri mashini iriho Ollama",
      ],
    },
    native: {
      description: ["Modeli yawe n'ibiganiro byawe biboneka kuri iyi instance"],
    },
    togetherai: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Ibyifuzo n'inyandiko zawe biragaragara kuri TogetherAI",
      ],
    },
    mistral: {
      description: ["Ibyifuzo byawe n'inyandiko bikagaragara kuri Mistral"],
    },
    huggingface: {
      description: [
        "Ibyifuzo byawe n'inyandiko zawe zo gusubiza bizajya kuri endpoint ya HuggingFace wakoresheje",
      ],
    },
    perplexity: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Ibyifuzo byawe n'inyandiko zawe bikagaragara kuri Perplexity AI",
      ],
    },
    openrouter: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Ibyifuzo byawe n'inyandiko zawe zikoreshwa bikagaragara kuri OpenRouter",
      ],
    },
    groq: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Ibyifuzo byawe n'inyandiko zawe bikagaragara kuri Groq",
      ],
    },
    koboldcpp: {
      description: [
        "Modeli yawe n'ibiganiro byawe biboneka kuri server iriho KoboldCPP",
      ],
    },
    textgenwebui: {
      description: [
        "Modeli yawe n'ibiganiro byawe biboneka kuri server iriho Oobabooga Text Generation Web UI",
      ],
    },
    "generic-openai": {
      description: [
        "Amakuru asangirwa hakurikijwe amabwiriza ya serivisi waba uhuje nayo.",
      ],
    },
    cohere: {
      description: [
        "Amakuru asangirwa hakurikijwe amabwiriza ya cohere.com n'amategeko agenga aho utuye.",
      ],
    },
    litellm: {
      description: [
        "Modeli yawe n'ibiganiro byawe biboneka kuri server iriho LiteLLM",
      ],
    },
  },

  // =========================
  // VECTOR DATABASE PRIVACY
  // =========================
  "vector-db-privacy": {
    chroma: {
      description: [
        "Vectors zawe n'inyandiko ziba zibitswe kuri Chroma yawe",
        "Kwinjira kuri instance yawe kugenwa nawe ubwawe",
      ],
    },
    pinecone: {
      description: [
        "Vectors zawe n'inyandiko zibitswe kuri server za Pinecone",
        "Kwinjira kuri data yawe bikagenwa na Pinecone",
      ],
    },
    qdrant: {
      description: [
        "Vectors zawe n'inyandiko zibitswe kuri Qdrant (cloud cyangwa self-hosted)",
      ],
    },
    weaviate: {
      description: [
        "Vectors zawe n'inyandiko zibitswe kuri Weaviate (cloud cyangwa self-hosted)",
      ],
    },
    milvus: {
      description: [
        "Vectors zawe n'inyandiko zibitswe kuri Milvus (cloud cyangwa self-hosted)",
      ],
    },
    zilliz: {
      description: [
        "Vectors zawe n'inyandiko zibitswe kuri Zilliz cloud cluster.",
      ],
    },
    astra: {
      description: [
        "Vectors zawe n'inyandiko zibitswe kuri AstraDB yawe iri kuri cloud.",
      ],
    },
    lancedb: {
      description: [
        "Vectors zawe n'inyandiko zibitswe hano kuri iyi instance ya platform",
      ],
    },
  },

  // =========================
  // EMBEDDING ENGINE PRIVACY
  // =========================
  "embedding-engine-privacy": {
    native: {
      description: [
        "Inyandiko zawe zishyirwa muri embedding kuri iyi platform",
      ],
    },
    openai: {
      description: [
        "Inyandiko zawe zoherezwa kuri server za OpenAI",
        "Inyandiko zawe ntizikoreshwa mu mahugurwa",
      ],
    },
    azure: {
      description: [
        "Inyandiko zawe zoherezwa kuri Microsoft Azure service",
        "Inyandiko zawe ntizikoreshwa mu mahugurwa",
      ],
    },
    localai: {
      description: [
        "Inyandiko zawe zishyirwa muri embedding kuri server ikoresha LocalAI",
      ],
    },
    ollama: {
      description: [
        "Inyandiko zawe zishyirwa muri embedding kuri server iriho Ollama",
      ],
    },
    lmstudio: {
      description: [
        "Inyandiko zawe zishyirwa muri embedding kuri server iriho LMStudio",
      ],
    },
    cohere: {
      description: [
        "Amakuru asangirwa hakurikijwe amabwiriza ya cohere.com n'amategeko agenga aho utuye.",
      ],
    },
    voyageai: {
      description: [
        "Amakuru yoherezwa kuri Voyage AI asangirwa hakurikijwe amabwiriza ya voyageai.com.",
      ],
    },
  },
};
