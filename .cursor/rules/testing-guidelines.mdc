---
description:
globs:
alwaysApply: false
---
# Testing Guidelines

This document outlines the testing strategies and best practices for the ISTLegal project, covering both frontend and backend.

## General Principles

- **Write Testable Code:** Design components, functions, and classes with testability in mind. Favor pure functions, dependency injection, and clear interfaces.
- **Test Coverage:** Aim for reasonable test coverage, focusing on critical paths, business logic, and potential edge cases. Strive for quality over quantity.
- **Automation:** Automate tests as much as possible using CI/CD pipelines to ensure consistent checks on commits and pull requests.
- **Clarity:** Write clear, descriptive test names that explain what is being tested and the expected outcome.

## Frontend Testing

- **Tools:**
    - **Jest:** Primary framework for unit and integration tests.
    - **React Testing Library (RTL):** For testing React components by interacting with them as a user would.
    - **MSW (Mock Service Worker):** Recommended for mocking API requests in tests (if not already implemented).
- **Unit Tests:**
    - Test individual React components, hooks, and utility functions in isolation.
    - Use RTL to render components and assert their output or behavior based on props and state.
    - Focus on testing the component's public interface and behavior, not implementation details.
    - **UI Component Testing:**
      - Place tests under a `__tests__` directory next to each component (e.g., `Switch/__tests__`).
      - Target core UI components: Button, Input, Switch, Modal, Checkbox, Slider, Tabs, Dropdown, TabButton.
      - Cover both controlled and uncontrolled usage patterns where applicable.
      - Verify keyboard accessibility and focus states (Space/Enter activation, focus ring).
      - Assert correct ARIA attributes (`aria-checked`, `aria-labelledby`, `aria-describedby`).
      - Test all variants: sizes, label positions, disabled state, themes.
- **Integration Tests:**
    - Test the interaction between multiple components or components with hooks/context.
    - Use RTL to render a feature or page section and simulate user interactions (clicks, typing).
    - Mock API calls using MSW or Jest's mocking capabilities to control responses and test how components handle different data states (loading, success, error).
- **End-to-End (E2E) Tests:**
    - *(Currently Minimal/Future Goal)* If implemented, use frameworks like Cypress or Playwright.
    - Test critical user flows across the entire application stack.

## Backend Testing

- **Tools:**
    - **Jest:** Primary framework for unit and integration tests.
    - **Supertest:** For testing API endpoints (integration tests).
    - **Prisma Mocking/Test Database:** Recommended for isolating database interactions.
- **Unit Tests:**
    - Test individual utility functions, helpers, and service modules in isolation.
    - Mock external dependencies (like database calls, other services, or external APIs) using Jest's mocking features.
    - Focus on testing business logic within the unit.
- **Integration Tests:**
    - Test the interaction between different backend modules (e.g., API endpoint -> service -> model).
    - Use Supertest to make requests to API endpoints and assert the responses.
    - Use a test database or Prisma mocking to test database interactions without affecting the development DB.
    - Test middleware functions individually or within endpoint tests.

## Running Tests

- **Backend Tests:**
    - Navigate to the `server` directory: `cd server`
    - Run the test script: `npm test` or `npm run test`
    - This command executes Jest based on the configuration in `server/package.json`.
- **Frontend Tests:**
    - Navigate to the `frontend` directory: `cd frontend`
    - Run tests with `npm test`, or `npm run test:watch` for watch mode, or `npm run test:coverage` for coverage.

## Test Organization

- **Location:** Place test files alongside the code they are testing (e.g., `MyComponent.jsx` and `MyComponent.test.jsx` in the same directory).
- **Naming:** Use the `.test.js` or `.test.jsx` suffix for test files.
- **Structure:** Use `describe` blocks to group related tests for a component or module, and `it` or `test` blocks for individual test cases.

## Best Practices

- **Arrange, Act, Assert (AAA):** Structure tests clearly:
    - **Arrange:** Set up the test conditions (render component, mock data).
    - **Act:** Perform the action being tested (click button, call function).
    - **Assert:** Verify the outcome (check component output, assert function return value).
- **Avoid Testing Implementation Details:** Focus on the public API and user-visible behavior.
- **Keep Tests Independent:** Tests should not depend on the order of execution or the state left by previous tests.
- **Use Mocks Effectively:** Mock external dependencies to isolate the unit under test and ensure deterministic results.
- **Clean Up:** Ensure any mocks, spies, or stubs are restored after each test.
