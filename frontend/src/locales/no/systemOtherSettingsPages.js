export default {
  // Bsed on this order of pages in system settings:
  // Customization (Appearance)
  //Admin: System settings
  //Admin: Users
  //Admin: Invites
  //Admin: Workspaces
  //Admin: MCP Servers
  //Admin: Feedback List
  //Admin: Workspace chats
  //Admin: Default system
  //Admin: Document Builder
  //Admin: DD Prompt settings
  //Admin: Chat UI settings
  //AI-Providers: LLM
  //AI-Providers: Prompt Upgrade LLM
  //AI-Providers: Validation LLM
  //AI-Providers: CDB LLM
  //AI-Providers: Custom User AI
  //AI-Providers:  Vector Database
  //AI-Providers: Re-ranking settings
  //AI-Providers: Deep search
  //AI-Providers: Embedder
  //AI-Providers: Text Splitter & Chunking
  //AI-Providers: Voice & Speech
  //AI-Providers: transcription
  //AI-Providers: PDR System
  //Agent skills
  //Tools: Chat Embed History
  //Tools: Chat Embed
  //Tools: Event logs
  //Tools: Developer API
  //Tools: Browser Extension
  //Security
  //Document Drafting: Configuration
  //Document Drafting: Drafting LLM
  //Document Drafting: Chat Settings

  // =========================
  // LLM PREFERENCE PAGE
  // =========================
  llm: {
    title: "LLM-preferanse",
    description:
      "Dette er legitimasjonen og innstillingene for din foretrukne LLM-chat- og embedding-leverandør. Det er viktig at disse nøklene er oppdaterte og korrekte, ellers vil ikke systemet fungere riktig.",
    provider: "LLM-leverandør",
    "secondary-provider": "Sekundær LLM-leverandør",
    "none-selected": "Ingen valgt",
    "select-llm": "Agenter vil ikke fungere før et gyldig valg er gjort.",
    "search-llm": "Søk i alle LLM-leverandører",
    "context-window-warning":
      "Advarsel: Kunne ikke hente kontekstvindu for den valgte modellen.",
    "context-window-waiting": " -- venter på kontekstvindu-informasjon -- ",
    "validation-prompt": {
      disable: {
        label: "Deaktiver valideringsprompt",
        description:
          "Når aktivert vil valideringsknappen ikke vises i brukergrensesnittet.",
      },
    },
    "prompt-upgrade": {
      title: "LLM-leverandør for promptoppgradering",
      description:
        "Den spesifikke LLM-leverandøren og modellen som vil bli brukt for å oppgradere brukerprompt. Som standard brukes systemets LLM-leverandør og innstillinger.",
      search: "Søk tilgjengelige LLM-leverandører for funksjonen",
      template: "Mal for promptoppgradering",
      "template-description":
        "Denne malen vil bli brukt ved oppgradering av prompts. Bruk {{prompt}} for å referere til teksten som skal oppgraderes.",
      "template-placeholder":
        "Skriv inn malen som skal brukes for å oppgradere prompts...",
      "template-hint":
        "Eksempel: Vennligst oppgrader følgende tekst mens du beholder betydningen: {{prompt}}",
    },
    "logo-alt": "{{name}} logo",
    "context-window": "Kontekstvindu",
    "default-context-window": "(standardstørrelse for denne leverandøren)",
    tokens: "tokens",
    "save-error": "Kunne ikke lagre LLM-innstillinger",
  },

  // =========================
  // LLM PROVIDER DESCRIPTIONS
  // =========================
  "llm-provider": {
    openai: "Standardvalget for de fleste ikke-kommersielle bruksområder.",
    azure: "Enterprise-valget av OpenAI hostet på Azure-tjenester.",
    anthropic: "En vennlig AI-assistent hostet av Anthropic.",
    gemini: "Googles største og mest kapable AI-modell",
    huggingface:
      "Få tilgang til 150 000+ åpen kildekode LLMer og verdens AI-fellesskap",
    ollama: "Kjør LLMer lokalt på din egen maskin.",
    lmstudio:
      "Oppdag, last ned og kjør tusenvis av banebrytende LLMer med få klikk.",
    localai: "Kjør LLMer lokalt på din egen maskin.",
    togetherai: "Kjør åpen kildekode-modeller fra Together AI.",
    mistral: "Kjør åpen kildekode-modeller fra Mistral AI.",
    perplexityai:
      "Kjør kraftige og internett-tilkoblede modeller hostet av Perplexity AI.",
    openrouter: "Et enhetlig grensesnitt for LLMer.",
    groq: "Den raskeste LLM-inferensen tilgjengelig for sanntids-AI-applikasjoner.",
    koboldcpp: "Kjør lokale LLMer ved hjelp av koboldcpp.",
    oobabooga:
      "Kjør lokale LLMer ved hjelp av Oobaboogas Text Generation Web UI.",
    cohere: "Kjør Coheres kraftige Command-modeller.",
    lite: "Kjør LiteLLMs OpenAI-kompatible proxy for ulike LLMer.",
    "generic-openai":
      "Koble til enhver OpenAI-kompatibel tjeneste via tilpasset konfigurasjon",
    native:
      "Bruk en nedlastet tilpasset Llama-modell for chatting på denne instansen.",
    xai: "Kjør xAIs kraftige LLMer som Grok-2 og mer.",
    "aws-bedrock": "Kjør kraftige grunnmodeller privat med AWS Bedrock.",
    deepseek: "Kjør DeepSeeks kraftige LLMer.",
    fireworksai:
      "Den raskeste og mest effektive inferensmotoren for å bygge produksjonsklare, sammensatte AI-systemer.",
    bedrock: "Kjør kraftige grunnmodeller privat med AWS Bedrock.",
  },

  // =========================
  // CUSTOM USER AI
  // =========================
  "custom-user-ai": {
    title: "Tilpasset bruker-AI",
    settings: "Tilpasset bruker-AI",
    description: "Konfigurer tilpasset AI-leverandør",
    "custom-model-reference": "Tilpasset modellnavn og beskrivelse",
    "custom-model-reference-description":
      "Legg til en tilpasset referanse for denne modellen. Dette vil være synlig når du bruker den tilpassede bruker-AI-motorvelgeren i promptpanelet.",
    "custom-model-reference-name": "Tilpasset modellnavn",
    "custom-model-reference-description-label": "Modellbeskrivelse (Valgfritt)",
    "custom-model-reference-description-placeholder":
      "Skriv inn en valgfri beskrivelse for denne modellen",
    "custom-model-reference-name-placeholder":
      "Skriv inn et tilpasset navn for denne modellen",
    "model-ref-placeholder":
      "Skriv inn et tilpasset navn eller beskrivelse for dette modelloppsettet",
    "enter-custom-model-reference": "Angi et tilpasset navn for denne modellen",
    "standard-engine": "Standard AI-motor",
    "standard-engine-description":
      "Vår standardmotor som er nyttig for de fleste oppgaver",
    "dynamic-context-window-percentage":
      "Prosentandel for dynamisk kontekstvindu",
    "dynamic-context-window-percentage-desc":
      "Kontrollerer hvor mye av LLM-kontekstvinduet som kan brukes til ytterligere kilder (10-100%)",
    "no-alternative-title": "Ingen alternativ modell valgt",
    "no-alternative-desc":
      "Når dette alternativet er valgt, har ikke brukerne mulighet til å velge en alternativ modell.",
    "select-option": "Velg tilpasset AI-profil",
    tab: {
      "custom-1": "Tilpasset motor 1",
      "custom-2": "Tilpasset motor 2",
      "custom-3": "Tilpasset motor 3",
      "custom-4": "Tilpasset motor 4",
      "custom-5": "Tilpasset motor 5",
      "custom-6": "Tilpasset motor 6",
    },
    engine: {
      "custom-1": "Tilpasset motor 1",
      "custom-2": "Tilpasset motor 2",
      "custom-3": "Tilpasset motor 3",
      "custom-4": "Tilpasset motor 4",
      "custom-5": "Tilpasset motor 5",
      "custom-6": "Tilpasset motor 6",
      "custom-1-title": "Tilpasset motor 1",
      "custom-2-title": "Tilpasset motor 2",
      "custom-3-title": "Tilpasset motor 3",
      "custom-4-title": "Tilpasset motor 4",
      "custom-5-title": "Tilpasset motor 5",
      "custom-6-title": "Tilpasset motor 6",
      "custom-1-description": "Konfigurer innstillinger for Tilpasset motor 1",
      "custom-2-description": "Konfigurer innstillinger for Tilpasset motor 2",
      "custom-3-description": "Konfigurer innstillinger for Tilpasset motor 3",
      "custom-4-description": "Konfigurer innstillinger for Tilpasset motor 4",
      "custom-5-description": "Konfigurer innstillinger for Tilpasset motor 5",
      "custom-6-description": "Konfigurer innstillinger for Tilpasset motor 6",
    },
    "option-number": "Alternativ {{number}}",
    "llm-provider-selection": "LLM-leverandørvalg",
    "llm-provider-selection-desc":
      "Velg LLM-leverandøren for denne tilpassede AI-konfigurasjonen",
    "custom-option": "Tilpasset alternativ",
    saving: "Lagrer...",
    "save-changes": "Lagre endringer",
    "model-ref-saved": "Tilpassede modellinnstillinger lagret",
    "model-ref-save-failed":
      "Kunne ikke lagre tilpassede modellinnstillinger: {{error}}",
    "llm-settings-save-failed": "Kunne ikke lagre LLM-innstillinger: {{error}}",
    "settings-fetch-failed": "Kunne ikke hente innstillinger",
    "llm-saved": "LLM-innstillinger lagret",
    "select-provider-first":
      "Vennligst velg en LLM-leverandør for å konfigurere modellinnstillinger. Når den er konfigurert, vil dette alternativet være valgbart som en tilpasset AI-motor i brukergrensesnittet.",
  },

  // =========================
  // CDB LLM PREFERENCE
  // =========================
  "cdb-llm-preference": {
    title: "CDB LLM-preferanse",
    settings: "CDB LLM",
    description: "Konfigurer LLM-leverandøren for CDB",
  },
  // =========================
  // TEMPLATE LLM PREFERENCE
  // =========================
  "template-llm-preference": {
    title: "Mal-LLM-preferanse",
    settings: "Malen LLM",
    description:
      "Velg LLM-leverandøren som brukes for å generere dokumentmaler. Standard er systemleverandøren.",
    "toast-success": "Mal-LLM-innstillinger oppdatert",
    "toast-fail": "Kunne ikke oppdatere Mal-LLM-innstillinger",
    saving: "Lagrer...",
    "save-changes": "Lagre endringer",
  },

  // =========================
  // AUDIO PREFERENCE
  // =========================
  audio: {
    title: "Preferanse for tale-til-tekst",
    provider: "Leverandør",
    "system-native": "System innebygd",
    "desc-speech":
      "Her kan du spesifisere hvilken type tekst-til-tale og tale-til-tekst leverandører du vil bruke i plattformopplevelsen. Som standard bruker vi nettleserens innebygde støtte for disse tjenestene, men du kan ønske å bruke andre.",
    "title-text": "Preferanse for tekst-til-tale",
    "desc-text":
      "Her kan du spesifisere hvilken type tekst-til-tale leverandører du vil bruke i plattformopplevelsen. Som standard bruker vi nettleserens innebygde støtte for disse tjenestene, men du kan ønske å bruke andre.",
    "desc-config":
      "Ingen konfigurasjon nødvendig for nettleserens innebygde tekst-til-tale.",
    "placeholder-stt": "Søk etter tale-til-tekst leverandører",
    "placeholder-tts": "Søk etter tekst-til-tale leverandører",
    "native-stt": "Bruker nettleserens innebygde STT-tjeneste hvis støttet.",
    "native-tts": "Bruker nettleserens innebygde TTS-tjeneste hvis støttet.",
    "piper-tts": "Kjør TTS-modeller lokalt i nettleseren din privat.",
    "openai-description": "Bruk OpenAIs tekst-til-tale stemmer og teknologi.",
    openai: {
      "api-key": "API-nøkkel",
      "api-key-placeholder": "OpenAI API-nøkkel",
      "voice-model": "Stemmemodell",
    },
    elevenlabs: "Bruk ElevenLabs sine tekst-til-tale stemmer og teknologi.",
  },

  // =========================
  // TRANSCRIPTION PREFERENCE
  // =========================
  transcription: {
    title: "Preferanse for transkripsjonsmodell",
    description:
      "Dette er legitimasjonen og innstillingene for din foretrukne transkripsjonsmodell-leverandør. Det er viktig at disse nøklene er oppdaterte og korrekte, ellers vil ikke mediefiler og lyd bli transkribert.",
    provider: "Transkripsjonsleverandør",
    "warn-start":
      "Bruk av den lokale whisper-modellen på maskiner med begrenset RAM eller CPU kan stanse plattformen under behandling av mediefiler.",
    "warn-recommend":
      "Vi anbefaler minst 2GB RAM og filstørrelser under 10 MB.",
    "warn-end":
      "Den innebygde modellen vil automatisk lastes ned ved første bruk.",
    "search-audio": "Søk etter leverandører for lydtranskripsjon",
    "api-key": "API-nøkkel",
    "api-key-placeholder": "OpenAI API-nøkkel",
    "whisper-model": "Whisper-modell",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Standard innebygd",
    "default-built-in-desc":
      "Kjør en innebygd whisper-modell på denne instansen privat.",
    "openai-name": "OpenAI",
    "openai-desc": "Bruk OpenAI Whisper-large-modellen med din API-nøkkel.",
    "model-turbo": "openai/whisper-large-v3-turbo", // Ny modellnavn
    "model-size-turbo": "(~810mb)", // Ny modellstørrelse
  },

  // =========================
  // EMBEDDING PREFERENCE
  // =========================
  embedding: {
    title: "Preferanse for embedding",
    "desc-start":
      "Når du bruker en LLM som ikke støtter en innebygd embedding-motor, kan det hende du må spesifisere legitimasjon for å embede tekst.",
    "desc-end":
      "Embedding er prosessen med å omforme tekst til vektorer. Disse legitimasjonene kreves for å konvertere filene og promptene dine til et format som plattformen kan bruke til behandling.",
    provider: {
      title: "Leverandør for embedding",
      description:
        "Ingen oppsett kreves når du bruker plattformens innebygde embedding-motor.",
      "search-embed": "Søk i alle embedding-leverandører",
      search: "Søk i alle embedding-leverandører",
      select: "Velg en embedding-leverandør",
    },
    workspace: {
      title: "Arbeidsområdets embeddingpreferanse",
      description:
        "Den spesifikke embedding-leverandøren og modellen som vil bli brukt for dette arbeidsområdet. Som standard brukes systemets embedding-leverandør og innstillinger.",
      "multi-model":
        "Støtte for flere modeller er ennå ikke tilgjengelig for denne leverandøren.",
      "workspace-use": "Dette arbeidsområdet vil bruke",
      "model-set": "modellsettet for systemet.",
      embedding: "Arbeidsområdets embedding-modell",
      model:
        "Den spesifikke embedding-modellen som vil bli brukt for dette arbeidsområdet. Hvis tom, vil systemets embeddingpreferanse brukes.",
      wait: "-- venter på modeller --",
      setup: "Oppsett",
      use: "For å bruke",
      "need-setup": "som denne arbeidsområdets embedder må du sette opp først.",
      cancel: "Avbryt",
      save: "lagre",
      settings: "innstillinger",
      search: "Søk i alle embedding-leverandører",
      "need-llm": "som denne arbeidsområdets LLM må du sette opp først.",
      "save-error": "Kunne ikke lagre {{provider}}-innstillinger: {{error}}",
      "system-default": "Systemstandard",
      "system-default-desc":
        "Bruk systemets embedding-preferanse for dette arbeidsområdet.",
    },
    warning: {
      "switch-model":
        "Bytte embedding-modellen vil bryte forrige innbyggede dokumenter fra å fungere under chat. De må un-embed fra alle arbeidsområder og fjernes og lastes opp på nytt slik at de kan embeddes av den nye embedding-modellen.",
    },
  },

  // =========================
  // TEXT SPLITTING & CHUNKING
  // =========================
  text: {
    title: "Tekstdeling & Chunking-preferanser",

    "desc-start":
      "Noen ganger må du kanskje endre standardmetoden for hvordan nye dokumenter deles opp og chunkes før de legges til i vektordatabasen din.",

    "desc-end":
      "Endre dette bare hvis du forstår hvordan tekstdeling fungerer og hvilke konsekvenser det kan ha.",

    "warn-start": "Endringer her gjelder kun for",
    "warn-center": "nylig innebygde dokumenter",
    "warn-end": ", ikke eksisterende dokumenter.",

    method: {
      title: "Tekstdelingsmetode",

      "native-explain":
        "Bruk lokal chunkstørrelse & overlapping for oppdeling.",

      "jina-explain":
        "Deleger chunking/segmentering til Jinas innebygde metode.",

      "jina-info": "Jina-chunking aktiv.",

      jina: {
        api_key: "Jina API-nøkkel",
        api_key_desc:
          "Nødvendig for å bruke Jinas segmenteringstjeneste. Nøkkelen vil bli lagret i miljøet ditt.",
        max_tokens: "Jina: Maks tokens per chunk",
        max_tokens_desc:
          "Definerer maks tokens i hver chunk for Jinas segmenterer (maks 2000 tokens).",
        return_tokens: "Returner token-informasjon",
        return_tokens_desc:
          "Inkluder token-antall og tokenizer-informasjon i svaret.",
        return_chunks: "Returner chunk-informasjon",
        return_chunks_desc:
          "Inkluder detaljert informasjon om genererte chunks i svaret.",
      },
    },

    size: {
      title: "Tekstchunkstørrelse",
      description:
        "Dette er maksimalt antall tegn som kan være i en enkelt vektor.",
      recommend: "Maksimal lengde for innbyggingsmodellen er",
    },

    overlap: {
      title: "Tekstchunk-overlapping",
      description:
        "Dette er den maksimale overlappingen av tegn som oppstår under chunking mellom to tilstøtende tekstchunks.",
      error:
        "Chunk-overlapping kan ikke være større enn eller lik chunk-størrelse.",
    },
  },

  // =========================
  // CONTEXTUAL SETTINGS
  // =========================
  contextual: {
    checkbox: {
      label: "Kontekstuell embedding",
      hint: "Aktiver kontekstuell embedding for å forbedre embeddingprosessen med ekstra parametere",
    },
    systemPrompt: {
      label: "Systemprompt",
      placeholder: "Skriv inn en verdi...",
      description:
        "Eksempel: Vennligst gi en kort, konsis kontekst for å situere denne segmenten i det overordnede dokumentet for å forbedre søk og gjenfinning. Svar kun med den konsise konteksten og ingenting annet.",
    },
    userPrompt: {
      label: "Brukerprompt",
      placeholder: "Skriv inn en verdi...",
      description:
        "Eksempel: <document>\n{file}\n</document>\nHer er segmentet vi ønsker å situere innenfor hele dokumentet\n<chunk>\n{chunk}\n</chunk>",
    },
  },
  // =========================
  // CHAT UI SETTINGS
  // =========================
  "chat-ui-settings": {
    title: "Chatgrensesnitt-innstillinger",
    description: "Konfigurer chatinnstillingene.",
    auto_submit: {
      title: "Automatisk innsending av taleinndata",
      description: "Send taleinndata automatisk etter en periode med stillhet",
    },
    auto_speak: {
      title: "Automatisk opplesning av svar",
      description: "Les opp AI-svar automatisk",
    },
  },
  // =========================
  // PIPER TTS OPTIONS
  // =========================
  piperTTS: {
    description:
      "Alle PiperTTS-modeller kjører lokalt i nettleseren din. Dette kan være ressurskrevende på enheter med lav ytelse.",
    "voice-model": "Velg stemmemodell",
    "loading-models": "-- laster tilgjengelige modeller --",
    "stored-indicator":
      'Tegnet "✔" indikerer at modellen allerede er lagret lokalt og ikke trenger nedlasting ved kjøring.',
    "flush-cache": "Tøm stemmecache",
    "flush-success": "Alle stemmer tømt fra nettleserlageret",
    demo: {
      stop: "Stopp demo",
      loading: "Laster stemme",
      play: "Spill av eksempel",
      text: "Hei, velkommen til IST Legal!",
    },
  },

  // =========================
  // VECTOR DATABASE (SYSTEM)
  // =========================
  vector: {
    title: "Vektordatabase",
    description:
      "Dette er legitimasjon og innstillinger for hvordan plattforminstansen din skal fungere. Det er viktig at disse nøklene er oppdaterte og korrekte.",
    provider: {
      title: "Vektordatabase-leverandør",
      description: "Ingen konfigurasjon er nødvendig for LanceDB.",
      "search-db": "Søk i alle vektordatabase-leverandører",
      search: "Søk i alle vektordatabaser",
      select: "Velg vektordatabase-leverandør",
    },
    warning:
      "Å bytte vektordatabase vil kreve at du re-embedder alle dokumenter på tvers av alle relevante arbeidsområder. Dette kan ta litt tid.",
    search: {
      title: "Vektorsøkemodus",
      mode: {
        "globally-enabled":
          "Denne innstillingen styres globalt i systeminnstillingene. Besøk systeminnstillinger for å endre rangeringsatferd.",
        default: "Standardsøk",
        "default-desc": "Standard vektorlikhetssøk uten rangering.",
        "accuracy-optimized": "Nøyaktighetsoptimalisert",
        "accuracy-desc":
          "Rangerer resultatene på nytt for å forbedre nøyaktigheten ved hjelp av kryssoppmerksomhet.",
      },
    },
  },
  // =========================
  // VECTOR DATABASE PROVIDER DESCRIPTIONS
  // =========================
  vectordb: {
    lancedb:
      "100% lokal vektordatabase som kjører på samme instans som plattformen.",
    chroma: "Open source vektordatabase som du kan hoste selv eller i skyen.",
    pinecone: "100% skybasert vektordatabase for bedriftsbruk.",
    zilliz:
      "Skyhostet vektordatabase bygget for bedrifter med SOC 2-sertifisering.",
    qdrant: "Open source lokal og distribuert skyvektordatabase.",
    weaviate: "Open source lokal og skyhostet multimodal vektordatabase.",
    milvus: "Open source, svært skalerbar og lynrask.",
    astra: "Vektorsøk for reell GenAI.",
  },

  // =========================
  // DEEP SEARCH SETTINGS
  // =========================
  deep_search: {
    title: "Dypsøk",
    description:
      "Konfigurer nettsøkfunksjoner for chattsvar. Når aktivert, kan systemet søke på nettet etter informasjon for å forbedre svar.",
    enable: "Aktiver Dypsøk",
    enable_description:
      "Tillat systemet å søke på nettet etter informasjon når det svarer på spørsmål.",
    provider_settings: "Leverandørinnstillinger",
    provider: "Søkeleverandør",
    model: "Modell",
    api_key: "API-nøkkel",
    api_key_placeholder: "Skriv inn din API-nøkkel",
    api_key_placeholder_set:
      "API-nøkkel er satt (skriv inn ny nøkkel for å endre)",
    api_key_help:
      "Din API-nøkkel lagres sikkert og brukes kun for nettsøkforespørsler.",
    context_percentage: "Kontekstprosent",
    context_percentage_help:
      "Prosentandel av LLM-kontekstvinduet som skal tildeles nettsøkresultater (5-20%).",
    fetch_error: "Kunne ikke hente Dypsøk-innstillinger",
    save_success: "Dypsøk-innstillinger lagret",
    save_error: "Kunne ikke lagre Dypsøk-innstillinger: {{error}}",
    toast_success: "Dypsøk-innstillinger lagret",
    toast_error: "Kunne ikke lagre Dypsøk-innstillinger: {{error}}",
    brave_recommended:
      "Brave Search er for øyeblikket den anbefalte og mest pålitelige leverandøralternativet.",
  },

  // =========================
  // PDR SETTINGS
  // =========================
  "pdr-settings": {
    title: "PDR-innstillinger",
    description:
      "Konfigurer innstillingene for Parent Document Retrieval for arbeidsområdene dine.",
    "desc-end":
      "Disse innstillingene påvirker hvordan PDR-dokumenter behandles og brukes i chatsvar.",
    "global-override": {
      title: "Global overstyring for dynamisk PDR",
      description:
        "Når aktivert vil dette behandle alle arbeidsområdedokumenter som PDR-aktiverte for kontekst i svar. Når deaktivert vil kun dokumenter som er eksplisitt markert som PDR bli brukt, noe som kan redusere tilgjengelig kontekst og føre til svar av betydelig lavere kvalitet siden kun vektorchunks fra søk vil bli brukt som kilder i disse tilfellene.",
    },
    "toast-success": "PDR-innstillinger oppdatert",
    "toast-fail": "Kunne ikke oppdatere PDR-innstillinger",
    "adjacent-vector-limit": "Grense for tilstøtende vektorer",
    "adjacent-vector-limit-desc": "Begrensning for tilstøtende vektorer.",
    "adjacent-vector-limit-placeholder":
      "Skriv inn grense for tilstøtende vektorer",
    "keep-pdr-vectors": "Behold PDR-vektorer",
    "keep-pdr-vectors-desc": "Alternativ for å beholde PDR-vektorer.",
  },

  // =========================
  // ADMIN AGENTS
  // =========================
  agents: {
    title: "Agent-ferdigheter",
    "agent-skills": "Konfigurer og administrer agent-funksjoner",
    "custom-skills": "Tilpassede ferdigheter",
    back: "Tilbake",
    "select-skill": "Velg en ferdighet å konfigurere",
    "preferences-saved": "Agent-preferanser lagret",
    "preferences-failed": "Kunne ikke lagre agent-preferanser",
    "skill-status": {
      on: "På",
      off: "Av",
    },
    "skill-config-updated": "Agent-ferdighetskonfigurasjon oppdatert",
  },
  // =========================
  // AGENT MENU
  // =========================
  "agent-menu.default-agent": "Standard agent",
  "agent-menu.ability.rag-search": "RAG-søk",
  "agent-menu.ability.web-scraping": "Web-skraping",
  "agent-menu.ability.web-browsing": "Nettlesing",
  "agent-menu.ability.save-file-to-browser": "Lagre fil til nettleser",
  "agent-menu.ability.list-documents": "List dokumenter",
  "agent-menu.ability.summarize-document": "Sammendrag av dokument",
  "agent-menu.ability.chart-generation": "Diagramgenerering",
  // =========================
  // BADGES AGENT PAGE
  // =========================
  badges: {
    default: {
      text: "Standard",
      tooltip:
        "Denne ferdigheten er aktivert som standard og kan ikke slås av.",
    },
  },
  // =========================
  // EMBED CHATS
  // =========================
  "embed-chats": {
    title: "Embedde chatter",
    export: "Eksporter",
    description:
      "Dette er alle de innspilte chatter og meldingene fra alle embed du har publisert.",
    table: {
      embed: "Embed",
      sender: "Avsender",
      message: "Melding",
      response: "Svar",
      at: "Sendt",
    },
    delete: {
      title: "Slett chat",
      message:
        "Er du sikker på at du vil slette denne chatten?\n\nDenne handlingen kan ikke angres.",
    },
    config: {
      "delete-title": "Slett embed",
      "delete-message":
        "Er du sikker på at du vil slette dette embed?\n\nDenne handlingen kan ikke angres.",
      "disable-title": "Deaktiver embed",
      "disable-message":
        "Er du sikker på at du vil deaktivere dette embed?\n\nDenne handlingen kan ikke angres.",
      "enable-title": "Aktiver embed",
      "enable-message":
        "Er du sikker på at du vil aktivere dette embed?\n\nDenne handlingen kan ikke angres.",
    },
  },
  // =========================
  // EMBEDDABLE CHAT WIDGETS
  // =========================
  embeddable: {
    title: "Embedde chat-vinduer",
    description:
      "Embedde chat-vinduer er offentlige chat-grensesnitt knyttet til et enkelt arbeidsområde. Disse lar deg bygge arbeidsområder som du deretter kan publisere til verden.",
    create: "Opprett embed",
    table: {
      workspace: "Arbeidsområde",
      chats: "Sendte chatter",
      Active: "Aktive domener",
    },
  },
  // =========================
  // NEW EMBED
  // =========================
  "new-embed": {
    title: "Opprett nytt embed for arbeidsområde",
    error: "Feil: ",
    "desc-start":
      "Etter opprettelse vil du få en lenke som du kan publisere på nettstedet ditt med et enkelt",
    script: "script",
    tag: "tag.",
    cancel: "Avbryt",
    "create-embed": "Opprett embed",
    workspace: "Arbeidsområde",
    "desc-workspace":
      "Dette er arbeidsområdet som chat-vinduet ditt vil baseres på. Alle standarder vil bli arvet fra arbeidsområdet med mindre de overskrives av denne konfigurasjonen.",
    "allowed-chat": "Tillatt chat-metode",
    "desc-query":
      "Angi hvordan chatbotten din skal operere. Spørring betyr at den kun vil svare hvis et dokument hjelper til med å besvare spørringen.",
    "desc-chat":
      "Chat åpner for chatting om generelle spørsmål og kan svare på spørsmål som ikke er relatert til arbeidsområdet.",
    "desc-response": "Chat: Svar på alle spørsmål uavhengig av kontekst",
    "query-response":
      "Spørring: Svar kun på chatter relatert til dokumenter i arbeidsområdet",
    restrict: "Begrens forespørsler fra domener",
    filter:
      "Dette filteret vil blokkere forespørsler som kommer fra et annet domene enn de oppgitte nedenfor.",
    "use-embed":
      "Hvis denne er tom, betyr det at hvem som helst kan bruke ditt embed på hvilket som helst nettsted.",
    "max-chats": "Maks chatter per dag",
    "limit-chats":
      "Begrens antallet chatter dette embed kan prosessere i løpet av 24 timer. Null er ubegrenset.",
    "chats-session": "Maks chatter per sesjon",
    "limit-chats-session":
      "Begrens antallet chatter en sesjonsbruker kan sende med dette embed i løpet av 24 timer. Null er ubegrenset.",
    "enable-dynamic": "Aktiver dynamisk modellbruk",
    "llm-override":
      "Tillat å sette foretrukket LLM-modell for å overstyre arbeidsområdets standard.",
    "llm-temp": "Aktiver dynamisk LLM-temperatur",
    "desc-temp":
      "Tillat å sette LLM-temperaturen for å overstyre arbeidsområdets standard.",
    "prompt-override": "Aktiver prompt-overstyring",
    "desc-override":
      "Tillat å sette systemprompten for å overstyre arbeidsområdets standard.",
  },
  // =========================
  // EMBED CONFIGS
  // =========================
  embedConfigs: {
    "show-code": "Vis kode",
    enable: "Aktiver",
    disable: "Deaktiver",
    "all-domains": "alle",
    "disable-confirm":
      "Er du sikker på at du vil deaktivere denne embed?\nNår den er deaktivert, vil ikke embed lenger svare på noen chatforespørsler.",
    "delete-confirm":
      "Er du sikker på at du vil slette denne embed?\nNår den er slettet, vil ikke embed lenger svare på chatter eller være aktiv.\n\nDenne handlingen kan ikke angres.",
    "disabled-toast": "Embed er deaktivert",
    "enabled-toast": "Embed er aktiv",
  },
  // =========================
  // EVENT LOGS
  // =========================
  event: {
    title: "Hendelseslogger",
    description:
      "Se alle handlinger og hendelser som skjer på denne instansen for overvåking.",
    clear: "Fjern hendelseslogger",
    table: {
      type: "Hendelsestype",
      user: "Bruker",
      occurred: "Skjedde",
    },
  },
  // =========================
  // API KEYS
  // =========================
  api: {
    title: "API-nøkler",
    description:
      "API-nøkler gir innehaveren mulighet til å programmere tilgang til og administrere denne instansen.",
    link: "Les API-dokumentasjonen",
    generate: "Generer ny API-nøkkel",
    table: {
      key: "API-nøkkel",
      by: "Opprettet av",
      created: "Opprettet",
    },
    new: {
      title: "Opprett ny API-nøkkel",
      description:
        "Når opprettet kan API-nøkkelen brukes for å programmere tilgang til og konfigurere denne instansen.",
      doc: "Les API-dokumentasjonen",
      cancel: "Avbryt",
      "create-api": "Opprett API-nøkkel",
    },
  },

  // =========================
  // BROWSER EXTENSION API KEYS
  // =========================
  "browser-extension-api": {
    title: "API-nøkler",
    description: "Administrer API-nøkler for tilkobling til denne instansen.",
    "generate-key": "Generer ny API-nøkkel",
    "table-headers": {
      "connection-string": "Tilkoblingsstreng",
      "created-by": "Opprettet av",
      "created-at": "Opprettet",
      actions: "Handlinger",
    },
    "no-keys": "Ingen API-nøkler funnet",
    modal: {
      title: "Ny nettleserutvidelse API-nøkkel",
      "multi-user-warning":
        "Advarsel: Du er i flerbrukermodus. Denne API-nøkkelen vil gi tilgang til alle arbeidsområder tilknyttet kontoen din. Vær forsiktig med deling.",
      "create-description":
        'Etter å ha klikket på "Opprett API-nøkkel", vil denne instansen prøve å opprette en ny API-nøkkel for nettleserutvidelsen.',
      "connection-help":
        'Hvis du ser "Koblet til IST Legal" i utvidelsen, var tilkoblingen vellykket. Hvis ikke, vennligst kopier tilkoblingsstrengen og lim den inn i utvidelsen manuelt.',
      cancel: "Avbryt",
      "create-key": "Opprett API-nøkkel",
      "copy-key": "Kopier API-nøkkel",
      "key-copied": "API-nøkkel kopiert!",
    },
    tooltips: {
      "copy-connection": "Kopier tilkoblingsstreng",
      "auto-connect": "Koble til utvidelsen automatisk",
    },
    confirm: {
      revoke:
        "Er du sikker på at du vil tilbakekalle denne nettleserutvidelsens API-nøkkel?\nEtter dette vil den ikke lenger være brukbar.\n\nDenne handlingen kan ikke angres.",
    },
    toasts: {
      "key-revoked": "Nettleserutvidelsens API-nøkkel er permanent tilbakekalt",
      "revoke-failed": "Kunne ikke tilbakekalle API-nøkkel",
      copied: "Tilkoblingsstreng kopiert til utklippstavlen",
      connecting: "Prøver å koble til nettleserutvidelsen...",
    },
    "revoke-title": "Tilbakekall nettleserutvidelsens API-nøkkel",
    "revoke-message":
      "Er du sikker på at du vil tilbakekalle denne nettleserutvidelsens API-nøkkel?\nEtter dette vil den ikke lenger være brukbar.\n\nDenne handlingen kan ikke angres.",
  },
  // =========================
  // SECURITY
  // =========================
  security: {
    "multi-user-mode-permanent":
      "Multi-brukermodus er permanent aktivert av sikkerhetsgrunner",
    "password-validation": {
      "restricted-chars":
        "Passordet ditt inneholder ugyldige tegn. Tillatte symboler er _,-,!,@,$,%,^,&,*,(,),;",
    },
    "public-workspace": {
      "access-description":
        "Når aktivert, kan alle brukere få tilgang til de offentlige arbeidsområdene uten å logge inn.",
    },
    button: {
      saving: "Lagrer...",
      "save-changes": "Lagre endringer",
    },
  },
  // =========================
  // MULTI-USER MODE
  // =========================
  multi: {
    title: "Multi-brukermodus",
    description:
      "Sett opp instansen din for å støtte teamet ditt ved å aktivere multi-brukermodus.",
    enable: {
      "is-enable": "Multi-brukermodus er aktivert",
      enable: "Aktiver multi-brukermodus",
      description:
        "Som standard vil du være den eneste administratoren. Som administrator må du opprette kontoer for alle nye brukere eller administratorer. Ikke mist passordet ditt siden kun en administrator kan tilbakestille passord.",
      username: "Admin-kontoens e-post",
      password: "Admin-kontoens passord",
      "username-placeholder": "Ditt admin-brukernavn",
      "password-placeholder": "Ditt admin-passord",
    },
    password: {
      title: "Passordbeskyttelse",
      description:
        "Beskytt instansen din med et passord. Hvis du glemmer dette, finnes det ingen gjenopprettingsmetode, så sørg for å lagre passordet.",
    },
    instance: {
      title: "Passordbeskytt instansen",
      description:
        "Som standard vil du være den eneste administratoren. Som administrator må du opprette kontoer for alle nye brukere eller administratorer. Ikke mist passordet ditt siden kun en administrator kan tilbakestille passord.",
      password: "Instanspassord",
    },
  },

  // =========================
  // EXPERIMENTAL FEATURES
  // =========================
  experimental: {
    title: "Eksperimentelle Funksjoner",
    description: "Funksjoner som for tiden er i betatesting",
    "live-sync": {
      title: "Sanntidsdokumentsynkronisering",
      description:
        "Aktiver automatisk innholdssynkronisering fra eksterne kilder",
      "manage-title": "Overvåkede dokumenter",
      "manage-description":
        "Dette er alle dokumentene som for tiden overvåkes i din instans. Innholdet i disse dokumentene vil bli synkronisert regelmessig.",
      "document-name": "Dokumentnavn",
      "last-synced": "Sist synkronisert",
      "next-refresh": "Tid til neste oppdatering",
      "created-on": "Opprettet den",
      "auto-sync": "Automatisk innholdssynkronisering",
      "sync-description":
        'Aktiver muligheten til å spesifisere en innholdskilde som skal "overvåkes". Overvåket innhold vil regelmessig bli hentet og oppdatert i denne instansen.',
      "sync-workspace-note":
        "Overvåket innhold vil automatisk oppdateres i alle arbeidsområder der de er referert.",
      "sync-limitation":
        "Denne funksjonen gjelder kun for webbasert innhold, som nettsteder, Confluence, YouTube og GitHub-filer.",
      documentation: "Funksjonsdokumentasjon og advarsler",
      "manage-content": "Administrer overvåket innhold",
    },
    tos: {
      title: "Bruksvilkår for eksperimentelle funksjoner",
      description:
        "Eksperimentelle funksjoner på denne plattformen er funksjoner som vi tester og som er valgfrie. Vi vil proaktivt informere om eventuelle bekymringer som kan oppstå før godkjenning av en funksjon.",
      "possibilities-title":
        "Bruk av en funksjon på denne siden kan resultere i, men er ikke begrenset til, følgende muligheter:",
      possibilities: {
        "data-loss": "Tap av data.",
        "quality-change": "Endring i resultatkvalitet.",
        "storage-increase": "Økt lagringsplass.",
        "resource-consumption": "Økt ressursforbruk.",
        "cost-increase":
          "Økte kostnader eller bruk av tilkoblede LLM- eller innbyggingsleverandører.",
        "potential-bugs":
          "Potensielle feil eller problemer ved bruk av denne applikasjonen.",
      },
      "conditions-title":
        "Bruk av en eksperimentell funksjon kommer med følgende ikke-uttømmende liste over betingelser:",
      conditions: {
        "future-updates":
          "Funksjonen eksisterer kanskje ikke i fremtidige oppdateringer.",
        stability: "Funksjonen som brukes er for tiden ikke stabil.",
        availability:
          "Funksjonen kan være utilgjengelig i fremtidige versjoner, konfigurasjoner eller abonnementer av denne instansen.",
        privacy:
          "Dine personverninnstillinger vil bli respektert ved bruk av en betafunksjon.",
        changes: "Disse betingelsene kan endres i fremtidige oppdateringer.",
      },
      "read-more": "Hvis du vil lese mer kan du henvise til",
      contact: "eller kontakt",
      reject: "Avvis & Lukk",
      accept: "Jeg forstår",
    },
    "update-failed": "Oppdatering av eksperimentelle funksjoner mislyktes",
  },
};
