# Version Control API

## Overview

The version control API provides access to platform version information through a simple REST endpoint. This API serves version data to the frontend while maintaining backend-only tracking capabilities.

## Endpoint

### GET /version

Returns the current platform version information.

**URL:** `/version`
**Method:** `GET`
**Authentication:** None required
**Rate Limiting:** None

#### Response

**Success Response (200):**

```json
{
  "success": true,
  "version": "1.1.0",
  "description": "Added new document builder features and improved chat performance"
}
```

**Error Response (404) - Version file not found:**

```json
{
  "success": false,
  "error": "Version file not found"
}
```

**Error Response (500) - JSON parsing error:**

```json
{
  "success": false,
  "error": "Invalid version file format"
}
```

## Implementation Details

### File Location

The endpoint reads version information from `version.json` in the project root directory:

```
project-root/
├── version.json          # Version configuration file
├── server/
│   └── endpoints/
│       └── system.js     # Contains /version endpoint
```

### Backend Logging

The endpoint logs full version information (including timestamp) to the console for backend tracking:

```javascript
console.log("Version info accessed:", {
  version: versionData.version,
  timestamp: versionData.timestamp,
  description: versionData.description,
});
```

**Log Output Example:**

```
Version info accessed: {
  version: "1.1.0",
  timestamp: "2024-01-15T10:30:00Z",
  description: "Added new document builder features and improved chat performance"
}
```

### Data Filtering

The endpoint excludes sensitive or internal fields from the frontend response:

**Fields Included in Response:**

- `version` - Semantic version number
- `description` - User-friendly description of changes

**Fields Excluded from Response:**

- `timestamp` - Internal tracking timestamp
- Any other fields in version.json

## Configuration

### version.json Format

```json
{
  "version": "1.1.0",
  "description": "Added new document builder features and improved chat performance",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Field Specifications:**

| Field         | Type   | Required | Description                             | Frontend Visible |
| ------------- | ------ | -------- | --------------------------------------- | ---------------- |
| `version`     | String | Yes      | Semantic version number (e.g., "1.1.0") | Yes              |
| `description` | String | Yes      | User-friendly description of changes    | Yes              |
| `timestamp`   | String | No       | ISO 8601 timestamp for backend tracking | No               |

### Version Number Format

Follow semantic versioning (SemVer) specification:

- **Format:** `MAJOR.MINOR.PATCH`
- **Example:** `1.2.3`

**Version Types:**

- **Major** (X.0.0): Breaking changes or major new features
- **Minor** (1.X.0): New features, backward compatible
- **Patch** (1.1.X): Bug fixes, backward compatible

## Error Handling

### File Not Found

If `version.json` doesn't exist:

```json
{
  "success": false,
  "error": "Version file not found"
}
```

### Invalid JSON

If `version.json` contains invalid JSON:

```json
{
  "success": false,
  "error": "Invalid version file format"
}
```

### Missing Required Fields

If required fields are missing, the endpoint will still return available data but may cause frontend issues.

## Security Considerations

### Data Privacy

- Timestamp information is logged backend-only
- No sensitive deployment information exposed
- Only public version information returned

### Access Control

- No authentication required (public endpoint)
- Read-only access to version information
- Cannot modify version data through API

### Input Validation

- File path is hardcoded (no user input)
- JSON parsing with error handling
- Graceful degradation on errors

## Testing

### Unit Tests

Located in `server/tests/unit/endpoints/version.test.js`:

**Test Cases:**

- Successful version data retrieval
- File not found handling
- JSON parsing error handling
- Response format validation
- Backend logging verification
- Timestamp exclusion from response

### Manual Testing

```bash
# Test successful response
curl -X GET http://localhost:3001/version

# Expected response:
{
  "success": true,
  "version": "1.1.0",
  "description": "Added new document builder features and improved chat performance"
}
```

### Integration Testing

The endpoint integrates with:

- Frontend version model (`frontend/src/models/version.js`)
- VersionDisplay component
- System monitoring and logging

## Deployment Considerations

### Docker Environments

- Ensure `version.json` is included in Docker build context
- File paths resolve correctly in containers
- No additional configuration required

### Production Deployment

- Update `version.json` as part of deployment process
- Monitor backend logs for version access tracking
- Consider automation for version updates

### CI/CD Integration

- Automate version.json updates in build pipeline
- Include version validation in deployment checks
- Track version changes in deployment logs

## Monitoring and Logging

### Access Logging

Every version request is logged with full version data:

```
Version info accessed: { version: "1.1.0", timestamp: "2024-01-15T10:30:00Z", description: "..." }
```

### Error Logging

Errors are logged with context:

```
Error reading version file: [Error details]
```

### Metrics

Consider tracking:

- Version endpoint request frequency
- Error rates for version requests
- Version update deployment success

## Related Endpoints

The version endpoint is part of the system endpoints group in `server/endpoints/system.js`:

- `/version` - Version information (this endpoint)
- `/system/...` - Other system-related endpoints

## API Documentation

This endpoint is documented in the OpenAPI/Swagger specification:

**Tags:** System
**Summary:** Get platform version information
**Description:** Returns current platform version and description

## Changelog

### Version 1.1.0

- Initial implementation of version control API
- Added backend logging for version access
- Implemented timestamp exclusion from frontend response
- Added comprehensive error handling

## Future Enhancements

Potential improvements:

- **Version History API**: Endpoint to retrieve previous versions
- **Version Comparison**: Compare between versions
- **Release Notes**: Link to detailed changelog
- **Admin Version Management**: API for updating version information
- **Version Validation**: Ensure semantic versioning compliance
