import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import { TbEdit, TbTrash, Tb<PERSON><PERSON>, Tb<PERSON>heck } from "react-icons/tb";

export default function PromptLibraryItem({
  prompt,
  onEdit,
  onDelete,
  onApply,
}) {
  const { t } = useTranslation();
  const [showPreview, setShowPreview] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleDelete = () => {
    setShowDeleteConfirm(true);
  };

  const confirmDelete = () => {
    onDelete(prompt.id);
    setShowDeleteConfirm(false);
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const truncateText = (text, maxLength = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  return (
    <div className="border rounded-lg p-4 bg-secondary/10 hover:bg-secondary/20 transition-colors">
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-foreground mb-1">{prompt.name}</h4>
          {prompt.description && (
            <p className="text-sm text-muted-foreground mb-2">
              {prompt.description}
            </p>
          )}
          <div className="text-xs text-muted-foreground mb-3">
            Created: {formatDate(prompt.created_at)}
            {prompt.updated_at !== prompt.created_at && (
              <span className="ml-2">
                • Updated: {formatDate(prompt.updated_at)}
              </span>
            )}
          </div>

          {/* Preview toggle */}
          <div className="mb-3">
            <button
              onClick={() => setShowPreview(!showPreview)}
              className="text-sm text-primary hover:text-primary-hover flex items-center gap-1"
            >
              <TbEye size={14} />
              {showPreview
                ? t("prompt-library.hide-preview")
                : t("prompt-library.show-preview")}
            </button>
          </div>

          {/* Preview content */}
          {showPreview && (
            <div className="mt-2 p-3 bg-secondary rounded border">
              <pre className="text-sm whitespace-pre-wrap text-foreground max-h-32 overflow-y-auto">
                {prompt.prompt_text}
              </pre>
            </div>
          )}

          {/* Truncated preview when not expanded */}
          {!showPreview && (
            <div className="text-sm text-muted-foreground">
              {truncateText(prompt.prompt_text)}
            </div>
          )}
        </div>

        <div className="flex items-center gap-2 ml-4">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => onApply(prompt)}
            className="flex items-center gap-1"
          >
            <TbCheck size={14} />
            {t("prompt-library.apply-button")}
          </Button>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => onEdit(prompt)}
            className="flex items-center gap-1"
          >
            <TbEdit size={14} />
            {t("prompt-library.edit-button")}
          </Button>
          <Button
            type="button"
            variant="destructive"
            size="sm"
            onClick={handleDelete}
            className="flex items-center gap-1"
          >
            <TbTrash size={14} />
            {t("prompt-library.delete-button")}
          </Button>
        </div>
      </div>

      {/* Delete confirmation */}
      {showDeleteConfirm && (
        <div className="mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded">
          <h5 className="font-medium text-destructive mb-2">
            {t("prompt-library.delete-confirm-title")}
          </h5>
          <p className="text-sm text-muted-foreground mb-3">
            {t("prompt-library.delete-confirm-message")}
          </p>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="destructive"
              size="sm"
              onClick={confirmDelete}
            >
              {t("prompt-library.delete-confirm-button")}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={cancelDelete}
            >
              {t("prompt-library.delete-cancel-button")}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
