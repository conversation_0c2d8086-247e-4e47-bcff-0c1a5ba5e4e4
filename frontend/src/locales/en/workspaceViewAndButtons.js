export default {
  // =========================
  // WORKSPACE VIEW
  // =========================
  "workspace-chats": {
    welcome: "Welcome to your new workspace.",
    "desc-start": "To get started either",
    "desc-mid": "upload a document",
    "desc-or": "or",
    start: "To get started",
    "desc-end": "send a chat.",
    "attached-file": "Attached file",
    "attached-files": "Attached files",
    "token-count": "Token count",
    "total-tokens": "Total Tokens",
    "context-window": "Context Window",
    "remaining-tokens": "Remaining",
    "view-files": "View attached files",
    prompt: {
      send: "Send",
      "send-message": "Send message",
      placeholder: "Ask for legal information",
      "change-size": "Change text size",
      reset: "Reset your chat",
      clear: "Clear your chat history and begin a new chat",
      command: "Command",
      description: "Description",
      save: "save",
      small: "Small",
      normal: "Normal",
      large: "Large",
      larger: "Larger",
      attach: "Attach a file to this chat",
      upgrade: "Upgrade your prompt",
      upgrading: "Upgrading prompt...",
      "original-prompt": "Original Prompt:",
      "upgraded-prompt": "Upgraded Prompt:",
      "edit-prompt": "You can edit the new prompt before submitting",
      "shortcut-tip":
        "Tip: Press Enter to accept changes. Use Shift+Enter for new lines.",
      "speak-prompt": "Speak your prompt",
      "view-agents": "View all available agents you can use for chatting",
      "deep-search": "Web Search",
      "deep-search-tooltip":
        "Search the web for information to enhance responses",
      "ability-tag": "Ability",
      "workspace-chats.prompt.view-agents": "View Agents",
      "workspace-chats.prompt.ability-tag": "Ability",
      "workspace-chats.prompt.speak-prompt": "Speak your prompt",
      "total-tokens": "Total tokens",
    },
  },
  // =========================
  // NEW WORKSPACE
  // =========================
  "new-workspace": {
    title: "New Workspace",
    placeholder: "My Workspace",
    "legal-areas": "Legal Areas",
    create: {
      title: "Create new workspace",
      description:
        "After creating this workspace only admins will be able to see it. You can add users after it has been created.",
      error: "Error: ",
      cancel: "Cancel",
      "create-workspace": "Create workspace",
    },
  },
  // =========================
  // WORKSPACE NAMES (LEGAL AREAS)
  // =========================
  "workspace-names": {
    "Administrative Law": "Administrative Law",
    "Business Law": "Business Law",
    "Civil Law": "Civil Law",
    "Criminal Law": "Criminal Law",
    "Diplomatic Law": "Diplomatic Law",
    "Fundamental Law": "Fundamental Law",
    "Human Rights Law": "Human Rights Law",
    "Judicial Laws": "Judicial Laws",
    "Security Laws": "Security Laws",
    "Taxation Laws": "Taxation Laws",
  },
  // =========================
  // PRESETS BUTTON
  // =========================
  presets: {
    "edit-title": "Edit Standard Prompt",
    description: "Description of the prompt",
    "description-placeholder": "Makes a summary of the attached files.",
    deleting: "Deleting...",
    "delete-preset": "Delete Standard Prompt",
    cancel: "Cancel",
    save: "Save",
    "add-title": "Add Standard Prompt",
    "command-label": "Name of the prompt, one single word",
    "command-placeholder": "Summary",
    "command-desc":
      "The name is also the chatbox shortcut, starting with /, to use this prompt without pressing buttons.",
    "prompt-label": "Prompt",
    "prompt-placeholder": "Produce a summary of the files I have attached.",
    "prompt-desc":
      "The prompt that will be sent when this prompt preset is used.",
    "tooltip-add": "Add New Standard Prompt",
    "tooltip-hover": "View your own standard prompts.",
    "confirm-delete": "Confirm deleting standard prompt preset.",
  },
  // =========================
  // QURA BUTTONS
  // =========================
  qura: {
    "copy-to-cora": "Qura source check",
    "qura-status": "Qura button is ",
    "copy-option": "Copy option",
    "option-quest": "Question",
    "option-resp": "Response",
    "role-description": "Add a Qura button to prompt responses on Qura.law",
  },
  // =========================
  // CHAT BOX DRAG & DROP COMPONENT
  // =========================
  chatboxdnd: {
    title: "Add a file to this prompt",
    description:
      "Drop your file here to have it added for this prompt. It is not stored in the workspace as a permanent source.",
    "file-prefix": "File:",
    "attachment-tooltip":
      "This file will be attached to your message. It will not be saved in the workspace as a permanent source.",
    "uploaded-file-tag": "USER UPLOADED FILE",
  },
  // =========================
  // CONTEXT WINDOW DISPLAY
  // =========================
  context_window: {
    "context-window": "Context Window",
    "max-output-tokens": "Max Output Tokens",
    "output-limit": "Output Limit",
    tokens: "tokens",
    "fallback-value": "Fallback value used",
  },
  // =========================
  // PROMPT VALIDATION
  // =========================
  "prompt-validate": {
    edit: "Edit",
    response: "Response",
    prompt: "Prompt",
    regenerate: "Regenerate response",
    good: "Good response",
    bad: "Bad response",
    copy: "Copy",
    more: "More actions",
    fork: "Fork",
    delete: "Delete",
    cancel: "Cancel",
    save: "Save & Submit",
    "export-word": "Export to Word",
    exporting: "Exporting...",
  },
  // =========================
  // VALIDATE ANSWER
  // =========================
  "validate-answer": {
    setting: "Validation LLM",
    title: "Validation LLM Preference",
    description:
      "These are the credentials and settings for your preferred validation LLM chat & embedding provider. Its important these keys are current and correct or else the system will not function properly.",
    "toast-success": "Validation LLM settings updated",
    "toast-fail": "Failed to update Validation LLM settings",
    saving: "Saving...",
    "save-changes": "Save changes",
  },
  // =========================
  // VALIDATION MODAL
  // =========================
  validation: {
    responseHeader: "Here is the Response that was generated",
    contextHeader: "Original Context and Sources",
  },
  // =========================
  // CITATIONS
  // =========================
  citations: {
    show: "Show Citations",
    hide: "Hide Citations",
    chunk: "Citation Chunks",
    pdr: "Parent Document",
    "pdr-h": "Document Highlighting",
    referenced: "Referenced",
    times: "times.",
    citation: "Citation",
    match: "match",
    download:
      "This browser does not support PDFs. Please download the PDF to view it:",
    "download-btn": "Download PDF",
    view: "View citations",
    sources: "View sources and citations",
    "pdf-collapse-tip":
      "Tip: You can collapse this PDF tab using the button in the upper left corner",
    "open-in-browser": "Open in browser",
    "loading-pdf": "-- loading PDF --",
    "error-loading": "Error loading PDF",
    "no-valid-path": "No valid PDF path found",
    "view-details": "View Details",
    "web-search": "Web Search",
    "web-search-summary": "Web Search Summary",
    "web-search-results": "Web Search Results",
    "no-web-search-results": "No web search results found",
    "previous-highlight": "Previous highlight",
    "next-highlight": "Next highlight",
    "try-alternative-view": "Try alternative view",
  },
  // =========================
  // MANUAL WORK ESTIMATOR
  // =========================
  "manual-work-estimator": {
    title: "Manual Work Estimation",
    button: "Time estimate",
    "show-prompt": "Show prompt",
    "hide-prompt": "Hide prompt",
    "prompt-title": "Prompt used for estimation",
    "system-prompt": "System prompt",
    "user-content": "User content",
    "provider-info": "Provider information",
    model: "Model",
    provider: "Provider",
  },
};
