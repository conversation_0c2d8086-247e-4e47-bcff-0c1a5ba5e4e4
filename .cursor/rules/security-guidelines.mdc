---
description: General guidelines of safety requirements in coding
globs:
alwaysApply: false
---
# Security Guidelines

This document outlines security best practices and considerations for the ISTLegal project.

## General Principles

- **Principle of Least Privilege:** Grant users and system components only the minimum permissions necessary to perform their functions.
- **Defense in Depth:** Implement multiple layers of security controls.
- **Secure Defaults:** Configure services and libraries with security in mind from the start.
- **Keep Dependencies Updated:** Regularly update dependencies to patch known vulnerabilities. Use tools like `npm audit` or GitHub Dependabot.

## Input Validation and Sanitization

- **Always Validate Input:** Validate all input from users, APIs, and external sources on the backend.
- **Use Schema Validation:** Leverage Zod for schema validation in forms ([form-guidelines.mdc](mdc:.cursor/rules/form-guidelines.mdc)) and API endpoints.
- **Sanitize Output:** Prevent Cross-Site Scripting (XSS) by correctly encoding or sanitizing data before rendering it in the frontend (React generally helps, but be cautious with `dangerouslySetInnerHTML`).
- **Parameterize Database Queries:** Use Prisma or other ORM features to prevent SQL injection. Avoid constructing raw SQL queries with user input.

## Authentication and Authorization

- **Strong Authentication:** Enforce strong password policies and consider multi-factor authentication (MFA) options.
- **Secure Session Management:** Use secure, HTTP-only cookies for session tokens. Implement proper session expiration and invalidation on logout.
- **Authorization Checks:** Ensure robust authorization checks are performed on the backend for every request accessing protected resources or performing sensitive actions. Verify user roles and permissions (e.g., admin checks in API endpoints).
- **Rate Limiting:** Implement rate limiting on authentication endpoints (login, password reset) and potentially other sensitive API endpoints to prevent brute-force attacks.

## Data Protection

- **Encryption in Transit:** Use HTTPS for all communication between the client, server, and external services.
- **Encryption at Rest:** Ensure sensitive data stored in the database (e.g., API keys stored for users, potentially PII) is encrypted. Leverage database-level encryption where available.
- **Secrets Management:**
    - Do **not** commit secrets (API keys, database credentials, tokens) directly into the codebase.
    - Use environment variables (`.env` files, securely managed in deployment environments) to store secrets.
    - Use tools like Doppler or cloud provider secret managers for managing secrets in production.

## API Security

- **Authentication/Authorization:** Protect all API endpoints (except public ones like login/signup) with authentication and appropriate authorization checks.
- **Input Validation:** Validate all request parameters and body payloads on the server-side.
- **HTTPS:** Ensure all API endpoints are served over HTTPS.
- **CORS Configuration:** Configure Cross-Origin Resource Sharing (CORS) correctly on the backend to only allow requests from trusted origins (the frontend domain).
- **Error Handling:** Avoid leaking sensitive information (e.g., stack traces, internal paths) in error messages sent to the client.

## Dependency Management

- **Regular Audits:** Regularly run `npm audit` or use integrated tools (like GitHub Dependabot) to identify and fix vulnerabilities in dependencies.
- **Minimize Dependencies:** Only include necessary dependencies to reduce the attack surface.

## Logging and Monitoring

- **Log Security Events:** Log important security events like login attempts (success/failure), permission changes, and access to sensitive data.
- **Avoid Logging Sensitive Data:** Ensure sensitive information (passwords, API keys, PII) is not logged in plain text.
- **Monitor Logs:** Regularly monitor logs for suspicious activity.
