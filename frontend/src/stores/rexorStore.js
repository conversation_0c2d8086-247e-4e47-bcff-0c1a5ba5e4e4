import { create } from "zustand";
import { persist } from "zustand/middleware";
import {
  loginToRexor,
  registerProject,
  writeArticleTransaction,
  updateArticleTransaction,
} from "@/services/rexorService";
import showToast from "@/utils/toast";

const useRexorStore = create(
  persist(
    (set, get) => ({
      // Authentication state
      token: null,
      isLoggedIn: false,
      loading: false,
      error: null,

      // Saved credentials for auto-login
      savedCredentials: null,

      // Project and transaction state
      registeredProject: null,
      transactionUID: null,
      activeReference: null,

      isAuthorizationError: (error) => {
        const errorMsg = error.message || "";
        const errorResponse = error.response?.data?.error || "";

        if (errorMsg.includes("401") || errorMsg.includes("403")) {
          return true;
        }

        // Fallback: Check for specific error messages
        return (
          errorMsg.includes("Unauthorized") ||
          errorMsg.includes("Token might be invalid") ||
          errorMsg.includes("Authorization has been denied") ||
          errorMsg.includes("Authentication failed") ||
          errorResponse.includes("Unauthorized") ||
          errorResponse.includes("Authorization has been denied") ||
          errorResponse.includes("Authentication failed")
        );
      },

      login: async (username, password) => {
        set({ loading: true, error: null });

        try {
          const data = await loginToRexor(username, password);

          if (data.success && data.access_token) {
            set({
              token: data.access_token,
              isLoggedIn: true,
              savedCredentials: { username, password },
              loading: false,
              error: null,
            });

            return data.access_token;
          } else {
            set({
              error: "No token received in response",
              loading: false,
              isLoggedIn: false,
            });
            return null;
          }
        } catch (err) {
          set({
            error: err.message,
            loading: false,
            isLoggedIn: false,
          });
          return null;
        }
      },

      logout: () => {
        set({
          token: null,
          isLoggedIn: false,
          savedCredentials: null,
          registeredProject: null,
          transactionUID: null,
          activeReference: null,
          error: null,
        });
      },

      handleAuthError: (_, showLoginModal) => {
        get().logout();

        showToast("Authentication failed. Please log in again.", "error");

        if (showLoginModal) {
          showLoginModal(true);
        }
      },

      checkLoginStatus: () => {
        const state = get();
        set({
          isLoggedIn: !!state.token,
        });
      },

      registerNewProject: async (projectData, showLoginModal) => {
        const { token, isAuthorizationError, handleAuthError } = get();

        if (!token) {
          throw new Error("User is not logged in");
        }

        try {
          const projectResponse = await registerProject(projectData, token);

          set({
            registeredProject: projectResponse,
            error: null,
          });

          return projectResponse;
        } catch (err) {
          set({ error: err.message });

          if (isAuthorizationError(err)) {
            handleAuthError(err, showLoginModal);
            return null;
          }

          throw err;
        }
      },

      writeNewArticleTransaction: async (transactionData, showLoginModal) => {
        const { token, isAuthorizationError, handleAuthError } = get();

        if (!token) {
          throw new Error("User is not logged in");
        }

        try {
          const transactionResponse = await writeArticleTransaction(
            transactionData,
            token
          );

          set({
            transactionUID: transactionResponse.UID,
            registeredProject: {
              ...get().registeredProject,
              UID: transactionResponse.UID,
            },
            error: null,
          });

          return transactionResponse;
        } catch (err) {
          set({ error: err.message });

          if (isAuthorizationError(err)) {
            handleAuthError(err, showLoginModal);
            return null;
          }

          throw err;
        }
      },

      updateTransactionForPrompt: async (transactionData, showLoginModal) => {
        const { token, isAuthorizationError, handleAuthError } = get();

        if (!token) {
          console.log("No Rexor token available");
          return null;
        }

        try {
          const result = await updateArticleTransaction(transactionData, token);

          set({ error: null });

          return result;
        } catch (err) {
          set({ error: err.message });

          if (isAuthorizationError(err)) {
            handleAuthError(err, showLoginModal);
            return null;
          }

          return null;
        }
      },

      writeOrUpdateTransaction: async (showLoginModal) => {
        const {
          registeredProject,
          isLoggedIn,
          writeNewArticleTransaction,
          updateTransactionForPrompt,
          isAuthorizationError,
          handleAuthError,
        } = get();

        try {
          if (!registeredProject) {
            console.log("No registered project available");
            return;
          }

          if (!isLoggedIn) {
            if (showLoginModal) {
              showLoginModal(true);
            }
            return;
          }

          if (!registeredProject.UID) {
            return await writeNewArticleTransaction(
              registeredProject,
              showLoginModal
            );
          } else {
            return await updateTransactionForPrompt(
              registeredProject,
              showLoginModal
            );
          }
        } catch (error) {
          console.error("Error in writeOrUpdateTransaction:", error);
          set({ error: error.message });

          if (isAuthorizationError(error)) {
            handleAuthError(error, showLoginModal);
          }

          return null;
        }
      },

      setRegisteredProject: (project) => {
        set({ registeredProject: project });
      },

      setActiveReference: (reference) => {
        set({ activeReference: reference });
      },

      clearActiveReference: () => {
        set({ activeReference: null });
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: "rexor-store",
      partialize: (state) => ({
        token: state.token,
        isLoggedIn: state.isLoggedIn,
        savedCredentials: state.savedCredentials,
        registeredProject: state.registeredProject,
        transactionUID: state.transactionUID,
        activeReference: state.activeReference,
      }),
    }
  )
);

export default useRexorStore;

// Exported selectors for specific state pieces
export const useRexorToken = () => useRexorStore((state) => state.token);
export const useRexorIsLoggedIn = () =>
  useRexorStore((state) => state.isLoggedIn);
export const useRexorLoading = () => useRexorStore((state) => state.loading);
export const useRexorError = () => useRexorStore((state) => state.error);
export const useRexorSavedCredentials = () =>
  useRexorStore((state) => state.savedCredentials);
export const useRexorRegisteredProject = () =>
  useRexorStore((state) => state.registeredProject);
export const useRexorActiveReference = () =>
  useRexorStore((state) => state.activeReference);

// Exported actions
export const useRexorLogin = () => useRexorStore((state) => state.login);
export const useRexorLogout = () => useRexorStore((state) => state.logout);
export const useRexorCheckLoginStatus = () =>
  useRexorStore((state) => state.checkLoginStatus);
export const useRexorRegisterProject = () =>
  useRexorStore((state) => state.registerNewProject);
export const useRexorWriteOrUpdateTransaction = () =>
  useRexorStore((state) => state.writeOrUpdateTransaction);
export const useRexorSetRegisteredProject = () =>
  useRexorStore((state) => state.setRegisteredProject);
export const useRexorSetActiveReference = () =>
  useRexorStore((state) => state.setActiveReference);
export const useRexorClearActiveReference = () =>
  useRexorStore((state) => state.clearActiveReference);
export const useRexorClearError = () =>
  useRexorStore((state) => state.clearError);
