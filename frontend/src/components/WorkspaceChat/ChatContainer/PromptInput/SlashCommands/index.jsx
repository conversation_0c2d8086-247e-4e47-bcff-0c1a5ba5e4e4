import { useEffect, useRef, useState } from "react";
import { Toolt<PERSON> } from "react-tooltip";
import EndAgentSession from "./endAgentSession";
import SlashPresets from "./SlashPresets";
import { useTranslation } from "react-i18next";
import { RiSlashCommands2 } from "react-icons/ri";
import { Button } from "@/components/Button";

export default function SlashCommandsButton({ showing, setShowSlashCommand }) {
  const { t } = useTranslation();
  const tooltipRef = useRef(null);

  const handleClick = () => {
    setShowSlashCommand(!showing);
    tooltipRef.current.close();
  };

  return (
    <>
      <Button
        type="button"
        variant="ghost"
        size="icon"
        className="h-8 w-8"
        id="slash-cmd-btn"
        data-tooltip-id="tooltip-slash-cmd-btn"
        data-tooltip-content={t("presets.tooltip-hover")}
        onClick={handleClick}
      >
        <RiSlashCommands2 />
      </Button>
      <Tooltip
        ref={tooltipRef}
        id="tooltip-slash-cmd-btn"
        delayShow={300}
        className="tooltip"
      />
    </>
  );
}

export function SlashCommands({ showing, setShowing, sendCommand }) {
  const cmdRef = useRef(null);
  useEffect(() => {
    function listenForOutsideClick() {
      if (!showing || !cmdRef.current) return false;
      document.addEventListener("click", closeIfOutside);
    }
    listenForOutsideClick();
  }, [showing, cmdRef.current]);

  const closeIfOutside = ({ target }) => {
    if (target.id === "slash-cmd-btn") return;
    const isOutside = !cmdRef?.current?.contains(target);
    if (!isOutside) return;
    setShowing(false);
  };

  return (
    <div hidden={!showing}>
      <div className="flex justify-start absolute md:w-[34%] w-[100%] left-0 bottom-full mb-2 z-10 px-4">
        <div
          ref={cmdRef}
          className="max-h-[60vh] overflow-y-auto p-[2px] md:p-1 rounded-lg bg-background shadow justify-start items-start gap-2.5 inline-flex flex-col md:flex-col w-full"
        >
          <EndAgentSession sendCommand={sendCommand} setShowing={setShowing} />
          <SlashPresets sendCommand={sendCommand} setShowing={setShowing} />
        </div>
      </div>
    </div>
  );
}

export function useSlashCommands() {
  const [showSlashCommand, setShowSlashCommand] = useState(false);
  return { showSlashCommand, setShowSlashCommand };
}
