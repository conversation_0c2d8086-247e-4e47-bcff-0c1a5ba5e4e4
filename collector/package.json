{"name": "ist-legal-document-collector", "version": "1.1.0", "description": "Document collector server endpoints", "main": "index.js", "author": "IST Legal", "license": "Copyright", "private": false, "engines": {"node": ">=18.12.1"}, "scripts": {"dev:collector": "cd collector && npm run dev", "dev": "cross-env NODE_ENV=development nodemon --ignore hotdir --ignore storage --trace-warnings index.js", "start": "cross-env NODE_ENV=production node index.js"}, "dependencies": {"@langchain/community": "^0.2.23", "@xenova/transformers": "^2.11.0", "bcrypt": "^5.1.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.0.3", "epub2": "^3.0.2", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.2", "gm": "^1.25.0", "html-to-text": "^9.0.5", "i18next": "^24.2.0", "ignore": "^5.3.0", "js-tiktoken": "^1.0.8", "langchain": "0.1.36", "mammoth": "^1.6.0", "mbox-parser": "^1.0.1", "mime": "^3.0.0", "moment": "^2.29.4", "node-html-parser": "^6.1.13", "node-xlsx": "^0.24.0", "officeparser": "^4.0.5", "openai": "4.38.5", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "puppeteer": "~21.5.2", "sharp": "^0.33.5", "slugify": "^1.6.6", "tesseract.js": "^5.1.1", "tmp": "^0.2.3", "url-pattern": "^1.0.3", "uuid": "^9.0.0", "wavefile": "^11.0.0", "winston": "^3.13.0", "youtubei.js": "^9.1.0"}, "devDependencies": {"nodemon": "^2.0.22", "prettier": "^2.4.1"}}