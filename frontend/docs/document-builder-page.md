# Document Builder Page

_Last updated: 2025-05-17_

The **Document Builder** administration page lets system administrators configure the prompts that power the Case Document Builder (CDB) pipeline.

---

## 1. Architecture Overview

```mermaid
┌────────────┐        GET /system/document-builder-prompts     ┌────────────────────┐
│ React Page │ ──────────────────────────────────────────────► │  System Endpoint   │
└────────────┘                                                 └────────────────────┘
      │                                                              │
      │                                          builds from `exportedLegalPrompts`
      ▼                                                              │
`System.getDocumentBuilderPrompts()`                                 │
      │                                                              │
      │   descriptor[]                                               │
      ▼                                                              ▼
Dynamic form (react-hook-form + zod)                        System Settings storage
      │                                                              ▲
      │  POST /system/update-document-builder-prompts                │
      └──────────────────────────────────────────────────────────────┘
```

1. The page calls **System.getDocumentBuilderPrompts** on mount to retrieve an array of prompt descriptors.
2. Descriptors are grouped by `promptKey` and rendered as controlled `<textarea>` fields.
3. Clicking **Save Prompt Settings** bundles the state into `{ [systemSettingName]: value }` and posts it using **System.updateDocumentBuilderPrompts**.
4. The backend validates and persists the settings in `system_settings`.

---

## 2. Prompt Descriptor Shape

```ts
interface PromptDescriptor {
  promptKey: string; // Logical group id (e.g. "SUMMARY")
  promptField: string; // SYSTEM_PROMPT | USER_PROMPT | PROMPT_TEMPLATE
  label: string; // i18n key for field label
  description: string; // i18n key for helper text
  defaultContent: string; // Placeholder string
  systemSettingName: string; // System-settings key (e.g. "cdb_summary_system_prompt")
  currentValue: string; // Saved override value ─ empty string ⇒ use default
}
```

Descriptors originate from `exportedLegalPrompts` in `server/utils/chats/prompts/legalDrafting.js` ensuring a single source of truth.

---

## 3. Component Flow

| Phase                 | Hook                             | Description                                                    |
| --------------------- | -------------------------------- | -------------------------------------------------------------- |
| **Load**              | `useEffect()`                    | Fetch descriptors and `reset()` react-hook-form default values |
| **Render**            | JSX map over grouped descriptors | Renders `<Label>`, `<Textarea>`, helper `<p>`                  |
| **Validate & Submit** | RHF + `zod` schema               | All fields are optional strings; no extra validation           |
| **Save**              | `handleSubmit(onSubmit)`         | Calls `System.updateDocumentBuilderPrompts`, shows toast       |

All user-visible text is pulled via `useTranslation("common")` with the **document-builder.\*** namespace.

---

## 4. Testing Strategy

File: `frontend/src/pages/GeneralSettings/DocumentBuilder/__tests__/DocumentBuilder.test.jsx`

1. **Render Success** – mocks successful API, asserts correct fields.
2. **Fetch Error** – mocks rejection, expects error toast.
3. **Save Success** – edits a field, clicks save, expects API call + success toast.
4. **Save Failure** – mocks failed save, expects error toast.

These tests ensure the entire integration path works without spinning up a server.

---

## 5. Extending the Prompt List

To add a new prompt:

1. Add a descriptor to `exportedLegalPrompts` with a unique `systemSettingName`.
2. Run the backend; the new prompt appears automatically in the UI.
3. Add translation keys for the new label & description in **all locale files**.
4. Update integration tests if the new prompt affects existing assumptions.

---

## 6. Internationalisation

All strings live under the `document-builder` namespace. Example keys:

```json5
{
  "document-builder.save": "Save Prompt Settings",
  "document-builder.saving": "Saving…",
  "document-builder.toast-success": "Document builder prompts saved successfully.",
  "document-builder.toast-fail": "Failed to save document builder prompts.",
  "document_builder_page.toast-fail-load-prompts": "Failed to load prompt configurations.",
  "document-builder.override-prompt-placeholder": "Enter prompt to override…",
}
```

### Prompt Translation Keys

Legal drafting prompt labels and descriptions are internationalized using keys in the `document-builder.prompts` namespace. Each prompt has two translation keys:

1. A label key: `document-builder.prompts.[prompt-identifier]-label`
2. A description key: `document-builder.prompts.[prompt-identifier]-description`

For example:

```json5
{
  "document-builder.prompts.document-summary-system-label": "Document Summary (System)",
  "document-builder.prompts.document-summary-system-description": "System prompt for instructing the AI on how to summarize a document's content and relevance to a legal task.",
}
```

When adding new prompts to `exportedLegalPrompts` in `server/utils/chats/prompts/legalDrafting.js`, you must add corresponding translation keys to **all locale files** to ensure proper internationalization.

---

## 7. Related Files

- **Frontend**

  - `frontend/src/pages/GeneralSettings/DocumentBuilder/index.jsx` – main component
  - `frontend/src/models/system.js` – helper methods

- **Backend**
  - `server/endpoints/system.js` – `/system/document-builder-prompts` handlers
  - `server/utils/chats/prompts/legalDrafting.js` – `exportedLegalPrompts` source

---

## 8. Gotchas & Tips

1. The save endpoint only persists _changed_ fields; keep empty strings when the admin wants to revert to default.
2. When adding tests, remember to `mockReset()` the methods between cases to avoid call-count bleed.
3. Keep translation keys singular; avoid "prompts".
