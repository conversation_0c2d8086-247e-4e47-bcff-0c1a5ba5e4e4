const { v4: uuidv4 } = require("uuid");
const { WorkspaceChats } = require("../../models/workspaceChats");
const { getVectorDbClass, getLLMProvider } = require("../helpers");
const { writeResponseChunk } = require("../helpers/chat/responses");
const { grepAgents } = require("./agents");
const {
  grepCommand,
  VALID_COMMANDS,
  chatPrompt,
  recentChatHistory,
} = require("./index");
const { LinkedWorkspace } = require("../../models/linkedWorkspaces");
const { Workspace } = require("../../models/workspace");
const { SystemSettings } = require("../../models/systemSettings");
const { TokenManager } = require("../helpers/tiktoken");
const path = require("path");
const { generateLegalMemo } = require("../helpers/legalMemo");
const { getUserDocumentPathName } = require("../../endpoints/document");
const fs = require("fs");

const VALID_CHAT_MODE = ["chat", "query"];

// Default prompts for document drafting
const DEFAULT_DOCUMENT_DRAFTING_PROMPT =
  "You are a legal assistant tasked with helping draft legal documents. Provide a comprehensive and accurate response based on the information provided. Ensure that the response is given in the same language as the original user query.";

// Default prompt for legal issues analysis
const DEFAULT_LEGAL_ISSUES_PROMPT =
  "Identify and analyze the key legal issues in the given scenario. Provide a structured analysis with clear reasoning and potential implications.";

// Default prompt for legal memo creation
const DEFAULT_MEMO_PROMPT =
  "Create a legal memorandum that addresses the presented issues. Include facts, legal questions, analysis, and conclusions in a structured format.";

// Default prompt for combining multiple document excerpts into a single response
const DEFAULT_COMBINE_PROMPT =
  "A user has provided a question and multiple documents, upon which several memos on different parts of the document corpus has been provided along with possible memos on legal matters involved. You are tasked with combining these multiple outputs into a coherent response. Gather the information from these responses and synthesize it into a single comprehensive answer, without mentioning that the response is based on multiple memos, instead answering as if answering the original user queastion. Focus on maintaining accuracy while providing a comprehensive answer based on the information provided. Ensure that the response is given in the same language as the original user query.";

/**
 * Main function to handle chat interactions with the workspace, always pinning all files
 * @param {_request} - The request object
 * @param {response} - The response object
 * @param {workspace} - Workspace details
 * @param {message} - User message
 * @param {chatMode} - Mode of chat interaction
 * @param {user} - User details
 * @param {thread} - Chat thread details
 * @param {attachments} - Message attachments
 * @param {chatId} - Chat session ID
 * @param {isCanvasChat} - Flag for canvas chat
 * @param {preventChatCreation} - Flag to prevent chat creation
 * @param {settings_suffix} - Settings suffix
 * @param {invoice_ref} - Invoice reference
 * @param {_vectorSearchMode} - Vector search mode
 */
async function streamChatWithWorkspaceDD(
  _request,
  response,
  workspace,
  message,
  chatMode = "chat",
  user = null,
  thread = null,
  attachments = [],
  chatId = null,
  isCanvasChat = false,
  preventChatCreation = false,
  settings_suffix = "",
  invoice_ref,
  _vectorSearchMode = "default",
  displayMessage = null
) {
  // Flag to track if finalizeResponseStream has been sent
  let finalizeSent = false;
  // --- DD PROGRESS TRACKING HELPER -----------------------------
  /**
   * Helper to send progress events to the client so it can visualise
   * the document-drafting workflow.  Mirrors the implementation used
   * by streamCDB (cdbProgress) but uses the `progress` type.
   *
   * @param {Object} data Additional payload data (step, subStep, etc.)
   */
  const sendProgress = (data = {}) => {
    try {
      writeResponseChunk(response, {
        uuid,
        type: "progress",
        ...data,
      });
    } catch (err) {
      console.error("[PROGRESS] Failed to send progress chunk", err);
    }
  };

  // Keep track of which logical step we are in. 1-based index like LegalTask/CDB.
  let ddStepCounter = 1;

  const uuid = uuidv4();

  const sendProcessComplete = () => {
    writeResponseChunk(response, {
      uuid,
      type: "process_complete",
    });
  };

  const updatedMessage = await grepCommand(message, user);

  // Handle predefined commands if present
  if (Object.keys(VALID_COMMANDS).includes(updatedMessage)) {
    const data = await VALID_COMMANDS[updatedMessage](
      workspace,
      message,
      uuid,
      user,
      thread,
      invoice_ref
    );
    writeResponseChunk(response, data);
    return;
  }

  // Handle agent interactions if applicable
  const isAgentChat = await grepAgents({
    uuid,
    response,
    message,
    user,
    workspace,
    thread,
  });
  if (isAgentChat) return;

  // Determine chat provider and model based on settings_suffix
  let chatProvider;
  let chatModel;
  if (settings_suffix === "_DD") {
    chatProvider = workspace?.chatProvider || process.env.LLM_PROVIDER_DD;
    chatModel = workspace?.chatModel;
  } else {
    chatProvider = workspace?.chatProvider || process.env.LLM_PROVIDER_DD_2;
    chatModel = workspace?.chatModel;
  }

  // Add debug logging for LLM provider selection
  console.log(
    `\x1b[36m[LLM SELECTION]\x1b[0m Using provider ${chatProvider} with model ${chatModel} and settings_suffix ${settings_suffix || "none"}`
  );
  if (settings_suffix === "_DD" && process.env.LLM_PROVIDER_DD) {
    console.log(
      `\x1b[36m[LLM SELECTION]\x1b[0m LLM_PROVIDER_DD = ${process.env.LLM_PROVIDER_DD}`
    );
  } else if (process.env.LLM_PROVIDER_DD_2) {
    console.log(
      `\x1b[36m[LLM SELECTION]\x1b[0m LLM_PROVIDER_DD_2 = ${process.env.LLM_PROVIDER_DD_2}`
    );
  }

  // Initialize LLM connector and VectorDb class
  const LLMConnector = getLLMProvider({
    provider: chatProvider,
    model: chatModel,
    workspace,
    settings_suffix,
  });

  // Add detailed diagnostic for multi-provider architecture
  console.log(
    `\x1b[36m[ARCHITECTURE INFO]\x1b[0m Document drafting may use multiple LLM providers by design:`
  );
  console.log(
    `\x1b[36m[ARCHITECTURE INFO]\x1b[0m - Main provider (${LLMConnector.constructor.name}): Used for document generation`
  );
  console.log(
    `\x1b[36m[ARCHITECTURE INFO]\x1b[0m - Legal analysis: May use a specialized provider (like Gemini) for legal reasoning if configured`
  );
  console.log(
    `\x1b[36m[ARCHITECTURE INFO]\x1b[0m - Linked workspaces: Each might use its own configured provider`
  );

  const VectorDb = getVectorDbClass();
  const messageLimit = isCanvasChat ? 1 : workspace?.openAiHistory || 20;

  let completeText;
  let metrics = {};
  let contextTexts = [];
  let sources = [];
  let sourcesSave = [];
  let hasVectorizedSpace = await VectorDb.hasNamespace(workspace.slug);
  let totalEmbeddingsCount = await VectorDb.namespaceCount(workspace.slug);

  // Retrieve DD settings for vector, memo, base, and linked workspace impacts
  const {
    ddVectorEnabled,
    ddMemoEnabled,
    ddBaseEnabled,
    ddVectorTokenLimit,
    ddMemoTokenLimit,
    ddBaseTokenLimit,
    ddLinkedWorkspaceImpact,
  } = await SystemSettings.getDDSettings();

  // Determine if document drafting is linked to other workspaces
  const isDocumentDraftingLinked =
    await SystemSettings.isDocumentDraftingLinking();
  // DEBUG logging for document drafting linking and memo settings
  console.log(
    `\x1b[36m[DD DEBUG]\x1b[0m ddMemoEnabled=${ddMemoEnabled}, ddBaseEnabled=${ddBaseEnabled}, ddLinkedWorkspaceImpact=${ddLinkedWorkspaceImpact}, isDocumentDraftingLinked=${isDocumentDraftingLinked}`
  );

  // Initialize token management and calculate max available tokens
  const tokenManager = new TokenManager();
  let maxAvailableTokens = LLMConnector.promptWindowLimit();
  let linkedWorkspaceCount = 0;

  // Fetch linked workspaces if document drafting is linked
  const linkedWorkspaces = isDocumentDraftingLinked
    ? await LinkedWorkspace.where({ workspace_id: Number(workspace.id) })
    : [];

  linkedWorkspaceCount = linkedWorkspaces.length;

  // Early return for token management when no linked workspaces
  const hasLinkedWorkspaces =
    isDocumentDraftingLinked && linkedWorkspaceCount > 0;

  // Skip all linked workspace processing upfront if none are present
  // This prevents unnecessary initialization of DocumentDrafting instances
  if (!hasLinkedWorkspaces) {
    console.log(
      `\x1b[36m[LINKED WORKSPACES]\x1b[0m No linked workspaces detected, skipping all linked workspace processing`
    );
  }

  // Adjust maxAvailableTokens based on linked workspace impact and DD settings
  if (ddLinkedWorkspaceImpact && hasLinkedWorkspaces) {
    if (ddVectorEnabled) {
      maxAvailableTokens -= linkedWorkspaceCount * ddVectorTokenLimit;
    }
    if (ddMemoEnabled) {
      maxAvailableTokens -= linkedWorkspaceCount * ddMemoTokenLimit;
    }
  }

  let availableTokens = maxAvailableTokens;
  let usedTokens = 0;
  let baseTokensUsed = 0;
  let memoTokensUsed = 0;
  let vectorTokensUsed = 0;

  let memoResults = [];
  let vectorResults = [];

  // Set temperature for LLM responses
  const temperature = workspace?.openAiTemp ?? LLMConnector.defaultTemp;

  // Enhanced diagnostic function instead of forcing same provider
  // This preserves intentional multi-provider design where Gemini might be used for legal analysis
  const logProviderOperation = async (
    operationName,
    prompt,
    content,
    provider,
    temp = temperature
  ) => {
    console.log(
      `\x1b[36m[${operationName}]\x1b[0m Using provider: ${provider.constructor.name}`
    );
    return await provider.getChatCompletion(
      [
        { role: "system", content: prompt },
        { role: "user", content },
      ],
      { temperature: temp }
    );
  };

  // Perform base analysis for legal issues if enabled
  let legalIssues = null;
  if (ddBaseEnabled) {
    const legalIssuesPrompt =
      workspace?.legalIssuesPrompt ||
      (await SystemSettings.getDocumentDraftingLegalIssuesPrompt()) ||
      DEFAULT_LEGAL_ISSUES_PROMPT;

    console.log(
      `\x1b[36m[DocumentDrafting]\x1b[0m Legal issues prompt source: ${workspace?.legalIssuesPrompt ? "workspace" : (await SystemSettings.getDocumentDraftingLegalIssuesPrompt()) ? "system settings" : "default constant"}`
    );

    try {
      // Log operation with existing provider (might be Gemini by design)
      const { textResponse } = await logProviderOperation(
        "LEGAL ANALYSIS",
        legalIssuesPrompt,
        message,
        LLMConnector
      );

      legalIssues = textResponse;
      const legalIssuesTokens = tokenManager.countFromString(legalIssues);
      if (legalIssuesTokens <= ddBaseTokenLimit) {
        baseTokensUsed = legalIssuesTokens;
        usedTokens += legalIssuesTokens;
        availableTokens -= legalIssuesTokens;
      } else {
        console.warn(
          "Legal issues analysis exceeded token limit, discarding result"
        );
        legalIssues = null;
      }
    } catch (error) {
      console.error("Error generating legal issues analysis:", error);
      legalIssues = null;
    }
  }

  // Fetch memos from linked workspaces if enabled and linked workspaces exist
  if (ddMemoEnabled && hasLinkedWorkspaces) {
    // Step: Fetching memos from linked workspaces
    sendProgress({
      step: ddStepCounter,
      label: "streamdd_progress_modal.step_fetching_memos",
      description: "streamdd_progress_modal.desc_fetching_memos",
      progress: -1,
      total: linkedWorkspaceCount,
    });

    console.log(
      `\x1b[36m[MEMO GENERATION]\x1b[0m Starting memo generation for ${linkedWorkspaceCount} linked workspaces`
    );

    const memoPrompt =
      workspace?.memoPrompt ||
      (await SystemSettings.getDocumentDraftingMemoPrompt()) ||
      DEFAULT_MEMO_PROMPT;

    console.log(
      `\x1b[36m[DocumentDrafting]\x1b[0m Memo prompt source: ${workspace?.memoPrompt ? "workspace" : (await SystemSettings.getDocumentDraftingMemoPrompt()) ? "system settings" : "default constant"}`
    );

    const effectiveMemoTokenLimit = ddMemoTokenLimit ?? Infinity;

    for (const linkedWorkspace of linkedWorkspaces) {
      const linked = await Workspace.get({
        id: linkedWorkspace.linkedWorkspace_id,
      });

      if (!linked.slug) {
        continue;
      }

      try {
        console.log(
          `\x1b[36m[MEMO GENERATION]\x1b[0m Attempting to generate memo for workspace ${linked.slug}`
        );

        const {
          memo,
          tokenCount,
          sources: memoSources,
        } = await generateLegalMemo({
          workspace: linked,
          systemPrompt: memoPrompt,
          userPrompt: legalIssues || message,
          LLMConnector,
          temperature,
          tokenLimit: ddMemoTokenLimit ?? null,
          settings: {
            isDocumentDraftingLinked,
            ddLinkedWorkspaceImpact,
            ddMemoEnabled,
            ddVectorEnabled,
          },
        });

        if (tokenCount <= effectiveMemoTokenLimit) {
          console.log(
            `\x1b[36m[MEMO GENERATION]\x1b[0m Generated memo with ${tokenCount} tokens (under limit of ${effectiveMemoTokenLimit})`
          );
          memoResults.push(memo);
          memoTokensUsed += tokenCount;
          usedTokens += tokenCount;
          availableTokens -= tokenCount;
          vectorResults.push(...memoSources);
          // update sub progress for each memo fetched
          sendProgress({
            step: ddStepCounter,
            subStep: memoResults.length,
            label: "streamdd_progress_modal.sub_step_memo_label",
            labelArgs: { workspaceSlug: linked.slug },
            total: linkedWorkspaceCount,
          });
        } else {
          console.warn(
            `\x1b[33m[MEMO GENERATION WARNING]\x1b[0m Memo for workspace ${linked.slug} exceeded token limit (${tokenCount} > ${effectiveMemoTokenLimit})`
          );
        }
      } catch (error) {
        // Send progress update indicating failure for this sub-step
        sendProgress({
          step: ddStepCounter,
          subStep: memoResults.length + vectorResults.length + 1, // Ensure unique subStep ID even on failure
          label: `streamdd_progress_modal.sub_step_memo_label`,
          labelArgs: { workspaceSlug: linked.slug }, // Still send slug for context
          total: linkedWorkspaceCount,
          progress: -2, // Use -2 to signify error
          error: error.message || "Unknown error during memo generation", // Include error message
        });
        console.error(
          `\x1b[31m[MEMO GENERATION ERROR]\x1b[0m Failed for ${linked.slug}: ${error.message}`
        );
      }
    }

    // Log summary of memo generation results
    if (memoResults.length > 0) {
      console.log(
        `\x1b[32m[MEMO RETRIEVAL SUCCEEDED]\x1b[0m Successfully retrieved ${memoResults.length} memos from linked workspaces. Total tokens: ${memoTokensUsed}`
      );
      // mark memo step completed
      sendProgress({ step: ddStepCounter, progress: 100 });

      // Log first few characters of each memo to help identify content
      memoResults.forEach((memo, index) => {
        try {
          // Ensure memo is a string before using substring
          if (typeof memo === "string") {
            const previewText = memo.substring(0, 50).replace(/\n/g, " ");
            console.log(
              `\x1b[32m[MEMO ${index + 1}]\x1b[0m Preview: "${previewText}..."`
            );
          }
        } catch (error) {
          console.error(
            `\x1b[31m[MEMO ERROR]\x1b[0m Failed to process memo preview: ${error.message}`
          );
        }
      });
    } else {
      console.log(
        `\x1b[31m[MEMO RETRIEVAL FAILED]\x1b[0m No memos were retrieved from linked workspaces despite having ${linkedWorkspaces.length} linked workspace(s)`
      );
    }
  } else {
    console.log(
      `\x1b[36m[MEMO GENERATION]\x1b[0m Skipping memo generation: ddMemoEnabled=${ddMemoEnabled}, isDocumentDraftingLinked=${isDocumentDraftingLinked}, linkedWorkspaceCount=${linkedWorkspaceCount}, hasLinkedWorkspaces=${hasLinkedWorkspaces}`
    );
  }

  // Only perform vector search on linked workspaces if enabled
  // AND if we didn't get enough content from memos
  // AND if we actually have linked workspaces
  if (ddVectorEnabled && hasLinkedWorkspaces && memoResults.length === 0) {
    console.log(
      `\x1b[36m[VECTOR SEARCH]\x1b[0m No memos generated, falling back to vector search for ${linkedWorkspaceCount} linked workspaces`
    );
    for (const linkedWorkspace of linkedWorkspaces) {
      const linked = await Workspace.get({
        id: linkedWorkspace.linkedWorkspace_id,
      });

      if (!linked.slug) continue;

      try {
        const vectorSearchResult = await VectorDb.performSimilaritySearch({
          namespace: linked.slug,
          input: message,
          LLMConnector,
          similarityThreshold: linked?.similarityThreshold,
          topN: linked?.topN,
          workspace: linked,
        });

        if (vectorSearchResult && vectorSearchResult.sources) {
          const vectorTokens = vectorSearchResult.sources.reduce(
            (total, source) =>
              total + tokenManager.countFromString(source.text),
            0
          );

          if (vectorTokens <= ddVectorTokenLimit) {
            console.log(
              `\x1b[36m[VECTOR SEARCH]\x1b[0m Found ${vectorSearchResult.sources.length} relevant sources (${vectorTokens} tokens) from ${linked.slug}`
            );
            vectorResults.push(...vectorSearchResult.sources);
            vectorTokensUsed += vectorTokens;
            usedTokens += vectorTokens;
            availableTokens -= vectorTokens;
          } else {
            console.warn(
              `\x1b[33m[VECTOR SEARCH WARNING]\x1b[0m Vector search for workspace ${linked.slug} exceeded token limit (${vectorTokens} > ${ddVectorTokenLimit})`
            );
          }
        }
      } catch (error) {
        console.error(
          `\x1b[31m[VECTOR SEARCH ERROR]\x1b[0m Error in vector search for workspace ${linked.slug}: ${error.message}`
        );
      }
    }
  } else if (!ddVectorEnabled && hasLinkedWorkspaces) {
    console.log(
      `\x1b[36m[VECTOR SEARCH]\x1b[0m Vector search is disabled, relying only on memos`
    );
  }

  // Calculate remaining upload capacity
  const remainingUploadCapacity = Math.max(0, maxAvailableTokens - usedTokens);

  // Build token metadata object
  const tokenMetadata = {
    totalTokensAvailable: maxAvailableTokens,
    tokensUsed: usedTokens,
    tokensRemaining: availableTokens,
    baseTokensUsed,
    memoTokensUsed,
    vectorTokensUsed,
    remainingUploadCapacity,
    linkedWorkspaceImpact: hasLinkedWorkspaces
      ? {
          workspaces: linkedWorkspaceCount,
          vectorReservation: ddVectorEnabled
            ? linkedWorkspaceCount * ddVectorTokenLimit
            : 0,
          memoReservation: ddMemoEnabled
            ? linkedWorkspaceCount * (ddMemoTokenLimit ?? 0)
            : 0,
        }
      : null,
  };

  // Check if workspace has no vectorized data and mode is query, handle accordingly
  if (
    (!hasVectorizedSpace || totalEmbeddingsCount === 0) &&
    chatMode === "query"
  ) {
    const textResponse =
      workspace?.queryRefusalResponse ??
      "There is no relevant information in this workspace to answer your query.";
    writeResponseChunk(response, {
      uuid,
      type: "textResponse",
      textResponse,
      sources: [],
      attachments,
      close: true,
      error: null,
    });
    await WorkspaceChats.new({
      workspaceId: workspace.id,
      prompt: displayMessage || message,
      response: {
        text: textResponse,
        sources: [],
        attachments,
        type: chatMode,
      },
      threadId: thread?.id || null,
      include: true,
      user,
      invoice_ref,
    });
    return;
  }

  // Fetch recent chat history
  const { rawHistory, chatHistory } = await recentChatHistory({
    user,
    workspace,
    thread,
    messageLimit,
  });

  // Add vector results from linked workspaces to context and sources
  if (vectorResults.length > 0) {
    vectorResults.forEach((source) => {
      contextTexts.push(source.text);
      sources.push(source);
    });
  }

  // Format source URLs and paths
  for (const source of sources) {
    const url = source.url;
    const match = url.match(/\w+:\/\/[^/]+\/(.*)/);
    source.url = match ? match[1] : url;
    source.path =
      "file:///" + path.resolve(process.env.STORAGE_DIR, `hotdir`) + "/" + url;
    if (!source.url.startsWith("/hotdir/")) {
      source.url = path.join("/hotdir/", source.url);
    }
  }

  // Check if contextTexts are empty for query mode and handle accordingly
  if (chatMode === "query" && contextTexts.length === 0) {
    const textResponse =
      workspace?.queryRefusalResponse ??
      "There is no relevant information in this workspace to answer your query.";
    writeResponseChunk(response, {
      uuid,
      type: "textResponse",
      textResponse,
      sources: [],
      close: true,
      error: null,
    });

    if (!preventChatCreation) {
      await WorkspaceChats.new({
        workspaceId: workspace.id,
        prompt: displayMessage || message,
        response: {
          text: textResponse,
          sources: [],
          type: chatMode,
          attachments,
        },
        threadId: thread?.id || null,
        user,
        invoice_ref,
      });
    }
    return;
  }

  // Compress messages for LLM input
  // TODO: CUSTOM SYSTEM PROMPT ALIGNMENT
  // streamDD currently does not implement user custom system prompt hierarchy like streamLQA does.
  // streamLQA correctly uses chatPrompt(workspace, user, styleAlignment) which implements:
  // 1. User custom system prompt (highest priority)
  // 2. Workspace system prompt
  // 3. Default system prompt (fallback)
  //
  // streamDD only uses SystemSettings.getDocumentDraftingPrompt() which ignores user/workspace prompts.
  // Implementation will require a separate custom prompt system for streamDD since it:
  // - Uses document-specific prompts (drafting, legal issues, memo, combine)
  // - Processes documents in chunks rather than using vector search sources
  // - Has different context handling than streamLQA
  //
  // Proposed solution: Create a documentDraftingChatPrompt() function that:
  // - Takes the same parameters as chatPrompt(workspace, user, styleAlignment)
  // - Returns user custom prompt if set, otherwise workspace prompt, otherwise document drafting prompt
  // - Maintains the document drafting context while respecting user customization
  let documentDraftingPrompt = await SystemSettings.getDocumentDraftingPrompt();

  // Set default document drafting prompt if null
  if (!documentDraftingPrompt) {
    documentDraftingPrompt = DEFAULT_DOCUMENT_DRAFTING_PROMPT;
    console.log(
      `\x1b[36m[DocumentDrafting]\x1b[0m Using default document drafting prompt since system setting is null`
    );
  }

  let combinePrompt = await SystemSettings.getDocumentDraftingCombinePrompt();

  // Log the combine prompt for debugging
  console.log(
    `\x1b[36m[DocumentDrafting]\x1b[0m Combine prompt type: ${typeof combinePrompt}, value: ${combinePrompt ? "exists" : "null"}`
  );

  // Set default combine prompt if null
  if (!combinePrompt) {
    combinePrompt = DEFAULT_COMBINE_PROMPT;
    console.log(
      `\x1b[36m[DocumentDrafting]\x1b[0m Using default combine prompt since system setting is null`
    );
  }

  const temperatureValue = workspace?.openAiTemp ?? LLMConnector.defaultTemp;

  // Get the actual prompt window limit from the connector
  const modelWindowLimit = LLMConnector.promptWindowLimit();

  // Use a consistent approach to chunk sizing, adaptive to model capabilities
  // This works for both OpenAI and Ollama without provider-specific code
  // Calculate the effective context size based on a percentage of the model's context window
  // For larger context windows, we can use smaller percentages to create more chunks
  let percentToUse;
  if (modelWindowLimit > 32000) {
    percentToUse = 0.85; // For very large context windows
  } else if (modelWindowLimit > 16000) {
    percentToUse = 0.8; // For large context windows
  } else if (modelWindowLimit > 8000) {
    percentToUse = 0.75; // For medium context windows
  } else {
    percentToUse = 0.65; // For small context windows
  }

  // Calculate tokens for context fetched from linked workspaces **including memos**.
  // We combine memoResults with other context texts so that the chunk-sizing logic
  // fully accounts for the extra tokens introduced by the fetched memos. This
  // prevents oversizing each document chunk when memos are present.
  const calcContextTexts = [...contextTexts, ...memoResults];
  const contextTextsTokens = tokenManager.countFromString(calcContextTexts);

  // Estimate fixed overhead per LLM call (system prompt, user prompt, buffer)
  // Note: This is an estimate. compressMessages might add more based on history/attachments.
  const fixedOverheadPerCall =
    tokenManager.countFromString(documentDraftingPrompt) +
    tokenManager.countFromString(updatedMessage) +
    500; // Extra buffer for formatting/API overhead

  // Calculate the maximum size for the document content within each chunk
  const maxChunkContentSize = Math.max(
    // Ensure it's not negative if overhead is huge
    0,
    Math.floor(modelWindowLimit * percentToUse) -
      fixedOverheadPerCall -
      contextTextsTokens // Subtract tokens used by linked workspace context
  );

  // Original calculation of reservedTokens (includes other context like linked memos)
  // This is still useful for logging or potentially for overall budget checks,
  // but not for limiting *each* document chunk's content size directly.

  const reservedTokens =
    tokenManager.countFromString(documentDraftingPrompt) +
    tokenManager.countFromString(updatedMessage) +
    tokenManager.countFromString(calcContextTexts) + // Context from linked workspaces & memos
    500; // Extra buffer

  console.log(
    `\x1b[36m[DocumentDrafting]\x1b[0m Model has ${modelWindowLimit} token context window, using ${Math.round(percentToUse * 100)}% of window for target usable space.`
  );
  console.log(
    `\x1b[36m[DocumentDrafting]\x1b[0m Tokens for fetched context (memos/vectors): ${contextTextsTokens}.`
  );
  console.log(
    `\x1b[36m[DocumentDrafting]\x1b[0m Estimated fixed overhead per call (prompts/buffer): ${fixedOverheadPerCall} tokens.`
  );
  console.log(
    `\x1b[36m[DocumentDrafting]\x1b[0m Calculated max content size per chunk: ${maxChunkContentSize} tokens.`
  );
  console.log(
    `\x1b[36m[DocumentDrafting]\x1b[0m Initially reserved tokens (including other context): ${reservedTokens} tokens.`
  );

  const folderName = getUserDocumentPathName(user, true, workspace.slug);
  const workspacePath = path.join(
    __dirname,
    "../../storage/documents",
    folderName
  );
  if (!fs.existsSync(workspacePath)) {
    return { success: false, message: "Workspace not found" };
  }

  const files = fs
    .readdirSync(workspacePath)
    .filter((file) => file.endsWith(".json"));
  const contextChunks = [];
  let currentChunk = "";
  let currentChunkTokens = 0;
  let currentChunkFiles = [];

  // Process all documents consistently, ensuring proper chunking for any provider
  for (const file of files) {
    const filePath = path.join(workspacePath, file);
    const fileContent = fs.readFileSync(filePath, "utf8");
    const documentData = JSON.parse(fileContent);

    if (documentData.pageContent) {
      const contentTokens = documentData.token_count_estimate || 0;

      // If content is too large, split it intelligently into smaller pieces
      // This approach works for any provider
      if (contentTokens > maxChunkContentSize) {
        console.log(
          `\x1b[36m[DocumentDrafting]\x1b[0m File ${file} is large (${contentTokens} tokens > ${maxChunkContentSize}), splitting into multiple chunks`
        );

        const content = documentData.pageContent;
        const tokens = tokenManager.tokensFromString(content);

        // Universal chunk size calculation - works for any model
        const chunkSize = Math.floor(maxChunkContentSize * 0.9); // Use 90% of the calculated max content size for safety
        const overlapSize = 50;

        // Create multiple chunks from this single document with overlap
        for (let i = 0; i < tokens.length; i += chunkSize - overlapSize) {
          // Ensure we don't go past the end of the tokens array
          const endIdx = Math.min(i + chunkSize, tokens.length);
          const chunkTokens = tokens.slice(i, endIdx);
          const chunkContent = tokenManager.bytesFromTokens(chunkTokens);

          // Add document metadata to help with context - useful for any model
          const totalParts = Math.ceil(
            tokens.length / (chunkSize - overlapSize)
          );
          const currentPart = Math.floor(i / (chunkSize - overlapSize)) + 1;

          const chunkWithMetadata =
            `DOCUMENT: ${documentData.filename || file}\n` +
            `SECTION: Part ${currentPart} of ${totalParts}\n` +
            `${chunkContent}`;

          // Start a new chunk for each piece
          contextChunks.push(chunkWithMetadata);
          console.log(
            `\x1b[36m[DocumentDrafting]\x1b[0m Created chunk ${contextChunks.length} with ~${chunkTokens.length} tokens from document part ${currentPart}/${totalParts} (with ${overlapSize} token overlap)`
          );
        }

        // Skip the normal document handling since we've already processed this document
        continue;
      }

      // If adding this content would exceed the limit, start a new chunk
      if (currentChunkTokens + contentTokens > maxChunkContentSize) {
        if (currentChunk) {
          contextChunks.push(currentChunk);
        }

        // When starting a new chunk, check if we should include the last part
        // of the previous chunk to create an overlap
        const overlapSize = 50; // 50 token overlap
        let overlapText = "";

        if (currentChunk && currentChunkTokens > overlapSize) {
          // Extract the last ~50 tokens from the previous chunk as overlap
          const currentChunkTokenList =
            tokenManager.tokensFromString(currentChunk);
          const overlapTokens = currentChunkTokenList.slice(-overlapSize);
          overlapText = tokenManager.bytesFromTokens(overlapTokens);

          console.log(
            `\x1b[36m[DocumentDrafting]\x1b[0m Adding ${overlapSize} token overlap between chunks`
          );
        }

        currentChunk =
          overlapText + (overlapText ? "\n\n" : "") + documentData.pageContent;
        currentChunkTokens = tokenManager.countFromString(currentChunk);
        currentChunkFiles = [file];
      } else {
        // Add to current chunk with separator if needed
        if (currentChunk) currentChunk += "\n\n";
        currentChunk += documentData.pageContent;
        currentChunkTokens += contentTokens;
        currentChunkFiles.push(file);
      }
    }
  }

  if (currentChunk) {
    contextChunks.push(currentChunk);
  }

  // Log the number of chunks created - useful for any provider
  console.log(
    `\x1b[36m[DocumentDrafting]\x1b[0m Created ${contextChunks.length} context chunks for processing`
  );

  let totalTokenCount = contextChunks.reduce((sum, chunk, index) => {
    const chunkTokens = tokenManager.countFromString(chunk);
    console.log(
      `\x1b[36m[DocumentDrafting]\x1b[0m Chunk ${index + 1} size: ${chunkTokens} tokens`
    );
    return sum + chunkTokens;
  }, 0);

  console.log(
    `\x1b[36m[DocumentDrafting]\x1b[0m Total token count across all chunks: ${totalTokenCount}`
  );

  // ----------------------------------
  // STEP 2: Processing document chunks
  // ----------------------------------
  if (contextChunks.length > 0) {
    // Advance to the next logical parent step so we don't overwrite the previous (memos) step.
    ddStepCounter += 1;

    // Emit an initial progress marker for chunk processing
    sendProgress({
      step: ddStepCounter,
      label: "streamdd_progress_modal.step_processing_chunks",
      description: "streamdd_progress_modal.desc_processing_chunks",
      progress: -1,
      total: contextChunks.length,
    });
  }

  let messages = [];
  // Handle streaming or regular response based on LLM connector capabilities
  if (LLMConnector.streamingEnabled() !== true) {
    messages = []; // Clear messages array
    console.log(
      `\x1b[31m[STREAMING DISABLED]\x1b[0m Streaming is not available for ${LLMConnector.constructor.name}. Will use regular chat method.`
    );

    // If no chunks, just process the direct question
    if (contextChunks.length === 0) {
      const chunkMessages = await LLMConnector.compressMessages({
        systemPrompt: documentDraftingPrompt,
        userPrompt: updatedMessage,
        contextTexts: [],
        chatHistory,
        attachments,
      });
      messages.push({
        type: "direct_query",
        description: "Direct question without context",
        messages: chunkMessages,
      });

      try {
        const { textResponse: partialResponse, metrics: chunkMetrics } =
          await LLMConnector.getChatCompletion(chunkMessages, {
            temperature: temperatureValue,
          });

        if (typeof partialResponse === "string") {
          completeText = partialResponse;
          metrics = chunkMetrics;
        } else {
          console.error(
            `Received invalid textResponse: ${typeof partialResponse}`
          );
          writeResponseChunk(response, {
            uuid,
            type: "abort",
            textResponse: "Error processing your request.",
            sources: [],
            close: true,
            error: `Error processing document: Invalid response format`,
          });
          return;
        }
      } catch (err) {
        console.error(`Error getting chat completion: ${err.message}`);
        writeResponseChunk(response, {
          uuid,
          type: "abort",
          textResponse: "Error processing your request.",
          sources: [],
          close: true,
          error: `Error processing document: ${err.message}`,
        });
        return;
      }
    } else {
      const partialResponses = [];
      // Mark chunk-processing step as active (ddStepCounter already set)
      let currentChunkStep = ddStepCounter;
      let chunkIndex = 0;
      for (const chunk of contextChunks) {
        chunkIndex += 1;
        // Emit progress for this chunk start
        sendProgress({
          step: currentChunkStep,
          subStep: chunkIndex,
          label: "streamdd_progress_modal.sub_step_chunk_label",
          labelArgs: { index: chunkIndex },
          total: contextChunks.length,
          progress: -1,
        });
        const chunkMessages = await LLMConnector.compressMessages({
          systemPrompt: documentDraftingPrompt,
          userPrompt: updatedMessage,
          contextTexts: [...memoResults, chunk],
          chatHistory,
          attachments,
        });
        messages.push({
          type: "partial_chunk",
          description: `Chunk ${contextChunks.indexOf(chunk) + 1} of ${
            contextChunks.length
          }`,
          messages: chunkMessages,
        });

        try {
          // For single chunk case, use streaming
          if (contextChunks.length === 1) {
            sendProcessComplete();
            const stream = await LLMConnector.streamGetChatCompletion(
              chunkMessages,
              {
                temperature: temperatureValue,
              }
            );
            completeText = await LLMConnector.handleStream(response, stream, {
              uuid,
              sources,
            });
            metrics = stream.metrics;

            // For single chunks, send a single finalizeResponseStream with the complete text
            writeResponseChunk(response, {
              uuid,
              sources,
              type: "finalizeResponseStream",
              textResponse: completeText,
              close: true,
              error: false,
              metrics,
              chatId,
            });
            finalizeSent = true;
            sendProgress({ step: ddStepCounter, progress: 100 });
            return;
          }

          const { textResponse, metrics: chunkMetrics } =
            await LLMConnector.getChatCompletion(chunkMessages, {
              temperature: temperatureValue,
            });
          metrics = chunkMetrics;

          // Verify that textResponse is a valid string before adding it to partialResponses
          if (typeof textResponse === "string") {
            partialResponses.push(textResponse);
            // Mark this sub-step as completed successfully
            sendProgress({
              step: currentChunkStep,
              subStep: chunkIndex,
              progress: 100,
            });
          } else {
            console.error(
              `Received invalid textResponse: ${typeof textResponse}`
            );
            partialResponses.push(
              `Error processing chunk ${contextChunks.indexOf(chunk) + 1}`
            );
            sendProgress({
              step: currentChunkStep,
              subStep: chunkIndex,
              progress: -2,
              error: "Invalid response format",
            });
          }
        } catch (err) {
          console.error(
            `Error getting chat completion for chunk ${contextChunks.indexOf(chunk) + 1}: ${err.message}`
          );
          partialResponses.push(
            `Error processing chunk ${contextChunks.indexOf(chunk) + 1}: ${err.message}`
          );
          sendProgress({
            step: currentChunkStep,
            subStep: chunkIndex,
            progress: -2,
            error: err.message || "Unknown error",
          });
        }
      }

      if (contextChunks.length > 1) {
        // Ensure all partialResponses are valid strings
        const validResponses = partialResponses.filter(
          (response) => typeof response === "string"
        );

        // New step: combining responses (step increments)
        ddStepCounter += 1;
        sendProgress({
          step: ddStepCounter,
          label: "streamdd_progress_modal.step_combining_responses",
          description: "streamdd_progress_modal.desc_combining_responses",
          progress: -1,
        });

        // Create a universal approach to combining responses that works for any provider
        console.log(
          `\x1b[36m[DocumentDrafting]\x1b[0m Combining ${validResponses.length} partial responses`
        );

        // Format responses in a structured way to help any model understand
        const formattedResponses = validResponses.map((resp, index) => {
          // Limit individual response size if needed to prevent any provider issues
          let processedResponse = resp;
          const responseTokens = tokenManager.countFromString(resp);
          const maxResponseSize = Math.floor(
            LLMConnector.promptWindowLimit() * 0.5
          ); // Scale with model size

          if (responseTokens > maxResponseSize) {
            console.log(
              `\x1b[36m[DocumentDrafting]\x1b[0m Truncating response ${index + 1} from ${responseTokens} to ~${maxResponseSize} tokens`
            );
            const tokens = tokenManager.tokensFromString(resp);
            const halfSize = Math.floor(maxResponseSize / 2);
            processedResponse =
              tokenManager.bytesFromTokens(tokens.slice(0, halfSize)) +
              "\n\n--truncated content--\n\n" +
              tokenManager.bytesFromTokens(tokens.slice(-halfSize));
          }

          return {
            index: index + 1,
            content: processedResponse,
            tokens: tokenManager.countFromString(processedResponse),
          };
        });

        // Create a universal combine prompt that works with all providers
        const universalCombinePrompt = combinePrompt
          ? combinePrompt.trim()
          : "";

        // Calculate total tokens and available context window
        const combinePromptTokens = tokenManager.countFromString(
          universalCombinePrompt
        );
        const maxWindowForResponses =
          Math.floor(LLMConnector.promptWindowLimit() * 0.85) -
          combinePromptTokens -
          500; // Reserve space for system prompt and overhead

        // Check if we need hierarchical combining
        const totalResponseTokens = formattedResponses.reduce(
          (sum, resp) => sum + resp.tokens,
          0
        );
        console.log(
          `\x1b[36m[DocumentDrafting]\x1b[0m Total response tokens: ${totalResponseTokens}, available window: ${maxWindowForResponses}`
        );

        let finalCombinationMessages = null;

        if (
          totalResponseTokens > maxWindowForResponses &&
          formattedResponses.length > 2
        ) {
          // Implement hierarchical combination for large response sets
          console.log(
            `\x1b[36m[DocumentDrafting]\x1b[0m Using hierarchical combination due to large response size`
          );

          // Group responses into batches that fit within the context window
          const batches = [];
          let currentBatch = [];
          let currentBatchTokens = 0;

          // Sort responses by token count (ascending) to optimize grouping
          const sortedResponses = [...formattedResponses].sort(
            (a, b) => a.tokens - b.tokens
          );

          for (const response of sortedResponses) {
            // If adding this response would exceed the batch limit, start a new batch
            // Leave ~20% room for formatting and overhead
            if (
              currentBatchTokens + response.tokens >
                maxWindowForResponses * 0.8 &&
              currentBatch.length > 0
            ) {
              batches.push(currentBatch);
              currentBatch = [response];
              currentBatchTokens = response.tokens;
            } else {
              currentBatch.push(response);
              currentBatchTokens += response.tokens;
            }
          }

          // Add the last batch if it's not empty
          if (currentBatch.length > 0) {
            batches.push(currentBatch);
          }

          console.log(
            `\x1b[36m[DocumentDrafting]\x1b[0m Created ${batches.length} batches for hierarchical combination`
          );

          // Process each batch to get intermediate combined responses
          const intermediateCombinations = [];

          for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
            const batch = batches[batchIndex];
            console.log(
              `\x1b[36m[DocumentDrafting]\x1b[0m Processing batch ${batchIndex + 1} with ${batch.length} responses`
            );

            // Format the batch for the combination prompt
            const batchContent = batch
              .map(
                (response) =>
                  `RESPONSE ${response.index}:\n${response.content}\nEND RESPONSE ${response.index}\n\n`
              )
              .join("");

            const batchPrompt = `Process and synthesize this batch of responses (${batchIndex + 1}/${batches.length}):\n\n${batchContent}`;
            const batchCombinationMessages =
              await LLMConnector.compressMessages({
                systemPrompt: universalCombinePrompt,
                userPrompt: batchPrompt,
                contextTexts: [],
                attachments,
              });

            // Get completion for this batch
            try {
              const { textResponse } = await LLMConnector.getChatCompletion(
                batchCombinationMessages,
                { temperature: temperatureValue * 0.8 } // Use slightly lower temperature for intermediate combinations
              );

              if (typeof textResponse === "string") {
                intermediateCombinations.push({
                  batchIndex: batchIndex + 1,
                  content: textResponse,
                  tokens: tokenManager.countFromString(textResponse),
                });
                console.log(
                  `\x1b[36m[DocumentDrafting]\x1b[0m Batch ${batchIndex + 1} produced ${tokenManager.countFromString(textResponse)} tokens`
                );
              } else {
                console.error(
                  `Received invalid batch response: ${typeof textResponse}`
                );
                intermediateCombinations.push({
                  batchIndex: batchIndex + 1,
                  content: `Error processing batch ${batchIndex + 1}`,
                  tokens: 0,
                });
              }
            } catch (err) {
              console.error(
                `Error processing batch ${batchIndex + 1}: ${err.message}`
              );
              intermediateCombinations.push({
                batchIndex: batchIndex + 1,
                content: `Error processing batch ${batchIndex + 1}: ${err.message}`,
                tokens: 0,
              });
            }
          }

          // Final combination of intermediate results
          console.log(
            `\x1b[36m[DocumentDrafting]\x1b[0m Creating final synthesis from ${intermediateCombinations.length} intermediate combinations`
          );

          // Format intermediate combinations for the final prompt
          const finalCombinationContent = intermediateCombinations
            .map(
              (combo) =>
                `SYNTHESIS ${combo.batchIndex}:\n${combo.content}\nEND SYNTHESIS ${combo.batchIndex}\n\n`
            )
            .join("");

          const finalUserPrompt = `Create a final comprehensive synthesis from these intermediate results:\n\n${finalCombinationContent}`;

          // Use streaming for the final combination to show progress to the user
          finalCombinationMessages = await LLMConnector.compressMessages({
            systemPrompt:
              "You are synthesizing multiple intermediate results into a final comprehensive answer. Focus on creating a cohesive, well-structured response that maintains all key information.",
            userPrompt: finalUserPrompt,
            contextTexts: [],
            attachments,
          });

          messages.push({
            type: "hierarchical_combination",
            description: "Final synthesis of intermediate combinations",
            messages: finalCombinationMessages,
          });
        } else {
          // Standard combining approach for smaller response sets
          // Format the content for LLM processing
          const combinedContent = formattedResponses
            .map(
              (resp) =>
                `RESPONSE ${resp.index}:\n${resp.content}\nEND RESPONSE ${resp.index}\n\n`
            )
            .join("");

          // Log combination message size for any provider
          const combinedSize = tokenManager.countFromString(combinedContent);
          console.log(
            `\x1b[36m[DocumentDrafting]\x1b[0m Combined responses size: ${combinedSize} tokens`
          );

          // Create combination messages in a provider-agnostic way
          finalCombinationMessages = await LLMConnector.compressMessages({
            systemPrompt: universalCombinePrompt,
            userPrompt: combinedContent,
            contextTexts: [],
            attachments,
          });

          // Log final message structure
          console.log(
            `\x1b[36m[DocumentDrafting]\x1b[0m Final combination message type: ${typeof finalCombinationMessages}, isArray: ${Array.isArray(finalCombinationMessages)}`
          );
          if (
            Array.isArray(finalCombinationMessages) &&
            finalCombinationMessages.length > 0
          ) {
            console.log(
              `\x1b[36m[DocumentDrafting]\x1b[0m First message type: ${typeof finalCombinationMessages[0]}`
            );
          }

          messages.push({
            type: "combination",
            description: "Final combination of all partial responses",
            messages: finalCombinationMessages,
          });
        }

        // Stream the final combined result, regardless of which method we used
        try {
          sendProcessComplete();
          const finalStream = await LLMConnector.streamGetChatCompletion(
            finalCombinationMessages,
            { temperature: temperatureValue }
          );

          completeText = await LLMConnector.handleStream(
            response,
            finalStream,
            {
              uuid,
              sources,
            }
          );

          metrics = finalStream.metrics;

          // Send a single finalizeResponseStream with the complete text
          writeResponseChunk(response, {
            uuid,
            sources,
            type: "finalizeResponseStream",
            textResponse: completeText,
            close: true,
            error: false,
            metrics,
            chatId,
          });
          finalizeSent = true;
          sendProgress({ step: ddStepCounter, progress: 100 });
          return;
        } catch (err) {
          // Add enhanced error logging
          console.error(`Error in final stream processing: ${err.message}`);
          console.error(`Final stream error details:`, {
            errorName: err.name,
            fullError: err.toString(),
            stack: err.stack,
            llmConnector: LLMConnector.constructor.name,
            combinationMessagesFormat:
              JSON.stringify(finalCombinationMessages).substring(0, 200) +
              "...",
            validResponsesCount: validResponses.length,
          });

          writeResponseChunk(response, {
            uuid,
            type: "abort",
            textResponse: "Error processing your request.",
            sources: [],
            close: true,
            error: `Error in final processing: ${err.message}`,
          });
          return;
        }
      }
    }
  } else {
    messages = []; // Clear messages array

    if (contextChunks.length === 0) {
      const chunkMessages = await LLMConnector.compressMessages(
        {
          systemPrompt: documentDraftingPrompt,
          userPrompt: updatedMessage,
          contextTexts: [],
          chatHistory,
          attachments,
        },
        rawHistory
      );
      messages.push({
        type: "direct_query",
        description: "Direct question without context",
        messages: chunkMessages,
      });

      sendProcessComplete();
      const stream = await LLMConnector.streamGetChatCompletion(chunkMessages, {
        temperature: temperatureValue,
      });

      completeText = await LLMConnector.handleStream(response, stream, {
        uuid,
        sources,
      });

      metrics = stream.metrics;
    } else {
      const partialResponses = [];

      // Log the context chunks before processing
      console.log(
        `\x1b[36m[DocumentDrafting]\x1b[0m Processing ${contextChunks.length} context chunks with ${LLMConnector.constructor.name}`
      );
      for (let i = 0; i < contextChunks.length; i++) {
        const chunkTokenCount = tokenManager.countFromString(contextChunks[i]);
        console.log(
          `\x1b[36m[DocumentDrafting]\x1b[0m Context chunk ${i + 1} size: ${chunkTokenCount} tokens`
        );
      }

      // Mark chunk-processing step as active (ddStepCounter already set)
      let currentChunkStep = ddStepCounter;
      let chunkIndex = 0;
      for (const chunk of contextChunks) {
        chunkIndex += 1;
        // Emit progress for this chunk start
        sendProgress({
          step: currentChunkStep,
          subStep: chunkIndex,
          label: "streamdd_progress_modal.sub_step_chunk_label",
          labelArgs: { index: chunkIndex },
          total: contextChunks.length,
          progress: -1,
        });
        const chunkMessages = await LLMConnector.compressMessages({
          systemPrompt: documentDraftingPrompt,
          userPrompt: updatedMessage,
          contextTexts: [...memoResults, chunk],
          chatHistory,
          attachments,
        });
        messages.push({
          type: "partial_chunk",
          description: `Chunk ${contextChunks.indexOf(chunk) + 1} of ${
            contextChunks.length
          }`,
          messages: chunkMessages,
        });

        try {
          // For single chunk case, use streaming
          if (contextChunks.length === 1) {
            sendProcessComplete();
            const stream = await LLMConnector.streamGetChatCompletion(
              chunkMessages,
              {
                temperature: temperatureValue,
              }
            );
            completeText = await LLMConnector.handleStream(response, stream, {
              uuid,
              sources,
            });
            metrics = stream.metrics;

            // For single chunks, send a single finalizeResponseStream with the complete text
            writeResponseChunk(response, {
              uuid,
              sources,
              type: "finalizeResponseStream",
              textResponse: completeText,
              close: true,
              error: false,
              metrics,
              chatId,
            });
            finalizeSent = true;
            sendProgress({ step: ddStepCounter, progress: 100 });
            return;
          }

          const { textResponse, metrics: chunkMetrics } =
            await LLMConnector.getChatCompletion(chunkMessages, {
              temperature: temperatureValue,
            });
          metrics = chunkMetrics;

          // Verify that textResponse is a valid string before adding it to partialResponses
          if (typeof textResponse === "string") {
            partialResponses.push(textResponse);
            // Mark this sub-step as completed successfully
            sendProgress({
              step: currentChunkStep,
              subStep: chunkIndex,
              progress: 100,
            });
          } else {
            console.error(
              `Received invalid textResponse: ${typeof textResponse}`
            );
            partialResponses.push(
              `Error processing chunk ${contextChunks.indexOf(chunk) + 1}`
            );
            sendProgress({
              step: currentChunkStep,
              subStep: chunkIndex,
              progress: -2,
              error: "Invalid response format",
            });
          }
        } catch (err) {
          console.error(
            `Error getting chat completion for chunk ${contextChunks.indexOf(chunk) + 1}: ${err.message}`
          );
          partialResponses.push(
            `Error processing chunk ${contextChunks.indexOf(chunk) + 1}: ${err.message}`
          );
          sendProgress({
            step: currentChunkStep,
            subStep: chunkIndex,
            progress: -2,
            error: err.message || "Unknown error",
          });
        }
      }

      if (contextChunks.length > 1) {
        // Ensure all partialResponses are valid strings
        const validResponses = partialResponses.filter(
          (response) => typeof response === "string"
        );

        // New step: combining responses (step increments)
        ddStepCounter += 1;
        sendProgress({
          step: ddStepCounter,
          label: "streamdd_progress_modal.step_combining_responses",
          description: "streamdd_progress_modal.desc_combining_responses",
          progress: -1,
        });

        // Create a universal approach to combining responses that works for any provider
        console.log(
          `\x1b[36m[DocumentDrafting]\x1b[0m Combining ${validResponses.length} partial responses`
        );

        // Format responses in a structured way to help any model understand
        const formattedResponses = validResponses.map((resp, index) => {
          // Limit individual response size if needed to prevent any provider issues
          let processedResponse = resp;
          const responseTokens = tokenManager.countFromString(resp);
          const maxResponseSize = Math.floor(
            LLMConnector.promptWindowLimit() * 0.5
          ); // Scale with model size

          if (responseTokens > maxResponseSize) {
            console.log(
              `\x1b[36m[DocumentDrafting]\x1b[0m Truncating response ${index + 1} from ${responseTokens} to ~${maxResponseSize} tokens`
            );
            const tokens = tokenManager.tokensFromString(resp);
            const halfSize = Math.floor(maxResponseSize / 2);
            processedResponse =
              tokenManager.bytesFromTokens(tokens.slice(0, halfSize)) +
              "\n\n--truncated content--\n\n" +
              tokenManager.bytesFromTokens(tokens.slice(-halfSize));
          }

          return {
            index: index + 1,
            content: processedResponse,
            tokens: tokenManager.countFromString(processedResponse),
          };
        });

        // Create a universal combine prompt that works with all providers
        const universalCombinePrompt = combinePrompt
          ? combinePrompt.trim()
          : "";

        // Calculate total tokens and available context window
        const combinePromptTokens = tokenManager.countFromString(
          universalCombinePrompt
        );
        const maxWindowForResponses =
          Math.floor(LLMConnector.promptWindowLimit() * 0.85) -
          combinePromptTokens -
          500; // Reserve space for system prompt and overhead

        // Check if we need hierarchical combining
        const totalResponseTokens = formattedResponses.reduce(
          (sum, resp) => sum + resp.tokens,
          0
        );
        console.log(
          `\x1b[36m[DocumentDrafting]\x1b[0m Total response tokens: ${totalResponseTokens}, available window: ${maxWindowForResponses}`
        );

        let finalCombinationMessages = null;

        if (
          totalResponseTokens > maxWindowForResponses &&
          formattedResponses.length > 2
        ) {
          // Implement hierarchical combination for large response sets
          console.log(
            `\x1b[36m[DocumentDrafting]\x1b[0m Using hierarchical combination due to large response size`
          );

          // Group responses into batches that fit within the context window
          const batches = [];
          let currentBatch = [];
          let currentBatchTokens = 0;

          // Sort responses by token count (ascending) to optimize grouping
          const sortedResponses = [...formattedResponses].sort(
            (a, b) => a.tokens - b.tokens
          );

          for (const response of sortedResponses) {
            // If adding this response would exceed the batch limit, start a new batch
            // Leave ~20% room for formatting and overhead
            if (
              currentBatchTokens + response.tokens >
                maxWindowForResponses * 0.8 &&
              currentBatch.length > 0
            ) {
              batches.push(currentBatch);
              currentBatch = [response];
              currentBatchTokens = response.tokens;
            } else {
              currentBatch.push(response);
              currentBatchTokens += response.tokens;
            }
          }

          // Add the last batch if it's not empty
          if (currentBatch.length > 0) {
            batches.push(currentBatch);
          }

          console.log(
            `\x1b[36m[DocumentDrafting]\x1b[0m Created ${batches.length} batches for hierarchical combination`
          );

          // Process each batch to get intermediate combined responses
          const intermediateCombinations = [];

          for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
            const batch = batches[batchIndex];
            console.log(
              `\x1b[36m[DocumentDrafting]\x1b[0m Processing batch ${batchIndex + 1} with ${batch.length} responses`
            );

            // Format the batch for the combination prompt
            const batchContent = batch
              .map(
                (response) =>
                  `RESPONSE ${response.index}:\n${response.content}\nEND RESPONSE ${response.index}\n\n`
              )
              .join("");

            const batchPrompt = `Process and synthesize this batch of responses (${batchIndex + 1}/${batches.length}):\n\n${batchContent}`;
            const batchCombinationMessages =
              await LLMConnector.compressMessages({
                systemPrompt: universalCombinePrompt,
                userPrompt: batchPrompt,
                contextTexts: [],
                attachments,
              });

            // Get completion for this batch
            try {
              const { textResponse } = await LLMConnector.getChatCompletion(
                batchCombinationMessages,
                { temperature: temperatureValue * 0.8 } // Use slightly lower temperature for intermediate combinations
              );

              if (typeof textResponse === "string") {
                intermediateCombinations.push({
                  batchIndex: batchIndex + 1,
                  content: textResponse,
                  tokens: tokenManager.countFromString(textResponse),
                });
                console.log(
                  `\x1b[36m[DocumentDrafting]\x1b[0m Batch ${batchIndex + 1} produced ${tokenManager.countFromString(textResponse)} tokens`
                );
              } else {
                console.error(
                  `Received invalid batch response: ${typeof textResponse}`
                );
                intermediateCombinations.push({
                  batchIndex: batchIndex + 1,
                  content: `Error processing batch ${batchIndex + 1}`,
                  tokens: 0,
                });
              }
            } catch (err) {
              console.error(
                `Error processing batch ${batchIndex + 1}: ${err.message}`
              );
              intermediateCombinations.push({
                batchIndex: batchIndex + 1,
                content: `Error processing batch ${batchIndex + 1}: ${err.message}`,
                tokens: 0,
              });
            }
          }

          // Final combination of intermediate results
          console.log(
            `\x1b[36m[DocumentDrafting]\x1b[0m Creating final synthesis from ${intermediateCombinations.length} intermediate combinations`
          );

          // Format intermediate combinations for the final prompt
          const finalCombinationContent = intermediateCombinations
            .map(
              (combo) =>
                `SYNTHESIS ${combo.batchIndex}:\n${combo.content}\nEND SYNTHESIS ${combo.batchIndex}\n\n`
            )
            .join("");

          const finalUserPrompt = `Create a final comprehensive synthesis from these intermediate results:\n\n${finalCombinationContent}`;

          // Use streaming for the final combination to show progress to the user
          finalCombinationMessages = await LLMConnector.compressMessages({
            systemPrompt:
              "You are synthesizing multiple intermediate results into a final comprehensive answer. Focus on creating a cohesive, well-structured response that maintains all key information.",
            userPrompt: finalUserPrompt,
            contextTexts: [],
            attachments,
          });

          messages.push({
            type: "hierarchical_combination",
            description: "Final synthesis of intermediate combinations",
            messages: finalCombinationMessages,
          });
        } else {
          // Standard combining approach for smaller response sets
          // Format the content for LLM processing
          const combinedContent = formattedResponses
            .map(
              (resp) =>
                `RESPONSE ${resp.index}:\n${resp.content}\nEND RESPONSE ${resp.index}\n\n`
            )
            .join("");

          // Log combination message size for any provider
          const combinedSize = tokenManager.countFromString(combinedContent);
          console.log(
            `\x1b[36m[DocumentDrafting]\x1b[0m Combined responses size: ${combinedSize} tokens`
          );

          // Create combination messages in a provider-agnostic way
          finalCombinationMessages = await LLMConnector.compressMessages({
            systemPrompt: universalCombinePrompt,
            userPrompt: combinedContent,
            contextTexts: [],
            attachments,
          });

          // Log final message structure
          console.log(
            `\x1b[36m[DocumentDrafting]\x1b[0m Final combination message type: ${typeof finalCombinationMessages}, isArray: ${Array.isArray(finalCombinationMessages)}`
          );
          if (
            Array.isArray(finalCombinationMessages) &&
            finalCombinationMessages.length > 0
          ) {
            console.log(
              `\x1b[36m[DocumentDrafting]\x1b[0m First message type: ${typeof finalCombinationMessages[0]}`
            );
          }

          messages.push({
            type: "combination",
            description: "Final combination of all partial responses",
            messages: finalCombinationMessages,
          });
        }

        // Stream the final combined result, regardless of which method we used
        try {
          sendProcessComplete();
          const finalStream = await LLMConnector.streamGetChatCompletion(
            finalCombinationMessages,
            { temperature: temperatureValue }
          );

          completeText = await LLMConnector.handleStream(
            response,
            finalStream,
            {
              uuid,
              sources,
            }
          );

          metrics = finalStream.metrics;

          // Send a single finalizeResponseStream with the complete text
          writeResponseChunk(response, {
            uuid,
            sources,
            type: "finalizeResponseStream",
            textResponse: completeText,
            close: true,
            error: false,
            metrics,
            chatId,
          });
          finalizeSent = true;
          sendProgress({ step: ddStepCounter, progress: 100 });
          return;
        } catch (err) {
          // Add enhanced error logging
          console.error(`Error in final stream processing: ${err.message}`);
          console.error(`Final stream error details:`, {
            errorName: err.name,
            fullError: err.toString(),
            stack: err.stack,
            llmConnector: LLMConnector.constructor.name,
            combinationMessagesFormat:
              JSON.stringify(finalCombinationMessages).substring(0, 200) +
              "...",
            validResponsesCount: validResponses.length,
          });

          writeResponseChunk(response, {
            uuid,
            type: "abort",
            textResponse: "Error processing your request.",
            sources: [],
            close: true,
            error: `Error in final processing: ${err.message}`,
          });
          return;
        }
      }
    }
  }

  // Save chat logs for auditing or debugging purposes
  // TODO Ensure full alignment with Infinity Context Window, to log all tokens used in generating final response
  if (completeText?.length > 0) {
    if (!preventChatCreation) {
      // Calculate completion tokens only once
      const completionTokens = tokenManager.countFromString(completeText) || 0;

      // Create a clean metrics object without any token counts that might confuse the UI
      const {
        prompt_tokens,
        completion_tokens,
        total_tokens,
        baseTokensUsed: _baseTokensUsed,
        memoTokensUsed: _memoTokensUsed,
        vectorTokensUsed: _vectorTokensUsed,
        usedTokens: _usedTokens,
        ...otherMetrics
      } = metrics || {};

      // Use token counts from our tracking variables for consistency
      const finalPromptTokens = usedTokens || 0;
      const finalCompletionTokens = completionTokens;

      // Ensure total is exactly the sum of the input and output tokens
      const finalTotalTokens = finalPromptTokens + finalCompletionTokens;

      // Log the token counts for debugging
      console.log(
        `\x1b[36m[TokenMetrics]\x1b[0m Final token counts: prompt=${finalPromptTokens}, completion=${finalCompletionTokens}, total=${finalTotalTokens}`
      );

      const metricsToSave = {
        ...otherMetrics,
        prompt_tokens: finalPromptTokens,
        completion_tokens: finalCompletionTokens,
        total_tokens: finalTotalTokens,
        // Include the detailed breakdowns for the document drafting UI
        baseTokensUsed,
        memoTokensUsed,
        vectorTokensUsed,
      };

      const { chat } = await WorkspaceChats.new({
        workspaceId: workspace.id,
        prompt: displayMessage || message,
        response: {
          text: completeText,
          sources: sourcesSave,
          type: chatMode,
          attachments,
          metrics: metricsToSave,
        },
        threadId: thread?.id || null,
        include: true,
        user,
        invoice_ref,
        metrics: metricsToSave,
      });
      chatId = chat?.id;
    } else if (chatId) {
      // Calculate completion tokens only once
      const completionTokens = tokenManager.countFromString(completeText) || 0;

      // Get existing chat
      const chat = await WorkspaceChats.get({ id: chatId });
      const chatResponse = JSON.parse(chat.response);
      chatResponse.text = completeText;

      // Get existing metrics but remove any token fields to avoid confusion
      const existingMetrics = chat.metrics ? JSON.parse(chat.metrics) : {};
      const {
        prompt_tokens,
        completion_tokens,
        total_tokens,
        baseTokensUsed: _baseTokensUsed,
        memoTokensUsed: _memoTokensUsed,
        vectorTokensUsed: _vectorTokensUsed,
        usedTokens: _usedTokens,
        ...otherMetrics
      } = existingMetrics;

      // Use token counts from our tracking variables for consistency
      const finalPromptTokens = usedTokens || 0;
      const finalCompletionTokens = completionTokens;

      // Ensure total is exactly the sum of the input and output tokens
      const finalTotalTokens = finalPromptTokens + finalCompletionTokens;

      // Log the token counts for debugging
      console.log(
        `\x1b[36m[TokenMetrics]\x1b[0m Updated token counts: prompt=${finalPromptTokens}, completion=${finalCompletionTokens}, total=${finalTotalTokens}`
      );

      const metricsToSave = {
        ...otherMetrics,
        prompt_tokens: finalPromptTokens,
        completion_tokens: finalCompletionTokens,
        total_tokens: finalTotalTokens,
        // Include the detailed breakdowns for the document drafting UI
        baseTokensUsed,
        memoTokensUsed,
        vectorTokensUsed,
      };

      await WorkspaceChats.update(chatId, {
        response: JSON.stringify(chatResponse),
        metrics: metricsToSave,
      });
    }

    if (chatId) {
      const saveChatLog = await SystemSettings.isPromptOutputLogging();
      if (saveChatLog && chatId) {
        const logsDir = path.join(__dirname, "../../storage/logs");
        fs.mkdirSync(logsDir, { recursive: true });
        const filePath = path.join(logsDir, `workspace-chat-${chatId}.json`);
        fs.writeFile(filePath, JSON.stringify(messages, null, 2), (err) => {
          if (err) {
            console.error("Error saving chat log file:", err);
          }
        });
      }
    }

    // Only send finalizeResponseStream if we haven't already sent one
    if (!finalizeSent) {
      writeResponseChunk(response, {
        uuid,
        type: "finalizeResponseStream",
        close: true,
        error: false,
        chatId,
        metrics,
      });
    }
    return;
  }

  // Only send finalizeResponseStream if we haven't already sent one
  if (!finalizeSent) {
    writeResponseChunk(response, {
      uuid,
      type: "finalizeResponseStream",
      close: true,
      error: false,
    });
  }

  // Increment step counter for next phase
  ddStepCounter += 1;
}

module.exports = {
  VALID_CHAT_MODE,
  DEFAULT_COMBINE_PROMPT,
  DEFAULT_DOCUMENT_DRAFTING_PROMPT,
  DEFAULT_LEGAL_ISSUES_PROMPT,
  DEFAULT_MEMO_PROMPT,
  streamChatWithWorkspaceDD,
};
