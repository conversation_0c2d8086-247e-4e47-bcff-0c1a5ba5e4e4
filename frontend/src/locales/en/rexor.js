export default {
  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Register Rexor Project",
    "project-id": "Project ID",
    "resource-id": "Resource ID",
    "activity-id": "Activity ID",
    register: "Register Project",
    "invoice-text": "Foynet number of lookups",
    registering: "registering ...",
    "not-active": "This case is not active to register",
    account: {
      title: "Login to <PERSON>or",
      username: "Username",
      password: "Password",
      "no-token": "No token received in handleLoginSuccess",
      logout: "Logout",
      "no-user": "Please loggin first",
      connected: "Connected to <PERSON><PERSON>",
      "not-connected": "Not connected",
      "change-account": "Logout/Change Rexor account",
      "session-expired": "Session expired. Please log in again.",
    },
    "hide-article-transaction": "Hide Article Transaction Form",
    "show-article-transaction": "Show Article Transaction Form",
    "article-transaction-title": "Add Article Transaction",
    "registration-date": "Registration Date",
    description: "Description",
    "description-internal": "Internal Description",
    "hours-worked": "Hours Worked",
    "invoiced-hours": "Invoiced Hours",
    invoiceable: "Invoiceable",
    "sending-article-transaction": "Sending Article Transaction...",
    "save-article-transaction": "Save Article Transaction",
    "project-not-register": "Project must be registered first.",
    "article-transaction-error": "Failed to write article transaction",
    "not-exist": "This case could not be found",
    "missing-economy-id-admin":
      "Please add an economy ID to your user profile before registering a project.",
    "missing-economy-id-user":
      "Please ask system admin to add an economy ID to your user profile to be able to register a project.",
    "api-settings": {
      title: "Rexor API Configuration",
      description:
        "Configure custom Rexor API endpoints and credentials. Leave empty to use default values.",
      "loading-message": "Loading Rexor API settings...",
      "api-base-url": "API Base URL",
      "api-base-url-placeholder": "https://api.rexor.se/v231/Api",
      "auth-url": "Authentication URL",
      "auth-url-placeholder": "https://auth.rexor.se/v231/Token",
      "client-id-dev": "Development Client ID",
      "client-id-dev-placeholder": "testfoyen",
      "client-id-prod": "Production Client ID",
      "client-id-prod-placeholder": "foyen",
      "api-host": "API Host",
      "api-host-placeholder": "api.rexor.se",
      "save-button": "Save Settings",
      "reset-button": "Reset to Defaults",
      "success-message": "Rexor API settings saved successfully",
      "error-message": "Failed to save Rexor API settings",
    },
  },
};
