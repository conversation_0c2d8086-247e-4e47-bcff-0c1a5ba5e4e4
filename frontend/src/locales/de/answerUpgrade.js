export default {
  "answer-upgrade": {
    title: "Antwort aktualisieren",
    subtitle: "Unterkategorie auswählen",
    steps: "Rechtliche Aufgaben werden ausgeführt",
    planning: "Planung läuft...",
    "cancel-process":
      "<PERSON><PERSON> du sicher, dass du den Vorgang der rechtlichen Aufgabe abbrechen möchtest?",
    "wait-process": "Bitte warte, während der Vorgang vorbereitet wird.",
    "process-title-one": "Aktionsplan erstellen",
    "process-description-one":
      "Definition der Struktur, Ziele und Hauptaufgaben des Dokuments für einen klaren und organisierten Prozess.",
    "process-title-two": "Aktionsschritt erstellen",
    "process-description-two":
      "Ausführung der Aufgaben aus dem Plan, Erstellung von Inhalten und Abschnitten basierend auf Kontext und Zweck.",
    "process-title-three": "Endergebnis erstellen",
    "process-description-three":
      "Kombination aller Inhalte, Verfeinerung auf Genauigkeit und Bereitstellung eines fertigen Dokuments.",
    "category-step": {
      title: "Kategorie auswählen",
      description:
        "Wähle eine Kategorie, die am besten zu deinen Bedürfnissen passt",
      categories: {
        formality: {
          label: "Formalität",
          choices: {
            more_formal: "Formeller gestalten",
            less_formal: "Weniger formell gestalten",
            more_professional: "Professioneller gestalten",
            more_casual: "Legerer gestalten",
            more_polished: "Ausgereifter gestalten",
            more_relaxed: "Entspannter gestalten",
            academic_tone: "Akademischen Ton verwenden",
            conversational_tone: "Gesprächston verwenden",
          },
        },
        complexity: {
          label: "Sprachkomplexität",
          choices: {
            simplify: "Sprache vereinfachen",
            more_descriptive: "Mehr beschreibende Sprache hinzufügen",
            complex_vocab: "Komplexeren Wortschatz verwenden",
            simple_vocab: "Einfacheren Wortschatz verwenden",
            technical: "Technischere Sprache verwenden",
            layman: "Laienhaft formulieren",
            add_jargon: "Fachspezifischen Jargon einbauen",
            avoid_jargon: "Jargon vermeiden",
            add_rhetorical: "Mehr rhetorische Fragen einbauen",
            less_rhetorical: "Weniger rhetorische Fragen verwenden",
          },
        },
        structure: {
          label: "Satzstruktur",
          choices: {
            shorter: "Sätze verkürzen",
            longer: "Sätze verlängern",
            vary: "Satzstruktur variieren",
            standardize: "Satzstruktur vereinheitlichen",
            more_complex: "Komplexere Sätze verwenden",
            simpler: "Einfachere Sätze verwenden",
            active_voice: "Aktive Stimme verstärken",
            passive_voice: "Passive Stimme verstärken",
          },
        },
        figurative: {
          label: "Bildhafte Sprache",
          choices: {
            more_figurative: "Mehr bildhafte Sprache verwenden",
            less_figurative: "Weniger bildhafte Sprache verwenden",
            metaphors: "Mehr Metaphern und Vergleiche einbauen",
            literal: "Wörtlicher formulieren",
            more_idioms: "Mehr Redewendungen einbauen",
            less_idioms: "Weniger Redewendungen verwenden",
            more_symbolism: "Mehr Symbolik einbauen",
            less_symbolism: "Weniger Symbolik verwenden",
          },
        },
        conciseness: {
          label: "Prägnanz",
          choices: {
            more_concise: "Kürzer formulieren",
            more_wordy: "Ausführlicher formulieren",
            remove_redundant: "Überflüssige Phrasen entfernen",
            add_details: "Mehr Details hinzufügen",
            reduce_filler: "Füllwörter reduzieren",
            add_elaboration: "Mehr Erläuterungen einbauen",
          },
        },
        imagery: {
          label: "Bildhaftigkeit und sensorische Details",
          choices: {
            enhance_imagery: "Bildhaftigkeit verstärken",
            simplify_imagery: "Bildhafte Beschreibungen vereinfachen",
            vivid_descriptions: "Lebendigere Beschreibungen verwenden",
            straightforward_descriptions:
              "Klare und direkte Beschreibungen verwenden",
            more_visual: "Mehr visuelle Details einbauen",
            less_visual: "Weniger visuelle Details verwenden",
          },
        },
        paragraph: {
          label: "Absatz- und Textstruktur",
          choices: {
            shorter_paragraphs: "Absätze verkürzen",
            longer_paragraphs: "Absätze verlängern",
            break_sections: "Text in kleinere Abschnitte aufteilen",
            combine_sections: "Abschnitte zusammenfassen",
            more_lists: "Mehr Aufzählungen verwenden",
            more_continuous: "Fließenderen Text verwenden",
            vary_paragraphs: "Absatzlängen variieren",
            consistent_length: "Konstante Absatzlänge beibehalten",
          },
        },
        content_length_legal_memo: {
          label: "Inhalt und Länge, rechtliches Memo",
          choices: {
            extend_memo: "Memo umfassender gestalten",
            summarize_memo: "Memo prägnanter gestalten",
            expand_analysis: "Rechtliche Analyse erweitern",
            deepen_case_law: "Mehr Rechtsprechungsreferenzen hinzufügen",
            add_statutory_references: "Mehr Gesetzesreferenzen hinzufügen",
            add_conclusion: "Schlussfolgerung stärken",
            add_recommendations: "Praktische Empfehlungen hinzufügen",
            add_risk_assessment: "Risikobewertung einschließen",
            add_executive_summary: "Executive Summary hinzufügen",
          },
        },
        content_length_legal_document: {
          label: "Inhalt und Länge, rechtliches Dokument",
          choices: {
            extend_document: "Dokument umfassender gestalten",
            shorten_document: "Dokument prägnanter gestalten",
            add_clauses: "Mehr Schutzklauseln hinzufügen",
            simplify_clauses: "Komplexe Klauseln vereinfachen",
            add_definitions: "Mehr definierte Begriffe hinzufügen",
            expand_scope: "Geltungsbereich erweitern",
            strengthen_warranties: "Garantien und Zusicherungen stärken",
            enhance_remedies: "Rechtsmittelabschnitt verbessern",
            add_boilerplate: "Standardklauseln hinzufügen",
            add_schedules: "Anhänge/Anlagen hinzufügen oder erweitern",
          },
        },
        other: {
          label: "Weitere Aspekte",
          choices: {
            replace_context:
              "Ersetze 'CONTEXT' durch den tatsächlichen Namen der Quelle",
            add_numbering: "Absätze nummerieren",
            remove_numbering: "Nummerierung entfernen",
            extend_statutories: "Text zu gesetzlichen Bestimmungen erweitern",
            reduce_statutories: "Text zu gesetzlichen Bestimmungen kürzen",
            extend_jurisprudence: "Text zur Rechtsprechung erweitern",
            reduce_jurisprudence: "Text zur Rechtsprechung kürzen",
          },
        },
      },
    },
    "prompt-step": {
      title: "Prompt auswählen",
      description: "Wähle, wie du die Antwort verbessern möchtest",
    },
    actions: {
      next: "Weiter",
      back: "Zurück",
      upgrade: "Antwort aktualisieren",
      cancel: "Abbrechen",
    },
    "text-upgrade-prompt":
      "{{prompt}} Der zu verbessernde Text lautet: {{selectedText}}",
  },
};
