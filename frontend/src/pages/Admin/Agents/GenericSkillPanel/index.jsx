import React from "react";
import Toggle from "@/components/ui/Toggle";

export default function GenericSkillPanel({
  title,
  description,
  skill,
  toggleSkill,
  enabled = false,
  disabled = false,
  image,
  icon,
}) {
  return (
    <div className="p-2">
      <div className="flex flex-col gap-y-[18px] max-w-[500px]">
        <div className="flex items-center gap-x-2">
          {icon &&
            React.createElement(icon, {
              size: 24,
              className: "text-foreground",
              weight: "bold",
            })}
          <label htmlFor="name" className="text-foreground text-md font-bold">
            {title}
          </label>
          <label
            className={`border-none relative inline-flex items-center ml-auto ${
              disabled ? "cursor-not-allowed" : "cursor-pointer"
            }`}
          >
            <Toggle
              disabled={disabled}
              checked={enabled}
              onChange={() => toggleSkill(skill)}
            />
            <span className="ml-3 text-sm font-medium text-foreground"></span>
          </label>
        </div>
        <img src={image} alt={title} className="w-full rounded-md" />
        <p className="text-foreground text-opacity-60 text-xs font-medium py-1.5">
          {description}
        </p>
      </div>
    </div>
  );
}
