import { describe, test, expect } from "@jest/globals";

// Import all translation files
import enNewsSystemItems from "@/locales/en/newsSystemItems";
import svNewsSystemItems from "@/locales/sv/newsSystemItems";
import frNewsSystemItems from "@/locales/fr/newsSystemItems";
import deNewsSystemItems from "@/locales/de/newsSystemItems";
import noNewsSystemItems from "@/locales/no/newsSystemItems";
import plNewsSystemItems from "@/locales/pl/newsSystemItems";
import rwNewsSystemItems from "@/locales/rw/newsSystemItems";

// Import system news items
import {
  systemNewsItems,
  systemNewsInitialized,
  getSystemNewsItems,
  clearSystemNewsCache,
  reinitializeSystemNews,
} from "@/data/news";

describe("News System Translation Tests", () => {
  // Mock fetch for the server-based news system
  beforeEach(() => {
    clearSystemNewsCache(); // Clear cache before each test
    global.fetch = jest.fn((url) => {
      if (url.includes("/api/news/system")) {
        return Promise.resolve({
          ok: true,
          json: () =>
            Promise.resolve({
              success: true,
              systemNews: [
                {
                  id: "system-welcome-2024",
                  title: "Welcome to IST Legal Platform",
                  content: "Welcome to our comprehensive legal platform...",
                  titleKey: "news-system-items.system-welcome-2024.title",
                  contentKey: "news-system-items.system-welcome-2024.content",
                  priority: "high",
                  isActive: true,
                  isSystemNews: true,
                  targetRoles: null,
                  createdAt: "2024-01-01T00:00:00Z",
                  expiresAt: null,
                },
                {
                  id: "system-new-features-2025",
                  title: "New System Features Available",
                  content:
                    "We've updated the platform with several new features...",
                  titleKey: "news-system-items.system-new-features-2025.title",
                  contentKey:
                    "news-system-items.system-new-features-2025.content",
                  priority: "medium",
                  isActive: true,
                  isSystemNews: true,
                  targetRoles: null,
                  createdAt: "2025-01-01T00:00:00Z",
                  expiresAt: null,
                },
              ],
            }),
        });
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ success: true }),
      });
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  const supportedLanguages = {
    en: enNewsSystemItems,
    sv: svNewsSystemItems,
    fr: frNewsSystemItems,
    de: deNewsSystemItems,
    no: noNewsSystemItems,
    pl: plNewsSystemItems,
    rw: rwNewsSystemItems,
  };

  const languageNames = {
    en: "English",
    sv: "Swedish",
    fr: "French",
    de: "German",
    no: "Norwegian",
    pl: "Polish",
    rw: "Kinyarwanda",
  };

  describe("Translation File Structure", () => {
    test("all language files should have the same structure", () => {
      const baseStructure = enNewsSystemItems;

      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        expect(translations).toHaveProperty("news-system-items");
        expect(typeof translations["news-system-items"]).toBe("object");

        // Check that all languages have the same top-level keys
        const baseKeys = Object.keys(baseStructure["news-system-items"]);
        const langKeys = Object.keys(translations["news-system-items"]);

        expect(langKeys.sort()).toEqual(baseKeys.sort());
      });
    });

    test("all translation files should export valid objects", () => {
      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        expect(translations).toBeDefined();
        expect(typeof translations).toBe("object");
        expect(translations).not.toBeNull();
      });
    });
  });

  describe("System News Translation Coverage", () => {
    test("all system news items with translation keys should have translations in all languages", async () => {
      // Get fresh system news items from the server
      const allSystemNewsItems = await getSystemNewsItems();

      // Find all system news items that use translation keys
      const newsWithTranslationKeys = allSystemNewsItems.filter(
        (item) => item.titleKey && item.contentKey
      );

      expect(newsWithTranslationKeys.length).toBeGreaterThan(0);

      newsWithTranslationKeys.forEach((newsItem) => {
        const newsId = newsItem.id;

        Object.entries(supportedLanguages).forEach(([lang, translations]) => {
          const newsTranslations = translations["news-system-items"][newsId];

          expect(newsTranslations).toBeDefined();
          expect(newsTranslations).toHaveProperty("title");
          expect(newsTranslations).toHaveProperty("content");
          expect(typeof newsTranslations.title).toBe("string");
          expect(typeof newsTranslations.content).toBe("string");
          expect(newsTranslations.title.length).toBeGreaterThan(0);
          expect(newsTranslations.content.length).toBeGreaterThan(0);
        });
      });
    });

    test("welcome news should be translated in all languages", () => {
      const welcomeNewsId = "system-welcome-2024";

      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        const welcomeTranslations =
          translations["news-system-items"][welcomeNewsId];

        expect(welcomeTranslations).toBeDefined();
        expect(welcomeTranslations.title).toBeDefined();
        expect(welcomeTranslations.content).toBeDefined();

        // Check that translations are not just the English version
        if (lang !== "en") {
          const englishTranslations =
            enNewsSystemItems["news-system-items"][welcomeNewsId];
          expect(welcomeTranslations.title).not.toBe(englishTranslations.title);
          expect(welcomeTranslations.content).not.toBe(
            englishTranslations.content
          );
        }
      });
    });

    test("template example should be translated in all languages", () => {
      const templateNewsId = "system-template-example";

      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        const templateTranslations =
          translations["news-system-items"][templateNewsId];

        expect(templateTranslations).toBeDefined();
        expect(templateTranslations.title).toBeDefined();
        expect(templateTranslations.content).toBeDefined();

        // Check that translations are not just the English version
        if (lang !== "en") {
          const englishTranslations =
            enNewsSystemItems["news-system-items"][templateNewsId];
          expect(templateTranslations.title).not.toBe(
            englishTranslations.title
          );
          expect(templateTranslations.content).not.toBe(
            englishTranslations.content
          );
        }
      });
    });

    test("new features news should be translated in all languages", () => {
      const newFeaturesNewsId = "system-new-features-2025";

      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        const newFeaturesTranslations =
          translations["news-system-items"][newFeaturesNewsId];

        expect(newFeaturesTranslations).toBeDefined();
        expect(newFeaturesTranslations.title).toBeDefined();
        expect(newFeaturesTranslations.content).toBeDefined();

        // Check that translations are not just the English version
        if (lang !== "en") {
          const englishTranslations =
            enNewsSystemItems["news-system-items"][newFeaturesNewsId];
          expect(newFeaturesTranslations.title).not.toBe(
            englishTranslations.title
          );
          expect(newFeaturesTranslations.content).not.toBe(
            englishTranslations.content
          );
        }
      });
    });
  });

  describe("Translation Quality", () => {
    test("all translations should contain meaningful content", () => {
      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        const newsItems = translations["news-system-items"];

        Object.entries(newsItems).forEach(([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          // Title should be meaningful (not just placeholder text)
          expect(newsTranslations.title).not.toMatch(/^(title|Title|TITLE)$/);
          expect(newsTranslations.title).not.toMatch(/^(test|Test|TEST)/);
          expect(newsTranslations.title.length).toBeGreaterThan(5);

          // Content should be meaningful
          expect(newsTranslations.content).not.toMatch(
            /^(content|Content|CONTENT)$/
          );
          expect(newsTranslations.content).not.toMatch(/^(test|Test|TEST)/);
          expect(newsTranslations.content.length).toBeGreaterThan(20);
        });
      });
    });

    test("translations should not contain obvious placeholder text", () => {
      const placeholderPatterns = [
        /lorem ipsum/i,
        /placeholder/i,
        /TODO/i,
        /FIXME/i,
        /\[(?!.*\]\(.*\)).*\]/, // Text in brackets like [TRANSLATE] but not markdown links [text](url)
        /{{.*}}/, // Template variables like {{title}}
      ];

      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        const newsItems = translations["news-system-items"];

        Object.entries(newsItems).forEach(([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          placeholderPatterns.forEach((pattern) => {
            expect(newsTranslations.title).not.toMatch(pattern);
            expect(newsTranslations.content).not.toMatch(pattern);
          });
        });
      });
    });

    test("translations should preserve markdown formatting", () => {
      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        const newsItems = translations["news-system-items"];

        Object.entries(newsItems).forEach(([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          const content = newsTranslations.content;

          // If English version has markdown, translated versions should too
          const englishContent =
            enNewsSystemItems["news-system-items"][newsId]?.content || "";

          if (englishContent.includes("**")) {
            expect(content).toMatch(/\*\*.*\*\*/); // Bold text
          }

          if (/^- /m.test(englishContent)) {
            expect(content).toMatch(/^- /m); // List items
          }

          if (englishContent.includes("[") && englishContent.includes("](")) {
            expect(content).toMatch(/\[.*\]\(.*\)/); // Links
          }
        });
      });
    });
  });

  describe("Language-Specific Validation", () => {
    test("Swedish translations should use appropriate Swedish characters", () => {
      const swedishTranslations = svNewsSystemItems["news-system-items"];

      Object.entries(swedishTranslations).forEach(
        ([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          const text = newsTranslations.title + " " + newsTranslations.content;

          // Swedish should contain some Swedish-specific words or characters
          const hasSwedishCharacters = /[åäöÅÄÖ]/.test(text);
          const hasSwedishWords =
            /\b(och|eller|för|till|från|med|på|av|är|har|kan|ska|kommer|använd|hjälp)\b/i.test(
              text
            );

          expect(hasSwedishCharacters || hasSwedishWords).toBe(true);
        }
      );
    });

    test("French translations should use appropriate French characters", () => {
      const frenchTranslations = frNewsSystemItems["news-system-items"];

      Object.entries(frenchTranslations).forEach(
        ([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          const text = newsTranslations.title + " " + newsTranslations.content;

          // French should contain some French-specific words or characters
          const hasFrenchCharacters = /[àâäéèêëïîôöùûüÿçÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ]/.test(
            text
          );
          const hasFrenchWords =
            /\b(et|ou|pour|avec|sur|dans|de|du|des|le|la|les|un|une|est|sont|avoir|être)\b/i.test(
              text
            );

          expect(hasFrenchCharacters || hasFrenchWords).toBe(true);
        }
      );
    });

    test("German translations should use appropriate German characteristics", () => {
      const germanTranslations = deNewsSystemItems["news-system-items"];

      Object.entries(germanTranslations).forEach(
        ([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          const text = newsTranslations.title + " " + newsTranslations.content;

          // German should contain some German-specific words or characters
          const hasGermanCharacters = /[äöüßÄÖÜ]/.test(text);
          const hasGermanWords =
            /\b(und|oder|für|mit|auf|von|zu|der|die|das|ein|eine|ist|sind|haben|können)\b/i.test(
              text
            );

          expect(hasGermanCharacters || hasGermanWords).toBe(true);
        }
      );
    });

    test("Polish translations should use appropriate Polish characters", () => {
      const polishTranslations = plNewsSystemItems["news-system-items"];

      Object.entries(polishTranslations).forEach(
        ([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          const text = newsTranslations.title + " " + newsTranslations.content;

          // Polish should contain some Polish-specific characters
          const hasPolishCharacters = /[ąćęłńóśźżĄĆĘŁŃÓŚŹŻ]/.test(text);
          const hasPolishWords =
            /\b(i|lub|dla|z|na|w|do|od|jest|są|może|można|będzie)\b/i.test(
              text
            );

          expect(hasPolishCharacters || hasPolishWords).toBe(true);
        }
      );
    });
  });

  describe("Translation Consistency", () => {
    test("all languages should have consistent key structure for each news item", () => {
      const baseKeys = Object.keys(enNewsSystemItems["news-system-items"]);

      baseKeys.forEach((newsId) => {
        // Skip metadata, authors, and categories sections as they have different structure
        if (
          newsId === "metadata" ||
          newsId === "authors" ||
          newsId === "categories"
        )
          return;

        const englishItem = enNewsSystemItems["news-system-items"][newsId];
        const englishSubKeys = Object.keys(englishItem).sort();

        Object.entries(supportedLanguages).forEach(([lang, translations]) => {
          if (lang === "en") return; // Skip English as it's the base

          const translatedItem = translations["news-system-items"][newsId];
          expect(translatedItem).toBeDefined();

          const translatedSubKeys = Object.keys(translatedItem).sort();
          expect(translatedSubKeys).toEqual(englishSubKeys);
        });
      });
    });

    test("translation lengths should be reasonable compared to English", () => {
      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        if (lang === "en") return; // Skip English as it's the base

        const newsItems = translations["news-system-items"];

        Object.entries(newsItems).forEach(([newsId, newsTranslations]) => {
          // Skip metadata, authors, and categories sections as they contain labels, not news items
          if (
            newsId === "metadata" ||
            newsId === "authors" ||
            newsId === "categories"
          )
            return;

          const englishTranslations =
            enNewsSystemItems["news-system-items"][newsId];

          // Translated title should not be more than 3x or less than 0.3x the English length
          const titleRatio =
            newsTranslations.title.length / englishTranslations.title.length;
          expect(titleRatio).toBeGreaterThan(0.3);
          expect(titleRatio).toBeLessThan(3.0);

          // Translated content should not be more than 2x or less than 0.5x the English length
          const contentRatio =
            newsTranslations.content.length /
            englishTranslations.content.length;
          expect(contentRatio).toBeGreaterThan(0.5);
          expect(contentRatio).toBeLessThan(2.0);
        });
      });
    });
  });

  describe("Integration with System News", () => {
    test("all system news translation keys should match actual news items", async () => {
      const allSystemNewsItems = await getSystemNewsItems();
      const systemNewsIds = allSystemNewsItems
        .filter((item) => item.titleKey && item.contentKey)
        .map((item) => item.id);

      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        const translationKeys = Object.keys(translations["news-system-items"]);

        systemNewsIds.forEach((newsId) => {
          expect(translationKeys).toContain(newsId);
        });
      });
    });

    test("no orphaned translations should exist", async () => {
      const allSystemNewsItems = await getSystemNewsItems();
      const systemNewsIds = allSystemNewsItems.map((item) => item.id);

      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        const translationKeys = Object.keys(translations["news-system-items"]);

        translationKeys.forEach((translationKey) => {
          // Allow template example as it's for documentation
          if (translationKey === "system-template-example") return;
          // Allow metadata section as it contains translation labels
          if (translationKey === "metadata") return;
          // Allow authors section as it contains author translation labels
          if (translationKey === "authors") return;
          // Allow categories section as it contains category translation labels
          if (translationKey === "categories") return;

          expect(systemNewsIds).toContain(translationKey);
        });
      });
    });
  });

  describe("Performance and Loading", () => {
    test("all translation files should load without errors", () => {
      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        expect(() => {
          JSON.stringify(translations);
        }).not.toThrow();
      });
    });

    test("translation files should not be excessively large", () => {
      Object.entries(supportedLanguages).forEach(([lang, translations]) => {
        const serialized = JSON.stringify(translations);

        // Each translation file should be less than 50KB
        expect(serialized.length).toBeLessThan(50000);
      });
    });
  });
});
