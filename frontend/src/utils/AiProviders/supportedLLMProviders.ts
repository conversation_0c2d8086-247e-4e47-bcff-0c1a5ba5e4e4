export const SUPPORTED_UPGRADE_PROVIDERS = [
  "openai",
  "anthropic",
  "gemini",
  "lmstudio",
  "ollama",
  "localai",
  "groq",
  "azure",
  "koboldcpp",
  "togetherai",
  "openrouter",
  "mistral",
  "perplexity",
  "textgenwebui",
  "generic-openai",
  "bedrock",
  "fireworksai",
  "deepseek",
  "xai",
  "system-standard",
];

export const ENABLED_WORKSPACE_PROVIDERS = [
  "openai",
  "anthropic",
  "gemini",
  "lmstudio",
  "ollama",
  "localai",
  "groq",
  "azure",
  "koboldcpp",
  "togetherai",
  "openrouter",
  "mistral",
  "perplexity",
  "textgenwebui",
  "generic-openai",
  "bedrock",
  "fireworksai",
  "deepseek",
  "xai",
  // TODO: More agent support.
  // "cohere",         // Has tool calling and will need to build explicit support
  // "huggingface"     // Can be done but already has issues with no-chat templated. Needs to be tested.
];

export const WARN_PERFORMANCE = [
  "lmstudio",
  "groq",
  "azure",
  "koboldcpp",
  "ollama",
  "localai",
  "openrouter",
  "generic-openai",
  "textgenwebui",
];

export const SUPPORTED_CUSTOM_USER_AI_PROVIDERS = [
  "openai",
  "anthropic",
  "gemini",
  "groq",
  "deepseek",
  "generic-openai",
  "native",
];
