export default {
  // =========================
  // DATA CONNECTORS
  // =========================
  dataConnectors: {
    github: {
      name: "GitHub Repo",
      description:
        "Injiza repository ya GitHub (public cyangwa private) numa guhamagara kumwe.",
      url: "GitHub Repo URL",
      "collect-url": "Url ya GitHub repo ushaka gukusanya.",
      "access-token": "Github Access Token",
      optional: "si ngombwa",
      "rate-limiting": "Access Token yo kwirinda rate limiting.",
      "desc-picker":
        "<PERSON><PERSON><PERSON> yo kurang<PERSON>, dosiye zose zizaboneka mu guhuza na workspace.",
      branch: "Ishami",
      "branch-desc": "Ishami ushaka gukusanyaho dosiye.",
      "branch-loading": "-- kubyaza amashami aboneka --",
      "desc-start": "Utahaye",
      "desc-token": "Github Access Token",
      "desc-connector": "iyi data connector izabasha guku<PERSON>",
      "desc-level": "inzego z'imbere (top-level)",
      "desc-end": "gusa bitewe na rate-limits za GitHub public API.",
      "personal-token":
        "Fata Personal Access Token kuri GitHub account yawe hano.",
      without: "Nta",
      "personal-token-access": "Personal Access Token",
      "desc-api": ", GitHub API ishobora gukumira dosiye nyinshi.",
      "temp-token": "kora temporarily Access Token",
      "avoid-issue": "kugira ngo wirinde iki kibazo.",
      submit: "Ohereza",
      "collecting-files": "Birimo gukusanya dosiye...",
    },
    "youtube-transcript": {
      name: "YouTube Transcript",
      description: "Injiza inyandiko ya videwo ya YouTube aho iri link.",
      url: "YouTube Video URL",
      "url-video": "URL ya YouTube videwo ushaka gutohozaho.",
      collect: "Kusanya transcript",
      collecting: "Birimo gutohoza transcript...",
      "desc-end":
        "nyuma yo kurangiza, transcript izaboneka mu guhuza na workspace.",
    },
    "website-depth": {
      name: "Bulk Link Scraper",
      description:
        "Sora urubuga n'inkuru zako z'imbere kugeza kuri depth runaka.",
      url: "Website URL",
      "url-scrape": "URL y'urubuga ushaka gusora.",
      depth: "Depth",
      "child-links":
        "Umubare w'inkuru zako agent izakurikira kuva kuri URL y'inkomoko.",
      "max-links": "Max Links",
      "links-scrape": "Umubare ntarengwa w'inkuru zo gusora.",
      scraping: "Gusora urubuga...",
      submit: "Ohereza",
      "desc-scrap":
        "Nyuma yo kurangiza, zose zizaboneka mu guhuza na workspace.",
    },
    confluence: {
      name: "Confluence",
      description: "Injiza page ya Confluence numa guhamagara kumwe.",
      url: "Confluence Page URL",
      "url-page": "URL ya page iri mu Confluence space.",
      username: "Confluence Username",
      "own-username": "Izina ryawe kuri Confluence.",
      token: "Confluence Access Token",
      "desc-start":
        "Ukeneye access token kuri authentication. Shobora kugikora",
      here: "hano",
      access: "Access token y'ubwirinzi.",
      collecting: "Birimo gukusanya pages...",
      submit: "Ohereza",
      "desc-end": "Nyuma yo kurangiza, zose zizaboneka mu guhuza na workspace.",
    },
  },
  // =========================
  // CONFLUENCE CONNECTOR COMPONENT
  // =========================
  confluence: {
    "space-key": "Urufunguzo rw'umwanya wa Confluence",
    "space-key-desc":
      "Uru ni urufunguzo rw'umwanya wa Confluence uzakoresha. Akenshi utangira na ~",
    "space-key-placeholder": "urugero: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "urugero: https://example.atlassian.net, http://localhost:8211, n'ibindi...",
    "token-tooltip": "Urashobora gukora urufunguzo rwa API",
    "token-tooltip-here": "hano",
  },
};
