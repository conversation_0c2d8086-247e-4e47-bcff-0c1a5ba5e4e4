import React, { useEffect, useRef, useState, useCallback } from "react";
import HistoricalMessage from "./HistoricalMessage";
import PromptReply from "./PromptReply";
import { useManageWorkspaceModal } from "../../../Modals/ManageWorkspace";
import ManageWorkspace from "../../../Modals/ManageWorkspace";
import { ArrowDown } from "@phosphor-icons/react";
import Chartable from "./Chartable";
import Workspace from "@/models/workspace";
import { useParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import paths from "@/utils/paths";
import Appearance from "@/models/appearance";
import System from "@/models/system";
import showToast from "@/utils/toast";
import { useTextSize } from "@/stores/userStore";
import ChatProgress from "@/components/ChatProgress";
import useThreadProgress, {
  useIsProcessActive,
} from "@/hooks/useThreadProgress";
import useProgressStore from "@/stores/progressStore";

// Define consistent scroll thresholds as default fallbacks.
const BOTTOM_THRESHOLD = 10;
const STREAMING_DISABLE_THRESHOLD = 50;
const AUTO_SCROLL_THRESHOLD = 30;

export default function ChatHistory({
  history = [],
  workspace,
  sendCommand,
  updateHistory,
  regenerateAssistantMessage,
}) {
  const lastScrollTopRef = useRef(0);
  const { t } = useTranslation();
  const { threadSlug = null } = useParams();
  const { showing, hideModal, refreshTrigger } = useManageWorkspaceModal();
  const [isAtBottom, setIsAtBottom] = useState(true);
  const chatHistoryRef = useRef(null);
  const textSize = useTextSize();
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const showScrollbar = Appearance.getSettings()?.showScrollbar || false;
  const isStreaming = history[history.length - 1]?.animate;
  const [isCitationEnabled, setIsCitationEnabled] = useState(false);
  const [isLegalTaskEnabled, setIsLegalTaskEnabled] = useState(false);
  const isNewPromptPending = history[history.length - 1]?.pending === true;
  const navigate = useNavigate();
  const [scrollThresholds, setScrollThresholds] = useState({
    bottom: BOTTOM_THRESHOLD,
    streaming: STREAMING_DISABLE_THRESHOLD,
    autoScroll: AUTO_SCROLL_THRESHOLD,
  });

  useEffect(() => {
    async function fetchScrollSettings() {
      const settings = await System.keys();
      setScrollThresholds({
        bottom: settings?.scroll_bottom_threshold || BOTTOM_THRESHOLD,
        streaming:
          settings?.scroll_streaming_disable_threshold ||
          STREAMING_DISABLE_THRESHOLD,
        autoScroll:
          settings?.scroll_auto_scroll_threshold || AUTO_SCROLL_THRESHOLD,
      });
    }
    fetchScrollSettings();
  }, []);

  const isProcessActive = useIsProcessActive(threadSlug);
  const { forceCleanup } = useThreadProgress(threadSlug);

  // Create reactive hook to check for thread error
  const hasThreadError = useProgressStore((state) => {
    if (!threadSlug) return false;
    const threadState = state.threads.get(threadSlug);
    return !!threadState?.error;
  });

  // Effect to clean up progress when streaming completes
  useEffect(() => {
    if (!threadSlug) return;

    // Check if we have any completed messages (not streaming, not pending)
    const hasCompletedMessages = history.some(
      (msg) =>
        msg.role === "assistant" && msg.closed && !msg.pending && !msg.animate
    );

    // If we have completed messages but progress is still active, clean it up
    if (
      hasCompletedMessages &&
      isProcessActive &&
      !isStreaming &&
      !isNewPromptPending
    ) {
      console.log(
        "[ChatHistory] Detected completed streaming, cleaning up progress"
      );
      setTimeout(() => forceCleanup(), 500);
    }
  }, [
    history,
    isProcessActive,
    isStreaming,
    isNewPromptPending,
    threadSlug,
    forceCleanup,
  ]);

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const performLegalTaskResponse = await System.getPerformLegalTask();
        setIsLegalTaskEnabled(performLegalTaskResponse?.enabled || false);
      } catch (error) {
        console.error("Error fetching legal task status:", error);
      }
    };
    fetchInitialData();
  }, []);

  useEffect(() => {
    const fetchCitation = async () => {
      try {
        const isCitationEnabled = await System.isCitationButton();
        setIsCitationEnabled(isCitationEnabled);
      } catch (error) {
        console.error("Error fetching Citation status:", error);
      } finally {
        //
      }
    };

    fetchCitation();
  }, []);

  const handleScroll = useCallback(
    (e) => {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      const distanceFromBottom = Math.abs(
        scrollHeight - scrollTop - clientHeight
      );
      const isBottom = distanceFromBottom < scrollThresholds.bottom;
      const scrollDistance = scrollTop - lastScrollTopRef.current;
      const isScrollingUp = scrollDistance < 0;

      // More sensitive detection during streaming - any upward scroll or movement away from bottom
      // should immediately disable auto-scroll
      if (isStreaming) {
        if (isScrollingUp || distanceFromBottom > scrollThresholds.streaming) {
          setIsUserScrolling(true);
        }
        // Also detect any manual scroll movement during rapid streaming
        if (Math.abs(scrollDistance) > 2) {
          setIsUserScrolling(true);
        }
      }

      // Update bottom state and handle auto-scroll re-enabling
      if (isBottom) {
        setIsAtBottom(true);
        // Only re-enable auto-scroll if user is scrolling down or not scrolling up
        // This ensures that if they reach bottom by scrolling down, auto-scroll resumes
        if (!isScrollingUp) {
          setIsUserScrolling(false);
        }
      } else {
        setIsAtBottom(false);
      }

      lastScrollTopRef.current = scrollTop;
    },
    [isStreaming]
  );

  useEffect(() => {
    if (isNewPromptPending && (isAtBottom || !isUserScrolling)) {
      scrollToBottom(true);
      setIsAtBottom(true);
      setIsUserScrolling(false);
      return;
    }

    // Auto-scroll during streaming only if user hasn't manually scrolled away
    if (isStreaming && !isUserScrolling && isAtBottom) {
      // Add a small delay to allow user scroll detection to take effect
      const streamingScrollTimeout = setTimeout(() => {
        if (!isUserScrolling && isAtBottom) {
          scrollToBottom(false); // Use instant scroll for auto-scrolling during streaming
        }
      }, 50); // Small delay to allow scroll detection
      return () => clearTimeout(streamingScrollTimeout);
    }
    // Auto-scroll when not streaming and user hasn't manually scrolled away
    else if (!isStreaming && !isUserScrolling && isAtBottom) {
      scrollToBottom(false); // Use instant scroll for auto-scrolling
    }
    // Fallback auto-scroll check for edge cases during streaming.
    // This handles cases where the isAtBottom state may not update quickly enough.
    else if (isStreaming && !isUserScrolling) {
      const fallbackTimeout = setTimeout(() => {
        if (chatHistoryRef.current && !isUserScrolling) {
          const { scrollTop, scrollHeight, clientHeight } =
            chatHistoryRef.current;
          const distanceFromBottom = Math.abs(
            scrollHeight - scrollTop - clientHeight
          );

          // Only auto-scroll if very close to bottom and user hasn't manually scrolled
          const shouldAutoScroll =
            isAtBottom && distanceFromBottom < scrollThresholds.autoScroll;

          if (shouldAutoScroll) {
            scrollToBottom(false);
          }
        }
      }, 100); // Delay to allow state updates to propagate.
      return () => clearTimeout(fallbackTimeout);
    }
  }, [history, isAtBottom, isStreaming, isUserScrolling, isNewPromptPending]);

  // Remove debounce, we want immediate response
  useEffect(() => {
    const chatHistoryElement = chatHistoryRef.current;
    if (chatHistoryElement) {
      chatHistoryElement.addEventListener("scroll", handleScroll);
      return () =>
        chatHistoryElement.removeEventListener("scroll", handleScroll);
    }
  }, [handleScroll]);

  const scrollToBottom = (smooth = false) => {
    if (chatHistoryRef.current) {
      const scrollOptions = {
        top: chatHistoryRef.current.scrollHeight,
        behavior: smooth ? "smooth" : "auto",
      };

      requestAnimationFrame(() => {
        chatHistoryRef.current.scrollTo(scrollOptions);

        setTimeout(() => {
          if (chatHistoryRef.current) {
            const { scrollTop, scrollHeight, clientHeight } =
              chatHistoryRef.current;
            if (
              Math.abs(scrollHeight - scrollTop - clientHeight) >
              scrollThresholds.bottom
            ) {
              chatHistoryRef.current.scrollTo(scrollOptions);
            }
          }
        }, 200);
      });
    }
  };

  const forkThread = async (chatId) => {
    const newThreadSlug = await Workspace.forkThread(
      workspace.slug,
      threadSlug,
      chatId
    );

    if (newThreadSlug) {
      navigate(paths.workspace.thread(workspace.slug, newThreadSlug));
    } else {
      showToast(t("toast.errors.failed-fork-thread"), "error");
    }
  };

  return (
    <div
      className={`bg-background text-foreground font-light ${textSize} h-full flex flex-col justify-start overflow-y-scroll ${
        showScrollbar ? "" : "no-scroll"
      }`}
      id="chat-history"
      ref={chatHistoryRef}
      onScroll={handleScroll}
    >
      <div className="w-full max-w-4xl mx-auto pb-56">
        {history.map((props, index) => {
          let question = "";
          let answer = "";
          if (index % 2 === 0) {
            question = props.content;
            answer = history[index + 1]?.content || "";
          } else {
            question = history[index - 1].content;
            answer = props.content;
          }

          const isLastBotReply =
            index === history.length - 1 && props.role === "assistant";

          if (props?.type === "statusResponse" && !!props.content) {
            return (
              <StatusResponse
                key={`status-${props.uuid}-${index}`}
                props={props}
              />
            );
          }

          if (props.type === "rechartVisualize" && !!props.content) {
            return (
              <Chartable
                key={`chart-${props.uuid}-${index}`}
                workspace={workspace}
                props={props}
              />
            );
          }

          if (
            isLastBotReply &&
            props.animate &&
            !props.content &&
            !props.error
          ) {
            if (props.pending && !isProcessActive) {
              return (
                <PromptReply
                  key={`reply-${props.uuid}-${index}`}
                  uuid={props.uuid}
                  reply=""
                  pending={true}
                  sources={[]}
                  error={null}
                  workspace={workspace}
                  closed={false}
                />
              );
            }
            return null;
          }

          if (isLastBotReply && props.animate && !props.closed) {
            return (
              <PromptReply
                key={`reply-${props.uuid}-${index}`}
                uuid={props.uuid}
                reply={props.content}
                pending={props.pending}
                sources={props.sources}
                error={props.error}
                workspace={workspace}
                closed={props.closed}
              />
            );
          }

          return (
            <HistoricalMessage
              key={`msg-${props.uuid}-${index}`}
              chat={props}
              role={props.role}
              error={props.error}
              chatId={props.chatId}
              workspace={workspace}
              message={props.content}
              sources={props.sources}
              feedbackScore={props.feedbackScore}
              attachments={props.attachments}
              regenerateMessage={regenerateAssistantMessage}
              isLastMessage={isLastBotReply}
              forkThread={forkThread}
              metrics={props.metrics}
              index={index}
              question={question}
              answer={answer}
              sendCommand={sendCommand}
              updateHistory={updateHistory}
              threadSlug={threadSlug}
              history={history}
              isLegalTaskEnabled={isLegalTaskEnabled}
            />
          );
        })}

        {(isProcessActive || hasThreadError) && (
          <ChatProgress threadSlug={threadSlug} updateHistory={updateHistory} />
        )}
      </div>

      {showing && (
        <ManageWorkspace
          hideModal={hideModal}
          providedSlug={workspace.slug}
          refreshTrigger={refreshTrigger}
        />
      )}

      {!isAtBottom && (
        <div className="fixed bottom-40 right-10 md:right-20 z-50 cursor-pointer animate-pulse">
          <div className="flex flex-col items-center">
            <div
              className="p-1 rounded-full border border-white/10 bg-white/10 hover:bg-white/20 hover:text-white"
              onClick={() => {
                scrollToBottom(true);
                setIsUserScrolling(false);
              }}
            >
              <ArrowDown weight="bold" className="text-white w-5 h-5" />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function StatusResponse({ props }) {
  return <p className="text-muted font-medium">{props.content}</p>;
}
