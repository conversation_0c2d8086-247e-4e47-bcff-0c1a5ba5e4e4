import {
  isValidUrl,
  isValidCssColor,
  sanitizeUrl,
  sanitizeCssColor,
  isColorInSafePalette,
  SAFE_TEXT_COLORS,
  SAFE_HIGHLIGHT_COLORS,
} from "../validation";

describe("URL Validation", () => {
  describe("isValidUrl", () => {
    test("should accept valid HTTP URLs", () => {
      expect(isValidUrl("http://example.com")).toBe(true);
      expect(isValidUrl("https://example.com")).toBe(true);
      expect(isValidUrl("https://subdomain.example.com/path")).toBe(true);
    });

    test("should accept valid email and tel URLs", () => {
      expect(isValidUrl("mailto:<EMAIL>")).toBe(true);
      expect(isValidUrl("tel:+1234567890")).toBe(true);
    });

    test("should reject dangerous protocols", () => {
      expect(isValidUrl('javascript:alert("xss")')).toBe(false);
      expect(isValidUrl('data:text/html,<script>alert("xss")</script>')).toBe(
        false
      );
      expect(isValidUrl('vbscript:msgbox("xss")')).toBe(false);
      expect(isValidUrl("file:///etc/passwd")).toBe(false);
    });

    test("should reject invalid URLs", () => {
      expect(isValidUrl("")).toBe(false);
      expect(isValidUrl("not-a-url")).toBe(false);
      expect(isValidUrl("http://")).toBe(false);
      expect(isValidUrl(null)).toBe(false);
      expect(isValidUrl(undefined)).toBe(false);
    });

    test("should reject URLs with suspicious patterns", () => {
      expect(isValidUrl("http://example..com")).toBe(false);
    });
  });

  describe("sanitizeUrl", () => {
    test("should sanitize valid URLs", () => {
      expect(sanitizeUrl("http://example.com")).toBe("http://example.com");
      expect(sanitizeUrl("  https://example.com  ")).toBe(
        "https://example.com"
      );
    });

    test("should add https to URLs without protocol", () => {
      expect(sanitizeUrl("example.com")).toBe("https://example.com");
    });

    test("should return null for invalid URLs", () => {
      expect(sanitizeUrl('javascript:alert("xss")')).toBe(null);
      expect(sanitizeUrl("")).toBe(null);
    });
  });
});

describe("CSS Color Validation", () => {
  describe("isValidCssColor", () => {
    test("should accept valid hex colors", () => {
      expect(isValidCssColor("#fff")).toBe(true);
      expect(isValidCssColor("#ffffff")).toBe(true);
      expect(isValidCssColor("#123ABC")).toBe(true);
    });

    test("should accept valid RGB colors", () => {
      expect(isValidCssColor("rgb(255, 0, 0)")).toBe(true);
      expect(isValidCssColor("rgba(255, 0, 0, 0.5)")).toBe(true);
    });

    test("should accept valid named colors", () => {
      expect(isValidCssColor("red")).toBe(true);
      expect(isValidCssColor("blue")).toBe(true);
      expect(isValidCssColor("transparent")).toBe(true);
    });

    test("should reject dangerous patterns", () => {
      expect(isValidCssColor('javascript:alert("xss")')).toBe(false);
      expect(isValidCssColor('expression(alert("xss"))')).toBe(false);
      expect(isValidCssColor('url(javascript:alert("xss"))')).toBe(false);
      expect(isValidCssColor('@import url("malicious.css")')).toBe(false);
      expect(isValidCssColor('<script>alert("xss")</script>')).toBe(false);
    });

    test("should reject invalid colors", () => {
      expect(isValidCssColor("")).toBe(false);
      expect(isValidCssColor("not-a-color")).toBe(false);
      expect(isValidCssColor("#gggggg")).toBe(false);
      expect(isValidCssColor(null)).toBe(false);
      expect(isValidCssColor(undefined)).toBe(false);
    });
  });

  describe("sanitizeCssColor", () => {
    test("should sanitize valid colors", () => {
      expect(sanitizeCssColor("#fff")).toBe("#fff");
      expect(sanitizeCssColor("  red  ")).toBe("red");
    });

    test("should return null for invalid colors", () => {
      expect(sanitizeCssColor('javascript:alert("xss")')).toBe(null);
      expect(sanitizeCssColor("")).toBe(null);
    });
  });

  describe("isColorInSafePalette", () => {
    test("should recognize colors in safe text palette", () => {
      expect(isColorInSafePalette("#000000", "text")).toBe(true);
      expect(isColorInSafePalette("#EF4444", "text")).toBe(true);
    });

    test("should recognize colors in safe highlight palette", () => {
      expect(isColorInSafePalette("#FFFF99", "highlight")).toBe(true);
      expect(isColorInSafePalette("#BBF7D0", "highlight")).toBe(true);
    });

    test("should reject colors not in palette", () => {
      expect(isColorInSafePalette("#999999", "text")).toBe(false);
      expect(isColorInSafePalette("#FFFF99", "text")).toBe(false); // highlight color in text palette
    });
  });
});
