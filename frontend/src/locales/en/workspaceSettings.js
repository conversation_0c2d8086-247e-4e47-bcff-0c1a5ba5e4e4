export default {
  // =========================
  // WORKSPACES SETTINGS MENU
  // =========================

  "workspaces-settings": {
    general: "General Settings",
    chat: "Chat Settings",
    vector: "Vector Database",
    members: "Members",
    agent: "Agent Configuration",
    "general-settings": {
      "workspace-name": "Workspace Name",
      "desc-name": "This will only change the display name of your workspace.",
      "assistant-profile": "Assistant Profile Image",
      "assistant-image":
        "Customize the profile image of the assistant for this workspace.",
      "workspace-image": "Workspace Image",
      "remove-image": "Remove Workspace Image",
      delete: "Delete Workspace",
      deleting: "Deleting Workspace...",
      update: "Update workspace",
      updating: "Updating workspace...",
    },
    "chat-settings": {
      type: "Chat type",
      private: "Private",
      standard: "Standard",
      "private-desc-start": "will manually grant access to",
      "private-desc-mid": "only",
      "private-desc-end": "specific users.",
      "standard-desc-start": "will automatically grant access to",
      "standard-desc-mid": "all",
      "standard-desc-end": "new users.",
    },
    users: {
      manage: "Manage Users",
      "workspace-member": "No workspace members",
      username: "Email Address",
      role: "Role",
      default: "Default",
      manager: "Manager",
      admin: "Admin",
      superuser: "Superuser",
      "date-added": "Date Added",
      users: "Users",
      search: "Search for a user",
      "no-user": "No users found",
      select: "Select All",
      unselect: "Unselect",
      save: "Save",
    },
    "linked-workspaces": {
      title: "Linked Workspaces",
      description:
        "If workspaces are linked, legal data relevant for the prompt will be automatically fetched from each linked legal area. Note that linked workspaces will increase processing time",
      "linked-workspace": "No linked workspaces",
      manage: "Manage Workspaces",
      name: "Name",
      slug: "Slug",
      date: "Date Added",
      workspaces: "Workspaces",
      search: "Search for a workspace",
      "no-workspace": "No workspaces found",
      select: "Select All",
      unselect: "Unselect",
      save: "Save",
    },
    "delete-workspace": "Delete Workspace",
    "delete-workspace-message":
      "You are about to delete your entire {{workspace}} workspace. This will remove all vector embeddings on your vector database.\n\nThe original source files will remain untouched. This action is irreversible.",
    "vector-database": {
      reset: {
        title: "Reset Vector Database",
        message:
          "You are about to reset this workspace's vector database. This will remove all vector embeddings currently embedded.\n\nThe original source files will remain untouched. This action is irreversible.",
      },
    },
  },

  // =========================
  // GENERAL APPEARANCE & CUSTOMIZATION
  // =========================
  general: {
    vector: {
      title: "Vector Count",
      description: "Total number of vectors in your vector database.",
      vectors: "Number of vectors",
    },
    names: {
      description: "This will only change the display name of your workspace.",
    },
    message: {
      title: "Suggested Chat Messages",
      description:
        "Customize the messages that will be suggested to your workspace users.",
      add: "Add new message",
      save: "Save Messages",
      heading: "Explain to me",
      body: "the benefits of platform",
      message: "Message",
      "new-heading": "Heading",
    },
    pfp: {
      title: "Assistant Profile Image",
      description:
        "Customize the profile image of the assistant for this workspace.",
      image: "Workspace Image",
      remove: "Remove Workspace Image",
    },
    delete: {
      delete: "Delete Workspace",
      deleting: "Deleting Workspace...",
      "confirm-start": "You are about to delete your entire",
      "confirm-end":
        "workspace. This will remove all vector embeddings in your vector database.\n\nThe original source files will remain untouched. This action is irreversible.",
    },
  },

  // =========================
  // CHAT SETTINGS
  // =========================
  chat: {
    llm: {
      title: "Workspace LLM Provider",
      description:
        "The specific LLM provider & model that will be used for this workspace. By default, it uses the system LLM provider and settings.",
      search: "Search all LLM providers",
      "save-error": "Failed to save {{provider}} settings: {{error}}",
      setup: "Setup",
      use: "To use",
      "need-setup": "you'll need to set up the following credentials.",
      cancel: "Cancel",
      save: "Save",
      settings: "settings",
      "multi-model": "This provider does not support multiple models.",
      "workspace-use": "Your workspace will use the model configured in",
      "model-set": "system settings",
      "system-default": "System default",
      "system-default-desc":
        "Use the system LLM preference for this workspace.",
      "no-selection": "No LLM selected",
      "select-provider": "Select an LLM provider",
      // Add keys for System Standard Option
      "system-standard-name": "System Standard",
      "system-standard-desc": "Use the primary LLM defined in system settings.",
    },
    "speak-prompt": "Speak your prompt",
    "view-agents": "View all available agents you can use for chatting",
    "ability-tag": "Ability",
    "change-text-size": "Change text size",
    "aria-text-size": "Change text size",

    model: {
      title: "Workspace Chat Model",
      description:
        "The specific chat model that will be used for this workspace. If empty, will use the system LLM preference.",
      wait: "-- waiting for models --",
      general: "General models",
      custom: "Custom models",
    },
    mode: {
      title: "Chat mode",
      chat: {
        title: "Chat",
        "desc-start": "will provide answers with the LLM's general knowledge",
        and: "and",
        "desc-end": "document context that is found.",
      },
      query: {
        title: "Query",
        "desc-start": "will provide answers",
        only: "only",
        "desc-end": "if document context is found.",
      },
    },
    history: {
      title: "Chat History",
      "desc-start":
        "The number of previous chats that will be included in the response's short-term memory.",
      recommend: "Recommend 20. ",
      "desc-end":
        "Anything more than 45 is likely to lead to continuous chat failures depending on message size.",
    },
    prompt: {
      title: "Prompt",
      description:
        "The prompt that will be used on this workspace. Define the context and instructions for the AI to generate a response. You should to provide a carefully crafted prompt so the AI can generate a relevant and accurate response.",
    },
    refusal: {
      title: "Query mode refusal response",
      "desc-start": "When in",
      query: "query",
      "desc-end":
        "mode, you may want to return a custom refusal response when no context is found.",
    },
    temperature: {
      title: "LLM Temperature",
      "desc-start":
        'This setting controls how "creative" your LLM responses will be.',
      "desc-end":
        "The higher the number the more creative. For some models this can lead to incoherent responses when set too high.",
      hint: "Most LLMs have various acceptable ranges of valid values. Consult your LLM provider for that information.",
    },
    "dynamic-pdr": {
      title: "Dynamic PDR for Workspace",
      description: "Enable or disable Dynamic PDR for this workspace.",
      "global-enabled":
        "Dynamic PDR is globally enabled and cannot be disabled for individual workspaces.",
    },
    // Chat log display section
    display_prompt_output_description:
      "Display the prompt output logging, Open & Download the file",
    display_prompt_output: "Display prompt output",
    loading_prompt_output: "Loading prompt output...",
    prompt_output_not_available:
      "*** Prompt output is not available for this chat.",
    open_in_new_tab: "Open in new tab",
  },

  // =========================
  // VECTOR DATABASE (WORKSPACE)
  // =========================
  "vector-workspace": {
    identifier: "Vector database identifier",
    snippets: {
      title: "Max Context Snippets",
      description:
        "This setting controls the maximum amount of context snippets the will be sent to the LLM for per chat or query.",
      recommend:
        "Recommended value is at least 30. Setting much higher numbers will increase processing time without necessarily improving precision depending on the capacity of the LLM used.",
    },
    doc: {
      title: "Document similarity threshold",
      description:
        "The minimum similarity score required for a source to be considered related to the chat. The higher the number, the more similar the source must be to the chat.",
      zero: "No restriction",
      low: "Low (similarity score ≥ .25)",
      medium: "Medium (similarity score ≥ .50)",
      high: "High (similarity score ≥ .75)",
    },
    reset: {
      reset: "Reset Vector Database",
      resetting: "Clearing vectors...",
      confirm:
        "You are about to reset this workspace's vector database. This will remove all vector embeddings currently embedded.\n\nThe original source files will remain untouched. This action is irreversible.",
      error: "Workspace vector database could not be reset!",
      success: "Workspace vector database was reset!",
    },
    prompt: { placeholder: "Ask your question here..." },
    refusal: { placeholder: "Sorry, I cannot answer that question" },
  },

  // =========================
  // AGENT CONFIGURATION
  // =========================
  agent: {
    "performance-warning":
      "Performance of LLMs that do not explicitly support tool-calling is highly dependent on the model's capabilities and accuracy. Some abilities may be limited or non-functional.",
    provider: {
      title: "Workspace Agent LLM Provider",
      description:
        "The specific LLM provider & model that will be used for this workspace's @agent agent.",
      "need-setup":
        "To use {{name}} as this workspace's agent LLM you need to set it up first.",
    },
    mode: {
      chat: {
        title: "Workspace Agent Chat model",
        description:
          "The specific chat model that will be used for this workspace's @agent agent.",
      },
      title: "Workspace Agent model",
      description:
        "The specific LLM model that will be used for this workspace's @agent agent.",
      wait: "-- waiting for models --",
    },
    skill: {
      title: "Default agent skills",
      description:
        "Improve the natural abilities of the default agent with these pre-built skills. This set up applies to all workspaces.",
      rag: {
        title: "RAG & long-term memory",
        description:
          'Allow the agent to leverage your local documents to answer a query or ask the agent to "remember" pieces of content for long-term memory retrieval.',
      },
      configure: {
        title: "Configure Agent Skills",
        description:
          "Customize and enhance the default agent's capabilities by enabling or disabling specific skills. These settings will be applied across all workspaces.",
      },
      view: {
        title: "View & summarize documents",
        description:
          "Allow the agent to list and summarize the content of workspace files currently embedded.",
      },
      scrape: {
        title: "Scrape websites",
        description:
          "Allow the agent to visit and scrape the content of websites.",
      },
      generate: {
        title: "Generate charts",
        description:
          "Enable the default agent to generate various types of charts from data provided or given in chat.",
      },
      save: {
        title: "Generate & save files to browser",
        description:
          "Enable the default agent to generate and write to files that save and can be downloaded in your browser.",
      },
      web: {
        title: "Live web search and browsing",
        "desc-start":
          "Enable your agent to search the web to answer your questions by connecting to a web-search (SERP) provider.",
        "desc-end":
          "Web search during agent sessions will not work until this is set up.",
      },
    },
  },
};
