import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, jest, beforeEach } from "@jest/globals";
import ChatProgress from "../index";
import useProgressStore from "@/stores/progressStore";

// Mock the useThreadProgress hook
jest.mock("@/hooks/useThreadProgress", () => {
  return jest.fn(() => ({
    isActive: false,
    currentStep: 1,
    totalSteps: 7,
    flowType: null,
    cancel: jest.fn(),
    error: null,
    clearError: jest.fn(),
  }));
});

// Mock the ProgressModal component
jest.mock("@/components/Modals/ProgressModal", () => {
  return function ProgressModal({ isOpen, onClose, threadSlug }) {
    if (!isOpen) return null;
    return (
      <div data-testid="progress-modal">
        Progress Modal for {threadSlug}
        <button onClick={onClose}>Close Modal</button>
      </div>
    );
  };
});

// Mock the AbortWarningModal component
jest.mock("@/components/Modals/AbortWarningModal", () => {
  return function AbortWarningModal({ isOpen, onCancel, onAbort }) {
    if (!isOpen) return null;
    return (
      <div data-testid="abort-warning-modal">
        Abort Warning Modal
        <button onClick={onCancel}>Keep Running</button>
        <button onClick={onAbort}>Abort Run</button>
      </div>
    );
  };
});

// Mock the ChatError component
jest.mock("../ChatError", () => {
  return function ChatError({ error, onDismiss }) {
    return (
      <div data-testid="chat-error">
        Error: {error}
        <button onClick={onDismiss}>Dismiss Error</button>
      </div>
    );
  };
});

// Mock the Progress component
jest.mock("@/components/ui/Progress", () => {
  return function Progress({ value }) {
    return <div data-testid="progress-bar">Progress: {value}%</div>;
  };
});

// Mock the Button component
jest.mock("@/components/Button", () => ({
  Button: function Button({ children, onClick, className, ...props }) {
    return (
      <button onClick={onClick} className={className} {...props}>
        {children}
      </button>
    );
  },
}));

// Mock translation
const mockT = jest.fn((key, options) => {
  const translations = {
    "chatProgress.step": "Step",
    "chatProgress.processing": "Processing",
    "chatProgress.details": "Details",
    "chatProgress.abort": "Abort",
    "chatProgress.cancelled": "Process was cancelled",
    "chatProgress.types.cdb.step1.label": "Step 1: Initialization",
    "chatProgress.types.cdb.step1.desc": "Starting the process",
    "chatProgress.types.cdb.step2.label": "Step 2: Processing",
    "chatProgress.types.cdb.step2.desc": "Processing your request",
  };

  if (options && translations[key]) {
    return translations[key];
  }

  return translations[key] || key;
});

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

const useThreadProgress = require("@/hooks/useThreadProgress");

describe("ChatProgress", () => {
  const MOCK_THREAD_ID = "thread-123";
  let mockUpdateHistory;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Reset the progress store
    useProgressStore.setState({ threads: new Map() });

    mockUpdateHistory = jest.fn();

    // Reset the mock implementation
    useThreadProgress.mockReturnValue({
      isActive: false,
      currentStep: 1,
      totalSteps: 7,
      flowType: null,
      cancel: jest.fn(),
      error: null,
      clearError: jest.fn(),
    });
  });

  describe("visibility conditions", () => {
    it("should not render when no threadSlug provided", () => {
      render(<ChatProgress />);
      expect(screen.queryByTestId("progress-bar")).not.toBeInTheDocument();
    });

    it("should not render when threadSlug is empty", () => {
      render(<ChatProgress threadSlug="" />);
      expect(screen.queryByTestId("progress-bar")).not.toBeInTheDocument();
    });

    it("should not render when process is not active", () => {
      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 1,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: null,
        clearError: jest.fn(),
      });

      render(<ChatProgress threadSlug={MOCK_THREAD_ID} />);
      expect(screen.queryByTestId("progress-bar")).not.toBeInTheDocument();
    });

    it("should render when process is active", async () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: null,
        clearError: jest.fn(),
      });

      render(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      // Component should appear after delay
      await waitFor(
        () => {
          expect(screen.getByTestId("progress-bar")).toBeInTheDocument();
        },
        { timeout: 1000 }
      );
    });
  });

  describe("error handling", () => {
    it("should show error component when error exists", () => {
      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 1,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: "Test error message",
        clearError: jest.fn(),
      });

      render(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      expect(screen.getByTestId("chat-error")).toBeInTheDocument();
      expect(screen.getByText("Error: Test error message")).toBeInTheDocument();
    });

    it("should dismiss error and call updateHistory when error is dismissed", () => {
      const mockClearError = jest.fn();

      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 1,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: "Test error message",
        clearError: mockClearError,
      });

      render(
        <ChatProgress
          threadSlug={MOCK_THREAD_ID}
          updateHistory={mockUpdateHistory}
        />
      );

      const dismissButton = screen.getByText("Dismiss Error");
      fireEvent.click(dismissButton);

      expect(mockClearError).toHaveBeenCalled();
      expect(mockUpdateHistory).toHaveBeenCalledWith(expect.any(Function));
    });

    it("should not show error when it has been dismissed", () => {
      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 1,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: "Test error message",
        clearError: jest.fn(),
      });

      const { rerender } = render(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      expect(screen.getByTestId("chat-error")).toBeInTheDocument();

      // Dismiss the error
      fireEvent.click(screen.getByText("Dismiss Error"));

      // Re-render with same error (simulating component update)
      rerender(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      expect(screen.queryByTestId("chat-error")).not.toBeInTheDocument();
    });
  });

  describe("progress display", () => {
    it("should display correct progress information", async () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 3,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: null,
        clearError: jest.fn(),
      });

      render(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      await waitFor(() => {
        expect(screen.getByText("Step 3")).toBeInTheDocument();
        expect(screen.getByText("Processing")).toBeInTheDocument();
        expect(screen.getByTestId("progress-bar")).toBeInTheDocument();
        expect(screen.getByText("Progress: 29%")).toBeInTheDocument(); // (3-1)/7 * 100 = 28.57% rounded to 29%
      });
    });

    it("should display flow-specific step labels when flowType is provided", async () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 7,
        flowType: "cdb",
        cancel: jest.fn(),
        error: null,
        clearError: jest.fn(),
      });

      render(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      await waitFor(() => {
        expect(screen.getByText("Step 1: Initialization")).toBeInTheDocument();
        expect(screen.getByText("Starting the process")).toBeInTheDocument();
      });
    });

    it("should calculate progress percentage correctly", async () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 4,
        flowType: null,
        cancel: jest.fn(),
        error: null,
        clearError: jest.fn(),
      });

      render(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      await waitFor(() => {
        // (1-1)/4 * 100 = 0%
        expect(screen.getByText("Progress: 0%")).toBeInTheDocument();
      });
    });

    it("should handle zero totalSteps gracefully", async () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 0,
        flowType: null,
        cancel: jest.fn(),
        error: null,
        clearError: jest.fn(),
      });

      render(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      await waitFor(() => {
        expect(screen.getByText("Progress: 0%")).toBeInTheDocument();
      });
    });
  });

  describe("user interactions", () => {
    it("should open progress modal when details button is clicked", async () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: null,
        clearError: jest.fn(),
      });

      render(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      await waitFor(() => {
        expect(screen.getByText("Details")).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText("Details"));

      expect(screen.getByTestId("progress-modal")).toBeInTheDocument();
      expect(
        screen.getByText(`Progress Modal for ${MOCK_THREAD_ID}`)
      ).toBeInTheDocument();
    });

    it("should close progress modal when close is called", async () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: null,
        clearError: jest.fn(),
      });

      render(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      await waitFor(() => {
        fireEvent.click(screen.getByText("Details"));
      });

      expect(screen.getByTestId("progress-modal")).toBeInTheDocument();

      fireEvent.click(screen.getByText("Close Modal"));

      expect(screen.queryByTestId("progress-modal")).not.toBeInTheDocument();
    });

    it("should open abort warning modal when abort button is clicked", async () => {
      const mockCancel = jest.fn();

      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 7,
        flowType: null,
        cancel: mockCancel,
        error: null,
        clearError: jest.fn(),
      });

      render(
        <ChatProgress
          threadSlug={MOCK_THREAD_ID}
          updateHistory={mockUpdateHistory}
        />
      );

      await waitFor(() => {
        expect(screen.getByText("Abort")).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText("Abort"));

      // Should open abort warning modal
      expect(screen.getByTestId("abort-warning-modal")).toBeInTheDocument();
      expect(screen.getByText("Keep Running")).toBeInTheDocument();
      expect(screen.getByText("Abort Run")).toBeInTheDocument();

      // Should not have called cancel yet
      expect(mockCancel).not.toHaveBeenCalled();
      expect(mockUpdateHistory).not.toHaveBeenCalled();
    });

    it("should close abort modal when 'Keep Running' is clicked", async () => {
      const mockCancel = jest.fn();

      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 7,
        flowType: null,
        cancel: mockCancel,
        error: null,
        clearError: jest.fn(),
      });

      render(
        <ChatProgress
          threadSlug={MOCK_THREAD_ID}
          updateHistory={mockUpdateHistory}
        />
      );

      await waitFor(() => {
        fireEvent.click(screen.getByText("Abort"));
      });

      expect(screen.getByTestId("abort-warning-modal")).toBeInTheDocument();

      fireEvent.click(screen.getByText("Keep Running"));

      expect(
        screen.queryByTestId("abort-warning-modal")
      ).not.toBeInTheDocument();
      expect(mockCancel).not.toHaveBeenCalled();
      expect(mockUpdateHistory).not.toHaveBeenCalled();
    });

    it("should call cancel and updateHistory when 'Abort Run' is clicked in modal", async () => {
      const mockCancel = jest.fn();

      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 7,
        flowType: null,
        cancel: mockCancel,
        error: null,
        clearError: jest.fn(),
      });

      render(
        <ChatProgress
          threadSlug={MOCK_THREAD_ID}
          updateHistory={mockUpdateHistory}
        />
      );

      await waitFor(() => {
        fireEvent.click(screen.getByText("Abort"));
      });

      expect(screen.getByTestId("abort-warning-modal")).toBeInTheDocument();

      fireEvent.click(screen.getByText("Abort Run"));

      // Modal should close
      expect(
        screen.queryByTestId("abort-warning-modal")
      ).not.toBeInTheDocument();

      // Should call cancel and updateHistory
      expect(mockCancel).toHaveBeenCalled();
      expect(mockUpdateHistory).toHaveBeenCalledWith(expect.any(Function));
    });
  });

  describe("visibility timing", () => {
    it("should show component after delay when process becomes active", async () => {
      const { rerender } = render(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      // Initially not visible
      expect(screen.queryByTestId("progress-bar")).not.toBeInTheDocument();

      // Make process active
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: null,
        clearError: jest.fn(),
      });

      rerender(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      // Should appear after delay
      await waitFor(
        () => {
          expect(screen.getByTestId("progress-bar")).toBeInTheDocument();
        },
        { timeout: 1000 }
      );
    });

    it("should hide component immediately when process becomes inactive", () => {
      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 1,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: null,
        clearError: jest.fn(),
      });

      const { rerender } = render(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      // Make process active first
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: null,
        clearError: jest.fn(),
      });

      rerender(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      // Make process inactive
      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 1,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: null,
        clearError: jest.fn(),
      });

      rerender(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      expect(screen.queryByTestId("progress-bar")).not.toBeInTheDocument();
    });
  });

  describe("thread slug changes", () => {
    it("should reset error dismissed state when thread slug changes", () => {
      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 1,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: "First error",
        clearError: jest.fn(),
      });

      const { rerender } = render(<ChatProgress threadSlug={MOCK_THREAD_ID} />);

      // Dismiss the error
      fireEvent.click(screen.getByText("Dismiss Error"));
      expect(screen.queryByTestId("chat-error")).not.toBeInTheDocument();

      // Change thread slug
      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 1,
        totalSteps: 7,
        flowType: null,
        cancel: jest.fn(),
        error: "Second error",
        clearError: jest.fn(),
      });

      rerender(<ChatProgress threadSlug="different-thread" />);

      // Error should show again for new thread
      expect(screen.getByTestId("chat-error")).toBeInTheDocument();
      expect(screen.getByText("Error: Second error")).toBeInTheDocument();
    });
  });

  describe("updateHistory callback behavior", () => {
    it("should add cancellation message with correct structure", async () => {
      const mockCancel = jest.fn();

      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 7,
        flowType: null,
        cancel: mockCancel,
        error: null,
        clearError: jest.fn(),
      });

      render(
        <ChatProgress
          threadSlug={MOCK_THREAD_ID}
          updateHistory={mockUpdateHistory}
        />
      );

      await waitFor(() => {
        fireEvent.click(screen.getByText("Abort"));
      });

      // Modal should open, but updateHistory not called yet
      expect(screen.getByTestId("abort-warning-modal")).toBeInTheDocument();
      expect(mockUpdateHistory).not.toHaveBeenCalled();

      // Click "Abort Run" in the modal
      fireEvent.click(screen.getByText("Abort Run"));

      expect(mockUpdateHistory).toHaveBeenCalledWith(expect.any(Function));

      // Test the function passed to updateHistory
      const updateFunction = mockUpdateHistory.mock.calls[0][0];
      const mockPrevHistory = [{ id: 1, content: "Previous message" }];
      const result = updateFunction(mockPrevHistory);

      expect(result).toHaveLength(2);
      expect(result[1]).toMatchObject({
        type: "statusResponse",
        content: "Process was cancelled",
        role: "assistant",
        closed: true,
        error: null,
        animate: false,
        pending: false,
      });
      expect(result[1].uuid).toMatch(/^cancelled-\d+$/);
    });
  });
});
