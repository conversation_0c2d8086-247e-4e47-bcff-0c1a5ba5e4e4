# Troubleshooting User Endpoints

This document outlines common issues and solutions when working with user-related endpoints in the ISTLegal application.

## Common Issues

### 1. Invalid User ID Validation Errors

**Symptoms:**

- Prisma errors: "Invalid `prisma.users.findFirst()` invocation - Argument `id` is missing"
- 500 Internal Server Error responses
- JWT token validation failures

**Root Cause:**
The `User.get()` method and related functions were not properly validating user IDs before passing them to Prisma database queries. Invalid values like `null`, `undefined`, or `NaN` were being passed directly to Prisma, causing validation errors.

**Solution:**
Enhanced ID validation in multiple layers:

1. **User Model (`server/models/user.js`)**:

   ```javascript
   static async get(clause = {}) {
     // Validate ID if present
     if (clause.id !== undefined) {
       const id = clause.id;
       if (id === null || id === undefined || (typeof id === 'number' && isNaN(id))) {
         console.log(`User.get: Invalid ID provided: ${id} (type: ${typeof id})`);
         return null;
       }

       // Convert string IDs to numbers if valid
       if (typeof id === 'string') {
         const parsedId = parseInt(id);
         if (isNaN(parsedId)) {
           console.log(`User.get: Invalid string ID: ${id}`);
           return null;
         }
         clause.id = parsedId;
       }
     }

     // Proceed with Prisma query
     const user = await prisma.users.findFirst({ where: clause });
     return user || null;
   }
   ```

2. **JWT Validation (`server/utils/http/index.js`)**:

   ```javascript
   function userFromSession(request, response = null) {
     const token =
       request.cookies?.token || request.headers?.authorization?.split(" ")[1];
     if (!token) return null;

     try {
       const valid = jwt.verify(token, process.env.JWT_SECRET);
       if (!valid?.id) return null;

       // Validate and parse user ID
       const userId = parseInt(valid.id);
       if (isNaN(userId)) {
         console.log(`userFromSession: Invalid user ID in token: ${valid.id}`);
         return null;
       }

       return User.get({ id: userId });
     } catch (error) {
       return null;
     }
   }
   ```

3. **Middleware Validation (`server/utils/middleware/validatedRequest.js`)**:

   ```javascript
   function validateMultiUserRequest(request, response, next) {
     const user = userFromSession(request, response);
     if (!user || !user.id) {
       return response
         .status(401)
         .json({ success: false, error: "Unauthorized" });
     }

     // Validate user ID is a valid integer
     const userId = parseInt(user.id);
     if (isNaN(userId)) {
       return response
         .status(401)
         .json({ success: false, error: "Invalid user ID" });
     }

     response.locals.user = user;
     next();
   }
   ```

### 2. Route Registration Order Issues

**Symptoms:**

- 400 Bad Request errors for specific endpoints
- Routes being caught by wrong handlers
- Endpoints not being found despite correct implementation

**Root Cause:**
Route registration order in `server/index.js` can cause conflicts when routes have similar patterns. More specific routes must be registered before more general ones.

**Solution:**
Ensure proper route registration order:

```javascript
// server/index.js
// Register more specific routes first
app.use("/api", userCustomSystemPromptEndpoints);
app.use("/api", developerEndpoints); // This has catch-all routes like /user/:id

// General pattern: specific before general
app.use("/api/specific-endpoint", specificEndpoints);
app.use("/api/general", generalEndpoints);
```

### 3. Missing Environment Variables

**Symptoms:**

- JWT verification failures
- "JWT_SECRET is not defined" errors
- Authentication not working despite correct tokens

**Root Cause:**
Missing or incorrectly configured environment variables, particularly `JWT_SECRET`.

**Solution:**

1. Ensure `.env` file contains all required variables:

   ```
   JWT_SECRET=your-secret-key-here
   DATABASE_URL=your-database-url
   ```

2. Verify environment loading in `server/index.js`:

   ```javascript
   require("dotenv").config();

   if (!process.env.JWT_SECRET) {
     console.error("JWT_SECRET environment variable is required");
     process.exit(1);
   }
   ```

### 4. Database Schema Synchronization

**Symptoms:**

- Column does not exist errors
- Prisma schema out of sync with database
- Missing fields in database queries

**Root Cause:**
Database schema not synchronized with Prisma schema after migrations or schema changes.

**Solution:**

1. Run database synchronization:

   ```bash
   cd server
   npx prisma db push
   ```

2. For production, use proper migrations:
   ```bash
   npx prisma migrate deploy
   ```

## Debugging Techniques

### 1. Add Comprehensive Logging

Add detailed logging to trace the flow of data:

```javascript
// In User.get() method
console.log(`User.get called with clause:`, JSON.stringify(clause));
console.log(`User.get: Processing ID: ${clause.id} type: ${typeof clause.id}`);
console.log(
  `User.get: Final clause before Prisma call:`,
  JSON.stringify(clause)
);
```

### 2. Validate Input Parameters

Always validate input parameters before processing:

```javascript
// Check for null/undefined/NaN values
if (
  value === null ||
  value === undefined ||
  (typeof value === "number" && isNaN(value))
) {
  console.log(`Invalid value: ${value} (type: ${typeof value})`);
  return null;
}
```

### 3. Use parseInt() with Validation

When converting string IDs to numbers:

```javascript
const id = parseInt(stringId);
if (isNaN(id)) {
  console.log(`Invalid ID conversion: ${stringId} -> ${id}`);
  return null;
}
```

## Prevention Best Practices

### 1. Input Validation

- Always validate user inputs before database operations
- Use proper type checking and conversion
- Handle edge cases like null, undefined, and NaN

### 2. Error Handling

- Implement comprehensive error handling in all user-related endpoints
- Log errors with sufficient context for debugging
- Return appropriate HTTP status codes

### 3. Route Organization

- Register specific routes before general ones
- Use clear, non-conflicting route patterns
- Document route registration order in comments

### 4. Environment Management

- Use environment validation on server startup
- Provide clear error messages for missing variables
- Document all required environment variables

### 5. Database Operations

- Always validate IDs before Prisma operations
- Use proper error handling for database queries
- Keep database schema synchronized with application code

## Testing

When implementing fixes:

1. **Test with various ID types:**

   - Valid integers
   - String representations of numbers
   - null and undefined values
   - NaN values
   - Invalid strings

2. **Test authentication flows:**

   - Valid JWT tokens
   - Invalid JWT tokens
   - Missing tokens
   - Expired tokens

3. **Test route conflicts:**
   - Ensure specific routes work correctly
   - Verify general routes don't interfere
   - Test route parameter parsing

## Related Files

Key files involved in user endpoint functionality:

- `server/models/user.js` - User data model and validation
- `server/utils/http/index.js` - JWT validation and user session handling
- `server/utils/middleware/validatedRequest.js` - Request validation middleware
- `server/endpoints/userCustomSystemPrompt.js` - User custom prompt endpoints
- `server/index.js` - Route registration and server setup
- `frontend/src/hooks/useUser.js` - Frontend user state management
