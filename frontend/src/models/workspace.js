/* eslint-disable unused-imports/no-unused-vars */
/* eslint-disable no-unused-vars */
import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import WorkspaceThread from "@/models/workspaceThread";
import { v4 } from "uuid";
import useUserStore from "@/stores/userStore";
import useWorkspaceStore from "@/stores/workspaceStore";
import i18next from "../i18n";
import useProgressStore from "@/stores/progressStore";
import useStreamAbortStore from "@/stores/useStreamAbortStore";

const Workspace = {
  // Keep track of pending workspace creation requests
  _pendingCreations: new Map(),

  new: async function (data = {}) {
    const slugModule = useUserStore.getState().selectedModule;

    // Generate a unique key for this workspace creation request
    const requestKey = `${slugModule}_${data.name || "unnamed"}_${Date.now()}`;

    // Check if there's already a pending request for this workspace
    if (this._pendingCreations.has(requestKey)) {
      console.log("Duplicate workspace creation prevented");
      return this._pendingCreations.get(requestKey);
    }

    // Create a promise for this request and store it
    const creationPromise = (async () => {
      try {
        const { workspace, message } = await fetch(
          `${API_BASE}/workspace/new/${slugModule}`,
          { method: "POST", body: JSON.stringify(data), headers: baseHeaders() }
        )
          .then((res) => res.json())
          .catch((_) => {
            return { workspace: null, message: "Error fetching workspace" };
          });

        // Update cache via store action
        if (workspace) {
          useWorkspaceStore.getState().addWorkspace(workspace);
        }

        return { workspace, message };
      } finally {
        // Remove the pending request after a small delay
        setTimeout(() => {
          this._pendingCreations.delete(requestKey);
        }, 1000);
      }
    })();

    // Store the promise
    this._pendingCreations.set(requestKey, creationPromise);

    // Return the promise result
    return creationPromise;
  },
  update: async function (slug, data = {}) {
    const { workspace, message } = await fetch(
      `${API_BASE}/workspace/${slug}/update`,
      { method: "POST", body: JSON.stringify(data), headers: baseHeaders() }
    )
      .then((res) => res.json())
      .catch((_) => {
        return { workspace: null, message: "Error fetching workspace" };
      });

    return { workspace, message };
  },
  updateOrder: async function (workspaces) {
    const { success, message } = await fetch(
      `${API_BASE}/workspace/update-order`,
      {
        method: "POST",
        body: JSON.stringify(workspaces),
        headers: baseHeaders(),
      }
    )
      .then((res) => res.json())
      .catch((_) => {
        return { success: false, message: "Error updating workspace order" };
      });

    return { success, message };
  },
  all: async function () {
    // Get the current module directly from localStorage for consistency
    const moduleSlug = useUserStore.getState().selectedModule;

    // Use a cache-busting query parameter
    const timestamp = Date.now();

    const workspaces = await fetch(
      `${API_BASE}/workspaces/${moduleSlug}?_=${timestamp}`,
      {
        method: "GET",
        headers: {
          ...baseHeaders(),
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    )
      .then((res) => res.json())
      .then((res) => res.workspaces || [])
      .catch((_) => {
        console.error("Error fetching workspaces:");
        return [];
      });

    return workspaces;
  },
  fromAllModules: async function () {
    const moduleTypes = ["legal-qa", "document-drafting"];
    try {
      const workspaces = await Promise.all(
        moduleTypes.map((type) =>
          fetch(`${API_BASE}/workspaces/${type}`, {
            method: "GET",
            headers: baseHeaders(),
          })
            .then((res) => res.json())
            .then((res) => res.workspaces || [])
            .catch(() => [])
        )
      );
      return workspaces.flat();
    } catch (error) {
      console.error("Error fetching all workspaces:", error);
      return [];
    }
  },
  getPopulatedWorkspaces: async function () {
    try {
      const response = await fetch(`${API_BASE}/populated-workspaces`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error(
          `Error fetching populated workspaces: ${response.status}`
        );
      }

      const data = await response.json();
      return data.workspaces || [];
    } catch (error) {
      console.error("Error fetching populated workspaces:", error);
      return [];
    }
  },
  modifyEmbeddings: async function (slug, changes = {}) {
    const { workspace, message } = await fetch(
      `${API_BASE}/workspace/${slug}/update-embeddings`,
      {
        method: "POST",
        body: JSON.stringify(changes), // contains 'adds' and 'removes' keys that are arrays of filepaths
        headers: baseHeaders(),
      }
    )
      .then((res) => res.json())
      .catch((_) => {
        return { workspace: null, message: "Error modifying embeddings" };
      });

    return { workspace, message };
  },
  chatHistory: async function (slug) {
    const history = await fetch(`${API_BASE}/workspace/${slug}/chats`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json())
      .then((res) => res.history || [])
      .catch(() => []);
    return history;
  },
  chatLog: async function (chatId) {
    try {
      const response = await fetch(`${API_BASE}/workspace/chat-log/${chatId}`, {
        method: "GET",
        headers: baseHeaders(),
      });
      const data = await response.json();
      return data.chatLog || null;
    } catch (error) {
      console.error("Error fetching chat log:", error);
      return null;
    }
  },
  legalTask: async function (slug, legalTask) {
    try {
      const response = await fetch(`${API_BASE}/workspace/${slug}/legal-task`, {
        method: "POST",
        body: JSON.stringify({ slug, legalTask }),
        headers: baseHeaders(),
      });

      if (!response.ok) {
        console.error(
          `Server error: ${response.status} ${response.statusText}`
        );
        return null;
      }

      const data = await response.json();
      return data.legalTask || null;
    } catch (error) {
      console.error("Error retrieving legal task:", error);
      return null;
    }
  },

  updateChatFeedback: async function (chatId, slug, feedback) {
    const result = await fetch(
      `${API_BASE}/workspace/${slug}/chat-feedback/${chatId}`,
      {
        method: "POST",
        headers: baseHeaders(),
        body: JSON.stringify({ feedback }),
      }
    )
      .then((res) => res.ok)
      .catch(() => false);
    return result;
  },
  deleteChat: async function (chatId, slug = null) {
    if (slug) {
      // Original workspace-specific delete chat
      return await fetch(
        `${API_BASE}/workspace/${slug}/delete-chat/${chatId}`,
        { method: "DELETE", headers: baseHeaders() }
      )
        .then((res) => res.ok)
        .catch(() => false);
    }
    // General workspace chat deletion
    return await fetch(`${API_BASE}/workspace/workspace-chats/${chatId}`, {
      method: "PUT",
      headers: baseHeaders(),
    })
      .then((res) => res.json())
      .catch((_) => {
        console.error("Error deleting chat:");
        return { success: false, error: "Error deleting chat" };
      });
  },
  deleteChats: async function (slug = "", chatIds = []) {
    return await fetch(`${API_BASE}/workspace/${slug}/delete-chats`, {
      method: "DELETE",
      headers: baseHeaders(),
      body: JSON.stringify({ chatIds }),
    })
      .then((res) => {
        if (res.ok) return true;
        throw new Error("Failed to delete chats.");
      })
      .catch((_) => {
        console.error("Error deleting chats:");
        return false;
      });
  },
  deleteEditedChats: async function (slug = "", threadSlug = "", startingId) {
    if (threadSlug)
      return this.threads._deleteEditedChats(slug, threadSlug, startingId);
    return this._deleteEditedChats(slug, startingId);
  },
  updateChatResponse: async function (
    slug = "",
    threadSlug = "",
    chatId,
    newText
  ) {
    if (threadSlug)
      return this.threads._updateChatResponse(
        slug,
        threadSlug,
        chatId,
        newText
      );
    return this._updateChatResponse(slug, chatId, newText);
  },
  multiplexStream: async function ({
    workspaceSlug,
    threadSlug = null,
    prompt,
    chatHandler,
    attachments = [],
    chatId = null,
    isCanvasChat = false,
    preventChatCreation = false,
    cdb = false,
    invoice_ref = null,
    abortController = null,
    hasUploadedFile = false,
    docxContent = null,
    displayMessage = null,
    useDeepSearch = false,
    cdbOptions = [],
    settingsSuffix = "",
  }) {
    // Mark workspace as populated on any chat interaction
    try {
      const ws = await this.bySlug(workspaceSlug); // Fetch workspace details
      useWorkspaceStore.getState().markWorkspacePopulated(ws); // Store handles idempotency
    } catch (e) {
      console.error("Error marking workspace populated:", e);
    }

    var llmSelected = localStorage.getItem("selectedLLMOption");
    //This is for the binary llm selection, to make sure that the llm is set to 0
    //(first option) when the module is not document drafting or when the llm is not set.
    if (
      llmSelected === null ||
      localStorage.getItem("module") !== "document-drafting"
    ) {
      llmSelected = 0;
    }

    if (threadSlug)
      return this.threads.streamChat(
        { workspaceSlug, threadSlug },
        prompt,
        chatHandler,
        attachments,
        chatId,
        isCanvasChat,
        preventChatCreation,
        cdb,
        llmSelected,
        invoice_ref,
        abortController,
        hasUploadedFile,
        docxContent,
        displayMessage,
        useDeepSearch,
        cdbOptions,
        settingsSuffix
      );
    return this.streamChat(
      { slug: workspaceSlug },
      prompt,
      chatHandler,
      attachments,
      chatId,
      isCanvasChat,
      preventChatCreation,
      cdb,
      llmSelected,
      invoice_ref,
      abortController,
      hasUploadedFile,
      docxContent,
      displayMessage,
      useDeepSearch,
      cdbOptions,
      settingsSuffix
    );
  },
  streamChat: async function (
    workspaceSlug,
    message,
    handleChat,
    attachments,
    chatId,
    isCanvasChat,
    preventChatCreation,
    cdb,
    llmSelected,
    invoice_ref,
    passedAbortController,
    hasUploadedFile = false,
    displayMessage = null,
    useDeepSearch = false,
    cdbOptions = [],
    settingsSuffix = ""
  ) {
    // Use passed abort controller or create a new one
    // Note: For workspace-level streaming, we don't have a specific threadSlug
    // so we use the passed controller or create a new one
    const localAbortController = passedAbortController || new AbortController();

    // Subscribe to the abort request timestamp from the Zustand store
    let lastSeenAbortTimestamp =
      useStreamAbortStore.getState().abortRequestTimestamp;
    const unsubscribeFromStore = useStreamAbortStore.subscribe(
      (state, prevState) => {
        const newTimestamp = state.abortRequestTimestamp;
        console.log("[StreamAbort] Workspace subscription triggered:", {
          newTimestamp,
          lastSeenAbortTimestamp,
          state: state.abortRequestTimestamp,
        });
        if (newTimestamp !== null && newTimestamp !== lastSeenAbortTimestamp) {
          lastSeenAbortTimestamp = newTimestamp;
          console.log(
            "[StreamAbort] Aborting workspace stream due to abort request"
          );
          localAbortController.abort();
          handleChat({ id: v4(), type: "stopGeneration" });
        }
      }
    );

    const slugModule = useUserStore.getState().selectedModule;

    try {
      await fetchEventSource(
        `${API_BASE}/workspace/${workspaceSlug}/stream-chat/${slugModule}?cdb=${cdb}`,
        {
          method: "POST",
          body: JSON.stringify({
            message,
            attachments,
            chatId,
            isCanvasChat,
            preventChatCreation,
            cdb,
            llmSelected,
            invoice_ref,
            hasUploadedFile,
            displayMessage,
            useDeepSearch,
            cdbOptions,
            settings_suffix: settingsSuffix,
          }),
          headers: baseHeaders(),
          signal: localAbortController.signal,
          openWhenHidden: true,
          async onopen(response) {
            if (response.ok) {
              return; // everything's good
            } else if (
              response.status >= 400 &&
              response.status < 500 &&
              response.status !== 429
            ) {
              handleChat({
                id: v4(),
                type: "abort",
                textResponse: null,
                sources: [],
                close: true,
                error: `${i18next.t("errors.streaming.failed", { defaultValue: "An error occurred while streaming response." })} ${i18next.t("errors.streaming.code", { defaultValue: "Code", code: response.status })} ${response.status}`,
              });
              localAbortController.abort();
              throw new Error("Invalid Status code response.");
            } else {
              handleChat({
                id: v4(),
                type: "abort",
                textResponse: null,
                sources: [],
                close: true,
                error: `${i18next.t("errors.streaming.failed", { defaultValue: "An error occurred while streaming response." })} ${i18next.t("errors.streaming.unknown", { defaultValue: "Unknown Error." })}`,
              });
              localAbortController.abort();
              throw new Error("Unknown error");
            }
          },
          async onmessage(msg) {
            try {
              const chatResult = JSON.parse(msg.data);
              handleChat(chatResult);
            } catch (error) {
              console.error("Error parsing message data:", error);
            }
          },
          onerror(err) {
            handleChat({
              id: v4(),
              type: "abort",
              textResponse: null,
              sources: [],
              close: true,
              error: `${i18next.t("errors.streaming.failed", { defaultValue: "An error occurred while streaming response." })} ${err.message}`,
            });
            localAbortController.abort(); // Ensure abort is called on error
            throw new Error(err.message || "Stream error");
          },
        }
      );
    } catch (error) {
      if (error.name !== "AbortError") {
        // Only handle non-abort errors here, aborts are handled by the subscription
        handleChat({
          id: v4(),
          type: "abort",
          textResponse: error.message,
          sources: [],
        });
      }
    } finally {
      // IMPORTANT: Unsubscribe from the store when the stream function is done
      unsubscribeFromStore();
    }
  },
  bySlug: async function (slug = "", includeDocuments = false) {
    // Extract any query parameters already in the slug
    let baseSlug = slug;
    let queryParams = `includeDocuments=${includeDocuments}`;

    if (slug.includes("?")) {
      const [slugPart, queryPart] = slug.split("?");
      baseSlug = slugPart;
      queryParams = `${queryPart}&includeDocuments=${includeDocuments}`;
    }

    // Add cache-busting parameter
    const timestamp = Date.now();
    queryParams = `${queryParams}&_=${timestamp}`;

    console.log(
      `[bySlug] Fetching workspace with slug: ${baseSlug} and params: ${queryParams}`
    );

    const workspace = await fetch(
      `${API_BASE}/workspace/${baseSlug}?${queryParams}`,
      {
        headers: {
          ...baseHeaders(),
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    )
      .then((res) => res.json())
      .then((res) => res.workspace)
      .catch(() => null);
    return workspace;
  },
  delete: async function (slug) {
    const result = await fetch(`${API_BASE}/workspace/${slug}`, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then((res) => res.ok)
      .catch(() => false);

    if (result) {
      useWorkspaceStore.getState().removeWorkspace(slug);
    }
    return result;
  },
  wipeVectorDb: async function (slug) {
    return await fetch(`${API_BASE}/workspace/${slug}/reset-vector-db`, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then((res) => res.ok)
      .catch(() => false);
  },
  uploadFile: async function (slug, formData) {
    const slugModule = useUserStore.getState().selectedModule;

    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/upload/${slugModule}`,
        { method: "POST", body: formData, headers: baseHeaders() }
      );

      const data = await response.json();
      return { response, data };
    } catch (error) {
      // Handle network-level errors
      return {
        response: { ok: false },
        data: {
          error:
            error.name === "TypeError" &&
            error.message.includes("Failed to fetch")
              ? "ERR_INSUFFICIENT_RESOURCES"
              : error.message,
          originalname: formData.get("file").name,
        },
      };
    }
  },
  getTokenCount: async function (slug) {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/token-count`,
        { method: "GET", headers: baseHeaders() }
      );
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Failed to fetch token count:", error);
      return null;
    }
  },
  uploadLink: async function (slug, link) {
    const response = await fetch(`${API_BASE}/workspace/${slug}/upload-link`, {
      method: "POST",
      body: JSON.stringify({ link }),
      headers: baseHeaders(),
    });

    const data = await response.json();
    return { response, data };
  },

  getSuggestedMessages: async function (slug) {
    return await fetch(`${API_BASE}/workspace/${slug}/suggested-messages`, {
      method: "GET",
      cache: "no-cache",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Could not fetch suggested messages.");
        return res.json();
      })
      .then((res) => res.suggestedMessages)
      .catch((_) => {
        console.error("Error fetching suggested messages:");
        return null;
      });
  },
  setSuggestedMessages: async function (slug, messages) {
    return fetch(`${API_BASE}/workspace/${slug}/suggested-messages`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ messages }),
    })
      .then((res) => {
        if (!res.ok) {
          throw new Error(
            res.statusText || "Error setting suggested messages."
          );
        }
        return { success: true, ...res.json() };
      })
      .catch((_) => {
        console.error("Error setting suggested messages:");
        return { success: false, error: "Error setting suggested messages" };
      });
  },
  setPinForDocument: async function (
    slug,
    docPath,
    pinStatus,
    isFolder = false
  ) {
    return fetch(`${API_BASE}/workspace/${slug}/update-pin`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ docPath, pinStatus, isFolder }),
    })
      .then((res) => {
        if (!res.ok) {
          throw new Error(
            res.statusText || "Error setting pin status for document."
          );
        }
        return true;
      })
      .catch((_) => {
        console.error("Error setting pin status for document:");
        return false;
      });
  },
  setPDRForDocument: async function (
    slug,
    docPath,
    pdrStatus,
    isFolder = false
  ) {
    return fetch(`${API_BASE}/workspace/${slug}/update-pdr`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ docPath, pdrStatus, isFolder }),
    })
      .then((res) => {
        if (!res.ok) {
          throw new Error(
            res.statusText || "Error setting PDR status for document."
          );
        }
        return true;
      })
      .catch((_) => {
        console.error("Error setting PDR status for document:");
        return false;
      });
  },
  setStarForDocument: async function (
    slug,
    docPath,
    starStatus,
    isFolder = false,
    forceUpdate = false
  ) {
    return fetch(`${API_BASE}/workspace/${slug}/update-star`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ docPath, starStatus, isFolder, forceUpdate }),
    })
      .then((res) => {
        if (!res.ok) {
          throw new Error(
            res.statusText || "Error setting star status for document."
          );
        }
        return res.json();
      })
      .catch((error) => {
        console.error("Error setting star status for document:", error);
        return { success: false, error: error.message };
      });
  },

  reVectorizeDocument: async function (slug, docId) {
    return fetch(`${API_BASE}/workspace/${slug}/re-vectorize`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ docId }),
    })
      .then((res) => {
        if (!res.ok) {
          throw new Error(
            res.statusText || "Error updating document metadata."
          );
        }
        return res.json();
      })
      .catch((error) => {
        console.error("Error updating document metadata:", error);
        return { success: false, error: error.message };
      });
  },
  ttsMessage: async function (slug, chatId) {
    return await fetch(`${API_BASE}/workspace/${slug}/tts/${chatId}`, {
      method: "GET",
      cache: "no-cache",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (res.ok && res.status !== 204) return res.blob();
        throw new Error("Failed to fetch TTS.");
      })
      .then((blob) => (blob ? URL.createObjectURL(blob) : null))
      .catch((_) => {
        return null;
      });
  },
  uploadPfp: async function (formData, slug) {
    return await fetch(`${API_BASE}/workspace/${slug}/upload-pfp`, {
      method: "POST",
      body: formData,
      headers: baseHeaders(),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Error uploading pfp.");
        return { success: true, error: null };
      })
      .catch((_) => {
        console.error("Error uploading pfp:");
        return { success: false, error: "Error uploading pfp" };
      });
  },

  fetchPfp: async function (slug) {
    return await fetch(`${API_BASE}/workspace/${slug}/pfp`, {
      method: "GET",
      cache: "no-cache",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (res.ok && res.status !== 204) return res.blob();
        throw new Error("Failed to fetch pfp.");
      })
      .then((blob) => (blob ? URL.createObjectURL(blob) : null))
      .catch((_) => {
        return null;
      });
  },

  removePfp: async function (slug) {
    return await fetch(`${API_BASE}/workspace/${slug}/remove-pfp`, {
      method: "DELETE",
      headers: baseHeaders(),
    })
      .then((res) => {
        if (res.ok) return { success: true, error: null };
        throw new Error("Failed to remove pfp.");
      })
      .catch((_) => {
        console.error("Error removing pfp:");
        return { success: false, error: "Error removing pfp" };
      });
  },
  _updateChatResponse: async function (slug = "", chatId, newText) {
    return await fetch(`${API_BASE}/workspace/${slug}/update-chat`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ chatId, newText }),
    })
      .then((res) => {
        if (res.ok) return true;
        throw new Error("Failed to update chat.");
      })
      .catch((_) => {
        console.error("Error updating chat:");
        return false;
      });
  },
  _deleteEditedChats: async function (slug = "", startingId) {
    return await fetch(`${API_BASE}/workspace/${slug}/delete-edited-chats`, {
      method: "DELETE",
      headers: baseHeaders(),
      body: JSON.stringify({ startingId }),
    })
      .then((res) => {
        if (res.ok) return true;
        throw new Error("Failed to delete chats.");
      })
      .catch((_) => {
        console.error("Error deleting edited chats:");
        return false;
      });
  },
  forkThread: async function (slug = "", threadSlug = null, chatId = null) {
    return await fetch(`${API_BASE}/workspace/${slug}/thread/fork`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ threadSlug, chatId }),
    })
      .then((res) => {
        if (!res.ok) throw new Error("Failed to fork thread.");
        return res.json();
      })
      .then((data) => data.newThreadSlug)
      .catch((_) => {
        console.error("Error forking thread:");
        return null;
      });
  },
  /**
   * Uploads and embeds a single file in a single call into a workspace
   * @param {string} slug - workspace slug
   * @param {FormData} formData
   * @returns {Promise<{response: {ok: boolean}, data: {success: boolean, error: string|null, document: {id: string, location:string}|null}}>}
   */
  uploadAndEmbedFile: async function (slug, formData) {
    const slugModule = useUserStore.getState().selectedModule;

    const response = await fetch(
      `${API_BASE}/workspace/${slug}/upload-and-embed/${slugModule}`,
      { method: "POST", body: formData, headers: baseHeaders() }
    );

    const data = await response.json();
    return { response, data };
  },

  upgradeUserPrompt: async (prompt, settings_suffix = null) => {
    const body = { prompt };
    if (settings_suffix) {
      body.settings_suffix = settings_suffix;
    }

    const response = await fetch(`${API_BASE}/upgrade-prompt`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(body),
    });

    const data = await response.json();
    return { response, data };
  },

  upgradeDeepSearchPrompt: async (prompt) => {
    const response = await fetch(`${API_BASE}/upgrade-deepsearch-prompt`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ prompt }),
    });

    const data = await response.json();
    return { response, data };
  },

  /**
   * Deletes and un-embeds a single file in a single call from a workspace
   * @param {string} slug - workspace slug
   * @param {string} documentLocation - location of file eg: custom-documents/my-file-uuid.json
   * @returns {Promise<boolean>}
   */
  deleteAndUnembedFile: async function (slug, documentLocation) {
    const response = await fetch(
      `${API_BASE}/workspace/${slug}/remove-and-unembed`,
      {
        method: "DELETE",
        body: JSON.stringify({ documentLocation }),
        headers: baseHeaders(),
      }
    );
    return response.ok;
  },

  /**
   * Gets all documents in a folder for a workspace
   * @param {string} slug - workspace slug
   * @param {string} folderName - name of the folder
   * @returns {Promise<Array>} - array of documents in the folder
   */
  getFolderDocuments: async function (slug, folderName) {
    try {
      // First get the workspace with all documents
      const workspace = await this.bySlug(slug, true);
      if (!workspace || !workspace.documents) {
        return [];
      }

      // Filter documents that belong to the specified folder
      // The folder path should be at the beginning of the document path
      const folderPath = folderName.endsWith("/")
        ? folderName
        : `${folderName}/`;
      const documentsInFolder = workspace.documents.filter((doc) => {
        return doc.docpath.startsWith(folderPath);
      });

      console.log(
        `[getFolderDocuments] Found ${documentsInFolder.length} documents in folder ${folderName}`
      );
      return documentsInFolder;
    } catch (error) {
      console.error(`Error getting documents for folder ${folderName}:`, error);
      return [];
    }
  },
  threads: WorkspaceThread,
  getDocumentDraftingSuggestedMessages: async function (slug) {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/document-drafting-suggested-messages`,
        { method: "GET", headers: baseHeaders() }
      );
      const result = await response.json();
      return result.messages || [];
    } catch (error) {
      console.error(
        "Error fetching document drafting suggested messages:",
        error
      );
      return [];
    }
  },
  setDocumentDraftingSuggestedMessages: async function (slug, messages) {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/document-drafting-suggested-messages`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify({ messages }),
        }
      );
      const result = await response.json();
      return { success: response.ok, error: result.message };
    } catch (error) {
      console.error(
        "Error setting document drafting suggested messages:",
        error
      );
      return { success: false, error: error.message };
    }
  },
  async deleteWorkspace(workspaceId) {
    try {
      const response = await fetch(`${API_BASE}/workspace/${workspaceId}`, {
        method: "DELETE",
      });
      const success = response.ok;
      if (success) {
        useWorkspaceStore.getState().removeWorkspace(workspaceId);
      }
      return success;
    } catch (error) {
      console.error("Error deleting workspace:", error);
      return false;
    }
  },

  async deleteThread(workspaceId, threadId) {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${workspaceId}/thread/${threadId}`,
        { method: "DELETE" }
      );
      const success = response.ok;
      if (success) {
        window.dispatchEvent(
          new CustomEvent("THREAD_DELETED", {
            detail: { workspaceId, threadId },
          })
        );
      }
      return success;
    } catch (error) {
      console.error("Error deleting thread:", error);
      return false;
    }
  },

  // Sharing methods
  getShareStatus: async function (slug) {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/share-status`,
        {
          method: "GET",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch share status");
      }

      return await response.json();
    } catch (error) {
      console.error("Error fetching workspace share status:", error);
      return { success: false, error: error.message };
    }
  },

  share: async function (slug, { userIds = [], shareWithOrg = undefined }) {
    try {
      const response = await fetch(`${API_BASE}/workspace/${slug}/share`, {
        method: "POST",
        headers: baseHeaders(),
        body: JSON.stringify({ userIds, shareWithOrg }),
      });

      if (!response.ok) {
        throw new Error("Failed to share workspace");
      }

      return await response.json();
    } catch (error) {
      console.error("Error sharing workspace:", error);
      return { success: false, error: error.message };
    }
  },

  revokeShareUser: async function (slug, userId) {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${slug}/revoke-share`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify({ userId }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to revoke workspace share");
      }

      return await response.json();
    } catch (error) {
      console.error("Error revoking workspace share:", error);
      return { success: false, error: error.message };
    }
  },

  // Thread sharing methods
  getThreadShareStatus: async function (threadId) {
    try {
      const thread = await WorkspaceThread.get(threadId);
      if (!thread) {
        throw new Error("Thread not found");
      }

      const response = await fetch(
        `${API_BASE}/workspace/${thread.workspaceSlug}/thread/${thread.slug}/share-status`,
        {
          method: "GET",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch thread share status");
      }

      return await response.json();
    } catch (error) {
      console.error("Error fetching thread share status:", error);
      return { success: false, error: error.message };
    }
  },

  shareThread: async function (
    threadId,
    { userIds = [], shareWithOrg = undefined }
  ) {
    try {
      const thread = await WorkspaceThread.get(threadId);
      if (!thread) {
        throw new Error("Thread not found");
      }

      const response = await fetch(
        `${API_BASE}/workspace/${thread.workspaceSlug}/thread/${thread.slug}/share`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify({ userIds, shareWithOrg }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to share thread");
      }

      return await response.json();
    } catch (error) {
      console.error("Error sharing thread:", error);
      return { success: false, error: error.message };
    }
  },

  revokeThreadShareUser: async function (threadId, userId) {
    try {
      const thread = await WorkspaceThread.get(threadId);
      if (!thread) {
        throw new Error("Thread not found");
      }

      const response = await fetch(
        `${API_BASE}/workspace/${thread.workspaceSlug}/thread/${thread.slug}/revoke-share`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify({ userId }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to revoke thread share");
      }

      return await response.json();
    } catch (error) {
      console.error("Error revoking thread share:", error);
      return { success: false, error: error.message };
    }
  },
};

export default Workspace;
