const prisma = require("../utils/prisma");
const { EventLogs } = require("./eventLogs");

const WorkspaceShare = {
  create: async function (workspaceId, userId) {
    // Idempotent: if a share already exists, return it
    const existing = await prisma.workspaceShare.findFirst({
      where: { workspaceId, userId },
    });
    if (existing) {
      return { share: existing, error: null };
    }
    try {
      const share = await prisma.workspaceShare.create({
        data: {
          workspaceId,
          userId,
        },
      });
      await EventLogs.logEvent("workspace_shared", { workspaceId, userId });
      return { share, error: null };
    } catch (error) {
      console.error("Failed to create workspace share:", error);
      return { share: null, error: "Failed to create workspace share" };
    }
  },

  getByWorkspace: async function (workspaceId) {
    try {
      const shares = await prisma.workspaceShare.findMany({
        where: { workspaceId },
        include: { user: true },
      });
      return { shares, error: null };
    } catch (error) {
      console.error("Failed to get workspace shares:", error);
      return { shares: [], error: "Failed to get workspace shares" };
    }
  },

  delete: async function (workspaceId, userId) {
    try {
      await prisma.workspaceShare.deleteMany({
        where: {
          workspaceId,
          userId,
        },
      });
      await EventLogs.logEvent("workspace_share_revoked", {
        workspaceId,
        userId,
      });
      return { success: true, error: null };
    } catch (error) {
      console.error("Failed to delete workspace share:", error);
      return { success: false, error: "Failed to delete workspace share" };
    }
  },

  hasAccess: async function (workspaceId, userId) {
    if (!prisma) {
      console.error(
        "FATAL: prisma instance is undefined in WorkspaceShare.hasAccess."
      );
      return false; // Cannot proceed
    }
    if (!prisma.workspaceShare) {
      console.error(
        "FATAL: prisma.workspaceShare model is undefined in WorkspaceShare.hasAccess. Prisma keys:",
        Object.keys(prisma)
      );
      return false; // Cannot proceed
    }

    try {
      // Check direct share
      const directShare = await prisma.workspaceShare.findFirst({
        where: {
          workspaceId,
          userId,
        },
      });
      if (directShare) return true;

      // Check organization-wide share based on the workspace owner
      const workspaceRow = await prisma.workspaces.findUnique({
        where: { id: workspaceId },
        select: { sharedWithOrg: true, user_id: true },
      });
      if (workspaceRow?.sharedWithOrg) {
        const targetUser = await prisma.users.findUnique({
          where: { id: userId },
          select: { organizationId: true },
        });
        if (targetUser?.organizationId) {
          // Check if the workspace owner belongs to the same organization
          const owner = await prisma.users.findUnique({
            where: { id: workspaceRow.user_id },
            select: { organizationId: true },
          });
          if (owner?.organizationId === targetUser.organizationId) {
            return true;
          }
        }
      }

      return false;
    } catch (error) {
      console.error("Failed to check workspace share access:", error);
      return false;
    }
  },
};

module.exports = { WorkspaceShare };
