import React, { useState, useRef, useEffect, useCallback } from "react";
import { SlashCommands, useSlashCommands } from "./SlashCommands";
import { AvailableAgents, useAvailableAgents } from "./AgentMenu";
import { useTranslation } from "react-i18next";
import Workspace from "@/models/workspace";
import { useParams } from "react-router-dom";
import System from "@/models/system";
import showToast from "@/utils/toast";
import UploadedFilesModal from "./UploadedFilesModal";
import PromptActions from "./PromptActions";
import PromptSubmission from "./PromptSubmission";
import UpgradePromptModal from "./UpgradePromptModal";
import PromptTextarea from "./PromptTextarea";
import LegalTemplateModal from "@/components/Modals/LegalTemplateModal/index";
import { SUPPORTED_UPGRADE_PROVIDERS } from "@/utils/AiProviders/supportedLLMProviders";
import {
  useSelectedFeatureCard,
  useSetSelectedFeatureCard,
} from "@/stores/userStore";

export const PROMPT_INPUT_EVENT = "prompt-input-update";
export const SUBMIT_PROMPT_EVENT = "submit-prompt";

export default function PromptInput({
  workspace,
  submit,
  onChange,
  inputDisabled,
  buttonDisabled,
  sendCommand,
  attachments = [],
  useDeepSearch,
  setUseDeepSearch,
  handleFiles,
  addUserMessageImmediately,
}) {
  const { t } = useTranslation();
  const selectedFeatureCard = useSelectedFeatureCard();
  const setSelectedFeatureCard = useSetSelectedFeatureCard();
  const [promptInput, setPromptInput] = useState("");
  const { showAgents, setShowAgents } = useAvailableAgents();
  const { showSlashCommand, setShowSlashCommand } = useSlashCommands();
  const formRef = useRef(null);
  const textareaRef = useRef(null);
  const [upgradedPrompt, setUpgradedPrompt] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [settings, setSettings] = useState();
  const [isLegalTaskEnabled, setIsLegalTaskEnabled] = useState(false);
  const [isFilesModalOpen, setIsFilesModalOpen] = useState(false);
  const [isLegalTemplateModalOpen, setIsLegalTemplateModalOpen] =
    useState(false);
  const { threadSlug } = useParams();

  useEffect(() => {
    if (selectedFeatureCard === "draft-from-template") {
      setIsLegalTemplateModalOpen(true);
      setSelectedFeatureCard(null);
    }
  }, [selectedFeatureCard]);

  useEffect(() => {
    async function fetchSettings() {
      const settings = await System.keys();
      setSettings(settings);
    }
    fetchSettings();
  }, []);

  useEffect(() => {
    const fetchLegalTaskStatus = async () => {
      try {
        const performLegalTaskResponse = await System.getPerformLegalTask();
        setIsLegalTaskEnabled(performLegalTaskResponse?.enabled || false);
      } catch (error) {
        console.error("Error fetching legal task status:", error);
      }
    };
    fetchLegalTaskStatus();
  }, []);

  useEffect(() => {
    if (!inputDisabled && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [inputDisabled]);

  useEffect(() => {
    if (window) window.addEventListener(PROMPT_INPUT_EVENT, handlePromptUpdate);
    return () =>
      window?.removeEventListener(PROMPT_INPUT_EVENT, handlePromptUpdate);
  }, []);

  const handlePromptUpdate = (e) => {
    setPromptInput(e?.detail ?? "");
  };

  const checkForSlash = (e) => {
    const input = e.target.value;
    if (input === "/") setShowSlashCommand(true);
    if (showSlashCommand) setShowSlashCommand(false);
  };

  const checkForAt = (e) => {
    const input = e.target.value;
    if (input === "@") return setShowAgents(true);
    if (showAgents) return setShowAgents(false);
  };

  const captureEnter = async (event) => {
    if (event.keyCode === 13 && !event.shiftKey) {
      event.preventDefault();
      if (!buttonDisabled && promptInput.trim()) {
        event.preventDefault();
        window.dispatchEvent(new CustomEvent(SUBMIT_PROMPT_EVENT));
      }
    }
  };

  const handleUpgradeUserPrompt = async () => {
    setIsLoading(true);
    try {
      const { data } = await Workspace.upgradeUserPrompt(promptInput);
      if (data.success) {
        setUpgradedPrompt(data.upgradedPrompt);
        setIsModalOpen(true);
      } else {
        showToast(t("show-toast.upgrade-answer-error"), "error");
      }
    } catch (error) {
      console.error("Error upgrading prompt:", error);
      showToast(t("show-toast.upgrade-answer-error"), "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitUpgradedPrompt = (editedPrompt) => {
    setIsModalOpen(false);
    submit(editedPrompt);
    setPromptInput("");
  };

  const handleDecline = () => {
    setIsModalOpen(false);
  };

  // Handler for the legal template modal
  // This function receives the generated prompt from the modal,
  // updates the input, and then submits it
  const handleLegalTemplateSubmit = useCallback(
    async (generatedPrompt, options = {}) => {
      const promptForLLM = options.fullPrompt || generatedPrompt;
      const firstSentence =
        promptForLLM.split("\n")[0].trim().split(".")[0].trim() + ".";

      const newPromptText = promptInput
        ? `${promptInput}\n\n${generatedPrompt}`
        : generatedPrompt;

      // Show a toast notification
      showToast(t("legal-templates.submitting-template"), "info");

      // Call onChange to ensure parent state is updated, like in Upgrade modal flow
      onChange({ target: { value: newPromptText } });

      // Create the display message for the user
      const displayMessage =
        options.displayMessage ||
        firstSentence ||
        t("legal-templates.draft-template-display");

      // Immediately add the user message to chat history to show it before processing
      const attachmentsList = [];
      const chatId = addUserMessageImmediately(
        displayMessage,
        promptForLLM,
        attachmentsList
      );

      // Now perform the background processing (prompt upgrade and LLM call)
      try {
        let upgradedPrompt = promptForLLM;
        try {
          const { data } = await Workspace.upgradeUserPrompt(
            promptForLLM,
            "_TM"
          );
          if (data?.success && data.upgradedPrompt) {
            upgradedPrompt = data.upgradedPrompt;
          }
        } catch (e) {
          console.error("Prompt upgrade failed, proceeding with original:", e);
        }

        // Use a custom submit that skips adding the user message again
        submit(upgradedPrompt, {
          displayMessage,
          settingsSuffix: "_TM",
          skipUserMessage: true, // Flag to skip adding user message since we already did it
          existingChatId: chatId, // Use the same chatId
        });
      } catch (error) {
        console.error("Error submitting legal template:", error);
        showToast(t("legal-templates.submission-error"), "error");
      } finally {
        // Clear the local input state after submission
        setPromptInput("");
      }
    },
    [
      promptInput,
      setPromptInput,
      t,
      submit,
      onChange,
      addUserMessageImmediately,
    ]
  );

  const isUpgradeAvailable =
    settings?.LLMProvider_PU &&
    (SUPPORTED_UPGRADE_PROVIDERS.includes(settings.LLMProvider_PU) ||
      settings.LLMProvider_PU === "system-standard");

  return (
    <div className="relative">
      <UploadedFilesModal
        isOpen={isFilesModalOpen}
        onClose={() => setIsFilesModalOpen(false)}
        workspace={workspace}
      />
      <div>
        <SlashCommands
          showing={showSlashCommand}
          setShowing={setShowSlashCommand}
          sendCommand={sendCommand}
        />
        <AvailableAgents
          showing={showAgents}
          setShowing={setShowAgents}
          sendCommand={sendCommand}
          promptRef={textareaRef}
        />
        <form
          onSubmit={(e) => {
            e.preventDefault();
            window.dispatchEvent(new CustomEvent(SUBMIT_PROMPT_EVENT));
          }}
          className="relative"
          ref={formRef}
        >
          <div className="flex flex-col items-center bg-background">
            <div className="flex flex-col gap-1 w-full mb-6 md:mb-12 pt-5 pb-3 border rounded-2xl bg-elevated shadow-sm">
              <PromptTextarea
                textareaRef={textareaRef}
                value={promptInput}
                onChange={(e) => {
                  onChange(e);
                  setPromptInput(e.target.value);
                }}
                onKeyDown={captureEnter}
                disabled={inputDisabled}
                checkForSlash={checkForSlash}
                checkForAt={checkForAt}
                handleFiles={handleFiles}
              />
              <div className="flex items-end justify-between pl-3 pr-4">
                <PromptActions
                  sendCommand={sendCommand}
                  isUpgradeAvailable={isUpgradeAvailable}
                  isLoading={isLoading}
                  handleUpgradeUserPrompt={handleUpgradeUserPrompt}
                  showSlashCommand={showSlashCommand}
                  setShowSlashCommand={setShowSlashCommand}
                  isLegalTaskEnabled={isLegalTaskEnabled}
                  promptInput={promptInput}
                  setIsFilesModalOpen={setIsFilesModalOpen}
                  attachments={attachments}
                  setIsLegalTemplateModalOpen={setIsLegalTemplateModalOpen}
                  useDeepSearch={useDeepSearch}
                  setUseDeepSearch={setUseDeepSearch}
                  workspace={workspace}
                />
                <PromptSubmission
                  promptInput={promptInput}
                  setPromptInput={setPromptInput}
                  buttonDisabled={buttonDisabled}
                  submit={submit}
                  onChange={onChange}
                  formRef={formRef}
                  textareaRef={textareaRef}
                  setShowSlashCommand={setShowSlashCommand}
                  setShowAgents={setShowAgents}
                  upgradedPrompt={upgradedPrompt}
                  isModalOpen={isModalOpen}
                  setIsModalOpen={setIsModalOpen}
                  threadSlug={threadSlug}
                />
              </div>
            </div>
          </div>
        </form>
      </div>

      <UpgradePromptModal
        isOpen={isModalOpen}
        originalPrompt={promptInput}
        upgradedPrompt={upgradedPrompt}
        onAccept={handleSubmitUpgradedPrompt}
        onDecline={handleDecline}
        onChange={onChange}
      />

      <LegalTemplateModal
        isOpen={isLegalTemplateModalOpen}
        onClose={() => setIsLegalTemplateModalOpen(false)}
        onSubmit={handleLegalTemplateSubmit}
      />
    </div>
  );
}
