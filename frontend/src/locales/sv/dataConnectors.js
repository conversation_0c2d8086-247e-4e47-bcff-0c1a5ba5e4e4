export default {
  // =========================
  // DATA CONNECTORS
  // =========================
  dataConnectors: {
    github: {
      name: "GitHub Repo",
      description:
        "Importera ett helt offentligt eller privat GitHub-repository med ett enda klick.",
      url: "GitHub Repo URL",
      "collect-url": "URL till GitHub-repot du vill samla in.",
      "access-token": "GitHub Åtkomsttoken",
      optional: "valfritt",
      "rate-limiting": "Åtkomsttoken för att undvika rate-limiting.",
      "desc-picker":
        "När processen är klar blir alla filer tillgängliga för inbäddning i dokumenthanteraren.",
      branch: "Gren",
      "branch-desc": "Grenen du vill samla filer ifrån.",
      "branch-loading": "-- laddar tillgängliga grenar --",
      "desc-start": "Utan att fylla i",
      "desc-token": "GitHub Åtkomsttoken",
      "desc-connector": "kommer denna datakontakt endast kunna samla in",
      "desc-level": "top-nivåns",
      "desc-end":
        "filer i repot på grund av GitHubs offentliga API-begränsningar.",
      "personal-token":
        "Få en personlig åtkomsttoken gratis med ett GitHub-konto här.",
      without: "Utan en",
      "personal-token-access": "Personlig Åtkomsttoken",
      "desc-api":
        ", kan GitHub-API begränsa antalet filer som kan samlas in p.g.a. rate-limits. Du kan",
      "temp-token": "skapa en tillfällig åtkomsttoken",
      "avoid-issue": "för att undvika detta problem.",
      submit: "Skicka",
      "collecting-files": "Samlar in filer...",
    },
    "youtube-transcript": {
      name: "YouTube Transkript",
      description:
        "Importera transkriptet av en hel YouTube-video från en länk.",
      url: "YouTube Video URL",
      "url-video": "URL till den YouTube-video du vill transkribera.",
      collect: "Hämta transkript",
      collecting: "Hämtar transkript...",
      "desc-end":
        "När processen är klar blir transkriptet tillgängligt för inbäddning i dokumenthanteraren.",
    },
    "website-depth": {
      name: "Bulk Link Scraper",
      description:
        "Skrapa en webbplats och dess underlänkar ner till en viss nivå.",
      url: "Webbplats URL",
      "url-scrape": "URL till webbplatsen du vill skrapa.",
      depth: "Djup",
      "child-links":
        "Antalet underlänkar som ska följas från ursprungs-URL:en.",
      "max-links": "Max antal länkar",
      "links-scrape": "Max antal länkar att skrapa.",
      scraping: "Skrapar webbplats...",
      submit: "Skicka",
      "desc-scrap":
        "När klart blir alla skrapade sidor tillgängliga för inbäddning i dokumenthanteraren.",
    },
    confluence: {
      name: "Confluence",
      description: "Importera en hel Confluence-sida med ett klick.",
      url: "Confluence Sid URL",
      "url-page": "URL till en sida i Confluence-utrymmet.",
      username: "Confluence Användarnamn",
      "own-username": "Ditt Confluence-användarnamn.",
      token: "Confluence Åtkomsttoken",
      "desc-start":
        "Du måste ange en åtkomsttoken för autentisering. Du kan generera en API-token",
      here: "här",
      access: "Åtkomsttoken för autentisering.",
      collecting: "Samlar in sidor...",
      submit: "Skicka",
      "desc-end":
        "När klart blir alla sidor tillgängliga för inbäddning i dokumenthanteraren.",
    },
  },
  // =========================
  // CONFLUENCE CONNECTOR COMPONENT
  // =========================
  confluence: {
    "space-key": "Confluence utrymmesnyckel",
    "space-key-desc":
      "Detta är nyckeln för ditt Confluence-utrymme som kommer att användas. Börjar vanligtvis med ~",
    "space-key-placeholder": "t.ex: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "t.ex: https://example.atlassian.net, http://localhost:8211, etc...",
    "token-tooltip": "Du kan skapa en API-token",
    "token-tooltip-here": "här",
  },
};
