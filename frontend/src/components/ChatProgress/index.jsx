import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import ProgressModal from "@/components/Modals/ProgressModal";
import AbortWarningModal from "@/components/Modals/AbortWarningModal";
import useThreadProgress from "@/hooks/useThreadProgress";
import Progress from "@/components/ui/Progress";
import ChatError from "./ChatError";

export default function ChatProgress({ threadSlug = "", updateHistory }) {
  const { t } = useTranslation();
  const [isProgressModalOpen, setIsProgressModalOpen] = useState(false);
  const [isAbortModalOpen, setIsAbortModalOpen] = useState(false);
  const [isErrorDismissed, setIsErrorDismissed] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  const {
    isActive,
    currentStep,
    totalSteps,
    flowType,
    cancel,
    error,
    clearError,
  } = useThreadProgress(threadSlug);

  useEffect(() => {
    if (threadSlug && isActive) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 500);

      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
    }
  }, [threadSlug, isActive]);

  useEffect(() => {
    setIsErrorDismissed(false);
  }, [threadSlug]);

  useEffect(() => {
    if (isActive) {
      setIsErrorDismissed(false);
    }
  }, [isActive]);

  const addCancellationMessage = () => {
    if (updateHistory) {
      const cancellationMessage = {
        uuid: `cancelled-${Date.now()}`,
        type: "statusResponse",
        content: t("chatProgress.cancelled"),
        role: "assistant",
        closed: true,
        error: null,
        animate: false,
        pending: false,
      };

      updateHistory((prevHistory) => [...prevHistory, cancellationMessage]);
    }
  };

  const handleDismissError = () => {
    setIsErrorDismissed(true);
    addCancellationMessage();
    clearError();
  };

  if (error && !isErrorDismissed) {
    return <ChatError error={error} onDismiss={handleDismissError} />;
  }

  if (!threadSlug || !isActive) return null;

  const handleAbort = () => {
    cancel();
    addCancellationMessage();
  };

  const handleShowAbortModal = () => {
    setIsAbortModalOpen(true);
  };

  const handleCancelAbort = () => {
    setIsAbortModalOpen(false);
  };

  const handleConfirmAbort = () => {
    setIsAbortModalOpen(false);
    handleAbort();
  };

  const handleViewDetails = () => {
    setIsProgressModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsProgressModalOpen(false);
  };

  const progressValue =
    totalSteps > 0 ? Math.round(((currentStep - 1) / totalSteps) * 100) : 0;

  const stepKey =
    flowType && currentStep
      ? `chatProgress.types.${flowType}.step${currentStep}`
      : null;

  const stepLabel = stepKey
    ? t(`${stepKey}.label`)
    : `${t("chatProgress.step")} ${currentStep}`;
  const stepDescription = stepKey
    ? t(`${stepKey}.desc`)
    : t("chatProgress.processing");

  return (
    <>
      <div
        className={`flex flex-col gap-6 max-w-[26rem] px-5 pt-5 pb-6 rounded-xl border bg-elevated transition-all duration-300 transform ${
          isVisible ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-8"
        }`}
      >
        <div className="flex items-start justify-between gap-4">
          <div className="flex flex-col gap-1 max-w-[18rem]">
            <h4 className="text-lg font-medium truncate">{stepLabel}</h4>
            <p className="text-muted font-medium line-clamp-2">
              {stepDescription}
            </p>
          </div>

          <div className="flex flex-col items-center gap-3 w-20">
            <Button
              variant="outline"
              size="sm"
              onClick={handleViewDetails}
              className="w-full"
            >
              {t("chatProgress.details")}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleShowAbortModal}
              className="w-full text-red-600"
            >
              {t("chatProgress.abort")}
            </Button>
          </div>
        </div>

        <Progress value={progressValue} />
      </div>

      <ProgressModal
        isOpen={isProgressModalOpen}
        onClose={handleCloseModal}
        threadSlug={threadSlug}
      />

      <AbortWarningModal
        isOpen={isAbortModalOpen}
        onCancel={handleCancelAbort}
        onAbort={handleConfirmAbort}
      />
    </>
  );
}
