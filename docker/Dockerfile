ARG TARGETARCH=amd64
# Setup base image
FROM ubuntu:jammy-20240627.1 AS base

# Build arguments
ARG ARG_UID=1000
ARG ARG_GID=1000

# amd64-specific stage
FROM base AS build-amd64
ARG ARG_UID=1000
ARG ARG_GID=1000
RUN echo "Preparing build of ISTLegal image for non-ARM architecture"
ENV NODE_ENV=production
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV GENERATE_SOURCEMAP=false

SHELL ["/bin/bash", "-o", "pipefail", "-c"]

# Install necessary packages
RUN DEBIAN_FRONTEND=noninteractive apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -yq --no-install-recommends \
        curl gnupg libgfortran5 libgbm1 tzdata netcat \
        libasound2 libatk1.0-0 libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 \
        libgcc1 libglib2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libx11-6 libx11-xcb1 libxcb1 \
        libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 \
        libxss1 libxtst6 ca-certificates fonts-liberation libappindicator1 libnss3 lsb-release \
        xdg-utils git build-essential ffmpeg \
        graphicsmagick imagemagick ghostscript \
        libcairo2-dev libjpeg-dev libpango1.0-dev libgif-dev librsvg2-dev pkg-config && \
    mkdir -p /etc/apt/keyrings && \
    curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg && \
    echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_20.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list && \
    apt-get update && \
        # Install node
    apt-get install -yq --no-install-recommends nodejs && \
    # Install uvx (pinned to 0.6.10) for MCP support
    curl -LsSf https://astral.sh/uv/0.6.10/install.sh | sh && \
        mv /root/.local/bin/uv /usr/local/bin/uv && \
        mv /root/.local/bin/uvx /usr/local/bin/uvx && \
        echo "Installed uvx! $(uv --version)" && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create a group and user with specific UID and GID
RUN groupadd -g "$ARG_GID" istlegal && \
    useradd -l -u "$ARG_UID" -m -d /app -s /bin/bash -g istlegal istlegal && \
    mkdir -p /app/frontend /app/server /app/collector && chown -R istlegal:istlegal /app

# Copy docker helper scripts
COPY ./docker/docker-entrypoint.sh /usr/local/bin/
COPY ./docker/docker-healthcheck.sh /usr/local/bin/
COPY --chown=istlegal:istlegal ./docker/.env.example /app/server/.env

# Ensure the scripts are executable
RUN chmod +x /usr/local/bin/docker-entrypoint.sh && \
    chmod +x /usr/local/bin/docker-healthcheck.sh

# hadolint ignore=DL3006
FROM build-${TARGETARCH} AS build
RUN echo "Running common build flow of ISTLegal image for all architectures"

USER istlegal
WORKDIR /app

# Install & Build frontend layer
FROM build AS frontend-build
COPY --chown=istlegal:istlegal frontend /app/frontend/
WORKDIR /app/frontend
ENV NODE_OPTIONS="--max-old-space-size=8192"
ENV GENERATE_SOURCEMAP=false
RUN npm ci --production=false && npm cache clean --force

# Split build into separate steps for clearer diagnostics
RUN echo 'Starting frontend production build' && set -x
RUN NODE_ENV=production npm run build
RUN echo 'Build output:' && ls -la
RUN cp -r dist /tmp/frontend-build
RUN echo 'Cleaning workspace except tmp' && find . -mindepth 1 -maxdepth 1 -not -name 'tmp' -exec rm -rf {} +
RUN echo 'Copying build artifacts back' && cp -r /tmp/frontend-build/* .
RUN echo 'Removing temporary build directory' && rm -rf /tmp/frontend-build

WORKDIR /app

# Install server layer
# Also pull and build collector deps (chromium issues prevent bad bindings)
FROM build AS backend-build
COPY --chown=istlegal:istlegal ./server /app/server/
COPY --chown=istlegal:istlegal ./version.json /app/version.json
WORKDIR /app/server
ENV NODE_OPTIONS="--max-old-space-size=4096"
RUN npm ci --production && npm cache clean --force
WORKDIR /app

# Install collector dependencies
COPY --chown=istlegal:istlegal ./collector/ ./collector/
WORKDIR /app/collector
ENV PUPPETEER_DOWNLOAD_BASE_URL=https://storage.googleapis.com/chrome-for-testing-public
RUN npm ci --production \
    && npm install --include=optional sharp \
    && npm install --os=linux --cpu=x64 sharp \
    && npm cache clean --force

# Creates appropriate bindings for the OS
USER root
WORKDIR /app/server
RUN npx --no node-llama-cpp download || (echo "Failed to download node-llama-cpp, retrying..." && npx --no node-llama-cpp download)
WORKDIR /app
USER istlegal

# Since we are building from backend-build we just need to move built frontend into server/public
RUN npm cache clean --force && \
    rm -rf /tmp/* && \
    rm -rf /var/lib/apt/lists/*
FROM backend-build AS production-build
WORKDIR /app
COPY --chown=istlegal:istlegal --from=frontend-build /app/frontend/. /app/server/public
USER root
RUN chown -R istlegal:istlegal /app/server && \
    chown -R istlegal:istlegal /app/collector
USER istlegal

# Setup the environment
ENV NODE_ENV=production
ENV ANYTHING_LLM_RUNTIME=docker

# Setup the healthcheck
HEALTHCHECK --interval=1m --timeout=10s --start-period=1m \
  CMD /bin/bash /usr/local/bin/docker-healthcheck.sh || exit 1

# Run the server
ENTRYPOINT ["/bin/bash", "/usr/local/bin/docker-entrypoint.sh"]
