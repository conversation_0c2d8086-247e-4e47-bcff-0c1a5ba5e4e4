export default {
  // Bsed on this order of pages in system settings:
  // Customization (Appearance)
  //Admin: System settings
  //Admin: Users
  //Admin: Invites
  //Admin: Workspaces
  //Admin: MCP Servers
  //Admin: Feedback List
  //Admin: Workspace chats
  //Admin: Default system
  //Admin: Document Builder
  //Admin: DD Prompt settings
  //Admin: Chat UI settings
  //AI-Providers: LLM
  //AI-Providers: Prompt Upgrade LLM
  //AI-Providers: Validation LLM
  //AI-Providers: CDB LLM
  //AI-Providers: Custom User AI
  //AI-Providers:  Vector Database
  //AI-Providers: Re-ranking settings
  //AI-Providers: Deep search
  //AI-Providers: Embedder
  //AI-Providers: Text Splitter & Chunking
  //AI-Providers: Voice & Speech
  //AI-Providers: transcription
  //AI-Providers: PDR System
  //Agent skills
  //Tools: Chat Embed History
  //Tools: Chat Embed
  //Tools: Event logs
  //Tools: Developer API
  //Tools: Browser Extension
  //Security
  //Document Drafting: Configuration
  //Document Drafting: Drafting LLM
  //Document Drafting: Chat Settings

  // =========================
  // LLM PREFERENCES PAGE
  // =========================
  llm: {
    title: "LLM-inställningar",
    description:
      "Dessa är referenser och inställningar för din föredragna LLM-chatt & inbäddningsleverantör. Det är viktigt att dessa nycklar är aktuella och korrekta eller så kommer systemet inte att fungera korrekt.",
    provider: "LLM-leverantör",
    "secondary-provider": "Sekundär LLM-leverantör",
    "none-selected": "Ingen vald",
    "select-llm": "Du måste välja en LLM",
    "search-llm": "Sök bland alla LLM-leverantörer",
    "context-window-warning":
      "Varning: Kunde inte hämta kontextfönster för den valda modellen.",
    "context-window-waiting": " -- väntar på kontextfönsterinformation -- ",
    "validation-prompt": {
      disable: {
        label: "Inaktivera valideringsprompt",
        description:
          "När detta är aktiverat kommer valideringsknappen inte att visas i användargränssnittet.",
      },
    },
    "prompt-upgrade": {
      title: "LLM-leverantör för promptförbättring",
      description:
        "Den specifika LLM-leverantören och modellen som kommer att användas för att förbättra användarens prompt. Som standard används systemets LLM-leverantör och inställningar.",
      search: "Sök tillgängliga LLM-leverantörer för funktionen",
      template: "Mall för promptförbättring",
      "template-description":
        "Denna mall kommer att användas vid förbättring av prompts. Använd {{prompt}} för att referera till texten som ska förbättras.",
      "template-placeholder":
        "Ange mallen som ska användas för att förbättra prompts...",
      "template-hint":
        "Exempel: Uppdatera originalprompt för att passa juridisk professionell promptning. Texten som ska uppdateras är: {{prompt}}",
    },
    "logo-alt": "{{name}} logotyp",
    "context-window": "Kontextfönster",
    "default-context-window": "(standardstorlek för denna leverantör)",
    tokens: "tokens",
    "save-error": "Misslyckades att spara LLM-inställningar",
  },

  // =========================
  // LLM PROVIDER DESCRIPTIONS
  // =========================
  "llm-provider": {
    openai: "Standardalternativ för de flesta icke-kommersiella användningar.",
    azure: "Enterprisealternativ för OpenAI som är värd på Azure-tjänster.",
    anthropic: "En vänlig AI-assistent som är värd för Anthropic.",
    gemini: "Google's största och mest kapabla AI-modell",
    huggingface:
      "Åtkomst till 150,000+ öppna källkod LLM:er och världens AI-gemenskap",
    ollama: "Kör LLM lokalt på din egen maskin.",
    lmstudio:
      "Upptäck, ladda ner och kör tusentals av de mest avancerade LLM:erna på några klick.",
    localai: "Kör LLM lokalt på din egen maskin.",
    togetherai: "Kör öppna källkodsmodeller från Together AI.",
    mistral: "Kör öppna källkodsmodeller från Mistral AI.",
    perplexityai:
      "Kör kraftfulla och internetanslutna modeller som är värd för Perplexity AI.",
    openrouter: "En sammanhängande gränssnitt för LLM:er.",
    groq: "Den snabbaste LLM-inferensen som är tillgänglig för realtids-AI-applikationer.",
    koboldcpp: "Kör lokala LLM:er med koboldcpp.",
    oobabooga: "Kör lokala LLM:er med Oobabooga's Text Generation Web UI.",
    cohere: "Kör Cohere's kraftfulla Command-modeller.",
    lite: "Kör LiteLLM's OpenAI-kompatibla proxy för olika LLM:er.",
    "generic-openai":
      "Anslut till valfri OpenAI-kompatibel tjänst via en anpassad konfiguration",
    native:
      "Använd en nedladdad anpassad Llama-modell för att chatta på denna instans.",
    xai: "Kör xAI's kraftfulla LLM:er som Grok-2 och mer.",
    "aws-bedrock": "Kör kraftfulla foundation-modeller privat med AWS Bedrock.",
    deepseek: "Kör DeepSeek's kraftfulla LLM:er.",
    fireworksai:
      "Den snabbaste och mest effektiva inferensmotorn för att bygga produktionsfärdiga, komplexa AI-system.",
    bedrock: "Kör kraftfulla foundation-modeller privat med AWS Bedrock.",
  },

  "custom-user-ai": {
    title: "Anpassad användar-AI",
    settings: "Anpassad användar-AI",
    description: "Konfigurera den anpassade användar-AI-leverantören",
    "custom-model-reference": "Anpassat modellnamn & beskrivning",
    "custom-model-reference-description":
      "Lägg till en anpassad referens för denna modell. Detta kommer att vara synligt när du använder valväljaren för Anpassad användar-AI i promptpanelen.",
    "custom-model-reference-name": "Anpassat modellnamn",
    "custom-model-reference-description-label": "Modellbeskrivning (Valfritt)",
    "custom-model-reference-description-placeholder":
      "Ange en valfri beskrivning för denna modell",
    "custom-model-reference-name-placeholder":
      "Ange ett anpassat namn för denna modell",
    "model-ref-placeholder":
      "Ange ett anpassat namn eller beskrivning för denna modellinställning",
    "enter-custom-model-reference": "Ange ett anpassat namn för denna modell",
    "standard-engine": "Standard AI-motor",
    "standard-engine-description":
      "Vår standardmotor som är användbar för de flesta uppgifter",
    "dynamic-context-window-percentage":
      "Procentandel för dynamiskt kontextfönster",
    "dynamic-context-window-percentage-desc":
      "Styr hur mycket av denna LLM:s kontextfönster som kan användas för ytterligare källor (10-100%)",
    "no-alternative-title": "Ingen alternativ modell vald",
    "no-alternative-desc":
      "När detta alternativ är valt har användare inte möjlighet att välja en alternativ modell.",
    "select-option": "Välj anpassad AI-profil",
    tab: {
      "custom-1": "Anpassad motor 1",
      "custom-2": "Anpassad motor 2",
      "custom-3": "Anpassad motor 3",
      "custom-4": "Anpassad motor 4",
      "custom-5": "Anpassad motor 5",
      "custom-6": "Anpassad motor 6",
    },
    engine: {
      "custom-1": "Anpassad motor 1",
      "custom-2": "Anpassad motor 2",
      "custom-3": "Anpassad motor 3",
      "custom-4": "Anpassad motor 4",
      "custom-5": "Anpassad motor 5",
      "custom-6": "Anpassad motor 6",
      "custom-1-title": "Anpassad motor 1",
      "custom-2-title": "Anpassad motor 2",
      "custom-3-title": "Anpassad motor 3",
      "custom-4-title": "Anpassad motor 4",
      "custom-5-title": "Anpassad motor 5",
      "custom-6-title": "Anpassad motor 6",
      "custom-1-description": "Konfigurera inställningar för Anpassad motor 1",
      "custom-2-description": "Konfigurera inställningar för Anpassad motor 2",
      "custom-3-description": "Konfigurera inställningar för Anpassad motor 3",
      "custom-4-description": "Konfigurera inställningar för Anpassad motor 4",
      "custom-5-description": "Konfigurera inställningar för Anpassad motor 5",
      "custom-6-description": "Konfigurera inställningar för Anpassad motor 6",
    },
    "option-number": "Alternativ {{number}}",
    "llm-provider-selection": "LLM-leverantörsval",
    "llm-provider-selection-desc":
      "Välj LLM-leverantören för denna anpassade AI-konfiguration",
    "custom-option": "Anpassat alternativ",
    saving: "Sparar...",
    "save-changes": "Spara ändringar",
    "model-ref-saved": "Anpassade modellinställningar sparade",
    "model-ref-save-failed":
      "Kunde inte spara anpassade modellinställningar: {{error}}",
    "llm-settings-save-failed": "Kunde inte spara LLM-inställningar: {{error}}",
    "settings-fetch-failed": "Kunde inte hämta inställningar",
    "llm-saved": "LLM-inställningar sparade",
    "select-provider-first":
      "Välj en LLM-leverantör för att konfigurera modellinställningar. När det är konfigurerat kommer detta alternativ att kunna väljas som en anpassad AI-motor i användargränssnittet.",
  },
  // =========================
  // CDB LLM PREFERENCE
  // =========================
  "cdb-llm-preference": {
    title: "CDB LLM-preferens",
    settings: "CDB LLM",
    description: "Konfigurera LLM-leverantören för CDB",
  },
  // =========================
  // TEMPLATE LLM PREFERENCE
  // =========================
  "template-llm-preference": {
    title: "Mall-LLM-preferens",
    settings: "Mall-LLM",
    description:
      "Välj LLM-leverantören som används när dokumentmallar genereras. Standard är systemleverantören.",
    "toast-success": "Mall-LLM-inställningar uppdaterade",
    "toast-fail": "Misslyckades med att uppdatera Mall-LLM-inställningar",
    saving: "Sparar...",
    "save-changes": "Spara ändringar",
  },

  // =========================
  // AUDIO PREFERENCE
  // =========================
  audio: {
    title: "Inställningar för Tal-till-text",
    provider: "Leverantör",
    "system-native": "Systemets inbyggda",
    "desc-speech":
      "Här kan du ange vilken typ av tal-till-text och text-till-tal-leverantörer du vill använda. Som standard används webbläsarens inbyggda funktioner, men du kan välja andra.",
    "title-text": "Inställningar för Text-till-tal",
    "desc-text":
      "Här kan du ange vilken typ av text-till-tal-leverantörer du vill använda. Som standard används webbläsarens inbyggda funktioner, men du kan välja andra.",
    "desc-config": "Ingen konfiguration krävs för webbläsarens inbyggda TTS.",
    "placeholder-stt": "Sök tal-till-text-leverantörer",
    "placeholder-tts": "Sök text-till-tal-leverantörer",
    "native-stt": "Använder webbläsarens inbyggda STT om det stöds.",
    "native-tts": "Använder webbläsarens inbyggda TTS om det stöds.",
    "piper-tts": "Kör TTS-modeller lokalt i din webbläsare privat.",
    "openai-description": "Använd OpenAIs text-till-tal-röster och teknik.",
    openai: {
      "api-key": "API-nyckel",
      "api-key-placeholder": "OpenAI API-nyckel",
      "voice-model": "Röstmodell",
    },
    elevenlabs: "Använd ElevenLabs text-till-tal-röster och teknik.",
  },

  // =========================
  // TRANSCRIPTION PREFERENCE
  // =========================
  transcription: {
    title: "Transkriptionsmodellpreferens",
    description:
      "Dessa är referenser och inställningar för din föredragna transkriptionsmodellleverantör. Om dessa inte är aktuella eller korrekta fungerar inte mediefiler och ljud korrekt.",
    provider: "Transkriptionsleverantör",
    "warn-start":
      "Att använda den lokala Whisper-modellen på maskiner med begränsat RAM eller CPU kan göra att plattformen fastnar när den bearbetar mediefiler.",
    "warn-recommend":
      "Vi rekommenderar minst 2 GB RAM och att du laddar upp filer <10Mb.",
    "warn-end":
      "Den inbyggda modellen laddas ner automatiskt vid första användning.",
    "search-audio": "Sök ljudtranskriptionsleverantörer",
    "api-key": "API-nyckel",
    "api-key-placeholder": "OpenAI API-nyckel",
    "whisper-model": "Whisper-modell",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Inbyggd standardmodell",
    "default-built-in-desc":
      "Kör en inbyggd whisper-modell på denna instans privat.",
    "openai-name": "OpenAI",
    "openai-desc": "Använd OpenAI:s Whisper-large-modell med din API-nyckel.",
    "model-turbo": "openai/whisper-large-v3-turbo", // Ny modellnamn
    "model-size-turbo": "(~810mb)", // Ny modellstorlek
  },

  // =========================
  // EMBEDDING PREFERENCE
  // =========================
  embedding: {
    title: "Inbäddningspreferens",
    "desc-start":
      "När du använder en LLM som inte nativt stödjer en inbäddningsmotor kan du behöva ange referenser för att inbädda text.",
    "desc-end":
      "Inbäddning är processen att omvandla text till vektorer. Dessa referenser är nödvändiga för att göra om dina filer och prompts till ett format plattformen kan hantera.",
    provider: {
      title: "Inbäddningsleverantör",
      description:
        "Ingen konfiguration krävs om du använder plattformens inbyggda inbäddningsmotor.",
      "search-embed": "Sök bland alla inbäddningsleverantörer",
      search: "Sök bland alla inbäddningsleverantörer",
      select: "Välj en inbäddningsleverantör",
    },
    workspace: {
      title: "Arbetsytans inbäddningspreferens",
      description:
        "Den specifika inbäddningsleverantören & modellen som används för denna arbetsyta. Som standard används systemets inbäddningsleverantör och inställningar.",
      "multi-model": "Denna leverantör stöder ännu inte flera modeller.",
      "workspace-use": "Denna arbetsyta kommer att använda",
      "model-set": "den modell som är inställd i systemet.",
      embedding: "Arbetsytans inbäddningsmodell",
      model:
        "Den specifika inbäddningsmodellen som används för denna arbetsyta. Om det är tomt, används systemets inbäddningspreferens.",
      wait: "-- väntar på modeller --",
      setup: "Installera",
      use: "För att använda",
      "need-setup": "som denna arbetsytas inbäddare måste du konfigurera det.",
      cancel: "Avbryt",
      save: "Spara",
      settings: "inställningar",
      search: "Sök bland alla inbäddningsleverantörer",
      "need-llm": "som denna arbetsytas LLM måste du först konfigurera det.",
      "save-error":
        "Misslyckades att spara {{provider}} inställningar: {{error}}",
      "system-default": "Systemstandard",
      "system-default-desc":
        "Använd systemets inbäddningspreferens för denna arbetsyta.",
    },
    warning: {
      "switch-model":
        "Byt inbäddningsmodell kommer att bryta för tidigare inbäddade dokument från att fungera under chatt. De måste un-embeds från alla arbetsytter och tas bort och laddas upp igen så att de kan inbäddas av den nya inbäddningsmodellen.",
    },
  },

  // =========================
  // TEXT SPLITTING & CHUNKING
  // =========================
  text: {
    title: "Textdelning & Chunking-preferenser",
    "desc-start":
      "Ibland kan du behöva ändra standardmetoden för hur nya dokument delas upp och chunkas innan de läggs till i din vektordatabas.",
    "desc-end":
      "Ändra endast detta om du förstår hur textdelning fungerar och vilka följder det kan ha.",
    "warn-start": "Ändringar här gäller endast",
    "warn-center": "nyligen inbäddade dokument",
    "warn-end": ", inte befintliga dokument.",
    method: {
      title: "Text-splitter metod",

      "native-explain":
        "Använd lokal chunkstorlek & överlappning för uppdelning.",

      "jina-explain":
        "Delegera chunking/segmentering till Jinas inbyggda metod.",

      "jina-info": "Jina-chunking aktiv.",

      jina: {
        api_key: "Jina API-nyckel",
        api_key_desc:
          "Krävs för att använda Jinas segmenteringstjänst. Nyckeln kommer att lagras i din miljö.",
        max_tokens: "Jina: Max tokens per chunk",
        max_tokens_desc:
          "Definierar max tokens i varje chunk för Jinas segmenterare (max 2000 tokens).",
        return_tokens: "Returnera token-information",
        return_tokens_desc:
          "Inkludera token-antal och tokenizer-information i svaret.",
        return_chunks: "Returnera chunk-information",
        return_chunks_desc:
          "Inkludera detaljerad information om genererade chunks i svaret.",
      },
    },
    size: {
      title: "Textchunkstorlek",
      description:
        "Detta är den maximala teckenlängden som får finnas i en enskild vektor.",
      recommend: "Inbäddningsmodellens maximala längd är",
    },
    overlap: {
      title: "Textchunk överlappning",
      description:
        "Detta är den maximala överlappningen av tecken mellan två intilliggande textchunks.",
      error:
        "Chunk-överlappningen kan inte vara större eller lika med chunkstorleken.",
    },
  },
  // =========================
  // CONTEXTUAL SETTINGS
  // =========================
  contextual: {
    checkbox: {
      label: "Kontextuell inbäddning",
      hint: "Aktivera kontextuell inbäddning för att förbättra inbäddningsprocessen med ytterligare parametrar",
    },
    systemPrompt: {
      label: "Systemmeddelande",
      placeholder: "Ange ett värde...",
      description:
        "Exempel: Ge en kort och koncis kontext för att placera detta stycke inom hela dokumentet, i syfte att förbättra sökningen efter stycket. Svara endast med den koncisa kontexten och inget annat.",
    },
    userPrompt: {
      label: "Användarmeddelande",
      placeholder: "Ange ett värde...",
      description:
        "Exempel: <document>\n{file}\n</document>\nHär är stycket vi vill placera inom hela dokumentet\n<chunk>\n{chunk}\n</chunk>",
    },
  },
  // =========================
  // CHAT UI SETTINGS
  // =========================
  "chat-ui-settings": {
    title: "Chattgränssnittsinställningar",
    description: "Konfigurera chattinställningarna.",
    auto_submit: {
      title: "Automatisk inlämning av talad input",
      description: "Skicka automatiskt talad input efter en period av tystnad",
    },
    auto_speak: {
      title: "Automatisk uppläsning av svar",
      description: "Läs automatiskt upp svar från AI:n",
    },
  },
  // =========================
  // PIPER TTS OPTIONS
  // =========================
  piperTTS: {
    description:
      "Alla PiperTTS-modeller körs lokalt i din webbläsare. Detta kan vara resurskrävande på enheter med lägre prestanda.",

    "voice-model": "Val av röstmodell",
    "loading-models": "-- laddar tillgängliga modeller --",

    "stored-indicator":
      'Ett "✔" visar att modellen redan är lagrad lokalt och inte behöver laddas ner när den körs.',

    "flush-cache": "Töm röstcache",
    "flush-success": "Alla röster togs bort från webbläsarens lagring",

    demo: {
      stop: "Stoppa demo",
      loading: "Laddar röst",
      play: "Spela exempel",
      text: "Detta är en exempeltext för att demonstrera rösten.",
    },
  },

  // =========================
  // VECTOR DATABASE (SYSTEM)
  // =========================
  vector: {
    title: "Vektordatabas",

    description:
      "Detta är inloggningsuppgifterna och inställningarna för hur din plattformsinstans ska fungera. Det är viktigt att dessa nycklar är aktuella och korrekta.",

    provider: {
      title: "Vektordatabasleverantör",
      description: "Ingen konfiguration behövs för LanceDB.",
      "search-db": "Sök alla vektordatabasleverantörer",
      search: "Sök alla vektordatabaser",
      select: "Välj vektordatabasleverantör",
    },

    warning:
      "Att byta vektordatabas kommer att kräva att du återinbäddar alla dokument i alla relevanta arbetsytor. Detta kan ta lite tid.",

    search: {
      title: "Vektorsökläge",
      mode: {
        "globally-enabled":
          "Denna inställning styrs globalt i systeminställningarna. Besök systeminställningar för att ändra omrankning.",
        default: "Standardsökning",
        "default-desc": "Standard vektorlikhetssökning utan omrankning.",
        "accuracy-optimized": "Optimerad för noggrannhet",
        "accuracy-desc":
          "Omrangordnar resultat för att förbättra noggrannheten, men ökar genereringstid.",
      },
    },
  },
  // =========================
  // VECTOR DATABASE PROVIDER DESCRIPTIONS
  // =========================
  vectordb: {
    lancedb:
      "100 % lokal vektordatabas som körs på samma instans som plattformen.",
    chroma:
      "Öppen källkod vektordatabas som du kan drifta själv eller i molnet.",
    pinecone: "100 % molnbaserad vektordatabas för företagsanvändningsfall.",
    zilliz:
      "Molnbaserad vektordatabas byggd för företag med SOC 2-efterlevnad.",
    qdrant: "Öppen källkod, lokal och distribuerad molnvektordatabas.",
    weaviate: "Öppen källkod, lokal och molnhostad multimodal vektordatabas.",
    milvus: "Öppen källkod, mycket skalbar och extremt snabb.",
    astra: "Vektorsök för verklig GenAI.",
  },

  // =========================
  // DEEP SEARCH SETTINGS
  // =========================
  deep_search: {
    title: "Djupsökning",
    description:
      "Konfigurera webbsökningsfunktioner för chattsvar. När aktiverad kan systemet söka på webben efter information för att förbättra svar.",
    enable: "Aktivera Djupsökning",
    enable_description:
      "Tillåt systemet att söka på webben efter information när det svarar på frågor.",
    provider_settings: "Leverantörsinställningar",
    provider: "Sökleverantör",
    model: "Modell",
    api_key: "API-nyckel",
    api_key_placeholder: "Ange din API-nyckel",
    api_key_placeholder_set:
      "API-nyckel är inställd (ange ny nyckel för att ändra)",
    api_key_help:
      "Din API-nyckel lagras säkert och används endast för webbsökningsförfrågningar.",
    context_percentage: "Kontextprocent",
    context_percentage_help:
      "Procentandel av LLM:s kontextfönster som ska allokeras för webbsökningsresultat (5-20%).",
    fetch_error: "Kunde inte hämta Djupsökningsinställningar",
    save_success: "Djupsökningsinställningar sparades framgångsrikt",
    save_error: "Kunde inte spara Djupsökningsinställningar: {{error}}",
    toast_success: "Djupsökningsinställningar sparades framgångsrikt",
    toast_error: "Kunde inte spara Djupsökningsinställningar: {{error}}",
    brave_recommended:
      "Brave Search är för närvarande det rekommenderade och mest tillförlitliga leverantörsalternativet.",
  },

  // =========================
  // PDR SETTINGS
  // =========================
  "pdr-settings": {
    title: "PDR-inställningar",

    description:
      "Hantera inställningar för Parent Document Retrieval för dina arbetsytor.",

    "desc-end":
      "Dessa inställningar påverkar hur PDR-dokument hanteras och används i chattsvar.",

    "global-override": {
      title: "Global dynamisk PDR-åsidosättning",
      description:
        "När detta är aktiverat kommer alla arbetsytors dokument att hanteras som PDR-aktiverade i källgenereringen. När det är inaktiverat kommer endast dokument som uttryckligen markerats som PDR att hanteras som sådana, vilket kan minska källsammanhang och leda till svar av lägre kvalitet eftersom endast vektordelar från sökningar kommer att användas som källor i dessa fall.",
    },

    "toast-success": "PDR-inställningar uppdaterade",
    "toast-fail": "Misslyckades att uppdatera PDR-inställningarna.",
    "adjacent-vector-limit": "Angränsande vektorgräns",
    "adjacent-vector-limit-desc": "Gräns för hur många angränsande vektorer.",
    "adjacent-vector-limit-placeholder": "Ange angränsande vektorgräns",
    "keep-pdr-vectors": "Behåll PDR-vektorer",
    "keep-pdr-vectors-desc": "Inställning för att behålla PDR-vektorer.",
  },

  // =========================
  // AGENTS
  // =========================
  agents: {
    title: "Agentfärdigheter",
    "agent-skills": "Konfigurera och hantera agentfunktioner",
    "custom-skills": "Anpassade färdigheter",
    back: "Tillbaka",
    "select-skill": "Välj en färdighet att konfigurera",
    "preferences-saved": "Agentinställningar sparades",
    "preferences-failed": "Det gick inte att spara agentinställningar",
    "skill-status": {
      on: "På",
      off: "Av",
    },
    "skill-config-updated": "Färdighetskonfiguration uppdaterad framgångsrikt",
  },
  // =========================
  // AGENT MENU
  // =========================
  "agent-menu.default-agent": "Standardagent",
  "agent-menu.ability.rag-search": "RAG-sökning",
  "agent-menu.ability.web-scraping": "Webbskrapning",
  "agent-menu.ability.web-browsing": "Webbläsning",
  "agent-menu.ability.save-file-to-browser": "Spara fil till webbläsare",
  "agent-menu.ability.list-documents": "Lista dokument",
  "agent-menu.ability.summarize-document": "Sammanfatta dokument",
  "agent-menu.ability.chart-generation": "Diagramgenerering",
  // =========================
  // BADGES AGENT PAGE
  // =========================
  badges: {
    default: {
      text: "Standard",
      tooltip:
        "Denna färdighet är aktiverad som standard och kan inte stängas av.",
    },
  },
  // =========================
  // EMBED CHATS
  // =========================
  "embed-chats": {
    title: "Inbäddningschattar",
    export: "Exportera",
    description:
      "Här visas alla registrerade chattar och meddelanden från en publicerad inbäddning.",
    table: {
      embed: "Inbäddning",
      sender: "Avsändare",
      message: "Meddelande",
      response: "Svar",
      at: "Skickat vid",
    },
    delete: {
      title: "Ta bort chat",
      message: "Är du säker på att du vill ta bort denna chat?",
    },
    config: {
      "delete-title": "Ta bort chat",
      "delete-message": "Är du säker på att du vill ta bort denna chat?",
      "disable-title": "Ta bort chat",
      "disable-message": "Är du säker på att du vill ta bort denna chat?",
      "enable-title": "Ta bort chat",
      "enable-message": "Är du säker på att du vill ta bort denna chat?",
    },
  },
  // =========================
  // EMBEDDABLE CHAT WIDGETS
  // =========================
  embeddable: {
    title: "Inbäddningsbara chattwidgets",
    description:
      "Inbäddningsbara chattwidgets är publika chattgränssnitt som är knutna till en enskild arbetsyta. Dessa låter dig bygga arbetsytor som du sedan kan publicera för allmänheten.",
    create: "Skapa inbäddning",
    table: {
      workspace: "Arbetsyta",
      chats: "Skickade chattar",
      Active: "Aktiva domäner",
    },
  },
  // =========================
  // NEW EMBED
  // =========================
  "new-embed": {
    title: "Skapa ny inbäddning för arbetsyta",
    error: "Fel: ",
    "desc-start":
      "Efter att ha skapat en inbäddning får du en länk som du kan publicera på din webbplats med en enkel",
    script: "script",
    tag: "tag.",
    cancel: "Avbryt",
    "create-embed": "Skapa inbäddning",
    workspace: "Arbetsyta",
    "desc-workspace":
      "Detta är arbetsytan som chatten baseras på. Alla standardvärden ärvs från arbetsytan om de inte åsidosätts av denna konfiguration.",
    "allowed-chat": "Tillåten chattmetod",
    "desc-query":
      "Ange hur din chattbot ska bete sig. 'Fråga' innebär att den endast besvarar frågor om det finns dokumentkontext.",
    "desc-chat":
      "Chatt öppnar boten för generella frågor och kan besvara frågor som inte är relaterade till arbetsytan.",
    "desc-response": "Chatt: Svara på alla frågor oavsett kontext",
    "query-response":
      "Fråga: Endast svar om frågan är relaterad till dokument i arbetsytan",
    restrict: "Begränsa förfrågningar från domäner",
    filter:
      "Detta filter blockerar alla förfrågningar från andra domäner än de listade nedan.",
    "use-embed":
      "Om detta lämnas tomt kan vem som helst använda din inbäddning på vilken webbplats som helst.",
    "max-chats": "Max chattar per dag",
    "limit-chats":
      "Begränsa antalet chattar denna inbäddade chatt kan hantera under en 24-timmarsperiod. 0 innebär obegränsat.",
    "chats-session": "Max chattar per session",
    "limit-chats-session":
      "Begränsa antalet chattar en användarsession kan skicka i denna inbäddning under en 24-timmarsperiod. 0 innebär obegränsat.",
    "enable-dynamic": "Aktivera dynamisk modellanvändning",
    "llm-override":
      "Tillåt att man väljer en föredragen LLM-modell som åsidosätter arbetsytans standard.",
    "llm-temp": "Aktivera dynamisk LLM-temperatur",
    "desc-temp":
      "Tillåt att LLM-temperaturen kan ställas in för att åsidosätta arbetsytans standard.",
    "prompt-override": "Aktivera prompt-återställning",
    "desc-override":
      "Tillåt att systemprompten kan åsidosättas och ändra arbetsytans standardprompt.",
  },
  // =========================
  // EMBED CONFIGS
  // =========================
  embedConfigs: {
    "show-code": "Visa Kod",
    enable: "Aktivera",
    disable: "Inaktivera",
    "all-domains": "alla",
    "disable-confirm":
      "Är du säker på att du vill inaktivera denna inbäddning?\nNär den är inaktiverad kommer den inte längre att svara på några chattförfrågningar.",
    "delete-confirm":
      "Är du säker på att du vill radera denna inbäddning?\nNär den är raderad kommer den inte längre att svara på chattar.\n\nDenna åtgärd går inte att ångra.",
    "disabled-toast": "Inbäddning har inaktiverats",
    "enabled-toast": "Inbäddning är aktiverad",
  },
  // =========================
  // EVENT LOGS
  // =========================
  event: {
    title: "Händelseloggar",
    description:
      "Visa alla åtgärder och händelser som sker på denna instans för övervakning.",
    clear: "Rensa händelseloggar",
    table: {
      type: "Händelsetyp",
      user: "Användare",
      occurred: "Inträffade vid",
    },
  },
  // =========================
  // API KEYS
  // =========================
  api: {
    title: "API-nycklar",
    description:
      "API-nycklar gör att innehavaren kan komma åt och hantera denna instans via programmering.",
    link: "Läs API-dokumentationen",
    generate: "Generera ny API-nyckel",
    table: {
      key: "API-nyckel",
      by: "Skapad av",
      created: "Skapad",
    },
    new: {
      title: "Skapa ny API-nyckel",
      description:
        "När den har skapats kan API-nyckeln användas för att programmässigt komma åt och konfigurera denna instans.",
      doc: "Läs API-dokumentationen",
      cancel: "Avbryt",
      "create-api": "Skapa API-nyckel",
    },
  },
  // =========================
  // BROWSER EXTENSION API KEYS
  // =========================
  "browser-extension-api": {
    title: "API-nycklar",
    description: "Hantera API-nycklar för anslutning till denna instans.",
    "generate-key": "Generera ny API-nyckel",
    "table-headers": {
      "connection-string": "Anslutningssträng",
      "created-by": "Skapad av",
      "created-at": "Skapad",
      actions: "Åtgärder",
    },
    "no-keys": "Inga API-nycklar hittades",
    modal: {
      title: "Ny webbläsartillägg API-nyckel",
      "multi-user-warning":
        "Varning: Du är i fleranvändarläge. Denna API-nyckel kommer att ge tillgång till alla arbetsytor kopplade till ditt konto. Var försiktig med delning.",
      "create-description":
        'Efter att du klickat på "Skapa API-nyckel" kommer denna instans att försöka skapa en ny API-nyckel för webbläsartillägget.',
      "connection-help":
        'Om du ser "Ansluten till IST Legal" i tillägget var anslutningen lyckad. Om inte, vänligen kopiera anslutningssträngen och klistra in den manuellt i tillägget.',
      cancel: "Avbryt",
      "create-key": "Skapa API-nyckel",
      "copy-key": "Kopiera API-nyckel",
      "key-copied": "API-nyckel kopierad!",
    },
    tooltips: {
      "copy-connection": "Kopiera anslutningssträng",
      "auto-connect": "Anslut automatiskt till tillägget",
    },
    confirm: {
      revoke:
        "Är du säker på att du vill återkalla denna webbläsartillägg API-nyckel?\nEfter detta kommer den inte längre att vara användbar.\n\nDenna åtgärd kan inte ångras.",
    },
    toasts: {
      "key-revoked": "Webbläsartillägg API-nyckel har permanent återkallats",
      "revoke-failed": "Kunde inte återkalla API-nyckeln",
      copied: "Anslutningssträng kopierad till urklipp",
      connecting: "Försöker ansluta till webbläsartillägget...",
    },
    "revoke-title": "Återkalla webbläsartillägg API-nyckel",
    "revoke-message":
      "Är du säker på att du vill återkalla denna webbläsartillägg API-nyckel?\nEfter detta kommer den inte längre att vara användbar.\n\nDenna åtgärd kan inte ångras.",
  },
  // =========================
  // SECURITY
  // =========================
  security: {
    "multi-user-mode-permanent":
      "Fleranvändarläge är permanent aktiverat av säkerhetsskäl",
    "password-validation": {
      "restricted-chars":
        "Ditt lösenord innehåller otillåtna tecken. Tillåtna symboler är _,-,!,@,$,%,^,&,*,(,),;",
    },
    "public-workspace": {
      "access-description":
        "När aktiverat kan alla användare komma åt de offentliga arbetsytorna utan att logga in.",
    },
    button: {
      saving: "Sparar...",
      "save-changes": "Spara ändringar",
    },
  },

  // =========================
  // MULTI-USER MODE
  // =========================
  multi: {
    title: "Multi-användarläge",
    description:
      "Konfigurera din instans för att stödja flera användare genom att aktivera multi-användarläge.",
    enable: {
      "is-enable": "Multi-användarläge är aktiverat",
      enable: "Aktivera multi-användarläge",
      description:
        "Som standard är du den enda administratören. Som administratör måste du skapa konton för alla nya användare eller administratörer. Förlora inte ditt lösenord eftersom endast en administratör kan återställa lösenord.",
      username: "Administratörskonto (e-post)",
      password: "Administratörslösenord",
      "username-placeholder": "Ditt admin-användarnamn",
      "password-placeholder": "Ditt admin-lösenord",
    },
    password: {
      title: "Lösenordsskydd",
      description:
        "Skydda din instans med ett lösenord. Om du glömmer detta finns ingen metod för återställning, så säkerställ att du sparar det.",
    },
    instance: {
      title: "Lösenordsskydda instans",
      description:
        "Som standard är du den enda administratören. Du måste skapa konton för alla nya användare eller administratörer. Förlora inte lösenordet eftersom endast administratörer kan återställa det.",
      password: "Instanslösenord",
    },
  },

  // =========================
  // EXPERIMENTAL FEATURES
  // =========================
  experimental: {
    title: "Experimentella Funktioner",
    description: "Funktioner som för närvarande är i betatestningsfas",
    "live-sync": {
      title: "Live-dokumentsynkronisering",
      description:
        "Aktivera automatisk innehållssynkronisering från externa källor",
      "manage-title": "Övervakade dokument",
      "manage-description":
        "Detta är alla dokument som för närvarande övervakas i din instans. Innehållet i dessa dokument kommer att synkroniseras regelbundet.",
      "document-name": "Dokumentnamn",
      "last-synced": "Senast synkroniserad",
      "next-refresh": "Tid till nästa uppdatering",
      "created-on": "Skapad den",
      "auto-sync": "Automatisk innehållssynkronisering",
      "sync-description":
        'Aktivera möjligheten att specificera en innehållskälla som ska "övervakas". Övervakat innehåll kommer regelbundet att hämtas och uppdateras i denna instans.',
      "sync-workspace-note":
        "Övervakat innehåll kommer automatiskt att uppdateras i alla arbetsytor där de refereras.",
      "sync-limitation":
        "Denna funktion gäller endast webbaserat innehåll, såsom webbplatser, Confluence, YouTube och GitHub-filer.",
      documentation: "Funktionsdokumentation och varningar",
      "manage-content": "Hantera övervakat innehåll",
    },
    tos: {
      title: "Användarvillkor för experimentella funktioner",
      description:
        "Experimentella funktioner på denna plattform är funktioner som vi testar och som är valfria. Vi kommer proaktivt att informera om eventuella problem som kan uppstå innan godkännande av någon funktion.",
      "possibilities-title":
        "Användning av en funktion på denna sida kan resultera i, men är inte begränsat till, följande möjligheter:",
      possibilities: {
        "data-loss": "Förlust av data.",
        "quality-change": "Förändring i resultatens kvalitet.",
        "storage-increase": "Ökat lagringsutrymme.",
        "resource-consumption": "Ökad resursförbrukning.",
        "cost-increase":
          "Ökade kostnader eller användning av anslutna LLM- eller inbäddningsleverantörer.",
        "potential-bugs":
          "Potentiella buggar eller problem vid användning av denna applikation.",
      },
      "conditions-title":
        "Användning av en experimentell funktion kommer med följande icke-uttömmande lista över villkor:",
      conditions: {
        "future-updates":
          "Funktionen kanske inte finns i framtida uppdateringar.",
        stability: "Funktionen som används är för närvarande inte stabil.",
        availability:
          "Funktionen kanske inte är tillgänglig i framtida versioner, konfigurationer eller prenumerationer av denna instans.",
        privacy:
          "Dina sekretessinställningar kommer att respekteras vid användning av en betafunktion.",
        changes: "Dessa villkor kan ändras i framtida uppdateringar.",
      },
      "read-more": "Om du vill läsa mer kan du hänvisa till",
      contact: "eller kontakta",
      reject: "Avvisa & Stäng",
      accept: "Jag förstår",
    },
    "update-failed": "Misslyckades att uppdatera funktionsstatus",
  },
};
