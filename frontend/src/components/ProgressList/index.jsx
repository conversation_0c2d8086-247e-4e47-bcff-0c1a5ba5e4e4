import { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { LuChevronDown, LuLoaderCircle } from "react-icons/lu";
import { IoIosCheckmarkCircle } from "react-icons/io";
import useThreadProgress from "@/hooks/useThreadProgress";

const ProgressList = ({ threadSlug }) => {
  const { t } = useTranslation();
  const { isActive, currentStep, totalSteps, flowType, stepDetails } =
    useThreadProgress(threadSlug);

  const [expandedSteps, setExpandedSteps] = useState(new Set());

  const toggleStepExpansion = (stepNumber) => {
    setExpandedSteps((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(stepNumber)) {
        newSet.delete(stepNumber);
      } else {
        newSet.add(stepNumber);
      }
      return newSet;
    });
  };

  const allSteps = useMemo(() => {
    const steps = [];
    for (let i = 1; i <= totalSteps; i++) {
      // Find step detail from backend data
      const stepDetail = stepDetails.find((detail) => detail.step === i);

      const stepStatus =
        stepDetail?.status ||
        (i < currentStep
          ? "complete"
          : i === currentStep && isActive
            ? "in_progress"
            : i === currentStep && !isActive
              ? "complete"
              : "pending");

      let stepLabel = `${t("chatProgress.step")} ${i}`;
      if (flowType) {
        const stepKey = `chatProgress.types.${flowType}.step${i}`;
        const translatedLabel = t(`${stepKey}.label`, { defaultValue: "" });
        if (translatedLabel) {
          stepLabel = translatedLabel;
        }
      }

      // Use backend message if available
      if (stepDetail?.message) {
        stepLabel = stepDetail.message;
      }

      steps.push({
        step: i,
        label: stepLabel,
        status: stepStatus,
        subTasks: stepDetail?.subTasks || [],
        hasSubTasks: stepDetail?.subTasks && stepDetail.subTasks.length > 0,
      });
    }
    return steps;
  }, [totalSteps, currentStep, isActive, flowType, stepDetails, t]);

  const getStatusIcon = (status, step) => {
    switch (status) {
      case "complete":
        return <IoIosCheckmarkCircle className="text-green-500 size-5" />;
      case "starting":
      case "in_progress":
        return (
          <LuLoaderCircle className="animate-spin text-foreground size-5" />
        );
      default:
        return (
          <div className="size-5 bg-secondary rounded-md flex items-center justify-center text-xs font-medium text-foreground">
            {step.step}
          </div>
        );
    }
  };

  if (!threadSlug) {
    return (
      <div className="text-center text-muted py-8">
        {t("chatProgress.noThreadSelected")}
      </div>
    );
  }

  if (!isActive && currentStep === 1) {
    return (
      <div className="text-center text-muted py-8">
        {t("chatProgress.noActiveProgress")}
      </div>
    );
  }

  return (
    <div className="space-y-2 overflow-y-auto max-h-[32rem]">
      {allSteps.map((step) => {
        const isExpanded = expandedSteps.has(step.step);

        return (
          <div key={step.step} className="space-y-1">
            <div
              className={`flex items-start gap-2 p-3 rounded-lg transition-colors`}
            >
              <div className="flex-shrink-0 pt-0.5">
                {getStatusIcon(step.status, step)}
              </div>

              <div className="flex-1">
                <div
                  className={`flex items-center gap-2 ${
                    step.hasSubTasks ? "cursor-pointer transition-colors" : ""
                  }`}
                  onClick={
                    step.hasSubTasks
                      ? () => toggleStepExpansion(step.step)
                      : undefined
                  }
                >
                  <div
                    className={`font-medium ${
                      step.status === "in_progress" ||
                      step.status === "complete"
                        ? "text-foreground"
                        : "text-muted"
                    } ${step.hasSubTasks ? "hover:underline" : ""}`}
                  >
                    {step.label}
                  </div>

                  {step.hasSubTasks && (
                    <LuChevronDown
                      className={`shrink-0 size-5 ${isExpanded ? "-rotate-90" : ""} transition-transform`}
                    />
                  )}
                </div>
              </div>
            </div>

            {step.hasSubTasks && isExpanded && (
              <div className="ml-8 space-y-2">
                {step.subTasks.map((subTask) => (
                  <div
                    key={subTask.subStep}
                    className="flex items-center gap-3 px-2 py-1 rounded"
                  >
                    <div className="flex-1">
                      <div className="text-sm font-medium text-foreground">
                        {subTask.message ||
                          t("cdbProgress.general.placeholderSubTask", {
                            index: subTask.subStep,
                          })}
                      </div>
                      {(subTask.status === "starting" ||
                        subTask.status === "in_progress") && (
                        <div className="text-xs text-muted">
                          {t("chatProgress.processing")}...
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default ProgressList;
