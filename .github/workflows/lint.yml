name: <PERSON>t Check

on:
  pull_request:
    branches:
      - develop
      - main
      - main-stage
      - main-dev

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Lint server code
        working-directory: server
        run: |
          npm install
          npm run lint
      - name: Lint frontend code
        working-directory: frontend
        run: |
          npm install
          npm run lint
