import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";
import { useTranslation } from "react-i18next";

const FeatureUnavailableModal = ({ isOpen, onClose }) => {
  const { t } = useTranslation();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t("workspaceSelector.featureUnavailable.title")}
      footer={
        <Button onClick={onClose}>
          {t("workspaceSelector.featureUnavailable.close")}
        </Button>
      }
    >
      <p>{t("workspaceSelector.featureUnavailable.description")}</p>
    </Modal>
  );
};

export default FeatureUnavailableModal;
