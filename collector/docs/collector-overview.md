# Collector Service Overview

## Introduction

The Collector service is a critical component of the ISTLegal system responsible for ingesting and processing documents from various sources. It converts different file types into a standardized format that can be embedded and used for Retrieval-Augmented Generation (RAG).

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Key Components](#key-components)
3. [Supported File Types](#supported-file-types)
4. [Processing Flow](#processing-flow)
5. [Extension System](#extension-system)
6. [Integration with Server](#integration-with-server)
7. [Configuration](#configuration)

## Architecture Overview

The Collector service is designed as a standalone service that communicates with the main server. It follows a modular architecture with specialized converters for different file types and sources.

```
collector/
├── index.js                 # Main entry point
├── processSingleFile/       # File processing logic
│   ├── index.js             # Orchestration
│   └── convert/             # File type converters
├── processLink/             # Link processing logic
│   ├── index.js             # Orchestration
│   └── convert/             # Link type converters
├── processRawText/          # Raw text processing
│   └── index.js             # Text processing logic
├── extensions/              # Extension system
│   ├── index.js             # Extension registration
│   ├── github/              # GitHub connector
│   ├── youtube/             # YouTube connector
│   └── resync/              # Resync functionality
└── utils/                   # Shared utilities
    ├── files/               # File operations
    ├── tokenizer/           # Text tokenization
    ├── comKey/              # Communication key
    └── EncryptionWorker/    # Encryption utilities
```

## Key Components

### 1. File Processing (`processSingleFile/`)

The file processing module handles the conversion of uploaded files into a standardized text format:

- **Orchestration** (`index.js`): Manages the overall file processing flow
- **Converters** (`convert/*.js`): Specialized converters for different file types
- **Chunking**: Splits large documents into manageable chunks
- **Metadata Extraction**: Extracts and preserves metadata from files

### 2. Link Processing (`processLink/`)

The link processing module handles the extraction of content from external URLs:

- **Orchestration** (`index.js`): Manages the link processing flow
- **Converters** (`convert/*.js`): Specialized converters for different link types
- **Web Scraping**: Extracts content from web pages
- **API Integration**: Connects to external APIs for content retrieval

### 3. Raw Text Processing (`processRawText/`)

The raw text processing module handles direct text input:

- **Text Processing** (`index.js`): Processes raw text input
- **Formatting**: Applies consistent formatting to raw text
- **Chunking**: Splits large text inputs into manageable chunks

### 4. Extension System (`extensions/`)

The extension system provides specialized connectors for different data sources:

- **GitHub** (`github/`): Processes GitHub repositories and files
- **YouTube** (`youtube/`): Processes YouTube video transcripts
- **Resync** (`resync/`): Handles document resyncing for watched sources

### 5. Utilities (`utils/`)

The utilities module provides shared functionality across the Collector service:

- **File Operations** (`files/`): File reading, writing, and manipulation
- **Tokenization** (`tokenizer/`): Text tokenization for chunk management
- **Communication Key** (`comKey/`): Secure communication with the server
- **Encryption** (`EncryptionWorker/`): Data encryption and decryption

## Supported File Types

The Collector service supports a wide range of file types:

### Document Files

- PDF (`.pdf`)
- Microsoft Word (`.docx`, `.doc`)
- Microsoft Excel (`.xlsx`, `.xls`)
- Microsoft PowerPoint (`.pptx`, `.ppt`)
- Text files (`.txt`, `.md`, `.csv`)
- Rich Text Format (`.rtf`)
- HTML (`.html`, `.htm`)
- XML (`.xml`)
- JSON (`.json`)
- EPUB (`.epub`)

### Email Files

- MBOX (`.mbox`)
- EML (`.eml`)

### Media Files

- Audio (`.mp3`, `.wav`, `.m4a`, `.flac`) - transcribed to text
- Video (`.mp4`, `.avi`, `.mov`) - audio extracted and transcribed

### Code Files

- Various programming languages (`.js`, `.py`, `.java`, `.cpp`, etc.)

### Archive Files

- ZIP (`.zip`) - contents are extracted and processed
- TAR (`.tar`, `.tar.gz`, `.tgz`) - contents are extracted and processed

## Processing Flow

The document processing flow follows these steps:

1. **Request Validation**

   - Verify request integrity
   - Validate file type and size
   - Check for required parameters

2. **File Conversion**

   - Select appropriate converter based on file type
   - Extract text content from the file
   - Preserve document structure when possible
   - Extract metadata (title, author, creation date, etc.)

3. **Text Processing**

   - Clean and normalize text
   - Remove irrelevant content (e.g., headers, footers)
   - Handle special characters and encoding issues

4. **Chunking**

   - Split large documents into manageable chunks
   - Maintain context across chunk boundaries
   - Balance chunk size for optimal embedding

5. **Metadata Enrichment**

   - Add processing metadata (timestamp, processor version, etc.)
   - Calculate token counts for each chunk
   - Generate unique identifiers for chunks

6. **Output Generation**
   - Create standardized JSON output
   - Include both content and metadata
   - Prepare for storage and embedding

## Extension System

The extension system allows the Collector to process specialized data sources:

### GitHub Extension

Processes GitHub repositories and files:

- Clone repositories
- Extract file content
- Process markdown files
- Handle code files with appropriate syntax highlighting
- Support for specific branches and directories

### YouTube Extension

Processes YouTube video transcripts:

- Extract video metadata
- Retrieve transcripts (auto-generated or manual)
- Process transcripts into text chunks
- Preserve timestamp information

### Resync Extension

Handles document resyncing for watched sources:

- Check for updates to previously processed sources
- Process only changed content
- Maintain version history
- Support for scheduled resyncing

## Integration with Server

The Collector service integrates with the main server through:

1. **API Endpoints**

   - `/process`: Process a single file
   - `/process-link`: Process content from a URL
   - `/process-text`: Process raw text input
   - `/extensions/*`: Specialized extension endpoints

2. **Communication Protocol**

   - REST API for command and control
   - Secure communication using shared keys
   - File transfer via multipart/form-data
   - JSON response format

3. **Error Handling**
   - Standardized error responses
   - Detailed error information for debugging
   - Graceful failure handling

## Configuration

The Collector service is configured through:

1. **Environment Variables**

   - `PORT`: Service port (default: 3005)
   - `HOST`: Service host (default: localhost)
   - `MAX_FILE_SIZE`: Maximum file size in bytes
   - `TEMP_DIR`: Directory for temporary files
   - `OUTPUT_DIR`: Directory for processed output
   - `LOG_LEVEL`: Logging verbosity

2. **Runtime Configuration**

   - Converter-specific settings
   - Extension-specific settings
   - Processing parameters

3. **Security Configuration**
   - Communication keys
   - Encryption settings
   - Access control
