export default {
  // =========================
  // DATA CONNECTORS
  // =========================
  dataConnectors: {
    github: {
      name: "GitHub-repo",
      description:
        "Importer et helt offentlig eller privat GitHub-repositorium med ett klikk.",
      url: "GitHub-repo URL",
      "collect-url": "URL til GitHub-repoen du ønsker å hente.",
      "access-token": "GitHub tilgangstoken",
      optional: "valgfritt",
      "rate-limiting": "Tilgangstoken for å unngå rate limiting.",
      "desc-picker":
        "Når fullført vil alle filer være tilgjengelige for embedding i arbeidsområder i dokumentvelgeren.",
      branch: "Gren",
      "branch-desc": "Grenen du ønsker å hente filer fra.",
      "branch-loading": "-- laster tilgjengelige grener --",
      "desc-start": "Uten å fylle ut",
      "desc-token": "GitHub tilgangstoken",
      "desc-connector": "vil denne datakoblingen kun kunne hente",
      "desc-level": "topplevelds",
      "desc-end":
        "filer fra repoen på grunn av GitHubs offentlige API rate limits.",
      "personal-token":
        "Få en gratis personlig tilgangstoken med en GitHub-konto her.",
      without: "Uten en",
      "personal-token-access": "personlig tilgangstoken",
      "desc-api":
        ", kan GitHub API begrense antall filer som kan hentes på grunn av rate limits. Du kan",
      "temp-token": "opprette en midlertidig tilgangstoken",
      "avoid-issue": "for å unngå dette problemet.",
      submit: "Send",
      "collecting-files": "Henter filer...",
    },
    "youtube-transcript": {
      name: "YouTube-transkripsjon",
      description:
        "Importer transkripsjonen av en hel YouTube-video fra en lenke.",
      url: "YouTube Video URL",
      "url-video": "URL til YouTube-videoen du ønsker å transkribere.",
      collect: "Hent transkripsjon",
      collecting: "Henter transkripsjon...",
      "desc-end":
        "når fullført vil transkripsjonen være tilgjengelig for embedding i arbeidsområder i dokumentvelgeren.",
    },
    "website-depth": {
      name: "Bulk link-skrabber",
      description:
        "Skrap et nettsted og dets underlenker opp til en viss dybde.",
      url: "Nettsteds URL",
      "url-scrape": "URL til nettstedet du ønsker å skrape.",
      depth: "Dybde",
      "child-links":
        "Dette er antallet underlenker som skrabberen skal følge fra den opprinnelige URL-en.",
      "max-links": "Maks antall lenker",
      "links-scrape": "Maks antall lenker å skrape.",
      scraping: "Skraper nettsted...",
      submit: "Send",
      "desc-scrap":
        "Når fullført vil alle skrapede sider være tilgjengelige for embedding i arbeidsområder i dokumentvelgeren.",
    },
    confluence: {
      name: "Confluence",
      description: "Importer en hel Confluence-side med ett klikk.",
      url: "Confluence side URL",
      "url-page": "URL til en side i Confluence-plassen.",
      username: "Confluence brukernavn",
      "own-username": "Ditt Confluence-brukernavn.",
      token: "Confluence tilgangstoken",
      "desc-start":
        "Du må oppgi en tilgangstoken for autentisering. Du kan generere en tilgangstoken",
      here: "her",
      access: "Tilgangstoken for autentisering.",
      collecting: "Henter sider...",
      submit: "Send",
      "desc-end":
        "Når fullført vil alle sider være tilgjengelige for embedding i arbeidsområder.",
    },
  },
  // =========================
  // CONFLUENCE CONNECTOR COMPONENT
  // =========================
  confluence: {
    "space-key": "Confluence space-nøkkel",
    "space-key-desc":
      "Dette er nøkkelen til plassen i din Confluence-instans som vil bli brukt. Begynner vanligvis med ~",
    "space-key-placeholder": "f.eks.: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "f.eks.: https://eksempel.atlassian.net, http://localhost:8211, etc...",
    "token-tooltip": "Du kan opprette en API-token",
    "token-tooltip-here": "her",
  },
};
