export default {
  // =========================
  // DOCUMENT BUILDER SETTINGS
  // =========================

  // Document Builder Page
  "document-builder": {
    title: "Dokumentbyggare-prompter",
    description:
      "Anpassa standardprompter som används av Dokumentbyggare-funktionen.",
    "override-prompt-placeholder":
      "Ange prompt för att åsidosätta standard systemprompten",
    saving: "Sparar...",
    save: "Spara Prompt-inställningar",
    "toast-success": "Dokumentbyggare-prompter sparade framgångsrikt.",
    "toast-fail": "Misslyckades att spara dokumentbyggare-prompter.",

    // =========================
    // LEGAL DRAFTING PROMPTS
    // =========================
    prompts: {
      group: {
        document_summary: {
          title: "Prompter för <PERSON>",
          description:
            "Konfigurera system- och användarprompter för Dokumentsammanfattning.",
        },
        document_relevance: {
          title: "Prompter för Dokumentrelevans",
          description:
            "Konfigurera system- och användarprompter för Dokumentrelevans.",
        },
        section_drafting: {
          title: "Prompter för Sektionsutkast",
          description:
            "Konfigurera system- och användarprompter för Sektionsutkast.",
        },
        section_legal_issues: {
          title: "Prompter för Juridiska Frågor i Sektioner",
          description:
            "Konfigurera system- och användarprompter för Juridiska Frågor i Sektioner.",
        },
        memo_creation: {
          title: "Prompter för PM-skapande",
          description: "Konfigurera prompter för PM-skapande.",
        },
        section_index: {
          title: "Prompter för Sektionsindex",
          description: "Konfigurera prompter för Sektionsindex.",
        },
        select_main_document: {
          title: "Prompter för Val av Huvuddokument",
          description:
            "Konfigurera system- och användarprompter för Val av Huvuddokument.",
        },
        section_list_from_main: {
          title: "Prompter för Sektionslista från Huvuddokument",
          description:
            "Konfigurera system- och användarprompter för Sektionslista från Huvuddokument.",
        },
        section_list_from_summaries: {
          title: "Sektionslista från Sammanfattningar Prompter",
          description:
            "Konfigurera system- och användarprompts för Sektionslista från Sammanfattningar.",
        },
        reference_files_description: {
          title: "Referensfiler Beskrivning Prompter",
          description:
            "Konfigurera system- och användarprompts för Referensfiler Beskrivning.",
        },
        review_files_description: {
          title: "Granskningsfiler Beskrivning Prompter",
          description:
            "Konfigurera system- och användarprompts för Granskningsfiler Beskrivning.",
        },
        reference_review_sections: {
          title: "Referens-/Granskningssektioner Prompter",
          description:
            "Konfigurera system- och användarprompts för Referens-/Granskningssektioner.",
        },
      },
      // Document Summary
      "document-summary-system-label": "Dokumentsammanfattning (System)",
      "document-summary-system-description":
        "Systemprompt för att instruera AI om hur man sammanfattar ett dokuments innehåll och relevans för en juridisk uppgift.",
      "document-summary-user-label": "Dokumentsammanfattning (Användare)",
      "document-summary-user-description":
        "Användarpromptmall för att generera en detaljerad sammanfattning av dokumentinnehåll i relation till en specifik juridisk uppgift.",

      // Reference Files Description Prompts
      "reference-files-description-system-label":
        "Referensfiler Beskrivning (System)",
      "reference-files-description-system-description":
        "Systemprompt för att beskriva referensfiler.",
      "reference-files-description-user-label":
        "Referensfiler Beskrivning (Användare)",
      "reference-files-description-user-description":
        "Användarprompter mall för att beskriva referensfiler.",

      // Document Relevance
      "document-relevance-system-label": "Dokumentrelevans (System)",
      "document-relevance-system-description":
        "Systemprompt för att utvärdera om ett dokument är relevant för en juridisk uppgift, förväntar sig ett sant/falskt svar.",
      "document-relevance-user-label": "Dokumentrelevans (Användare)",
      "document-relevance-user-description":
        "Användarpromptmall för att kontrollera om dokumentinnehåll är relevant för en given juridisk uppgift.",

      // Section Drafting
      "section-drafting-system-label": "Sektionsutkast (System)",
      "section-drafting-system-description":
        "Systemprompt för att generera en enskild dokumentsektion i professionell juridisk stil med hjälp av specificerade dokument och kontext.",
      "section-drafting-user-label": "Sektionsutkast (Användare)",
      "section-drafting-user-description":
        "Användarpromptmall för att generera en specifik sektion av ett juridiskt dokument, med hänsyn till titel, uppgift, källdokument och angränsande sektioner.",

      // Section Legal Issues
      "section-legal-issues-system-label":
        "Identifiering av Juridiska Frågor för Sektion (System)",
      "section-legal-issues-system-description":
        "Systemprompt för att identifiera specifika juridiska ämnen för vilka faktainformation bör hämtas för att stödja utarbetandet av en dokumentsektion.",

      "section-legal-issues-user-label":
        "Identifiering av Juridiska Frågor för Sektion (Användare)",
      "section-legal-issues-user-description":
        "Användarpromptmall för att lista juridiska ämnen eller datapunkter för att hämta kontextuell information relevant för en specifik dokumentsektion och juridisk uppgift.",

      // Memo Creation
      "memo-creation-template-label": "Standard PM-skapandemall",
      "memo-creation-template-description":
        "Promptmall för att skapa ett juridiskt memorandum som adresserar en specifik juridisk fråga, med hänsyn till de medföljande dokumenten och uppgiftskontexten.",
      // Section Index
      "section-index-system-label": "Sektionsindex (System)",
      "section-index-system-description":
        "Systemprompt för att generera ett strukturerat index av sektioner för ett juridiskt dokument.",

      // Select Main Document
      "select-main-document-system-label": "Val av Huvuddokument (System)",
      "select-main-document-system-description":
        "Systemprompt för att identifiera det mest relevanta huvuddokumentet för en juridisk uppgift från flera dokumentsammanfattningar.",
      "select-main-document-user-label": "Val av Huvuddokument (Användare)",
      "select-main-document-user-description":
        "Användarpromptmall för att identifiera huvuddokumentet för en juridisk uppgift baserat på sammanfattningar av flera dokument.",

      // Section List from Main Document
      "section-list-from-main-system-label":
        "Sektionslista från Huvuddokument (System)",
      "section-list-from-main-system-description":
        "Systemprompt för att skapa en JSON-strukturerad lista över sektioner för ett juridiskt dokument baserat på huvuddokumentets innehåll och den juridiska uppgiften.",
      "section-list-from-main-user-label":
        "Sektionslista från Huvuddokument (Användare)",
      "section-list-from-main-user-description":
        "Användarpromptmall för att tillhandahålla den juridiska uppgiften och huvuddokumentets innehåll för att generera en sektionslista.",

      // Section List from Summaries
      "section-list-from-summaries-system-label":
        "Sektionslista från Sammanfattningar (System)",
      "section-list-from-summaries-system-description":
        "Systemprompt för att skapa en JSON-strukturerad lista över sektioner baserat på dokumentsammanfattningar och den juridiska uppgiften, när inget huvuddokument finns.",
      "section-list-from-summaries-user-label":
        "Sektionslista från Sammanfattningar (Användare)",
      "section-list-from-summaries-user-description":
        "Användarpromptmall för att tillhandahålla den juridiska uppgiften och dokumentsammanfattningar för att generera en sektionslista, när inget huvuddokument finns.",

      // Review Files Description Prompts
      "review-files-description-system-label":
        "Granskningsfiler Beskrivning (System)",
      "review-files-description-system-description":
        "Systemprompt för att beskriva granskningsfiler.",
      "review-files-description-user-label":
        "Granskningsfiler Beskrivning (Användare)",
      "review-files-description-user-description":
        "Användarprompter mall för att beskriva granskningsfiler.",

      // Reference/Review Sections Prompts
      "reference-review-sections-system-label":
        "Referens-/Granskningssektioner (System)",
      "reference-review-sections-system-description":
        "Systemprompt för att definiera referens-/granskningssektioner.",
      "reference-review-sections-user-label":
        "Referens-/Granskningssektioner (Användare)",
      "reference-review-sections-user-description":
        "Användarprompter mall för att definiera referens-/granskningssektioner.",
    },

    // =========================
    // ADD/EDIT LEGAL TASK KEYS
    // =========================
    "view-categories": "Visa alla kategorier",
    "hide-categories": "Dölj listan",
    "add-task": "Lägg till juridisk uppgift",
    loading: "Laddar...",
    table: {
      title: "Juridiska uppgifter",
      name: "Namn",
      "sub-category": "Underkategori",
      description: "Beskrivning",
      prompt: "Juridisk uppgiftsprompt",
      actions: "Åtgärder",
      delete: "Radera",
      "delete-confirm": "Är du säker på att du vill radera denna kategori?",
      "delete-success": "Kategorin har raderats",
      "delete-error": "Kunde inte radera kategorin",
    },
    "create-task-title": "Skapa en juridisk uppgift",
    "category-name": "Kategorinamn",
    "category-name-desc": "Ange huvudkategorins namn.",
    "category-name-placeholder": "Ange kategorinamn",
    "subcategory-name": "Underkategorinamn",
    "subcategory-name-desc": "Ange namnet på underkategorin.",
    "subcategory-name-placeholder": "Ange underkategorinamn",
    "description-desc": "Ge en beskrivning av kategorin och underkategorin.",
    "description-placeholder": "Ange en kort beskrivning",
    submitting: "Skickar...",
    submit: "Skicka",
    validation: {
      "category-required": "Kategorinamn krävs.",
      "subcategory-required": "Underkategorinamn krävs.",
      "description-required": "Beskrivning krävs.",
      "prompt-required": "Prompt för juridisk uppgift krävs.",
    },
    "create-task": {
      title: "Skapa juridisk uppgift",
      category: {
        name: "Kategorinamn",
        desc: "Ange kategorins namn.",
        placeholder: "Ange kategorinamn",
        type: "Kategorityp",
        new: "Skapa ny kategori",
        existing: "Använd befintlig kategori",
        select: "Välj kategori",
        "select-placeholder": "Välj en befintlig kategori",
      },
      subcategory: {
        name: "Namn på juridisk uppgift",
        desc: "Ange namnet på den juridiska uppgiften.",
        placeholder: "Ange namn på juridisk uppgift",
      },
      description: {
        name: "Beskrivning och användarinstruktioner",
        desc: "Informationen och instruktionerna som användaren kommer att se.",
        placeholder:
          "Beskriv t.ex. vilka slags dokument som behöver laddas upp i arbetsytan för att utfallet ska bli så bra som möjligt",
      },
      prompt: {
        name: "Juridisk uppgiftsprompt",
        desc: "Ange prompten som ska användas för denna juridiska uppgift. Du kan också ladda upp exempeldokument med knapparna för att lägga till innehållsexempel i din prompt.",
        placeholder:
          "Ange juridisk uppgiftsprompt eller ladda upp exempel dokument för att förbättra din prompt...",
      },
      submitting: "Skickar...",
      submit: "Skicka",
      validation: {
        "category-required": "Kategorinamn krävs.",
        "subcategory-required": "Namn på juridisk uppgift krävs.",
        "description-required": "Beskrivning krävs.",
        "prompt-required": "Juridisk uppgiftsprompt krävs.",
        "legal-task-type-required": "Typ av juridisk uppgift krävs.",
      },
    },
    "edit-task": {
      title: "Redigera juridisk uppgift",
      submitting: "Uppdaterar...",
      submit: "Uppdatera uppgift",
      subcategory: {
        name: "Namn på juridisk uppgift",
        desc: "Ange ett nytt namn för denna juridiska uppgift",
        placeholder: "Ange juridisk uppgift...",
      },
      description: {
        name: "Beskrivning och användarinstruktioner",
        desc: "Ange beskrivning och användarinstruktioner för denna juridiska uppgift",
        placeholder: "Ange beskrivning och användarinstruktioner...",
      },
      prompt: {
        name: "Juridisk uppgiftsprompt",
        desc: "Ange prompten som ska användas för denna juridiska uppgift. Du kan också ladda upp exempeldokument med knapparna för att lägga till innehållsexempel i din prompt.",
        placeholder:
          "Ange juridisk uppgiftsprompt eller ladda upp exempel dokument för att förbättra din prompt...",
      },
      validation: {
        "subcategory-required": "Namn på juridisk uppgift krävs",
        "description-required": "Beskrivning krävs",
        "prompt-required": "Juridisk uppgiftsprompt krävs",
        "legal-task-type-required": "Typ av juridisk uppgift krävs",
      },
    },
    "task-form": {
      "requires-main-doc-label": "Val av huvuddokument krävs",
      "requires-main-doc-description":
        "Om markerad måste användaren välja huvuddokumentet från de uppladdade filerna när denna uppgift utförs. Detta rekommenderas starkt för juridiska uppgifter som involverar svar på ett brev eller en domstolsinlaga eller liknande, eftersom det strukturerar resultatet baserat på det dokument som besvaras.",
      "requires-main-doc-placeholder": "Ja eller Nej",
      "requires-main-doc-explanation-default":
        "Ett val behövs eftersom detta avgör hur dokumentet kommer att byggas.",
      "requires-main-doc-explanation-yes":
        "Om 'Ja' kommer användaren att behöva välja ett huvuddokument när denna juridiska uppgift påbörjas. Detta dokument kommer att vara centralt för uppgiftens arbetsflöde.",
      "requires-main-doc-explanation-no":
        "Om 'Nej' kommer den juridiska uppgiften att fortsätta utan att kräva ett förvalt huvuddokument. Uppgiften kommer att skapa resultat mer dynamiskt baserat på alla uppladdade dokument och den juridiska uppgiften.",
      "legal-task-type-label": "Typ av juridisk uppgift",
      "legal-task-type-placeholder": "Välj typ av juridisk uppgift",
      option: {
        mainDoc: "Huvuddokumentflöde",
        noMainDoc: "Inget huvuddokumentflöde",
        referenceFiles: "Jämförelse av referensfiler",
      },
      "legal-task-type-explanation":
        "Välj hur den juridiska uppgiften ska hantera dokument.",
      "legal-task-type-explanation-mainDoc":
        "Detta flöde kräver att du väljer ett huvuddokument innan du fortsätter.",
      "legal-task-type-explanation-noMainDoc":
        "Detta flöde fortsätter utan ett primärt dokument.",
      "legal-task-type-explanation-referenceFiles":
        "Detta flöde bearbetar grupper av regler och förordningar mot granskningsdokument.",
    },

    // New keys for Review Generator Prompt feature
    reviewGeneratorPromptButton: "Granska Generatorprompt",
    reviewGeneratorPromptButtonTooltip:
      "Visa den exakta promptmallen som användes för att generera förslaget för juridisk uppgift. (Endast admin)",
    reviewGeneratorPromptTitle: "Granskning av Generatorprompt",
    reviewPromptLabel: "Följande prompt användes för generering:",
    reviewPromptTextareaLabel: "Innehåll i Generatorprompt",
  },

  // =========================
  // PERFORM LEGAL TASK
  // =========================
  performLegalTask: {
    title: "Utför juridisk uppgift",
    noTaskfund: "Inga juridiska uppgifter tillgängliga.",
    noSubtskfund: "Inga underkategorier tillgängliga.",
    "loading-subcategory": "Laddar underkategorier...",
    "select-category": "Välj kategori",
    "choose-task": "Välj juridisk uppgift att utföra",
    "duration-info":
      "Tiden för att utföra en juridisk uppgift beror på antalet dokument i arbetsytan. Med många dokument och en komplex uppgift kan detta ta mycket lång tid.",
    description:
      "Aktivera eller inaktivera knappen för att utföra juridisk uppgift i dokumentutkast.",
    successMessage: "Funktionen för juridisk uppgift har {{status}}",
    failureUpdateMessage:
      "Misslyckades att uppdatera inställning för juridisk uppgift.",
    errorSubmitting:
      "Fel vid uppdatering av inställningar för juridisk uppgift.",
    "additional-instructions-label": "Ytterligare instruktioner:",
    "custom-instructions-label": "Anpassade instruktioner",
    "custom-instructions-placeholder":
      "Ange ytterligare instruktioner för den juridiska uppgiften (valfritt)...",
    "select-main-document-label": "Välj huvuddokument (krävs)",
    "select-document-placeholder": "-- Välj ett dokument --",
    selectReferenceFilesLabel: "Välj referensfiler (regler och förordningar)",
    "warning-title": "Varning",
    "no-files-title": "Inga filer tillgängliga",
    "no-files-description":
      "Det finns inga filer i denna arbetsyta. Vänligen ladda upp minst en fil innan du utför en juridisk uppgift.",
    "settings-button":
      "Lägg till eller redigera tillgängliga juridiska uppgifter",
    settings: "Inställningar för Juridiska Uppgifter",
    subStep: "Pågående eller köat steg",
  },

  // =========================
  // LEGAL TASK PROMPT GENERATOR
  // =========================
  "legal-task-prompt-generator": {
    title: "Generator för användarprompt",
    description:
      "Automatiskt förslag på anpassad prompt för en juridisk uppgift",
    "task-description": "Beskrivning av juridisk uppgift",
    "task-description-placeholder":
      "Beskriv den juridiska uppgift du vill utföra...",
    "specific-instructions": "Specifika instruktioner eller expertis",
    "specific-instructions-description":
      "Inkludera alla specifika instruktioner eller kunskap som är relevant för denna juridiska uppgift",
    "specific-instructions-placeholder":
      "Lägg till specifika instruktioner, expertis eller know-how för att hantera denna juridiska uppgift...",
    "suggested-prompt": "Föreslagen användarprompt",
    "generation-prompt": "Prompt för generering",
    "create-task": "Skapa juridisk uppgift baserad på detta förslag",
    generating: "Genererar...",
    generate: "Generera förslag",
    "toast-success": "Prompt genererad framgångsrikt",
    "toast-fail": "Misslyckades att generera prompt",
    button: "Generera prompt",
    success: "Prompt genererad framgångsrikt",
    error: "Ange först ett namn eller en underkategori",
    failed: "Misslyckades att generera prompt",
  },
};
