import { Tooltip } from "react-tooltip";
import { FaStop } from "react-icons/fa";
import { useTranslation } from "react-i18next";
import useStreamAbortStore from "@/stores/useStreamAbortStore";

export default function StopGenerationButton() {
  const { t } = useTranslation();
  const requestAbort = useStreamAbortStore((state) => state.requestAbort);

  function handleStopGeneration() {
    requestAbort();
  }

  return (
    <>
      <button
        type="button"
        onClick={handleStopGeneration}
        data-tooltip-id="stop-generation-button"
        data-tooltip-content={t("common.stop-response")}
        className="bg-primary rounded-full text-background p-3"
        aria-label={t("common.stop-generating")}
      >
        <FaStop size={13} />
      </button>
      <Tooltip
        id="stop-generation-button"
        delayShow={300}
        className="tooltip"
      />
    </>
  );
}
