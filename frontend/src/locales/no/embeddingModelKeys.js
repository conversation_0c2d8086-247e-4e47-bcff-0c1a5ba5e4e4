export default {
  // =========================
  // EMBEDDER (EMBEDDING PROVIDER NAMES)
  // =========================
  embeder: {
    allm: "Bruk den innebygde innebyggingsleverandøren. Ingen oppsett!",
    openai: "Standardvalg for de fleste ikke-kommersielle bruksområder.",
    azure: "Enterprise-valg av OpenAI som er værende på Azure-tjenestene.",
    localai: "Kjør innebyggingsmodeller lokalt på din egen maskin.",
    ollama: "Kjør innebyggingsmodeller lokalt på din egen maskin.",
    lmstudio:
      "Oppdag, laste ned og kjør tusenvis av avanserte LLMs med ett klikk.",
    cohere: "Kjør kraftige innebyggingsmodeller fra Cohere.",
    voyageai: "Kjør kraftige innebyggingsmodeller fra Voyage AI.",
    "generic-openai": "Bruk et generelt OpenAI-innebyggingsmodell.",
    "default.embedder": "Standardinnebyggingsleverandør",
    jina: "Jina AI's tekstinnebyggingsmodeller for flerspråklige og høy ytelsesinnebygginger.",
    litellm: "Kjør kraftige innebyggingsmodeller fra LiteLLM.",
  },
  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Common strings
    "provider-logo": "{{provider}} logo",

    // LMStudio Embedding Options
    lmstudio: {
      "model-label": "LM Studio-innbyggingsmodell",
      "max-chunk-length": "Maksimal segmentlengde",
      "max-chunk-length-help":
        "Maksimal lengde på tekstsegmenter for innebygging.",
      "hide-endpoint": "Skjul manuell inndata for endepunkt",
      "show-endpoint": "Vis manuell inndata for endepunkt",
      "base-url": "LM Studio Base-URL",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help": "Skriv inn URL-en der LM Studio kjører.",
      "auto-detect": "Automatisk oppdag",
      "loading-models": "--laster tilgjengelige modeller--",
      "enter-url-first": "Skriv inn LM Studio-URL først",
      "model-help":
        "Velg LM Studio-modellen for innebygging. Modellene lastes etter at du har angitt en gyldig LM Studio-URL.",
      "loaded-models": "Dine lastede modeller",
    },
    // Ollama Embedding Options
    ollama: {
      "model-label": "Ollama-innbyggingsmodell",
      "max-chunk-length": "Maksimal segmentlengde",
      "max-chunk-length-help":
        "Maksimal lengde på tekstsegmenter for innebygging.",
      "hide-endpoint": "Skjul manuell inndata for endepunkt",
      "show-endpoint": "Vis manuell inndata for endepunkt",
      "base-url": "Ollama Base-URL",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help": "Skriv inn URL-en der Ollama kjører.",
      "auto-detect": "Automatisk oppdag",
      "loading-models": "--laster tilgjengelige modeller--",
      "enter-url-first": "Skriv inn Ollama-URL først",
      "model-help":
        "Velg Ollama-modellen for innebygging. Modellene lastes etter at du har angitt en gyldig Ollama-URL.",
      "loaded-models": "Dine lastede modeller",
    },
    // LiteLLM Embedding Options
    litellm: {
      "model-label": "Valg av innebyggingsmodell",
      "max-chunk-length": "Maksimal segmentlengde",
      "max-chunk-length-help":
        "Maksimal lengde på tekstsegmenter for innebygging.",
      "api-key": "API-nøkkel",
      optional: "valgfritt",
      "api-key-placeholder": "sk-mysecretkey",
      "loading-models": "-- laster tilgjengelige modeller --",
      "waiting-url": "-- venter på URL --",
      "loaded-models": "Dine lastede modeller",
      "model-tooltip": "Se støttede innebyggingsmodeller på",
      "model-tooltip-link": "LiteLLM-dokumentasjon",
      "model-tooltip-more": "for mer informasjon om tilgjengelige modeller.",
    },
    // Cohere Embedding Options
    cohere: {
      "api-key": "Cohere API-nøkkel",
      "api-key-placeholder": "Skriv inn din Cohere API-nøkkel",
      "model-label": "Modellvalg",
      "available-models": "Tilgjengelige innebyggingsmodeller",
    },
    // Jina Embedding Options
    jina: {
      "api-key": "Jina API-nøkkel",
      "api-key-format": "Jina API-nøkkel må starte med 'jina_'",
      "api-key-placeholder": "Skriv inn din Jina API-nøkkel",
      "api-key-error": "Ugyldig Jina API-nøkkel",
      "model-label": "Modellvalg",
      "available-models": "Tilgjengelige innebyggingsmodeller",
      "embedding-type": "Type innebygging",
      "available-types": "Tilgjengelige typer innebygging",
      dimensions: "Dimensjoner",
      "available-dimensions": "Tilgjengelige dimensjoner",
      task: "Oppgave",
      "available-tasks": "Tilgjengelige oppgaver",
      "late-chunking": "Forsinket segmentering",
      "late-chunking-help":
        "Aktiver forsinket segmentering for dokumentbehandling",
    },
    // LocalAI Embedding Options
    localai: {
      "model-label": "Navn på innebyggingsmodell",
      "hide-endpoint": "Skjul avanserte innstillinger",
      "show-endpoint": "Vis avanserte innstillinger",
      "base-url": "LocalAI Base-URL",
      "base-url-placeholder": "http://localhost:8080/v1",
      "base-url-help": "Skriv inn URL-en der LocalAI kjører.",
      "auto-detect": "Automatisk oppdag",
      "loading-models": "-- laster tilgjengelige modeller --",
      "waiting-url": "-- venter på URL --",
      "loaded-models": "Dine lastede modeller",
    },
    // Generic OpenAI-Compatible Embedding Options
    generic: {
      "base-url": "Base-URL",
      "base-url-placeholder": "https://api.openai.com/v1",
      "base-url-help":
        "Skriv inn base-URL-en for ditt OpenAI-kompatible API-endepunkt.",
      "model-label": "Innebyggingsmodell",
      "model-placeholder":
        "Skriv inn modellnavnet (f.eks. text-embedding-ada-002)",
      "model-help":
        "Spesifiser modellidentifikatoren for å generere innebygginger.",
      "api-key": "API-nøkkel",
      "api-key-placeholder": "sk-mysecretkey",
      "api-key-help": "Skriv inn din API-nøkkel for autentisering.",
    },
    // OpenAI Embedding Options
    openai: {
      "api-key": "OpenAI API-nøkkel",
      "api-key-placeholder": "Skriv inn din OpenAI API-nøkkel",
      "model-label": "Modellvalg",
      "available-models": "Tilgjengelige innebyggingsmodeller",
    },
    // VoyageAI Embedding Options
    voyageai: {
      "api-key": "VoyageAI API-nøkkel",
      "api-key-placeholder": "Skriv inn din VoyageAI API-nøkkel",
      "model-label": "Modellvalg",
      "available-models": "Tilgjengelige innebyggingsmodeller",
    },
    // Azure OpenAI Embedding Options
    azureai: {
      "service-endpoint": "Azure OpenAI-tjenesteendepunkt",
      "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
      "service-endpoint-help":
        "Skriv inn URL-en til ditt Azure OpenAI-tjenesteendepunkt",
      "api-key": "Azure OpenAI API-nøkkel",
      "api-key-placeholder": "Skriv inn din Azure OpenAI API-nøkkel",
      "api-key-help": "Skriv inn din Azure OpenAI API-nøkkel for autentisering",
      "deployment-name": "Distribusjonsnavn for innebyggingsmodell",
      "deployment-name-placeholder":
        "Skriv inn navnet på distribusjonen av din Azure OpenAI-innebyggingsmodell",
      "deployment-name-help":
        "Distribusjonsnavnet for din Azure OpenAI-innebyggingsmodell",
    },
    // Native Embedding Options
    native: {
      description: "Bruker innfødt innebyggingsleverandør for tekstbehandling",
    },
  },
  // =========================
  // JINA
  // =========================
  jina: {
    "api-key": "Jina API-nøkkel",
    "api-key-placeholder": "Skriv inn din Jina API-nøkkel",
    "api-key-format": "Jina API-nøkkel må starte med 'jina_'",
    "model-preference": "Modellpreferanse",
  },

  // =========================
  // OLLAMA
  // =========================
  ollama: {
    "max-embedding-chunk-length": "Maks embedding chunk lengde",
  },

  // =========================
  // VOYAGEAI
  // =========================
  voyageai: {
    "api-key": "VoyageAI API-nøkkel",
    "api-key-placeholder": "Skriv inn din VoyageAI API-nøkkel",
    "model-preference": "Modellpreferanse",
  },
};
