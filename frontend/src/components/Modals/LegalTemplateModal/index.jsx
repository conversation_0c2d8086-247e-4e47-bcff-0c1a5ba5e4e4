import React, { useState, useEffect, useRef } from "react";
import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";
import Select from "@/components/Select";
import { useTranslation } from "react-i18next";
import { Info, Gear, UploadSimple } from "@phosphor-icons/react";
import { Tooltip } from "react-tooltip";
// import { supportedLanguageCodes } from "@/locales/resources";
import showToast from "@/utils/toast";
// import Workspace from "@/models/workspace"; // Unused import
import System from "@/models/system";
import Input from "@/components/ui/Input";
import Textarea from "@/components/ui/Textarea";
import Label from "@/components/ui/Label";
import Slider from "@/components/ui/Slider";
import FormItem from "@/components/ui/FormItem";
import { ImSpinner2 } from "react-icons/im";
import { LuSend } from "react-icons/lu";
import useUser from "@/hooks/useUser";
import CustomLegalTemplatesModal from "../CustomLegalTemplatesModal";
import Checkbox from "@/components/ui/Checkbox";
import { useSystemLanguage } from "@/stores/settingsStore";

export default function LegalTemplateModal({
  isOpen,
  onClose,
  onSubmit, // Callback to handle submission with generated prompt
  currentOrganizationId, // Added prop to know which org context we are in
}) {
  const { t, i18n } = useTranslation();
  const { user } = useUser(); // Assuming useUser provides the logged-in user context
  const systemLanguage = useSystemLanguage() || "en";
  const [documentCategory, setDocumentCategory] = useState("all-categories");
  const [documentType, setDocumentType] = useState("nda-agreement");
  const isCustomTemplate = documentType.startsWith("custom-");
  const [businessName, setBusinessName] = useState("");
  const [opposingParty, setOpposingParty] = useState("");
  const [jurisdiction, setJurisdiction] = useState("");
  const [industry, setIndustry] = useState("none");
  const [protectionLevel, setProtectionLevel] = useState([2]); // Default value for Slider
  const [additionalInstructions, setAdditionalInstructions] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  // Initialize document language with current UI language or fallback to English
  const [documentLanguage, setDocumentLanguage] = useState(
    i18n.language || "en"
  );
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [settingsScope, setSettingsScope] = useState("user");
  const [isUploadingDocx, setIsUploadingDocx] = useState(false);
  const docxFileInputRef = useRef(null);

  // State for custom templates
  const [customTemplates, setCustomTemplates] = useState([]);
  // State for custom input values
  const [customInputValues, setCustomInputValues] = useState([]);
  // Show only custom templates toggle
  const [showOnlyCustom, setShowOnlyCustom] = useState(false);

  // Remove isInitialized state and use a ref instead
  const initializedRef = useRef(false);

  // Update document language and jurisdiction when modal is opened or closed
  useEffect(() => {
    if (isOpen) {
      // Set document language to system language from settingsStore
      setDocumentLanguage(systemLanguage);

      // Log for debugging
      console.log("Modal opened with system language:", systemLanguage);

      // Update jurisdiction based on the system language
      const defaultJurisdictions = t("legal-templates.default-jurisdictions", {
        returnObjects: true,
      });

      const defaultJurisdiction =
        defaultJurisdictions[systemLanguage] ||
        defaultJurisdictions["en"] ||
        "";
      setJurisdiction(defaultJurisdiction);

      // Reset the initialized ref when modal opens
      initializedRef.current = false;
    } else {
      // Reset settings modal state when main modal is closed
      setIsSettingsModalOpen(false);
    }
  }, [isOpen, i18n.language, t, systemLanguage]);

  // Fetch custom templates when modal opens for any user
  useEffect(() => {
    if (isOpen) {
      fetchCustomTemplates();
    }
  }, [isOpen, user]);

  // Fetch custom templates from the server
  const fetchCustomTemplates = async () => {
    setIsLoading(true);
    try {
      // Always fetch system templates
      const systemPromise = System.fetchCustomLegalTemplates();
      // Prepare organization fetch promise or empty data
      const orgIdToFetch =
        user?.organizationId ??
        (user?.role === "admin" ? currentOrganizationId || 1 : null);
      let orgPromise = Promise.resolve({ success: true, data: [] });
      if (orgIdToFetch) {
        // Always fetch organization templates when an organization ID is available
        orgPromise = System.fetchOrganizationLegalTemplates(orgIdToFetch);
      }
      // Always fetch user templates
      const userPromise = System.fetchUserLegalTemplates();
      // Execute requests concurrently
      const [systemRes, orgRes, userRes] = await Promise.all([
        systemPromise,
        orgPromise,
        userPromise,
      ]);
      // Combine and annotate templates
      let combinedTemplates = [];
      if (systemRes.success && systemRes.data) {
        combinedTemplates = combinedTemplates.concat(
          systemRes.data.map((t) => ({ ...t, scope: "system" }))
        );
      }
      if (orgRes.success && orgRes.data) {
        combinedTemplates = combinedTemplates.concat(
          orgRes.data.map((t) => ({ ...t, scope: "organization" }))
        );
      }
      if (userRes.success && userRes.data) {
        combinedTemplates = combinedTemplates.concat(
          userRes.data.map((t) => ({ ...t, scope: "user" }))
        );
      }
      // Remove duplicates by ID
      const uniqueTemplates = combinedTemplates.reduce((acc, current) => {
        const exists = acc.find((item) => item.id === current.id);
        if (!exists) {
          return acc.concat([current]);
        }
        return acc;
      }, []);
      setCustomTemplates(uniqueTemplates);
    } catch (error) {
      console.error("Error fetching custom templates:", error);
      showToast(t("custom-legal-templates.fetch-error"), "error");
    } finally {
      setIsLoading(false);
    }
  };

  // Get document categories
  const getDocumentCategories = () => {
    if (showOnlyCustom) {
      // Only show categories from custom templates
      const customCats = customTemplates.reduce((acc, template) => {
        if (!acc[template.category]) {
          acc[template.category] = template.category;
        }
        return acc;
      }, {});

      return [
        {
          value: "all-categories",
          label: t(
            "legal-templates.document-categories.all-categories",
            "All Document Types"
          ),
        },
        ...Object.entries(customCats)
          .map(([key, value]) => ({ value: key, label: value }))
          .sort((a, b) => a.label.localeCompare(b.label, i18n.language)),
      ];
    }

    // Get standard categories from translations
    const categories = t("legal-templates.document-categories", {
      returnObjects: true,
    });

    // Get unique categories from custom templates
    const customCats = customTemplates.reduce((acc, template) => {
      if (!acc[template.category]) {
        acc[template.category] = template.category;
      }
      return acc;
    }, {});

    // Combine standard and custom categories
    const allCategories = {
      "all-categories": t(
        "legal-templates.document-categories.all-categories",
        "All Document Types"
      ),
      ...categories,
      ...customCats,
    };

    // Convert to array and sort
    return Object.entries(allCategories)
      .map(([key, value]) => ({ value: key, label: value }))
      .sort((a, b) => {
        if (a.value === "all-categories") return -1;
        if (b.value === "all-categories") return 1;
        return a.label.localeCompare(b.label, i18n.language);
      });
  };

  // Get document options sorted by translated label and filtered by category
  const getSortedDocumentOptions = (language, category) => {
    // If showing only custom templates, return only those
    if (showOnlyCustom) {
      return customTemplates
        .filter(
          (template) =>
            category === "all-categories" || template.category === category
        )
        .map((template) => ({
          value: `custom-${template.id}`,
          label: template.documentType,
          isCustom: true,
          templateContent: template.templateContent,
          category: template.category,
          scope: template.scope,
        }))
        .sort((a, b) => a.label.localeCompare(b.label, language));
    }

    // Get standard document types from translations
    const types = t("legal-templates.document-types", {
      returnObjects: true,
    });
    const categoryMap = t("legal-templates.document-category-map", {
      returnObjects: true,
    });

    // Start with standard document types
    let options = Object.entries(types)
      .filter(([key]) => {
        if (key.endsWith("-desc")) return false;
        return (
          category === "all-categories" ||
          (categoryMap[key] && categoryMap[key] === category)
        );
      })
      .map(([key, value]) => ({
        value: key,
        label: value,
        isCustom: false,
      }));

    // Add custom templates
    const customOptions = customTemplates
      .filter((template) => {
        return category === "all-categories" || template.category === category;
      })
      .map((template) => ({
        value: `custom-${template.id}`,
        label: template.documentType,
        isCustom: true,
        templateContent: template.templateContent,
        category: template.category,
        scope: template.scope,
      }));

    // Combine and sort all options
    return [...options, ...customOptions].sort((a, b) =>
      a.label.localeCompare(b.label, language)
    );
  };

  // Get industry options sorted by translated label
  const getSortedIndustryOptions = (language) => {
    const industries = t("legal-templates.industry-types", {
      returnObjects: true,
    });

    // Create options array from industries, excluding the 'none' option
    const sortedOptions = Object.entries(industries)
      .filter(([key]) => key !== "none") // Exclude the 'none' option from sorting
      .map(([key, value]) => ({
        value: key,
        label: value,
      }))
      .sort((a, b) => a.label.localeCompare(b.label, language));

    // Add the "None selected" option at the top
    const options = [
      {
        value: "none",
        label: t("legal-templates.industry-types.none"),
      },
      ...sortedOptions,
    ];

    return options;
  };

  // Get language options sorted by translated label
  const getSortedLanguageOptions = (language) => {
    // Map ISO language codes to their keys in the translations
    const languageKeyMap = {
      en: "english",
      es: "spanish",
      fr: "french",
      de: "german",
      it: "italian",
      pt: "portuguese",
      nl: "dutch",
      sv: "swedish",
      no: "norwegian",
      da: "danish",
      fi: "finnish",
      pl: "polish",
      ru: "russian",
      ja: "japanese",
      zh: "chinese",
      ar: "arabic",
      rw: "kinyarwanda",
    };

    const languages = t("legal-templates.languages", {
      returnObjects: true,
    });

    // Create options for all languages in the map, not just supported ones
    return Object.entries(languageKeyMap)
      .map(([code, key]) => ({
        value: code,
        label: languages[key] || key,
      }))
      .sort((a, b) => a.label.localeCompare(b.label, language));
  };

  // Get default jurisdiction based on selected language
  const getDefaultJurisdiction = () => {
    const defaultJurisdictions = t("legal-templates.default-jurisdictions", {
      returnObjects: true,
    });

    // Try to get jurisdiction for current language, fallback to English, or empty string
    return (
      defaultJurisdictions[documentLanguage] || defaultJurisdictions["en"] || ""
    );
  };

  // Calculate document options based on current state
  const documentOptions = getSortedDocumentOptions(
    documentLanguage,
    documentCategory
  );

  // Effect for document type setting (initialization)
  useEffect(() => {
    if (isOpen && documentOptions.length > 0 && !initializedRef.current) {
      setDocumentType(documentOptions[0].value);
      initializedRef.current = true;
    }
  }, [isOpen, documentOptions]);

  // Effect for resetting custom input values when documentType, customTemplates, or modal open state changes
  useEffect(() => {
    if (isOpen) {
      const templateId = documentType.replace(/^[^0-9]*/, "");
      const customTemplate = customTemplates.find(
        (t) => String(t.id) === templateId
      );
      if (
        customTemplate &&
        Array.isArray(customTemplate.customInputs) &&
        customTemplate.customInputs.length > 0
      ) {
        setCustomInputValues(
          Array(customTemplate.customInputs.length).fill("")
        );
      } else {
        setCustomInputValues([]);
      }
    }
  }, [documentType, customTemplates, isOpen]);

  // Effect to ensure a valid custom document type is selected when filtering is enabled
  useEffect(() => {
    if (showOnlyCustom) {
      const customOptions = getSortedDocumentOptions(
        i18n.language,
        documentCategory
      );
      if (customOptions.length > 0 && !documentType.startsWith("custom-")) {
        setDocumentType(customOptions[0].value);
      }
    }
  }, [showOnlyCustom, documentCategory]);

  // Handle document category change
  const handleCategoryChange = (value) => {
    setDocumentCategory(value);
    // Get available document types for the new category
    const options = getSortedDocumentOptions(documentLanguage, value);
    // If current document type is not in the new category's options, select the first available option
    if (!options.find((opt) => opt.value === documentType)) {
      setDocumentType(options[0]?.value || "nda-agreement");
    }
  };

  // Handle document language change
  const handleLanguageChange = (value) => {
    // Ensure we have a valid language value
    const newLanguage = value || i18n.language || "en";
    console.log("Language changed to:", newLanguage);

    setDocumentLanguage(newLanguage);

    // Update jurisdiction based on language
    const defaultJurisdictions = t("legal-templates.default-jurisdictions", {
      returnObjects: true,
    });
    setJurisdiction(
      defaultJurisdictions[newLanguage] || defaultJurisdictions["en"] || ""
    );
  };

  const handleGenerate = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    let prompt = "";
    let documentTypeName = ""; // Initialize variable to hold the document type name

    if (isCustomTemplate) {
      // Find the custom template
      const templateId = documentType.replace("custom-", "");
      const customTemplate = customTemplates.find(
        (t) => String(t.id) === templateId
      );

      if (customTemplate) {
        // If custom input fields are enabled, use only those for the prompt
        if (
          Array.isArray(customTemplate.customInputs) &&
          customTemplate.customInputs.length > 0
        ) {
          // Construct prompt from custom input fields only
          prompt = `${t("legal-templates.prompt.generate-in-language", { language: t(`legal-templates.languages.${documentLanguage}`) })} ${customTemplate.documentType}`;
          customTemplate.customInputs.forEach((field, idx) => {
            prompt += `\n${field}: ${customInputValues[idx] || ""}`;
          });
          // Optionally append template description/content/formatting as before
          if (
            customTemplate.templateDescription &&
            customTemplate.templateDescription.trim() !== ""
          ) {
            prompt += `\n\n${t("legal-templates.prompt.template-description")}: ${customTemplate.templateDescription}`;
          }
          if (
            customTemplate.templateContent &&
            customTemplate.templateContent.trim() !== ""
          ) {
            prompt += `\n\n${t("legal-templates.prompt.example-content")}:\n<${t("legal-templates.prompt.content-examples-start")}>\n${customTemplate.templateContent}\n</${t("legal-templates.prompt.content-examples-end")}>`;
          }
          if (
            customTemplate.templateFormatting &&
            customTemplate.templateFormatting.trim() !== ""
          ) {
            prompt += `\n\n${t("legal-templates.prompt.formatting-guidelines")}:\n<${t("legal-templates.prompt.formatting-examples-start")}>\n${customTemplate.templateFormatting}\n</${t("legal-templates.prompt.formatting-examples-end")}>`;
            prompt += `\n\n${t("legal-templates.prompt.prioritize-formatting")}`;
          }
        } else {
          // Get the document type name for the prompt
          documentTypeName = customTemplate.documentType;

          // Construct the base prompt including the document type name
          prompt = `${t("legal-templates.prompt.generate-in-language", { language: t(`legal-templates.languages.${documentLanguage}`) })} ${documentTypeName}`;

          // Always include business name (use placeholder if empty)
          prompt += ` ${t("legal-templates.prompt.for")} ${businessName || t("legal-templates.placeholders.business-name")}.`;
          prompt += ` ${t("legal-templates.prompt.company-description")} ${businessName || t("legal-templates.placeholders.business-name")}`;

          // Add opposing party if provided
          if (opposingParty && opposingParty.trim() !== "") {
            prompt += ` ${t("legal-templates.prompt.opposing-party-description")} ${opposingParty}`;
          }

          // Only include industry information if an industry is selected and not "none"
          if (industry && industry !== "none") {
            // Get the industry label from the full list of industries
            const industries = t("legal-templates.industry-types", {
              returnObjects: true,
            });
            const industryLabel =
              industries[industry] ||
              t("legal-templates.placeholders.industry");
            prompt += ` ${t("legal-templates.prompt.in-the")} ${industryLabel},`;
          }

          // For jurisdiction, use provided value or default based on language
          if (jurisdiction && jurisdiction.trim() !== "") {
            prompt += ` ${t("legal-templates.prompt.operating-in")} ${jurisdiction}.`;
          } else {
            const defaultJurisdiction = getDefaultJurisdiction();
            if (defaultJurisdiction && defaultJurisdiction.trim() !== "") {
              prompt += ` ${t("legal-templates.prompt.operating-in")} ${defaultJurisdiction}.`;
            } else {
              // End the sentence if no jurisdiction
              prompt += ".";
            }
          }
          prompt += "\n\n" + t("legal-templates.prompt.professional-drafting");
          prompt +=
            "\n\n" +
            t("legal-templates.prompt.protection-level") +
            ": " +
            protectionLevel[0] +
            " " +
            t("legal-templates.prompt.out-of") +
            " 3 (";
          if (protectionLevel[0] === 1) {
            prompt += t("legal-templates.protection-levels.standard");
          } else if (protectionLevel[0] === 2) {
            prompt += t("legal-templates.protection-levels.comprehensive");
          } else {
            prompt += t("legal-templates.protection-levels.maximum");
          }
          prompt += " - ";
          if (protectionLevel[0] === 1) {
            prompt += t(
              "legal-templates.protection-levels.level-1-description"
            );
          } else if (protectionLevel[0] === 2) {
            prompt += t(
              "legal-templates.protection-levels.level-2-description"
            );
          } else {
            prompt += t(
              "legal-templates.protection-levels.level-3-description"
            );
          }
          prompt += ").";

          // Append template description if it exists
          if (
            customTemplate.templateDescription &&
            customTemplate.templateDescription.trim() !== ""
          ) {
            prompt +=
              "\n\n" +
              t("legal-templates.prompt.template-description") +
              ": " +
              customTemplate.templateDescription;
          }

          // Append additional instructions if provided
          if (additionalInstructions && additionalInstructions.trim() !== "") {
            prompt +=
              "\n\n" +
              t("legal-templates.prompt.additional-instructions") +
              ": " +
              additionalInstructions;
          }

          // Append custom template content if it exists
          if (
            customTemplate.templateContent &&
            customTemplate.templateContent.trim() !== ""
          ) {
            prompt += `\n\n${t("legal-templates.prompt.example-content")}:\n<${t("legal-templates.prompt.content-examples-start")}>\n${customTemplate.templateContent}\n</${t("legal-templates.prompt.content-examples-end")}>`;
          }

          // Append template formatting if it exists
          if (
            customTemplate.templateFormatting &&
            customTemplate.templateFormatting.trim() !== ""
          ) {
            prompt += `\n\n${t("legal-templates.prompt.formatting-guidelines")}:\n<${t("legal-templates.prompt.formatting-examples-start")}>\n${customTemplate.templateFormatting}\n</${t("legal-templates.prompt.formatting-examples-end")}>`;
            // Add explicit instruction to prioritize this formatting over any other formatting guidelines
            prompt += `\n\n${t("legal-templates.prompt.prioritize-formatting")}`;
          }
        }
      } else {
        // Fallback to standard template if custom template not found (should ideally not happen)
        showToast(t("legal-templates.custom-template-not-found"), "error");
        setIsLoading(false);
        return;
      }
    } else {
      // Standard (non-custom) template prompt construction
      documentTypeName =
        getSortedDocumentOptions(documentLanguage, documentCategory).find(
          (opt) => opt.value === documentType
        )?.label || t("legal-templates.default-document");

      // Add document type description if available
      const docTypeDesc = t(
        `legal-templates.document-types.${documentType}-desc`
      );
      if (
        docTypeDesc &&
        !docTypeDesc.startsWith("legal-templates.document-types")
      ) {
        prompt += `\n\n${t("legal-templates.prompt.more-detailed-description")}${docTypeDesc}`;
      }

      prompt = `${t("legal-templates.prompt.generate-in-language", { language: t(`legal-templates.languages.${documentLanguage}`) })} ${documentTypeName}`;

      // Always include business name (use placeholder if empty)
      prompt += ` ${t("legal-templates.prompt.for")} ${businessName || t("legal-templates.placeholders.business-name")}.`;
      prompt += ` ${t("legal-templates.prompt.company-description")} ${businessName || t("legal-templates.placeholders.business-name")}`;

      // Add opposing party if provided
      if (opposingParty && opposingParty.trim() !== "") {
        prompt += ` ${t("legal-templates.prompt.opposing-party-description")} ${opposingParty}`;
      }

      // Only include industry information if an industry is selected and not "none"
      if (industry && industry !== "none") {
        // Get the industry label from the full list of industries
        const industries = t("legal-templates.industry-types", {
          returnObjects: true,
        });
        const industryLabel =
          industries[industry] || t("legal-templates.placeholders.industry");
        prompt += ` ${t("legal-templates.prompt.in-the")} ${industryLabel},`;
      }

      // For jurisdiction, use provided value or default based on language
      if (jurisdiction && jurisdiction.trim() !== "") {
        prompt += ` ${t("legal-templates.prompt.operating-in")} ${jurisdiction}.`;
      } else {
        const defaultJurisdiction = getDefaultJurisdiction();
        if (defaultJurisdiction && defaultJurisdiction.trim() !== "") {
          prompt += ` ${t("legal-templates.prompt.operating-in")} ${defaultJurisdiction}.`;
        } else {
          // End the sentence if no jurisdiction
          prompt += ".";
        }
      }
      prompt += "\n\n" + t("legal-templates.prompt.professional-drafting");
      prompt +=
        "\n\n" +
        t("legal-templates.prompt.protection-level") +
        ": " +
        protectionLevel[0] +
        " " +
        t("legal-templates.prompt.out-of") +
        " 3 (";
      if (protectionLevel[0] === 1) {
        prompt += t("legal-templates.protection-levels.standard");
      } else if (protectionLevel[0] === 2) {
        prompt += t("legal-templates.protection-levels.comprehensive");
      } else {
        prompt += t("legal-templates.protection-levels.maximum");
      }
      prompt += " - ";
      if (protectionLevel[0] === 1) {
        prompt += t("legal-templates.protection-levels.level-1-description");
      } else if (protectionLevel[0] === 2) {
        prompt += t("legal-templates.protection-levels.level-2-description");
      } else {
        prompt += t("legal-templates.protection-levels.level-3-description");
      }
      prompt += ").";

      // Append additional instructions if provided
      if (additionalInstructions && additionalInstructions.trim() !== "") {
        prompt +=
          "\n\n" +
          t("legal-templates.prompt.additional-instructions") +
          ": " +
          additionalInstructions;
      }
    }

    // Log the final generated prompt for debugging
    console.log("Generated Prompt:", prompt);

    // Call the onSubmit callback with the constructed prompt and options
    if (typeof onSubmit === "function") {
      onSubmit(prompt, {
        documentType: documentTypeName,
        settingsSuffix: "_TM",
      });
    } else {
      console.warn("onSubmit prop is not a function");
    }

    setIsLoading(false);
    onClose(); // Close the modal after submission
  };

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        title={t("legal-templates.modal-title")}
        description={t("legal-templates.modal-description")}
        className="max-w-[52rem] max-h-[95vh] md:max-h-[85vh] w-full md:w-auto"
        footer={
          <div className="flex justify-between w-full items-center flex-wrap gap-2">
            <div>
              {(user?.role === "admin" ||
                user?.role === "manager" ||
                user?.role === "superuser") && <></>}
            </div>
            <div className="flex gap-2 flex-wrap">
              <Button type="button" variant="outline" onClick={onClose}>
                {t("common.cancel")}
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                form="legal-template-form"
              >
                {isLoading ? (
                  <>
                    {t("legal-templates.generating")}
                    <ImSpinner2 className="animate-spin-fast ml-2" />
                  </>
                ) : (
                  <>
                    {t("legal-templates.generate-button")}
                    <LuSend className="ml-2" />
                  </>
                )}
              </Button>
            </div>
          </div>
        }
      >
        <div className="flex flex-col h-full min-h-0">
          {/* Template settings scope buttons */}
          <div className="flex items-center gap-3 mb-4 flex-wrap flex-shrink-0 px-6">
            {/* System templates: admin only */}
            {user?.role === "admin" && (
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setSettingsScope("system");
                  setIsSettingsModalOpen(true);
                }}
                className="flex items-center gap-2"
              >
                <Gear size={16} />
                <span className="hidden sm:inline">
                  {t("legal-templates.system-templates")}
                </span>
                <span className="sm:hidden">System</span>
              </Button>
            )}
            {/* Org templates: admin, manager, superuser */}
            {(user?.role === "admin" ||
              user?.role === "manager" ||
              user?.role === "superuser") && (
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setSettingsScope("org");
                  setIsSettingsModalOpen(true);
                }}
                className="flex items-center gap-2"
              >
                <Gear size={16} />
                <span className="hidden sm:inline">
                  {t("legal-templates.org-templates")}
                </span>
                <span className="sm:hidden">Org</span>
              </Button>
            )}
            {/* My templates: all logged-in users */}
            {user && (
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setSettingsScope("user");
                  setIsSettingsModalOpen(true);
                }}
                className="flex items-center gap-2"
              >
                <Gear size={16} />
                <span className="hidden sm:inline">
                  {t("legal-templates.my-templates")}
                </span>
                <span className="sm:hidden">My</span>
              </Button>
            )}
          </div>

          <form
            onSubmit={handleGenerate}
            id="legal-template-form"
            className="flex flex-col flex-1 min-h-0"
          >
            <div className="flex-1 min-h-0 overflow-y-auto -webkit-overflow-scrolling-touch">
              <div className="space-y-4 pb-4">
                <div className="px-6 flex items-center gap-3">
                  <Checkbox
                    id="showOnlyCustom"
                    checked={showOnlyCustom}
                    onChange={setShowOnlyCustom}
                    label={t("legal-templates.show-only-custom")}
                  />
                </div>
                {/* Always show selectors */}
                <div className="px-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItem>
                      <Label htmlFor="documentCategory">
                        {t("legal-templates.document-category-label")}
                      </Label>
                      <Select
                        id="documentCategory"
                        value={documentCategory}
                        options={getDocumentCategories()}
                        onChange={handleCategoryChange}
                        hideIcon={true}
                      />
                    </FormItem>
                    <FormItem>
                      <Label htmlFor="documentType">
                        {t("legal-templates.document-type-label")}
                      </Label>
                      <Select
                        id="documentType"
                        value={documentType}
                        options={getSortedDocumentOptions(
                          i18n.language,
                          documentCategory
                        )}
                        onChange={(newValue) => setDocumentType(newValue)}
                        className="document-type-select"
                        maxHeight="160"
                        minWidth="96"
                        hideIcon={true}
                      />
                      {/* Show description for templates if available */}
                      {(() => {
                        // For custom templates, show the template description if available
                        if (isCustomTemplate) {
                          const templateId = documentType.replace(
                            "custom-",
                            ""
                          );
                          const customTemplate = customTemplates.find(
                            (t) => String(t.id) === templateId
                          );

                          if (
                            customTemplate &&
                            customTemplate.templateDescription &&
                            customTemplate.templateDescription.trim() !== ""
                          ) {
                            return (
                              <div className="mt-2 text-xs text-muted-foreground">
                                {customTemplate.templateDescription}
                              </div>
                            );
                          }
                        } else {
                          // For standard templates, show the description from translations
                          const desc = t(
                            `legal-templates.document-types.${documentType}-desc`
                          );
                          // Only show if translation exists and is not the key itself
                          if (
                            desc &&
                            !desc.startsWith("legal-templates.document-types")
                          ) {
                            return (
                              <div className="mt-2 text-xs text-muted-foreground">
                                {desc}
                              </div>
                            );
                          }
                        }
                        return null;
                      })()}
                    </FormItem>
                  </div>
                </div>
                <div className="px-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItem>
                      <Label htmlFor="documentLanguage">
                        {t("legal-templates.document-language-label")}
                      </Label>
                      <Select
                        id="documentLanguage"
                        value={documentLanguage || i18n.language || "en"}
                        onChange={handleLanguageChange}
                        options={getSortedLanguageOptions(i18n.language)}
                        placeholder={t(
                          "legal-templates.document-language-label"
                        )}
                        hideIcon={true}
                      />
                    </FormItem>
                    <div></div>
                  </div>
                </div>
                {(() => {
                  const templateId = documentType.replace(/^[^0-9]*/, "");
                  const customTemplate = customTemplates.find(
                    (t) => String(t.id) === templateId
                  );
                  if (
                    customTemplate &&
                    Array.isArray(customTemplate.customInputs) &&
                    customTemplate.customInputs.length > 0
                  ) {
                    // Only render custom input fields
                    return (
                      <div className="px-6 space-y-4">
                        {customTemplate.customInputs.map((field, idx) => (
                          <FormItem key={idx}>
                            <Label htmlFor={`customInput${idx}`}>{field}</Label>
                            <Input
                              id={`customInput${idx}`}
                              value={customInputValues[idx] || ""}
                              onChange={(e) => {
                                const newVals = [...customInputValues];
                                newVals[idx] = e.target.value;
                                setCustomInputValues(newVals);
                              }}
                              placeholder=""
                            />
                          </FormItem>
                        ))}
                      </div>
                    );
                  }
                  // Otherwise, render standard fields and protection level
                  return (
                    <>
                      <div className="px-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItem>
                            <Label htmlFor="businessName">
                              {t("legal-templates.business-name")}
                            </Label>
                            <Input
                              id="businessName"
                              value={businessName}
                              onChange={(e) => setBusinessName(e.target.value)}
                              placeholder={t(
                                "legal-templates.placeholders.business-name"
                              )}
                            />
                          </FormItem>
                          <FormItem>
                            <Label htmlFor="opposingParty">
                              {t("legal-templates.opposing-party")}
                            </Label>
                            <Input
                              id="opposingParty"
                              value={opposingParty}
                              onChange={(e) => setOpposingParty(e.target.value)}
                              placeholder={t(
                                "legal-templates.placeholders.opposing-party"
                              )}
                            />
                          </FormItem>
                        </div>
                      </div>
                      <div className="px-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItem>
                            <Label htmlFor="jurisdiction">
                              {t("legal-templates.jurisdiction")}
                            </Label>
                            <Input
                              id="jurisdiction"
                              type="text"
                              value={jurisdiction}
                              onChange={(e) => setJurisdiction(e.target.value)}
                              placeholder={getDefaultJurisdiction()}
                            />
                          </FormItem>
                          <FormItem>
                            <Label htmlFor="industry">
                              {t("legal-templates.industry")}
                            </Label>
                            <Select
                              id="industry"
                              value={industry === "none" ? "" : industry}
                              options={getSortedIndustryOptions(i18n.language)}
                              onChange={(newValue) =>
                                setIndustry(newValue || "none")
                              }
                              placeholder={t(
                                "legal-templates.industry-types.none"
                              )}
                              hideIcon={true}
                            />
                          </FormItem>
                        </div>
                      </div>
                      <div className="px-6">
                        <FormItem>
                          <div className="flex items-center justify-between">
                            <Label htmlFor="protectionLevel">
                              {t("legal-templates.protection-level")}
                            </Label>
                            <div className="text-sm text-muted">
                              {protectionLevel[0]} / 3
                              <span
                                className="ml-2 cursor-help"
                                data-tooltip-id="protection-level-tooltip"
                              >
                                <Info size={16} />
                              </span>
                              <Tooltip
                                id="protection-level-tooltip"
                                place="top"
                                delayShow={300}
                                className="max-w-xs !opacity-100 z-[999]"
                              >
                                <div className="space-y-3">
                                  <p className="font-semibold">
                                    {t(
                                      "legal-templates.protection-levels.tooltip-title"
                                    )}
                                  </p>
                                  <div className="space-y-1">
                                    <p>
                                      <span className="font-medium">
                                        {t(
                                          "legal-templates.protection-levels.standard"
                                        )}
                                        :
                                      </span>{" "}
                                      {t(
                                        "legal-templates.protection-levels.standard-description"
                                      )}
                                    </p>
                                    <p>
                                      <span className="font-medium">
                                        {t(
                                          "legal-templates.protection-levels.comprehensive"
                                        )}
                                        :
                                      </span>{" "}
                                      {t(
                                        "legal-templates.protection-levels.comprehensive-description"
                                      )}
                                    </p>
                                    <p>
                                      <span className="font-medium">
                                        {t(
                                          "legal-templates.protection-levels.maximum"
                                        )}
                                        :
                                      </span>{" "}
                                      {t(
                                        "legal-templates.protection-levels.maximum-description"
                                      )}
                                    </p>
                                  </div>
                                </div>
                              </Tooltip>
                            </div>
                          </div>
                          <div className="w-[calc(100%-12px)] mx-auto">
                            <Slider
                              id="protectionLevel"
                              value={protectionLevel}
                              onValueChange={setProtectionLevel}
                              min={1}
                              max={3}
                              step={1}
                            />
                            <div className="flex justify-between text-xs px-1 mt-1">
                              <span>
                                {t(
                                  "legal-templates.protection-levels.standard"
                                )}
                              </span>
                              <span>
                                {t(
                                  "legal-templates.protection-levels.comprehensive"
                                )}
                              </span>
                              <span>
                                {t("legal-templates.protection-levels.maximum")}
                              </span>
                            </div>
                            <div className="mt-3 text-xs text-muted py-2 px-3 rounded-md border border-border bg-background">
                              <p className="font-medium">
                                {t(
                                  `legal-templates.protection-levels.level-${protectionLevel[0]}-title`
                                )}
                              </p>
                              <p>
                                {t(
                                  `legal-templates.protection-levels.level-${protectionLevel[0]}-description`
                                )}
                              </p>
                            </div>
                          </div>
                        </FormItem>
                      </div>
                    </>
                  );
                })()}
                {/* Always show additional instructions field */}
                <div className="px-6 pb-4">
                  <FormItem>
                    <div className="flex flex-col gap-2 mb-3">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="additionalInstructions">
                          {t("legal-templates.additional-instructions")}
                        </Label>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() =>
                            docxFileInputRef.current &&
                            docxFileInputRef.current.click()
                          }
                          disabled={isUploadingDocx}
                          className="flex items-center gap-2 flex-shrink-0"
                          size="sm"
                        >
                          {isUploadingDocx ? (
                            <ImSpinner2 className="animate-spin-fast" />
                          ) : (
                            <UploadSimple />
                          )}
                          <span className="hidden sm:inline">
                            {t("docx-edit.upload-button", "Upload DOCX")}
                          </span>
                          <span className="sm:hidden">Upload</span>
                        </Button>
                      </div>
                      <input
                        type="file"
                        ref={docxFileInputRef}
                        onChange={async (event) => {
                          const file = event.target.files[0];
                          if (!file) return;
                          if (!file.name.toLowerCase().endsWith(".docx")) {
                            showToast(
                              t(
                                "docx-edit.file-type-note",
                                "Only .docx files are supported"
                              ),
                              "error"
                            );
                            return;
                          }
                          try {
                            setIsUploadingDocx(true);
                            const response =
                              await System.uploadDocxForTemplate(file);
                            if (!response.success) {
                              throw new Error(
                                response.error || "Error uploading file"
                              );
                            }
                            // Append the extracted content to additionalInstructions
                            setAdditionalInstructions((prev) =>
                              prev && prev.trim()
                                ? prev + "\n\n" + response.data.content
                                : response.data.content
                            );
                            showToast(
                              t(
                                "docx-edit.content-extracted",
                                "Content extracted from DOCX file"
                              ),
                              "success"
                            );
                          } catch (error) {
                            console.error("Error uploading DOCX file:", error);
                            showToast(
                              t(
                                "docx-edit.upload-error",
                                "Error uploading file: "
                              ) + error.message,
                              "error"
                            );
                          } finally {
                            setIsUploadingDocx(false);
                            if (docxFileInputRef.current) {
                              docxFileInputRef.current.value = "";
                            }
                          }
                        }}
                        accept=".docx"
                        className="hidden"
                      />
                    </div>
                    <Textarea
                      id="additionalInstructions"
                      value={additionalInstructions}
                      onChange={(e) =>
                        setAdditionalInstructions(e.target.value)
                      }
                      placeholder={t(
                        "legal-templates.placeholders.additional-instructions"
                      )}
                      rows={6}
                      className="min-h-[150px] w-full"
                    />
                  </FormItem>
                </div>
              </div>
            </div>
          </form>
        </div>

        {/* Custom Templates Modal */}
        <CustomLegalTemplatesModal
          isOpen={isSettingsModalOpen}
          onClose={() => setIsSettingsModalOpen(false)}
          onSuccess={fetchCustomTemplates}
          scope={settingsScope}
          organizationId={
            settingsScope === "org" ? user?.organization?.id : null
          }
        />
      </Modal>
    </>
  );
}
