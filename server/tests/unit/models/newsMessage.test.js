const { NewsMessage } = require("../../../models/newsMessage");

// Mock Prisma
jest.mock("../../../utils/prisma", () => ({
  news_messages: {
    create: jest.fn(),
    findMany: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  users: {
    findUnique: jest.fn(),
  },
  user_news_dismissals: {
    findMany: jest.fn(),
    upsert: jest.fn(),
  },
}));

// Get the mocked prisma for use in tests
const mockPrisma = require("../../../utils/prisma");

describe("NewsMessage Model Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("create", () => {
    test("should create a news message successfully", async () => {
      const mockNewsMessage = {
        id: 1,
        title: "Test News",
        content: "Test Content",
        priority: "medium",
        target_roles: null,
        expires_at: null,
        created_by: 1,
        is_active: true,
        createdAt: new Date(),
      };

      mockPrisma.news_messages.create.mockResolvedValue(mockNewsMessage);

      const result = await NewsMessage.create({
        title: "Test News",
        content: "Test Content",
        priority: "medium",
        createdBy: 1,
      });

      expect(result.newsMessage).toEqual(mockNewsMessage);
      expect(result.message).toBeNull();
      expect(mockPrisma.news_messages.create).toHaveBeenCalledWith({
        data: {
          title: "Test News",
          content: "Test Content",
          priority: "medium",
          target_roles: null,
          expires_at: null,
          created_by: 1,
        },
      });
    });

    test("should create news message with target roles", async () => {
      const mockNewsMessage = {
        id: 1,
        title: "Admin News",
        content: "Admin Content",
        priority: "high",
        target_roles: '["admin","manager"]',
        expires_at: null,
        created_by: 1,
      };

      mockPrisma.news_messages.create.mockResolvedValue(mockNewsMessage);

      const result = await NewsMessage.create({
        title: "Admin News",
        content: "Admin Content",
        priority: "high",
        targetRoles: ["admin", "manager"],
        createdBy: 1,
      });

      expect(result.newsMessage).toEqual(mockNewsMessage);
      expect(mockPrisma.news_messages.create).toHaveBeenCalledWith({
        data: {
          title: "Admin News",
          content: "Admin Content",
          priority: "high",
          target_roles: '["admin","manager"]',
          expires_at: null,
          created_by: 1,
        },
      });
    });

    test("should create news message with expiration date", async () => {
      const expirationDate = new Date("2024-12-31T23:59:59Z");
      const mockNewsMessage = {
        id: 1,
        title: "Expiring News",
        content: "This news expires",
        priority: "urgent",
        expires_at: expirationDate,
        created_by: 1,
      };

      mockPrisma.news_messages.create.mockResolvedValue(mockNewsMessage);

      const result = await NewsMessage.create({
        title: "Expiring News",
        content: "This news expires",
        priority: "urgent",
        expiresAt: expirationDate,
        createdBy: 1,
      });

      expect(result.newsMessage).toEqual(mockNewsMessage);
      expect(mockPrisma.news_messages.create).toHaveBeenCalledWith({
        data: {
          title: "Expiring News",
          content: "This news expires",
          priority: "urgent",
          target_roles: null,
          expires_at: expirationDate,
          created_by: 1,
        },
      });
    });

    test("should handle creation errors", async () => {
      const error = new Error("Database error");
      mockPrisma.news_messages.create.mockRejectedValue(error);

      const result = await NewsMessage.create({
        title: "Test News",
        content: "Test Content",
        createdBy: 1,
      });

      expect(result.newsMessage).toBeNull();
      expect(result.message).toBe("Database error");
    });
  });

  describe("getActive", () => {
    test("should get all active news messages", async () => {
      const mockActiveNews = [
        {
          id: 1,
          title: "Active News 1",
          priority: "high",
          is_active: true,
          expires_at: null,
          createdAt: new Date("2024-01-01"),
        },
        {
          id: 2,
          title: "Active News 2",
          priority: "medium",
          is_active: true,
          expires_at: new Date("2025-01-01"),
          createdAt: new Date("2024-01-02"),
        },
      ];

      mockPrisma.news_messages.findMany.mockResolvedValue(mockActiveNews);

      const result = await NewsMessage.getActive();

      expect(result).toEqual(mockActiveNews);
      expect(mockPrisma.news_messages.findMany).toHaveBeenCalledWith({
        where: {
          is_active: true,
          OR: [{ expires_at: null }, { expires_at: { gt: expect.any(Date) } }],
        },
        orderBy: { createdAt: "desc" },
      });
    });

    test("should handle database errors", async () => {
      mockPrisma.news_messages.findMany.mockRejectedValue(
        new Error("Database error")
      );

      const result = await NewsMessage.getActive();

      expect(result).toEqual([]);
    });
  });

  describe("getUnreadForUser", () => {
    test("should get unread news for user with default role", async () => {
      const userId = 1;
      const mockUser = {
        id: 1,
        role: "default",
        createdAt: new Date("2024-01-01"),
      };

      const mockDismissedNews = [
        { news_id: "local-2" }, // Dismissed local news
        { news_id: "system-welcome-2024" }, // Dismissed system news
      ];

      const mockNewsMessages = [
        {
          id: 3,
          title: "News for All",
          content: "Content 3",
          priority: "high",
          target_roles: null,
          is_active: true,
          createdAt: new Date("2024-01-03"),
        },
        {
          id: 1,
          title: "News for Default Users",
          content: "Content 1",
          priority: "medium",
          target_roles: null,
          is_active: true,
          createdAt: new Date("2024-01-02"),
        },
      ];

      mockPrisma.users.findUnique.mockResolvedValue(mockUser);
      mockPrisma.user_news_dismissals.findMany.mockResolvedValue(
        mockDismissedNews
      );
      mockPrisma.news_messages.findMany.mockResolvedValue(mockNewsMessages);

      const result = await NewsMessage.getUnreadForUser(userId);

      expect(result).toEqual(mockNewsMessages);
      expect(mockPrisma.users.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
        select: { createdAt: true, role: true },
      });
      expect(mockPrisma.user_news_dismissals.findMany).toHaveBeenCalledWith({
        where: { user_id: userId },
        select: { news_id: true },
      });
      expect(mockPrisma.news_messages.findMany).toHaveBeenCalledWith({
        where: {
          is_active: true,
          createdAt: { gt: mockUser.createdAt },
          OR: [{ expires_at: null }, { expires_at: { gt: expect.any(Date) } }],
          NOT: {
            id: { in: [2] }, // Only local news ID 2 should be excluded
          },
        },
        orderBy: { createdAt: "desc" },
      });
    });

    test("should filter news by target roles for admin user", async () => {
      const userId = 1;
      const mockUser = {
        id: 1,
        role: "admin",
        createdAt: new Date("2024-01-01"),
      };

      const mockNewsMessages = [
        {
          id: 1,
          title: "Admin Only News",
          content: "Admin Content",
          priority: "high",
          target_roles: '["admin"]',
          is_active: true,
          createdAt: new Date("2024-01-02"),
        },
        {
          id: 2,
          title: "Manager Only News",
          content: "Manager Content",
          priority: "medium",
          target_roles: '["manager"]',
          is_active: true,
          createdAt: new Date("2024-01-03"),
        },
        {
          id: 3,
          title: "All Users News",
          content: "All Content",
          priority: "low",
          target_roles: null,
          is_active: true,
          createdAt: new Date("2024-01-04"),
        },
      ];

      mockPrisma.users.findUnique.mockResolvedValue(mockUser);
      mockPrisma.user_news_dismissals.findMany.mockResolvedValue([]);
      mockPrisma.news_messages.findMany.mockResolvedValue(mockNewsMessages);

      const result = await NewsMessage.getUnreadForUser(userId);

      // Should include admin news and all-users news, but not manager-only news
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe(1); // Admin news
      expect(result[1].id).toBe(3); // All users news
    });

    test("should handle invalid target_roles JSON", async () => {
      const userId = 1;
      const mockUser = {
        id: 1,
        role: "default",
        createdAt: new Date("2024-01-01"),
      };

      const mockNewsMessages = [
        {
          id: 1,
          title: "News with Invalid JSON",
          content: "Content",
          priority: "medium",
          target_roles: "invalid json",
          is_active: true,
          createdAt: new Date("2024-01-02"),
        },
      ];

      mockPrisma.users.findUnique.mockResolvedValue(mockUser);
      mockPrisma.user_news_dismissals.findMany.mockResolvedValue([]);
      mockPrisma.news_messages.findMany.mockResolvedValue(mockNewsMessages);

      const result = await NewsMessage.getUnreadForUser(userId);

      // Should include the news item despite invalid JSON (fallback behavior)
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(1);
    });

    test("should return empty array for non-existent user", async () => {
      mockPrisma.users.findUnique.mockResolvedValue(null);

      const result = await NewsMessage.getUnreadForUser(999);

      expect(result).toEqual([]);
    });

    test("should handle database errors", async () => {
      mockPrisma.users.findUnique.mockRejectedValue(
        new Error("Database error")
      );

      const result = await NewsMessage.getUnreadForUser(1);

      expect(result).toEqual([]);
    });
  });

  describe("dismiss", () => {
    test("should dismiss local news correctly", async () => {
      const mockDismissal = {
        user_id: 1,
        news_id: "local-123",
        dismissed_at: new Date(),
      };

      mockPrisma.user_news_dismissals.upsert.mockResolvedValue(mockDismissal);

      const result = await NewsMessage.dismiss(1, 123, false);

      expect(result.dismissal).toEqual(mockDismissal);
      expect(result.message).toBeNull();
      expect(mockPrisma.user_news_dismissals.upsert).toHaveBeenCalledWith({
        where: {
          user_id_news_id: {
            user_id: 1,
            news_id: "local-123",
          },
        },
        update: {
          dismissed_at: expect.any(Date),
        },
        create: {
          user_id: 1,
          news_id: "local-123",
          dismissed_at: expect.any(Date),
        },
      });
    });

    test("should dismiss system news correctly", async () => {
      const mockDismissal = {
        user_id: 1,
        news_id: "system-welcome-2024",
        dismissed_at: new Date(),
      };

      mockPrisma.user_news_dismissals.upsert.mockResolvedValue(mockDismissal);

      const result = await NewsMessage.dismiss(1, "system-welcome-2024", true);

      expect(result.dismissal).toEqual(mockDismissal);
      expect(result.message).toBeNull();
      expect(mockPrisma.user_news_dismissals.upsert).toHaveBeenCalledWith({
        where: {
          user_id_news_id: {
            user_id: 1,
            news_id: "system-welcome-2024",
          },
        },
        update: {
          dismissed_at: expect.any(Date),
        },
        create: {
          user_id: 1,
          news_id: "system-welcome-2024",
          dismissed_at: expect.any(Date),
        },
      });
    });

    test("should handle dismissal errors", async () => {
      mockPrisma.user_news_dismissals.upsert.mockRejectedValue(
        new Error("Database error")
      );

      const result = await NewsMessage.dismiss(1, 123, false);

      expect(result.dismissal).toBeNull();
      expect(result.message).toBe("Database error");
    });
  });

  describe("getDismissedNewsIds", () => {
    test("should get dismissed news IDs correctly", async () => {
      const mockDismissals = [
        { news_id: "local-1" },
        { news_id: "local-5" },
        { news_id: "system-welcome-2024" },
        { news_id: "system-update-2024" },
      ];

      mockPrisma.user_news_dismissals.findMany.mockResolvedValue(
        mockDismissals
      );

      const result = await NewsMessage.getDismissedNewsIds(1);

      expect(result.local).toEqual([1, 5]);
      expect(result.system).toEqual([
        "system-welcome-2024",
        "system-update-2024",
      ]);
      expect(mockPrisma.user_news_dismissals.findMany).toHaveBeenCalledWith({
        where: { user_id: 1 },
        select: { news_id: true },
      });
    });

    test("should handle empty dismissals", async () => {
      mockPrisma.user_news_dismissals.findMany.mockResolvedValue([]);

      const result = await NewsMessage.getDismissedNewsIds(1);

      expect(result.local).toEqual([]);
      expect(result.system).toEqual([]);
    });

    test("should handle database errors", async () => {
      mockPrisma.user_news_dismissals.findMany.mockRejectedValue(
        new Error("Database error")
      );

      const result = await NewsMessage.getDismissedNewsIds(1);

      expect(result.local).toEqual([]);
      expect(result.system).toEqual([]);
    });
  });

  describe("update", () => {
    test("should update news message successfully", async () => {
      const mockUpdatedNews = {
        id: 1,
        title: "Updated Title",
        content: "Updated Content",
        priority: "high",
        target_roles: '["admin"]',
        expires_at: new Date("2025-01-01"),
      };

      mockPrisma.news_messages.update.mockResolvedValue(mockUpdatedNews);

      const result = await NewsMessage.update(1, {
        title: "Updated Title",
        content: "Updated Content",
        priority: "high",
        targetRoles: ["admin"],
        expiresAt: new Date("2025-01-01"),
      });

      expect(result.newsMessage).toEqual(mockUpdatedNews);
      expect(result.message).toBeNull();
      expect(mockPrisma.news_messages.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          title: "Updated Title",
          content: "Updated Content",
          priority: "high",
          target_roles: '["admin"]',
          expires_at: new Date("2025-01-01"),
        },
      });
    });

    test("should handle both camelCase and snake_case field names", async () => {
      const mockUpdatedNews = {
        id: 1,
        title: "Test Title",
        content: "Test Content",
        priority: "medium",
        target_roles: '["manager"]',
        expires_at: new Date("2025-06-01"),
      };

      mockPrisma.news_messages.update.mockResolvedValue(mockUpdatedNews);

      // Test with camelCase fields (from frontend)
      const camelCaseResult = await NewsMessage.update(1, {
        title: "Test Title",
        content: "Test Content",
        priority: "medium",
        targetRoles: ["manager"],
        expiresAt: new Date("2025-06-01"),
      });

      expect(camelCaseResult.newsMessage).toEqual(mockUpdatedNews);
      expect(camelCaseResult.message).toBeNull();

      // Test with snake_case fields (legacy support)
      const snakeCaseResult = await NewsMessage.update(1, {
        title: "Test Title",
        content: "Test Content",
        priority: "medium",
        target_roles: ["manager"],
        expires_at: new Date("2025-06-01"),
      });

      expect(snakeCaseResult.newsMessage).toEqual(mockUpdatedNews);
      expect(snakeCaseResult.message).toBeNull();

      // Verify that both calls result in the same database call
      expect(mockPrisma.news_messages.update).toHaveBeenCalledTimes(2);
      expect(mockPrisma.news_messages.update).toHaveBeenNthCalledWith(1, {
        where: { id: 1 },
        data: {
          title: "Test Title",
          content: "Test Content",
          priority: "medium",
          target_roles: '["manager"]',
          expires_at: new Date("2025-06-01"),
        },
      });
      expect(mockPrisma.news_messages.update).toHaveBeenNthCalledWith(2, {
        where: { id: 1 },
        data: {
          title: "Test Title",
          content: "Test Content",
          priority: "medium",
          target_roles: '["manager"]',
          expires_at: new Date("2025-06-01"),
        },
      });
    });

    test("should handle update errors", async () => {
      mockPrisma.news_messages.update.mockRejectedValue(
        new Error("Database error")
      );

      const result = await NewsMessage.update(1, { title: "New Title" });

      expect(result.newsMessage).toBeNull();
      expect(result.message).toBe("Database error");
    });
  });

  describe("delete", () => {
    test("should delete news message successfully", async () => {
      mockPrisma.news_messages.delete.mockResolvedValue({});

      const result = await NewsMessage.delete(1);

      expect(result.success).toBe(true);
      expect(result.message).toBeNull();
      expect(mockPrisma.news_messages.delete).toHaveBeenCalledWith({
        where: { id: 1 },
      });
    });

    test("should handle deletion errors", async () => {
      mockPrisma.news_messages.delete.mockRejectedValue(
        new Error("Database error")
      );

      const result = await NewsMessage.delete(1);

      expect(result.success).toBe(false);
      expect(result.message).toBe("Database error");
    });
  });

  describe("News Priority and Role Filtering", () => {
    test("should handle all priority levels correctly", async () => {
      const priorities = ["low", "medium", "high", "urgent"];

      for (const priority of priorities) {
        mockPrisma.news_messages.create.mockResolvedValue({
          id: 1,
          priority,
          title: `${priority} priority news`,
        });

        const result = await NewsMessage.create({
          title: `${priority} priority news`,
          content: "Content",
          priority,
          createdBy: 1,
        });

        expect(result.newsMessage.priority).toBe(priority);
      }
    });

    test("should handle all user roles correctly", async () => {
      const roles = ["admin", "manager", "default"];
      const userId = 1;

      for (const role of roles) {
        const mockUser = {
          id: userId,
          role,
          createdAt: new Date("2024-01-01"),
        };

        const mockNewsMessages = [
          {
            id: 1,
            title: `News for ${role}`,
            target_roles: `["${role}"]`,
            is_active: true,
            createdAt: new Date("2024-01-02"),
          },
          {
            id: 2,
            title: "News for all",
            target_roles: null,
            is_active: true,
            createdAt: new Date("2024-01-02"),
          },
        ];

        mockPrisma.users.findUnique.mockResolvedValue(mockUser);
        mockPrisma.user_news_dismissals.findMany.mockResolvedValue([]);
        mockPrisma.news_messages.findMany.mockResolvedValue(mockNewsMessages);

        const result = await NewsMessage.getUnreadForUser(userId);

        // Should include both role-specific and general news
        expect(result).toHaveLength(2);
        expect(result.some((news) => news.title === `News for ${role}`)).toBe(
          true
        );
        expect(result.some((news) => news.title === "News for all")).toBe(true);
      }
    });
  });
});
