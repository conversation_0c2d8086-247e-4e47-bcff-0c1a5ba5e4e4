import React, { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { IoClose } from "react-icons/io5";
import { UploadSimple, Eye } from "@phosphor-icons/react";
import { ImSpinner2 } from "react-icons/im";
import { Button } from "@/components/Button";
import showToast from "@/utils/toast";
import System from "@/models/system";
import useLegalTasksStore from "@/stores/legalTasksStore";
import DocumentImportButton from "@/components/DocumentImportButton";
import LegalTaskPromptGenerator from "@/components/LegalTasks/LegalTaskPromptGenerator";
import Modal from "@/components/ui/Modal";
import useUser from "@/hooks/useUser";
import {
  autoResizeTextarea,
  formatContentWithTags,
} from "@/utils/legalTaskHelpers";

/**
 * Reusable Legal Task Form Component
 * @param {Object} props
 * @param {string} props.mode - 'create' or 'edit'
 * @param {Object} props.initialData - Initial data for the form (required for edit mode)
 * @param {Function} props.onSubmit - Function to handle form submission
 * @param {Function} props.onClose - Function to handle form closure
 * @param {boolean} props.isLoading - Loading state for the form submission
 */
const LegalTaskForm = ({
  mode = "create",
  initialData = null,
  onSubmit,
  onClose,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const isEditMode = mode === "edit";
  const { user } = useUser();
  const isAdmin = user && user.role === "admin";

  // Form state
  const [name, setName] = useState("");
  const [subCategory, setSubCategory] = useState("");
  const [description, setDescription] = useState("");
  const [legalPrompt, setLegalPrompt] = useState("");
  const [legalTaskType, setLegalTaskType] = useState("");
  const [isUploadingDocx, setIsUploadingDocx] = useState(false);
  const [errors, setErrors] = useState({});
  const [existingCategories, setExistingCategories] = useState([]);
  const [isNewCategory, setIsNewCategory] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState("");

  // State for reviewing generated prompt
  const [generatedPromptTemplate, setGeneratedPromptTemplate] = useState(null);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);

  const docxFileInputRef = useRef(null);

  // Use the centralized legal tasks store
  const { fetchLegalTasks, getUniqueCategories } = useLegalTasksStore();

  // Initialize form data
  useEffect(() => {
    const loadData = async () => {
      // Load categories for create mode first, if needed
      if (!isEditMode) {
        await fetchLegalTasks();
        const uniqueCategories = getUniqueCategories();
        setExistingCategories(uniqueCategories);
      }

      // Load data for edit mode or create mode with initialData
      if (initialData) {
        if (isEditMode) {
          // Edit Mode: Populate other fields from initialData, but require explicit selection for main document
          setSubCategory(initialData.sub_category || "");
          setDescription(initialData.description || "");
          setLegalPrompt(initialData.legal_task_prompt || "");
        } else {
          // Create Mode with initialData: Populate other fields, but require explicit selection for main document
          const uniqueCategories = getUniqueCategories();
          if (initialData.name) {
            if (uniqueCategories.includes(initialData.name)) {
              setIsNewCategory(false);
              setSelectedCategory(initialData.name);
            } else {
              setName(initialData.name);
            }
          }
          if (initialData.subCategory) setSubCategory(initialData.subCategory);
          if (initialData.description) setDescription(initialData.description);
          if (initialData.legalPrompt) setLegalPrompt(initialData.legalPrompt);
        }
      }
    };

    loadData();

    // Auto-resize textareas after initial render
    setTimeout(() => {
      const textareas = document.querySelectorAll("textarea");
      textareas.forEach((textarea) => {
        const event = { target: textarea };
        const maxHeight = textarea.id === "legalPrompt" ? 500 : 200;
        autoResizeTextarea(event, maxHeight);
      });
    }, 0);
  }, [isEditMode, initialData, fetchLegalTasks, getUniqueCategories]);

  // Reset generated prompt template if relevant fields change
  useEffect(() => {
    setGeneratedPromptTemplate(null);
  }, [
    name,
    selectedCategory,
    subCategory,
    description,
    legalTaskType,
    isNewCategory,
  ]);

  // Handle content extraction from document import
  const handleContentExtracted = (content) => {
    const exampleContent = formatContentWithTags(content, t);
    setLegalPrompt((prev) => {
      return prev && prev.trim()
        ? prev + exampleContent
        : exampleContent.trim();
    });

    // Auto-resize textarea after content update
    setTimeout(() => {
      const textarea = document.getElementById("legalPrompt");
      if (textarea) {
        const event = { target: textarea };
        autoResizeTextarea(event, 500);
      }
    }, 0);
  };

  // Handle DOCX file upload
  const handleDocxUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.name.toLowerCase().endsWith(".docx")) {
      showToast(
        t("docx-edit.file-type-note", "Only .docx files are supported"),
        "error"
      );
      return;
    }

    try {
      setIsUploadingDocx(true);
      const response = await System.uploadDocxForTemplate(file);

      if (!response.success) {
        throw new Error(response.error || "Error uploading file");
      }

      // Append the extracted content to legalPrompt with formatting
      const exampleContent = formatContentWithTags(response.data.content, t);

      setLegalPrompt((prev) => {
        return prev && prev.trim()
          ? prev + exampleContent
          : exampleContent.trim();
      });

      // Auto-resize textarea after content update
      setTimeout(() => {
        const textarea = document.getElementById("legalPrompt");
        if (textarea) {
          const event = { target: textarea };
          autoResizeTextarea(event, 500);
        }
      }, 0);

      showToast(
        t("docx-edit.content-extracted", "Content extracted from DOCX file"),
        "success"
      );
    } catch (error) {
      console.error("Error uploading DOCX file:", error);
      showToast(
        t("docx-edit.upload-error", "Error uploading file: ") + error.message,
        "error"
      );
    } finally {
      setIsUploadingDocx(false);
      if (docxFileInputRef.current) {
        docxFileInputRef.current.value = "";
      }
    }
  };

  // Handle prompt generation
  const handlePromptGenerated = (generatedLlmOutput, originalInputTemplate) => {
    // If onTemplateConstructed hasn't set it yet or if it's a different template somehow,
    // ensure generatedPromptTemplate is set with the input template.
    if (
      !generatedPromptTemplate ||
      generatedPromptTemplate !== originalInputTemplate
    ) {
      setGeneratedPromptTemplate(originalInputTemplate);
    }

    // Check if the current prompt contains bracketed examples
    const tagOpen = t(
      "docx-edit.content-examples-tag-open",
      "<CONTENT_EXAMPLES>"
    );

    if (legalPrompt && legalPrompt.includes(tagOpen)) {
      // If there are bracketed examples, insert the generated prompt before them
      const exampleStartIndex = legalPrompt.indexOf(tagOpen);
      const beforeExamples = legalPrompt.substring(0, exampleStartIndex).trim();
      const examples = legalPrompt.substring(exampleStartIndex);

      // If there's already content before the examples, add a line break
      const newPrompt = beforeExamples
        ? `${beforeExamples}\n\n${generatedLlmOutput}\n\n${examples}`
        : `${generatedLlmOutput}\n\n${examples}`;

      setLegalPrompt(newPrompt);
    } else {
      // If there are no bracketed examples, just set the prompt
      setLegalPrompt(generatedLlmOutput);
    }

    // Auto-resize the textarea
    setTimeout(() => {
      const textarea = document.getElementById("legalPrompt");
      if (textarea) {
        const event = { target: textarea };
        autoResizeTextarea(event, 500);
      }
    }, 0);
  };

  // Handle form submission
  const handleSubmit = (event) => {
    event.preventDefault();

    const newErrors = {};

    // Validation for create mode
    if (!isEditMode) {
      if (isNewCategory && !name) {
        newErrors.name = t(
          "document-builder.create-task.validation.category-required"
        );
      }
      if (!isNewCategory && !selectedCategory) {
        newErrors.selectedCategory = t(
          "document-builder.create-task.validation.category-required"
        );
      }
    }

    // Legal task type selection must be made
    if (!legalTaskType) {
      newErrors.legalTaskType = t(
        isEditMode
          ? "document-builder.edit-task.validation.legal-task-type-required"
          : "document-builder.create-task.validation.legal-task-type-required"
      );
    }

    // Common validation for both modes
    if (!subCategory) {
      newErrors.subCategory = t(
        isEditMode
          ? "document-builder.edit-task.validation.subcategory-required"
          : "document-builder.create-task.validation.subcategory-required"
      );
    }
    if (!description) {
      newErrors.description = t(
        isEditMode
          ? "document-builder.edit-task.validation.description-required"
          : "document-builder.create-task.validation.description-required"
      );
    }
    if (!legalPrompt) {
      newErrors.legalPrompt = t(
        isEditMode
          ? "document-builder.edit-task.validation.prompt-required"
          : "document-builder.create-task.validation.prompt-required"
      );
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Prepare data for submission
    const formData = {
      subCategory,
      description,
      legalPrompt,
      legalTaskType,
    };

    // Add mode-specific data
    if (isEditMode) {
      formData.id = initialData.id;
    } else {
      formData.name = isNewCategory ? name : selectedCategory;
    }

    // Submit the form
    onSubmit(formData);
  };

  return (
    <div className="w-full flex flex-col">
      <div className="relative bg-background rounded-lg w-full max-h-[80vh] flex flex-col">
        <div className="flex items-start justify-between py-3 px-4 modal-header">
          <h3 className="text-lg font-semibold text-foreground">
            {isEditMode
              ? `${t("document-builder.edit-task.title")} - ${initialData.name}`
              : t("document-builder.create-task.title")}
          </h3>
          <button
            onClick={onClose}
            type="button"
            className="transition-all duration-300 bg-transparent text-sm p-1.5 ml-auto inline-flex items-center"
          >
            <IoClose className="text-foreground text-lg" />
          </button>
        </div>

        <form
          className="flex flex-col w-full py-2 overflow-auto max-h-[calc(80vh-60px)]"
          onSubmit={handleSubmit}
        >
          {/* Category selection - only for create mode */}
          {!isEditMode && (
            <div className="mb-4 px-5">
              <label
                htmlFor="categoryType"
                className="block text-md font-medium text-foreground mb-2"
              >
                {t("document-builder.create-task.category.type")}
              </label>
              <div className="flex gap-4 mb-4">
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    className="form-radio"
                    checked={isNewCategory}
                    onChange={() => setIsNewCategory(true)}
                  />
                  <span className="ml-2">
                    {t("document-builder.create-task.category.new")}
                  </span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    className="form-radio"
                    checked={!isNewCategory}
                    onChange={() => setIsNewCategory(false)}
                  />
                  <span className="ml-2">
                    {t("document-builder.create-task.category.existing")}
                  </span>
                </label>
              </div>

              {isNewCategory ? (
                <>
                  <label
                    htmlFor="name"
                    className="block text-md font-medium text-foreground"
                  >
                    {t("document-builder.create-task.category.name")}
                  </label>
                  <small className="text-foreground opacity-60 font-[10px]">
                    {t("document-builder.create-task.category.desc")}
                  </small>
                  <input
                    type="text"
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="mt-1 dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2"
                    placeholder={t(
                      "document-builder.create-task.category.placeholder"
                    )}
                  />
                  {errors.name && (
                    <small className="text-red-500 text-[9px] mt-1">
                      {errors.name}
                    </small>
                  )}
                </>
              ) : (
                <>
                  <label
                    htmlFor="selectedCategory"
                    className="block text-md font-medium text-foreground"
                  >
                    {t("document-builder.create-task.category.select")}
                  </label>
                  <select
                    id="selectedCategory"
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="mt-1 dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2"
                  >
                    <option value="">
                      {t(
                        "document-builder.create-task.category.select-placeholder"
                      )}
                    </option>
                    {existingCategories.map((category, index) => (
                      <option key={index} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                  {errors.selectedCategory && (
                    <small className="text-red-500 text-[9px] mt-1">
                      {errors.selectedCategory}
                    </small>
                  )}
                </>
              )}
            </div>
          )}

          {/* Subcategory - common to both modes */}
          <div className="mb-4 px-5">
            <label
              htmlFor="subCategory"
              className="block text-sm font-medium text-foreground"
            >
              {t(
                isEditMode
                  ? "document-builder.edit-task.subcategory.name"
                  : "document-builder.create-task.subcategory.name"
              )}
            </label>
            <small className="text-foreground opacity-60 font-[10px]">
              {t(
                isEditMode
                  ? "document-builder.edit-task.subcategory.desc"
                  : "document-builder.create-task.subcategory.desc"
              )}
            </small>
            <input
              type="text"
              id="subCategory"
              value={subCategory}
              onChange={(e) => setSubCategory(e.target.value)}
              className="mt-1 dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2"
              placeholder={t(
                isEditMode
                  ? "document-builder.edit-task.subcategory.placeholder"
                  : "document-builder.create-task.subcategory.placeholder"
              )}
            />
            {errors.subCategory && (
              <small className="text-red-500 text-[9px] mt-1">
                {errors.subCategory}
              </small>
            )}
          </div>

          {/* Description - common to both modes */}
          <div className="mb-4 px-5">
            <label
              htmlFor="description"
              className="block text-sm font-medium text-foreground"
            >
              {t(
                isEditMode
                  ? "document-builder.edit-task.description.name"
                  : "document-builder.create-task.description.name"
              )}
            </label>
            <small className="text-foreground opacity-60 font-[10px]">
              {t(
                isEditMode
                  ? "document-builder.edit-task.description.desc"
                  : "document-builder.create-task.description.desc"
              )}
            </small>
            <textarea
              id="description"
              value={description}
              onChange={(e) => {
                setDescription(e.target.value);
                autoResizeTextarea(e, 200);
              }}
              onInput={(e) => autoResizeTextarea(e, 200)}
              className="mt-1 dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2 min-h-[70px] max-h-[200px] resize-none [&[data-needs-scroll='true']]:overflow-y-auto [&[data-needs-scroll='false']]:overflow-hidden"
              placeholder={t(
                isEditMode
                  ? "document-builder.edit-task.description.placeholder"
                  : "document-builder.create-task.description.placeholder"
              )}
            ></textarea>
            {errors.description && (
              <small className="text-red-500 text-[9px] mt-1">
                {errors.description}
              </small>
            )}
          </div>

          {/* Legal Task Type Selection */}
          <div className="mb-4 px-5">
            <label
              htmlFor="legalTaskType"
              className="block text-sm font-medium text-foreground"
            >
              {t("document-builder.task-form.legal-task-type-label")}
            </label>
            <div className="mt-1 flex items-start gap-4">
              <select
                id="legalTaskType"
                value={legalTaskType}
                onChange={(e) => setLegalTaskType(e.target.value)}
                className="dark-input-mdl w-64 text-foreground text-sm rounded-lg p-2 flex-shrink-0"
              >
                <option value="">
                  {t("document-builder.task-form.legal-task-type-placeholder")}
                </option>
                <option value="mainDoc">
                  {t("document-builder.task-form.option.mainDoc")}
                </option>
                <option value="noMainDoc">
                  {t("document-builder.task-form.option.noMainDoc")}
                </option>
                <option value="referenceFiles">
                  {t("document-builder.task-form.option.referenceFiles")}
                </option>
              </select>
              <div className="text-xs text-foreground/70 flex-1 pt-2">
                {!legalTaskType && (
                  <p>
                    {t(
                      "document-builder.task-form.legal-task-type-explanation"
                    )}
                  </p>
                )}
                {legalTaskType === "mainDoc" && (
                  <p>
                    {t(
                      "document-builder.task-form.legal-task-type-explanation-mainDoc"
                    )}
                  </p>
                )}
                {legalTaskType === "noMainDoc" && (
                  <p>
                    {t(
                      "document-builder.task-form.legal-task-type-explanation-noMainDoc"
                    )}
                  </p>
                )}
                {legalTaskType === "referenceFiles" && (
                  <p>
                    {t(
                      "document-builder.task-form.legal-task-type-explanation-referenceFiles"
                    )}
                  </p>
                )}
              </div>
            </div>
            {errors.legalTaskType && (
              <small className="text-red-500 text-[9px] mt-1">
                {errors.legalTaskType}
              </small>
            )}
          </div>

          {/* Legal Prompt - common to both modes */}
          <div className="mb-4 px-5">
            <div className="flex justify-between items-center">
              <label
                htmlFor="legalPrompt"
                className="block text-sm font-medium text-foreground"
              >
                {t(
                  isEditMode
                    ? "document-builder.edit-task.prompt.name"
                    : "document-builder.create-task.prompt.name"
                )}
              </label>
              <div className="flex items-center space-x-2">
                {/* Review Generator Prompt Button - Admin Only - appears to the left */}
                {isAdmin && generatedPromptTemplate && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setIsReviewModalOpen(true)}
                    className="flex items-center gap-2"
                    title={t(
                      "document-builder.reviewGeneratorPromptButtonTooltip"
                    )}
                  >
                    <Eye size={16} />
                    {t("document-builder.reviewGeneratorPromptButton")}
                  </Button>
                )}

                {/* Generate Prompt Button - stays in place */}
                <LegalTaskPromptGenerator
                  taskName={
                    isEditMode
                      ? initialData.name
                      : isNewCategory
                        ? name
                        : selectedCategory
                  }
                  subCategory={subCategory}
                  description={description}
                  onPromptGenerated={handlePromptGenerated}
                  onTemplateConstructed={setGeneratedPromptTemplate}
                  size="sm"
                  legalTaskType={legalTaskType}
                />

                {/* Document Import Button */}
                {isEditMode ? (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      docxFileInputRef.current &&
                      docxFileInputRef.current.click()
                    }
                    disabled={isUploadingDocx}
                    className="flex items-center gap-2"
                  >
                    {isUploadingDocx ? (
                      <ImSpinner2 className="animate-spin-fast" />
                    ) : (
                      <UploadSimple />
                    )}
                    {t("docx-edit.upload-button", "Upload DOCX")}
                  </Button>
                ) : (
                  <DocumentImportButton
                    onContentExtracted={handleContentExtracted}
                  />
                )}
              </div>

              {/* Hidden file input for DOCX upload in edit mode */}
              {isEditMode && (
                <input
                  type="file"
                  ref={docxFileInputRef}
                  onChange={handleDocxUpload}
                  style={{ display: "none" }}
                  accept=".docx"
                />
              )}
            </div>
            <small className="text-foreground opacity-60 font-[10px]">
              {t(
                isEditMode
                  ? "document-builder.edit-task.prompt.desc"
                  : "document-builder.create-task.prompt.desc"
              )}
            </small>
            <textarea
              id="legalPrompt"
              value={legalPrompt}
              onChange={(e) => {
                setLegalPrompt(e.target.value);
                autoResizeTextarea(e, 500);
              }}
              onInput={(e) => autoResizeTextarea(e, 500)}
              className="mt-1 dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2 min-h-[70px] max-h-[500px] resize-none overflow-y-auto"
              placeholder={t(
                isEditMode
                  ? "document-builder.edit-task.prompt.placeholder"
                  : "document-builder.create-task.prompt.placeholder"
              )}
            />
            {errors.legalPrompt && (
              <p className="text-red-500 text-[9px] mt-1">
                {errors.legalPrompt}
              </p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex w-full justify-end items-center pt-3 space-x-2 modal-footer pr-4">
            <button
              onClick={onClose}
              type="button"
              className="px-4 py-2 rounded-lg text-foreground transition-all duration-300"
            >
              {t("modals.warning.cancel")}
            </button>
            <button
              type="submit"
              disabled={
                isLoading || !legalTaskType || !description || !legalPrompt
              }
              className={`transition-all duration-300 text-white px-4 py-2
              rounded-lg primary-bg text-sm items-center flex gap-x-2 ${
                isLoading || !legalTaskType || !description || !legalPrompt
                  ? "opacity-80 cursor-not-allowed"
                  : "opacity-100"
              }`}
            >
              {isLoading
                ? t(
                    isEditMode
                      ? "document-builder.edit-task.submitting"
                      : "document-builder.create-task.submitting"
                  )
                : t(
                    isEditMode
                      ? "document-builder.edit-task.submit"
                      : "document-builder.create-task.submit"
                  )}
            </button>
          </div>
        </form>
      </div>

      {/* Modal for Reviewing Generator Prompt */}
      {isAdmin && generatedPromptTemplate && isReviewModalOpen && (
        <Modal
          isOpen={isReviewModalOpen}
          onClose={() => setIsReviewModalOpen(false)}
          title={t("document-builder.reviewGeneratorPromptTitle")}
          className="max-w-2xl"
        >
          <div className="p-4 bg-background-element rounded-md max-h-[60vh] overflow-y-auto">
            <label className="block text-sm font-medium text-foreground mb-1">
              {t("document-builder.reviewPromptLabel")}
            </label>
            <textarea
              readOnly
              value={generatedPromptTemplate}
              className="w-full h-96 p-2 border rounded-md bg-background-input text-foreground text-sm resize-none focus:ring-0 focus:outline-none"
              aria-label={t("document-builder.reviewPromptTextareaLabel")}
            />
          </div>
          <div className="flex justify-end p-4 border-t border-border">
            <Button
              variant="outline"
              onClick={() => setIsReviewModalOpen(false)}
            >
              {t("modals.actions.close", "Close")}
            </Button>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default LegalTaskForm;
