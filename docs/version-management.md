# Version Management System

This document describes the streamlined version management system for the IST Legal platform, which integrates with GitHub's native version tracking capabilities.

## Overview

The platform uses a unified versioning system that combines:
- **Semantic Versioning** (SemVer) with Git tags
- **Automated GitHub Releases**
- **Synchronized package.json versions**
- **Enhanced Docker image tagging**

## 🏗️ **Architecture**

### Version Sources (Priority Order)
1. **Git Tags** - Primary source for released versions (`v1.2.3`)
2. **version.json** - Fallback with metadata and descriptions
3. **package.json** - Backup versioning information
4. **Git Commit SHA** - Development builds identifier

### Files Managed
- `/version.json` - Central version metadata
- `/package.json` - Root package version
- `/server/package.json` - Server component version
- `/collector/package.json` - Collector component version

## 🚀 **Quick Start**

### Check Current Version
```bash
npm run version:info
```

### Create a New Release
```bash
# Method 1: Using the script
npm run version:release 1.2.3

# Method 2: Manual Git tag (triggers automated release)
git tag v1.2.3
git push origin v1.2.3
```

### Bump Version
```bash
# Patch version (1.2.3 → 1.2.4)
npm run version:bump:patch

# Minor version (1.2.3 → 1.3.0)
npm run version:bump:minor

# Major version (1.2.3 → 2.0.0)
npm run version:bump:major
```

## 📋 **Available Commands**

### NPM Scripts
| Command | Description |
|---------|-------------|
| `npm run version:info` | Show current version information |
| `npm run version:sync` | Sync all package.json files to current version |
| `npm run version:bump:patch` | Bump patch version |
| `npm run version:bump:minor` | Bump minor version |
| `npm run version:bump:major` | Bump major version |
| `npm run version:set <version>` | Set specific version |
| `npm run version:release <version>` | Create complete release |
| `npm run version:tag <version>` | Create Git tag only |

### Direct Script Usage
```bash
# Show detailed version information
node scripts/version-management.js info

# Set specific version with description
node scripts/version-management.js set 1.2.3 "Bug fixes and improvements"

# Create release with custom message
node scripts/version-management.js release 1.2.3 "Major feature release"
```

## 🔄 **Release Process**

### Automated Release (Recommended)
1. **Create and push a Git tag:**
   ```bash
   git tag v1.2.3
   git push origin v1.2.3
   ```

2. **GitHub Actions automatically:**
   - Updates all package.json versions
   - Updates version.json with metadata
   - Creates GitHub Release with changelog
   - Builds and tags Docker images
   - Commits version changes back to repository

### Manual Release
1. **Update versions locally:**
   ```bash
   npm run version:release 1.2.3
   ```

2. **Review and commit changes:**
   ```bash
   git add .
   git commit -m "chore: release v1.2.3"
   ```

3. **Push tag and commits:**
   ```bash
   git push origin v1.2.3
   git push
   ```

## 🐳 **Docker Image Tagging**

### Production Images (`main-prod` branch)
- `ghcr.io/repository:prod`
- `ghcr.io/repository:production`
- `ghcr.io/repository:1.2.3` (version tag)

### Stage Images (`main-stage` branch)
- `ghcr.io/repository:stage`

### Development Images
- `ghcr.io/repository:dev` (`main-dev` branch)
- `ghcr.io/repository:develop` (`develop` branch)
- `ghcr.io/repository:main` (`main` branch)

### Release Images (Git tags)
- `ghcr.io/repository:v1.2.3`
- `ghcr.io/repository:latest`

## 📊 **Version Information API**

### Endpoints
- `GET /utils/metrics` - Basic version in system metrics
- `GET /utils/version` - Detailed version information

### Example Response (`/utils/version`)
```json
{
  "current": "1.2.3",
  "sources": {
    "gitTag": "v1.2.3",
    "latestTag": "v1.2.3",
    "versionFile": "1.2.3",
    "packageVersion": "1.2.3",
    "gitCommit": "abc123def"
  },
  "metadata": {
    "description": "Release v1.2.3",
    "timestamp": "2024-01-15T10:30:00Z",
    "commit": "abc123def456789",
    "isRelease": true,
    "isDevelopment": false,
    "isDocker": false
  }
}
```

## 🔧 **Configuration**

### GitHub Actions Secrets
Required for automated releases:
- `GITHUB_TOKEN` - Automatically provided
- `ISTLEGAL_GITHUB_TOKEN` - For Docker registry access

### Workflow Triggers
The release workflow triggers on:
- **Git tags**: `v*.*.*` pattern (e.g., `v1.2.3`)
- **Manual dispatch**: Through GitHub Actions UI

## 📝 **Version.json Structure**

```json
{
  "version": "1.2.3",
  "description": "Release description or changelog",
  "timestamp": "2024-01-15T10:30:00Z",
  "commit": "full-git-commit-sha"
}
```

## 🎯 **Best Practices**

### 1. **Semantic Versioning**
- **Major** (1.0.0): Breaking changes
- **Minor** (0.1.0): New features (backward compatible)
- **Patch** (0.0.1): Bug fixes (backward compatible)

### 2. **Release Descriptions**
Provide meaningful descriptions for releases:
```bash
npm run version:release 1.2.3 "Added user authentication and fixed critical security issue"
```

### 3. **Pre-release Testing**
Test releases in staging environment before production:
1. Deploy to `main-stage` branch
2. Run integration tests
3. Promote to `main-prod` with version tag

### 4. **Rollback Strategy**
If a release needs to be rolled back:
```bash
# Revert to previous version
git checkout v1.2.2
npm run version:set 1.2.2
git commit -m "chore: rollback to v1.2.2"
git push
```

## 🔍 **Troubleshooting**

### Version Mismatch
If versions are out of sync:
```bash
npm run version:sync
```

### Missing Git Tags
To retroactively tag a commit:
```bash
git tag v1.2.3 <commit-hash>
git push origin v1.2.3
```

### Failed Release Build
Check GitHub Actions logs and ensure:
- All tests pass
- No linting errors
- Docker build succeeds
- Required secrets are configured

## 🚀 **Migration from Old System**

### Current State Issues
- `package.json` versions: `0.2.0`
- `version.json` version: `1.1.0`
- No Git tags
- Inconsistent versioning

### Migration Steps
1. **Sync to current version:**
   ```bash
   npm run version:set 1.1.0 "Synchronized version across all components"
   ```

2. **Create initial Git tag:**
   ```bash
   git tag v1.1.0
   git push origin v1.1.0
   ```

3. **Commit synchronized versions:**
   ```bash
   git add .
   git commit -m "chore: synchronize versions to v1.1.0"
   git push
   ```

4. **Future releases use new system:**
   ```bash
   npm run version:bump:patch  # Creates 1.1.1
   ```

## 📚 **Additional Resources**

- [Semantic Versioning Specification](https://semver.org/)
- [GitHub Releases Documentation](https://docs.github.com/en/repositories/releasing-projects-on-github)
- [Docker Image Tagging Best Practices](https://docs.docker.com/develop/dev-best-practices/)
