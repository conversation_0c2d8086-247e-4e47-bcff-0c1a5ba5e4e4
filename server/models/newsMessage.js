const prisma = require("../utils/prisma");

// Priority ranking for correct sorting order
const PRIORITY_RANKS = {
  urgent: 1,
  high: 2,
  medium: 3,
  low: 4,
};

// Custom comparator function for priority sorting
const sortByPriority = (a, b) => {
  const aRank = PRIORITY_RANKS[a.priority] || 999;
  const bRank = PRIORITY_RANKS[b.priority] || 999;

  // Primary sort by priority (lower rank number = higher priority)
  if (aRank !== bRank) {
    return aRank - bRank;
  }

  // Secondary sort by createdAt (newer first)
  return new Date(b.createdAt) - new Date(a.createdAt);
};

const NewsMessage = {
  // Create a new news message
  create: async function ({
    title,
    content,
    priority = "medium",
    targetRoles = null,
    expiresAt = null,
    createdBy,
  }) {
    try {
      const newsMessage = await prisma.news_messages.create({
        data: {
          title,
          content,
          priority,
          target_roles: targetRoles ? JSON.stringify(targetRoles) : null,
          expires_at: expiresAt,
          created_by: createdBy,
        },
      });
      return { newsMessage, message: null };
    } catch (error) {
      console.error("FAILED TO CREATE NEWS MESSAGE.", error.message);
      return { newsMessage: null, message: error.message };
    }
  },

  // Get all active news messages
  getActive: async function () {
    try {
      const newsMessages = await prisma.news_messages.findMany({
        where: {
          is_active: true,
          OR: [{ expires_at: null }, { expires_at: { gt: new Date() } }],
        },
        orderBy: { createdAt: "desc" },
      });

      // Sort by priority using custom comparator
      return newsMessages.sort(sortByPriority);
    } catch (error) {
      console.error("FAILED TO GET ACTIVE NEWS MESSAGES.", error.message);
      return [];
    }
  },

  // Get all news messages (for admin)
  getAll: async function () {
    try {
      const newsMessages = await prisma.news_messages.findMany({
        orderBy: { createdAt: "desc" },
      });
      return newsMessages;
    } catch (error) {
      console.error("FAILED TO GET ALL NEWS MESSAGES.", error.message);
      return [];
    }
  },

  // Get news messages for a specific user (unread only)
  getUnreadForUser: async function (userId) {
    try {
      const user = await prisma.users.findUnique({
        where: { id: userId },
        select: { createdAt: true, role: true },
      });

      if (!user) return [];

      // Get dismissed news IDs for this user
      const dismissedNews = await prisma.user_news_dismissals.findMany({
        where: { user_id: userId },
        select: { news_id: true },
      });

      const dismissedLocalIds = dismissedNews
        .filter((d) => d.news_id.startsWith("local-"))
        .map((d) => parseInt(d.news_id.replace("local-", "")))
        .filter((id) => !isNaN(id));

      const newsMessages = await prisma.news_messages.findMany({
        where: {
          is_active: true,
          createdAt: { gt: user.createdAt },
          OR: [{ expires_at: null }, { expires_at: { gt: new Date() } }],
          NOT: {
            id: { in: dismissedLocalIds },
          },
        },
        orderBy: { createdAt: "desc" },
      });

      // Filter by target roles if specified
      const filteredMessages = newsMessages.filter((message) => {
        if (!message.target_roles) return true;

        try {
          const targetRoles = JSON.parse(message.target_roles);
          return targetRoles.includes(user.role);
        } catch (error) {
          console.error("Error parsing target_roles:", error);
          return true; // Include message if parsing fails
        }
      });

      // Sort by priority using custom comparator
      return filteredMessages.sort(sortByPriority);
    } catch (error) {
      console.error("FAILED TO GET UNREAD NEWS FOR USER.", error.message);
      return [];
    }
  },

  // Update a news message
  update: async function (id, updates) {
    try {
      // Map camelCase frontend fields to snake_case database fields
      const dbUpdates = {};

      // Map basic fields that don't need transformation
      if (updates.title !== undefined) dbUpdates.title = updates.title;
      if (updates.content !== undefined) dbUpdates.content = updates.content;
      if (updates.priority !== undefined) dbUpdates.priority = updates.priority;
      if (updates.is_active !== undefined)
        dbUpdates.is_active = updates.is_active;
      if (updates.isActive !== undefined)
        dbUpdates.is_active = updates.isActive;

      // Map camelCase to snake_case fields
      if (updates.targetRoles !== undefined) {
        dbUpdates.target_roles = updates.targetRoles
          ? JSON.stringify(updates.targetRoles)
          : null;
      }
      if (updates.target_roles !== undefined) {
        dbUpdates.target_roles = updates.target_roles
          ? JSON.stringify(updates.target_roles)
          : null;
      }

      if (updates.expiresAt !== undefined) {
        dbUpdates.expires_at = updates.expiresAt;
      }
      if (updates.expires_at !== undefined) {
        dbUpdates.expires_at = updates.expires_at;
      }

      const newsMessage = await prisma.news_messages.update({
        where: { id: parseInt(id) },
        data: dbUpdates,
      });
      return { newsMessage, message: null };
    } catch (error) {
      console.error("FAILED TO UPDATE NEWS MESSAGE.", error.message);
      return { newsMessage: null, message: error.message };
    }
  },

  // Delete a news message
  delete: async function (id) {
    try {
      await prisma.news_messages.delete({
        where: { id: parseInt(id) },
      });
      return { success: true, message: null };
    } catch (error) {
      console.error("FAILED TO DELETE NEWS MESSAGE.", error.message);
      return { success: false, message: error.message };
    }
  },

  // Dismiss a news message for a user (unified for both local and system news)
  dismiss: async function (userId, newsId, isSystemNews = false) {
    try {
      // Format the news ID consistently
      const formattedNewsId = isSystemNews
        ? newsId // System news IDs are used as-is (e.g., "system-welcome-2024")
        : `local-${newsId}`; // Local news IDs are prefixed with "local-"

      const dismissal = await prisma.user_news_dismissals.upsert({
        where: {
          user_id_news_id: {
            user_id: userId,
            news_id: formattedNewsId,
          },
        },
        update: {
          dismissed_at: new Date(),
        },
        create: {
          user_id: userId,
          news_id: formattedNewsId,
          dismissed_at: new Date(),
        },
      });

      return { dismissal, message: null };
    } catch (error) {
      console.error("FAILED TO DISMISS NEWS.", error.message);
      return { dismissal: null, message: error.message };
    }
  },

  // Get dismissed news IDs for a user (unified for both local and system news)
  getDismissedNewsIds: async function (userId) {
    try {
      const dismissals = await prisma.user_news_dismissals.findMany({
        where: { user_id: userId },
        select: { news_id: true },
      });

      return {
        local: dismissals
          .filter((d) => d.news_id.startsWith("local-"))
          .map((d) => parseInt(d.news_id.replace("local-", "")))
          .filter((id) => !isNaN(id)),
        system: dismissals
          .filter((d) => !d.news_id.startsWith("local-"))
          .map((d) => d.news_id),
      };
    } catch (error) {
      console.error("FAILED TO GET DISMISSED NEWS IDS.", error.message);
      return { local: [], system: [] };
    }
  },

  // Get analytics for a news message
  getAnalytics: async function (newsId) {
    try {
      const formattedNewsId = `local-${newsId}`;

      const totalDismissals = await prisma.user_news_dismissals.count({
        where: { news_id: formattedNewsId },
      });

      return {
        totalDismissals,
        // Since we only track dismissals now, viewed = dismissed
        totalViews: totalDismissals,
        dismissed: totalDismissals,
        viewed: totalDismissals,
      };
    } catch (error) {
      console.error("FAILED TO GET NEWS ANALYTICS.", error.message);
      return { totalViews: 0, dismissed: 0, viewed: 0, totalDismissals: 0 };
    }
  },

  // Legacy methods for backward compatibility (now use the unified dismiss method)

  // Mark as viewed - now just an alias for dismiss since we only track dismissals
  markAsViewed: async function (userId, newsId) {
    return this.dismiss(userId, newsId, false);
  },

  // Dismiss system news - now uses the unified dismiss method
  dismissSystemNews: async function (userId, systemNewsId) {
    const result = await this.dismiss(userId, systemNewsId, true);
    return result.dismissal;
  },

  // Get dismissed system news IDs for a user
  getDismissedSystemNewsIds: async function (userId) {
    try {
      const dismissals = await prisma.user_news_dismissals.findMany({
        where: {
          user_id: userId,
          news_id: { not: { startsWith: "local-" } },
        },
        select: { news_id: true },
      });

      return dismissals.map((d) => d.news_id);
    } catch (error) {
      console.error("FAILED TO GET DISMISSED SYSTEM NEWS IDS.", error.message);
      return [];
    }
  },

  // Helper function to create a hash from a string (kept for backward compatibility)
  hashString: function (str) {
    let hash = 0;
    if (str.length === 0) return hash;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  },

  // Get all active news for a user (including dismissed ones) with dismissal status
  getAllActiveForUser: async function (userId) {
    try {
      const user = await prisma.users.findUnique({
        where: { id: userId },
        select: { createdAt: true, role: true },
      });

      if (!user) return [];

      // Get dismissed news IDs for this user
      const dismissedNews = await prisma.user_news_dismissals.findMany({
        where: { user_id: userId },
        select: { news_id: true, dismissed_at: true },
      });

      const dismissedLocalIds = dismissedNews
        .filter((d) => d.news_id.startsWith("local-"))
        .map((d) => parseInt(d.news_id.replace("local-", "")))
        .filter((id) => !isNaN(id));

      // Create dismissal lookup maps
      const dismissalMap = {};
      dismissedNews.forEach((d) => {
        dismissalMap[d.news_id] = d.dismissed_at;
      });

      // Get all active database news (regardless of dismissal status)
      const newsMessages = await prisma.news_messages.findMany({
        where: {
          is_active: true,
          createdAt: { gt: user.createdAt },
          OR: [{ expires_at: null }, { expires_at: { gt: new Date() } }],
        },
        orderBy: { createdAt: "desc" },
      });

      // Filter by target roles if specified and add dismissal status
      const filteredMessages = newsMessages
        .filter((message) => {
          if (!message.target_roles) return true;

          try {
            const targetRoles = JSON.parse(message.target_roles);
            return targetRoles.includes(user.role);
          } catch (error) {
            console.error("Error parsing target_roles:", error);
            return true; // Include message if parsing fails
          }
        })
        .map((message) => {
          const localNewsId = `local-${message.id}`;
          const isDismissed = dismissedLocalIds.includes(message.id);
          return {
            ...message,
            isSystemNews: false,
            isDismissed,
            dismissedAt: isDismissed ? dismissalMap[localNewsId] : null,
          };
        });

      // Sort by priority using custom comparator
      return filteredMessages.sort(sortByPriority);
    } catch (error) {
      console.error("FAILED TO GET ALL ACTIVE NEWS FOR USER.", error.message);
      return [];
    }
  },
};

module.exports = { NewsMessage };
