export default {
  // =========================
  // ONBOARDING
  // =========================
  onboarding: {
    welcome: "Welcome to",
    "get-started": "Get started",
    "llm-preference": {
      title: "LLM Preference",
      description:
        "ISTLLM can work with many LLM providers. This will be the service which handles chatting.",
      "LLM-search": "Search LLM providers",
    },
    "user-setup": {
      title: "User Setup",
      description: "Configure your user settings.",
      "sub-title": "How many people will be using your instance?",
      "single-user": "Just me",
      "multiple-user": "My team",
      "setup-password": "Would you like to set up a password?",
      "password-requirment": "Passwords must be at least 8 characters.",
      "save-password":
        "It's important to save this password because there is no recovery method.",
      "password-label": "Instance Password",
      username: "Admin account email",
      password: "Admin account password",
      "account-requirment":
        "Email must be valid and only contain lowercase letters, numbers, underscores, and hyphens with no spaces. Password must be at least 8 characters long.",
      "password-note":
        "By default, you will be the only admin. Once onboarding is completed you can create and invite others to be users or admins. Do not lose your password as only admins can reset passwords.",
    },
    "data-handling": {
      title: "Data Handling & Privacy",
      description:
        "We are committed to transparency and control when it comes to your personal data.",
      "llm-label": "LLM Selection",
      "embedding-label": "Embedding Preference",
      "database-lablel": "Vector Database",
      "reconfigure-option":
        "These settings can be reconfigured at any time in the settings.",
    },
    survey: {
      title: "Welcome to IST Legal LLM",
      description: "Help us make IST Legal LLM built for your needs. Optional.",
      email: "What's your email?",
      usage: "What will you use the platform for?",
      work: "For work",
      "personal-use": "For my personal use",
      other: "Other",
      comment: "Any comments for the team?",
      optional: "(Optional)",
      feedback: "Thank you for your feedback!",
    },
    button: { yes: "Yes", no: "No", "skip-survey": "Skip Survey" },
    placeholder: {
      "admin-password": "Your admin password",
      "admin-username": "Your admin email",
      "email-example": "<EMAIL>",
      comment:
        "If you have any questions or comments right now, you can leave them here and we will get back to you. You can <NAME_EMAIL>",
    },
  },
};
