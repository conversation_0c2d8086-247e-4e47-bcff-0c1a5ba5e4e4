export default {
  // =========================
  // MODALE (DOCUMENT & CONNECTORS)
  // =========================
  modale: {
    document: {
      title: "Meine Dokumente",
      document: "Dokumente",
      search: "Nach Dokument suchen",
      folder: "Neuer Ordner",
      name: "Name",
      empty: "Keine Dokumente",
      "move-workspace": "In Arbeitsbereich verschieben",
      "doc-processor": "Dokumentprozessor",
      "processor-offline":
        "Der Dokumentprozessor ist derzeit offline. Bitte versuchen Sie es später erneut.",
      "drag-drop": "Zum Hochladen klicken oder Dateien hierher ziehen",
      "supported-files": "Unterstützte Dateien: PDF",
      "submit-link": "Oder senden Sie einen Link zu einem Dokument",
      fetch: "Abrufen",
      fetching: "Abrufen...",
      "file-desc":
        "Hinweis: Das Dokument wird verarbeitet und Ihrem Arbeitsbereich hinzugefügt. Dies kann einen Moment dauern.",
      cost: "*Einmalige Kosten für Embedding",
      "save-embed": "Speichern und einbetten",
      "failed-uploads": "Fehlgeschlagene Uploads",
      "loading-message": "Dies kann bei großen Dokumenten eine Weile dauern",
      "uploading-file": "Datei(en) werden hochgeladen...",
      "scraping-link": "Link wird verarbeitet...",
      "moving-documents":
        "{{count}} Dokumente werden verschoben. Bitte warten.",
      "exceeds-prompt-limit":
        "Hinweis: Der hochgeladene Inhalt übersteigt das, was in einer Anfrage Platz findet. Das System wird Anfragen durch mehrere Prompts verarbeiten, was die Zeit für die Generierung der Antwort verlängert und die Präzision beeinträchtigen kann.",
    },
    connectors: {
      title: "Daten-Connectoren",
      search: "Nach Daten-Connectoren suchen",
      empty: "Keine Daten-Connectoren gefunden.",
    },
    "justify-betweening": "Verarbeitung...",
  },
  // =========================
  // MODALS
  // =========================
  modals: {
    warning: {
      title: "Warnung",
      proceed: "Bist du sicher, dass du fortfahren möchtest?",
      cancel: "Abbrechen",
      confirm: "Bestätigen",
      "got-it": "Okay, verstanden",
    },
  },
  // =========================
  // SHARE MODAL
  // =========================
  shareModal: {
    title: "Teilen {type}",
    titleWorkspace: "Arbeitsbereich teilen",
    titleThread: "Thread teilen",
    shareWithUsers: "Mit Benutzern teilen",
    shareWithOrg: "Mit der gesamten Organisation teilen",
    searchUsers: "Benutzer suchen...",
    noUsersFound: "Keine Benutzer gefunden",
    loadingUsers: "Benutzer werden geladen...",
    errorLoadingUsers: "Fehler beim Laden der Benutzer",
    errorLoadingStatus: "Fehler beim Laden des Freigabestatus",
    userAccessGranted: "Benutzerzugriff erfolgreich gewährt",
    userAccessRevoked: "Benutzerzugriff erfolgreich widerrufen",
    orgAccessGranted: "Organisationszugriff erfolgreich gewährt",
    orgAccessRevoked: "Organisationszugriff erfolgreich widerrufen",
    errorUpdateUser: "Fehler beim Aktualisieren des Benutzerzugriffs",
    errorUpdateOrg: "Fehler beim Aktualisieren des Organisationszugriffs",
    errorNoOrg:
      "Teilen nicht möglich: Konto ist mit keiner Organisation verknüpft",
    close: "Schließen",
    grantAccess: "Zugriff gewähren",
    revokeAccess: "Zugriff widerrufen",
  },
  // =========================
  // VARIOUS MODALS AND NEW FEATURES
  // =========================

  // Organization & Organizations translations for user management
  organization: {
    label: "Organisation",
    select: "-- Organisation auswählen --",
    none: "Keine",
    "create-new": "+ Neue Organisation erstellen",
    "new-name": "Name der neuen Organisation",
    "new-name-ph": "Geben Sie den Namen der neuen Organisation ein",
  },
  organizations: {
    "fetch-error": "Fehler beim Abrufen der Organisationen",
  },
  // =========================
  // REQUEST LEGAL ASSISTANCE
  // =========================
  "request-legal-assistance": {
    title: "Rechtshilfe anfordern",
    description:
      "Konfigurieren Sie die Sichtbarkeit der Schaltfläche zum Anfordern von Rechtshilfe.",
    enable: "Rechtshilfeanfrage aktivieren",
    "law-firm-name": "Name der Anwaltskanzlei",
    "law-firm-placeholder": "Geben Sie den Namen der Anwaltskanzlei ein",
    "law-firm-help":
      "Name der Anwaltskanzlei, die Rechtshilfeanfragen bearbeiten wird",
    email: "E-Mail für Rechtshilfe",
    "email-placeholder": "Geben Sie die E-Mail-Adresse für Rechtshilfe ein",
    "email-help": "E-Mail-Adresse, an die Rechtshilfeanfragen gesendet werden",
    "settings-saved": "Rechtshilfe-Einstellungen erfolgreich gespeichert",
    "save-error": "Fehler beim Speichern der Rechtshilfe-Einstellungen",
    status: "Schaltfläche für Rechtshilfe: ",
    "load-error": "Fehler beim Laden der Rechtshilfe-Einstellungen",
    "save-button": "Änderungen speichern",
    request: {
      title: "Rechtshilfe anfordern",
      description:
        "Senden Sie eine Anfrage an {{lawFirmName}} für Rechtshilfe, Abschluss von Recherchen oder andere Beratungen. Sie werden per E-Mail benachrichtigt, wenn die Anfrage bearbeitet wurde.",
      button: "Rechtshilfe anfordern",
      message: "Nachricht",
      "message-placeholder":
        "Geben Sie spezifische Anweisungen oder Informationen für das Rechtshilfeteam ein",
      send: "Anfrage senden",
      cancel: "Abbrechen",
      error: "Fehler beim Senden der Rechtshilfeanfrage",
      success: "Rechtshilfeanfrage erfolgreich gesendet",
      submitting: "Anfrage wird gesendet...",
      submit: "Anfrage einreichen",
      partyName: "Parteienname",
      partyOrgId: "Organisationsnummer für Partei",
      partyNameRequired: "Parteienname ist erforderlich",
      partyOrgIdRequired: "Organisationsnummer für Partei ist erforderlich",
      partyNamePlaceholder: "Geben Sie den Namen Ihrer Organisation ein",
      partyOrgIdPlaceholder: "Geben Sie Ihre Organisationnummer ein",
      opposingPartyName: "Name der Gegenseite (falls relevant)",
      opposingPartyOrgId: "Organisationsnummer für Gegenseite (falls bekannt)",
      opposingPartyNamePlaceholder: "Geben Sie den Namen der Gegenseite ein",
      opposingPartyOrgIdPlaceholder:
        "Geben Sie die Organisationnummer der Gegenseite ein",
    },
  },
  // =========================
  // WORKSPACE SELECTOR MODAL
  // =========================
  workspaceSelector: {
    chooseWorkspace: "Starten Sie einen neuen Chat",
    selectAiType:
      "Auswahl von Modul und Arbeitsbereich für die Initiierung der Funktion",
    cloudAiDescription:
      "Verwendet ein Cloud-basiertes KI-Modell für Chat und Frage-Antwort. Ihre Dokumente werden sicher in der Cloud verarbeitet und gespeichert.",
    localAiDescription:
      "Verwendet ein lokales KI-Modell für Chat und Dokumentenerstellung. Ihre Dokumente werden auf Ihrem lokalen Computer verarbeitet und gespeichert.",
    cloudAiDescriptionTemplateFeature:
      "Erstellen Sie eine Vorlage in einem bestehenden Arbeitsbereich mit juristischen Daten, die Vorlage kann je nach Anfrage juristische Daten abrufen.",
    localAiDescriptionTemplateFeature:
      "Erstellen Sie eine Vorlage in Ihrem eigenen Arbeitsbereich und nutzen Sie vollständig lokale KI, wenn diese auf dem Server aktiviert ist.",
    cloudAiDescriptionComplexFeature:
      "Die Erstellung komplexer Dokumente ist für diese Arbeitsbereiche nicht verfügbar, da der Benutzer vor dem Start Dokumente in den Arbeitsbereich hochladen muss",
    localAiDescriptionComplexFeature:
      "Wählen Sie einen Ihrer Arbeitsbereiche für die Initiierung einer juristischen Aufgabe und stellen Sie sicher, dass die erforderlichen Dokumente vor dem Start in den Arbeitsbereich hochgeladen werden.",
    newWorkspaceComplexTaskInfo:
      "Wenn Sie einen neuen Arbeitsbereich erstellen, gelangen Sie zur Upload-Ansicht, um alle erforderlichen Dokumente hochzuladen, was für die Durchführung einer juristischen Aufgabendokumentgenerierung notwendig ist.",
    selectExistingWorkspace: "Wählen Sie einen vorhandenen Arbeitsbereich",
    selectExistingDocumentDraftingWorkspace:
      "Wählen Sie einen vorhandenen Dokumenterstellungs-Arbeitsbereich",
    orCreateNewBelow:
      "Oder erstellen Sie unten einen neuen Dokumenterstellungs-Arbeitsbereich.",
    newWorkspaceName:
      "Geben Sie einen Namen für Ihren neuen Arbeitsbereich ein",
    newWorkspaceNameOptional:
      "Geben Sie einen Namen für Ihren neuen Arbeitsbereich ein (wenn Sie keinen vorhandenen Arbeitsbereich verwenden)",
    workspaceNamePlaceholder: "z.B.: Mein neuer Arbeitsbereich",
    next: "Weiter",
    pleaseSelectWorkspace: "Bitte wählen Sie einen Arbeitsbereich aus.",
    workspaceNameRequired: "Name des Arbeitsbereichs ist erforderlich.",
    workspaceNameOrExistingWorkspaceRequired:
      "Bitte geben Sie einen Arbeitsbereichsnamen ein oder wählen Sie einen vorhandenen Arbeitsbereich aus.",
    workspaceNameMustBeMoreThanOneCharacter:
      "Der Name des Arbeitsbereichs muss mehr als ein Zeichen umfassen.",
    noWorkspacesAvailable: "Keine Arbeitsbereiche verfügbar",
    selectWorkspacePlaceholder: "Bitte wählen",
    featureUnavailable: {
      title: "Funktion ist nicht verfügbar",
      description:
        "Diese Funktion ist für Ihr Konto nicht aktiviert oder in diesem System deaktiviert. Bitte kontaktieren Sie einen Administrator, um diese Funktion bei Bedarf zu aktivieren.",
      close: "Schließen",
    },
    createNewWorkspace: {
      title: "Neuen Dokumenterstellungs-Arbeitsbereich erstellen",
      description:
        "Dies erstellt einen neuen Arbeitsbereich speziell für die komplexe Dokumenterstellung mit der ausgewählten Vorlage.",
      workspaceName: "Name des Arbeitsbereichs",
      create: "Arbeitsbereich erstellen",
    },
    selectExisting: {
      title: "Arbeitsbereich für Rechtsfragen auswählen",
      description:
        "Wählen Sie einen vorhandenen Arbeitsbereich aus, um eine Rechtsfragen-Chat-Sitzung zu starten.",
      selectWorkspace: "Arbeitsbereich auswählen",
    },
  },
  // =========================
  // CANVAS CHAT
  // =========================
  canvasChat: {
    title: "Canvas",
    "input-placeholder": "Rechtliche Informationen anfragen",
    chatboxinstruction: "Passe die Antwort nach Bedarf an",
    explanation:
      "Dieses Tool ist für die AI-Bearbeitung der Antwort in verschiedenen Weisen. Die Quellen für die zugrunde liegende Antwort werden angewendet, was bedeutet, dass du zusätzliche Klärungen anfragen kannst, indem du die gleichen Quellenmaterialien verwendest, die für die zugrunde liegende Antwort verwendet wurden.",
    editAnswer: "Antwort bearbeiten",
  },
  // =========================
  // USER MENU
  // =========================
  "user-menu": {
    edit: "Konto bearbeiten",
    profile: "Profilbild",
    size: "800 x 800",
    "remove-profile": "Profilbild entfernen",
    username: "E-Mail-Adresse",
    "username-placeholder": "E-Mail-Adresse eingeben",
    "new-password": "Neues Passwort",
    "new-password-placeholder": "Neues Passwort",
    cancel: "Abbrechen",
    update: "Konto aktualisieren",
    language: "Bevorzugte Sprache",
    email: "E-Mail-Adresse",
    "email-placeholder": "E-Mail-Adresse eingeben",
    "style-alignment": "Persönliche Stilanpassung",
    "style-upload": "Beispiele eigener Dokumente hochladen",
    "style-generate": "Stilprofil aus hochgeladenen Dokumenten erstellen",
    "style-enabled": "Persönlichen Stil aktivieren",
    "style-profile": "Stilprofil",
    "style-profiles": "Stilprofile",
    "style-generated-success": "Stilprofil erfolgreich generiert",
    "style-generation-failed": "Stilprofil-Generierung fehlgeschlagen",
    "view-prompt": "Generierungsaufforderung anzeigen (Admin)",
    "prompt-modal-title": "Stilgenerierungsaufforderung",
    "prompt-loading": "Aufforderung wird geladen...",
    "prompt-error": "Aufforderung konnte nicht geladen werden",
    "prompt-close": "Schließen",
    "edit-profile": "Profil bearbeiten",
    "edit-profile-name": "Profilname",
    "edit-profile-instructions": "Stilanweisungen",
    "save-changes": "Änderungen speichern",
    "cancel-edit": "Abbrechen",
    "profile-updated-success": "Profil erfolgreich aktualisiert",
    "profile-update-failed": "Profil-Aktualisierung fehlgeschlagen",
    "profile-name-required": "Profilname ist erforderlich",
  },
  // =========================
  // STYLE UPLOAD
  // =========================
  "style-upload": {
    "manage-files": "Stilreferenzdateien verwalten",
    "manage-files-description":
      "Hier können Sie Dateien hochladen, die Sie verfasst haben, um eine personalisierte Stilanpassung zu generieren. Wenn dies aktiv ist, werden die Ausgaben der Plattform besser mit Ihrem persönlichen professionellen Schreibstil übereinstimmen.",
    "file-already-exists": "Datei existiert bereits",
    "files-uploaded": "Dateien erfolgreich hochgeladen",
    "remove-file": "Datei entfernen",
    "add-more": "Weitere Dateien hinzufügen",
    "clear-all": "Alle löschen",
    "no-files": "Bitte laden Sie mindestens eine Datei hoch",
    "token-usage": "Token-Nutzung",
    "token-limit-exceeded":
      "Token-Limit überschritten. Ihre Dateien enthalten {{current}} Tokens, aber nur {{available}} Tokens sind für die Verarbeitung verfügbar.",
    "token-limit-warning":
      "Ihre Dateien überschreiten das Token-Limit. Bitte entfernen Sie Inhalte oder Dateien, um fortzufahren.",
    "context-window-info":
      "Kontextfenster: {{contextWindow}} Tokens ({{reservedTokens}} für Verarbeitung reserviert)",
  },
  // =========================
  // VALIDATE RESPONSE
  // =========================
  "validate-response": {
    title: "Validierungsergebnis",
    "toast-fail": "Antwort konnte nicht validiert werden",
    validating: "Antwort wird validiert",
    button: "Antwort validieren",
    "adjust-prefix":
      "Übernehme alle angegebenen Änderungen an der Antwort basierend auf diesem Feedback: ",
    "adjust-button": "Vorgeschlagene Änderungen anwenden",
  },
};
