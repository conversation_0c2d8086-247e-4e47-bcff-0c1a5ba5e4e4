-- Migration to set LLM provider defaults to "system-standard"
-- This ensures that prompt upgrade, validation, and template LLMs have explicit defaults
-- Only affects installations where these values are not explicitly set

INSERT INTO system_settings (label, value)
SELECT 'LLMProvider_PU', 'system-standard'
WHERE NOT EXISTS (
    SELECT 1 FROM system_settings WHERE label = 'LLMProvider_PU'
);

INSERT INTO system_settings (label, value)
SELECT 'LLMProvider_VA', 'system-standard'
WHERE NOT EXISTS (
    SELECT 1 FROM system_settings WHERE label = 'LLMProvider_VA'
);

INSERT INTO system_settings (label, value)
SELECT 'LLMProvider_TM', 'system-standard'
WHERE NOT EXISTS (
    SELECT 1 FROM system_settings WHERE label = 'LLMProvider_TM'
);
