-- Remove the requiresMainDocument column and add legalTaskType column
PRAG<PERSON> defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;

-- Create new Category table with legalTaskType instead of requiresMainDocument
CREATE TABLE IF NOT EXISTS "new_Category" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "name" TEXT NOT NULL,
    "sub_category" TEXT,
    "description" TEXT,
    "legal_task_prompt" TEXT,
    "legalTaskType" TEXT NOT NULL DEFAULT 'noMainDoc',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- Copy data from old table to new table
-- Map requiresMainDocument values to legalTaskType values
INSERT INTO "new_Category" ("id", "name", "sub_category", "description", "legal_task_prompt", "legalTaskType", "createdAt", "updatedAt")
SELECT
    "id",
    "name",
    "sub_category",
    "description",
    "legal_task_prompt",
    CASE
        WHEN "requiresMainDocument" = 1 THEN 'mainDoc'
        ELSE 'noMainDoc'
    END,
    "createdAt",
    "updatedAt"
FROM "Category";

-- Drop the old table and rename the new one
DROP TABLE "Category";
ALTER TABLE "new_Category" RENAME TO "Category";

PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
