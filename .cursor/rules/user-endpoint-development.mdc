---
description:
globs:
alwaysApply: false
---
# User Endpoint Development Guidelines

## Overview

This rule provides specific guidelines for developing user-related endpoints in the ISTLegal application, based on lessons learned from troubleshooting common issues.

## Development Environment

### Auto-Reloading Servers

**Important**: In development, all servers automatically restart when code changes are detected. You do **NOT** need to manually restart servers to test your endpoint changes.

- **Backend Server**: Automatically restarts on changes to `server/` files
- **Frontend Development Server**: Automatically reloads on changes to `frontend/` files
- **Testing Workflow**: Save your files → servers auto-restart → test your changes immediately

**What this means for endpoint development:**
- Make changes to endpoint files and save
- Server will automatically restart and pick up your changes
- Test your endpoints immediately after the auto-restart completes
- No need to run terminal commands to restart servers
- Focus on coding and testing rather than server management

## Critical Requirements

### 1. User ID Validation

**ALWAYS validate user IDs before any database operation.** This prevents Prisma validation errors and security vulnerabilities.

#### Required Validation Pattern

```javascript
// In any function that accepts user IDs
function validateUserId(id) {
  // Check for null, undefined, or NaN
  if (id === null || id === undefined || (typeof id === 'number' && isNaN(id))) {
    console.log(`Invalid ID provided: ${id} (type: ${typeof id})`);
    return null;
  }

  // Convert string IDs to numbers if valid
  if (typeof id === 'string') {
    const parsedId = parseInt(id);
    if (isNaN(parsedId)) {
      console.log(`Invalid string ID: ${id}`);
      return null;
    }
    return parsedId;
  }

  // Return valid number IDs as-is
  if (typeof id === 'number' && !isNaN(id)) {
    return id;
  }

  return null;
}
```

#### Model Layer Validation

```javascript
// In server/models/user.js
static async get(clause = {}) {
  // Validate ID if present
  if (clause.id !== undefined) {
    const validId = validateUserId(clause.id);
    if (validId === null) {
      return null;
    }
    clause.id = validId;
  }

  try {
    const user = await prisma.users.findFirst({ where: clause });
    return user || null;
  } catch (error) {
    console.error('Database error in User.get:', error);
    return null;
  }
}
```

#### JWT Token Validation

```javascript
// In server/utils/http/index.js
function userFromSession(request, response = null) {
  const token = request.cookies?.token || request.headers?.authorization?.split(' ')[1];
  if (!token) return null;

  try {
    const valid = jwt.verify(token, process.env.JWT_SECRET);
    if (!valid?.id) return null;

    // Validate user ID from token
    const userId = validateUserId(valid.id);
    if (userId === null) {
      console.log(`userFromSession: Invalid user ID in token: ${valid.id}`);
      return null;
    }

    return User.get({ id: userId });
  } catch (error) {
    console.error('JWT verification error:', error);
    return null;
  }
}
```

### 2. Route Registration Order

**CRITICAL:** Register specific routes before general ones in `server/index.js`.

#### Correct Registration Pattern

```javascript
// server/index.js

// 1. Register most specific user endpoints first
app.use("/api", userCustomSystemPromptEndpoints); // /user/custom-system-prompt

// 2. Register feature-specific endpoints
app.use("/api", userProfileEndpoints); // /user/profile/*
app.use("/api", userSettingsEndpoints); // /user/settings/*

// 3. Register general endpoints last (these contain catch-all routes)
app.use("/api", developerEndpoints); // /user/:id (catch-all)
app.use("/api", adminEndpoints); // /admin/:action
```

#### Why Order Matters

Express.js matches routes in the order they are registered. If you register `/user/:id` before `/user/custom-system-prompt`, the `:id` parameter will catch `custom-system-prompt` as an ID value.

### 3. Endpoint Structure

#### Standard User Endpoint Pattern

```javascript
// server/endpoints/userFeature.js
const express = require("express");
const { validatedRequest } = require("../utils/middleware/validatedRequest");
const User = require("../models/user");

function userFeatureEndpoints(app) {
  if (!app) return;

  // GET endpoint
  app.get("/user/feature", [validatedRequest], async (request, response) => {
    try {
      const user = response.locals.user;

      // Additional validation if needed
      const userId = validateUserId(user.id);
      if (!userId) {
        return response.status(400).json({
          success: false,
          error: "Invalid user ID"
        });
      }

      // Your endpoint logic here
      const result = await someOperation(userId);

      response.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error("Error in /user/feature:", error);
      response.status(500).json({
        success: false,
        error: "Internal server error"
      });
    }
  });

  // POST endpoint
  app.post("/user/feature", [validatedRequest], async (request, response) => {
    try {
      const user = response.locals.user;
      const { data } = request.body;

      // Validate user ID
      const userId = validateUserId(user.id);
      if (!userId) {
        return response.status(400).json({
          success: false,
          error: "Invalid user ID"
        });
      }

      // Validate request data
      if (!data) {
        return response.status(400).json({
          success: false,
          error: "Missing required data"
        });
      }

      // Your endpoint logic here
      const result = await someUpdateOperation(userId, data);

      response.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error("Error in POST /user/feature:", error);
      response.status(500).json({
        success: false,
        error: "Internal server error"
      });
    }
  });
}

module.exports = { userFeatureEndpoints };
```

### 4. Frontend Integration

#### useUser Hook Pattern

```javascript
// frontend/src/hooks/useUser.js
import { useState, useEffect } from "react";
import User from "@/models/user";

export default function useUser() {
  const [user, setUserState] = useState(null);
  const setUser = (userData) => setUserState(userData);
  return { user, setUser };
}
```

#### Frontend Model Pattern

```javascript
// frontend/src/models/user.js
import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";

const User = {
  // Get user feature data
  async getFeature() {
    return await fetch(`${API_BASE}/user/feature`, {
      method: "GET",
      headers: baseHeaders(),
    })
      .then((res) => res.json())
      .catch((e) => {
        console.error("Error fetching user feature:", e);
        return { success: false, error: e.message };
      });
  },

  // Update user feature data
  async updateFeature(data) {
    return await fetch(`${API_BASE}/user/feature`, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify({ data }),
    })
      .then((res) => res.json())
      .catch((e) => {
        console.error("Error updating user feature:", e);
        return { success: false, error: e.message };
      });
  },
};

export default User;
```

## Common Pitfalls and Solutions

### 1. Prisma Validation Errors

**Problem:** `Invalid prisma.users.findFirst() invocation - Argument id is missing`

**Cause:** Passing null, undefined, or NaN values to Prisma queries

**Solution:** Always validate IDs before database calls

```javascript
// Bad
const user = await prisma.users.findFirst({ where: { id: null } });

// Good
const userId = validateUserId(id);
if (!userId) return null;
const user = await prisma.users.findFirst({ where: { id: userId } });
```

### 2. Route Conflicts

**Problem:** 400 Bad Request for specific endpoints

**Cause:** General routes registered before specific ones

**Solution:** Register specific routes first

```javascript
// Bad order
app.use("/api", generalEndpoints); // /user/:id catches everything
app.use("/api", specificEndpoints); // /user/custom-prompt never reached

// Good order
app.use("/api", specificEndpoints); // /user/custom-prompt registered first
app.use("/api", generalEndpoints); // /user/:id as fallback
```

### 3. Missing setUser Function

**Problem:** Frontend components can't update user state

**Cause:** useUser hook not providing update function

**Solution:** Return both user state and setter

```javascript
// Bad
export default function useUser() {
  const [user, setUser] = useState(null);
  return { user }; // Missing setUser
}

// Good
export default function useUser() {
  const [user, setUserState] = useState(null);
  const setUser = (userData) => setUserState(userData);
  return { user, setUser };
}
```

## Testing Checklist

When implementing user endpoints, test:

### Backend Tests

1. **ID Validation:**
   - Valid integer IDs
   - String representations of numbers
   - null values
   - undefined values
   - NaN values
   - Invalid strings

2. **Authentication:**
   - Valid JWT tokens
   - Invalid JWT tokens
   - Missing tokens
   - Expired tokens

3. **Route Conflicts:**
   - Specific routes work correctly
   - General routes don't interfere
   - Parameter parsing works

### Frontend Tests

1. **User State Management:**
   - User data loads correctly
   - User state updates work
   - Error handling for failed requests

2. **Component Integration:**
   - Components receive user data
   - Components can update user data
   - Loading states work correctly

## Documentation Requirements

When creating user endpoints:

1. **Document the endpoint purpose and usage**
2. **Include example requests and responses**
3. **Document any special validation requirements**
4. **Update API documentation (Swagger)**
5. **Add troubleshooting notes for common issues**

## Related Files

Key files for user endpoint development:

- `server/models/user.js` - User data model
- `server/utils/http/index.js` - JWT validation
- `server/utils/middleware/validatedRequest.js` - Request validation
- `server/index.js` - Route registration
- `frontend/src/hooks/useUser.js` - User state management
- `frontend/src/models/user.js` - Frontend user API calls

## References

- [Troubleshooting User Endpoints](../server/docs/troubleshooting_user_endpoints.md)
- [Backend Development Guidelines](./backend-development.mdc)
- [Frontend Development Guidelines](./frontend-development.mdc)
