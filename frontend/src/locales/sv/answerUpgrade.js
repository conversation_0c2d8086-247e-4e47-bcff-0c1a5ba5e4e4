export default {
  "answer-upgrade": {
    title: "Förbättra svaret",
    subtitle: "Välj underkategori",
    steps: "Utför de juridiska uppgifterna",
    planning: "Planerar...",
    "cancel-process":
      "Är du säker på att du vill avbryta denna juridiska uppgiftsprocess?",
    "wait-process": "Vänta medan processen förbereds.",
    "process-title-one": "Skapa handlingsplan",
    "process-description-one":
      "Definiera dokumentets struktur, mål och nyckeluppgifter för att säkerställa en tydlig och organiserad process.",
    "process-title-two": "Skapa handlingssteg",
    "process-description-two":
      "Utföra uppgifterna från planen, skapa innehåll och sektioner baserat på sammanhang och syfte.",
    "process-title-three": "Skapa slutresultatet",
    "process-description-three":
      "Kombinera allt innehåll, fö<PERSON>ina noggrannheten och leverera ett färdigt dokument redo att använda.",
    "category-step": {
      title: "Välj kategori",
      description: "Välj den kategori som bäst uppfyller dina behov",
      categories: {
        formality: {
          label: "Formalitet",
          choices: {
            more_formal: "Gör svaret mer formellt",
            less_formal: "Gör svaret mindre formellt",
            more_professional: "Gör svaret mer professionellt",
            more_casual: "Gör svaret mer avslappnat",
            more_polished: "Gör svaret mer polerat",
            more_relaxed: "Gör svaret mer avspänt",
            academic_tone: "Använd en mer akademisk ton",
            conversational_tone: "Använd en mer konverserande ton",
          },
        },
        complexity: {
          label: "Språkkomplexitet",
          choices: {
            simplify: "Förenkla språket",
            more_descriptive: "Lägg till mer beskrivande språk",
            complex_vocab: "Använd mer komplext ordförråd",
            simple_vocab: "Använd enklare ordförråd",
            technical: "Öka användningen av tekniskt språk",
            layman: "Använd mer lättbegripligt språk",
            add_jargon: "Inkludera fackspråk",
            avoid_jargon: "Undvik fackspråk och använd allmänna termer",
            add_rhetorical: "Lägg till fler retoriska frågor",
            less_rhetorical: "Använd färre retoriska frågor",
          },
        },
        structure: {
          label: "Meningsstruktur",
          choices: {
            shorter: "Gör meningarna kortare",
            longer: "Gör meningarna längre",
            vary: "Variera meningsstrukturen",
            standardize: "Standardisera meningsstrukturen",
            more_complex: "Använd mer komplexa meningar",
            simpler: "Använd enklare meningar",
            active_voice: "Öka användningen av aktiv form",
            passive_voice: "Öka användningen av passiv form",
          },
        },
        figurative: {
          label: "Bildligt språk",
          choices: {
            more_figurative: "Använd mer bildligt språk",
            less_figurative: "Minska det bildliga språket",
            metaphors: "Lägg till fler metaforer och liknelser",
            literal: "Använd mer bokstavligt språk",
            more_idioms: "Inkludera fler idiom",
            less_idioms: "Minska användningen av idiom",
            more_symbolism: "Öka användningen av symbolik",
            less_symbolism: "Minska användningen av symbolik",
          },
        },
        conciseness: {
          label: "Omfattning",
          choices: {
            more_concise: "Gör svaret mer koncist",
            more_wordy: "Gör svaret mer ordrikt",
            remove_redundant: "Ta bort överflödiga fraser",
            add_details: "Lägg till fler detaljer",
            reduce_filler: "Minska utfyllnadsord",
            add_elaboration: "Lägg till mer utveckling/förklaring",
          },
        },
        imagery: {
          label: "Bildspråk & sensoriska detaljer",
          choices: {
            enhance_imagery: "Förstärk bildspråket",
            simplify_imagery: "Förenkla bildspråket",
            vivid_descriptions: "Använd mer levande beskrivningar",
            straightforward_descriptions: "Använd mer raka beskrivningar",
            more_visual: "Inkludera fler visuella detaljer",
            less_visual: "Minska visuella detaljer",
          },
        },
        paragraph: {
          label: "Stycke & textstruktur",
          choices: {
            shorter_paragraphs: "Gör styckena kortare",
            longer_paragraphs: "Gör styckena längre",
            break_sections: "Dela upp texten i mindre sektioner",
            combine_sections: "Slå ihop sektioner för mjukare flöde",
            more_lists: "Använd fler punktlistor",
            more_continuous: "Använd mer löpande text",
            vary_paragraphs: "Variera stycken",
            consistent_length: "Behåll en konsekvent styckelängd",
          },
        },
        content_length_legal_memo: {
          label: "Innehåll och längd, juridiskt PM",
          choices: {
            extend_memo: "Gör PM:et mer omfattande",
            summarize_memo: "Gör PM:et mer koncist",
            expand_analysis: "Utöka den juridiska analysen",
            deepen_case_law: "Lägg till fler rättsfallsreferenser",
            add_statutory_references: "Lägg till fler lagreferenser",
            add_conclusion: "Stärk slutsatsen",
            add_recommendations: "Lägg till praktiska rekommendationer",
            add_risk_assessment: "Inkludera riskbedömning",
            add_executive_summary: "Lägg till en sammanfattning",
          },
        },
        content_length_legal_document: {
          label: "Innehåll och längd, juridiskt dokument",
          choices: {
            extend_document: "Gör dokumentet mer omfattande",
            shorten_document: "Gör dokumentet mer koncist",
            add_clauses: "Lägg till fler skyddsklausuler",
            simplify_clauses: "Förenkla komplexa klausuler",
            add_definitions: "Lägg till fler definierade termer",
            expand_scope: "Utöka omfattningsavsnittet",
            strengthen_warranties: "Stärk garantier och utfästelser",
            enhance_remedies: "Förbättra påföljdsavsnittet",
            add_boilerplate: "Lägg till standardklausuler",
            add_schedules: "Lägg till eller utöka bilagor",
          },
        },
        other: {
          label: "Övrigt",
          choices: {
            replace_context:
              "Ersätt referenser till CONTEXT med källans faktiska namn",
            add_numbering: "Lägg till numrering av stycken",
            remove_numbering: "Ta bort numrering av stycken",
            extend_statutories: "Utöka texten om lagar",
            reduce_statutories: "Minska texten om lagar",
            extend_jurisprudence: "Utöka texten om rättspraxis",
            reduce_jurisprudence: "Minska texten om rättspraxis",
          },
        },
      },
    },
    "prompt-step": {
      title: "Välj prompt",
      description: "Välj hur du vill förbättra svaret",
    },
    actions: {
      next: "Nästa",
      back: "Tillbaka",
      upgrade: "Förbättra svar",
      cancel: "Avbryt",
    },
    "text-upgrade-prompt":
      "{{prompt}} Texten att förbättra är: {{selectedText}}",
  },
};
