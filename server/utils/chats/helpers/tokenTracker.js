const { TokenManager } = require("../../helpers/tiktoken");

/**
 * Token Tracker for comprehensive token metrics collection and real-time monitoring
 *
 * This utility provides:
 * - Real-time token budget monitoring
 * - Comprehensive metrics collection for all processing stages
 * - Token usage reporting and analysis
 * - Integration with iterative processing workflows
 */
class TokenTracker {
  constructor(LLMConnector, options = {}) {
    this.LLMConnector = LLMConnector;
    this.tokenManager = new TokenManager(LLMConnector.model);
    this.options = {
      enableDetailedTracking: true,
      trackContentTypes: true,
      logTokenUsage: false,
      ...options,
    };

    // Initialize process ID first
    this.processId = this._generateProcessId();

    // Initialize metrics structure
    this.resetMetrics();
  }

  /**
   * Generate unique process ID for tracking
   * @private
   */
  _generateProcessId() {
    return `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Reset all metrics to initial state
   */
  resetMetrics() {
    this.metrics = {
      processId: this.processId,
      startTime: Date.now(),
      endTime: null,

      // Overall token counts
      totalInputTokens: 0,
      totalOutputTokens: 0,
      totalTokens: 0,

      // Content breakdown
      contentBreakdown: {
        systemPrompts: 0,
        userPrompts: 0,
        documents: 0,
        memos: 0,
        previousResults: 0,
        context: 0,
      },

      // Processing stages
      stages: {
        documentDescriptions: { tokens: 0, operations: 0, duration: 0 },
        relevanceChecking: { tokens: 0, operations: 0, duration: 0 },
        sectionGeneration: { tokens: 0, operations: 0, duration: 0 },
        legalMemos: { tokens: 0, operations: 0, duration: 0 },
        sectionDrafting: { tokens: 0, operations: 0, duration: 0 },
        iterativeProcessing: { tokens: 0, operations: 0, duration: 0 },
      },

      // Iterative processing metrics
      iterativeMetrics: {
        totalIterations: 0,
        documentsProcessed: 0,
        memosProcessed: 0,
        averageTokensPerIteration: 0,
        maxTokensInIteration: 0,
        minTokensInIteration: Number.MAX_SAFE_INTEGER,
      },

      // Budget and limits
      budgetTracking: {
        contextWindowSize: 0,
        availableBudget: 0,
        budgetUtilization: 0,
        exceedsBudget: false,
        budgetAllocations: {},
      },

      // Error tracking
      errors: [],
      warnings: [],
    };
  }

  /**
   * Start tracking a processing stage
   * @param {string} stageName - Name of the processing stage
   * @returns {Object} Stage tracker object
   */
  startStage(stageName) {
    const stageStart = Date.now();

    return {
      stageName,
      startTime: stageStart,
      addTokens: (tokens, operation = "default") => {
        if (!this.metrics.stages[stageName]) {
          this.metrics.stages[stageName] = {
            tokens: 0,
            operations: 0,
            duration: 0,
          };
        }
        this.metrics.stages[stageName].tokens += tokens;
        this.metrics.stages[stageName].operations += 1;

        if (this.options.logTokenUsage) {
          console.log(
            `[TokenTracker] ${stageName}/${operation}: +${tokens} tokens`
          );
        }
      },
      finish: () => {
        const duration = Date.now() - stageStart;
        if (this.metrics.stages[stageName]) {
          this.metrics.stages[stageName].duration += duration;
        }

        if (this.options.logTokenUsage) {
          console.log(`[TokenTracker] ${stageName} completed in ${duration}ms`);
        }
      },
    };
  }

  /**
   * Track LLM response and extract token metrics
   * @param {Object} llmResponse - Response from LLM connector
   * @param {string} stage - Processing stage name
   * @param {string} operation - Specific operation name
   */
  trackLLMResponse(llmResponse, stage = "unknown", operation = "default") {
    if (!llmResponse) return;

    const inputTokens = llmResponse.metrics?.prompt_tokens || 0;
    const outputTokens = llmResponse.metrics?.completion_tokens || 0;
    const totalTokens =
      llmResponse.metrics?.total_tokens || inputTokens + outputTokens;

    // Update overall metrics
    this.metrics.totalInputTokens += inputTokens;
    this.metrics.totalOutputTokens += outputTokens;
    this.metrics.totalTokens += totalTokens;

    // Update stage metrics
    if (!this.metrics.stages[stage]) {
      this.metrics.stages[stage] = { tokens: 0, operations: 0, duration: 0 };
    }
    this.metrics.stages[stage].tokens += totalTokens;
    this.metrics.stages[stage].operations += 1;

    if (this.options.logTokenUsage) {
      console.log(
        `[TokenTracker] ${stage}/${operation}: ${inputTokens} input + ${outputTokens} output = ${totalTokens} total tokens`
      );
    }
  }

  /**
   * Track content tokens by type
   * @param {string} content - Content to track
   * @param {string} contentType - Type of content (documents, memos, etc.)
   * @param {string} identifier - Optional identifier for the content
   */
  trackContentTokens(content, contentType, identifier = null) {
    if (!content || !this.options.trackContentTypes) return 0;

    const tokens = this.tokenManager.countFromString(content);

    if (this.metrics.contentBreakdown[contentType] !== undefined) {
      this.metrics.contentBreakdown[contentType] += tokens;
    } else {
      this.metrics.contentBreakdown[contentType] = tokens;
    }

    if (this.options.logTokenUsage && identifier) {
      console.log(
        `[TokenTracker] ${contentType}[${identifier}]: ${tokens} tokens`
      );
    }

    return tokens;
  }

  /**
   * Track iterative processing metrics
   * @param {number} iteration - Current iteration number
   * @param {number} documentsProcessed - Number of documents in this iteration
   * @param {number} memosProcessed - Number of memos in this iteration
   * @param {number} tokensUsed - Tokens used in this iteration
   */
  trackIteration(iteration, documentsProcessed, memosProcessed, tokensUsed) {
    this.metrics.iterativeMetrics.totalIterations = Math.max(
      this.metrics.iterativeMetrics.totalIterations,
      iteration
    );

    this.metrics.iterativeMetrics.documentsProcessed += documentsProcessed;
    this.metrics.iterativeMetrics.memosProcessed += memosProcessed;

    // Update min/max tokens per iteration
    if (tokensUsed > this.metrics.iterativeMetrics.maxTokensInIteration) {
      this.metrics.iterativeMetrics.maxTokensInIteration = tokensUsed;
    }
    if (tokensUsed < this.metrics.iterativeMetrics.minTokensInIteration) {
      this.metrics.iterativeMetrics.minTokensInIteration = tokensUsed;
    }

    // Calculate average
    const totalIterations = this.metrics.iterativeMetrics.totalIterations;
    if (totalIterations > 0) {
      this.metrics.iterativeMetrics.averageTokensPerIteration = Math.round(
        this.metrics.totalTokens / totalIterations
      );
    }

    if (this.options.logTokenUsage) {
      console.log(
        `[TokenTracker] Iteration ${iteration}: ${documentsProcessed} docs, ${memosProcessed} memos, ${tokensUsed} tokens`
      );
    }
  }

  /**
   * Track budget allocation and utilization
   * @param {Object} budget - Budget allocation from ContextWindowManager
   * @param {number} actualUsage - Actual tokens used
   */
  trackBudget(budget, actualUsage = 0) {
    this.metrics.budgetTracking.contextWindowSize =
      this.LLMConnector.promptWindowLimit();
    this.metrics.budgetTracking.availableBudget =
      budget.availableForContent || 0;
    this.metrics.budgetTracking.budgetAllocations = { ...budget };

    if (actualUsage > 0) {
      this.metrics.budgetTracking.budgetUtilization = Math.round(
        (actualUsage / this.metrics.budgetTracking.availableBudget) * 100
      );
      this.metrics.budgetTracking.exceedsBudget =
        actualUsage > this.metrics.budgetTracking.availableBudget;

      if (this.metrics.budgetTracking.exceedsBudget) {
        this.addWarning(
          `Budget exceeded: ${actualUsage} tokens used, ${this.metrics.budgetTracking.availableBudget} available`
        );
      }
    }
  }

  /**
   * Add error to tracking
   * @param {string} error - Error message
   * @param {string} stage - Stage where error occurred
   */
  addError(error, stage = "unknown") {
    this.metrics.errors.push({
      timestamp: Date.now(),
      stage,
      error: error.toString(),
    });
  }

  /**
   * Add warning to tracking
   * @param {string} warning - Warning message
   * @param {string} stage - Stage where warning occurred
   */
  addWarning(warning, stage = "unknown") {
    this.metrics.warnings.push({
      timestamp: Date.now(),
      stage,
      warning: warning.toString(),
    });
  }

  /**
   * Calculate processing efficiency metrics
   * @returns {Object} Efficiency metrics
   */
  calculateEfficiency() {
    const duration =
      (this.metrics.endTime || Date.now()) - this.metrics.startTime;
    const tokensPerSecond =
      duration > 0
        ? Math.round(this.metrics.totalTokens / (duration / 1000))
        : 0;

    return {
      processingDuration: duration,
      tokensPerSecond,
      budgetUtilization: this.metrics.budgetTracking.budgetUtilization,
      iterativeEfficiency:
        this.metrics.iterativeMetrics.totalIterations > 0
          ? Math.round(
              this.metrics.totalTokens /
                this.metrics.iterativeMetrics.totalIterations
            )
          : 0,
      errorRate:
        this.metrics.errors.length / Math.max(1, this.getTotalOperations()),
    };
  }

  /**
   * Get total number of operations across all stages
   * @returns {number} Total operations
   */
  getTotalOperations() {
    return Object.values(this.metrics.stages).reduce(
      (total, stage) => total + stage.operations,
      0
    );
  }

  /**
   * Generate comprehensive token usage report
   * @returns {Object} Detailed token usage report
   */
  generateReport() {
    this.metrics.endTime = Date.now();
    const efficiency = this.calculateEfficiency();

    return {
      summary: {
        processId: this.metrics.processId,
        totalTokens: this.metrics.totalTokens,
        inputTokens: this.metrics.totalInputTokens,
        outputTokens: this.metrics.totalOutputTokens,
        processingTime: efficiency.processingDuration,
        tokensPerSecond: efficiency.tokensPerSecond,
        budgetUtilization: `${this.metrics.budgetTracking.budgetUtilization}%`,
        totalOperations: this.getTotalOperations(),
      },

      contentBreakdown: this.metrics.contentBreakdown,

      stageMetrics: Object.entries(this.metrics.stages).map(
        ([stageName, metrics]) => ({
          stage: stageName,
          tokens: metrics.tokens,
          operations: metrics.operations,
          duration: metrics.duration,
          averageTokensPerOperation:
            metrics.operations > 0
              ? Math.round(metrics.tokens / metrics.operations)
              : 0,
        })
      ),

      iterativeMetrics: {
        ...this.metrics.iterativeMetrics,
        efficiency: efficiency.iterativeEfficiency,
      },

      budgetAnalysis: {
        ...this.metrics.budgetTracking,
        recommendations: this._generateBudgetRecommendations(),
      },

      efficiency,

      issues: {
        errors: this.metrics.errors,
        warnings: this.metrics.warnings,
        errorRate: efficiency.errorRate,
      },
    };
  }

  /**
   * Generate budget optimization recommendations
   * @private
   */
  _generateBudgetRecommendations() {
    const recommendations = [];

    if (this.metrics.budgetTracking.budgetUtilization > 90) {
      recommendations.push(
        "Consider increasing chunk size or reducing content per iteration"
      );
    }

    if (this.metrics.iterativeMetrics.totalIterations > 5) {
      recommendations.push(
        "High iteration count - consider optimizing content prioritization"
      );
    }

    if (this.metrics.errors.length > 0) {
      recommendations.push(
        "Errors detected - review error handling and fallback strategies"
      );
    }

    const documentTokens = this.metrics.contentBreakdown.documents || 0;
    const totalContentTokens = Object.values(
      this.metrics.contentBreakdown
    ).reduce((a, b) => a + b, 0);

    if (documentTokens / totalContentTokens > 0.8) {
      recommendations.push(
        "Document content dominates - consider better document chunking"
      );
    }

    return recommendations;
  }

  /**
   * Get token usage for a specific stage
   * @param {string} stageName - Name of the processing stage
   * @returns {number} Total tokens used in that stage
   */
  getTokenUsageForStage(stageName) {
    if (!this.metrics.stages[stageName]) {
      return 0;
    }
    return this.metrics.stages[stageName].tokens;
  }

  /**
   * Get current metrics snapshot
   * @returns {Object} Current metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      efficiency: this.calculateEfficiency(),
    };
  }

  /**
   * Export metrics to JSON format
   * @param {boolean} pretty - Whether to format JSON prettily
   * @returns {string} JSON string of metrics
   */
  exportMetrics(pretty = false) {
    const report = this.generateReport();
    return JSON.stringify(report, null, pretty ? 2 : 0);
  }
}

module.exports = {
  TokenTracker,
};
