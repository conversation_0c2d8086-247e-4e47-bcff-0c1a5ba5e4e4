export default {
  // Bsed on this order of pages in system settings:
  // Customization (Appearance)
  //Admin: System settings
  //Admin: Users
  //Admin: Invites
  //Admin: Workspaces
  //Admin: MCP Servers
  //Admin: Feedback List
  //Admin: Workspace chats
  //Admin: Default system
  //Admin: Document Builder
  //Admin: DD Prompt settings
  //Admin: Chat UI settings
  //AI-Providers: LLM
  //AI-Providers: Prompt Upgrade LLM
  //AI-Providers: Validation LLM
  //AI-Providers: CDB LLM
  //AI-Providers: Custom User AI
  //AI-Providers:  Vector Database
  //AI-Providers: Re-ranking settings
  //AI-Providers: Deep search
  //AI-Providers: Embedder
  //AI-Providers: Text Splitter & Chunking
  //AI-Providers: Voice & Speech
  //AI-Providers: transcription
  //AI-Providers: PDR System
  //Agent skills
  //Tools: Chat Embed History
  //Tools: Chat Embed
  //Tools: Event logs
  //Tools: Developer API
  //Tools: Browser Extension
  //Security
  //Document Drafting: Configuration
  //Document Drafting: Drafting LLM
  //Document Drafting: Chat Settings
  // =========================
  // LLM PREFERENCE PAGE
  // =========================
  llm: {
    title: "Inzira ya LLM",
    description:
      "Aya ni amakuru n'igenamiterere rya LLM y'ingenzi yo kuganira no kwinjiza. Ni ngombwa ko aya makuru ari ay'ubu kandi nyayo, ubundi sisitemu ntizakora neza.",
    provider: "Umugabuzi wa LLM",
    "secondary-provider": "Umutanga LLM w'ubwunganizi",
    "none-selected": "Nta cyatoranijwe",
    "select-llm": "Abakoze ntibazakora kugeza haboneka ihitamo ry'ukuri.",
    "search-llm": "Shakisha abagabuzi ba LLM bose",
    "context-window-warning":
      "Iburira: Ntibishobotse kubona idirishya rya context kuri modeli yatoranijwe.",
    "context-window-waiting": " -- kugirango kugira ibikorwa byose -- ",
    "validation-prompt": {
      disable: {
        label: "Kuramo prompt yo kugenzura",
        description:
          "Iyo ikoreshejwe, buto yo kugenzura ntizagaragara mu mwanya w'umukoresha.",
      },
    },
    "prompt-upgrade": {
      title: "Utanga LLM wo kunoza prompt",
      description:
        "Utanga LLM wihariye n'igishushanyo bizakoreshwa mu kunoza prompt y'ukoresha. Mu buryo busanzwe, hakoreshwa utanga LLM wa sisitemu n'igenamiterere yayo.",
      search: "Shakisha abatanga LLM baboneka kuri iyi feature",
      template: "Urugero rwo kunoza prompt",
      "template-description":
        "Uru rugero ruzakoreshwa mu kunoza prompt. Koresha {{prompt}} kugira ngo werekane umwandiko ugomba kunozwa.",
      "template-placeholder":
        "Andika template izakoreshwa mu kunoza prompts...",
      "template-hint":
        "Urugero: Nyamuneka kunoza umwandiko ukurikira ugakomeza ibisobanuro byawo: {{prompt}}",
    },
    "logo-alt": "{{name}} logo",
    "context-window": "Idirishya rya Context",
    "default-context-window": "(ingano isanzwe kuri uyu mugabuzi)",
    tokens: "tokens",
    "save-error": "Ntibyakunze kuvugurura LLM settings",
  },
  // =========================
  // LLM PROVIDER DESCRIPTIONS
  // =========================
  "llm-provider": {
    openai: "Icyitegererezo gisanzwe ku bikorwa bitari iby'ubucuruzi.",
    azure:
      "Icyitegererezo cy'ubucuruzi cya OpenAI gihagarariwe na Azure services.",
    anthropic: "Umufasha w'AI w'inshuti wahagarariwe na Anthropic.",
    gemini: "Modeli nini kandi ifite ubushobozi bwa Google",
    huggingface:
      "Gerageza 150,000+ LLM zifunguye hamwe n'umuryango wa AI ku isi.",
    ollama: "Koresha LLM zigendanwa kuri mashini yawe.",
    lmstudio: "Gerageza, ikura, no gukoresha LLM zigezweho muri click nkeya.",
    localai: "Koresha LLM zigendanwa kuri mashini yawe.",
    togetherai: "Koresha LLM ifunguye yavuye muri Together AI.",
    mistral: "Koresha LLM zigendanwa zavuye muri Mistral AI.",
    perplexityai:
      "Koresha LLM zifite ubushobozi n'izihuza n'interineti zatanzwe na Perplexity AI.",
    openrouter: "Interineti imwe yo guhuza LLMs.",
    groq: "Igisubizo cyihuse cya LLM ku buryo bwa real-time AI applications.",
    koboldcpp: "Koresha LLM zigendanwa ukoresheje koboldcpp.",
    oobabooga:
      "Koresha LLM zigendanwa ukoresheje Oobabooga Text Generation Web UI.",
    cohere: "Koresha LLM za Cohere Command.",
    lite: "Koresha proxy ya LiteLLM ifite OpenAI compatibility.",
    "generic-openai":
      "Huzaho serivisi iyo ari yo yose ya OpenAI ihuza n'igenamiterere ry'ibanze.",
    native: "Koresha modeli ya Llama yakuweho ku instance.",
    xai: "Koresha LLM zifite ubushobozi nka Grok-2 n'izindi.",
    "aws-bedrock": "Koresha LLM zifite ubushobozi mu buryo bwa AWS Bedrock.",
    deepseek: "Koresha DeepSeek's powerful LLMs.",
    fireworksai: "Imikorere ihuse kandi inoze kuri compound AI systems. ",
    bedrock: "Koresha LLM zifite ubushobozi mu buryo bwa AWS Bedrock.",
  },

  "custom-user-ai": {
    title: "AI y'Ukoresha Yihariye",
    settings: "AI y'Ukoresha Yihariye",
    description: "Shiraho Utanga AI y'Ukoresha Yihariye",
    "custom-model-reference": "Izina rya Modeli Yihariye & Ibisobanuro",
    "custom-model-reference-description":
      "Ongeraho indango yihariye kuri iyi modeli. Ibi bizagaragara mugihe ukoresheje guhitamo AI y'ukoresha yihariye mu kibaho cya prompt.",
    "custom-model-reference-name": "Izina rya Modeli Yihariye",
    "custom-model-reference-description-label":
      "Ibisobanuro bya Modeli (Bitegetswe)",
    "custom-model-reference-description-placeholder":
      "Andika ibisobanuro bitegetswe kuri iyi modeli",
    "custom-model-reference-name-placeholder":
      "Andika izina ryihariye kuri iyi modeli",
    "model-ref-placeholder":
      "Andika izina cyangwa ibisobanuro byihariye kuri iyi modeli",
    "enter-custom-model-reference": "Andika izina ryihariye kuri iyi modeli",
    "standard-engine": "Moteri ya AI Isanzwe",
    "standard-engine-description":
      "Moteri yacu isanzwe ikaba ingirakamaro ku bikorwa byinshi",
    "dynamic-context-window-percentage": "Ijanisha rya Konteksti Ihindagurika",
    "dynamic-context-window-percentage-desc":
      "Igenzura ingano y'idirishya rya konteksti rishobora gukoreshwa ku masoko y'inyongera (10-100%)",
    "no-alternative-title": "Nta Modeli Yindi Yahiswemo",
    "no-alternative-desc":
      "Iyo iyi hitamo ihiswemo, abakoresha ntibafite ubushobozi bwo guhitamo modeli yindi.",
    "select-option": "Hitamo Umwirondoro wa AI Yihariye",
    tab: {
      "custom-1": "Moteri Yihariye 1",
      "custom-2": "Moteri Yihariye 2",
      "custom-3": "Moteri Yihariye 3",
      "custom-4": "Moteri Yihariye 4",
      "custom-5": "Moteri Yihariye 5",
      "custom-6": "Moteri Yihariye 6",
    },
    engine: {
      "custom-1": "Moteri Yihariye 1",
      "custom-2": "Moteri Yihariye 2",
      "custom-3": "Moteri Yihariye 3",
      "custom-4": "Moteri Yihariye 4",
      "custom-5": "Moteri Yihariye 5",
      "custom-6": "Moteri Yihariye 6",
      "custom-1-title": "Moteri Yihariye 1",
      "custom-2-title": "Moteri Yihariye 2",
      "custom-3-title": "Moteri Yihariye 3",
      "custom-4-title": "Moteri Yihariye 4",
      "custom-5-title": "Moteri Yihariye 5",
      "custom-6-title": "Moteri Yihariye 6",
      "custom-1-description": "Shiraho igenamiterere rya Moteri Yihariye 1",
      "custom-2-description": "Shiraho igenamiterere rya Moteri Yihariye 2",
      "custom-3-description": "Shiraho igenamiterere rya Moteri Yihariye 3",
      "custom-4-description": "Shiraho igenamiterere rya Moteri Yihariye 4",
      "custom-5-description": "Shiraho igenamiterere rya Moteri Yihariye 5",
      "custom-6-description": "Shiraho igenamiterere rya Moteri Yihariye 6",
    },
    "option-number": "Ihitamo {{number}}",
    "llm-provider-selection": "Guhitamo Utanga LLM",
    "llm-provider-selection-desc":
      "Hitamo utanga LLM kuri iyi migenamiterere ya AI yihariye",
    "custom-option": "Ihitamo Ryihariye",
    saving: "Bika...",
    "save-changes": "Bika Impinduka",
    "model-ref-saved": "Igenamiterere rya modeli yihariye ryabitswe neza",
    "model-ref-save-failed":
      "Byanze kubika igenamiterere rya modeli yihariye: {{error}}",
    "llm-settings-save-failed":
      "Byanze kubika igenamiterere rya LLM: {{error}}",
    "settings-fetch-failed": "Byanze gukuramo igenamiterere",
    "llm-saved": "Igenamiterere rya LLM ryabitswe neza",
    "select-provider-first":
      "Nyamuneka hitamo utanga LLM kugira ngo ushireho igenamiterere rya modeli. Iyo ryashyizweho, iri hitamo rizaba rishobora guhitamo nk'umushinga wa AI wihariye mu buryo bw'ukoresha.",
  },
  // =========================
  // CDB LLM PREFERENCE
  // =========================
  "cdb-llm-preference": {
    title: "Ihitamo rya CDB LLM",
    settings: "CDB LLM",
    description: "Shiraho umugabuzi wa LLM kuri CDB",
  },
  // =========================
  // TEMPLATE LLM PREFERENCE
  // =========================
  "template-llm-preference": {
    title: "Ihitamo rya LLM Template",
    settings: "LLM Template",
    description:
      "Hitamo utanga LLM ku gukora template z'amakuru ya dokumenti. By default, dukoresha umugabuzi wa sisitemu.",
    "toast-success": "Igenamiterere rya LLM Template ryabitswe neza",
    "toast-fail": "Byanze kubika igenamiterere rya LLM Template",
    saving: "Bika...",
    "save-changes": "Bika Impinduka",
  },

  // =========================
  // AUDIO PREFERENCE
  // =========================
  audio: {
    title: "Ihitamo rya Kuvuga-ubwanditsi (Speech-to-text)",
    provider: "Umugabuzi",
    "system-native": "Sisitemu kavukire",
    "desc-speech":
      "Hano ushobora gutangaza uburyo bwa serivisi za speech-to-text na text-to-speech ushaka gukoresha muri platform yawe. By default, dukoresha ubufasha bwa browser, ariko ushobora gukoresha izindi.",
    "title-text": "Ihitamo rya Text-to-speech",
    "desc-text":
      "Hano ushobora gutangaza uburyo bwa serivisi za text-to-speech ushaka gukoresha muri platform yawe. By default, dukoresha ubufasha bwa browser, ariko ushobora gukoresha izindi.",
    "desc-config": "Nta igenamiterere rikeneye kuri browser yawe.",
    "placeholder-stt": "Shakisha abagabuzi ba speech-to-text",
    "placeholder-tts": "Shakisha abagabuzi ba text-to-speech",
    "native-stt": "Koresha serivisi ya STT ya browser niba iboneye.",
    "native-tts": "Koresha serivisi ya TTS ya browser niba iboneye.",
    "piper-tts": "Kora modeli za TTS mu buryo bwite muri browser yawe.",
    "openai-description":
      "Koresha amajwi na tekinoloji ya OpenAI mu guhindura umwandiko mo ijwi.",
    openai: {
      "api-key": "Urufunguzo rwa API",
      "api-key-placeholder": "OpenAI API Key",
      "voice-model": "Modeli y'amajwi",
    },
    elevenlabs: "Koresha amajwi ya ElevenLabs mu gukora text-to-speech.",
  },

  // =========================
  // TRANSCRIPTION PREFERENCE
  // =========================
  transcription: {
    title: "Ihitamo rya Transcription",
    description:
      "Aya ni amakuru n'igenamiterere rya serivisi yo gushyira mu nyandiko. Ni ingenzi ko aya makuru ari ay'ubu kandi nyayo, ubundi amajwi ntashyirwa mu nyandiko.",
    provider: "Umugabuzi wa Transcription",
    "warn-start":
      "Gukoresha icyitegererezo cya whisper kuri mashini ifite RAM cyangwa CPU bike bishobora guhagarika platform igihe cyo gutunganya amajwi.",
    "warn-recommend": "Dusaba nibura 2GB RAM na dosiye ziri munsi ya 10MB.",
    "warn-end":
      "Icyitegererezo cyashyizwemo kizakurwa mu buryo bwikora ku nshuro ya mbere.",
    "search-audio": "Shakisha abatanga serivisi za transcription y'amajwi",
    "api-key": "Urufunguzo rwa API",
    "api-key-placeholder": "Urufunguzo rwa OpenAI API",
    "whisper-model": "Icyitegererezo cya Whisper",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Icyitegererezo Gisanzwe Cyashyizwemo",
    "default-built-in-desc":
      "Koresha icyitegererezo cya whisper cyashyizwemo kuri iyi instance mu buryo bwite.",
    "openai-name": "OpenAI",
    "openai-desc":
      "Koresha icyitegererezo cya OpenAI Whisper-large ukoresheje urufunguzo rwawe rwa API.",
    "model-turbo": "openai/whisper-large-v3-turbo", // Izina rishya rya modeli
    "model-size-turbo": "(~810mb)", // Ingano nshya ya modeli
  },

  // =========================
  // EMBEDDING PREFERENCE
  // =========================
  embedding: {
    title: "Ihitamo rya Embedding",
    "desc-start":
      "Mugihe ukoresheje LLM idafite embedding engine yihariye - ushobora kongera gutangaza uburyo bwo kwinjiza.",
    "desc-end":
      "Embedding ni uburyo bwo guhindura inyandiko mu vectors. Aya makuru akenewe kugirango dosiye zawe na prompts bihindurwe mu buryo bushobora gukoreshwa na platform.",
    provider: {
      title: "Umugabuzi wa Embedding",
      description: "Nta igenamiterere rikeneye kuri LanceDB.",
      "search-embed": "Shakisha abagabuzi ba embedding bose",
      search: "Shakisha abagabuzi ba embedding bose",
      select: "Ugomba guhitamo embedding",
    },
    workspace: {
      title: "Ihitamo rya Embedding ku cyumba cy'akazi",
      description:
        "Umugabuzi na modeli za embedding zizakoreshwa muri workspace. By default, bikoreshwa embedding y'ingenzi ya sisitemu.",
      "multi-model":
        "Inkunga ya multi-model ntabwo irashyigikirwa kuri uyu mugabuzi.",
      "workspace-use": "Uyu workspace uzakoresha",
      "model-set": "imiterere ya modeli yashyizweho kuri sisitemu.",
      embedding: "Modeli ya Embedding ya Workspace",
      model:
        "Modeli ya embedding izakoreshwa muri uyu workspace. Niba ituzuye, izakoresha embedding y'ingenzi ya sisitemu.",
      wait: "-- tegereza kuri modeli --",
      setup: "Shiraho",
      use: "Koresha",
      "need-setup":
        "Nk'umugabuzi wa embedding muri uyu workspace, ugomba kubanza kuyishyiraho.",
      cancel: "Hagarika",
      save: "Bika",
      settings: "Igenamiterere",
      search: "Shakisha abagabuzi ba embedding bose",
      "need-llm":
        "Nk'umugabuzi wa LLM muri uyu workspace, ugomba kubanza kuyishyiraho.",
      "save-error": "Failed to save {{provider}} settings: {{error}}",
      "system-default": "Igenamiterere rya Sisitemu",
      "system-default-desc":
        "Koresha embedding y'ingenzi ya sisitemu kuri uyu workspace.",
    },
    warning: {
      "switch-model":
        "Byakozwe kuri modeli ya embedding brichwa dokumenti zishyiraho zikibazo kugirango gushyiraho.",
    },
  },

  // =========================
  // TEXT SPLITTING & CHUNKING
  // =========================
  text: {
    title: "Ihitamo rya Text Splitting & Chunking",
    "desc-start":
      "Hari igihe ushaka guhindura uburyo inyandiko nshya zigabanywa zikanashyirwa mu bice mbere yo gushyirwa muri database ya vectors.",
    "desc-end":
      "Ugomba guhindura iyi igenamiterere gusa niba usobanukiwe uburyo bwo gukata inyandiko n'ingaruka zayo.",
    "warn-start": "Impinduka hano zizahabwa gusa kuri",
    "warn-center": "inyandiko nshya",
    "warn-end": ", ntizikore kuri inyandiko zihari.",
    method: {
      title: "Uburyo bwo gutandukanya inyandiko",
      "native-explain": "Koresha ingano n'ihurirana by'uduce two mu gace.",
      "jina-explain": "Ohereza gutandukanya/gucamo ku buryo bwa Jina.",
      "jina-info": "Gutandukanya kwa Jina birakora.",

      jina: {
        api_key: "Urufunguzo rwa API ya Jina",
        api_key_desc:
          "Bikenewe kugira ngo ubone serivisi yo gutandukanya ya Jina. Urufunguzo ruzabikwa mu ibidukikije byawe.",
        max_tokens: "Jina: Tokens ntarengwa ku gice",
        max_tokens_desc:
          "Igenamiterere ry'umubare ntarengwa wa tokens mu gice kuri Jina segmenter (ntarengwa 2000 tokens).",
        return_tokens: "Garura amakuru ya tokens",
        return_tokens_desc:
          "Shyiramo umubare wa tokens n'amakuru ya tokenizer mu gisubizo.",
        return_chunks: "Garura amakuru y'uduce",
        return_chunks_desc:
          "Shyiramo amakuru yuzuye ku duce twakoreshejwe mu gisubizo.",
      },
    },
    size: {
      title: "Ingano ya Text Chunk",
      description:
        "Iyi ni uburebure ntarengwa bw'inyuguti zishobora kuboneka muri vector imwe.",
      recommend: "Uburebure ntarengwa bwa modeli yo kwinjiza ni",
    },
    overlap: {
      title: "Kwiyongera kwa Text Chunk",
      description:
        "Iyi ni intera ntarengwa y'inyuguti ziba zihurira hagati ya chunks ebyiri z'inyandiko.",
      error:
        "Chunk overlap ntishobora kuba nini cyangwa kungana na chunk size.",
    },
  },
  // =========================
  // CONTEXTUAL SETTINGS
  // =========================
  contextual: {
    checkbox: {
      label: "Guhuza n'ibindi",
      hint: "Emerera guhuza n'ibindi kugira ngo wongere imikorere yo kwinjiza.",
    },
    systemPrompt: {
      label: "Ijambo rya Sisitemu",
      placeholder: "Andika agaciro hano...",
      description:
        "Urugero: Nyamuneka tanga ibisobanuro bigufi kugira ngo ushyire iki gice mu nyandiko yose mu rwego rwo koroshya gushaka igice. Subiza gusa ibisobanuro bigufi ntakindi.",
    },
    userPrompt: {
      label: "Ijambo ry'Umukoresha",
      placeholder: "Andika agaciro hano...",
      description:
        "Urugero: <document>\n{file}\n</document>\nDore igice dushaka gushyira mu nyandiko yose\n<chunk>\n{chunk}\n</chunk>",
    },
  },
  // =========================
  // CHAT UI SETTINGS
  // =========================
  "chat-ui-settings": {
    title: "Igenamiterere ry'Imiterere ya Chat",
    description: "Shiraho igenamiterere ya chat.",
    auto_submit: {
      title: "Kohereza byikora ibyo uvuze",
      description: "Kohereza byikora ibyo uvuze nyuma y'igihe cy'agacecetsi",
    },
    auto_speak: {
      title: "Gusoma byikora ibisubizo",
      description: "Soma byikora ibisubizo bya AI",
    },
  },
  // =========================
  // PIPER TTS OPTIONS
  // =========================
  piperTTS: {
    description:
      "Modeli za PiperTTS zikorera muri browser yawe kuburyo bwahoze. Ibi bishobora gufata umutungo mwinshi ku mashini zigoramye.",
    "voice-model": "Voice Model Selection",
    "loading-models": "-- loading available models --",
    "stored-indicator":
      '"✔" bivuze ko iyi modeli iri kubikwa locally kandi ntigomba kongera gukururwa.',
    "flush-cache": "Siba voice cache",
    "flush-success": "Amajwi yose yakuweho muri browser storage",
    demo: {
      stop: "Hagarika demo",
      loading: "Birimo gupakirwa ijwi",
      play: "Kanda ushakire urugero",
      text: "Murakaza neza kuri IST Legal!",
    },
  },

  // =========================
  // VECTOR DATABASE (SYSTEM)
  // =========================
  vector: {
    title: "Ububiko bwa Vector",
    description:
      "Aya ni amakuru n'igenamiterere bigena uburyo platformu yawe ikora. Ni ngombwa ko aya mafunguzo aba ari mashya kandi akwiye.",
    provider: {
      title: "Utanga ububiko bw'amakuru ya vector",
      description: "Nta gushyiraho bikenewe kuri LanceDB.",
      "search-db": "Shakisha abatanga ububiko bw'amakuru ya vector bose",
      search: "Shakisha ububiko bw'amakuru ya vector bwose",
      select: "Hitamo utanga ububiko bw'amakuru ya vector",
    },
    warning:
      "Guhindura ububiko bw'amakuru ya vector bizasaba ko wongera ushyira inyandiko zose mu bice by'akazi byose bifitanye isano. Ibi birashobora gufata igihe.",
    search: {
      title: "Uburyo bwo gushakisha vector",
      mode: {
        "globally-enabled":
          "Iri genwa rigenwa mu buryo rusange mu migenamiterere ya sisitemu. Sura migenamiterere ya sisitemu kugira ngo uhindure imyitwarire yo kongera gushyira mu byiciro.",
        default: "Gushakisha bisanzwe",
        "default-desc":
          "Gushakisha gusanzwe kw'imisusire ya vector nta kongera gushyira mu byiciro.",
        "accuracy-optimized": "Byatunganyijwe ku bw'ukuri",
        "accuracy-desc":
          "Kongera gushyira mu byiciro ibisubizo kugira ngo hongerwe ukuri hakoreshejwe kwitabwaho guhuza.",
      },
    },
  },
  // =========================
  // VECTOR DATABASE PROVIDER DESCRIPTIONS
  // =========================
  vectordb: {
    lancedb: "Database ya vectors 100% ikorera kuri instance ya platform.",
    chroma:
      "Database ya vectors ifunguye ushobora kuyikorera ku giti cyawe cyangwa kuri cloud.",
    pinecone: "Database ya vectors ikorera kuri cloud kubikorwa binini.",
    zilliz:
      "Database ya vectors ikorera kuri cloud yubatswe kubikorwa binini bifite SOC 2 compliance.",
    qdrant:
      "Database ya vectors ifunguye ishobora gukorerwa ku giti cyawe cyangwa kuri cloud.",
    weaviate:
      "Database ya vectors ifunguye ishobora gukorerwa ku giti cyawe cyangwa kuri cloud, ifite ububasha bwinshi.",
    milvus: "Ifunguye, ikora vuba cyane kandi ikomeye.",
    astra: "Ubushakashatsi bwa vector ku GenAI.",
  },
  // =========================
  // DEEP SEARCH SETTINGS
  // =========================
  deep_search: {
    title: "Gushakisha Kure",
    description:
      "Shiraho ubushobozi bwo gushakisha ku mbuga kugira ngo utange ibisubizo byiza. Iyo byemejwe, sisitemu ishobora gushakisha ku mbuga amakuru yo kunoza ibisubizo.",
    enable: "Koresha Gushakisha Kure",
    enable_description:
      "Reka sisitemu ishakishe amakuru ku mbuga igihe isubiza ibibazo.",
    provider_settings: "Igenamiterere ry'Utanga Serivisi",
    provider: "Utanga Serivisi yo Gushakisha",
    model: "Modeli",
    api_key: "Urufunguzo rwa API",
    api_key_placeholder: "Andika urufunguzo rwawe rwa API",
    api_key_placeholder_set:
      "Urufunguzo rwa API rwashyizweho (andika urufunguzo rushya kugira ngo uhindure)",
    api_key_help:
      "Urufunguzo rwawe rwa API rubikwa mu buryo bwizewe kandi rukoreshwa gusa mu gushakisha ku mbuga.",
    context_percentage: "Ijanisha rya Konteksti",
    context_percentage_help:
      "Ijanisha ry'idirishya rya konteksti rigenewe ibisubizo byo gushakisha ku mbuga (5-20%).",
    fetch_error: "Ntibyakunze kuzana igenamiterere rya Gushakisha Kure",
    save_success: "Igenamiterere rya Gushakisha Kure ryabitswe neza",
    save_error:
      "Ntibyakunze kubika igenamiterere rya Gushakisha Kure: {{error}}",
    toast_success: "Igenamiterere rya Gushakisha Kure ryabitswe neza",
    toast_error:
      "Ntibyakunze kubika igenamiterere rya Gushakisha Kure: {{error}}",
    brave_recommended:
      "Brave Search ni uburyo bwiza kandi bwizewe bwo gushakisha.",
  },
  // =========================
  // PDR SETTINGS
  // =========================
  "pdr-settings": {
    title: "PDR Settings",
    description: "Gena Parent Document Retrieval settings kuri workspace zawe.",
    "desc-end": "Ibi bigira ingaruka ku buryo inyandiko za PDR zikora.",

    "global-override": {
      title: "Global Dynamic PDR Override",
      description:
        "Iyo yashyizweho, iyi izafata inyandiko zose z'umwanya w'akazi nk'aho zifite PDR-yemejwe mu miterere y'ibisubizo. Iyo idakoreshwa, ni inyandiko zigaragara nk'aho zifite PDR gusa zizakoreshwa, bishobora kugabanya ububiko bw'amakuru kandi bikagira ingaruka yo gutanga ibisubizo bifite ubwiza buke cyane kubera ko ibice bya vektori biva mu ishakisha gusa bizakoreshwa nk'inkomoko muri ibyo bihe.",
    },

    "toast-success": "PDR settings yavuguruwe",
    "toast-fail": "Ntibyakunze kuvugurura PDR settings",
    "adjacent-vector-limit": "Adjacent Vector Limit",
    "adjacent-vector-limit-desc": "Umupaka w'inyandiko ziba zikwiranye hafi.",
    "adjacent-vector-limit-placeholder": "Injiza umubare w'inyandiko",
    "keep-pdr-vectors": "Keep PDR Vectors",
    "keep-pdr-vectors-desc": "Option yo kugumana vectors za PDR.",
  },

  // =========================
  // ADMIN AGENTS
  // =========================
  agents: {
    title: "Ubushobozi bw'Agent",
    "agent-skills": "Tegura kandi ucunge ubushobozi bw'agent",
    "custom-skills": "Ubushobozi Bwihariye",
    back: "Gusubira Inyuma",
    "select-skill": "Hitamo ubushobozi bwo gutegura",
    "preferences-saved": "Amahitamo y'agent yabitswe neza",
    "preferences-failed": "Amahitamo y'agent ntabwo yabitswe",
    "skill-status": {
      on: "Arakora",
      off: "Ntagikora",
    },
    "skill-config-updated": "Ibigize by'ubuhanga byavuguruwe neza",
  },
  // =========================
  // AGENT MENU
  // =========================
  "agent-menu.default-agent": "Agent usanzwe",
  "agent-menu.ability.rag-search": "RAG Search",
  "agent-menu.ability.web-scraping": "Gusora urubuga",
  "agent-menu.ability.web-browsing": "Kureba urubuga",
  "agent-menu.ability.save-file-to-browser": "Kubika dosiye muri browser",
  "agent-menu.ability.list-documents": "Urutonde rw'inyandiko",
  "agent-menu.ability.summarize-document": "Gusubiramo inyandiko",
  "agent-menu.ability.chart-generation": "Gukora grafike",
  // =========================
  // BADGES AGENT PAGE
  // =========================
  badges: {
    default: {
      text: "Default",
      tooltip:
        "Ubu buhanga bukoreshwa ku buryo bw'ibanze kandi ntibushobora guhagarikwa.",
    },
  },
  // =========================
  // EMBED CHATS
  // =========================
  "embed-chats": {
    title: "Ibiganiro bya Embed",
    export: "Ohereza",
    description:
      "Aha ni ibiganiro byose byanditswe byaturutse kuri embed washyize hanze.",
    table: {
      embed: "Embed",
      sender: "Wohereje",
      message: "Ubutumwa",
      response: "Igisubizo",
      at: "Byoherejwe",
    },
    delete: {
      title: "Gusiba ibiganiro",
      message: "Ushaka gusiba ibiganiro?",
    },
    config: {
      "delete-title": "Gusiba embed",
      "delete-message": "Ushaka gusiba ibiganiro?",
      "disable-title": "Gusiba embed",
      "disable-message": "Ushaka gusiba ibiganiro?",
      "enable-title": "Gusiba embed",
      "enable-message": "Ushaka gusiba ibiganiro?",
    },
  },
  // =========================
  // EMBEDDABLE CHAT WIDGETS
  // =========================
  embeddable: {
    title: "Ibikoresho byo kuganira bishobora gushyirwa ahandi",
    description:
      "Ibikoresho byo kuganira bishobora gushyirwa ahandi ni interfaces za chat zifatanyijwe n'icyumba cy'akazi kimwe. Ibi bigufasha gukora ahantu ho gukorera ushobora gusangiza isi.",
    create: "Kora embed",
    table: {
      workspace: "Umwanya w'akazi",
      chats: "Ibiganiro byoherejwe",
      Active: "Domains zemewe",
    },
  },
  // =========================
  // NEW EMBED
  // =========================
  "new-embed": {
    title: "Kora embed nshya kuri workspace",
    error: "Ikosa: ",
    "desc-start":
      "Nyuma yo gukora embed uzahabwa ihuriro ushobora gushyira kuri website yawe ukoresheje",
    script: "script",
    tag: "tag.",
    cancel: "Hagarika",
    "create-embed": "Kora embed",
    workspace: "Umwanya w'akazi",
    "desc-workspace":
      "Uyu ni umwanya w'akazi ibiciro bya chat bizashingiraho. By default, bigendera ku igenamiterere ry'uyu mwanya w'akazi keretse ubihindurije aho.",
    "allowed-chat": "Uburyo bwemerewe bwo kuganira",
    "desc-query":
      "Shiraho uko chatbot yawe izitwara. Query isobanura ko izasubiza gusa igihe inyandiko iboneka ifasha gusubiza ikibazo.",
    "desc-chat":
      "Chat ifungura ikiganiro ku bibazo rusange kandi ishobora gusubiza ibibazo bitarebana n'inyandiko za workspace.",
    "desc-response": "Chat: Subiza ibibazo byose uko byaba bimeze",
    "query-response":
      "Query: Subiza gusa ibibazo bifitanye isano n'inyandiko ziri muri workspace",
    restrict: "Zitira requests ziturutse kuri domain",
    filter:
      "Uyu murongo uzabuza requests ziva kuri domain itari iyi iri munsi.",
    "use-embed":
      "Iyo usize iri sanganya ubusa, bivuze ko embed ishobora gukoreshwa kuri site iyo ari yo yose.",
    "max-chats": "Umubare ntarengwa w'ibiganiro ku munsi",
    "limit-chats":
      "Gena umubare w'ibiganiro iyi embed yinjizwemo ishobora kwakira mu masaha 24. Zero bisobanura ko bitagira imipaka.",
    "chats-session": "Umubare ntarengwa w'ibiganiro kuri session",
    "limit-chats-session":
      "Gena umubare w'ibiganiro umukoresha ashobora kohereza kuri embed mu masaha 24. Zero bisobanura ko ntampaka.",
    "enable-dynamic": "Shyiraho modeli ihinduka",
    "llm-override":
      "Emera guhindura modeli ya LLM bityo ikaruta iya workspace isanzwe.",
    "llm-temp": "Emera guhindura ubukonje n'ubushyuhe bwa LLM",
    "desc-temp":
      "Emera guhindura ubushyuhe bwa LLM bityo ikaruta iherutse ya workspace.",
    "prompt-override": "Emera guhindura Prompt",
    "desc-override":
      "Emera guhindura system prompt bigasimbura prompt y'ibanze ya workspace.",
  },
  // =========================
  // EMBED CONFIGS
  // =========================
  embedConfigs: {
    "show-code": "Erekana Code",
    enable: "Emeza",
    disable: "Funga",
    "all-domains": "zose",
    "disable-confirm":
      "Urabyizeye ko ushaka gufunga iyi embed?\nNiba ibaye ifunzwe ntishobora kongera gusubiza ibiganiro.",
    "delete-confirm":
      "Urabyizeye ko ushaka gusiba iyi embed?\nNiba isibwe ntishobora kongera gukoreshwa.\n\nIbi ntibishobora gusubirwamo.",
    "disabled-toast": "Embed irafunzwe",
    "enabled-toast": "Embed irakora",
  },
  // =========================
  // EVENT LOGS
  // =========================
  event: {
    title: "Ibyanditswe by'Ibikorwa",
    description:
      "Reba ibikorwa byose bibera kuri instance kugirango ubikurikirane.",
    clear: "Siba ibyanditswe by'ibikorwa",
    table: {
      type: "Ubwoko bw'Igikorwa",
      user: "Umukoresha",
      occurred: "Byabaye",
    },
  },
  // =========================
  // API KEYS
  // =========================
  api: {
    title: "Imfunguzo za API",
    description:
      "Imfunguzo za API zituma ushobora kwinjira no gucunga iyi instance mu buryo bwa program.",
    link: "Soma inyandiko za API",
    generate: "Kora urufunguzo rushya rwa API",
    table: {
      key: "Urufunguzo rwa API",
      by: "Byakozwe na",
      created: "Byakozwe",
    },
    new: {
      title: "Shiraho urufunguzo rushya rwa API",
      description:
        "Nyuma yo gushyiraho, urufunguzo rwa API rushobora gukoreshwa kugira ngo ubone no guhindura iyi instance mu buryo bwa program.",
      doc: "Soma inyandiko za API",
      cancel: "Hagarika",
      "create-api": "Shiraho urufunguzo rwa API",
    },
  },
  // =========================
  // BROWSER EXTENSION API KEYS
  // =========================
  "browser-extension-api": {
    title: "Imfunguzo za API",
    description: "Gucunga imfunguzo za API zo guhuza kuri iyi seriveri.",
    "generate-key": "Kora urufunguzo rushya rwa API",
    "table-headers": {
      "connection-string": "Umurongo wo guhuza",
      "created-by": "Yakozwe na",
      "created-at": "Yaremwe",
      actions: "Ibikorwa",
    },
    "no-keys": "Nta mfunguzo za API zabonetse",
    modal: {
      title: "Urufunguzo rushya rwa API y'inyongera ya mushakisha",
      "multi-user-warning":
        "Iburira: Uri mu buryo bw'abakoresha benshi. Uru rufunguzo rwa API ruzatanga uburenganzira ku mbuga zose z'akazi zifitanye isano na konti yawe. Nyamuneka ugabanye witonze.",
      "create-description":
        'Nyuma yo gukanda "Kora urufunguzo rwa API", iyi seriveri izagerageza gukora urufunguzo rushya rwa API ku nyongera ya mushakisha.',
      "connection-help":
        'Niba ubona "Wahujwe na IST Legal" mu nyongera, guhuza byagenze neza. Niba atari ko biri, nyamuneka kora kopi y\'umurongo wo guhuza maze uwushyire mu nyongera ukoresha intoki.',
      cancel: "Kureka",
      "create-key": "Kora urufunguzo rwa API",
      "copy-key": "Kora kopi y'urufunguzo rwa API",
      "key-copied": "Urufunguzo rwa API rwakopiwe!",
    },
    tooltips: {
      "copy-connection": "Kora kopi y'umurongo wo guhuza",
      "auto-connect": "Huza mu buryo bwikora ku nyongera",
    },
    confirm: {
      revoke:
        "Uzi neza ko ushaka guhagarika uru rufunguzo rwa API y'inyongera ya mushakisha?\nNyuma y'ibi ntiruzongera gukoreshwa.\n\nIyi gahunda ntishobora gusubizwa inyuma.",
    },
    toasts: {
      "key-revoked":
        "Urufunguzo rwa API y'inyongera ya mushakisha rwahagaritswe burundu",
      "revoke-failed": "Ntibyashobotse guhagarika urufunguzo rwa API",
      copied: "Umurongo wo guhuza wakopiwe mu bubiko",
      connecting: "Kuragerageza guhuza ku nyongera ya mushakisha...",
    },
    "revoke-title": "Hagarika urufunguzo rwa API y'inyongera ya mushakisha",
    "revoke-message":
      "Uzi neza ko ushaka guhagarika uru rufunguzo rwa API y'inyongera ya mushakisha?\nNyuma y'ibi ntiruzongera gukoreshwa.\n\nIyi gahunda ntishobora gusubizwa inyuma.",
  },
  // =========================
  // SECURITY
  // =========================
  security: {
    "multi-user-mode-permanent":
      "Multi-user mode yashyizweho burundu ku mpamvu z'umutekano",
    "password-validation": {
      "restricted-chars":
        "Ijambo ry'ibanga ryawe rifite inyuguti zitemewe. Inyuguti zemewe ni _,-,!,@,$,%,^,&,*,(,),;",
    },
    "public-workspace": {
      "access-description":
        "Iyo rikoreshwa, umuntu wese ashobora kugera ku mbuga rusange nta konti ikenewe.",
    },
    button: {
      saving: "Kubika...",
      "save-changes": "Bika impinduka",
    },
  },
  // =========================
  // MULTI-USER MODE
  // =========================
  multi: {
    title: "Imikorere y'Abakoresha Benshi",
    description:
      "Tegura instance yawe gushyigikira itsinda ryawe ukoresheje Multi-User Mode.",
    enable: {
      "is-enable": "Multi-User Mode yashyizweho",
      enable: "Shyiraho Multi-User Mode",
      description:
        "By default, uzaba umuyobozi wenyine. Nk'umuyobozi, ugomba gukora accounts y'abakoresha bashya cyangwa abashinzwe. Ntugatakaze ijambo ry'ibanga kuko umuyobozi wenyine ashobora gusubiramo ijambo ry'ibanga.",
      username: "Email y'umuyobozi",
      password: "Ijambo ry'ibanga rya admin",
      "username-placeholder": "Izina ryawe rya admin",
      "password-placeholder": "Ijambo ry'ibanga rya admin",
    },
    password: {
      title: "Kurinda Ijambo ry'ibanga",
      description:
        "Kurinda instance yawe n'ijambo ry'ibanga. Niba uyibagiwe, nta buryo bwo kongera kuyibona, rero uyibike neza.",
    },
    instance: {
      title: "Kurinda instance n'ijambo ry'ibanga",
      description:
        "By default, uzaba umuyobozi wenyine. Nk'umuyobozi, ugomba gukora accounts y'abakoresha bashya cyangwa abakozi. Ntugatakaze ijambo ry'ibanga kuko umuyobozi wenyine ashobora gusubiramo ijambo ry'ibanga.",
      password: "Ijambo ry'ibanga rya instance",
    },
  },
  // =========================
  // EXPERIMENTAL
  // =========================
  experimental: {
    title: "Ibikorwa by'Igerageza",
    description: "Ibikorwa biri mu igerageza",
    "live-sync": {
      title: "Guhuza Inyandiko ku Muyoboro",
      description: "Gutangiza guhuza ibyanditswe biva mu masoko yo hanze",
      "manage-title": "Inyandiko zikurikiranwa",
      "manage-description":
        "Izi ni inyandiko zose zikurikiranwa kuri ubu muri sisitemu yawe. Ibyanditswe muri izi nyandiko bizajya bihurizwa hamwe buri gihe.",
      "document-name": "Izina ry'Inyandiko",
      "last-synced": "Igihe cya nyuma yahurijwe",
      "next-refresh": "Igihe gisigaye kugira ngo ivugururwe",
      "created-on": "Yaremwe ku wa",
      "auto-sync": "Guhuza Ibyanditswe ku Buryo Bwikora",
      "sync-description":
        "Gutangiza ubushobozi bwo kugena inkomoko y'ibyanditswe 'igomba gukurikiranwa'. Ibyanditswe bikurikiranwa bizajya bifatwa kandi bivugururwa muri iyi sisitemu.",
      "sync-workspace-note":
        "Ibyanditswe bikurikiranwa bizajya bivugururwa ku buryo bwikora mu mbuga zose z'akazi aho byavuzwe.",
      "sync-limitation":
        "Iyi feature ikoreshwa gusa ku byanditswe byo ku rubuga, nk'imbuga, Confluence, YouTube na dosiye za GitHub.",
      documentation: "Inyandiko z'Ibikorwa n'Imburizi",
      "manage-content": "Gucunga Ibyanditswe Bikurikiranwa",
    },
    tos: {
      title: "Amabwiriza yo gukoresha ibikorwa by'igerageza",
      description:
        "Ibikorwa by'igerageza kuri iyi platform ni ibikorwa turimo kugerageza kandi bitorwa ku bushake. Tuzakumenyesha mbere y'igihe ibibazo byose bishobora kubaho mbere yo kwemera igikorwa icyo ari cyo cyose.",
      "possibilities-title":
        "Gukoresha igikorwa kuri iyi paje bishobora gutanga, ariko ntibigomba kugera ku, ibi bikurikira:",
      possibilities: {
        "data-loss": "Gutakaza amakuru.",
        "quality-change": "Impinduka mu ngaruka z'ibisubizo.",
        "storage-increase": "Kwiyongera kw'ubwihugiko.",
        "resource-consumption": "Kwiyongera kw'imikoreshereze y'ibikoresho.",
        "cost-increase":
          "Kwiyongera kw'ikiguzi cyangwa imikoreshereze ya LLM cyangwa abatanga serivisi zo kwinjiza.",
        "potential-bugs":
          "Amakosa ashoboka cyangwa ibibazo mu gukoresha iyi porogaramu.",
      },
      "conditions-title":
        "Gukoresha igikorwa cy'igerageza biza n'urutonde rukurikira rw'amabwiriza atuzuye:",
      conditions: {
        "future-updates":
          "Igikorwa gishobora kutazaboneka mu ivugurura ry'ejo hazaza.",
        stability: "Igikorwa gikoreshwa ubu ntikimeze neza.",
        availability:
          "Igikorwa gishobora kutazaboneka mu bihe bizaza, ibigize cyangwa ubwishingizi bw'iyi sisitemu.",
        privacy:
          "Amabwiriza yawe y'ibanga azubahirizwa mu gukoresha igikorwa cya beta.",
        changes: "Aya mabwiriza ashobora guhinduka mu ivugurura ry'ejo hazaza.",
      },
      "read-more": "Niba ushaka gusoma byinshi ushobora kureba",
      contact: "cyangwa ukandikira",
      reject: "Kwanga & Gufunga",
      accept: "Ndabyumvise",
    },
    "update-failed": "Kuvugurura ibikorwa by'igerageza byarananiwe",
  },
};
