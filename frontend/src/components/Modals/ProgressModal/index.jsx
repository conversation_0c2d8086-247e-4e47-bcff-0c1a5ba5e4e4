import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import Modal from "@/components/ui/Modal";
import ProgressList from "@/components/ProgressList";
import useThreadProgress from "@/hooks/useThreadProgress";
import { Button } from "@/components/Button";

export default function ProgressModal({ isOpen, onClose, threadSlug }) {
  const { t } = useTranslation();
  const { error } = useThreadProgress(threadSlug);

  // Close modal when there's an error since we show errors in chat
  useEffect(() => {
    if (error && isOpen) {
      onClose();
    }
  }, [error, isOpen, onClose]);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t("progress.modalTitle", "Progress Details")}
      footer={
        <Button variant="secondary" onClick={onClose}>
          {t("common.close")}
        </Button>
      }
      className="max-w-2xl"
    >
      <ProgressList threadSlug={threadSlug} />
    </Modal>
  );
}
