const request = require("supertest");
const express = require("express");
const { workspaceDocumentEndpoints } = require("../workspaceDocuments");
const { Document } = require("../../models/documents");
const { Workspace } = require("../../models/workspace");
const {
  isDocumentProperlyVectorized,
  reVectorizeDocument,
} = require("../../utils/helpers/vectorizationCheck");

// Mock the required modules
jest.mock("../../models/documents", () => ({
  Document: {
    get: jest.fn(),
    where: jest.fn(),
    update: jest.fn(),
  },
}));

jest.mock("../../models/workspace", () => ({
  Workspace: {
    get: jest.fn(),
  },
}));

jest.mock("../../utils/helpers/vectorizationCheck", () => ({
  isDocumentProperlyVectorized: jest.fn(),
  reVectorizeDocument: jest.fn(),
}));

// Mock the middleware
jest.mock("../../utils/middleware/validApiKey", () => ({
  validApiKey: (req, res, next) => next(),
}));

describe("Workspace Document Endpoints", () => {
  let app;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create a new Express app for each test
    app = express();
    app.use(express.json());

    // Register the endpoints
    workspaceDocumentEndpoints(app);
  });

  describe("POST /workspace/:slug/update-star", () => {
    it("should update star status for a document that is properly vectorized", async () => {
      // Mock workspace and document
      const workspace = { id: "workspace-123", slug: "test-workspace" };
      const document = {
        id: "doc-123",
        docId: "doc-id-123",
        docpath: "path/to/doc.json",
        starred: false,
      };

      // Set up mocks
      Workspace.get.mockResolvedValue(workspace);
      Document.get.mockResolvedValue(document);
      isDocumentProperlyVectorized.mockResolvedValue(true);
      Document.update.mockResolvedValue({ ...document, starred: true });

      // Make the request
      const response = await request(app)
        .post("/workspace/test-workspace/update-star")
        .send({
          docPath: "path/to/doc.json",
          starStatus: true,
        });

      // Verify the response
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        message: "Star status updated successfully",
        requiresReVectorization: false,
        requiresMetadataUpdate: false,
      });

      // Verify the mocks were called correctly
      expect(Workspace.get).toHaveBeenCalledWith({ slug: "test-workspace" });
      expect(Document.get).toHaveBeenCalledWith({
        workspaceId: workspace.id,
        docpath: "path/to/doc.json",
      });
      expect(isDocumentProperlyVectorized).toHaveBeenCalledWith(
        workspace.slug,
        document.docId
      );
      expect(Document.update).toHaveBeenCalledWith(document.id, {
        starred: true,
      });
    });

    it("should not update star status for a document that is not properly vectorized", async () => {
      // Mock workspace and document
      const workspace = { id: "workspace-123", slug: "test-workspace" };
      const document = {
        id: "doc-123",
        docId: "doc-id-123",
        docpath: "path/to/doc.json",
        starred: false,
      };

      // Set up mocks
      Workspace.get.mockResolvedValue(workspace);
      Document.get.mockResolvedValue(document);
      isDocumentProperlyVectorized.mockResolvedValue(false);

      // Make the request
      const response = await request(app)
        .post("/workspace/test-workspace/update-star")
        .send({
          docPath: "path/to/doc.json",
          starStatus: true,
        });

      // Verify the response
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        message: "Document requires re-vectorization before it can be starred",
        requiresReVectorization: true,
        requiresMetadataUpdate: true,
        docId: document.docId,
        docPath: document.docpath,
      });

      // Verify the mocks were called correctly
      expect(Workspace.get).toHaveBeenCalledWith({ slug: "test-workspace" });
      expect(Document.get).toHaveBeenCalledWith({
        workspaceId: workspace.id,
        docpath: "path/to/doc.json",
      });
      expect(isDocumentProperlyVectorized).toHaveBeenCalledWith(
        workspace.slug,
        document.docId
      );
      expect(Document.update).not.toHaveBeenCalled();
    });

    it("should force update star status when forceUpdate is true", async () => {
      // Mock workspace and document
      const workspace = { id: "workspace-123", slug: "test-workspace" };
      const document = {
        id: "doc-123",
        docId: "doc-id-123",
        docpath: "path/to/doc.json",
        starred: false,
      };

      // Set up mocks
      Workspace.get.mockResolvedValue(workspace);
      Document.get.mockResolvedValue(document);
      // isDocumentProperlyVectorized would return false, but we're using forceUpdate
      Document.update.mockResolvedValue({ ...document, starred: true });

      // Make the request
      const response = await request(app)
        .post("/workspace/test-workspace/update-star")
        .send({
          docPath: "path/to/doc.json",
          starStatus: true,
          forceUpdate: true,
        });

      // Verify the response
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        message: "Star status updated successfully",
        requiresReVectorization: false,
        requiresMetadataUpdate: false,
      });

      // Verify the mocks were called correctly
      expect(Workspace.get).toHaveBeenCalledWith({ slug: "test-workspace" });
      expect(Document.get).toHaveBeenCalledWith({
        workspaceId: workspace.id,
        docpath: "path/to/doc.json",
      });
      // isDocumentProperlyVectorized should not be called when forceUpdate is true
      expect(isDocumentProperlyVectorized).not.toHaveBeenCalled();
      expect(Document.update).toHaveBeenCalledWith(document.id, {
        starred: true,
      });
    });

    it("should handle partial path matching when exact path is not found", async () => {
      // Mock workspace and documents
      const workspace = { id: "workspace-123", slug: "test-workspace" };
      const documents = [
        {
          id: "doc-123",
          docId: "doc-id-123",
          docpath: "path/to/document.json",
          starred: false,
        },
      ];

      // Set up mocks
      Workspace.get.mockResolvedValue(workspace);
      Document.get.mockResolvedValue(null); // Exact match fails
      Document.where.mockResolvedValue(documents); // Partial match succeeds
      isDocumentProperlyVectorized.mockResolvedValue(true);
      Document.update.mockResolvedValue({ ...documents[0], starred: true });

      // Make the request
      const response = await request(app)
        .post("/workspace/test-workspace/update-star")
        .send({
          docPath: "document.json",
          starStatus: true,
        });

      // Verify the response
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        message: "Star status updated successfully",
        requiresReVectorization: false,
        requiresMetadataUpdate: false,
      });

      // Verify the mocks were called correctly
      expect(Workspace.get).toHaveBeenCalledWith({ slug: "test-workspace" });
      expect(Document.get).toHaveBeenCalledWith({
        workspaceId: workspace.id,
        docpath: "document.json",
      });
      expect(Document.where).toHaveBeenCalledWith({
        workspaceId: workspace.id,
        docpath: { contains: "document.json" },
      });
      expect(isDocumentProperlyVectorized).toHaveBeenCalledWith(
        workspace.slug,
        documents[0].docId
      );
      expect(Document.update).toHaveBeenCalledWith(documents[0].id, {
        starred: true,
      });
    });

    it("should return 404 when document is not found", async () => {
      // Mock workspace
      const workspace = { id: "workspace-123", slug: "test-workspace" };

      // Set up mocks
      Workspace.get.mockResolvedValue(workspace);
      Document.get.mockResolvedValue(null); // Exact match fails
      Document.where.mockResolvedValue([]); // Partial match also fails

      // Make the request
      const response = await request(app)
        .post("/workspace/test-workspace/update-star")
        .send({
          docPath: "nonexistent.json",
          starStatus: true,
        });

      // Verify the response
      expect(response.status).toBe(404);
      expect(response.body).toEqual({
        message: "Document not found",
        error:
          "Document not found in workspace. It may have been deleted or not properly embedded.",
        docPath: "nonexistent.json",
      });

      // Verify the mocks were called correctly
      expect(Workspace.get).toHaveBeenCalledWith({ slug: "test-workspace" });
      expect(Document.get).toHaveBeenCalledWith({
        workspaceId: workspace.id,
        docpath: "nonexistent.json",
      });
      expect(Document.where).toHaveBeenCalledWith({
        workspaceId: workspace.id,
        docpath: { contains: "nonexistent.json" },
      });
      expect(Document.update).not.toHaveBeenCalled();
    });
  });

  describe("POST /workspace/:slug/re-vectorize", () => {
    it("should re-vectorize a document successfully", async () => {
      // Mock workspace and document
      const workspace = { id: "workspace-123", slug: "test-workspace" };
      const document = {
        id: "doc-123",
        docId: "doc-id-123",
        docpath: "path/to/doc.json",
      };

      // Set up mocks
      Workspace.get.mockResolvedValue(workspace);
      Document.get.mockResolvedValue(document);
      reVectorizeDocument.mockResolvedValue(true);

      // Make the request
      const response = await request(app)
        .post("/workspace/test-workspace/re-vectorize")
        .send({
          docId: "doc-id-123",
        });

      // Verify the response
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        message: "Document re-vectorized successfully",
        docId: document.docId,
        docPath: document.docpath,
      });

      // Verify the mocks were called correctly
      expect(Workspace.get).toHaveBeenCalledWith({ slug: "test-workspace" });
      expect(Document.get).toHaveBeenCalledWith({
        workspaceId: workspace.id,
        docId: "doc-id-123",
      });
      expect(reVectorizeDocument).toHaveBeenCalledWith(
        workspace.slug,
        "doc-id-123",
        document
      );
    });

    it("should return 400 when docId is missing", async () => {
      // Mock workspace
      const workspace = { id: "workspace-123", slug: "test-workspace" };

      // Set up mocks
      Workspace.get.mockResolvedValue(workspace);

      // Make the request without docId
      const response = await request(app)
        .post("/workspace/test-workspace/re-vectorize")
        .send({});

      // Verify the response
      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        success: false,
        message: "Document ID is required",
      });

      // Verify the mocks were called correctly
      expect(Workspace.get).toHaveBeenCalledWith({ slug: "test-workspace" });
      expect(Document.get).not.toHaveBeenCalled();
      expect(reVectorizeDocument).not.toHaveBeenCalled();
    });

    it("should return 404 when document is not found", async () => {
      // Mock workspace
      const workspace = { id: "workspace-123", slug: "test-workspace" };

      // Set up mocks
      Workspace.get.mockResolvedValue(workspace);
      Document.get.mockResolvedValue(null);

      // Make the request
      const response = await request(app)
        .post("/workspace/test-workspace/re-vectorize")
        .send({
          docId: "nonexistent-doc-id",
        });

      // Verify the response
      expect(response.status).toBe(404);
      expect(response.body).toEqual({
        success: false,
        message: "Document not found",
      });

      // Verify the mocks were called correctly
      expect(Workspace.get).toHaveBeenCalledWith({ slug: "test-workspace" });
      expect(Document.get).toHaveBeenCalledWith({
        workspaceId: workspace.id,
        docId: "nonexistent-doc-id",
      });
      expect(reVectorizeDocument).not.toHaveBeenCalled();
    });

    it("should return 500 when re-vectorization fails", async () => {
      // Mock workspace and document
      const workspace = { id: "workspace-123", slug: "test-workspace" };
      const document = {
        id: "doc-123",
        docId: "doc-id-123",
        docpath: "path/to/doc.json",
      };

      // Set up mocks
      Workspace.get.mockResolvedValue(workspace);
      Document.get.mockResolvedValue(document);
      reVectorizeDocument.mockResolvedValue(false);

      // Make the request
      const response = await request(app)
        .post("/workspace/test-workspace/re-vectorize")
        .send({
          docId: "doc-id-123",
        });

      // Verify the response
      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        message: "Failed to re-vectorize document",
        docId: document.docId,
        docPath: document.docpath,
      });

      // Verify the mocks were called correctly
      expect(Workspace.get).toHaveBeenCalledWith({ slug: "test-workspace" });
      expect(Document.get).toHaveBeenCalledWith({
        workspaceId: workspace.id,
        docId: "doc-id-123",
      });
      expect(reVectorizeDocument).toHaveBeenCalledWith(
        workspace.slug,
        "doc-id-123",
        document
      );
    });
  });
});
