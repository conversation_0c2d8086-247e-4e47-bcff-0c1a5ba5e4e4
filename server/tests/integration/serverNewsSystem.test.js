/**
 * Integration Test: Server-Based News System
 *
 * This test verifies that the server-based news system works correctly for both
 * direct module access and API endpoints.
 *
 * It ensures that the server is the single source of truth for news data,
 * preventing the production issue where only one news item was showing.
 */

const { describe, test, expect } = require("@jest/globals");
const request = require("supertest");
const express = require("express");

describe("Server-Based News System Integration", () => {
  let serverModule;

  beforeAll(() => {
    // Import the server news module
    serverModule = require("../../data/systemNewsItems.js");
  });

  describe("Server Module (Direct Access)", () => {
    test("should successfully import the server module", () => {
      expect(serverModule).toBeDefined();
      expect(serverModule.systemNewsItems).toBeDefined();
      expect(serverModule.getActiveSystemNews).toBeDefined();
    });

    test("should contain both system news items", () => {
      const { systemNewsItems } = serverModule;

      expect(systemNewsItems).toHaveLength(2);
      expect(systemNewsItems[0].id).toBe("system-welcome-2024");
      expect(systemNewsItems[1].id).toBe("system-new-features-2025");
    });

    test("should return active news items", () => {
      const { getActiveSystemNews } = serverModule;
      const activeNews = getActiveSystemNews();

      expect(activeNews).toHaveLength(2);
      expect(activeNews.every((item) => item.isActive)).toBe(true);
    });

    test("should find specific news items by ID", () => {
      const { getSystemNewsById } = serverModule;

      const welcomeNews = getSystemNewsById("system-welcome-2024");
      expect(welcomeNews).toBeDefined();
      expect(welcomeNews.id).toBe("system-welcome-2024");
      expect(welcomeNews.titleKey).toBe(
        "news-system-items.system-welcome-2024.title"
      );

      const featuresNews = getSystemNewsById("system-new-features-2025");
      expect(featuresNews).toBeDefined();
      expect(featuresNews.id).toBe("system-new-features-2025");
      expect(featuresNews.titleKey).toBe(
        "news-system-items.system-new-features-2025.title"
      );
    });

    test("should filter news by priority", () => {
      const { getSystemNewsByPriority } = serverModule;

      const highPriorityNews = getSystemNewsByPriority("high");
      expect(highPriorityNews).toHaveLength(1); // Welcome news is high priority

      const mediumPriorityNews = getSystemNewsByPriority("medium");
      expect(mediumPriorityNews).toHaveLength(1); // Features news is medium priority
    });

    test("should filter news by user roles", () => {
      const { getSystemNewsForRoles } = serverModule;

      // Both news items have targetRoles: null (show to all users)
      const allUsersNews = getSystemNewsForRoles(["user"]);
      expect(allUsersNews).toHaveLength(2);

      const adminNews = getSystemNewsForRoles(["admin"]);
      expect(adminNews).toHaveLength(2);
    });

    test("should resolve translations when provided", () => {
      const { resolveSystemNewsTranslations, getActiveSystemNews } =
        serverModule;

      // Mock translation function
      const mockT = (key, fallback) => {
        const translations = {
          "news-system-items.system-welcome-2024.title":
            "Välkommen till Foytechs plattform",
          "news-system-items.system-welcome-2024.content":
            "Välkommen till Foytechs plattform! Vi är glada att ha dig ombord.",
          "news-system-items.system-new-features-2025.title":
            "Nya systemfunktioner tillgängliga",
          "news-system-items.system-new-features-2025.content":
            "Vi har uppdaterat plattformen med flera nya funktioner.",
        };
        return translations[key] || fallback;
      };

      const activeNews = getActiveSystemNews();
      const translatedNews = activeNews.map((item) =>
        resolveSystemNewsTranslations(item, mockT)
      );

      expect(translatedNews).toHaveLength(2);

      const welcomeNews = translatedNews.find(
        (item) => item.id === "system-welcome-2024"
      );
      expect(welcomeNews.title).toBe("Välkommen till Foytechs plattform");

      const featuresNews = translatedNews.find(
        (item) => item.id === "system-new-features-2025"
      );
      expect(featuresNews.title).toBe("Nya systemfunktioner tillgängliga");
    });
  });

  describe("API Endpoint Integration", () => {
    let app;

    beforeAll(() => {
      // Create a minimal Express app for testing
      app = express();
      app.use(express.json());

      // Add the news endpoint
      app.get("/api/news/system", async (req, res) => {
        try {
          const {
            getActiveSystemNews,
          } = require("../../data/systemNewsItems.js");
          const systemNews = getActiveSystemNews();

          res.status(200).json({
            success: true,
            systemNews,
          });
        } catch (error) {
          console.error("Error fetching system news:", error);
          res.status(500).json({
            success: false,
            message: "Failed to fetch system news",
          });
        }
      });
    });

    test("should serve system news via API endpoint", async () => {
      const response = await request(app).get("/api/news/system").expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.systemNews).toBeDefined();
      expect(response.body.systemNews).toHaveLength(2);

      const newsIds = response.body.systemNews.map((item) => item.id);
      expect(newsIds).toContain("system-welcome-2024");
      expect(newsIds).toContain("system-new-features-2025");
    });

    test("should return same data as direct module access", async () => {
      const response = await request(app).get("/api/news/system").expect(200);

      const apiData = response.body.systemNews;
      const moduleData = serverModule.getActiveSystemNews();

      expect(apiData).toEqual(moduleData);
    });

    test("should handle API errors gracefully", async () => {
      // Create an app that throws an error
      const errorApp = express();
      errorApp.get("/api/news/system", (req, res) => {
        throw new Error("Test error");
      });

      const response = await request(errorApp)
        .get("/api/news/system")
        .expect(500);

      // The error should be caught and handled
      expect(response.status).toBe(500);
    });
  });

  describe("Data Consistency", () => {
    test("should have consistent data structure", () => {
      const { systemNewsItems } = serverModule;

      systemNewsItems.forEach((newsItem) => {
        // Required fields
        expect(newsItem.id).toBeDefined();
        expect(newsItem.priority).toBeDefined();
        expect(typeof newsItem.isActive).toBe("boolean");
        expect(typeof newsItem.isSystemNews).toBe("boolean");
        expect(newsItem.createdAt).toBeDefined();

        // Translation keys or direct content (items should have at least one)
        const hasTranslationKeys = !!(newsItem.titleKey && newsItem.contentKey);
        const hasDirectContent = !!(newsItem.title && newsItem.content);
        expect(hasTranslationKeys || hasDirectContent).toBe(true);

        // Valid priority
        expect(["low", "medium", "high", "urgent"]).toContain(
          newsItem.priority
        );

        // Valid dates
        expect(new Date(newsItem.createdAt).getTime()).not.toBeNaN();
        if (newsItem.expiresAt) {
          expect(new Date(newsItem.expiresAt).getTime()).not.toBeNaN();
        }
      });
    });

    test("should prevent the original production issue", () => {
      const { getActiveSystemNews } = serverModule;
      const activeNews = getActiveSystemNews();

      // This test ensures we have both news items that should be visible
      expect(activeNews).toHaveLength(2);

      const newsIds = activeNews.map((item) => item.id);
      expect(newsIds).toContain("system-welcome-2024");
      expect(newsIds).toContain("system-new-features-2025");

      // Verify both items are active
      expect(activeNews.every((item) => item.isActive)).toBe(true);
    });

    test("should maintain data integrity across different access methods", () => {
      const { getAllSystemNews, getActiveSystemNews, systemNewsItems } =
        serverModule;

      // All methods should return consistent data
      const allNews = getAllSystemNews();
      const activeNews = getActiveSystemNews();
      const directAccess = systemNewsItems;

      expect(allNews).toEqual(directAccess);
      expect(activeNews.length).toBeLessThanOrEqual(allNews.length);
      expect(activeNews.every((item) => item.isActive)).toBe(true);
    });
  });

  describe("Performance and Caching", () => {
    test("should handle multiple rapid calls efficiently", () => {
      const { getActiveSystemNews } = serverModule;

      const startTime = Date.now();

      // Make multiple calls
      for (let i = 0; i < 100; i++) {
        const news = getActiveSystemNews();
        expect(news).toHaveLength(2);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete quickly (less than 100ms for 100 calls)
      expect(duration).toBeLessThan(100);
    });
  });
});
