import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";

const UserPromptLibrary = {
  /**
   * Get all prompt libraries for the current user
   * @returns {Promise<{success: boolean, prompts?: Array, error?: string}>}
   */
  getAll: async function () {
    try {
      const response = await fetch(`${API_BASE}/user/prompt-library`, {
        method: "GET",
        headers: baseHeaders(),
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching prompt libraries:", error);
      return {
        success: false,
        error: "Failed to fetch prompt libraries",
      };
    }
  },

  /**
   * Get a specific prompt library by ID
   * @param {number} id - The prompt library ID
   * @returns {Promise<{success: boolean, prompt?: Object, error?: string}>}
   */
  getById: async function (id) {
    try {
      const response = await fetch(`${API_BASE}/user/prompt-library/${id}`, {
        method: "GET",
        headers: baseHeaders(),
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching prompt library:", error);
      return {
        success: false,
        error: "Failed to fetch prompt library",
      };
    }
  },

  /**
   * Create a new prompt library
   * @param {Object} promptData - The prompt data
   * @param {string} promptData.name - The prompt name
   * @param {string} promptData.prompt_text - The prompt text
   * @param {string} [promptData.description] - Optional description
   * @returns {Promise<{success: boolean, prompt?: Object, error?: string}>}
   */
  create: async function (promptData) {
    try {
      const response = await fetch(`${API_BASE}/user/prompt-library`, {
        method: "POST",
        headers: baseHeaders(),
        body: JSON.stringify(promptData),
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error creating prompt library:", error);
      return {
        success: false,
        error: "Failed to create prompt library",
      };
    }
  },

  /**
   * Update an existing prompt library
   * @param {number} id - The prompt library ID
   * @param {Object} promptData - The updated prompt data
   * @returns {Promise<{success: boolean, prompt?: Object, error?: string}>}
   */
  update: async function (id, promptData) {
    try {
      const response = await fetch(`${API_BASE}/user/prompt-library/${id}`, {
        method: "PUT",
        headers: baseHeaders(),
        body: JSON.stringify(promptData),
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error updating prompt library:", error);
      return {
        success: false,
        error: "Failed to update prompt library",
      };
    }
  },

  /**
   * Delete a prompt library
   * @param {number} id - The prompt library ID
   * @returns {Promise<{success: boolean, error?: string}>}
   */
  delete: async function (id) {
    try {
      const response = await fetch(`${API_BASE}/user/prompt-library/${id}`, {
        method: "DELETE",
        headers: baseHeaders(),
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error deleting prompt library:", error);
      return {
        success: false,
        error: "Failed to delete prompt library",
      };
    }
  },
};

export default UserPromptLibrary;
