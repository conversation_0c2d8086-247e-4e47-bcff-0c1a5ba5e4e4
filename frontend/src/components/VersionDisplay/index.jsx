import React, { useEffect, useState } from "react";
import Version from "@/models/version";
import SimpleTooltip from "@/components/ui/Tooltip";
import { useTranslation } from "react-i18next";

export default function VersionDisplay({ inline = false }) {
  const { t } = useTranslation();
  const [versionInfo, setVersionInfo] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchVersion() {
      try {
        const version = await Version.getVersion();
        setVersionInfo(version);
      } catch (error) {
        console.error("Failed to fetch version:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchVersion();
  }, []);

  // Don't render anything if loading or no version info
  if (loading || !versionInfo) {
    return null;
  }

  if (inline) {
    // Render as a menu item for dropdown
    return (
      <SimpleTooltip
        content={
          <div className="max-w-xs">
            <div className="font-semibold mb-1">
              {t("version.tooltip-title", "Version {{version}}", {
                version: versionInfo.version,
              })}
            </div>
            <div className="text-sm opacity-90">{versionInfo.description}</div>
          </div>
        }
        placement="left"
      >
        <div className="font-semibold text-[13px] text-foreground w-full text-center px-4 py-1 rounded-md flex flex-row items-center justify-center gap-2 outline-none cursor-default select-none">
          <span className="text-xs opacity-60">v{versionInfo.version}</span>
        </div>
      </SimpleTooltip>
    );
  }

  // Original fixed position styling
  return (
    <div className="fixed bottom-4 right-4 z-40">
      <SimpleTooltip
        content={
          <div className="max-w-xs">
            <div className="font-semibold mb-1">
              {t("version.tooltip-title", "Version {{version}}", {
                version: versionInfo.version,
              })}
            </div>
            <div className="text-sm opacity-90">{versionInfo.description}</div>
          </div>
        }
        placement="top"
      >
        <div className="bg-background/80 backdrop-blur-sm border border-border rounded-md px-2 py-1 text-sm text-muted-foreground hover:text-foreground hover:bg-background/90 transition-all duration-200 cursor-default select-none">
          v{versionInfo.version}
        </div>
      </SimpleTooltip>
    </div>
  );
}
