const fsPromises = require("fs").promises;
const { existsSync } = require("fs");
const path = require("path");
const { cleanOldDocxSessionFiles } = require("./files/docxSessionCleanup");

async function cleanOldUploadLogs() {
  try {
    const uploadLogsPath = path.join(
      process.env.NODE_ENV === "development"
        ? path.resolve(__dirname, "../storage/uploadlogs/document_uploads.log")
        : path.resolve(
            process.env.STORAGE_DIR,
            "uploadlogs/document_uploads.log"
          )
    );

    try {
      await fsPromises.access(uploadLogsPath);
    } catch (error) {
      console.log("Upload logs file does not exist, skipping cleanup");
      return;
    }

    const content = await fsPromises.readFile(uploadLogsPath, "utf8");
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    // Parse and filter logs
    const logs = content
      .split("\n")
      .filter((line) => line.trim())
      .map((line) => {
        try {
          return JSON.parse(line);
        } catch (e) {
          console.error("Error parsing log line:", e);
          return null;
        }
      })
      .filter((log) => log !== null);
    // Keep only logs newer than one month
    const recentLogs = logs.filter(
      (log) => new Date(log.dateAdded) > oneMonthAgo
    );
    // Write back filtered logs
    if (recentLogs.length < logs.length) {
      const newContent =
        recentLogs.map((log) => JSON.stringify(log)).join("\n") + "\n";

      await fsPromises.writeFile(uploadLogsPath, newContent, "utf8");
      console.log(
        `Cleaned up ${logs.length - recentLogs.length} old upload logs`
      );
    }
  } catch (error) {
    console.error("Error cleaning old upload logs:", error);
  }
}
async function cleanOldLogs() {
  try {
    const logsDir = path.join(__dirname, "../storage/logs");

    // Check if logs directory exists
    try {
      await fsPromises.access(logsDir);
    } catch (error) {
      console.log("Logs directory does not exist, skipping cleanup");
      // return; // Allow to proceed to other cleanup tasks
    }

    const files = existsSync(logsDir) ? await fsPromises.readdir(logsDir) : [];
    const twoDaysAgo = Date.now() - 2 * 24 * 60 * 60 * 1000; // 2 days in milliseconds

    for (const file of files) {
      const filePath = path.join(logsDir, file);
      try {
        const stats = await fsPromises.stat(filePath);
        if (!stats.isFile()) continue;
        if (stats.mtimeMs < twoDaysAgo) {
          await fsPromises.access(filePath); // Verify file still exists before deletion
          await fsPromises.unlink(filePath);
          console.log(`Deleted old log file: ${file}`);
        }
      } catch (error) {
        console.error(`Error processing log file ${file}:`, error.message);
        continue;
      }
    }

    // Clean up document-builder directory
    const docBuilderDir = path.join(__dirname, "../storage/document-builder");
    try {
      await fsPromises.access(docBuilderDir);

      const docBuilderFiles = await fsPromises.readdir(docBuilderDir);
      for (const file of docBuilderFiles) {
        const filePath = path.join(docBuilderDir, file);
        try {
          const stats = await fsPromises.stat(filePath);
          if (!stats.isFile()) continue;
          if (stats.mtimeMs < twoDaysAgo) {
            await fsPromises.access(filePath); // Verify file still exists before deletion
            await fsPromises.unlink(filePath);
            console.log(`Deleted old document-builder file: ${file}`);
          }
        } catch (error) {
          console.error(
            `Error processing document-builder file ${file}:`,
            error.message
          );
          continue;
        }
      }
    } catch (error) {
      console.log(
        "document-builder directory does not exist, skipping cleanup"
      );
    }

    await cleanOldUploadLogs();

    // Clean up old DOCX session files (older than 14 days)
    console.log("Running scheduled DOCX session file cleanup...");
    const docxCleanupResult = await cleanOldDocxSessionFiles(14); // 14 days retention
    if (docxCleanupResult.success) {
      console.log(
        `Successfully deleted ${docxCleanupResult.deletedCount} old DOCX session folders.`
      );
      if (docxCleanupResult.error) {
        console.warn(
          "Partial success in DOCX cleanup:",
          docxCleanupResult.error
        );
      }
    } else {
      console.error(
        "Failed to clean old DOCX session files:",
        docxCleanupResult.error
      );
    }
  } catch (error) {
    console.error("Error during general old log/file cleanup:", error);
  }
}

module.exports = {
  cleanOldLogs,
};
