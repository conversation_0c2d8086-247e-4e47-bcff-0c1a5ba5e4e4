const translations = {
  common: {
    thinking: "Ibitekerezo...",
  },
  errors: {
    ai: {
      ollama: {
        "connection-failed":
          "Ntabwo twashoboye kugera kuri Ollama yawe cyangwa ntiyitaba. Nyamuneka menya neza ko seriveri ya API ikora kandi amakuru yawe yo guhuza ari ukuri.",
        "empty-response":
          "Igisubizo cy'umwandiko wa Ollama cyari kirimo ubusa.",
        "stream-failed": "Ntabwo byashobotse gutunganya chat. {{message}}",
        "no-endpoint": "Nta endpoint ya API ya Ollama yashyizweho.",
        "no-token-limit": "Nta urugero rw'ibimenyetso bya Ollama rwashyizweho.",
        "provider-not-responding":
          "Utanga AI waho ntiyitaba, bishobora kuba ari ukubera ko seriveri idakora cyangwa ifite umuvuduko mwinshi.",
        "provider-unavailable":
          "Seriveri ya AI waho ntabwo ikoreshwa kuri ubu. Nyamuneka suzuma neza uko yashyizweho kandi urebe ko ikora neza kandi ntabwo ifite umuvuduko mwinshi.",
      },
      azure: {
        "no-endpoint": "Nta endpoint ya API ya Azure yashyizweho.",
        "no-key": "Nta urufunguzo rwa API ya Azure rwashyizweho.",
        "no-model": "Nta modeli ya Azure OpenAI yashyizweho.",
        "no-model-pref":
          "Nta OPEN_MODEL_PREF ENV yasobanuwe. Iki kigomba kuba izina ry'ishyirwa kuri konti yawe ya Azure kuri modeli ya LLM chat nka GPT-3.5.",
      },
      openai: {
        "no-key": "Nta urufunguzo rwa API ya OpenAI rwashyizweho.",
      },
      mistral: {
        "no-key": "Nta urufunguzo rwa API ya Mistral rwashyizweho.",
      },
      litellm: {
        "no-base-path":
          "LiteLLM igomba kugira inzira y'ibanze yemewe yo gukoresha API.",
        "no-model": "LiteLLM igomba kugira modeli yemewe yashyizweho.",
        "no-token-limit": "Nta urugero rw'ibimenyetso rwashyizweho.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI igomba kugira inzira y'ibanze yemewe yo gukoresha API.",
        "no-token-limit": "Nta urugero rw'ibimenyetso rwashyizweho.",
      },
      localai: {
        "no-token-limit":
          "Nta urugero rw'ibimenyetso bya LocalAi rwashyizweho.",
      },
      lmstudio: {
        "no-token-limit":
          "Nta urugero rw'ibimenyetso bya LMStudio rwashyizweho.",
      },
      fireworks: {
        "invalid-model":
          "FireworksAI chat: {{model}} ntibyemewe kuri chat completion!",
      },
      context: {
        header: "Inkomoko",
      },
    },
    system: {
      "ssl-boot-failed":
        "Gutangira SSL byanze: {{message}} - gusubira ku gutangira HTTP.",
      "no-app": 'Nta "app" yasobanuwe - guhomba!',
      validation: {
        "invalid-transcription-provider":
          "{{input}} si utanga serivisi wemewe w'inyandiko.",
        "invalid-safety-setting":
          "Igenamiterere ry'umutekano ritemewe. Rigomba kuba rimwe muri {{modes}}.",
        "invalid-model-type":
          "Ubwoko bwa modeli butemewe. Bugomba kuba bumwe muri {{models}}.",
        "invalid-embedding-model":
          "Ubwoko bwa modeli y'embedding butemewe. Bugomba kuba bumwe muri {{supported}}.",
      },
    },
    document: {
      "sync-failed":
        "Byanze guhuza inyandiko {{filename}} nyuma y'imirongo {{attempts}}.",
      "invalid-metadata":
        "Inyandiko {{filename}} ifite metadata itemewe cyangwa ibuze.",
      removed:
        "Inyandiko {{filename}} yakuwe mu matsinda y'inyandiko zikurikiranwa.",
    },
    validation: {
      "value-not-number": "Agaciro si umubare.",
      "value-must-be-nonzero": "Agaciro kagomba kuba katari zeru.",
      "value-cannot-be-negative": "Agaciro ntikashobora kuba munsi ya 0.",
      "invalid-serp-provider": "Utanga serivisi wa SERP utemewe.",
      "max-rerank-limit-min":
        "Umubare ntarengwa wo kongera gushyirwa mu byiciro ugomba kuba nibura 10.",
      "max-rerank-limit-max":
        "Umubare ntarengwa wo kongera gushyirwa mu byiciro ntushobora kurenza 200.",
    },
    embedding: {
      "lmstudio-failed": "Embedding ya LMStudio yananiwe: {{errors}}",
      "jina-failed": "Embedding ya Jina yananiwe: {{errors}}",
    },
    env: {
      "jina-api-key-error":
        "Urufunguzo rwa API ya Jina rugomba gutangira na jina_",
      "empty-value": "Agaciro ntikashobora kuba ubusa",
      "non-zero-required": "Agaciro kagomba kuba karenze zeru",
      "must-be-number": "Agaciro kagomba kuba umubare",
      "invalid-url": "URL si URL yemewe",
      "openai-key-format": "Urufunguzo rwa OpenAI rugomba gutangira na sk-",
      "contextual-prompt-format":
        "Ikibazo cy'umukoresha kigomba kuba kirimo {file} na {chunk}",
      "anthropic-key-format":
        "Urufunguzo rwa Anthropic rugomba gutangira na sk-ant-",
      "external-llm-url-format": "URL igomba kuba irimo /v1",
      "url-no-trailing-slash":
        "URL ntishobora kurangira n'akamenyetso ka slash",
      "invalid-tts-provider": "{{input}} si utanga serivisi wemewe wa TTS",
      "invalid-whisper-model": "{{input}} siyo ihitamo ryiza rya model Whisper",
      "invalid-llm-provider": "{{input}} si utanga serivisi wemewe wa LLM",
    },
    // Workspace sharing errors
    workspace: {
      onlyOwnerShare: "Ni ba nyir'ibyumba gusa bashobora gusangiza ibyumba",
      onlyOwnerRevoke:
        "Ni ba nyir'ibyumba gusa bashobora guhagarika gusangiza ibyumba",
      shareFailed: "Byananiwe gusangiza ibyumba",
      revokeFailed: "Byananiwe guhagarika gusangiza ibyumba",
      getStatusFailed: "Byananiwe kubona uko gusangiza ibyumba bimeze",
    },
    // Thread sharing errors
    thread: {
      onlyOwnerShare: "Ni ba nyir'imirongo gusa bashobora gusangiza imirongo",
      onlyOwnerRevoke:
        "Ni ba nyir'imirongo gusa bashobora guhagarika gusangiza imirongo",
      shareFailed: "Byananiwe gusangiza umurongo",
      revokeFailed: "Byananiwe guhagarika gusangiza umurongo",
      getStatusFailed: "Byananiwe kubona uko gusangiza imirongo bimeze",
    },
    auth: {
      "authentication-required": "Kugaragaza ubunyangamugayo bikenewe",
      "insufficient-permissions": "Ububasha butahagije",
    },
  },
  validation: {
    responseHeader: "Igisubizo Cyatanzwe",
    contextHeader: "Inyandiko n'Inkomoko by'Umwimerere",
    content: {
      "user-question-sources-header": "IKIBAZO CY'UKORESHA N'INKOMOKO",
      "system-response-header": "IGISUBIZO CY'UBURYO",
      "not-provided": "Ntabwo byatanzwe",
    },
  },
  manualWorkEstimator: {
    content: {
      "user-question-header": "IKIBAZO CY'UKORESHA",
      "system-response-header": "IGISUBIZO CY'UBURYO",
    },
  },
  notifications: {
    document: {
      invalid:
        "Inyandiko {{filename}} nta metadata ifite, yangiritse, cyangwa ntiyemewe kandi yakuweho.",
      removed:
        "Inyandiko {{filename}} yakuwe mu matsinda y'inyandiko zikurikiranwa.",
    },
  },
  docx: {
    title: "Kohereza Ikiganiro",
    exportedOn: "Byoherejwe",
    downloadedOn: "Byakuwe",
    keywords: "ikiganiro, kohereza",
    description: "Ubutumwa bwoherejwe",
    tokenCount: "Umubare wa tokens",
    canvasDocumentTitle: "Inyandiko ya Canvas",
    errors: {
      contentRequired: "Ibikubiyemo birakenewe",
      writingFile: "Ikosa mu kwandika dosiye y'agateganyo",
      generating: "Ikosa mu gukora inyandiko",
    },
  },
  deep_search: {
    instruction:
      "Niba hari inkomoko za DeepSearch zijyanye n'ikibazo cy'umukoresha kandi zikoreshwa mu gisubizo, tanga amahuriro ashobora gukandwaho kuri izo nkomoko ku mpera z'ubutumwa muri ubu buryo nyabwo: 'Inkomoko: [Umutwe w'Inkomoko 1](URL1), [Umutwe w'Inkomoko 2](URL2)'. Umwandiko ushobora gukandwaho kuri buri huriro ugomba kuba ibisobanuro bigufi by'ibirimo mu nkomoko (urugero, umutwe w'urupapuro cyangwa incamake). Ntukigere werekeza ku nkomoko za DeepSearch ukoresheje imibare mu mwandiko (nka [1] cyangwa [Inkomoko 1]); buri gihe vuga inkomoko ukoresha izina ryayo cyangwa ibikubiyemo mu buryo butaziguye. Ntukoreshe uburyo bwo gukoresha inkomoko bwa akademiki.",
  },
};

module.exports = translations;
