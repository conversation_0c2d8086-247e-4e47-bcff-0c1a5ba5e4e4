/*
  Custom System Prompt Feature Migration

  This migration adds the custom_system_prompt field to the users table to support
  user-specific system prompt customization. This feature allows users to override
  the default system prompt with their own personalized instructions.

  Changes:
  - Adds custom_system_prompt TEXT field to users table
  - Field is nullable to allow users to opt-in to custom prompts
  - When null, the system will fall back to workspace or default prompts
  - When set, user's custom prompt takes highest priority in the prompt hierarchy

  Prompt Hierarchy (highest to lowest priority):
  1. User custom system prompt (this field)
  2. Workspace system prompt (openAiPrompt field in workspaces table)
  3. System default prompt (hardcoded fallback)
*/

-- AlterTable
ALTER TABLE "users" ADD COLUMN "custom_system_prompt" TEXT;
