import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Sidebar from "@/components/SettingsSidebar";
import { isMobile } from "react-device-detect";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import {
  Notepad,
  CheckCircle,
  XCircle,
  ClockClockwise,
  Play,
  Stop,
  ArrowLeft,
  Trash,
} from "@phosphor-icons/react";
import MCPServers from "@/models/mcpServers";
import showToast from "@/utils/toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/Button";
import FormItem from "@/components/ui/FormItem";
import Input from "@/components/ui/Input";
import Label from "@/components/ui/Label";
import Textarea from "@/components/ui/Textarea";
import PreLoader from "@/components/Preloader";
import paths from "@/utils/paths";
import { Link } from "react-router-dom";
import { useModal } from "@/hooks/useModal";
import Modal from "@/components/ui/Modal";
import { useConfirmation } from "@/hooks/useConfirmation";

const parseJsonString = (value) => {
  try {
    return JSON.parse(value);
  } catch (e) {
    return null;
  }
};

// Zod schema for the config JSON string
const configSchema = z.object({
  command: z.string().min(1, "Command is required"),
  args: z.array(z.string(), { required_error: "Args array is required" }),
  env: z.record(z.string()).optional(),
});

// Zod schema for the main form
const formSchema = z.object({
  serverName: z.string().min(1, "Server name is required"),
  configJson: z
    .string()
    .min(1, "Configuration JSON is required")
    .transform((value, ctx) => {
      const parsed = parseJsonString(value);
      if (parsed === null) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Invalid JSON format",
        });
        return z.NEVER;
      }
      const validationResult = configSchema.safeParse(parsed);
      if (!validationResult.success) {
        // Flatten errors for better display
        const errors = validationResult.error.flatten().fieldErrors;
        const errorMessages = Object.entries(errors)
          .map(([field, msgs]) => `${field}: ${msgs.join(", ")}`)
          .join("; ");
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Invalid config: ${errorMessages}`,
        });
        return z.NEVER;
      }
      return validationResult.data; // Return the validated config object
    }),
});

export default function MCPManagement() {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [servers, setServers] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {
    isOpen: isAddModalOpen,
    openModal: openAddModal,
    closeModal: closeAddModal,
  } = useModal();
  const {
    isConfirmationOpen,
    openConfirmation,
    closeConfirmation,
    handleConfirm,
  } = useConfirmation();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      serverName: "",
      configJson: '{\n  "command": "",\n  "args": []\n}',
    },
  });

  const fetchServers = async () => {
    setLoading(true);
    const {
      success,
      servers: serverList,
      error,
    } = await MCPServers.listServers();
    if (success) {
      setServers(serverList);
    } else {
      showToast(
        t("admin.mcp.fetchError", "Failed to fetch servers: {{error}}", {
          error,
        }),
        "error"
      );
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchServers();
  }, []);

  const handleAddServer = async (data) => {
    setIsSubmitting(true);
    const { serverName, configJson: config } = data;
    const { success, message, error } = await MCPServers.addServer(
      serverName,
      config
    );
    if (success) {
      showToast(
        message || t("admin.mcp.addSuccess", "Server added successfully."),
        "success"
      );
      reset();
      closeAddModal();
      fetchServers();
    } else {
      showToast(
        t("admin.mcp.addError", "Failed to add server: {{error}}", { error }),
        "error"
      );
    }
    setIsSubmitting(false);
  };

  const handleDeleteServer = async (serverName) => {
    openConfirmation(async () => {
      const { success, error } = await MCPServers.deleteServer(serverName);
      if (success) {
        showToast(
          t(
            "admin.mcp.deleteSuccess",
            "Server '{{serverName}}' deleted successfully.",
            { serverName }
          ),
          "success"
        );
        fetchServers();
      } else {
        showToast(
          t(
            "admin.mcp.deleteError",
            "Failed to delete server '{{serverName}}': {{error}}",
            { serverName, error }
          ),
          "error"
        );
      }
    });
  };

  // Helper to render server status icon
  const renderStatusIcon = (server) => {
    if (!server.config)
      return (
        <ClockClockwise
          className="w-4 h-4 text-orange-500"
          title="Config not found"
        />
      ); // Should not happen if fetch is correct
    if (server.error)
      return (
        <XCircle
          className="w-4 h-4 text-red-500"
          title={`Error: ${server.error}`}
        />
      );
    if (server.running)
      return <CheckCircle className="w-4 h-4 text-green-500" title="Running" />;
    return (
      <ClockClockwise
        className="w-4 h-4 text-gray-500"
        title="Stopped/Not Started"
      />
    );
  };

  if (isMobile) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-between p-4 bg-background shadow-md">
          <Link to={paths.settings.system()}>
            <ArrowLeft className="h-6 w-6 text-foreground" />
          </Link>
          <p className="text-lg font-medium text-foreground">
            {t("mcp.title")}
          </p>
          <div className="w-6"></div>
        </div>
        <div className="p-4">
          <ContentArea
            loading={loading}
            servers={servers}
            errors={errors}
            register={register}
            handleSubmit={handleSubmit}
            handleAddServer={handleAddServer}
            isSubmitting={isSubmitting}
            fetchServers={fetchServers}
            t={t}
            renderStatusIcon={renderStatusIcon}
            openAddModal={openAddModal}
            handleDeleteServer={handleDeleteServer}
          />
        </div>
        <AddServerModal
          isOpen={isAddModalOpen}
          onClose={closeAddModal}
          register={register}
          handleSubmit={handleSubmit}
          handleAddServer={handleAddServer}
          errors={errors}
          isSubmitting={isSubmitting}
          t={t}
        />
      </div>
    );
  }

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="flex-1 flex flex-col h-full overflow-y-auto bg-background">
          <div className="top-main-block relative rounded-md w-full h-full overflow-y-scroll">
            <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
              <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-4">
                <Link to={paths.settings.system()}>
                  <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
                </Link>
                <div className="flex flex-col">
                  <div className="items-center">
                    <p className="text-lg leading-6 font-bold text-foreground flex items-center gap-x-1">
                      <Notepad className="w-5 h-5" />
                      {t("mcp.title")}
                    </p>
                  </div>
                  <div className="pb-4 mb-4 border-b-2 border-border">
                    <p className="text-base text-muted-foreground">
                      {t("mcp.description")}
                    </p>
                  </div>
                </div>
              </div>

              <ContentArea
                loading={loading}
                servers={servers}
                fetchServers={fetchServers}
                t={t}
                renderStatusIcon={renderStatusIcon}
                openAddModal={openAddModal}
                handleDeleteServer={handleDeleteServer}
              />
            </div>
          </div>
        </div>
      </div>
      <AddServerModal
        isOpen={isAddModalOpen}
        onClose={closeAddModal}
        register={register}
        handleSubmit={handleSubmit}
        handleAddServer={handleAddServer}
        errors={errors}
        isSubmitting={isSubmitting}
        t={t}
      />
      <Modal
        isOpen={isConfirmationOpen}
        onClose={closeConfirmation}
        title={t("admin.mcp.deleteConfirmTitle", "Confirm Deletion")}
        footer={
          <>
            <Button variant="secondary" onClick={closeConfirmation}>
              {t("common.cancel")}
            </Button>
            <Button variant="destructive" onClick={handleConfirm}>
              {t("common.delete")}
            </Button>
          </>
        }
      >
        <p className="text-foreground">
          {t(
            "admin.mcp.deleteConfirmMessageGeneric",
            "Are you sure you want to delete this MCP server? This action cannot be undone."
          )}
        </p>
      </Modal>
    </div>
  );
}

const ContentArea = ({
  loading,
  servers,
  fetchServers,
  t,
  renderStatusIcon,
  openAddModal,
  handleDeleteServer,
}) => (
  <div className="bg-card w-full p-6 rounded-lg flex flex-col gap-4 mt-4">
    <div className="flex flex-col space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium text-foreground">
          {t("mcp.currentServers")}
        </h2>
        <Button onClick={openAddModal}>{t("mcp.addServerButton")}</Button>
      </div>
      {loading ? (
        <PreLoader />
      ) : servers.length === 0 ? (
        <div className="text-center py-4">
          <p className="text-muted-foreground">{t("mcp.noServers")}</p>
        </div>
      ) : (
        <div className="space-y-3 max-h-[60vh] overflow-y-auto pr-2">
          {servers.map((server) => (
            <div
              key={server.name}
              className="border border-border p-3 rounded-md"
            >
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-x-2">
                  {renderStatusIcon(server)}
                  <span className="font-medium text-sm text-foreground">
                    {server.name}
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-red-500 hover:bg-red-100"
                  onClick={() => handleDeleteServer(server.name)}
                  aria-label={`Delete server ${server.name}`}
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </div>
              {server.config && (
                <pre className="mt-2 text-xs bg-secondary p-2 rounded overflow-x-auto text-muted-foreground">
                  {JSON.stringify(server.config, null, 2)}
                </pre>
              )}
              {server.error && !server.running && (
                <p className="mt-1 text-xs text-red-500">
                  Error: {server.error}
                </p>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  </div>
);

const AddServerModal = ({
  isOpen,
  onClose,
  register,
  handleSubmit,
  handleAddServer,
  errors,
  isSubmitting,
  t,
}) => (
  <Modal
    isOpen={isOpen}
    onClose={onClose}
    size="xl"
    title={t("mcp.addServerModalTitle")}
    description={t("mcp.addServerModalDesc")}
  >
    <div className="flex flex-col gap-y-1 p-6 pt-2">
      <form onSubmit={handleSubmit(handleAddServer)} className="space-y-4">
        <FormItem>
          <Label htmlFor="serverName">{t("mcp.serverName")}</Label>
          <Input
            id="serverName"
            {...register("serverName")}
            placeholder="e.g., my-custom-tool"
            className="w-full"
            required
          />
          {errors.serverName && (
            <p className="text-red-500 text-xs mt-1">
              {errors.serverName.message}
            </p>
          )}
        </FormItem>
        <FormItem>
          <Label htmlFor="configJson">{t("mcp.configJson")}</Label>
          <Textarea
            id="configJson"
            {...register("configJson")}
            placeholder={
              '{\n"command": "executable_or_script",\n"args": ["arg1", "arg2"],\n"env": { "VAR": "value" }\n}'
            }
            className="w-full min-h-[150px] font-mono text-sm"
            required
          />
          {errors.configJson && (
            <p className="text-red-500 text-xs mt-1">
              {errors.configJson.message}
            </p>
          )}
        </FormItem>
        <div className="flex justify-end space-x-2">
          <Button variant="secondary" onClick={onClose}>
            {t("common.cancel")}
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? t("common.saving", "Saving...")
              : t("mcp.addButton")}
          </Button>
        </div>
      </form>
    </div>
  </Modal>
);
