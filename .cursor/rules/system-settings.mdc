---
description: Guidelines for implementing system settings
globs: server/models/systemSettings.js,frontend/src/pages/Admin/System/**/*.jsx,frontend/src/stores/settingsStore.js
alwaysApply: false
---
# Implementing System Settings

## Important Note
System settings and ENV settings are two distinct configuration mechanisms that must not be mixed up:

- **System Settings**: Use internal APIs and endpoints (e.g., `Admin.systemPreferences()`, `System.updateSettings()`). These settings are stored in the database and can be modified through the application interface.
- **ENV Settings**: Environment variables defined in `.env` files. These are loaded at startup and typically contain sensitive information like API keys, database credentials, and server configurations.

Never use ENV settings endpoints or mechanisms to modify system settings, and vice versa. Each has its own distinct purpose and API interface.

---

## Backend Implementation

### 1. Add Setting to SystemSettings Model
- Add your new setting to the `supportedFields` array in `server/models/systemSettings.js`.
- Add a default value and retrieval logic using `getValueOrFallback`.
- Add a validation function for your setting in the `validations` object.

```js
// server/models/systemSettings.js
supportedFields: [
  // ...
  "your_new_setting",
],

validations: {
  // ...
  your_new_setting: (value) => {
    // Validate and transform value as needed
    if (typeof value !== "string") throw new Error("Must be a string");
    return value.trim();
  },
  // ...
},

// Usage
const value = await SystemSettings.getValueOrFallback({ label: "your_new_setting" }, defaultValue);
```

### 2. Expose via Endpoint
- Add or update a REST endpoint in `server/endpoints/system.js` to get and update your setting. Use `SystemSettings.updateSettings()` for updates (which applies validation).

```js
app.get("/system/your-new-setting", async (_, res) => {
  const value = await SystemSettings.getValueOrFallback(
    { label: "your_new_setting" },
    defaultValue
  );
  res.status(200).json({ your_new_setting: value });
});

app.post("/system/your-new-setting", async (req, res) => {
  const { your_new_setting } = req.body;
  const result = await SystemSettings.updateSettings({ your_new_setting });
  if (result.success) {
    res.status(200).json({ success: true });
  } else {
    res.status(400).json({ success: false, error: result.error });
  }
});
```

---

## Frontend Implementation

System settings are managed using a centralized Zustand store in `frontend/src/stores/settingsStore.js`.

### 1. Add to Zustand Store
- Add your setting to the `ENDPOINTS` object in `settingsStore.js`:

```js
const ENDPOINTS = {
  // ...
  "your-new-setting": {
    endpoint: "/system/your-new-setting",
    updateEndpoint: "/system/your-new-setting",
    defaultValue: "", // or your default value
  },
  // ...
};
```

- (Optional) Add a selector hook for easy access:

```js
export const useYourNewSetting = () =>
  useSystemSettingsStore((state) => state.getSetting("your-new-setting"));
```

### 2. Use Selector Hook in Components
- In your component, use the selector hook to access the setting:

```js
import { useYourNewSetting } from "@/stores/settingsStore";

function MyComponent() {
  const value = useYourNewSetting();
  // ...
}
```

### 3. Update Setting
- Use the store's `updateSetting` method to update the value:

```js
const updateSetting = useSystemSettingsStore((state) => state.updateSetting);

const handleChange = (newValue) => {
  updateSetting("your-new-setting", { your_new_setting: newValue });
};
```

### 4. Add Translations
- Add any new UI text to `frontend/src/locales/en/common.js` using the appropriate key structure. (See internationalization-with-i18n.mdc for translation key rules.)

### 5. Naming Consistency & Payload Mapping

System endpoints sometimes expect **snake_case** field names (e.g. `document_drafting`) while the frontend store keeps the same values in **camelCase** (`isDocumentDrafting`).
To avoid bugs where settings appear to save but are not persisted:

1. **Check backend endpoint payload requirements** (inspect `server/endpoints/system.js`).
2. **Transform payload keys** inside `settingsStore.updateSetting` when there is a mismatch.
   ```js
   // Example mapping for the Document Drafting toggle
   if (key === "document-drafting") {
     payload = {
       document_drafting: data?.isDocumentDrafting ?? false,
       document_drafting_linking: data?.isDocumentDraftingLinking ?? false,
     };
   }
   ```
3. **Keep store state in camelCase** for ergonomic React usage, but always send the backend-expected names in network requests.
4. **Document any new mappings** in this rule file so future developers know to keep them in sync.

💡  When adding new settings, prefer the same naming on both sides. If that is not feasible, *always* add the mapping layer in `frontend/src/stores/settingsStore.js`.

---

## Good and Bad Examples

### Backend
**Good:**
```js
// Always use getValueOrFallback and updateSettings
const value = await SystemSettings.getValueOrFallback(
  { label: "my_setting" },
  defaultValue
);
await SystemSettings.updateSettings({ my_setting: newValue });
```
**Bad:**
```js
// ❌ Direct DB access (bypasses validation and business logic)
const value = (
  await prisma.system_settings.findFirst({ where: { label: "my_setting" } })
)?.value;

// ❌ Directly calling .get() for updates (bypasses validation)
await SystemSettings.get({ label: "my_setting" });
```

### Frontend
**Good:**
```js
// Always use the Zustand store and selector hooks
const value = useMySetting();
updateSetting("my-setting", { my_setting: newValue });
```
**Bad:**
```js
// ❌ Direct API calls (bypasses store, breaks state sync)
fetch("/system/my-setting", {
  method: "POST",
  body: JSON.stringify({ my_setting: newValue }),
});

// ❌ Managing state in local React state (bypasses store)
const [mySetting, setMySetting] = useState(defaultValue);
```

---

## Best Practices

### Type Safety
- Always validate and convert values to the correct type (backend and frontend)
- Use appropriate default values
- Handle edge cases and invalid inputs

### Error Handling
- Implement proper error handling in both frontend and backend
- Show appropriate error messages to users
- Log errors for debugging

### State Management
- Use a centralized Zustand store for all system settings in the frontend
- Use selector hooks to access settings in components
- Never bypass the store or backend validation
- Track changes separately for each setting
- Reset change flags after successful updates
- Handle loading and saving states

### UI/UX
- Group related settings together
- Provide clear descriptions and help text
- Show immediate feedback for user actions
- All UI text should use translation keys (see istlegal.mdc)

### Testing
- Test both frontend and backend changes
- Verify error handling
- Check edge cases and default values

---

## Summary Checklist

### Backend
- Add new setting to supportedFields and validations
- Use getValueOrFallback for retrieval
- Use updateSettings for updates
- Expose via REST endpoint (never bypass validation)
- Add tests for validation and edge cases

### Frontend
- Add setting to ENDPOINTS in settingsStore.js
- Use selector hooks to access settings in components
- Use updateSetting for updates
- Never manage system settings with local React state
- Add translations for all UI text
- Add tests for UI and error handling
