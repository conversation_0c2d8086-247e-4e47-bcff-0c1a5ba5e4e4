# Iterative Context Window Management

This documentation describes the implementation of iterative context window management for handling large documents and complex processing tasks that exceed LLM token limits.

## Overview

The iterative context window management system provides sophisticated handling of LLM context window limitations through:

- **Token-aware chunking** for large documents
- **Iterative section drafting** when content exceeds context limits
- **Smart content prioritization** based on relevance and token budgets
- **Real-time token tracking** with comprehensive metrics collection
- **Progressive content integration** rather than all-at-once processing

## Core Components

### ContextWindowManager

The `ContextWindowManager` class is the main utility for handling token-aware processing:

```javascript
const { ContextWindowManager } = require('./utils/chats/helpers/contextWindowManager');

const contextManager = new ContextWindowManager(LLMConnector, {
  maxIterations: 10,
  reservedOutputTokens: 4000,
  enableTokenTracking: true,
  logTokenUsage: false
});
```

#### Key Methods

- `getAvailableContextWindow(reservedTokens)` - Calculate available tokens for content
- `calculateTokenBudget(params)` - Allocate tokens across different content types
- `chunkContent(content, options)` - Split content into token-aware chunks
- `processIteratively(params)` - Handle multi-pass processing for large content

### TokenTracker

The `TokenTracker` class provides comprehensive token metrics collection:

```javascript
const { TokenTracker } = require('./utils/chats/helpers/tokenTracker');

const tokenTracker = new TokenTracker(LLMConnector, {
  enableDetailedTracking: true,
  trackContentTypes: true,
  logTokenUsage: true
});
```

#### Features

- Real-time token budget monitoring
- Stage-based processing tracking
- Content type breakdown analysis
- Iterative processing metrics
- Budget utilization reporting
- Error and warning tracking

## Usage Patterns

### Document Description with Large Content

For documents that may exceed context limits:

```javascript
const description = await generateDocumentDescriptionIterative(
  docName,
  content,
  legalTask,
  LLMConnector,
  {
    temperature: 0.7,
    reservedTokens: 2000,
    overlapTokens: 200,
    logTokenUsage: true
  }
);
```

### Iterative Section Drafting

For section drafting with large amounts of supporting content:

```javascript
const result = await contextManager.processIteratively({
  processor: processIterativeSectionDrafting,
  documents: sectionDocuments,
  memos: sectionMemos,
  budget: tokenBudget,
  context: {
    sectionNumber,
    sectionTitle,
    legalTask,
    neighborContext
  }
});
```

### Token Budget Management

Calculate and allocate token budgets efficiently:

```javascript
const budget = contextManager.calculateTokenBudget({
  systemPrompt: "Your system prompt here",
  userPromptTemplate: "Template with {{placeholders}}",
  reservedTokens: 4000
});

// Budget breakdown:
// - budget.total: Total available tokens
// - budget.system: Tokens used by system prompt
// - budget.userPromptOverhead: Tokens used by user prompt template
// - budget.availableForContent: Tokens available for documents/memos
// - budget.reserved: Tokens reserved for output
```

## Integration with Document Processing

### Enhanced Document Processing

The system integrates with existing document processing workflows:

```javascript
// Replace standard document description generation
const description = await generateDocumentDescriptionIterative(
  docName,
  content,
  legalTask,
  LLMConnector,
  options
);

// Replace standard relevance checking
const isRelevant = await generateDocumentRelevanceIterative(
  docName,
  content,
  legalTask,
  LLMConnector,
  options
);
```

### Section Drafting Enhancement

Enhanced section drafting with automatic iterative processing:

```javascript
const sectionResult = await contextManager.processIteratively({
  processor: async (iterationData) => {
    return await processIterativeSectionDrafting({
      ...iterationData,
      context: sectionInfo,
      LLMConnector,
      prompts: sectionPrompts,
      temperature: 0.7
    });
  },
  documents: sectionDocuments,
  memos: sectionMemos,
  budget: tokenBudget,
  context: sectionContext
});
```

## Token Tracking and Metrics

### Comprehensive Tracking

The system provides detailed tracking of token usage:

```javascript
// Track content by type
tokenTracker.trackContentTokens(content, 'documents', documentName);
tokenTracker.trackContentTokens(memoContent, 'memos', memoIssue);

// Track processing stages
const stageTracker = tokenTracker.startStage('documentDescriptions');
// ... processing work ...
stageTracker.addTokens(tokensUsed, 'operation-name');
stageTracker.finish();

// Track LLM responses
tokenTracker.trackLLMResponse(llmResult, 'stageName', 'operationName');
```

### Metrics and Reporting

Generate comprehensive reports for optimization:

```javascript
const report = tokenTracker.generateReport();

console.log(`Total tokens processed: ${report.summary.totalTokens}`);
console.log(`Budget utilization: ${report.summary.budgetUtilization}`);
console.log(`Processing time: ${report.summary.processingTime}ms`);

// Get optimization recommendations
report.budgetAnalysis.recommendations.forEach(rec => {
  console.log(`Recommendation: ${rec}`);
});
```

## Configuration Options

### ContextWindowManager Options

```javascript
{
  defaultOverlapTokens: 100,        // Default overlap between chunks
  maxIterations: 10,                // Maximum iterations before stopping
  reservedOutputTokens: 4000,       // Tokens reserved for LLM output
  enableTokenTracking: true,        // Enable comprehensive token tracking
  logTokenUsage: false              // Log detailed token usage (for debugging)
}
```

### TokenTracker Options

```javascript
{
  enableDetailedTracking: true,     // Enable detailed metrics collection
  trackContentTypes: true,          // Track tokens by content type
  logTokenUsage: false              // Log token usage in real-time
}
```

### Processing Options

```javascript
{
  temperature: 0.7,                 // LLM generation temperature
  reservedTokens: 2000,             // Tokens reserved for this operation
  overlapTokens: 200,               // Overlap between content chunks
  maxTokensPerChunk: 8000,          // Maximum tokens per chunk
  strategy: 'token'                 // Chunking strategy ('token' or 'semantic')
}
```

## Performance Considerations

### Chunking Strategy

- **Token-based chunking**: Fast and accurate for token limits
- **Semantic chunking**: Preserves content meaning (future enhancement)
- **Overlap**: Prevents information loss at chunk boundaries

### Iteration Limits

- Default maximum of 10 iterations prevents infinite loops
- Early termination when no new content can be processed
- Progressive refinement rather than complete reprocessing

### Memory Management

- Processed content tracking prevents duplication
- Chunk-by-chunk processing reduces memory footprint
- Token counting uses efficient tiktoken implementation

## Error Handling

### Graceful Degradation

```javascript
try {
  const result = await contextManager.processIteratively({
    processor: myProcessor,
    documents,
    memos,
    budget,
    context
  });
} catch (error) {
  console.error('Iterative processing failed:', error);

  // Access partial results and token metrics
  const partialReport = contextManager.getTokenTracker()?.generateReport();
  console.log('Partial processing metrics:', partialReport);
}
```

### Common Issues and Solutions

1. **Budget Exceeded**: Increase `reservedOutputTokens` or reduce content per iteration
2. **Too Many Iterations**: Increase `maxTokensPerChunk` or improve content prioritization
3. **Low Performance**: Enable `logTokenUsage` to identify bottlenecks

## Testing

### Unit Tests

Comprehensive unit tests are available with 100% pass rate:

- `server/tests/unit/utils/contextWindowManager.test.js` (11 tests)
- `server/tests/unit/utils/tokenTracker.test.js` (27 tests)

### Running Tests

```bash
cd server
npm test -- tests/unit/utils/contextWindowManager.test.js
npm test -- tests/unit/utils/tokenTracker.test.js

# Run both test suites together
npm test -- --testNamePattern="ContextWindowManager|TokenTracker"
```

### Jest Testing Challenges and Solutions

During implementation, several Jest-specific testing challenges were encountered and resolved:

#### 1. TokenManager Mock Incompleteness

**Issue**: The original Jest setup (`server/tests/setup.js`) mocked the TokenManager but only included `countFromString` and `statsFrom` methods. The ContextWindowManager requires additional methods for token-based chunking.

**Error**: `TypeError: this.tokenManager.tokensFromString is not a function`

**Solution**: Enhanced the TokenManager mock to include all required methods:

```javascript
// In server/tests/setup.js
jest.mock("../utils/helpers/tiktoken", () => ({
  TokenManager: jest.fn().mockImplementation(() => ({
    countFromString: jest.fn((str) => Math.ceil(str.length / 4)),
    statsFrom: jest.fn(() => ({ tokens: 100 })),
    tokensFromString: jest.fn((str) => {
      // Mock tokenization by splitting on words and creating a rough token array
      const words = str.split(/\s+/).filter(word => word.length > 0);
      const tokens = [];
      for (let i = 0; i < words.length; i++) {
        // Each word gets roughly 1-2 tokens
        tokens.push(i * 2);
        if (words[i].length > 6) tokens.push(i * 2 + 1);
      }
      return tokens;
    }),
    bytesFromTokens: jest.fn((tokens) => {
      // Mock detokenization - just create dummy text based on token count
      return "word ".repeat(Math.ceil(tokens.length / 2)).trim();
    }),
  })),
}));
```

#### 2. Timing-Dependent Test Failures

**Issue**: Tests for stage duration tracking failed intermittently because JavaScript execution was too fast for meaningful duration measurement.

**Error**: `expect(received).toBeGreaterThan(expected) Expected: > 0 Received: 0`

**Solution**: Added explicit delays in timing-sensitive tests:

```javascript
test("should track processing stages", async () => {
  const stageTracker = tokenTracker.startStage("testStage");

  stageTracker.addTokens(1000, "operation1");

  // Add a small delay to ensure duration > 0
  await new Promise((resolve) => setTimeout(resolve, 10));

  stageTracker.addTokens(500, "operation2");
  stageTracker.finish();

  // Now duration will be > 0
  expect(metrics.stages.testStage.duration).toBeGreaterThan(0);
});
```

#### 3. Token Calculation Mismatches

**Issue**: Tests with hardcoded expected values failed due to mock token calculations not matching the expected totals.

**Solution**: Adjusted test data to match mock behavior or calculated expected values dynamically:

```javascript
// Before - hardcoded expectation that failed
expect(metrics.iterativeMetrics.averageTokensPerIteration).toBe(850);

// After - calculated expectation with comment explaining the math
// Total tokens: 800 + 700 = 1500, so average = 750
expect(metrics.iterativeMetrics.averageTokensPerIteration).toBe(
  Math.round(1500 / 2)
);
```

#### 4. Missing Method in TokenTracker

**Issue**: The main flow code called `tokenTracker.getTokenUsageForStage()` but this method didn't exist in the initial implementation.

**Solution**: Added the missing method to TokenTracker:

```javascript
/**
 * Get token usage for a specific stage
 * @param {string} stageName - Name of the processing stage
 * @returns {number} Total tokens used in that stage
 */
getTokenUsageForStage(stageName) {
  if (!this.metrics.stages[stageName]) {
    return 0;
  }
  return this.metrics.stages[stageName].tokens;
}
```

#### 5. Jest vs. Real Environment Discrepancies

**Issue**: Code that worked perfectly outside of Jest failed within the test environment due to mocking issues.

**Debugging Approach**: Tested functionality outside Jest to confirm real behavior:

```bash
# Standalone test to verify actual functionality
node -e "
const { ContextWindowManager } = require('./utils/chats/helpers/contextWindowManager');
const mockLLM = { model: 'gpt-3.5-turbo', promptWindowLimit: () => 16384 };
const cm = new ContextWindowManager(mockLLM);
const chunks = cm.chunkContent('word '.repeat(2000), { maxTokensPerChunk: 500 });
console.log('Chunking successful, created', chunks.length, 'chunks');
"
```

### Best Practices for Testing Context Window Management

1. **Mock Completeness**: When mocking TokenManager, ensure all methods used by the code under test are included
2. **Timing Considerations**: Use explicit delays for tests that measure duration or time-based operations
3. **Dynamic Expectations**: Calculate expected values based on mock behavior rather than hardcoding arbitrary numbers
4. **Integration Testing**: Test outside Jest environment for complex token operations to verify real behavior
5. **Method Coverage**: Ensure all public methods are tested and all required methods exist before integration
6. **Mock Verification**: Regularly verify that mocks match the actual implementation interface
7. **Error Path Testing**: Test both success and failure scenarios, especially for timeout and budget overflow cases

### Integration Testing

Integration tests demonstrate real-world usage scenarios with mock LLM responses and realistic document sizes, including:

- Complete iterative processing workflows
- Large document chunking scenarios
- Token budget overflow handling
- Multi-iteration processing with progressive content integration

## Best Practices

### 1. Token Budget Planning

- Calculate budgets before processing begins
- Reserve adequate tokens for LLM output
- Monitor budget utilization through metrics

### 2. Content Prioritization

- Process most relevant documents first
- Use relevance checking to filter content
- Prioritize main documents over supporting materials

### 3. Iterative Strategy

- Start with smaller chunks for faster iteration
- Use refinement iterations to enhance existing content
- Monitor iteration count and adjust chunk sizes accordingly

### 4. Performance Monitoring

- Enable token tracking for optimization insights
- Review budget recommendations regularly
- Use stage tracking to identify bottlenecks

### 5. Error Recovery

- Implement fallback strategies for budget overruns
- Log comprehensive metrics for debugging
- Use partial results when full processing fails

## Future Enhancements

### Planned Features

1. **Semantic Chunking**: Preserve document structure and meaning
2. **Dynamic Budget Adjustment**: Automatically adjust based on content complexity
3. **Parallel Processing**: Process independent chunks concurrently
4. **Advanced Prioritization**: ML-based content relevance scoring
5. **Caching Integration**: Cache processed chunks for reuse

### Extension Points

The system is designed for extensibility:

- Custom chunking strategies via strategy pattern
- Pluggable token tracking backends
- Configurable iteration termination conditions
- Custom content prioritization algorithms

## Migration Guide

### From Standard Processing

Replace standard document processing calls:

```javascript
// Before
const description = await generateDocumentDescription(docName, content, task, LLM);

// After (automatic fallback for small content)
const description = await generateDocumentDescriptionIterative(docName, content, task, LLM);
```

### Integration with Existing Flows

The iterative system is designed to be backward compatible:

- Small documents process normally without chunking
- Large documents automatically use iterative processing
- Existing progress reporting continues to work
- Metrics collection is additive (doesn't break existing tracking)

This ensures smooth integration with minimal changes to existing code while providing powerful new capabilities for handling large content.
