# DOCX Handling in Canvas

This document describes the helper files and processes involved in importing and exporting DOCX files in the Canvas component of the ISTLegal application.

## Overview

The Canvas component allows users to import DOCX files, view and edit their content in markdown format, and export the edited content back to DOCX format. This functionality is implemented through a combination of frontend and backend components.

## Key Components

### Frontend Components

1. **CanvasChat Component** (`frontend/src/components/WorkspaceChat/ChatContainer/ChatHistory/HistoricalMessage/Actions/CanvasChat/index.jsx`)

   - Main component that handles the Canvas chat interface
   - Manages the state for DOCX content
   - Provides UI for uploading and downloading DOCX files
   - Handles the display of imported DOCX content in the chat

2. **DocxContent Component** (`frontend/src/components/WorkspaceChat/ChatContainer/ChatHistory/HistoricalMessage/Actions/CanvasChat/DocxContent.jsx`)
   - Renders the DOCX content in the Canvas chat
   - Uses ReactMarkdown to display the content with proper formatting

### Backend Components

1. **DOCX LLM Processor** (`server/endpoints/docxLlmProcessor.js`)

   - Handles the processing of DOCX files with LLM (Language Learning Model)
   - Manages the API endpoints for DOCX processing

2. **DOCX to Markdown Converter** (`server/utils/docx/docxToMarkdown.js`)

   - Converts DOCX files to markdown format
   - Preserves formatting such as headings, lists, tables, etc.
   - Ensures proper handling of special characters and styles

3. **Markdown to DOCX Converter** (`server/utils/docx/textToDocx.js`)

   - Converts markdown content back to DOCX format using `unified`, `remark-parse`, and `remark-docx`.
   - Maintains formatting and structure during conversion.

4. **Compare and Highlight** (`server/utils/docx/compareAndHighlight.js`)

   - Compares original and edited DOCX content
   - Highlights changes (additions, deletions, modifications)
   - Helps users visualize the changes made to the document

5. **Edit with LLM** (`server/utils/docx/editWithLLM.js`)
   - Processes DOCX content with LLM based on user instructions
   - Applies edits to the document based on natural language instructions

## Import Process

The DOCX import process follows these steps:

1. User uploads a DOCX file through the Canvas chat interface
2. The file is sent to the server via a multipart form request
3. The server processes the file using `docxToMarkdown.js` to convert it to markdown
4. The markdown content is returned to the frontend
5. The Canvas chat component displays the markdown content in the chat
6. The user can edit the content or run prompts on it just like with any other Canvas content
7. The content is only saved to the chat history when the user clicks the Save button

```javascript
// Example of DOCX file upload handling in CanvasChat component
const handleDocxFileChange = useCallback(
  async (acceptedFiles) => {
    if (!acceptedFiles || acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    if (
      file.type !==
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ) {
      showToast(t("show-toast.invalid-file-type"), "error");
      return;
    }

    setIsProcessingDocx(true);

    try {
      const formData = new FormData();
      formData.append("file", file);

      const contentResponse = await fetch("/api/docx/convert", {
        method: "POST",
        body: formData,
      });

      if (!contentResponse.ok) {
        throw new Error(`HTTP error! status: ${contentResponse.status}`);
      }

      const contentData = await contentResponse.json();

      if (contentData.success && contentData.content) {
        const docxContent = contentData.content;

        // Set the DOCX content as the regular canvas text
        setStreamedResponse(docxContent);
        setDisplayedLines(docxContent.split("\n"));

        // Also keep the original DOCX content for reference
        setDocxContent(docxContent);

        // Turn off DOCX mode to display content in the main chat box
        setIsDocxMode(false);

        showToast(
          t("docx-edit.import-success", "DOCX content imported successfully"),
          "success"
        );
      }
    } catch (error) {
      console.error("Error processing DOCX file:", error);
      showToast(t("show-toast.docx-processing-error"), "error");
    } finally {
      setIsProcessingDocx(false);
      setIsDocxMode(false);
    }
  },
  [t]
);
```

## Export Process

The DOCX export process follows these steps:

1. User edits the markdown content in the Canvas chat
2. User clicks the download button to export the content as a DOCX file
3. The frontend sends the markdown content to the server
4. The server processes the content using `textToDocx.js` to convert it to DOCX
5. The DOCX file is returned to the frontend
6. The browser downloads the DOCX file

```javascript
// Example of DOCX file download handling in CanvasChat component
const handleDownloadDocx = useCallback(async () => {
  try {
    const content = streamedResponse;
    if (!content) {
      showToast(t("docx-edit.no-content"), "error");
      return;
    }

    const response = await fetch("/api/docx/convert-to-docx", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ content }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.style.display = "none";
    a.href = url;
    a.download = "document.docx";
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);

    showToast(t("docx-edit.download-success"), "success");
  } catch (error) {
    console.error("Error downloading DOCX:", error);
    showToast(t("docx-edit.download-error") + error.message, "error");
  }
}, [streamedResponse, t]);
```

## LLM Editing Process

The LLM editing process follows these steps:

1. User uploads a DOCX file through the Canvas chat interface
2. The file is converted to markdown and displayed in the chat
3. User provides editing instructions in natural language
4. The instructions and markdown content are sent to the server
5. The server processes the content using `editWithLLM.js`
6. The LLM applies the requested edits to the document
7. The edited content is returned to the frontend
8. The Canvas chat component displays the edited content
9. User can download the edited content as a DOCX file

## Server API Endpoints

The following API endpoints are used for DOCX handling:

1. **POST /api/docx/convert**

   - Converts a DOCX file to markdown
   - Accepts a multipart form with a file field
   - Returns the markdown content

2. **POST /api/docx/convert-to-docx**

   - Converts markdown content to a DOCX file
   - Accepts a JSON body with a content field
   - Returns the DOCX file as a blob

3. **POST /api/docx/edit**

   - Edits a document using LLM based on instructions
   - Accepts a JSON body with content and instructions fields
   - Returns the edited content

4. **POST /api/docx/compare**
   - Compares original and edited content
   - Accepts a JSON body with original and edited fields
   - Returns the content with highlighted changes

## Implementation Details

### DOCX to Markdown Conversion

The `docxToMarkdown.js` utility uses the `mammoth` library to convert DOCX files to HTML, and then converts the HTML to markdown using a custom converter. This ensures that formatting such as headings, lists, tables, and styles are preserved.

```javascript
// Example of DOCX to Markdown conversion
const mammoth = require("mammoth");
const html2md = require("html-to-md");

async function docxToMarkdown(buffer) {
  try {
    const result = await mammoth.convertToHtml({ buffer });
    const html = result.value;
    const markdown = html2md(html, {
      // Custom conversion options
    });

    return markdown;
  } catch (error) {
    console.error("Error converting DOCX to markdown:", error);
    throw error;
  }
}
```

### Markdown to DOCX Conversion

The `textToDocx.js` utility uses the `remark-docx` library to create a DOCX document from markdown content. It parses the markdown to identify headings, paragraphs, lists, and other formatting elements, and creates corresponding DOCX elements.

#### HTML Tag Support

The system supports HTML tags for rich text formatting in Word export with the following conversions:

- **Highlighting**: `<mark style="background-color: yellow">highlighted text</mark>` → **bold text** in Word
- **Text Colors**: `<span style="color: red">colored text</span>` → _italic text_ in Word

**Implementation Details:**

Since `remark-docx` has limited support for advanced formatting like background colors and text colors, the system converts HTML tags to standard markdown formatting as visual indicators:

```javascript
// HTML tags are converted to markdown during preprocessing:
// <mark style="background-color: yellow">text</mark> → **text**
// <span style="color: red">text</span> → *text*

async function textToDocx(text, outputPath) {
  // Convert HTML tags to markdown formatting
  let processedText = text;

  processedText = processedText.replace(
    /<mark\s+style="background-color:\s*([^"]+)"[^>]*>(.*?)<\/mark>/gi,
    (match, color, content) => `**${content}**`
  );

  processedText = processedText.replace(
    /<span\s+style="color:\s*([^"]+)"[^>]*>(.*?)<\/span>/gi,
    (match, color, content) => `*${content}*`
  );

  // Process with remark-docx
  const processor = unified().use(markdown).use(docx, { output: "buffer" });

  const doc = await processor.process(processedText);
  // ...
}
```

**Rationale:** This approach ensures compatibility with the `remark-docx` library while providing visual indicators for rich text formatting. Bold formatting represents highlighted text, and italic formatting represents colored text.

### Compare and Highlight

The `compareAndHighlight.js` utility compares the original and edited content using a diff algorithm, and highlights the changes using different colors or styles. This helps users visualize the changes made to the document.

```javascript
// Example of Compare and Highlight
const diff = require("diff");

function compareAndHighlight(original, edited) {
  const changes = diff.diffWords(original, edited);

  // Process changes and add highlighting
  // ...

  return highlightedContent;
}
```

## Best Practices

1. **Error Handling**: Always handle errors gracefully and provide meaningful error messages to users.
2. **File Size Limits**: Implement file size limits to prevent server overload.
3. **Format Preservation**: Ensure that formatting is preserved during conversion.
4. **Security**: Validate and sanitize user input to prevent security vulnerabilities.
5. **Performance**: Optimize conversion processes for large documents.
6. **HTML Tag Support**: When using HTML tags for rich formatting:
   - Use `<mark style="background-color: color">text</mark>` for highlighting (converts to **bold** in Word)
   - Use `<span style="color: color">text</span>` for colored text (converts to _italic_ in Word)
   - Any color values are supported, but they will be converted to standard markdown formatting
   - This approach ensures compatibility with the `remark-docx` library

## Troubleshooting

### Common Issues

1. **Formatting Loss**: Some complex formatting may be lost during conversion. Consider using simpler formatting for better results.
2. **Large Files**: Very large files may cause performance issues. Consider splitting large documents into smaller ones.
3. **Special Characters**: Special characters may not be properly converted. Use standard Unicode characters when possible.

### Debugging

1. Check the browser console for frontend errors.
2. Check the server logs for backend errors.
3. Use the network tab in browser developer tools to inspect API requests and responses.

## Future Improvements

1. **Track Changes**: Implement a track changes feature to show edits in a more user-friendly way.
2. **Comments**: Add support for comments in DOCX files.
3. **Templates**: Create templates for common document types.
4. **Collaborative Editing**: Implement real-time collaborative editing of DOCX files.
