/**
 * Flow Dispatcher for Document Drafting
 *
 * This module provides a unified entry point for different document drafting flows:
 * - "main": The main document flow (either with a main document or adapting if none is provided)
 * - "noMain": The explicit no-main-document flow
 */

const { runMainDocFlow } = require("./flows/mainDoc");
const { runNoMainDocFlow } = require("./flows/noMainDoc");
const { runReferenceFilesFlow } = require("./flows/referenceFlow");
const { purgeDocumentBuilder } = require("../files");

/**
 * Determine the appropriate flow type based on legal task configuration and mainDocName
 *
 * @param {string} [explicitFlowType] - Explicitly requested flow type (if provided)
 * @param {Object} [legalTask] - The legal task configuration object
 * @param {string} [mainDocName] - The name of the main document (if provided)
 * @returns {string} - The determined flow type ("main" or "noMain")
 */
function determineFlowType(explicitFlowType, legalTask, mainDocName) {
  // Case 1: Explicit flow type is provided and valid
  if (
    explicitFlowType &&
    ["main", "noMain", "referenceFiles"].includes(explicitFlowType)
  ) {
    return explicitFlowType;
  }

  // Case 2: Legal task configuration specifies flow type
  if (
    legalTask &&
    legalTask.flowType &&
    ["main", "noMain"].includes(legalTask.flowType)
  ) {
    return legalTask.flowType;
  }

  // Case 3: Determine by presence of mainDocName (Updated Logic)
  if (mainDocName) {
    return "main";
  } else {
    return "noMain";
  }
}

/**
 * Main flow dispatcher - routes to the appropriate flow based on flowType
 *
 * @param {Object} options - Options for the flow
 * @param {string} [options.flowType="main"] - The type of flow to run (can be overridden by legalTask config)
 * @param {Object} options.request - The request object
 * @param {Object} options.response - The response object
 * @param {Object} options.workspace - The workspace object
 * @param {string} options.message - The legal task message
 * @param {string} [options.chatMode="chat"] - Chat mode
 * @param {Object} [options.user=null] - User object
 * @param {Object} [options.thread=null] - Thread object
 * @param {Array} [options.attachments=[]] - Attachments
 * @param {string} [options.chatId] - Chat ID
 * @param {boolean} [options.isCanvasChat=false] - Whether this is a canvas chat
 * @param {boolean} [options.preventChatCreation=false] - Whether to prevent chat creation
 * @param {string} [options.settings_suffix=""] - Settings suffix
 * @param {string} [options.invoice_ref] - Invoice reference
 * @param {string} [options.vectorSearchMode="default"] - Vector search mode
 * @param {boolean} [options.hasUploadedFile=false] - Whether a file was uploaded
 * @param {string} [options.displayMessage=null] - Display message
 * @param {boolean} [options.useDeepSearch=false] - Whether to use deep search
 * @param {Array} [options.cdbOptions=[]] - CDB options array [legalPrompt, customInstructions, mainDocNameFromOptions]
 * @param {string} [options.mainDocName=null] - Explicitly passed main document name (takes precedence over cdbOptions)
 * @param {Object} [options.legalTask=null] - Legal task configuration object
 * @param {AbortSignal} [options.abortSignal=null] - Abort signal
 * @returns {Promise<void>}
 */
async function runFlow(options = {}) {
  const { abortSignal = null } = options;
  const { ...baseOptions } = options;

  const mainDocName =
    baseOptions.mainDocName ||
    (baseOptions.cdbOptions && baseOptions.cdbOptions[2]) ||
    null;
  const legalTask = baseOptions.legalTask || null;

  const determinedFlowType = determineFlowType(
    baseOptions.cdbOptions?.[3],
    legalTask,
    mainDocName
  );
  const flowRunnerOptions = {
    ...baseOptions,
    mainDocName,
    legalTask,
    abortSignal,
    flowType: determinedFlowType,
  };

  // Capture the output of the selected flow so it can be returned to callers
  let result;

  try {
    if (determinedFlowType === "main") {
      result = await runMainDocFlow(flowRunnerOptions);
    } else if (determinedFlowType === "noMain") {
      result = await runNoMainDocFlow(flowRunnerOptions);
    } else if (determinedFlowType === "referenceFiles") {
      result = await runReferenceFilesFlow(flowRunnerOptions);
    } else {
      console.error(
        `[FLOW DISPATCHER] Unsupported flow type: ${determinedFlowType}`
      );
      if (baseOptions.response && !baseOptions.response.writableEnded) {
        baseOptions.response.status(500).json({
          error: "Internal server error: Unsupported document drafting flow.",
        });
      }
    }
  } catch (error) {
    console.error(
      `[FLOW DISPATCHER] Error during ${determinedFlowType} flow execution for chatId ${flowRunnerOptions.chatId}:`,
      error
    );
    if (baseOptions.response && !baseOptions.response.writableEnded) {
      // Ensure an error response is sent via SSE if possible
      const { writeResponseChunk } = require("../helpers/chat/responses"); // Local require for safety
      writeResponseChunk(baseOptions.response, {
        uuid: flowRunnerOptions.chatId,
        type: "abort",
        error: error.message || "Flow execution failed unexpectedly.",
        close: true,
      });
    }
    // Re-throw the error so the calling endpoint handler can also catch it and terminate the response.
    throw error;
  } finally {
    console.log(
      `[FLOW DISPATCHER] Flow finished or errored for chatId: ${flowRunnerOptions.chatId}. Attempting cleanup.`
    );
    try {
      const removed = purgeDocumentBuilder({ uuid: flowRunnerOptions.chatId });
      if (removed > 0) {
        console.log(
          `[FLOW DISPATCHER] Cleaned up ${removed} temporary files from document-builder.`
        );
      }
    } catch (cleanupError) {
      console.error(
        `[FLOW DISPATCHER] Error during cleanup for chatId ${flowRunnerOptions.chatId}:`,
        cleanupError
      );
    }
  }

  // Return the flow's final combined document content (if any)
  return result;
}

module.exports = {
  runFlow,
  determineFlowType,
};
