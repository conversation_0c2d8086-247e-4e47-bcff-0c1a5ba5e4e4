export default {
  // =========================
  // DOCUMENT DRAFTING
  // =========================
  "document-drafting": {
    title: "Dokumentutkast",
    description: "Kontrollera inställningarna för dokumentutkast.",
    configuration: "Konfiguration",
    "drafting-model": "Dokumentutkast LLM",
    enabled: "Dokumentutkast är aktiverat",
    disabled: "Dokumentutkast är inaktiverat",
    "enabled-toast": "Dokumentutkast aktiverat",
    "disabled-toast": "Dokumentutkast inaktiverat",
    "desc-settings":
      "Administratören kan ändra inställningarna för dokumentutkast för alla användare.",
    "drafting-llm": "LLM-preferens för dokumentutkast",
    saving: "Sparar...",
    save: "Spara ändringar",
    "chat-settings": "Chattinställningar",
    "drafting-chat-settings": "Chattinställningar för dokumentutkast",
    "chat-settings-desc":
      "Kontrollera hur chattfunktionen beter sig för dokumentutkast.",
    "drafting-prompt": "Systemprompt för dokumentutkast",
    "drafting-prompt-desc":
      "Systemprompten som används i dokumentutkastet skiljer sig från systemprompten för juridisk Q&A. Denna prompt definierar kontext och instruktioner så att AI genererar ett relevant och korrekt svar.",
    linking: "Dokumentlänkning",
    "legal-issues-prompt": "Prompt för juridiska frågor",
    "legal-issues-prompt-desc": "Ange prompten för juridiska frågor.",
    "memo-prompt": "Prompt för anteckning",
    "memo-prompt-desc": "Ange prompten för memo/anteckningar.",
    "desc-linkage":
      "Aktivera ytterligare juridisk kontext genom att använda vektor/PDR-sökningar ovanpå memohämtning",
    message: {
      title: "Föreslagna meddelanden för dokumentutkast",
      description:
        "Lägg till förslag på meddelanden som användare snabbt kan välja vid dokumentutkast.",
      heading: "Standardmeddelanderubrik",
      body: "Standardmeddelandetext",
      "new-heading": "Meddelanderubrik",
      message: "Meddelandeinnehåll",
      add: "Lägg till meddelande",
      save: "Spara meddelanden",
    },
    "combine-prompt": "Kombinationsprompt",
    "combine-prompt-desc":
      "Ange systemprompten för att kombinera flera svar till ett enda svar. Denna prompt används både för att kombinera svar och DD Linkage-rapporter, och för att kombinera de olika svaren från Infinity Context-bearbetning.",
    "page-description":
      "Denna sida är för att justera de olika promptar som används i olika funktioner i dokumentutkastmodulen. I varje inmatningsfält visas standardprompten, som kommer att användas om inte en anpassad prompt tillämpas på denna sida.",
    "dd-linkage-steps": "Promptar som tillämpas för DD-länkningssteg",
    "general-combination-prompt": "Allmän kombinationsprompt",
    "import-memo": {
      title: "Importera rättsutredning",
      "button-text": "Importera PM",
      "search-placeholder": "Sök trådar...",
      import: "Importera",
      importing: "Importerar...",
      "no-threads": "Inga rättsutredningstrådar hittades",
      "no-matching-threads": "Inga trådar matchar din sökning",
      "thread-not-found": "Vald tråd hittades inte",
      "empty-thread": "Den valda tråden har inget innehåll att importera",
      "import-success": "Trådinnehåll importerades framgångsrikt",
      "import-error": "Kunde inte importera trådinnehåll",
      "import-error-details": "Fel vid import: {{details}}",
      "fetch-error": "Kunde inte hämta trådar. Försök igen senare.",
      "imported-from": "Importerad från Legal QA-tråd",
      "unnamed-thread": "Namnlös tråd",
      "unknown-workspace": "Okänd arbetsyta",
      "no-threads-available": "Inga trådar tillgängliga att importera",
      "create-conversations-first":
        "Skapa utredning i Extern AI först, sedan kan du importera dem här.",
      "no-legal-qa-workspaces":
        "Inga Extern AI-arbetsytor med aktiva trådar hittades. Skapa utredningar i en Extern AI-arbetsyta först för att importera dem.",
      "empty-workspaces-with-names":
        "Hittade arbetsytor ({{workspaceNames}}) men de innehåller inga aktiva trådar ännu. Skapa konversationer i dessa arbetsytor först för att importera dem.",
      "import-success-with-name": "Tråden har importerats: {{threadName}}",
    },
  },

  // =========================
  // DD SETTINGS (WORKSPACE LINKING SETTINGS)
  // =========================
  "dd-settings": {
    title: "Inställningar för länkning av arbetsytor",
    description:
      "Kontrollera tokenbegränsningar och beteende för länkade arbetsytor",
    "vector-search": {
      title: "Vektorsökning",
      description:
        "När denna funktion är aktiverad utförs semantiska vektorsökningar över alla länkade arbetsytor för att hitta relevanta juridiska dokument. Systemet konverterar användarfrågor till vektorinbäddningar och matchar dem mot dokumentvektorer i varje länkad arbetsytas databas. Denna funktion fungerar som en reservlösning när PM-generering är aktiverad men misslyckas med att producera resultat. När PM-generering är inaktiverad blir Vektorsökning den primära metoden för att hämta information från länkade arbetsytor. Sökdjupet kontrolleras av inställningen för Vektortokenbegränsning.",
    },
    "memo-generation": {
      title: "PM-generering",
      description:
        "Denna funktion genererar automatiskt koncisa juridiska PM från dokument som finns i länkade arbetsytor. När den är aktiverad analyserar systemet hämtade dokument för att skapa strukturerade sammanfattningar av juridiska nyckelpunkter, prejudikat och relevant kontext. Dessa PM fungerar som den primära metoden för att införliva kunskap från länkade arbetsytor. Om PM-generering misslyckas eller inte ger några resultat, kommer systemet automatiskt att återgå till Vektorsökning (om aktiverad) för att säkerställa att relevant information ändå hämtas. Längden och detaljnivån på dessa PM styrs av inställningen för PM-tokenbegränsning.",
    },
    "base-generation": {
      title: "Grundläggande juridisk analys",
      description:
        "Möjliggör generering av preliminär juridisk analys baserad på användarens ursprungliga förfrågan, innan information från länkade arbetsytor införlivas. När den är aktiv skapar systemet ett grundläggande analysramverk som hjälper till att vägleda de efterföljande dokumentsök- och PM-genereringsprocesserna. Denna grundanalys hjälper till att säkerställa att svar förblir fokuserade på de centrala juridiska frågorna samtidigt som stödjande information från länkade arbetsytor införlivas. Omfattningen av denna analys kontrolleras av inställningen för Grundtokenbegränsning.",
    },
    "linked-workspace-impact": {
      title: "Tokenpåverkan för länkade arbetsytor",
      description:
        "Kontrollerar hur systemet hanterar sin tokenbudget över flera länkade arbetsytor. När denna funktion är aktiverad justerar systemet dynamiskt de tillgängliga tokens för varje arbetsyta baserat på det totala antalet länkade arbetsytor, vilket säkerställer en rättvis fördelning av dataresurser. Detta förhindrar att en enskild arbetsyta dominerar kontextfönstret samtidigt som det upprätthåller omfattande täckning över alla relevanta juridiska områden. Denna inställning reserverar tokenkapacitet specifikt för PM-generering och/eller Vektorsökningsresultat från varje länkad arbetsyta, vilket kan minska det totala antalet tokens som är tillgängliga för den primära arbetsytan när många arbetsytor är länkade.",
    },
    "vector-token-limit": {
      title: "Vektortokenbegränsning",
      description:
        "Anger det maximala antalet tokens som tilldelas för vektorsökningsresultat från varje länkad arbetsyta. Denna begränsning gäller när Vektorsökning används, antingen som den primära metoden (när PM-generering är inaktiverad) eller som en reservlösning (när PM-generering misslyckas). Högre gränser möjliggör mer omfattande dokumenthämtning men minskar tokens tillgängliga för andra operationer.",
    },
    "memo-token-limit": {
      title: "PM-tokenbegränsning",
      description:
        "Kontrollerar den maximala längden på genererade juridiska PM från varje länkad arbetsyta. Som den primära metoden för kunskapsintegrering sammanfattar dessa PM juridiska nyckelpunkter från den länkade arbetsytans dokument. Om ett PM överskrider denna tokengräns kommer det att avvisas och systemet återgår till Vektorsökning (om aktiverad). Högre gränser möjliggör mer detaljerad juridisk analys men kan minska antalet länkade arbetsytor som kan införlivas.",
    },
    "base-token-limit": {
      title: "Grundtokenbegränsning",
      description:
        "Bestämmer den maximala tokenlängden för det inledande juridiska analysramverket. Denna begränsning påverkar hur omfattande grundanalysen kan vara innan information från länkade arbetsytor införlivas. En högre begränsning möjliggör mer detaljerad inledande analys men lämnar mindre utrymme för att införliva innehåll från länkade arbetsytor.",
    },
    "toast-success": "Inställningarna uppdaterades framgångsrikt",
    "toast-fail": "Det gick inte att uppdatera inställningarna",
  },

  // =========================
  // WORKSPACE LINKING
  // =========================
  "workspace-linking": {
    title: "Inställningar för länkning av arbetsytor",
    description:
      "Kontrollera tokenbegränsningar och beteende för länkade arbetsytor",
    "vector-search": {
      title: "Vektorsökning",
      description:
        "Reservmetod för att hitta relevanta dokument när PM-generering misslyckas eller är inaktiverad",
    },
    "memo-generation": {
      title: "PM-generering",
      description:
        "Primär metod för att införliva kunskap från länkade arbetsytor",
    },
    "base-generation": {
      title: "Grundläggande juridisk analys",
      description:
        "Generera inledande juridisk problemanalys från användarförfrågningar",
    },
    "linked-workspace-impact": {
      title: "Tokenpåverkan för länkade arbetsytor",
      description:
        "Reservera tokens för varje länkad arbetsyta i proportion till deras antal",
    },
    "vector-token-limit": {
      title: "Vektortokengräns",
      description:
        "Maximalt antal tokens per länkad arbetsyta för vektorsökning.",
    },
    "memo-token-limit": {
      title: "Memotokengräns",
      description:
        "Maximalt antal tokens för generering av juridiska anteckningar.",
    },
    "base-token-limit": {
      title: "Grundtokengräns",
      description: "Maximalt antal tokens för grundinnehåll.",
    },
    "toast-success": "Inställningar uppdaterade framgångsrikt",
    "toast-fail": "Misslyckades att uppdatera inställningarna",
  },
  // =========================
  // BINARY LLM SELECTION
  // =========================
  binary_llm_selection: {
    "secondary-llm-toggle": "Binärt LLM-val",
    "secondary-llm-toggle-description":
      "Aktivera detta för att ge administratörer möjlighet att välja mellan två LLM-modeller i modul för dokumentutkast.",
    "secondary-llm-toggle-status": "Status: ",
    "secondary-llm-user-level": "Sekundärt LLM-användarnivå",
    "secondary-llm-user-level-description":
      "Aktivera detta för att ge ALLA användare möjlighet att välja mellan två LLM-modeller i arbetsytan för dokumentutkast.",
  },
};
