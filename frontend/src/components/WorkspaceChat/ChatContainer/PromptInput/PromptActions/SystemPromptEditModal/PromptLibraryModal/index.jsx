import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";
import showToast from "@/utils/toast";
import UserPromptLibrary from "@/models/userPromptLibrary";
import PromptLibraryList from "./PromptLibraryList";
import PromptLibraryForm from "./PromptLibraryForm";

export default function PromptLibraryModal({
  isOpen,
  onClose,
  currentPrompt = "",
  onApplyPrompt,
}) {
  const { t } = useTranslation();
  const [prompts, setPrompts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState(null);

  // Fetch prompts when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchPrompts();
    }
  }, [isOpen]);

  const fetchPrompts = async () => {
    setIsLoading(true);
    try {
      const response = await UserPromptLibrary.getAll();
      if (response.success) {
        setPrompts(response.prompts || []);
      } else {
        showToast(t("prompt-library.error-loading"), "error");
      }
    } catch (error) {
      console.error("Error fetching prompts:", error);
      showToast(t("prompt-library.error-loading"), "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveCurrentPrompt = () => {
    if (!currentPrompt.trim()) {
      showToast(t("prompt-library.prompt-text-required"), "error");
      return;
    }
    setEditingPrompt(null);
    setShowForm(true);
  };

  const handleCreateNew = () => {
    setEditingPrompt(null);
    setShowForm(true);
  };

  const handleEditPrompt = (prompt) => {
    setEditingPrompt(prompt);
    setShowForm(true);
  };

  const handleFormSubmit = async (formData) => {
    try {
      let response;

      if (editingPrompt) {
        // Update existing prompt
        response = await UserPromptLibrary.update(editingPrompt.id, formData);
        if (response.success) {
          showToast(t("prompt-library.update-success"), "success");
          setPrompts(
            prompts.map((p) =>
              p.id === editingPrompt.id ? response.prompt : p
            )
          );
        } else {
          showToast(
            response.error || t("prompt-library.update-error"),
            "error"
          );
        }
      } else {
        // Create new prompt
        const promptData = {
          ...formData,
          prompt_text: formData.prompt_text || currentPrompt,
        };

        response = await UserPromptLibrary.create(promptData);
        if (response.success) {
          showToast(t("prompt-library.save-success"), "success");
          setPrompts([response.prompt, ...prompts]);
        } else {
          showToast(response.error || t("prompt-library.save-error"), "error");
        }
      }

      if (response.success) {
        setShowForm(false);
        setEditingPrompt(null);
      }
    } catch (error) {
      console.error("Error saving prompt:", error);
      showToast(
        editingPrompt
          ? t("prompt-library.update-error")
          : t("prompt-library.save-error"),
        "error"
      );
    }
  };

  const handleDeletePrompt = async (promptId) => {
    try {
      const response = await UserPromptLibrary.delete(promptId);
      if (response.success) {
        showToast(t("prompt-library.delete-success"), "success");
        setPrompts(prompts.filter((p) => p.id !== promptId));
      } else {
        showToast(response.error || t("prompt-library.delete-error"), "error");
      }
    } catch (error) {
      console.error("Error deleting prompt:", error);
      showToast(t("prompt-library.delete-error"), "error");
    }
  };

  const handleApplyPrompt = (prompt) => {
    if (onApplyPrompt) {
      onApplyPrompt(prompt.prompt_text);
      showToast(t("prompt-library.apply-success"), "success");
      onClose();
    }
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingPrompt(null);
  };

  const handleClose = () => {
    setShowForm(false);
    setEditingPrompt(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <Modal
      title={t("prompt-library.modal-title")}
      isOpen={isOpen}
      onClose={handleClose}
      className="w-[800px] max-w-[90vw]"
    >
      <div className="max-h-[70vh] overflow-y-auto px-4">
        {/* Modal description */}
        <p className="text-sm text-muted-foreground mb-4">
          {t("prompt-library.modal-description")}
        </p>

        {showForm ? (
          <PromptLibraryForm
            prompt={editingPrompt}
            currentPrompt={currentPrompt}
            onSubmit={handleFormSubmit}
            onCancel={handleFormCancel}
          />
        ) : (
          <div className="space-y-4">
            {/* Action buttons */}
            <div className="flex gap-2 pb-4 border-b">
              <Button
                type="button"
                onClick={handleSaveCurrentPrompt}
                disabled={!currentPrompt.trim()}
                size="sm"
              >
                {t("prompt-library.save-current-button")}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={handleCreateNew}
                size="sm"
              >
                {t("prompt-library.create-new-button")}
              </Button>
            </div>

            {/* Prompt list */}
            <PromptLibraryList
              prompts={prompts}
              isLoading={isLoading}
              onEdit={handleEditPrompt}
              onDelete={handleDeletePrompt}
              onApply={handleApplyPrompt}
            />

            {/* Close button */}
            <div className="flex justify-end pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                size="sm"
              >
                {t("prompt-library.close-button")}
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}
