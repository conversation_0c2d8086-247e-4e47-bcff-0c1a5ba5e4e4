---
description: Mandatory documentation updates for commits
globs:
alwaysApply: true
---
# Documentation Update Requirements

## Mandatory Documentation Updates

Every commit must include appropriate documentation updates based on the changes made. This is a mandatory requirement to ensure that our documentation stays up-to-date with the codebase.

## Documentation Locations

Depending on the nature of your changes, you must update one or more of the following:

1. **Frontend Documentation** (`frontend/docs/`)
   - Update when making changes to frontend architecture, components, or workflows
   - Create new documentation files for significant new features
   - Update existing documentation to reflect changes in behavior or API

2. **Server Documentation** (`server/docs/`)
   - Update when making changes to server architecture, endpoints, or data models
   - Create new documentation files for significant new features
   - Update existing documentation to reflect changes in behavior or API

3. **Collector Documentation** (`collector/docs/`)
   - Update when making changes to collector architecture, components, or workflows
   - Create new documentation files for significant new features
   - Update existing documentation to reflect changes in file processing, link processing, or extensions

4. **Cursor Project Rules** (`.cursor/rules/`)
   - Update when making changes that affect development guidelines or patterns
   - Create new rule files for significant new features or patterns
   - Update existing rules to reflect changes in best practices
   - **Important**: Ensure all Cursor rule files use the `.mdc` extension. Edits must target files with this extension (e.g., `some-rule.mdc`).

## Documentation Update Checklist

Before submitting a commit, ensure you have:

1. **Identified Documentation Impact**
   - Determine which documentation areas are affected by your changes
   - Consider both direct impacts (changing documented features) and indirect impacts (changing behavior that might affect other features)

2. **Updated Relevant Documentation**
   - Make necessary changes to existing documentation
   - Create new documentation if needed
   - Ensure documentation accurately reflects the current state of the code

3. **Updated Project Rules**
   - Update relevant .cursor rule files
   - Add new rules if introducing significant new patterns or guidelines
   - Update README.md in .cursor/rules if adding new rule files

4. **Included Documentation Changes in Commit**
   - Include all documentation changes in the same commit as code changes
   - If documentation changes are extensive, consider a separate follow-up commit specifically for documentation

## Documentation Standards

When updating documentation:

1. **Maintain Consistent Style**
   - Follow existing documentation format and style
   - Use markdown formatting consistently
   - Include code examples where appropriate

2. **Be Comprehensive**
   - Cover all significant aspects of the feature or change
   - Include both "how to use" and "how it works" information
   - Document edge cases and limitations

3. **Keep It Current**
   - Remove outdated information
   - Update examples to reflect current API and behavior
   - Ensure code snippets are correct and functional

## Commit Message Guidelines

When including documentation updates in your commit:

1. **Mention Documentation Updates**
   - Include "Updated docs" or similar in your commit message
   - Specify which documentation areas were updated

2. **Example Commit Messages**
   - "Implemented feature X and updated frontend/docs"
   - "Fixed bug in API endpoint and updated server/docs"
   - "Added new file converter and updated collector/docs"
   - "Refactored component Y, updated frontend/docs and .cursor rules"

## Enforcement

This rule is mandatory for all commits. Pull requests that include code changes without corresponding documentation updates will be returned for revision.

Remember: Documentation is not an afterthought—it's an integral part of the development process. Keeping documentation current is everyone's responsibility.
