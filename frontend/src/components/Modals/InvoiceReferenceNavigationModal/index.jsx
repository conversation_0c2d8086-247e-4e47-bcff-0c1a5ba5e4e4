import React from "react";
import { useTranslation } from "react-i18next";
import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";
import { useRexorActiveReference } from "@/stores/rexorStore";

export default function InvoiceReferenceNavigationModal({
  isOpen,
  onClose,
  onClearAndContinue,
  onKeepAndContinue,
  destinationType = "location",
}) {
  const { t } = useTranslation();
  const activeReference = useRexorActiveReference();

  if (!isOpen || !activeReference) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t("invoice-reference-navigation.title")}
      canClose={true}
      footer={
        <>
          <Button
            onClick={onKeepAndContinue}
            variant="outline"
            aria-label="keep-reference-and-continue"
          >
            {t("invoice-reference-navigation.keep-and-continue")}
          </Button>
          <Button
            onClick={onClearAndContinue}
            variant="default"
            aria-label="clear-reference-and-continue"
          >
            {t("invoice-reference-navigation.clear-and-continue")}
          </Button>
        </>
      }
    >
      <div className="space-y-4">
        <p className="text-foreground">
          {t("invoice-reference-navigation.message", {
            reference: activeReference,
            destinationType: t(
              `invoice-reference-navigation.destination-types.${destinationType}`
            ),
          })}
        </p>

        <div className="bg-elevated p-3 rounded-md border">
          <p className="text-sm text-foreground font-medium">
            {t("invoice-reference-navigation.current-reference")}
          </p>
          <p className="text-sm text-foreground mt-1 font-mono">
            {activeReference}
          </p>
        </div>

        <p className="text-sm text-foreground opacity-75">
          {t("invoice-reference-navigation.explanation")}
        </p>
      </div>
    </Modal>
  );
}
