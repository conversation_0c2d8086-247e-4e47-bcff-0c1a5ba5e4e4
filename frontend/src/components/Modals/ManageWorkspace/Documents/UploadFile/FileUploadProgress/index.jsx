import React, { useState, useEffect, memo, useCallback } from "react";
import { CheckCircle, XCircle } from "@phosphor-icons/react";
import Workspace from "../../../../../../models/workspace";
import { humanFileSize, milliToHms } from "../../../../../../utils/numbers";
import PreLoader from "../../../../../Preloader";
import { useTranslation } from "react-i18next";
import MarqueeText from "@/components/ui/MarqueeText";

function FileUploadProgressComponent({
  slug,
  uuid,
  file,
  setFiles,
  rejected = false,
  reason = null,
  onUploadSuccess,
  onUploadError,
  setLoading,
  setLoadingMessage,
  setIsUpload,
  workspace,
  isModalUpload = false,
  fetchTokenStats,
  onFileChange,
}) {
  const [timerMs, setTimerMs] = useState(10);
  const [status, setStatus] = useState("pending");
  const [error, setError] = useState("");
  const [isFadingOut, setIsFadingOut] = useState(false);
  const { t } = useTranslation();

  const fadeOut = (cb) => {
    setIsFadingOut(true);
    cb?.();
  };

  const updateTokenCounters = useCallback(async () => {
    try {
      if (fetchTokenStats) {
        const data = await fetchTokenStats();
        // Also call onFileChange if available to update parent components
        if (onFileChange && data) {
          onFileChange(data);
        }
      }
    } catch (error) {
      console.error("Error updating token counters:", error);
    }
  }, [fetchTokenStats, onFileChange]);

  const beginFadeOut = useCallback(() => {
    setIsFadingOut(false);

    // Handle both function and array styles for setFiles
    if (typeof setFiles === "function") {
      // If it's the new function style from parent
      setFiles(uuid);
    } else {
      // If it's the old array filter style
      setFiles((prev) => {
        return prev.filter((item) => item.uid !== uuid);
      });
    }

    // Update token stats after file is removed
    updateTokenCounters();
  }, [setIsFadingOut, setFiles, uuid, updateTokenCounters]);

  useEffect(() => {
    let timer = null;
    let fadeOutTimer = null;

    async function uploadFile() {
      if (file.uploading) return null;
      file.uploading = true;

      // Only check token limits for chat drag-and-drop uploads in legal Q&A workspaces
      if (!isModalUpload && workspace?.type === "legal-qa") {
        try {
          const tokenStats = await Workspace.getTokenCount(workspace.slug);
          if (tokenStats) {
            const { tokenCount, promptLimit } = tokenStats;
            // Estimate tokens for the new file (rough estimate based on file size)
            const estimatedTokens = Math.ceil(file.size / 4); // Rough estimate: 1 token ≈ 4 bytes

            if (tokenCount + estimatedTokens > promptLimit) {
              setStatus("failed");
              const errorMessage = t("show-toast.token-limit-exceeded", {
                available: (promptLimit - tokenCount).toLocaleString(),
                required: estimatedTokens.toLocaleString(),
              });
              setError(errorMessage);
              onUploadError({ error: errorMessage, originalname: file.name });
              return;
            }
          }
        } catch (err) {
          console.error("Error checking token count:", err);
        }
      }

      setLoading(true);
      setLoadingMessage(
        t("modale.document.uploading-file", "Uploading file...")
      );
      const start = Date.now();
      const formData = new FormData();
      formData.append("file", file, file.name);
      timer = setInterval(() => {
        setTimerMs(Date.now() - start);
      }, 100);

      try {
        const { response, data } = await Workspace.uploadFile(slug, formData);
        if (!response.ok) {
          setStatus("failed");
          onUploadError(data);
          setError(data.error);
        } else {
          setStatus("complete");
          onUploadSuccess(data);
          setIsUpload(true);

          // Update token stats after successful upload
          updateTokenCounters();
        }

        clearInterval(timer);
        setLoading(false);
        setLoadingMessage("");

        // Begin fadeout timer to clear uploader queue.
        fadeOut(() => {
          fadeOutTimer = setTimeout(() => beginFadeOut(), 300);
        });
      } catch (err) {
        setStatus("failed");
        setError(err.message);
        onUploadError({ error: err.message });
        clearInterval(timer);
        setLoading(false);
        setLoadingMessage("");

        // Update token stats even if upload fails
        updateTokenCounters();
      }
    }

    if (file && !rejected) {
      uploadFile();
    }

    // Cleanup function to clear timers
    return () => {
      if (timer) clearInterval(timer);
      if (fadeOutTimer) clearTimeout(fadeOutTimer);
    };
  }, [
    beginFadeOut,
    file,
    isModalUpload,
    onUploadError,
    onUploadSuccess,
    rejected,
    setIsUpload,
    setLoading,
    setLoadingMessage,
    slug,
    t,
    updateTokenCounters,
    workspace.slug,
    workspace?.type,
  ]);

  if (rejected) {
    return (
      <div
        className={`${
          isFadingOut ? "file-upload-fadeout" : "file-upload"
        } flex items-center gap-3 p-3 rounded-md border border-destructive/20 bg-destructive/10 transition-all duration-200`}
      >
        <div className="w-6 h-6 flex-shrink-0">
          <XCircle className="w-6 h-6 text-red-500" />
        </div>
        <div className="flex flex-col flex-grow overflow-hidden">
          <div className="overflow-hidden">
            <MarqueeText
              text={file.name}
              className="text-foreground text-xs font-medium"
              variant="upload"
            />
          </div>
          <p className="text-destructive text-xs font-medium">{reason}</p>
        </div>
      </div>
    );
  }

  if (status === "failed") {
    return (
      <div
        className={`${
          isFadingOut ? "file-upload-fadeout" : "file-upload"
        } flex items-center gap-3 p-3 rounded-md border border-destructive/20 bg-destructive/10 transition-all duration-200`}
      >
        <div className="w-6 h-6 flex-shrink-0">
          <XCircle className="w-6 h-6 text-red-500" />
        </div>
        <div className="flex flex-col flex-grow overflow-hidden">
          <div className="overflow-hidden">
            <MarqueeText
              text={file.name}
              className="text-foreground text-xs font-medium"
              variant="upload"
            />
          </div>
          <p className="text-destructive text-xs font-medium">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`${
        isFadingOut ? "file-upload-fadeout" : "file-upload"
      } flex items-center gap-3 p-3 rounded-md border border-border bg-elevated hover:bg-secondary/50 transition-all duration-200`}
    >
      <div className="w-6 h-6 flex-shrink-0">
        {status !== "complete" ? (
          <div className="flex items-center justify-center">
            <PreLoader size="1" />
          </div>
        ) : (
          <CheckCircle className="w-6 h-6 text-green-500" />
        )}
      </div>
      <div className="flex flex-col flex-grow overflow-hidden">
        <div className="overflow-hidden">
          <MarqueeText
            text={file.name}
            className="text-foreground text-xs font-medium"
            variant="upload"
          />
        </div>
        <p className="text-muted text-xs font-medium">
          {humanFileSize(file.size)} | {milliToHms(timerMs)}
        </p>
      </div>
    </div>
  );
}

export default memo(FileUploadProgressComponent);
