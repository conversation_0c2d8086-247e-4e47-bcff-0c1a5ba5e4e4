/**
 * File Attachment for automatic upload on the chat container page.
 * @typedef Attachment
 * @property {string} name - the given file name
 * @property {string} mime - the given file mime
 * @property {string} contentString - full base64 encoded string of file
 */

/**
 * @typedef {Object} ResponseMetrics
 * @property {number} prompt_tokens - The number of prompt tokens used
 * @property {number} completion_tokens - The number of completion tokens used
 * @property {number} total_tokens - The total number of tokens used
 * @property {number} outputTps - The output tokens per second
 * @property {number} duration - The duration of the request in seconds
 *
 * @typedef {Object} ChatMessage
 * @property {string} role - The role of the message sender (e.g. 'user', 'assistant', 'system')
 * @property {string} content - The content of the message
 *
 * @typedef {Object} ChatCompletionResponse
 * @property {string} textResponse - The text response from the LLM
 * @property {ResponseMetrics} metrics - The response metrics
 *
 * @typedef {Object} ChatCompletionOptions
 * @property {number} temperature - The sampling temperature for the LLM response
 *
 * @typedef {function(Array<ChatMessage>, ChatCompletionOptions): Promise<ChatCompletionResponse>} getChatCompletionFunction
 *
 * @typedef {function(Array<ChatMessage>, ChatCompletionOptions): Promise<import("./chat/LLMPerformanceMonitor").MonitoredStream>} streamGetChatCompletionFunction
 */

/**
 * @typedef {Object} BaseLLMProvider - A basic llm provider object
 * @property {Function} streamingEnabled - Checks if streaming is enabled for chat completions.
 * @property {Function} promptWindowLimit - Returns the token limit for the current model.
 * @property {Function} isValidChatCompletionModel - Validates if the provided model is suitable for chat completion.
 * @property {Function} constructPrompt - Constructs a formatted prompt for the chat completion request.
 * @property {getChatCompletionFunction} getChatCompletion - Gets a chat completion response from OpenAI.
 * @property {streamGetChatCompletionFunction} streamGetChatCompletion - Streams a chat completion response from OpenAI.
 * @property {Function} handleStream - Handles the streaming response.
 * @property {Function} embedTextInput - Embeds the provided text input using the specified embedder.
 * @property {Function} embedChunks - Embeds multiple chunks of text using the specified embedder.
 * @property {Function} compressMessages - Compresses chat messages to fit within the token limit.
 */

/**
 * @typedef {Object} BaseLLMProviderClass - Class method of provider - not instantiated
 * @property {function(string): number} promptWindowLimit - Returns the token limit for the provided model.
 */

/**
 * @typedef {Object} BaseVectorDatabaseProvider
 * @property {string} name - The name of the Vector Database instance.
 * @property {Function} connect - Connects to the Vector Database client.
 * @property {Function} totalVectors - Returns the total number of vectors in the database.
 * @property {Function} namespaceCount - Returns the count of vectors in a given namespace.
 * @property {Function} similarityResponse - Performs a similarity search on a given namespace.
 * @property {Function} rerankedSimilarityResponse - Performs a similarity search on a given namespace with reranking (if supported by provider).
 * @property {Function} namespace - Retrieves the specified namespace collection.
 * @property {Function} hasNamespace - Checks if a namespace exists.
 * @property {Function} namespaceExists - Verifies if a namespace exists in the client.
 * @property {Function} deleteVectorsInNamespace - Deletes all vectors in a specified namespace.
 * @property {Function} deleteDocumentFromNamespace - Deletes a document from a specified namespace.
 * @property {Function} addDocumentToNamespace - Adds a document to a specified namespace.
 * @property {Function} performSimilaritySearch - Performs a similarity search in the namespace.
 */

/**
 * @typedef {Object} BaseEmbedderProvider
 * @property {string} model - The model used for embedding.
 * @property {number} maxConcurrentChunks - The maximum number of chunks processed concurrently.
 * @property {number} embeddingMaxChunkLength - The maximum length of each chunk for embedding.
 * @property {Function} embedTextInput - Embeds a single text input.
 * @property {Function} embedChunks - Embeds multiple chunks of text.
 */

/**
 * Gets the systems current vector database provider.
 * @returns { BaseVectorDatabaseProvider}
 */
function getVectorDbClass() {
  const vectorSelection = process.env.VECTOR_DB || "lancedb";
  switch (vectorSelection) {
    case "pinecone":
      const { Pinecone } = require("../vectorDbProviders/pinecone");
      return Pinecone;
    case "chroma":
      const { Chroma } = require("../vectorDbProviders/chroma");
      return Chroma;
    case "lancedb":
      const { LanceDb } = require("../vectorDbProviders/lance");
      return LanceDb;
    case "weaviate":
      const { Weaviate } = require("../vectorDbProviders/weaviate");
      return Weaviate;
    case "qdrant":
      const { QDrant } = require("../vectorDbProviders/qdrant");
      return QDrant;
    case "milvus":
      const { Milvus } = require("../vectorDbProviders/milvus");
      return Milvus;
    case "zilliz":
      const { Zilliz } = require("../vectorDbProviders/zilliz");
      return Zilliz;
    case "astra":
      const { AstraDB } = require("../vectorDbProviders/astra");
      return AstraDB;
    default:
      throw new Error("ENV: No VECTOR_DB value found in environment!");
  }
}

/**
 * Returns the LLMProvider with its embedder attached via system or via defined provider.
 * @param {{provider: string | null, model: string | null, workspace: object | null} | null} params - Initialize params for LLMs provider
 * @returns {BaseLLMProvider}
 */
function getLLMProvider({
  provider = null,
  model = null,
  workspace = null,
  settings_suffix = "",
  useDeepSearch = false,
} = {}) {
  const embedder = getEmbeddingEngineSelection({ workspace });
  const LLMSelection =
    provider || workspace?.chatProvider || process.env.LLM_PROVIDER || "openai";
  let LLMInstance;

  switch (LLMSelection) {
    case "openai": {
      const { OpenAiLLM } = require("../AiProviders/openAi");
      LLMInstance = new OpenAiLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "azure": {
      const { AzureOpenAiLLM } = require("../AiProviders/azureOpenAi");
      LLMInstance = new AzureOpenAiLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "anthropic": {
      const { AnthropicLLM } = require("../AiProviders/anthropic");
      LLMInstance = new AnthropicLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "gemini": {
      const { GeminiLLM } = require("../AiProviders/gemini");
      LLMInstance = new GeminiLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "lmstudio": {
      const { LMStudioLLM } = require("../AiProviders/lmStudio");
      LLMInstance = new LMStudioLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "localai": {
      const { LocalAiLLM } = require("../AiProviders/localAi");
      LLMInstance = new LocalAiLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "ollama": {
      const { OllamaAILLM } = require("../AiProviders/ollama");
      LLMInstance = new OllamaAILLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "togetherai": {
      const { TogetherAiLLM } = require("../AiProviders/togetherAi");
      LLMInstance = new TogetherAiLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "fireworksai": {
      const { FireworksAiLLM } = require("../AiProviders/fireworksAi");
      LLMInstance = new FireworksAiLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "perplexity": {
      const { PerplexityLLM } = require("../AiProviders/perplexity");
      LLMInstance = new PerplexityLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "openrouter": {
      const { OpenRouterLLM } = require("../AiProviders/openRouter");
      LLMInstance = new OpenRouterLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "mistral": {
      const { MistralLLM } = require("../AiProviders/mistral");
      LLMInstance = new MistralLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "native": {
      const { NativeLLM } = require("../AiProviders/native");
      LLMInstance = new NativeLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "huggingface": {
      const { HuggingFaceLLM } = require("../AiProviders/huggingface");
      LLMInstance = new HuggingFaceLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "groq": {
      const { GroqLLM } = require("../AiProviders/groq");
      LLMInstance = new GroqLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "koboldcpp": {
      const { KoboldCPPLLM } = require("../AiProviders/koboldCPP");
      LLMInstance = new KoboldCPPLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "textgenwebui": {
      const { TextGenWebUILLM } = require("../AiProviders/textGenWebUI");
      LLMInstance = new TextGenWebUILLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "cohere": {
      const { CohereLLM } = require("../AiProviders/cohere");
      LLMInstance = new CohereLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "litellm": {
      const { LiteLLM } = require("../AiProviders/liteLLM");
      LLMInstance = new LiteLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "generic-openai": {
      const { GenericOpenAiLLM } = require("../AiProviders/genericOpenAi");
      LLMInstance = new GenericOpenAiLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "bedrock": {
      const { AWSBedrockLLM } = require("../AiProviders/bedrock");
      LLMInstance = new AWSBedrockLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "deepseek": {
      const { DeepSeekLLM } = require("../AiProviders/deepseek");
      LLMInstance = new DeepSeekLLM(
        embedder,
        model,
        settings_suffix,
        useDeepSearch
      );
      break;
    }
    case "xai": {
      const { XAiLLM } = require("../AiProviders/xai");
      LLMInstance = new XAiLLM(embedder, model, null, useDeepSearch);
      break;
    }
    default:
      throw new Error(
        `ENV: No valid LLM_PROVIDER value found in environment! Using`
      );
  }

  if (process.env.NODE_ENV === "development") {
    const stackTrace = new Error().stack;
    const callerLine = stackTrace.split("\n")[2] || "Unknown caller";
    const callerFunction =
      callerLine.match(/at (.+?) \(/) || callerLine.match(/at (.+)/);
    const caller = callerFunction ? callerFunction[1] : "Unknown";

    console.log(
      `\x1b[36m[LLM PROVIDER]\x1b[0m =========================================`
    );
    console.log(`\x1b[36m[LLM PROVIDER]\x1b[0m Called by: ${caller}`);
    console.log(
      `\x1b[36m[LLM PROVIDER]\x1b[0m Provider param: "${provider || "null"}"`
    );
    console.log(
      `\x1b[36m[LLM PROVIDER]\x1b[0m Model param: "${model || "null"}"`
    );
    console.log(
      `\x1b[36m[LLM PROVIDER]\x1b[0m Settings suffix: "${settings_suffix || "none"}"`
    );
    console.log(
      `\x1b[36m[LLM PROVIDER]\x1b[0m Use deep search: ${useDeepSearch}`
    );
    console.log(
      `\x1b[36m[LLM PROVIDER]\x1b[0m Workspace embedder: ${workspace?.embeddingProvider || "none"}`
    );
    console.log(
      `\x1b[36m[LLM PROVIDER]\x1b[0m Final LLM selection: "${LLMSelection}"`
    );
    console.log(
      `\x1b[36m[LLM PROVIDER]\x1b[0m Model used: "${LLMInstance.model}"`
    );
    console.log(
      `\x1b[36m[LLM PROVIDER]\x1b[0m Embedder type: ${embedder.constructor.name}`
    );
    console.log(
      `\x1b[36m[LLM PROVIDER]\x1b[0m =========================================`
    );
  }
  return LLMInstance;
}

/**
 * Returns the EmbedderProvider by itself to whatever is currently in the system settings.
 * @param {{workspace: object | null}} params - Workspace object containing embedding settings
 * @returns {BaseEmbedderProvider}
 */
function getEmbeddingEngineSelection({ workspace = null } = {}) {
  const { NativeEmbedder } = require("../EmbeddingEngines/native");
  const engineSelection =
    workspace?.embeddingProvider || process.env.EMBEDDING_ENGINE;
  switch (engineSelection) {
    case "openai":
      const { OpenAiEmbedder } = require("../EmbeddingEngines/openAi");
      return new OpenAiEmbedder();
    case "azure":
      const {
        AzureOpenAiEmbedder,
      } = require("../EmbeddingEngines/azureOpenAi");
      return new AzureOpenAiEmbedder();
    case "localai":
      const { LocalAiEmbedder } = require("../EmbeddingEngines/localAi");
      return new LocalAiEmbedder();
    case "ollama":
      const { OllamaEmbedder } = require("../EmbeddingEngines/ollama");
      return new OllamaEmbedder();
    case "native":
      return new NativeEmbedder();
    case "lmstudio":
      const { LMStudioEmbedder } = require("../EmbeddingEngines/lmstudio");
      return new LMStudioEmbedder();
    case "cohere":
      const { CohereEmbedder } = require("../EmbeddingEngines/cohere");
      return new CohereEmbedder();
    case "voyageai":
      const { VoyageAiEmbedder } = require("../EmbeddingEngines/voyageAi");
      return new VoyageAiEmbedder();
    case "litellm":
      const { LiteLLMEmbedder } = require("../EmbeddingEngines/liteLLM");
      return new LiteLLMEmbedder();
    case "generic-openai":
      const {
        GenericOpenAiEmbedder,
      } = require("../EmbeddingEngines/genericOpenAi");
      return new GenericOpenAiEmbedder();
    case "gemini":
      const { GeminiEmbedder } = require("../EmbeddingEngines/gemini");
      return new GeminiEmbedder();
    case "jina":
      const { JinaEmbedder } = require("../EmbeddingEngines/jina");
      return new JinaEmbedder();
    default:
      return new NativeEmbedder();
  }
}

/**
 * Returns the LLMProviderClass - this is a helper method to access static methods on a class
 * @param {{provider: string | null} | null} params - Initialize params for LLMs provider
 * @returns {BaseLLMProviderClass}
 */
function getLLMProviderClass({ provider = null } = {}) {
  switch (provider) {
    case "openai":
      const { OpenAiLLM } = require("../AiProviders/openAi");
      return OpenAiLLM;
    case "azure":
      const { AzureOpenAiLLM } = require("../AiProviders/azureOpenAi");
      return AzureOpenAiLLM;
    case "anthropic":
      const { AnthropicLLM } = require("../AiProviders/anthropic");
      return AnthropicLLM;
    case "gemini":
      const { GeminiLLM } = require("../AiProviders/gemini");
      return GeminiLLM;
    case "lmstudio":
      const { LMStudioLLM } = require("../AiProviders/lmStudio");
      return LMStudioLLM;
    case "localai":
      const { LocalAiLLM } = require("../AiProviders/localAi");
      return LocalAiLLM;
    case "ollama":
      const { OllamaAILLM } = require("../AiProviders/ollama");
      return OllamaAILLM;
    case "togetherai":
      const { TogetherAiLLM } = require("../AiProviders/togetherAi");
      return TogetherAiLLM;
    case "fireworksai":
      const { FireworksAiLLM } = require("../AiProviders/fireworksAi");
      return FireworksAiLLM;
    case "perplexity":
      const { PerplexityLLM } = require("../AiProviders/perplexity");
      return PerplexityLLM;
    case "openrouter":
      const { OpenRouterLLM } = require("../AiProviders/openRouter");
      return OpenRouterLLM;
    case "mistral":
      const { MistralLLM } = require("../AiProviders/mistral");
      return MistralLLM;
    case "native":
      const { NativeLLM } = require("../AiProviders/native");
      return NativeLLM;
    case "huggingface":
      const { HuggingFaceLLM } = require("../AiProviders/huggingface");
      return HuggingFaceLLM;
    case "groq":
      const { GroqLLM } = require("../AiProviders/groq");
      return GroqLLM;
    case "koboldcpp":
      const { KoboldCPPLLM } = require("../AiProviders/koboldCPP");
      return KoboldCPPLLM;
    case "textgenwebui":
      const { TextGenWebUILLM } = require("../AiProviders/textGenWebUI");
      return TextGenWebUILLM;
    case "cohere":
      const { CohereLLM } = require("../AiProviders/cohere");
      return CohereLLM;
    case "litellm":
      const { LiteLLM } = require("../AiProviders/liteLLM");
      return LiteLLM;
    case "generic-openai":
      const { GenericOpenAiLLM } = require("../AiProviders/genericOpenAi");
      return GenericOpenAiLLM;
    case "bedrock":
      const { AWSBedrockLLM } = require("../AiProviders/bedrock");
      return AWSBedrockLLM;
    case "xai":
      const { XAiLLM } = require("../AiProviders/xai");
      return XAiLLM;
    default:
      return null;
  }
}

// Some models have lower restrictions on chars that can be encoded in a single pass
// and by default we assume it can handle 1,000 chars, but some models use work with smaller
// chars so here we can override that value when embedding information.
function maximumChunkLength() {
  if (
    !!process.env.EMBEDDING_MODEL_MAX_CHUNK_LENGTH &&
    !isNaN(process.env.EMBEDDING_MODEL_MAX_CHUNK_LENGTH) &&
    Number(process.env.EMBEDDING_MODEL_MAX_CHUNK_LENGTH) > 1
  )
    return Number(process.env.EMBEDDING_MODEL_MAX_CHUNK_LENGTH);

  return 1_000;
}

function toChunks(arr, size) {
  return Array.from({ length: Math.ceil(arr.length / size) }, (_v, i) =>
    arr.slice(i * size, i * size + size)
  );
}

/**
 * Strips UUID patterns from filenames to create cleaner metadata references
 * @param {string} text - The text that may contain UUID patterns
 * @returns {string} - Text with UUID patterns removed
 */
function stripUuidFromText(text) {
  if (!text || typeof text !== "string") return text;

  // Remove UUID patterns (8-4-4-4-12 format) and surrounding hyphens
  // This handles cases like "filename-abc123de-f456-7890-abcd-ef1234567890.pdf"
  return text
    .replace(
      /-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi,
      ""
    )
    .replace(
      /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}-?/gi,
      ""
    )
    .replace(/--+/g, "-") // Replace multiple consecutive hyphens with single hyphen
    .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
}

/**
 * Formats an array of context texts with consistent context markers.
 * This centralizes the format used across all AI providers.
 *
 * @param {string[]} contextTexts - Array of context texts to format
 * @param {boolean} withHeader - Whether to include a header. Default: true
 * @returns {Promise<string>} - The formatted context text
 */
async function formatContextTexts(contextTexts, withHeader = true) {
  // For null or empty arrays, return an empty string
  if (!contextTexts || contextTexts.length === 0) {
    return "";
  }

  // Import t function for translations
  const { tSync } = require("../i18n");

  const formattedTexts = contextTexts.map((ctx, i) => {
    if (ctx && typeof ctx === "object" && Object.hasOwn(ctx, "text")) {
      const rawMeta =
        ctx.meta ||
        ctx.title ||
        ctx.name ||
        ctx.url ||
        ctx.docSource ||
        ctx.docAuthor ||
        ctx.id ||
        `Document ${i + 1}`;

      // Strip UUIDs from the metadata to create cleaner references
      const meta = stripUuidFromText(rawMeta);

      return `[${meta}]:\n${ctx.text}\n[END ${meta}]`;
    }

    // For non-object contexts (plain strings), try to extract meaningful info
    // or fall back to a descriptive label instead of numbered sources
    const contextLabel = `Document ${i + 1}`;
    return `[${contextLabel}]:\n${ctx}\n[END ${contextLabel}]`;
  });

  if (withHeader) {
    const header = tSync("errors.ai.context.header");
    return `${header}:\n${formattedTexts.join("\n\n")}\n\n`;
  }

  return `${formattedTexts.join("\n\n")}\n\n`;
}

module.exports = {
  getEmbeddingEngineSelection,
  maximumChunkLength,
  getVectorDbClass,
  getLLMProviderClass,
  getLLMProvider,
  toChunks,
  formatContextTexts,
  stripUuidFromText,
};
