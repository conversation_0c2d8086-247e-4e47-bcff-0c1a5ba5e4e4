const TRANSLATIONS = {
  "news-system": {
    title: "Amakuru n'Amatangazo",
    previous: "Ibanjirije",
    next: "<PERSON><PERSON><PERSON><PERSON>",
    of: "kuri",
    close: "<PERSON><PERSON>",
    dismissThis: "<PERSON><PERSON><PERSON><PERSON><PERSON> werekana",
    dismissAll: "<PERSON><PERSON> byose",
    management: {
      title: "Gucunga Amakuru",
      description:
        "<PERSON>unga amatangazo ya sisitemu n'amakuru. Amakuru ya sisitemu yoherezwa hamwe na porogaramu kandi ntashobora guhindurwa hano.",
      create: "Kurema Amakuru",
      edit: "Guhindura Amakuru",
      loading: "Gupakurura...",
      table: {
        title: "Umut<PERSON>",
        priority: "Icyamber<PERSON>",
        targetRoles: "Inshingano Zigamijwe",
        created: "Byaremwe",
        expires: "Birangira",
        status: "Uko bimeze",
        source: "Inkomoko",
        actions: "Ibikorwa",
        allUsers: "<PERSON><PERSON><PERSON><PERSON><PERSON> bose",
        never: "<PERSON>ta na rimwe",
        active: "<PERSON><PERSON><PERSON>",
        inactive: "<PERSON><PERSON><PERSON>kor<PERSON>",
        systemNews: "<PERSON><PERSON><PERSON>",
        localNews: "Byaho",
      },
      source: {
        system: "<PERSON>ak<PERSON> ya Sisitemu",
        local: "Amakuru y'Aho",
        systemTooltip:
          "Aya makuru yoherezwa hamwe na porogaramu kandi ntashobora guhindurwa",
        localTooltip: "Aya makuru yaremwe aho kandi ashobora guhindurwa",
      },
      form: {
        title: "Umutwe",
        titlePlaceholder: "Injiza umutwe w'amakuru",
        content: "Ibiri mo",
        contentPlaceholder: "Injiza ibiri mu makuru",
        priority: "Icyambere",
        targetRoles:
          "Inshingano Zigamijwe (siga ubusa kugirango byereke abakoresha bose)",
        expiresAt: "Birangira (bitegetswe)",
        priorities: {
          low: "Bito",
          medium: "Hagati",
          high: "Byinshi",
          urgent: "Byihutirwa",
        },
        roles: {
          admin: "Umuyobozi",
          manager: "Umucunga",
          default: "Bisanzwe",
        },
      },
      actions: {
        create: "Kurema",
        update: "Kuvugurura",
        cancel: "Kureka",
        delete: "Gusiba",
        edit: "Guhindura",
        view: "Kureba",
        cannotEdit: "Ntushobora guhindura amakuru ya sisitemu",
        cannotDelete: "Ntushobora gusiba amakuru ya sisitemu",
      },
      confirmations: {
        delete: "Uzi neza ko ushaka gusiba aya makuru?",
      },
      messages: {
        createSuccess: "Amakuru yaremwe neza",
        updateSuccess: "Amakuru yavuguruwe neza",
        deleteSuccess: "Amakuru yasibwe neza",
        createError: "Byanze kurema amakuru",
        updateError: "Byanze kuvugurura amakuru",
        deleteError: "Byanze gusiba amakuru",
        fetchError: "Byanze gukurura amakuru",
        systemNewsInfo:
          "Amakuru ya sisitemu ntashobora guhindurwa kandi yoherezwa hamwe na porogaramu",
      },
    },
    error: {
      dismiss: "Byanze kureka amakuru",
      dismissAll: "Byanze kureka amakuru yose",
      fetch: "Byanze gukurura amakuru",
    },
  },

  // News header and list functionality (moved from common.js)
  news: {
    header: {
      viewAll: "Reba amakuru yose",
      buttonText: "Amakuru",
    },
    list: {
      title: "Amakuru n'Amatangazo",
      empty: "Nta makuru yo kwerekana",
      active: "Bikora",
      dismissed: "Byahagaritswe",
      dismiss: "Hagarika",
      dismissSuccess: "Amakuru yahagaritswe neza",
      dismissError: "Byanze guhagarika amakuru",
      dismissedAt: "Byahagaritswe ku {{date}}",
      systemNews: "Sisitemu",
      viewFull: "Reba amakuru yuzuye",
      filter: {
        all: "Byose",
        unread: "Bikora",
        read: "Byahagaritswe",
      },
    },
    priority: {
      low: "Bike",
      medium: "Hagati",
      high: "Byinshi",
      urgent: "Byihutirwa",
    },
  },
};

export default TRANSLATIONS;
