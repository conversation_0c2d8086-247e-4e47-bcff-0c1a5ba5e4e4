/**
 * Unit tests for ContextWindowManager
 *
 * This test file demonstrates the functionality of the new iterative
 * context window management system.
 */

const {
  ContextWindowManager,
} = require("../../../utils/chats/helpers/contextWindowManager");
const { TokenManager } = require("../../../utils/helpers/tiktoken");

// Mock LLMConnector for testing
const mockLLMConnector = {
  model: "gpt-3.5-turbo",
  promptWindowLimit: () => 16384,
  compressMessages: async ({ systemPrompt, userPrompt }) => [
    { role: "system", content: systemPrompt },
    { role: "user", content: userPrompt },
  ],
  getChatCompletion: async (messages, options) => ({
    textResponse: "Mock response",
    metrics: { total_tokens: 100, prompt_tokens: 50, completion_tokens: 50 },
  }),
  metrics: {},
};

describe("ContextWindowManager", () => {
  let contextManager;

  beforeEach(() => {
    contextManager = new ContextWindowManager(mockLLMConnector);
  });

  describe("getAvailableContextWindow", () => {
    test("should calculate available context window correctly", () => {
      const available = contextManager.getAvailableContextWindow();
      const expected = Math.floor(16384 * 0.7) - 4000; // 70% minus reserved tokens
      expect(available).toBe(expected);
    });

    test("should respect custom reserved tokens", () => {
      const available = contextManager.getAvailableContextWindow(2000);
      const expected = Math.floor(16384 * 0.7) - 2000;
      expect(available).toBe(expected);
    });
  });

  describe("calculateTokenBudget", () => {
    test("should calculate token budget breakdown correctly", () => {
      const systemPrompt = "You are a helpful assistant.";
      const userPromptTemplate = "Process this: {{content}}";

      const budget = contextManager.calculateTokenBudget({
        systemPrompt,
        userPromptTemplate,
      });

      expect(budget).toHaveProperty("total");
      expect(budget).toHaveProperty("system");
      expect(budget).toHaveProperty("userPromptOverhead");
      expect(budget).toHaveProperty("availableForContent");
      expect(budget).toHaveProperty("reserved");

      expect(budget.system).toBeGreaterThan(0);
      expect(budget.availableForContent).toBeGreaterThan(0);
    });
  });

  describe("chunkContent", () => {
    test("should return single chunk for small content", () => {
      const smallContent = "This is a small piece of content.";
      const chunks = contextManager.chunkContent(smallContent, {
        maxTokensPerChunk: 1000,
      });

      expect(chunks).toHaveLength(1);
      expect(chunks[0].content).toBe(smallContent);
      expect(chunks[0].chunkIndex).toBe(0);
      expect(chunks[0].totalChunks).toBe(1);
    });

    test("should chunk large content appropriately", () => {
      // Create content that will definitely need chunking
      const largeContent = "word ".repeat(2000); // 2000 words
      const chunks = contextManager.chunkContent(largeContent, {
        maxTokensPerChunk: 500,
        overlapTokens: 50,
      });

      expect(chunks.length).toBeGreaterThan(1);

      // Check that chunks have the expected structure
      chunks.forEach((chunk, index) => {
        expect(chunk).toHaveProperty("content");
        expect(chunk).toHaveProperty("tokens");
        expect(chunk).toHaveProperty("chunkIndex");
        expect(chunk).toHaveProperty("totalChunks");
        expect(chunk.chunkIndex).toBe(index);
      });
    });

    test("should handle empty content gracefully", () => {
      const chunks = contextManager.chunkContent("");
      expect(chunks).toHaveLength(0);
    });
  });

  describe("processIteratively", () => {
    test("should process documents iteratively", async () => {
      const mockDocuments = [
        { name: "doc1.pdf", content: "Content 1", tokens: 100 },
        { name: "doc2.pdf", content: "Content 2", tokens: 150 },
        { name: "doc3.pdf", content: "Content 3", tokens: 200 },
      ];

      const mockMemos = [
        { issue: "Issue 1", content: "Memo content 1", tokens: 80 },
        { issue: "Issue 2", content: "Memo content 2", tokens: 120 },
      ];

      const mockBudget = {
        total: 8000,
        system: 50,
        userPromptOverhead: 100,
        availableForContent: 7850,
        reserved: 4000,
      };

      const mockProcessor = jest.fn().mockImplementation(async (params) => ({
        content: `Processed iteration ${params.iteration} with ${params.documents.length} docs and ${params.memos.length} memos`,
        tokensUsed: 200,
      }));

      const result = await contextManager.processIteratively({
        processor: mockProcessor,
        documents: mockDocuments,
        memos: mockMemos,
        budget: mockBudget,
        context: { test: "context" },
      });

      expect(result).toHaveProperty("finalContent");
      expect(result).toHaveProperty("iterations");
      expect(result).toHaveProperty("totalIterations");
      expect(result).toHaveProperty("metrics");

      expect(result.totalIterations).toBeGreaterThan(0);
      expect(result.iterations.length).toBe(result.totalIterations);
      expect(mockProcessor).toHaveBeenCalled();
    });

    test("should handle empty documents and memos", async () => {
      const mockBudget = {
        total: 8000,
        system: 50,
        userPromptOverhead: 100,
        availableForContent: 7850,
        reserved: 4000,
      };

      const mockProcessor = jest.fn().mockImplementation(async (params) => ({
        content: `Empty iteration ${params.iteration}`,
        tokensUsed: 50,
      }));

      const result = await contextManager.processIteratively({
        processor: mockProcessor,
        documents: [],
        memos: [],
        budget: mockBudget,
        context: {},
      });

      expect(result.totalIterations).toBe(1); // Should run at least once
      expect(mockProcessor).toHaveBeenCalledTimes(1);
    });
  });

  describe("getMetrics", () => {
    test("should return metrics object", () => {
      const metrics = contextManager.getMetrics();

      expect(metrics).toHaveProperty("totalTokensProcessed");
      expect(metrics).toHaveProperty("chunksCreated");
      expect(metrics).toHaveProperty("iterationsCompleted");
    });
  });

  describe("resetMetrics", () => {
    test("should reset metrics to initial values", () => {
      // Simulate some processing
      contextManager.metrics.totalTokensProcessed = 1000;
      contextManager.metrics.chunksCreated = 5;
      contextManager.metrics.iterationsCompleted = 3;

      contextManager.resetMetrics();

      const metrics = contextManager.getMetrics();
      expect(metrics.totalTokensProcessed).toBe(0);
      expect(metrics.chunksCreated).toBe(0);
      expect(metrics.iterationsCompleted).toBe(0);
    });
  });
});

// Integration test demonstrating the full flow
describe("ContextWindowManager Integration", () => {
  test("should demonstrate complete iterative processing flow", async () => {
    const contextManager = new ContextWindowManager(mockLLMConnector, {
      maxIterations: 3,
      reservedOutputTokens: 2000,
    });

    // Simulate a realistic scenario
    const documents = [
      {
        name: "contract.pdf",
        content: "Contract content " + "word ".repeat(500),
        tokens: 600,
      },
      {
        name: "memo.docx",
        content: "Legal memo content " + "word ".repeat(300),
        tokens: 400,
      },
      {
        name: "evidence.pdf",
        content: "Evidence content " + "word ".repeat(200),
        tokens: 300,
      },
    ];

    const memos = [
      {
        issue: "Contractual obligations",
        content: "Analysis of obligations",
        tokens: 150,
      },
      {
        issue: "Liability concerns",
        content: "Liability analysis",
        tokens: 200,
      },
    ];

    const budget = contextManager.calculateTokenBudget({
      systemPrompt: "You are a legal document analyzer.",
      userPromptTemplate: "Analyze these documents: {{content}}",
    });

    let iterationCount = 0;
    const processor = async (params) => {
      iterationCount++;

      // Simulate processing that takes different amounts of tokens based on content
      const baseTokens = 100;
      const docTokens = params.documents.reduce(
        (sum, doc) => sum + Math.min(doc.tokens, 50),
        0
      );
      const memoTokens = params.memos.reduce(
        (sum, memo) => sum + Math.min(memo.tokens, 30),
        0
      );

      return {
        content: `Iteration ${params.iteration}: Processed ${params.documents.length} documents and ${params.memos.length} memos.`,
        tokensUsed: baseTokens + docTokens + memoTokens,
      };
    };

    const result = await contextManager.processIteratively({
      processor,
      documents,
      memos,
      budget,
      context: { task: "Legal document analysis" },
    });

    // Verify the results
    expect(result.finalContent).toContain("Iteration");
    expect(result.totalIterations).toBeGreaterThan(0);
    expect(result.totalIterations).toBeLessThanOrEqual(3); // Respects maxIterations
    expect(result.iterations).toHaveLength(result.totalIterations);

    // Check that iterations have expected structure
    result.iterations.forEach((iteration, index) => {
      expect(iteration).toHaveProperty("iteration", index + 1);
      expect(iteration).toHaveProperty("documentsProcessed");
      expect(iteration).toHaveProperty("memosProcessed");
      expect(iteration).toHaveProperty("tokensUsed");
      expect(iteration.tokensUsed).toBeGreaterThan(0);
    });

    console.log("Integration test results:");
    console.log(`- Total iterations: ${result.totalIterations}`);
    console.log(`- Final content length: ${result.finalContent.length}`);
    console.log(
      `- Total documents processed across iterations: ${result.iterations.reduce((sum, iter) => sum + iter.documentsProcessed, 0)}`
    );
    console.log(
      `- Total memos processed across iterations: ${result.iterations.reduce((sum, iter) => sum + iter.memosProcessed, 0)}`
    );
  });
});
