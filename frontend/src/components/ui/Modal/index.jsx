import { createPortal } from "react-dom";
import { useEffect, useRef, useCallback, useState } from "react";
import { X } from "@phosphor-icons/react";
import { Button } from "@/components/Button";
import { cn } from "@/utils/classes";

/**
 * @typedef {Object} ModalProps
 * @property {React.ReactNode} children - The DOM/JSX to render
 * @property {boolean} isOpen - Option that renders the modal
 * @property {boolean} noPortal - (default: false) Used for creating sub-DOM modals that need to be rendered as a child element instead of a modal placed at the root
 **/

export default function Modal({
  children,
  isOpen,
  onClose,
  noPortal = false,
  zIndex = 50,
  initialFocus = null,
  title,
  description,
  footer,
  preventClose = false,
  className,
}) {
  const modalRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);

  const handleKeyDown = useCallback(
    (event) => {
      if (event.key === "Escape" && !preventClose) {
        // Ensure onClose is called properly when pressing Escape
        if (typeof onClose === "function") {
          onClose();
        }
      }
    },
    [onClose, preventClose]
  );

  const handleOutsideClick = useCallback(
    (event) => {
      if (event.target.classList.contains("backdrop") && !preventClose) {
        // Ensure onClose is called properly when clicking outside
        if (typeof onClose === "function") {
          onClose();
        }
      }
    },
    [onClose, preventClose]
  );

  const handleTabKey = useCallback((e) => {
    if (e.key === "Tab") {
      const focusableElements = modalRef.current?.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      if (!focusableElements?.length) return;

      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement.focus();
          e.preventDefault();
        }
      }
    }
  }, []);

  useEffect(() => {
    if (isOpen) {
      requestAnimationFrame(() => {
        setIsVisible(true);
      });

      document.addEventListener("keydown", handleKeyDown);
      document.addEventListener("mousedown", handleOutsideClick);
      document.addEventListener("keydown", handleTabKey);

      // Function to disable validation on all form elements on the page
      const disableFormValidations = () => {
        // Find all form elements on the page
        const allForms = document.querySelectorAll("form");

        // For each form, add noValidate attribute
        allForms.forEach((form) => {
          // Store original state to restore later
          form.dataset.originalNoValidate = form.noValidate;
          form.noValidate = true;
        });

        // Find all input elements with required attribute
        const requiredInputs = document.querySelectorAll(
          "input[required], textarea[required]"
        );

        // Store their original required state and temporarily remove required attribute
        requiredInputs.forEach((input) => {
          input.dataset.originalRequired = input.required;
          input.required = false;
        });
      };

      // Apply the disabling function
      disableFormValidations();

      return () => {
        document.removeEventListener("keydown", handleKeyDown);
        document.removeEventListener("mousedown", handleOutsideClick);
        document.removeEventListener("keydown", handleTabKey);
        setIsVisible(false);

        // Restore when modal is closed
        const allForms = document.querySelectorAll("form");
        allForms.forEach((form) => {
          if (form.dataset.originalNoValidate !== undefined) {
            form.noValidate = form.dataset.originalNoValidate === "true";
            delete form.dataset.originalNoValidate;
          }
        });

        const requiredInputs = document.querySelectorAll(
          "input[data-original-required], textarea[data-original-required]"
        );
        requiredInputs.forEach((input) => {
          if (input.dataset.originalRequired !== undefined) {
            input.required = input.dataset.originalRequired === "true";
            delete input.dataset.originalRequired;
          }
        });
      };
    }
  }, [isOpen, handleKeyDown, handleOutsideClick, handleTabKey, initialFocus]);

  if (!isOpen) return null;

  const modalContent = (
    <div
      className="fixed top-0 left-0 right-0 w-full h-screen flex items-center justify-center p-4 md:p-4 p-2 modal-container"
      style={{ zIndex: zIndex }}
      role="dialog"
      aria-modal="true"
      aria-labelledby={title}
    >
      <div
        className={cn(
          "backdrop absolute inset-0 bg-black/60 transition-opacity duration-200",
          isVisible ? "opacity-100" : "opacity-0"
        )}
        onClick={handleOutsideClick}
      />

      <div
        ref={modalRef}
        className={cn(
          "relative flex flex-col space-y-6 w-full max-w-[40rem] bg-background rounded-lg border shadow-md text-foreground focus:outline-none transition-all duration-200",
          className?.includes("flex") ? "overflow-auto" : "overflow-hidden",
          className,
          isVisible ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
        )}
        onClick={(e) => {
          // Prevent clicks inside the modal from bubbling up and triggering global listeners
          e.stopPropagation();
        }}
        role="document"
        tabIndex="-1"
      >
        {title && (
          <div className="flex-shrink-0 mb-4 pt-6 px-6">
            <div className="relative flex justify-between flex-shrink-0">
              <h3
                className="flex text-xl font-medium items-center"
                id="modal-title"
              >
                {title}
              </h3>
              {!preventClose && (
                <Button
                  onClick={() => {
                    if (typeof onClose === "function") {
                      onClose();
                    }
                  }}
                  variant="ghost"
                  size="icon"
                  className="absolute -top-2 -right-2"
                >
                  <X />
                </Button>
              )}
            </div>
            {description && (
              <div className="max-w-[44rem] mt-1 text-base text-pretty">
                {description}
              </div>
            )}
          </div>
        )}
        <div
          className={cn(
            "space-y-6 overflow-y-auto px-6 pb-6",
            className?.includes("flex") ? "flex-1 min-h-0" : "flex-grow"
          )}
        >
          {children}
        </div>
        {footer && (
          <div className="flex w-full justify-end items-center space-x-3 flex-shrink-0 mt-6 px-6 pb-6 modal-footer">
            {footer}
          </div>
        )}
      </div>
    </div>
  );

  return noPortal
    ? modalContent
    : createPortal(modalContent, document.getElementById("theme-wrapper"));
}
