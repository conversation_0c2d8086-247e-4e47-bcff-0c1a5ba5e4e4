const { TokenAwareBatcher } = require("../tokenAwareBatching");

// Mock TokenManager for consistent testing
jest.mock("../../../helpers/tiktoken", () => ({
  TokenManager: jest.fn().mockImplementation(() => ({
    countFromString: jest.fn((text) => {
      // Mock token counting: roughly 4 characters per token
      return Math.ceil((text?.length || 0) / 4);
    }),
  })),
}));

describe("TokenAwareBatcher", () => {
  let batcher;

  beforeEach(() => {
    // Clear environment variables for clean testing
    delete process.env.OPENAI_EMBEDDING_MAX_TOKENS_PER_REQUEST;
    delete process.env.OPENAI_EMBEDDING_SAFETY_MARGIN;
    delete process.env.OPENAI_EMBEDDING_MAX_RETRIES;
    delete process.env.OPENAI_EMBEDDING_LOG_TOKEN_USAGE;
    delete process.env.OPENAI_EMBEDDING_LOG_BATCH_OPTIMIZATION;

    batcher = new TokenAwareBatcher();
  });

  describe("constructor", () => {
    it("should use default values when environment variables are not set", () => {
      expect(batcher.maxTokensPerRequest).toBe(300000);
      expect(batcher.safetyMargin).toBe(0.75);
      expect(batcher.maxRetries).toBe(3);
      expect(batcher.effectiveTokenLimit).toBe(225000); // 300000 * 0.75
      expect(batcher.logTokenUsage).toBe(false);
      expect(batcher.logBatchOptimization).toBe(false);
    });

    it("should use environment variables when set", () => {
      process.env.OPENAI_EMBEDDING_MAX_TOKENS_PER_REQUEST = "200000";
      process.env.OPENAI_EMBEDDING_SAFETY_MARGIN = "0.8";
      process.env.OPENAI_EMBEDDING_MAX_RETRIES = "5";
      process.env.OPENAI_EMBEDDING_LOG_TOKEN_USAGE = "true";
      process.env.OPENAI_EMBEDDING_LOG_BATCH_OPTIMIZATION = "true";

      const customBatcher = new TokenAwareBatcher();
      expect(customBatcher.maxTokensPerRequest).toBe(200000);
      expect(customBatcher.safetyMargin).toBe(0.8);
      expect(customBatcher.maxRetries).toBe(5);
      expect(customBatcher.effectiveTokenLimit).toBe(160000); // 200000 * 0.8
      expect(customBatcher.logTokenUsage).toBe(true);
      expect(customBatcher.logBatchOptimization).toBe(true);
    });

    it("should allow options to override defaults", () => {
      const customBatcher = new TokenAwareBatcher({
        maxTokensPerRequest: 100000,
        safetyMargin: 0.9,
      });
      expect(customBatcher.maxTokensPerRequest).toBe(100000);
      expect(customBatcher.safetyMargin).toBe(0.9);
      expect(customBatcher.effectiveTokenLimit).toBe(90000); // 100000 * 0.9
    });
  });

  describe("createTokenAwareBatches", () => {
    it("should handle empty input", () => {
      const batches = batcher.createTokenAwareBatches([]);
      expect(batches).toEqual([]);
    });

    it("should handle null input", () => {
      const batches = batcher.createTokenAwareBatches(null);
      expect(batches).toEqual([]);
    });

    it("should create single batch for small chunks", () => {
      const chunks = ["short text", "another short text", "third short text"];
      const batches = batcher.createTokenAwareBatches(chunks);

      expect(batches).toHaveLength(1);
      expect(batches[0]).toEqual(chunks);
    });

    it("should split chunks when token limit is exceeded", () => {
      // Create chunks that would exceed token limit when combined
      // Using 4 chars per token, we need > 225000 tokens = > 900000 chars
      const largeChunk = "a".repeat(600000); // ~150k tokens
      const anotherLargeChunk = "b".repeat(600000); // ~150k tokens
      const chunks = [largeChunk, anotherLargeChunk];

      const batches = batcher.createTokenAwareBatches(chunks);

      expect(batches.length).toBeGreaterThan(1);
      expect(batches[0]).toEqual([largeChunk]);
      expect(batches[1]).toEqual([anotherLargeChunk]);
    });

    it("should respect maxConcurrentChunks limit", () => {
      const chunks = Array(1000).fill("small chunk");
      const batches = batcher.createTokenAwareBatches(chunks, 100);

      // Should create multiple batches due to item limit
      expect(batches.length).toBeGreaterThan(1);
      batches.forEach((batch) => {
        expect(batch.length).toBeLessThanOrEqual(100);
      });
    });

    it("should handle single oversized chunk", () => {
      // Create a chunk that exceeds the effective token limit
      const oversizedChunk = "x".repeat(1000000); // ~250k tokens
      const batches = batcher.createTokenAwareBatches([oversizedChunk]);

      expect(batches).toHaveLength(1);
      expect(batches[0]).toEqual([oversizedChunk]);
    });

    it("should optimize batch sizes based on token counts", () => {
      // Mix of different sized chunks
      const smallChunk = "small"; // ~2 tokens
      const mediumChunk = "medium".repeat(1000); // ~1.5k tokens
      const largeChunk = "large".repeat(50000); // ~62.5k tokens

      const chunks = [
        smallChunk,
        mediumChunk,
        largeChunk,
        smallChunk,
        mediumChunk,
      ];
      const batches = batcher.createTokenAwareBatches(chunks);

      expect(batches.length).toBeGreaterThanOrEqual(1);

      // Verify no batch exceeds token limit
      batches.forEach((batch) => {
        const batchTokens = batch.reduce(
          (sum, text) => sum + batcher.tokenManager.countFromString(text),
          0
        );
        expect(batchTokens).toBeLessThanOrEqual(batcher.effectiveTokenLimit);
      });
    });
  });

  describe("isTokenLimitError", () => {
    it("should detect max_tokens_per_request error type", () => {
      const error = {
        type: "max_tokens_per_request",
        message: "Token limit exceeded",
      };
      expect(batcher.isTokenLimitError(error)).toBe(true);
    });

    it("should detect token limit error in message", () => {
      const error = {
        message: "Requested 400000 tokens, max 300000 tokens per request",
      };
      expect(batcher.isTokenLimitError(error)).toBe(true);
    });

    it("should detect alternative token limit error format", () => {
      const error = { message: "max 300000 tokens per request" };
      expect(batcher.isTokenLimitError(error)).toBe(true);
    });

    it("should return false for non-token-limit errors", () => {
      const error = { type: "rate_limit", message: "Rate limit exceeded" };
      expect(batcher.isTokenLimitError(error)).toBe(false);
    });

    it("should handle null/undefined errors", () => {
      expect(batcher.isTokenLimitError(null)).toBe(false);
      expect(batcher.isTokenLimitError(undefined)).toBe(false);
    });
  });

  describe("createRetryBatches", () => {
    it("should create smaller batches for retry", () => {
      const originalBatch = ["text1", "text2", "text3", "text4"];
      const retryBatches = batcher.createRetryBatches(originalBatch, 1);

      expect(retryBatches.length).toBeGreaterThanOrEqual(1);

      // Verify all original text is preserved
      const allRetryText = retryBatches.flat();
      expect(allRetryText.sort()).toEqual(originalBatch.sort());
    });

    it("should reduce batch size exponentially", () => {
      const originalBatch = Array(100).fill("text");

      const retry1 = batcher.createRetryBatches(originalBatch, 1);
      const retry2 = batcher.createRetryBatches(originalBatch, 2);

      // Second retry should create more, smaller batches
      expect(retry2.length).toBeGreaterThanOrEqual(retry1.length);
    });

    it("should handle empty batch", () => {
      const retryBatches = batcher.createRetryBatches([], 1);
      expect(retryBatches).toEqual([]);
    });

    it("should handle null batch", () => {
      const retryBatches = batcher.createRetryBatches(null, 1);
      expect(retryBatches).toEqual([]);
    });
  });

  describe("logTokenDiscrepancy", () => {
    let consoleSpy;

    beforeEach(() => {
      consoleSpy = jest.spyOn(console, "log").mockImplementation();
    });

    afterEach(() => {
      consoleSpy.mockRestore();
    });

    it("should log when token usage logging is enabled", () => {
      const logBatcher = new TokenAwareBatcher({ logTokenUsage: true });
      logBatcher.logTokenDiscrepancy(1000, 1200, 5);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining("Token count discrepancy")
      );
    });

    it("should not log when token usage logging is disabled", () => {
      batcher.logTokenDiscrepancy(1000, 1200, 5);
      expect(consoleSpy).not.toHaveBeenCalled();
    });

    it("should calculate discrepancy percentage correctly", () => {
      const logBatcher = new TokenAwareBatcher({ logTokenUsage: true });
      logBatcher.logTokenDiscrepancy(1000, 1250, 5);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining("Diff=25.0%")
      );
    });
  });

  describe("getConfig", () => {
    it("should return current configuration", () => {
      const config = batcher.getConfig();

      expect(config).toEqual({
        maxTokensPerRequest: 300000,
        safetyMargin: 0.75,
        effectiveTokenLimit: 225000,
        maxRetries: 3,
        logTokenUsage: false,
        logBatchOptimization: false,
      });
    });
  });
});
