const TRANSLATIONS = {
  "news-system": {
    title: "News & Announcements",
    previous: "Previous",
    next: "Next",
    of: "of",
    close: "Close",
    dismissThis: "Don't show again",
    dismissAll: "Dismiss all",
    management: {
      title: "News Management",
      description:
        "Manage system announcements and news items. System news items are deployed with the application and cannot be edited here.",
      create: "Create News",
      edit: "Edit News",
      loading: "Loading...",
      table: {
        title: "Title",
        priority: "Priority",
        targetRoles: "Target Roles",
        created: "Created",
        expires: "Expires",
        status: "Status",
        source: "Source",
        actions: "Actions",
        allUsers: "All users",
        never: "Never",
        active: "Active",
        inactive: "Inactive",
        systemNews: "System",
        localNews: "Local",
      },
      source: {
        system: "System News",
        local: "Local News",
        systemTooltip:
          "This news item is deployed with the application and cannot be edited",
        localTooltip: "This news item was created locally and can be edited",
      },
      form: {
        title: "Title",
        titlePlaceholder: "Enter news title",
        content: "Content",
        contentPlaceholder: "Enter news content",
        priority: "Priority",
        targetRoles: "Target Roles (leave empty for all users)",
        expiresAt: "Expires At (optional)",
        priorities: {
          low: "Low",
          medium: "Medium",
          high: "High",
          urgent: "Urgent",
        },
        roles: {
          admin: "Admin",
          manager: "Manager",
          default: "Default",
        },
      },
      actions: {
        create: "Create",
        update: "Update",
        cancel: "Cancel",
        delete: "Delete",
        edit: "Edit",
        view: "View",
        cannotEdit: "Cannot edit system news",
        cannotDelete: "Cannot delete system news",
      },
      confirmations: {
        delete: "Are you sure you want to delete this news item?",
      },
      messages: {
        createSuccess: "News created successfully",
        updateSuccess: "News updated successfully",
        deleteSuccess: "News deleted successfully",
        createError: "Failed to create news",
        updateError: "Failed to update news",
        deleteError: "Failed to delete news",
        fetchError: "Failed to fetch news",
        systemNewsInfo:
          "System news items are read-only and deployed with the application",
      },
    },
    error: {
      dismiss: "Failed to dismiss news",
      dismissAll: "Failed to dismiss all news",
      fetch: "Failed to fetch news",
    },
  },

  // News header and list functionality (moved from common.js)
  news: {
    header: {
      viewAll: "View all news",
      buttonText: "News",
    },
    list: {
      title: "News & Announcements",
      empty: "No news items to display",
      active: "Active",
      dismissed: "Dismissed",
      dismiss: "Dismiss",
      dismissSuccess: "News item dismissed successfully",
      dismissError: "Failed to dismiss news item",
      dismissedAt: "Dismissed on {{date}}",
      systemNews: "System",
      viewFull: "View full news item",
      filter: {
        all: "All",
        unread: "Active",
        read: "Dismissed",
      },
    },
    priority: {
      low: "Low",
      medium: "Medium",
      high: "High",
      urgent: "Urgent",
    },
  },
};

export default TRANSLATIONS;
