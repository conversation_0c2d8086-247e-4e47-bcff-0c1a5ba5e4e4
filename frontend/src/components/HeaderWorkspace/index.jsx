import React, { useEffect, useState, useRef, useContext } from "react";
import paths from "@/utils/paths";
import System from "@/models/system";
import useUser from "@/hooks/useUser";
import { Link, useLocation } from "react-router-dom";
import { isMobile } from "react-device-detect";
import { ThemeContext } from "@/context";
import { ToolTipWrapper } from "../Footer";
import useLoginMode from "@/hooks/useLoginMode";
import AccountModal from "../UserMenu/AccountModal";
import StyleAlignmentModal from "../UserMenu/StyleAlignmentModal";
import {
  GearSix,
  SignOut,
  User,
  UserCircle,
  Envelope,
  Pen,
} from "@phosphor-icons/react";
import { AUTH_TIMESTAMP, AUTH_TOKEN, AUTH_USER } from "@/utils/constants";
import { useTranslation } from "react-i18next";
import useLogo from "../../hooks/useLogo";
import CaseReference from "@/components/CaseReference";
import RecentUploads from "./RecentUploads";
import ThemeToggle from "./ThemeToggle";
import RequestLegalAssistance from "./RequestLegalAssistance";
import FeedbackButton from "@/components/FeedbackButton";
import SettingsNotification from "./SettingsNotification";
import UseGuide from "./UseGuide";
import NewsHeaderItem from "./NewsHeaderItem";
import VersionDisplay from "@/components/VersionDisplay";

export default function HeaderWorkspace() {
  const { t } = useTranslation();
  const { currentLogo } = useLogo();
  const mode = useLoginMode();
  const { user } = useUser();
  const menuRef = useRef();
  const buttonRef = useRef();
  const [showMenu, setShowMenu] = useState(false);
  const [showAccountSettings, setShowAccountSettings] = useState(false);
  const [showStyleAlignment, setShowStyleAlignment] = useState(false);
  const [supportEmail, setSupportEmail] = useState("");
  const { theme, toggleTheme } = useContext(ThemeContext);
  const location = useLocation();
  const isSettingsPage = location.pathname.includes("/settings/");

  useEffect(() => {
    const fetchSupportEmail = async () => {
      const supportEmail = await System.fetchSupportEmail();
      setSupportEmail(
        supportEmail?.email
          ? `mailto:${supportEmail.email}`
          : paths.mailToISTLegal()
      );
    };
    fetchSupportEmail();
  }, []);

  useEffect(() => {
    const handleClose = (event) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        !buttonRef.current.contains(event.target)
      ) {
        setShowMenu(false);
      }
    };

    if (showMenu) {
      document.addEventListener("mousedown", handleClose);
    }
    return () => document.removeEventListener("mousedown", handleClose);
  }, [showMenu]);

  const handleOpenAccountModal = () => {
    setShowAccountSettings(true);
    setShowMenu(false);
  };

  const handleOpenStyleModal = () => {
    setShowStyleAlignment(true);
    setShowMenu(false);
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      <>
        <div
          className={`flex items-center justify-between h-[var(--header-height)] bg-elevated border-b`}
        >
          <Link
            to="/"
            className="flex items-center ml-16 md:ml-4 shrink-0"
            aria-label={t("common.home")}
          >
            <img
              src={currentLogo}
              alt="logo"
              style={{ height: "32px" }}
              className={`${currentLogo ? "opacity-100" : "opacity-0"} ${
                isSettingsPage ? "hidden md:block" : ""
              }`}
            />
          </Link>

          <div className="flex items-center gap-6 pr-5">
            {!isMobile && (
              <>
                <CaseReference user={user} className="hidden md:flex" />
                <RequestLegalAssistance />
                <RecentUploads />
                <UseGuide />
                <NewsHeaderItem />
                <FeedbackButton />
              </>
            )}
            <div className="flex flex-row items-center py-2 gap-2 pr-0 md:pr-3">
              <ThemeToggle />
              <button
                ref={buttonRef}
                onClick={() => setShowMenu(!showMenu)}
                type="button"
                className="focus:outline-none"
              >
                <UserCircle className="text-foreground navbar-blk-btn opacity-80" />
              </button>
              {(!user || user?.role !== "default") && (
                <Link
                  to={
                    user?.role
                      ? user?.role === "superuser"
                        ? paths.settings.documentBuilder()
                        : paths.settings.system()
                      : paths.settings.appearance()
                  }
                  className="focus:outline-none"
                  aria-label={t("common.settings")}
                  data-tooltip-id="open-settings"
                  data-tooltip-content={t("settings.link-settings")}
                >
                  <div className="relative">
                    <GearSix className="text-foreground font-thin navbar-blk-btn opacity-80" />
                    <SettingsNotification className="absolute -top-0.5 -right-1" />
                  </div>
                </Link>
              )}
            </div>
          </div>
          {showMenu && (
            <div
              ref={menuRef}
              className="bg-background border shadow-lg w-fit rounded-lg absolute top-14 z-50 right-4 py-2 px-1.5 flex items-center-justify-center"
            >
              <div className="flex flex-col gap-y-2">
                {mode === "multi" &&
                  !!user &&
                  user.username !== "publicuser" && (
                    <>
                      <button
                        onClick={handleOpenAccountModal}
                        className="font-semibold text-[13px] text-foreground hover:bg-secondary w-full text-left px-4 py-1 rounded-md flex flex-row items-center justify-start gap-2 outline-none"
                      >
                        <User size={16} weight="bold" /> {t("header.account")}
                      </button>
                      <button
                        onClick={handleOpenStyleModal}
                        className="font-semibold text-[13px] text-foreground hover:bg-secondary w-full text-left px-4 py-1 rounded-md flex flex-row items-center justify-start gap-2 outline-none"
                      >
                        <Pen size={16} weight="bold" />
                        {t("header.style-alignment")}
                      </button>
                      <button
                        title={
                          user.username === "publicuser"
                            ? t("header.login")
                            : t("header.sign-out")
                        }
                        onClick={() => {
                          window.localStorage.removeItem(AUTH_USER);
                          window.localStorage.removeItem(AUTH_TOKEN);
                          window.localStorage.removeItem(AUTH_TIMESTAMP);
                          window.localStorage.removeItem("workspace-store");
                          window.localStorage.removeItem("system-settings");
                          window.location.replace(paths.home());
                        }}
                        type="button"
                        className="focus:outline-none text-foreground hover:bg-secondary font-semibold text-[13px] w-full text-left px-4 py-1 rounded-md flex flex-row items-center justify-start gap-2 outline-none"
                      >
                        <SignOut size={20} weight="bold" />{" "}
                        {t("header.sign-out")}
                      </button>
                      {supportEmail && (
                        <ToolTipWrapper id="contact-support">
                          <Link
                            to={supportEmail}
                            className="text-foreground hover:bg-secondary font-semibold text-[13px] w-full text-left px-4 py-1 rounded-md flex flex-row items-center justify-start gap-2 outline-none"
                            aria-label={t("common.support")}
                            data-tooltip-content={t("common.contact-support")}
                          >
                            <Envelope
                              size={20}
                              className="font-thin navbar-blk-btn"
                            />
                            {t("support.title")}
                          </Link>
                        </ToolTipWrapper>
                      )}
                      <div className="border-t border-border my-1"></div>
                      <VersionDisplay inline={true} />
                    </>
                  )}
              </div>
            </div>
          )}
          {user && showAccountSettings && (
            <AccountModal
              user={user}
              hideModal={() => setShowAccountSettings(false)}
            />
          )}
          {user && showStyleAlignment && (
            <StyleAlignmentModal
              hideModal={() => setShowStyleAlignment(false)}
            />
          )}
        </div>
      </>
    </ThemeContext.Provider>
  );
}
