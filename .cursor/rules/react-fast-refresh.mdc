---
description: Guidelines for enabling effective Fast Refresh in React applications
globs: "{frontend/src/**/*.{js,jsx}}"
alwaysApply: true
---

# React Fast Refresh Best Practices

React Fast Refresh is a feature that provides instantaneous feedback for edits in React components. To ensure it works correctly and efficiently in our application, follow these guidelines.

## Core Principles

1. **Files should only export React components, custom hooks, or non-React functions/values**
   - Don't mix component exports with context definitions or other non-component exports

2. **Keep component definitions consistent**
   - Don't change component signatures between renders
   - Avoid changing a component from function to class or vice versa

3. **Maintain component identity**
   - Don't change the order of component definitions
   - Use named exports rather than default exports when possible

## Context Definitions

### ✅ DO: Separate context definitions into dedicated files

```javascript
// context.js
import { createContext } from 'react';

export const MyContext = createContext();
export const MyContextProvider = ({ children, value }) => (
  <MyContext.Provider value={value}>{children}</MyContext.Provider>
);

// Component.jsx
import { MyContext, MyContextProvider } from './context';

export function MyComponent() {
  const value = useContext(MyContext);
  // Component code...
}
```

### ❌ DON'T: Define and export contexts in component files

```javascript
// This pattern breaks Fast Refresh
import { createContext } from 'react';

export const MyContext = createContext(); // This export breaks Fast Refresh

export function MyComponent() {
  // Component code...
}
```

## Component Organization

### ✅ DO: Export multiple components from a single file when they're closely related

```javascript
// Related components can be in one file
export function Parent() { /* ... */ }
export function Child() { /* ... */ }
```

### ❌ DON'T: Export unrelated components, hooks, and utilities from the same file

```javascript
// This mixing of concerns can cause Fast Refresh issues
export function UserProfile() { /* ... */ }
export function unrelatedUtility() { /* ... */ }
export const SomeContext = createContext();
```

## Custom Hooks

### ✅ DO: Name custom hooks with the 'use' prefix

```javascript
export function useCustomHook() {
  // Hook implementation
}
```

### ❌ DON'T: Change hook signatures between renders

```javascript
// Changing parameters can break Fast Refresh
export function useCustomHook(param1, param2) { /* ... */ }
// Don't change to:
export function useCustomHook(options) { /* ... */ }
```

## State Management

### ✅ DO: Use React's built-in state management or Zustand

```javascript
// Component state
const [state, setState] = useState(initialState);

// Zustand store
const useStore = create((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
}));
```

### ❌ DON'T: Use custom event systems that bypass React's reactivity model

```javascript
// Avoid custom event systems
const listeners = [];
export function subscribe(listener) {
  listeners.push(listener);
  return () => {
    listeners = listeners.filter(l => l !== listener);
  };
}
export function notify(event) {
  listeners.forEach(listener => listener(event));
}
```

## Troubleshooting

If Fast Refresh isn't working correctly:

1. Check for mixed exports in your files
2. Ensure contexts are defined in separate files
3. Verify that component definitions remain consistent
4. Look for components that change their signature between renders
5. Check for direct DOM manipulations that might interfere with React's reconciliation

By following these guidelines, you'll ensure that Fast Refresh works correctly in your application, providing a smoother development experience.
