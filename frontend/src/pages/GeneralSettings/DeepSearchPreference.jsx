import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import System from "@/models/system";
import showToast from "@/utils/toast";
import { Button } from "@/components/Button";
import Selector from "@/components/Select";
import Input from "@/components/ui/Input";
import Slider from "@/components/ui/Slider";
import Toggle from "@/components/ui/Toggle";

export default function DeepSearchPreference() {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState(null);

  // Provider options sorted alphabetically
  const providerOptions = [
    // TODO: Temporarily disable <PERSON> until integration is verified
    // { value: "bing", label: "Bing" },
    { value: "brave", label: "Brave" },
    { value: "duckduckgo", label: "DuckDuckGo" },
    // TODO: Temporarily disable <PERSON> until integration is verified
    // { value: "google", label: "Google" },
  ];

  // Model options sorted alphabetically
  const modelOptions = [
    { value: "gemini-1.5-flash-latest", label: "Gemini 1.5 Flash (Latest)" },
    { value: "gemini-1.5-pro-latest", label: "Gemini 1.5 Pro (Latest)" },
    { value: "gemini-2.0-flash", label: "Gemini 2.0 Flash" },
    {
      value: "gemini-2.5-pro-exp-03-25",
      label: "Gemini 2.5 Pro (Experimental)",
    },
  ];

  useEffect(() => {
    async function fetchSettings() {
      const defaultSettings = {
        provider: "duckduckgo",
        modelId: "gemini-1.5-flash-latest",
        apiKey: "",
        hasApiKey: false,
        enabled: false,
        contextPercentage: 10,
      };
      try {
        const response = await System.getDeepSearchSettings();
        if (response?.settings) {
          let fetchedPercentage = response.settings.contextPercentage;
          let numericPercentage = Number(fetchedPercentage);
          if (
            isNaN(numericPercentage) ||
            numericPercentage < 5 ||
            numericPercentage > 20
          ) {
            console.warn(
              `Fetched invalid contextPercentage (${fetchedPercentage}), defaulting to 10.`
            );
            numericPercentage = 10;
          }
          // Ensure all default keys exist, merging fetched values
          setSettings({
            ...defaultSettings,
            ...response.settings,
            contextPercentage: numericPercentage,
          });
        } else {
          // If settings are missing, set the complete default state
          console.warn("No settings returned from API, using defaults.");
          setSettings(defaultSettings);
        }
      } catch (error) {
        console.error("Failed to fetch DeepSearch settings:", error);
        // Set complete default state on error
        setSettings(defaultSettings);
        showToast(t("deep_search.fetch_error"), "error");
      } finally {
        setLoading(false);
      }
    }
    fetchSettings();
  }, [t]);

  const handleSave = async () => {
    setSaving(true);
    try {
      // Do not send apiKey for DuckDuckGo (no API key required)
      const payload = { ...settings };
      if (payload.provider === "duckduckgo") {
        delete payload.apiKey;
      }
      const { success, error } = await System.updateDeepSearchSettings(payload);
      if (success) {
        showToast(t("deep_search.toast_success"), "success");
      } else {
        showToast(t("deep_search.toast_error", { error }), "error");
      }
    } catch (error) {
      console.error("Failed to save DeepSearch settings:", error);
      showToast(
        t("deep_search.toast_error", { error: error.message }),
        "error"
      );
    } finally {
      setSaving(false);
    }
  };

  const handleChange = (field, value) => {
    setSettings((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  if (loading || !settings) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-foreground"></div>
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-y-auto p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">{t("deep_search.title")}</h1>
        <p className="mb-6 text-muted-foreground">
          {t("deep_search.description")}
        </p>
        <p className="mb-6 text-sm text-orange-600 bg-orange-100 p-3 rounded-md border border-orange-300">
          <span className="font-semibold">{t("common.note")}:</span>{" "}
          {t("deep_search.brave_recommended")}
        </p>

        <div className="space-y-6 bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-medium">{t("deep_search.enable")}</h2>
              <p className="text-sm text-muted-foreground">
                {t("deep_search.enable_description")}
              </p>
            </div>
            <Toggle
              checked={settings.enabled}
              onChange={(e) => handleChange("enabled", e.target.checked)}
            />
          </div>

          <div className="border-t border-border pt-4">
            <h2 className="text-lg font-medium mb-4">
              {t("deep_search.provider_settings")}
            </h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {t("deep_search.provider")}
                </label>
                <Selector
                  options={providerOptions}
                  value={settings.provider}
                  onChange={(value) => handleChange("provider", value)}
                  disabled={!settings.enabled}
                />
              </div>

              {settings.provider === "google" && (
                <div>
                  <label className="block text-sm font-medium mb-1">
                    {t("deep_search.model")}
                  </label>
                  <Selector
                    options={modelOptions}
                    value={settings.modelId}
                    onChange={(value) => handleChange("modelId", value)}
                    disabled={!settings.enabled}
                  />
                </div>
              )}

              {settings.provider !== "duckduckgo" && (
                <div>
                  <label className="block text-sm font-medium mb-1">
                    {t("deep_search.api_key")}
                  </label>
                  <Input
                    type="password"
                    value={settings.apiKey}
                    onChange={(e) => handleChange("apiKey", e.target.value)}
                    placeholder={
                      settings.hasApiKey
                        ? t("deep_search.api_key_placeholder_set")
                        : t("deep_search.api_key_placeholder")
                    }
                    disabled={!settings.enabled}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {t("deep_search.api_key_help")}
                  </p>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium mb-1">
                  {t("deep_search.context_percentage")}
                </label>
                <div className="flex items-center space-x-4">
                  <div className="flex-grow">
                    <Slider
                      min={5}
                      max={20}
                      value={settings.contextPercentage ?? 15}
                      onValueChange={(valueArray) =>
                        handleChange("contextPercentage", valueArray[0])
                      }
                      disabled={!settings.enabled}
                    />
                  </div>
                  <div className="w-12 text-center">
                    {settings.contextPercentage}%
                  </div>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {t("deep_search.context_percentage_help")}
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-end pt-4 border-t border-border">
            <Button
              onClick={handleSave}
              isLoading={saving}
              disabled={saving}
              className="px-4 py-2"
            >
              {t("common.save")}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
