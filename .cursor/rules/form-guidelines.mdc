---
description: 
globs: 
alwaysApply: true
---
# Form Implementation and Validation Guidelines

## Overview

All forms in this codebase should be implemented using **react-hook-form** for state management and **zod** for schema-based validation. This ensures forms are minimal, robust, accessible, and easy to maintain.

## Principles

- **Minimal**: Only include fields and logic necessary for the form's purpose.
- **Self-documenting**: Use clear, descriptive names for fields and handlers. The structure should make the data flow obvious.
- **Consistent**: Follow the same structure and validation approach for all forms.
- **Accessible**: Use semantic HTML, proper labels, and error messages.
- **Performant**: Avoid unnecessary re-renders and keep form logic focused.

## Form Structure

### 1. Schema Definition

- Define a `zod` schema for the form at the top of the component.
- Use `.min()`, `.max()`, `.email()`, etc., for validation.
- Use translation keys for error messages if i18n is required.

```js
import { z } from "zod";

const formSchema = z.object({
  fieldName: z.string().min(1, t("form.fieldNameRequired")),
  // ...other fields
});
```

### 2. Form Setup

- Use `useForm` from `react-hook-form` with `zodResolver`.
- Set `defaultValues` for all fields.

```js
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

const {
  register,
  handleSubmit,
  formState: { errors },
  reset,
} = useForm({
  resolver: zodResolver(formSchema),
  defaultValues: {
    fieldName: "",
    // ...other fields
  },
});
```

### 3. Form Rendering

- Use semantic HTML: `<form>`, `<label>`, `<input>`, etc.
- Use project-standard UI components (e.g., `Input`, `Label`, `Button`, `FormItem`).
- Each field:
  - Is wrapped in a `<FormItem>`.
  - Has a `<Label>` with `htmlFor`.
  - Uses `register` for binding.
  - Shows an error message if validation fails.

```js
import FormItem from "@/components/ui/FormItem";

<form onSubmit={handleSubmit(onSubmit)} id="form-id">
  <FormItem>
    <Label htmlFor="fieldName">{t("form.fieldName")}</Label>
    <Input
      id="fieldName"
      {...register("fieldName")}
      placeholder={t("form.fieldNamePlaceholder")}
    />
    {errors.fieldName && (
      <p className="text-red-500">{errors.fieldName.message}</p>
    )}
  </FormItem>
  {/* ...other fields */}
  <Button type="submit">Submit</Button>
</form>;
```

### 4. Submission Handling

- Use an `onSubmit` handler that receives validated data.
- Set a loading state (`isSubmitting`) to prevent duplicate submissions.
- Show success/error toasts as appropriate.
- Reset the form and close modal/dialog on success.

```js
const onSubmit = async (data) => {
  setIsSubmitting(true);
  try {
    // ...submit logic
    showToast(t("form.success"), "success");
    reset();
    onClose();
  } catch (error) {
    showToast(t("form.error"), "error");
  } finally {
    setIsSubmitting(false);
  }
};
```

## Best Practices

- **Field Naming**: Use camelCase for field names and variables.
- **Error Handling**: Always display validation errors next to the relevant field.
- **Accessibility**: Ensure every input has a corresponding label and `id`.
- **i18n**: Use translation keys for all labels, placeholders, and error messages.
- **Component Sourcing**: Use existing UI components from the codebase. Only create new ones if necessary.
- **Minimal State**: Only track state needed for form logic (e.g., `isSubmitting`).

## Anti-Patterns

- Do **not** use uncontrolled inputs or manage field state manually.
- Do **not** skip schema validation for any user input.
- Do **not** use inline error messages without associating them with the correct field.
- Do **not** use TypeScript or Next.js-specific patterns unless the project is using them.

## Example

```js
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Input from "@/components/ui/Input";
import Label from "@/components/ui/Label";
import { Button } from "@/components/Button";
import FormItem from "@/components/ui/FormItem";

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email"),
});

export default function ExampleForm() {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: { name: "", email: "" },
  });

  const onSubmit = async (data) => {
    // ...submit logic
    reset();
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <FormItem>
        <Label htmlFor="name">Name</Label>
        <Input id="name" {...register("name")} />
        {errors.name && <p className="text-red-500">{errors.name.message}</p>}
      </FormItem>
      <FormItem>
        <Label htmlFor="email">Email</Label>
        <Input id="email" {...register("email")} />
        {errors.email && <p className="text-red-500">{errors.email.message}</p>}
      </FormItem>
      <Button type="submit">Submit</Button>
    </form>
  );
}
```

## References

- [shadcn/ui Form Docs](mdc:https:/ui.shadcn.com/docs/components/form) (for advanced patterns and accessibility)
- [react-hook-form Documentation](mdc:https:/react-hook-form.com)
- [zod Documentation](mdc:https:/zod.dev)

---

**Summary:**
All forms must use `react-hook-form` and `zod` for validation, follow a minimal and accessible structure, use project-standard UI components, and provide clear error handling and feedback. Avoid unnecessary complexity and always prefer clarity and maintainability.
