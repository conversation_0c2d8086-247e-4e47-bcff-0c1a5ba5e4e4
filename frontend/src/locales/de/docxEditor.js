export default {
  // =========================
  // DOCX EDITOR
  // =========================
  "docx-edit": {
    "edit-instructions":
      "G<PERSON> Anweisungen ein, wie du das Dokument bearbeiten möchtest. Sei spezi<PERSON>sch, welche Änderungen du vornehmen möchtest.",
    "instructions-placeholder":
      "z.B. Grammatikfehler korrigieren, den Ton formeller gestalten, einen Abschlussparagraphen hinzufügen...",
    "process-button": "Dokument verarbeiten",
    "upload-docx": "DOCX hochladen",
    "processing-upload": "Verarbeitung...",
    "content-extracted": "Inhalt aus DOCX-Datei extrahiert",
    "file-type-note": "Nur .docx-Dateien werden unterstützt",
    "upload-error": "Fehler beim Ho<PERSON>laden der Datei: ",
    "no-instructions": "Bitte gib Bearbeitungsanweisungen ein",
    "process-error": "Fehler bei der Verarbeitung des Dokuments: ",
    "changes-highlighted": "Dokument mit hervorgehobenen Änderungen",
    "download-button": "Dokument herunterladen",
    "start-over-button": "Neu beginnen",
    "no-document": "Kein Dokument zum Herunterladen verfügbar",
    "download-error": "Fehler beim Herunterladen des Dokuments: ",
    "download-success": "Dokument erfolgreich heruntergeladen",
    processing: "Dokument wird verarbeitet...",
    "instructions-used": "Verwendete Anweisungen",
    "import-success": "DOCX-Inhalt erfolgreich importiert",
    "edit-success": "DOCX-Inhalt erfolgreich aktualisiert",
    "canvas-document-title": "Canvas-Dokument",
    "upload-button": "DOCX hochladen",
    "download-as-docx": "Als DOCX herunterladen",
    "output-example": "Ausgabebeispiel",
    "output-example-desc":
      "Laden Sie eine DOCX-Datei hoch, um Beispielinhalte zu Ihrem Prompt hinzuzufügen",
    "content-examples-tag-open": "<INHALTS_BEISPIEL>",
    "content-examples-tag-close": "</INHALTS_BEISPIEL>",
    "content-examples-info":
      "<INFO>Dies ist ein Beispiel für den zu erstellenden Inhalt aus einer ähnlichen juristischen Aufgabe. Beachten Sie, dass dieser Beispielinhalt viel kürzer oder länger sein kann als der Inhalt, der jetzt erstellt werden soll.</INFO>",
    "contains-example-content": "[Enthält Beispielinhalt]",
  },
};
