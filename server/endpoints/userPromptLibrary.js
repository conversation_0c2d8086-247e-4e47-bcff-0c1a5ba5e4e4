/**
 * Endpoints for managing user prompt libraries
 */
const { reqBody, userFromSession } = require("../utils/http");
const { validatedRequest } = require("../utils/middleware/validatedRequest");
const { UserPromptLibrary } = require("../models/userPromptLibrary");

function userPromptLibraryEndpoints(router) {
  if (!router) {
    console.error("Router object is undefined in userPromptLibraryEndpoints");
    return;
  }

  /**
   * Get all prompt libraries for the authenticated user
   * GET /user/prompt-library
   */
  router.get(
    "/user/prompt-library",
    [validatedRequest],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);

        if (!user || !user.id) {
          return response.status(401).json({
            success: false,
            error: "Unauthorized",
          });
        }

        const prompts = await UserPromptLibrary.getByUserId(user.id);

        return response.status(200).json({
          success: true,
          prompts,
        });
      } catch (error) {
        console.error("Error in GET /user/prompt-library:", error.message);
        return response.status(500).json({
          success: false,
          error: "Internal server error",
        });
      }
    }
  );

  /**
   * Get a specific prompt library by ID
   * GET /user/prompt-library/:id
   */
  router.get(
    "/user/prompt-library/:id",
    [validatedRequest],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);

        if (!user || !user.id) {
          return response.status(401).json({
            success: false,
            error: "Unauthorized",
          });
        }

        const { id } = request.params;
        const prompt = await UserPromptLibrary.getByIdAndUserId(id, user.id);

        if (!prompt) {
          return response.status(404).json({
            success: false,
            error: "Prompt not found",
          });
        }

        return response.status(200).json({
          success: true,
          prompt,
        });
      } catch (error) {
        console.error("Error in GET /user/prompt-library/:id:", error.message);
        return response.status(500).json({
          success: false,
          error: "Internal server error",
        });
      }
    }
  );

  /**
   * Create a new prompt library
   * POST /user/prompt-library
   */
  router.post(
    "/user/prompt-library",
    [validatedRequest],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);

        if (!user || !user.id) {
          return response.status(401).json({
            success: false,
            error: "Unauthorized",
          });
        }

        const { name, prompt_text, description } = reqBody(request);

        // Validate required fields
        if (!name || !prompt_text) {
          return response.status(400).json({
            success: false,
            error: "Name and prompt text are required",
          });
        }

        try {
          const prompt = await UserPromptLibrary.create(user.id, {
            name,
            prompt_text,
            description,
          });

          return response.status(201).json({
            success: true,
            message: "Prompt library created successfully",
            prompt,
          });
        } catch (validationError) {
          return response.status(400).json({
            success: false,
            error: validationError.message,
          });
        }
      } catch (error) {
        console.error("Error in POST /user/prompt-library:", error.message);
        return response.status(500).json({
          success: false,
          error: "Internal server error",
        });
      }
    }
  );

  /**
   * Update an existing prompt library
   * PUT /user/prompt-library/:id
   */
  router.put(
    "/user/prompt-library/:id",
    [validatedRequest],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);

        if (!user || !user.id) {
          return response.status(401).json({
            success: false,
            error: "Unauthorized",
          });
        }

        const { id } = request.params;
        const { name, prompt_text, description } = reqBody(request);

        try {
          const updatedPrompt = await UserPromptLibrary.update(id, user.id, {
            name,
            prompt_text,
            description,
          });

          if (!updatedPrompt) {
            return response.status(404).json({
              success: false,
              error: "Prompt not found",
            });
          }

          return response.status(200).json({
            success: true,
            message: "Prompt library updated successfully",
            prompt: updatedPrompt,
          });
        } catch (validationError) {
          return response.status(400).json({
            success: false,
            error: validationError.message,
          });
        }
      } catch (error) {
        console.error("Error in PUT /user/prompt-library/:id:", error.message);
        return response.status(500).json({
          success: false,
          error: "Internal server error",
        });
      }
    }
  );

  /**
   * Delete a prompt library
   * DELETE /user/prompt-library/:id
   */
  router.delete(
    "/user/prompt-library/:id",
    [validatedRequest],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);

        if (!user || !user.id) {
          return response.status(401).json({
            success: false,
            error: "Unauthorized",
          });
        }

        const { id } = request.params;

        const deleted = await UserPromptLibrary.delete(id, user.id);

        if (!deleted) {
          return response.status(404).json({
            success: false,
            error: "Prompt not found",
          });
        }

        return response.status(200).json({
          success: true,
          message: "Prompt library deleted successfully",
        });
      } catch (error) {
        console.error(
          "Error in DELETE /user/prompt-library/:id:",
          error.message
        );
        return response.status(500).json({
          success: false,
          error: "Internal server error",
        });
      }
    }
  );
}

module.exports = { userPromptLibraryEndpoints };
