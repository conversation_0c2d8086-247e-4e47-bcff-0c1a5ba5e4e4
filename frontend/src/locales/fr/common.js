const TRANSLATIONS = {
  // =========================
  // COMMON STRINGS & PLACEHOLDERS
  // =========================
  common: {
    examples: "Exemples",
    "workspaces-name": "Nom de l'espace de travail",
    ok: "OK",
    error: "erreur",
    confirm: "Confirmer",
    confirmstart: "Confirmer et démarrer",
    savesuccess: "Paramètres enregistrés avec succès",
    saveerror: "Échec de l'enregistrement des paramètres",
    success: "succès",
    user: "Utilisateur",
    selection: "Sélection du modèle",
    saving: "Enregistrement...",
    save: "Enregistrer les modifications",
    previous: "Page précédente",
    next: "Page suivante",
    cancel: "Annuler",
    "search-placeholder": "Rechercher...",
    "no-results": "Aucun résultat trouvé",
    "more-actions": "Plus d'actions",
    "delete-message": "Supprimer le message",
    copy: "Copier",
    edit: "Modifier",
    regenerate: "Régénérer",
    "export-word": "Exporter vers Word",
    "stop-generating": "Arrêter la génération",
    "attach-file": "Joindre un fichier à cette conversation",
    home: "Accueil",
    settings: "Paramètres",
    support: "Support",
    "clear-reference": "Effacer la référence",
    "send-message": "Envoyer un message",
    "ask-legal": "Demander des informations juridiques",
    "stop-response": "Arrêter la génération de réponse",
    "contact-support": "Contacter le support",
    "copy-connection": "Copier la chaîne de connexion",
    "auto-connect": "Se connecter automatiquement à l'extension",
    back: "Retour",
    "back-to-workspaces": "Retour aux espaces de travail",
    off: "Désactivé",
    on: "Activé",
    continue: "Continuer",
    rename: "Renommer",
    delete: "Supprimer",
    "default-skill":
      "Cette compétence est activée par défaut et ne peut pas être désactivée.",
    timeframes: "Périodes",
    other: "Autres options",
    placeholder: {
      username: "Mon nom d'utilisateur",
      password: "Votre mot de passe",
      email: "Entrez votre email",
      "support-email": "<EMAIL>",
      website: "https://www.example.com",
      "site-name": "IST Legal",
      "search-llm": "Rechercher un fournisseur LLM spécifique",
      "search-providers": "Rechercher les fournisseurs disponibles",
      "message-heading": "En-tête du message",
      "message-content": "Message",
      "token-limit": "4096",
      "max-tokens": "Nombre maximum de tokens par requête (ex: 1024)",
      "api-key": "Clé API",
      "base-url": "URL de base",
      endpoint: "Point d'accès API",
    },
    tooltip: {
      copy: "Copier dans le presse-papiers",
      delete: "Supprimer cet élément",
      edit: "Modifier cet élément",
      save: "Enregistrer les modifications",
      cancel: "Annuler les modifications",
      search: "Rechercher des éléments",
      add: "Ajouter un nouvel élément",
      remove: "Supprimer l'élément",
      upload: "Télécharger un fichier",
      download: "Télécharger le fichier",
      refresh: "Actualiser les données",
      settings: "Ouvrir les paramètres",
      more: "Plus d'options",
    },
    "default.message": "Entrez votre message ici",
    preview: "Aperçu",
    prompt: "Invite",
    loading: "Chargement...",
    download: "Télécharger au format brut",
    open_in_new_tab: "Ouvrir dans un nouvel onglet avec formatage",
    close: "Fermer",
    done: "Terminé",
    note: "Note",
    clearing: "Effacement...",
  },

  // =========================
  // CONFIRMATION MESSAGES
  // =========================
  deleteWorkspaceConfirmation:
    "Êtes-vous sûr de vouloir supprimer {{name}} ?\nAprès cela, il sera indisponible dans cette instance.\n\nCette action est irréversible.",
  deleteConfirmation:
    "Êtes-vous sûr de vouloir supprimer ${user.username} ?\nAprès cela, ils seront déconnectés et ne pourront plus utiliser cette instance.\n\nCette action est irréversible.",
  suspendConfirmation:
    "Êtes-vous sûr de vouloir suspendre {{username}} ?\nAprès cela, ils seront déconnectés et ne pourront pas se reconnecter à cette instance tant qu'un administrateur ne les aura pas réactivés.",
  flushVectorCachesWorkspaceConfirmation:
    "Êtes-vous sûr de vouloir vider les caches vectoriels pour cet espace de travail ?",
  apiKeys: {
    "deactivate-title": "Désactiver la clé API",
    "deactivate-message":
      "Êtes-vous sûr de vouloir désactiver cette clé API ?\nAprès cela, elle ne sera plus utilisable.\n\nCette action est irréversible.",
  },

  // =========================
  // BUTTON LABELS
  // =========================
  button: {
    delete: "Supprimer",
    edit: "Modifier",
    suspend: "Suspendre",
    unsuspend: "Réactiver",
    save: "Sauvegarder",
    accept: "Accepter",
    decline: "Décliner",
    ok: "OK",
    "flush-vector-caches": "Vider les caches vectoriels",
    cancel: "Annuler",
    saving: "Enregistrement",
    save_llm: "Enregistrer la sélection LLM",
    save_template: "Enregistrer le modèle",
    "reset-to-default": "Réinitialiser par défaut",
    create: "Créer",
    enable: "Activer",
    disable: "Désactiver",
    reset: "Réinitialiser",
    revoke: "Révoquer",
  },

  // =========================
  // CONFIRM MESSAGE
  // =========================
  "confirm-message": {
    "delete-doc":
      "Êtes-vous sûr de vouloir supprimer ces fichiers et dossiers ?\nCela supprimera les fichiers du système et les retirera automatiquement de tous les espaces de travail.\nCette action est irréversible.",
  },

  // =========================
  // STATUSES
  // =========================
  statuses: { enabled: "activé", disabled: "désactivé" },

  // =========================
  // ERRORS
  // =========================
  errors: {
    "fetch-models": "Échec de la récupération des modèles personnalisés",
    "fetch-models-error": "Erreur lors de la récupération des modèles",
    "upgrade-error": "Erreur lors de l'amélioration de la réponse",
    "failed-process-file": "Échec du traitement du fichier : {{text}}",
    "failed-process-attachment": "Échec du traitement de la pièce jointe",
    "failed-extract-content":
      "Échec de l'extraction du contenu de {{fileName}}",
    "failed-process-content": "Échec du traitement du contenu du fichier",
    common: { error: "Erreur" },
    workspace: {
      "already-exists": "Un espace de travail avec ce nom existe déjà",
    },
    auth: {
      "invalid-credentials": "Identifiants de connexion invalides.",
      "account-suspended": "Compte suspendu par l'administrateur.",
      "invalid-password": "Mot de passe invalide fourni",
    },
    env: {
      "anthropic-key-format":
        "Format de clé API Anthropic invalide. La clé doit commencer par 'sk-ant-'",
      "openai-key-format": "La clé API OpenAI doit commencer par 'sk-'",
      "jina-key-format": "La clé API Jina doit commencer par 'jina_'",
    },
  },

  // =========================
  // CHARTS
  // =========================
  charts: {
    downloading: "Téléchargement de l'image...",
    download: "Télécharger l'image du graphique",
  },

  // =========================
  // OPTIONS
  // =========================
  options: { yes: "Oui", no: "Non" },

  // =========================
  // USER MANAGEMENT
  // =========================
  user: {
    "delete-title": "Supprimer l'utilisateur",
    "suspend-title": "Suspendre l'utilisateur",
    "unsuspend-title": "Réactiver l'utilisateur",
    suspended: "Utilisateur suspendu avec succès",
    unsuspended: "Utilisateur réactivé avec succès",
  },

  // =========================
  // METRICS VISIBILITY
  // =========================
  "metrics.visibility.hover": "Les métriques sont visibles.",
  "metrics.visibility.available": "Les métriques sont disponibles.",

  // =========================
  // INVOICE REFERENCE NAVIGATION
  // =========================
  "invoice-reference-navigation": {
    title: "Référence de facture active",
    message:
      "Vous avez une référence de facture active ({{reference}}) et vous êtes sur le point de naviguer vers un {{destinationType}} différent. Que souhaitez-vous faire ?",
    "current-reference": "Référence actuelle :",
    explanation:
      "Vous pouvez soit effacer la référence et continuer, soit conserver la référence et continuer.",
    "clear-and-continue": "Effacer la référence et continuer",
    "keep-and-continue": "Conserver la référence et continuer",
    success: "Référence de facture effacée avec succès",
    "destination-types": {
      thread: "fil de discussion",
      workspace: "espace de travail",
      module: "module",
      location: "emplacement",
    },
  },

  // Months
  "month.1": "janv.",
  "month.2": "févr.",
  "month.3": "mars",
  "month.4": "avril",
  "month.5": "mai",
  "month.6": "juin",
  "month.7": "juil.",
  "month.8": "août",
  "month.9": "sept.",
  "month.10": "oct.",
  "month.11": "nov.",
  "month.12": "déc",

  // =========================
  // VERSION DISPLAY
  // =========================
  version: {
    "tooltip-title": "Version {{version}}",
    title: "Version {{version}}",
  },

  // =========================
  // STYLE UPLOAD
  // =========================
  "style-upload": {
    "manage-files": "Gérer les fichiers de référence de style",
    "manage-files-description":
      "Ici, vous pouvez télécharger des fichiers que vous avez rédigés pour générer un style personnalisé. Lorsque cette fonction est active, les résultats de la plateforme s'aligneront plus étroitement sur votre style d'écriture professionnel personnel.",
    "file-already-exists": "Le fichier existe déjà",
    "files-uploaded": "Fichiers téléchargés avec succès",
    "remove-file": "Supprimer le fichier",
    "add-more": "Ajouter plus de fichiers",
    "clear-all": "Tout effacer",
    "no-files": "Veuillez télécharger au moins un fichier",
  },
};

export default TRANSLATIONS;
