module.exports = {
  testEnvironment: "jsdom",
  transform: {
    "^.+\\.(js|jsx|mjs|cjs)$": [
      "babel-jest",
      {
        presets: [
          [
            require.resolve("@babel/preset-env"),
            { targets: { node: "current" }, modules: "commonjs" },
          ],
          [require.resolve("@babel/preset-react"), { runtime: "automatic" }],
        ],
      },
    ],
  },
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/src/$1",
  },
  setupFilesAfterEnv: ["<rootDir>/src/setupTests.js"],
  testMatch: ["**/__tests__/**/*.test.js", "**/?(*.)+(spec|test).js?(x)"],
  moduleFileExtensions: ["js", "jsx", "json", "mjs", "cjs"],
  testPathIgnorePatterns: ["/node_modules/", "/dist/"],
  transformIgnorePatterns: [
    "/node_modules/(?!(zustand|immer|@testing-library)/)",
  ],
  collectCoverageFrom: [
    "src/stores/attachmentStore.js",
    "src/stores/progressStore.js",
    "src/hooks/useThreadProgress.js",
    "src/components/ChatProgress/index.jsx",
    "src/components/ProgressList/index.jsx",
    "src/utils/events.js",
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
