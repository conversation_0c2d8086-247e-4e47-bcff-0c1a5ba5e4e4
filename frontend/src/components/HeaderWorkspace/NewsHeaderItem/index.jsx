import React, { useState, useEffect, useMemo } from "react";
import { IoMegaphoneOutline } from "react-icons/io5";
import { useNews } from "@/hooks/useNews";
import NewsListModal from "./NewsListModal";
import { Button } from "@/components/Button";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";

export default function NewsHeaderItem() {
  const { t } = useTranslation();
  const location = useLocation();
  const [showModal, setShowModal] = useState(false);
  const { allActiveNews, fetchAllActiveNews } = useNews();

  // Filter out dismissed news for badge count
  const undismissedNews = useMemo(() => {
    return allActiveNews.filter((news) => !news.isDismissed);
  }, [allActiveNews]);

  const hasUndismissedNews = undismissedNews.length > 0;

  // Fetch news on all pages to ensure badge shows correct count everywhere
  useEffect(() => {
    fetchAllActiveNews();
  }, [fetchAllActiveNews]);

  const handleClick = () => {
    setShowModal(true);
    // Always refresh news when opening modal to get latest data
    fetchAllActiveNews();
  };

  // Always render the news button with badge on all pages
  return (
    <>
      <Button
        onClick={handleClick}
        variant="outline"
        className="relative w-full md:w-auto"
      >
        <IoMegaphoneOutline />
        {t("news.header.buttonText")}
        {hasUndismissedNews && (
          <svg
            className="w-5 h-5 absolute -top-2 -right-2 border-2 border-white bg-white text-primary rounded-full"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="10" cy="10" r="8" fill="currentColor" />
          </svg>
        )}
      </Button>

      <NewsListModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        news={allActiveNews}
        onNewsUpdate={fetchAllActiveNews}
      />
    </>
  );
}
