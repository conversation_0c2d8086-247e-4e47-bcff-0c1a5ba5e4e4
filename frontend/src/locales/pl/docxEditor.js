export default {
  // =========================
  // DOCX EDITOR
  // =========================
  "docx-edit": {
    "edit-instructions":
      "W<PERSON><PERSON><PERSON><PERSON> instrukcje, jak chcesz edytować dokument. Bądź konkretny co do zmian, które chcesz wprowadzić.",
    "instructions-placeholder":
      "np. Popraw błędy gramatyczne, nadaj bardziej formalny ton, dodaj akapit podsumowujący...",
    "process-button": "Przetwórz dokument",
    "upload-docx": "Prześlij DOCX",
    "processing-upload": "Przetwarzanie...",
    "content-extracted": "Zawartość wyodrębniona z pliku DOCX",
    "file-type-note": "Obsługiwane są tylko pliki .docx",
    "upload-error": "Bł<PERSON>d podczas przesyłania pliku: ",
    "no-instructions": "Wprow<PERSON>ź instrukcje edycji",
    "process-error": "<PERSON>łąd podczas przetwarzania dokumentu: ",
    "changes-highlighted": "Dokument z podświetlonymi zmianami",
    "download-button": "Pobierz dokument",
    "start-over-button": "Zacznij od nowa",
    "no-document": "Brak dokumentu dostępnego do pobrania",
    "download-error": "Błąd podczas pobierania dokumentu: ",
    "download-success": "Dokument pobrany pomyślnie",
    processing: "Przetwarzanie dokumentu...",
    "instructions-used": "Użyte instrukcje",
    "import-success": "Zawartość DOCX zaimportowana pomyślnie",
    "edit-success": "Zawartość DOCX zaktualizowana pomyślnie",
    "canvas-document-title": "Dokument Canvas",
    "upload-button": "Prześlij DOCX",
    "download-as-docx": "Pobierz jako DOCX",
    "output-example": "Przykład wyjścia",
    "output-example-desc":
      "Prześlij plik DOCX, aby dodać przykładową treść do swojego promptu",
    "content-examples-tag-open": "<PRZYKŁAD_TREŚCI>",
    "content-examples-tag-close": "</PRZYKŁAD_TREŚCI>",
    "content-examples-info":
      "<INFO>To jest przykład treści, która ma zostać wygenerowana, z podobnego zadania prawnego. Zauważ, że ta przykładowa treść może być znacznie krótsza lub dłuższa niż treść, która ma zostać teraz wygenerowana.</INFO>",
    "contains-example-content": "[Zawiera przykładową treść]",
  },
};
