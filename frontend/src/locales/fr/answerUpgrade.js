export default {
  "answer-upgrade": {
    title: "Améliorer la Réponse",
    subtitle: "Sélectionner une sous-catégorie",
    steps: "Exécution des tâches juridiques",
    planning: "Planification...",
    "cancel-process":
      "Êtes-vous sûr de vouloir annuler ce processus de tâche juridique ?",
    "wait-process":
      " Veuillez patienter pendant que nous préparons le processus.",
    "process-title-one": "Élaboration du plan d'action",
    "process-description-one":
      "Définir la structure, les objectifs et les tâches clés du document pour assurer un processus clair et organisé.",
    "process-title-two": "Élaboration des étapes d'action",
    "process-description-two":
      "Exécuter les tâches du plan, générer du contenu et des sections en fonction du contexte et de l'objectif.",
    "process-title-three": "Élaboration du résultat final",
    "process-description-three":
      "Combiner tout le contenu, affiner pour plus de précision et livrer un document finalisé prêt à l'emploi.",
    "category-step": {
      title: "Sélectionner une Catégorie",
      description:
        "Choisissez une catégorie qui correspond le mieux à vos besoins",
      categories: {
        formality: {
          label: "Formalité",
          choices: {
            more_formal: "Rendre plus formel",
            less_formal: "Rendre moins formel",
            more_professional: "Rendre plus professionnel",
            more_casual: "Rendre plus décontracté",
            more_polished: "Rendre plus raffiné",
            more_relaxed: "Rendre plus détendu",
            academic_tone: "Utiliser un ton plus académique",
            conversational_tone: "Utiliser un ton plus conversationnel",
          },
        },
        complexity: {
          label: "Complexité du Langage",
          choices: {
            simplify: "Simplifier le langage",
            more_descriptive: "Ajouter plus de descriptions",
            complex_vocab: "Utiliser un vocabulaire plus complexe",
            simple_vocab: "Utiliser un vocabulaire plus simple",
            technical: "Augmenter le langage technique",
            layman: "Utiliser des termes plus simples",
            add_jargon: "Incorporer du jargon spécifique",
            avoid_jargon: "Éviter le jargon",
            add_rhetorical: "Ajouter des questions rhétoriques",
            less_rhetorical: "Utiliser moins de questions rhétoriques",
          },
        },
        structure: {
          label: "Structure des Phrases",
          choices: {
            shorter: "Raccourcir les phrases",
            longer: "Allonger les phrases",
            vary: "Varier la structure des phrases",
            standardize: "Standardiser la structure des phrases",
            more_complex: "Utiliser des phrases plus complexes",
            simpler: "Utiliser des phrases plus simples",
            active_voice: "Augmenter l'utilisation de la voix active",
            passive_voice: "Augmenter l'utilisation de la voix passive",
          },
        },
        figurative: {
          label: "Langage Figuré",
          choices: {
            more_figurative: "Utiliser plus de langage figuré",
            less_figurative: "Réduire le langage figuré",
            metaphors: "Ajouter des métaphores et comparaisons",
            literal: "Utiliser un langage plus littéral",
            more_idioms: "Incorporer plus d'expressions idiomatiques",
            less_idioms: "Réduire les expressions idiomatiques",
            more_symbolism: "Renforcer l'utilisation du symbolisme",
            less_symbolism: "Réduire le symbolisme",
          },
        },
        conciseness: {
          label: "Concision",
          choices: {
            more_concise: "Rendre plus concis",
            more_wordy: "Rendre plus détaillé",
            remove_redundant: "Éliminer les redondances",
            add_details: "Ajouter des détails",
            reduce_filler: "Réduire les mots inutiles",
            add_elaboration: "Ajouter des précisions",
          },
        },
        imagery: {
          label: "Imagerie et Détails Sensoriels",
          choices: {
            enhance_imagery: "Améliorer l'imagerie",
            simplify_imagery: "Simplifier l'imagerie",
            vivid_descriptions: "Utiliser des descriptions vivantes",
            straightforward_descriptions: "Utiliser des descriptions directes",
            more_visual: "Incorporer plus de détails visuels",
            less_visual: "Réduire les détails visuels",
          },
        },
        paragraph: {
          label: "Structure des Paragraphes",
          choices: {
            shorter_paragraphs: "Raccourcir les paragraphes",
            longer_paragraphs: "Allonger les paragraphes",
            break_sections: "Diviser le texte en sections",
            combine_sections: "Combiner des sections",
            more_lists: "Utiliser des listes à puces",
            more_continuous: "Utiliser un texte continu",
            vary_paragraphs: "Varier la longueur des paragraphes",
            consistent_length: "Maintenir une longueur uniforme",
          },
        },
        content_length_legal_memo: {
          label: "Contenu et longueur, mémo juridique",
          choices: {
            extend_memo: "Rendre le mémo plus complet",
            summarize_memo: "Rendre le mémo plus concis",
            expand_analysis: "Étendre l'analyse juridique",
            deepen_case_law: "Ajouter plus de références jurisprudentielles",
            add_statutory_references: "Ajouter plus de références législatives",
            add_conclusion: "Renforcer la conclusion",
            add_recommendations: "Ajouter des recommandations pratiques",
            add_risk_assessment: "Inclure une évaluation des risques",
            add_executive_summary: "Ajouter un résumé exécutif",
          },
        },
        content_length_legal_document: {
          label: "Contenu et longueur, document juridique",
          choices: {
            extend_document: "Rendre le document plus complet",
            shorten_document: "Rendre le document plus concis",
            add_clauses: "Ajouter plus de clauses protectrices",
            simplify_clauses: "Simplifier les clauses complexes",
            add_definitions: "Ajouter plus de termes définis",
            expand_scope: "Étendre la section de portée",
            strengthen_warranties: "Renforcer les garanties et déclarations",
            enhance_remedies: "Améliorer la section des recours",
            add_boilerplate: "Ajouter des clauses standard",
            add_schedules: "Ajouter ou étendre les annexes",
          },
        },
        other: {
          label: "Autres Aspects",
          choices: {
            replace_context:
              "Remplacer les références CONTEXT par le nom réel de la source",
            add_numbering: "Ajouter la numérotation des paragraphes",
            remove_numbering: "Supprimer la numérotation des paragraphes",
            extend_statutories: "Allonger le texte sur les statuts",
            reduce_statutories: "Raccourcir le texte sur les statuts",
            extend_jurisprudence: "Allonger le texte sur la jurisprudence",
            reduce_jurisprudence: "Raccourcir le texte sur la jurisprudence",
          },
        },
      },
    },
    "prompt-step": {
      title: "Sélectionner un Prompt",
      description: "Choisissez comment vous souhaitez améliorer la réponse",
    },
    actions: {
      next: "Suivant",
      back: "Retour",
      upgrade: "Améliorer la réponse",
      cancel: "Annuler",
    },
    "text-upgrade-prompt":
      "Améliorez le prompt original pour l'adapter à une sollicitation juridique professionnelle. Le texte à améliorer est : {{prompt}}",
  },
};
