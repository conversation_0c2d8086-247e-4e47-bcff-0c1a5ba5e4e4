export default {
  // =========================
  // DOCX EDITOR BUTTON
  // =========================
  "docx-edit": {
    "edit-instructions":
      "Enter instructions for how you want to edit the document. Be specific about what changes you want to make.",
    "instructions-placeholder":
      "e.g., Fix grammatical errors, make the tone more formal, add a conclusion paragraph...",
    "process-button": "Process Document",
    "upload-docx": "Upload DOCX",
    "processing-upload": "Processing...",
    "content-extracted": "Content extracted from DOCX file",
    "file-type-note": "Only .docx files are supported",
    "upload-error": "Error uploading file: ",
    "no-instructions": "Please enter editing instructions",
    "process-error": "Error processing document: ",
    "changes-highlighted": "Document with highlighted changes",
    "download-button": "Download Document",
    "start-over-button": "Start Over",
    "no-document": "No document available for download",
    "download-error": "Error downloading document: ",
    "download-success": "Document downloaded successfully",
    processing: "Processing document...",
    "instructions-used": "Instructions Used",
    "import-success": "DOCX content imported successfully",
    "edit-success": "DOCX content updated successfully",
    "canvas-document-title": "Canvas Document",
    "upload-button": "Upload DOCX",
    "download-as-docx": "Download as DOCX",
    "output-example": "Output example",
    "output-example-desc":
      "Upload a DOCX file to add example output content to your prompt",
    "content-examples-tag-open": "<CONTENT_EXAMPLE>",
    "content-examples-tag-close": "</CONTENT_EXAMPLE>",
    "content-examples-info":
      "<INFO>This is an example of the content to be produced, from a similar legal task. Note that this example content can be much shorter or longer than the content that shall now be produced.</INFO>",
    "contains-example-content": "[Contains example content]",
  },
};
