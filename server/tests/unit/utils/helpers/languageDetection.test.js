const {
  getPreferredLanguage,
  getSystemLanguage,
  getLocalizedDescription,
  isLanguageSupported,
  SUPPORTED_LANGUAGES,
  DEFAULT_LANGUAGE,
} = require("../../../../utils/helpers/languageDetection");

// Mock dependencies
jest.mock("../../../../utils/http", () => ({
  userFromSession: jest.fn(),
}));

jest.mock("../../../../models/systemSettings", () => ({
  SystemSettings: {
    get: jest.fn(),
  },
}));

const { userFromSession } = require("../../../../utils/http");
const { SystemSettings } = require("../../../../models/systemSettings");

describe("Language Detection Utilities", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getSystemLanguage", () => {
    it("should return system language when found and supported", async () => {
      SystemSettings.get.mockResolvedValue({ value: "sv" });

      const result = await getSystemLanguage();

      expect(result).toBe("sv");
      expect(SystemSettings.get).toHaveBeenCalledWith({ label: "language" });
    });

    it("should return default language when system language is unsupported", async () => {
      SystemSettings.get.mockResolvedValue({ value: "unsupported" });

      const result = await getSystemLanguage();

      expect(result).toBe(DEFAULT_LANGUAGE);
    });

    it("should return default language when no system setting found", async () => {
      SystemSettings.get.mockResolvedValue(null);

      const result = await getSystemLanguage();

      expect(result).toBe(DEFAULT_LANGUAGE);
    });

    it("should return default language when SystemSettings throws error", async () => {
      SystemSettings.get.mockRejectedValue(new Error("Database error"));

      const result = await getSystemLanguage();

      expect(result).toBe(DEFAULT_LANGUAGE);
    });
  });

  describe("getPreferredLanguage", () => {
    it("should return system language when no user found", async () => {
      userFromSession.mockResolvedValue(null);
      SystemSettings.get.mockResolvedValue({ value: "fr" });

      const mockRequest = {};
      const mockResponse = {};

      const result = await getPreferredLanguage(mockRequest, mockResponse);

      expect(result).toBe("fr");
      expect(userFromSession).toHaveBeenCalledWith(mockRequest, mockResponse);
    });

    it("should return system language when user found but no user language preference", async () => {
      userFromSession.mockResolvedValue({ id: 1, username: "test" });
      SystemSettings.get.mockResolvedValue({ value: "de" });

      const mockRequest = {};
      const mockResponse = {};

      const result = await getPreferredLanguage(mockRequest, mockResponse);

      expect(result).toBe("de");
    });

    it("should return default language when error occurs", async () => {
      userFromSession.mockRejectedValue(new Error("Auth error"));

      const mockRequest = {};
      const mockResponse = {};

      const result = await getPreferredLanguage(mockRequest, mockResponse);

      expect(result).toBe(DEFAULT_LANGUAGE);
    });
  });

  describe("getLocalizedDescription", () => {
    const mockVersionObj = {
      version: "1.1.0",
      description: "Default description",
      "description-sv": "Svensk beskrivning",
      "description-no": "Norsk beskrivelse",
      "description-fr": "Description française",
      timestamp: "2025-01-01T10:00:00Z",
    };

    it("should return localized description when available", () => {
      const result = getLocalizedDescription(mockVersionObj, "sv");
      expect(result).toBe("Svensk beskrivning");
    });

    it("should return localized description for French", () => {
      const result = getLocalizedDescription(mockVersionObj, "fr");
      expect(result).toBe("Description française");
    });

    it("should fall back to default description when localized not available", () => {
      const result = getLocalizedDescription(mockVersionObj, "pl");
      expect(result).toBe("Default description");
    });

    it("should fall back to default description for English", () => {
      const result = getLocalizedDescription(mockVersionObj, "en");
      expect(result).toBe("Default description");
    });

    it("should return default description when language is undefined", () => {
      const result = getLocalizedDescription(mockVersionObj);
      expect(result).toBe("Default description");
    });

    it("should return fallback message when no description available", () => {
      const versionObjNoDesc = {
        version: "1.0.0",
        timestamp: "2025-01-01T10:00:00Z",
      };

      const result = getLocalizedDescription(versionObjNoDesc, "sv");
      expect(result).toBe("Version information available");
    });

    it("should return empty string when version object is null", () => {
      const result = getLocalizedDescription(null, "sv");
      expect(result).toBe("");
    });

    it("should return empty string when version object is undefined", () => {
      const result = getLocalizedDescription(undefined, "sv");
      expect(result).toBe("");
    });
  });

  describe("isLanguageSupported", () => {
    it("should return true for supported languages", () => {
      SUPPORTED_LANGUAGES.forEach((lang) => {
        expect(isLanguageSupported(lang)).toBe(true);
      });
    });

    it("should return false for unsupported languages", () => {
      expect(isLanguageSupported("unsupported")).toBe(false);
      expect(isLanguageSupported("xx")).toBe(false);
      expect(isLanguageSupported("")).toBe(false);
    });

    it("should return false for null or undefined", () => {
      expect(isLanguageSupported(null)).toBe(false);
      expect(isLanguageSupported(undefined)).toBe(false);
    });
  });

  describe("Constants", () => {
    it("should have correct supported languages", () => {
      const expectedLanguages = ["en", "fr", "de", "sv", "rw", "no", "pl"];
      expect(SUPPORTED_LANGUAGES).toEqual(expectedLanguages);
    });

    it("should have correct default language", () => {
      expect(DEFAULT_LANGUAGE).toBe("en");
    });
  });
});
