export default {
  // =========================
  // DATA CONNECTORS
  // =========================
  dataConnectors: {
    github: {
      name: "GitHub Repo",
      description:
        "Import an entire public or private Github repository in a single click.",
      url: "GitHub Repo URL",
      "collect-url": "Url of the GitHub repo you wish to collect.",
      "access-token": "Github Access Token",
      optional: "optional",
      "rate-limiting": "Access Token to prevent rate limiting.",
      "desc-picker":
        "Once complete, all files will be available for embedding into workspaces in the document picker.",
      branch: "Branch",
      "branch-desc": "Branch you wish to collect files from.",
      "branch-loading": "-- loading available branches --",
      "desc-start": "Without filling out the",
      "desc-token": "Github Access Token",
      "desc-connector": "this data connector will only be able to collect the",
      "desc-level": "top-level",
      "desc-end": "files of the repo due to GitHub's public API rate-limits.",
      "personal-token":
        "Get a free Personal Access Token with a GitHub account here.",
      without: "Without a",
      "personal-token-access": "Personal Access Token",
      "desc-api":
        ", the GitHub API may limit the number of files that can be collected due to rate limits. You can",
      "temp-token": "create a temporary Access Token",
      "avoid-issue": "to avoid this issue.",
      submit: "Submit",
      "collecting-files": "Collecting files...",
    },
    "youtube-transcript": {
      name: "YouTube Transcript",
      description:
        "Import the transcription of an entire YouTube video from a link.",
      url: "YouTube Video URL",
      "url-video": "URL of the YouTube video you wish to transcribe.",
      collect: "Collect transcript",
      collecting: "Collecting transcript...",
      "desc-end":
        "once complete, the transcription will be available for embedding into workspaces in the document picker.",
    },
    "website-depth": {
      name: "Bulk Link Scraper",
      description: "Scrape a website and its sub-links up to a certain depth.",
      url: "Website URL",
      "url-scrape": "URL of the website you want to scrape.",
      depth: "Depth",
      "child-links":
        "This is the number of child-links that the worker should follow from the origin URL.",
      "max-links": "Max Links",
      "links-scrape": "Maximum number of links to scrape.",
      scraping: "Scraping website...",
      submit: "Submit",
      "desc-scrap":
        "Once complete, all scraped pages will be available for embedding into workspaces in the document picker.",
    },
    confluence: {
      name: "Confluence",
      description: "Import an entire Confluence page in a single click.",
      url: "Confluence Page URL",
      "url-page": "URL of a page in the Confluence space.",
      username: "Confluence Username",
      "own-username": "Your Confluence username.",
      token: "Confluence Access Token",
      "desc-start":
        "You need to provide an access token for authentication. You can generate an access token",
      here: "here",
      access: "Access token for authentication.",
      collecting: "Collecting pages...",
      submit: "Submit",
      "desc-end":
        "Once complete, all pages will be available for embedding into workspaces.",
    },
  },
  // =========================
  // CONFLUENCE CONNECTOR COMPONENT
  // =========================
  confluence: {
    "space-key": "Confluence space key",
    "space-key-desc":
      "This is the spaces key of your confluence instance that will be used. Usually begins with ~",
    "space-key-placeholder": "eg: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "eg: https://example.atlassian.net, http://localhost:8211, etc...",
    "token-tooltip": "You can create an API token",
    "token-tooltip-here": "here",
  },
};
