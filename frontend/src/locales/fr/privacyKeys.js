export default {
  // ----------------------------
  // Privacy & Data Handling
  // ----------------------------
  privacy: {
    title: "Confidentialité & Gestion des données",
    description:
      "Voici la configuration de la manière dont les fournisseurs tiers et notre plateforme gèrent vos données.",
    llm: "Sélection LLM",
    embedding: "Préférence d'intégration",
    vector: "Base de données vectorielle",
    anonymous: "Télémétrie anonyme activée",
    "desc-event":
      "Les événements n'enregistrent pas l'adresse IP et ne contiennent aucune",
    "desc-id": "information identifiante",
    "desc-cont":
      "données, paramètres, chats ou autres informations non liées à l'utilisation. Pour consulter la liste des balises d'événements collectées, consultez",
    "desc-git": "Github ici",
    "desc-end":
      "Si vous décidez de désactiver la télémétrie, merci de nous faire part de vos retours afin que nous puissions améliorer la plateforme.",
  },
  // =========================
  // LLM SELECTION PRIVACY
  // =========================

  "llm-selection-privacy": {
    openai: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par OpenAI",
      ],
    },
    azure: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Votre texte et le texte d'intégration ne sont pas visibles par OpenAI ou Microsoft",
      ],
    },
    anthropic: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par Anthropic",
      ],
    },
    gemini: {
      description: [
        "Vos conversations sont anonymisées et utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par Google",
      ],
    },
    lmstudio: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur le serveur exécutant LMStudio",
      ],
    },
    localai: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur le serveur exécutant LocalAI",
      ],
    },
    ollama: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur la machine exécutant les modèles Ollama",
      ],
    },
    native: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur cette instance de la plateforme",
      ],
    },
    togetherai: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par TogetherAI",
      ],
    },
    mistral: {
      description: [
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par Mistral",
      ],
    },
    huggingface: {
      description: [
        "Vos invites et le texte des documents utilisés pour créer les réponses sont envoyés à votre point de terminaison HuggingFace géré",
      ],
    },
    perplexity: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par Perplexity AI",
      ],
    },
    openrouter: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par OpenRouter",
      ],
    },
    groq: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par Groq",
      ],
    },
    koboldcpp: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur le serveur exécutant KoboldCPP",
      ],
    },
    textgenwebui: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur le serveur exécutant l'interface Text Generation Web UI d'Oobabooga",
      ],
    },
    "generic-openai": {
      description: [
        "Les données sont partagées conformément aux conditions de service de votre fournisseur de point de terminaison générique.",
      ],
    },
    cohere: {
      description: [
        "Les données sont partagées conformément aux conditions de service de cohere.com et aux lois sur la confidentialité locales.",
      ],
    },
    litellm: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur le serveur exécutant LiteLLM",
      ],
    },
  },

  // ----------------------------
  // Vector DB Privacy
  // ----------------------------
  "vector-db-privacy": {
    chroma: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur votre instance Chroma",
        "L'accès à votre instance est géré par vous",
      ],
    },
    pinecone: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur les serveurs de Pinecone",
        "L'accès à vos données est géré par Pinecone",
      ],
    },
    qdrant: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur votre instance Qdrant (cloud ou auto-hébergée)",
      ],
    },
    weaviate: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur votre instance Weaviate (cloud ou auto-hébergée)",
      ],
    },
    milvus: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur votre instance Milvus (cloud ou auto-hébergée)",
      ],
    },
    zilliz: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur votre cluster cloud Zilliz.",
      ],
    },
    astra: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur votre base de données cloud AstraDB.",
      ],
    },
    lancedb: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés de manière privée sur cette instance de la plateforme",
      ],
    },
  },

  // ----------------------------
  // Embedding Engine Privacy
  // ----------------------------
  "embedding-engine-privacy": {
    native: {
      description: [
        "Le texte de vos documents est intégré de manière privée sur cette instance de la plateforme",
      ],
    },
    openai: {
      description: [
        "Le texte de vos documents est envoyé aux serveurs d'OpenAI",
        "Vos documents ne sont pas utilisés pour l'entraînement",
      ],
    },
    azure: {
      description: [
        "Le texte de vos documents est envoyé à votre service Microsoft Azure",
        "Vos documents ne sont pas utilisés pour l'entraînement",
      ],
    },
    localai: {
      description: [
        "Le texte de vos documents est intégré de manière privée sur le serveur exécutant LocalAI",
      ],
    },
    ollama: {
      description: [
        "Le texte de vos documents est intégré de manière privée sur le serveur exécutant Ollama",
      ],
    },
    lmstudio: {
      description: [
        "Le texte de vos documents est intégré de manière privée sur le serveur exécutant LMStudio",
      ],
    },
    cohere: {
      description: [
        "Les données sont partagées conformément aux conditions de service de cohere.com et aux lois locales sur la confidentialité.",
      ],
    },
    voyageai: {
      description: [
        "Les données envoyées aux serveurs de Voyage AI sont partagées conformément aux conditions de service de voyageai.com.",
      ],
    },
  },
};
