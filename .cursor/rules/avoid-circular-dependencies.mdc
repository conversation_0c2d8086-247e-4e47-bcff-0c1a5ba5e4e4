---
description: Guidelines for avoiding circular dependencies in the codebase
globs: "frontend/**/*.{js,jsx,ts,tsx}"
alwaysApply: false
---

# Avoiding Circular Dependencies

## Problem

Circular dependencies occur when two or more modules depend on each other, either directly or indirectly. This can lead to:

1. Infinite loops during module resolution
2. Undefined or partially initialized objects
3. Hard-to-debug issues
4. Eternal loading states in UI components

## Common Circular Dependency Patterns

### Direct Circular Dependencies

```javascript
// fileA.js
import { functionB } from './fileB';

export function functionA() {
  return functionB() + 1;
}

// fileB.js
import { functionA } from './fileA';

export function functionB() {
  return functionA() + 1;
}
```

### Indirect Circular Dependencies

```javascript
// fileA.js
import { functionB } from './fileB';

export function functionA() {
  return functionB() + 1;
}

// fileB.js
import { functionC } from './fileC';

export function functionB() {
  return functionC() + 1;
}

// fileC.js
import { functionA } from './fileA';

export function functionC() {
  return functionA() + 1;
}
```

## Best Practices

### 1. Use Direct API Calls Instead of Store Imports

When a model needs to interact with a store that also imports the model, make direct API calls instead of importing the store:

```javascript
// GOOD: Direct API call
const submitLegalTask = async (data) => {
  try {
    const response = await fetch(`${API_BASE}/categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...baseHeaders()
      },
      body: JSON.stringify(data)
    });
    
    return await response.json();
  } catch (error) {
    console.error("Error:", error);
    return { success: false, error: error.message };
  }
};

// BAD: Circular dependency
const submitLegalTask = async (data) => {
  try {
    const store = (await import('@/stores/someStore')).default;
    return await store.getState().createTask(data);
  } catch (error) {
    console.error("Error:", error);
    return { success: false, error: error.message };
  }
};
```

### 2. Create Interface Modules

Create a separate module that defines interfaces between components:

```javascript
// interface.js
export const events = {
  TASK_CREATED: 'task_created',
  TASK_UPDATED: 'task_updated'
};

// Both moduleA and moduleB can import from interface.js
// without creating a circular dependency
```

### 3. Use Dependency Injection

Pass dependencies as parameters instead of importing them directly:

```javascript
// GOOD: Dependency injection
export function createService(dependencies) {
  return {
    doSomething: () => {
      return dependencies.otherService.doSomethingElse();
    }
  };
}

// BAD: Direct import creating potential circular dependency
import { otherService } from './otherService';

export function createService() {
  return {
    doSomething: () => {
      return otherService.doSomethingElse();
    }
  };
}
```

### 4. Restructure Your Code

Sometimes the best solution is to restructure your code to eliminate the need for circular dependencies:

- Move shared functionality to a common module
- Split large modules into smaller, more focused ones
- Use a mediator pattern to coordinate between modules

## Specific Guidelines for ISTLegal

1. **Model-Store Interactions**: When a model needs to interact with a store, make direct API calls instead of importing the store.

2. **Store Actions**: Keep store actions focused on state management and delegate API calls to model functions.

3. **Shared Utilities**: Place shared utilities in a separate module that can be imported by both models and stores.

4. **Event-Based Communication**: Use event-based communication for complex interactions between modules.

## Detecting Circular Dependencies

You can use tools like `madge` to detect circular dependencies in your codebase:

```bash
npx madge --circular --extensions js,jsx frontend/src
```

## Example Fix for Legal Tasks

The legal tasks functionality had circular dependencies between `System.js` and `legalTasksStore.js`. The fix involved:

1. Modifying `System.submitLegalTask` to make direct API calls instead of importing the store
2. Modifying `System.updateLegalTask` to make direct API calls instead of importing the store
3. Modifying `System.deleteCategory` to make direct API calls instead of importing the store

This eliminated the circular dependencies and fixed the eternal loading state when saving legal tasks.
