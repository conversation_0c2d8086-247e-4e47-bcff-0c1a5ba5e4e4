import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import Label from "@/components/ui/Label";
import Input from "@/components/ui/Input";
import Textarea from "@/components/ui/Textarea";
import FormItem from "@/components/ui/FormItem";
import { Button } from "@/components/Button";

const formSchema = z.object({
  name: z
    .string()
    .min(1, "prompt-library.name-required")
    .max(100, "prompt-library.name-too-long"),
  description: z
    .string()
    .max(500, "prompt-library.description-too-long")
    .optional(),
  prompt_text: z
    .string()
    .min(1, "prompt-library.prompt-text-required")
    .max(10000, "prompt-library.prompt-text-too-long"),
});

export default function PromptLibraryForm({
  prompt = null,
  currentPrompt = "",
  onSubmit,
  onCancel,
}) {
  const { t } = useTranslation();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: prompt?.name || "",
      description: prompt?.description || "",
      prompt_text: prompt?.prompt_text || currentPrompt,
    },
  });

  // Watch form values for character counting
  const watchedName = watch("name");
  const watchedDescription = watch("description");
  const watchedPromptText = watch("prompt_text");

  const handleFormSubmit = (data) => {
    onSubmit(data);
  };

  const isEditing = !!prompt;

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      {/* Name field */}
      <FormItem>
        <Label htmlFor="name">{t("prompt-library.name-label")}</Label>
        <Input
          id="name"
          {...register("name")}
          placeholder={t("prompt-library.name-placeholder")}
        />
        {errors.name && (
          <p className="text-red-500 text-sm">{t(errors.name.message)}</p>
        )}
        <p className="text-xs text-muted-foreground">
          {t("prompt-library.character-count", {
            count: watchedName?.length || 0,
            max: 100,
          })}
        </p>
      </FormItem>

      {/* Description field */}
      <FormItem>
        <Label htmlFor="description">
          {t("prompt-library.description-label")}
        </Label>
        <Input
          id="description"
          {...register("description")}
          placeholder={t("prompt-library.description-placeholder")}
        />
        {errors.description && (
          <p className="text-red-500 text-sm">
            {t(errors.description.message)}
          </p>
        )}
        <p className="text-xs text-muted-foreground">
          {t("prompt-library.character-count", {
            count: watchedDescription?.length || 0,
            max: 500,
          })}
        </p>
      </FormItem>

      {/* Prompt text field */}
      <FormItem>
        <Label htmlFor="prompt_text">
          {t("prompt-library.prompt-text-label")}
        </Label>
        <Textarea
          id="prompt_text"
          {...register("prompt_text")}
          placeholder={t("prompt-library.prompt-text-placeholder")}
          rows={8}
          className="resize-none"
        />
        {errors.prompt_text && (
          <p className="text-red-500 text-sm">
            {t(errors.prompt_text.message)}
          </p>
        )}
        <p className="text-xs text-muted-foreground">
          {t("prompt-library.character-count", {
            count: watchedPromptText?.length || 0,
            max: 10000,
          })}
        </p>
      </FormItem>

      {/* Action buttons */}
      <div className="flex justify-end gap-2 pt-4 border-t">
        <Button type="button" variant="outline" onClick={onCancel} size="sm">
          {t("prompt-library.cancel-button")}
        </Button>
        <Button type="submit" size="sm">
          {isEditing
            ? t("prompt-library.update-button")
            : t("prompt-library.save-button")}
        </Button>
      </div>
    </form>
  );
}
