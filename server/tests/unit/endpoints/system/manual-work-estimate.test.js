const request = require("supertest");
const express = require("express");

// Mock SystemSettings
const mockSystemSettings = {
  getManualWorkEstimatorPrompt: jest.fn(),
};

// Create a minimal Express app for testing
const app = express();
app.use(express.json());

// Mock EstimateManualWork function
const mockEstimateManualWork = jest.fn();

// Add the endpoint with mocked function
app.post("/system/manual-work-estimate", async (req, res) => {
  try {
    const { question, answer } = req.body;

    if (!question || !answer) {
      return res.status(400).json({
        success: false,
        error: "Question and answer are required",
      });
    }

    const result = await mockEstimateManualWork(question, answer);

    res.json({
      success: true,
      result,
    });
  } catch (error) {
    console.error("Manual work estimation error:", error);
    res.status(500).json({
      success: false,
      error: error.message || "Failed to estimate manual work",
    });
  }
});

describe("POST /system/manual-work-estimate", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockEstimateManualWork.mockClear();
    mockSystemSettings.getManualWorkEstimatorPrompt.mockClear();
  });

  describe("Success Cases", () => {
    it("should return estimation result when valid input is provided", async () => {
      const mockResult = {
        textResponse:
          "Estimated time: 2-3 hours for document review and analysis.",
        prompt: {
          systemPrompt: "You are an expert legal assistant...",
          userContent:
            "Question: What are the legal implications?\nAnswer: The legal implications include...",
          provider: "OpenAI",
          model: "gpt-4",
        },
      };

      mockEstimateManualWork.mockResolvedValue(mockResult);

      const response = await request(app)
        .post("/system/manual-work-estimate")
        .send({
          question: "What are the legal implications?",
          answer:
            "The legal implications include contract law considerations...",
        });

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        result: mockResult,
      });

      expect(mockEstimateManualWork).toHaveBeenCalledWith(
        "What are the legal implications?",
        "The legal implications include contract law considerations..."
      );
    });

    it("should handle estimation without prompt details", async () => {
      const mockResult = {
        textResponse: "Estimated time: 1-2 hours",
      };

      mockEstimateManualWork.mockResolvedValue(mockResult);

      const response = await request(app)
        .post("/system/manual-work-estimate")
        .send({
          question: "Simple question?",
          answer: "Simple answer.",
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.result.textResponse).toBe(
        "Estimated time: 1-2 hours"
      );
    });
  });

  describe("Validation", () => {
    it("should return 400 when question is missing", async () => {
      const response = await request(app)
        .post("/system/manual-work-estimate")
        .send({
          answer: "Some answer",
        });

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        success: false,
        error: "Question and answer are required",
      });

      expect(mockEstimateManualWork).not.toHaveBeenCalled();
    });

    it("should return 400 when answer is missing", async () => {
      const response = await request(app)
        .post("/system/manual-work-estimate")
        .send({
          question: "Some question?",
        });

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        success: false,
        error: "Question and answer are required",
      });

      expect(mockEstimateManualWork).not.toHaveBeenCalled();
    });

    it("should return 400 when both question and answer are missing", async () => {
      const response = await request(app)
        .post("/system/manual-work-estimate")
        .send({});

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        success: false,
        error: "Question and answer are required",
      });

      expect(mockEstimateManualWork).not.toHaveBeenCalled();
    });
  });

  describe("Error Handling", () => {
    it("should return 500 when EstimateManualWork throws an error", async () => {
      const mockError = new Error("LLM service unavailable");
      mockEstimateManualWork.mockRejectedValue(mockError);

      const response = await request(app)
        .post("/system/manual-work-estimate")
        .send({
          question: "What are the legal implications?",
          answer: "The legal implications include...",
        });

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        error: "LLM service unavailable",
      });

      expect(mockEstimateManualWork).toHaveBeenCalled();
    });

    it("should handle errors without message", async () => {
      mockEstimateManualWork.mockRejectedValue(new Error());

      const response = await request(app)
        .post("/system/manual-work-estimate")
        .send({
          question: "Test question?",
          answer: "Test answer.",
        });

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        error: "Failed to estimate manual work",
      });
    });
  });
});
