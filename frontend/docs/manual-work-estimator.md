# Manual Work Estimator Feature

## Overview

The Manual Work Estimator is a feature that allows managers and administrators to estimate the amount of manual work time required to produce AI-generated responses in the Legal Q&A module. This helps with billing, project planning, and understanding the value provided by the AI system.

## Features

- **Time Estimation**: Uses the system LLM to estimate manual work hours for AI responses
- **Role-Based Access**: Available to manager and admin level accounts only
- **Prompt Transparency**: Admin users can view the prompts used for estimation
- **Modal Display**: Results are displayed in a user-friendly modal interface
- **Internationalization**: Supports multiple languages (English, Swedish, French, German, Norwegian, Polish, Kinyarwanda)

## User Access Levels

### Manager Users
- Can access the "Time estimate" button
- Can view estimation results
- **Cannot** view prompt details used for estimation

### Admin Users
- Can access the "Time estimate" button
- Can view estimation results
- **Can** view prompt details used for estimation (system prompt, user content, provider info)

### Regular Users
- No access to the Manual Work Estimator feature

## How It Works

1. **Trigger**: The "Time estimate" button appears on assistant messages in the Legal Q&A module for eligible users
2. **Input**: The feature uses the latest user question and AI response as input
3. **Processing**: Sends the question and answer to the system LLM with a specialized prompt
4. **Output**: Displays the estimated time in a modal with optional prompt details

## Technical Implementation

### Frontend Components

#### ManualWorkEstimator Component
- **Location**: `frontend/src/components/WorkspaceChat/ChatContainer/ChatHistory/HistoricalMessage/Actions/ManualWorkEstimator/`
- **Props**:
  - `question` (string): The user's question
  - `answer` (string): The AI's response
  - `user` (object): Current user object with role information

#### Integration
- **Location**: `frontend/src/components/WorkspaceChat/ChatContainer/ChatHistory/HistoricalMessage/Actions/index.jsx`
- **Condition**: `role === "assistant" && isLegalQA && (user?.role === "admin" || user?.role === "manager")`

### Backend API

#### Endpoint
- **URL**: `POST /system/manual-work-estimate`
- **Location**: `server/endpoints/system.js`
- **Authentication**: Required (inherits from system endpoint security)

#### Request Format
```json
{
  "question": "What are the legal implications of this contract?",
  "answer": "The legal implications include..."
}
```

#### Response Format
```json
{
  "success": true,
  "result": {
    "textResponse": "Estimated time: 2-3 hours for document review and analysis.",
    "prompt": {
      "systemPrompt": "You are an expert legal assistant...",
      "userContent": "Question: ...\nAnswer: ...",
      "provider": "OpenAI",
      "model": "gpt-4"
    }
  }
}
```

### Core Function
- **Location**: `server/utils/chats/helpers/EstimateManualWork.js`
- **Purpose**: Handles the LLM interaction for time estimation
- **Prompt**: Uses a hardcoded prompt optimized for legal work estimation

## Internationalization

The feature supports multiple languages with translations in:
- `frontend/src/locales/[lang]/common.js` - Main UI text
- `frontend/src/locales/[lang]/showToast.js` - Error messages

### Translation Keys
```javascript
"manual-work-estimator": {
  title: "Manual Work Estimation",
  button: "Time estimate", // Short button text
  "show-prompt": "Show prompt",
  "hide-prompt": "Hide prompt",
  "prompt-title": "Prompt used for estimation",
  "system-prompt": "System prompt",
  "user-content": "User content",
  "provider-info": "Provider information",
  model: "Model",
  provider: "Provider"
}
```

## Usage Examples

### For Managers
1. Navigate to Legal Q&A module
2. Ask a legal question and receive an AI response
3. Click the "Time estimate" button on the AI response
4. View the estimated time in the modal
5. Close the modal when done

### For Admins
1. Follow the same steps as managers
2. Additionally, click "Show prompt" to view:
   - System prompt used
   - User content sent to LLM
   - Provider and model information
3. Use this information for prompt optimization or debugging

## Error Handling

- **Network Errors**: Displays toast notification with error message
- **Invalid Input**: Server validates required fields
- **LLM Failures**: Gracefully handles LLM service unavailability
- **Permission Errors**: Feature is hidden for unauthorized users

## Testing

### Frontend Tests
- **Location**: `frontend/src/components/WorkspaceChat/ChatContainer/ChatHistory/HistoricalMessage/Actions/ManualWorkEstimator/__tests__/`
- **Coverage**: Component rendering, role-based access, API calls, error handling

### Backend Tests
- **Location**: `server/tests/unit/endpoints/system/manual-work-estimate.test.js`
- **Coverage**: API validation, success cases, error handling

### Running Tests
```bash
# Frontend tests
cd frontend
npm test ManualWorkEstimator

# Backend tests
cd server
npm test manual-work-estimate
```

## Configuration

### System Requirements
- System LLM must be configured and available
- User authentication system must be active
- Legal Q&A module must be enabled

### Customization
- Estimation prompt can be modified in `EstimateManualWork.js`
- UI text can be customized via translation files
- Access control can be adjusted in the Actions component

## Troubleshooting

### Common Issues

1. **Button Not Visible**
   - Check user role (must be manager or admin)
   - Verify in Legal Q&A module
   - Ensure viewing assistant message

2. **Estimation Fails**
   - Check system LLM configuration
   - Verify network connectivity
   - Check server logs for detailed errors

3. **Prompt Details Not Showing**
   - Feature is admin-only
   - Check user role permissions

### Debug Information
- Check browser console for frontend errors
- Check server logs for backend errors
- Verify API endpoint accessibility

## Future Enhancements

Potential improvements could include:
- Configurable estimation prompts via admin interface
- Historical estimation data and analytics
- Integration with billing systems
- Batch estimation for multiple responses
- Custom time estimation models per legal area
