# Git Handling Instructions

## Type: Always

## Description
This rule provides guidelines for handling git operations in the ISTLegal project. It ensures that the AI assistant follows proper git workflow practices and respects user review processes.

## Git Operation Guidelines

### General Principles

1. **Never commit directly to protected branches**
   - Even if git commands are available, never commit directly to `develop`, `main`, or any other protected branch
   - Instead, do file changes for the user to review, stage, and commit themselves
   - Always provide clear instructions on what changes have been made and what the user needs to do next

2. **File Changes vs. Direct Commits**
   - Always prefer adding file changes for user review over automatic commits
   - When making changes to multiple files, change them individually with clear explanations
   - Provide a suggested commit message for the user to use

3. **Branch Management**
   - Never create or switch branches without explicit user permission
   - When suggesting branch operations, provide the exact commands for the user to execute
   - Do not push to remote repositories without explicit user confirmation

4. **Pull Requests**
   - Do not create pull requests directly
   - Provide instructions for the user to create pull requests when needed
   - Include suggested PR title, description, and reviewers in your instructions

### Specific Instructions

#### When Adding Changes

```
# DO THIS
- Make file changes using appropriate tools (str-replace-editor, save-file)
- Explain what changes were made and why
- Let the user review, stage, and commit the changes

# DON'T DO THIS
- Use git commands to automatically commit changes
- Push changes to remote repositories
- Create branches or pull requests without explicit permission
```

#### When Suggesting Commits

```
# DO THIS
git add <specific-files>
# Then suggest a commit message like:
git commit -m "Fix button component disabled state handling"

# DON'T DO THIS
git commit -am "Fixed some issues"  # Too vague and commits all changes
git push origin branch-name  # Never push without explicit permission
```

#### When Working with Branches

```
# DO THIS
# Suggest commands for the user to execute:
git checkout -b feature/new-modal-component
git checkout develop

# DON'T DO THIS
# Execute these commands directly:
git checkout -b feature/new-modal-component
git checkout develop
```

### Error Recovery

If you accidentally commit changes or perform git operations that should have been left to the user:

1. Inform the user immediately about what happened
2. Provide instructions on how to undo the operation if possible
3. Apologize and ensure it doesn't happen again

## Examples

### Good Example

```
I've made the following changes to fix the button component:
1. Updated the Button component to properly handle disabled state
2. Added proper type checking for the disabled prop
3. Fixed the styling for disabled buttons

These changes are now ready for you to review. You can:
1. Review the changes in the files
2. Stage them with: git add frontend/src/components/Button/index.jsx
3. Commit them with: git commit -m "Fix Button component disabled state handling"
```

### Bad Example

```
I've fixed the button component and committed the changes to develop.
The changes are now pushed to the remote repository.
```

## Remember

Always prioritize user control and review over automation when it comes to git operations. The user should always have the final say on what gets committed and pushed to the repository.
