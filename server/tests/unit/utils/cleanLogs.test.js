console.log("[CleanLogsTest Top] process.cwd():", process.cwd());
console.log("[CleanLogsTest Top] __dirname:", __dirname);

const originalStorageDirForCleanLogs = process.env.STORAGE_DIR;
process.env.STORAGE_DIR =
  process.env.STORAGE_DIR || "/tmp/mock_storage_cl_test"; // Fallback

const path = require("path");
const { cleanOldLogs } = require("../../../utils/cleanLogs"); // Uncomment SUT import

jest.mock("fs", () => ({
  promises: {
    access: jest.fn(),
    readdir: jest.fn(),
    stat: jest.fn(),
    unlink: jest.fn(),
    readFile: jest.fn(),
    writeFile: jest.fn(),
  },
  existsSync: jest.fn(),
}));

const fs = require("fs"); // Get the mocked fs instance
const {
  cleanOldDocxSessionFiles: mockCleanOldDocxSessionFiles,
} = require("../../../utils/files/docxSessionCleanup");

// Mock the cleanOldDocxSessionFiles function directly from its new location
jest.mock("../../../utils/files/docxSessionCleanup", () => ({
  cleanOldDocxSessionFiles: jest
    .fn()
    .mockResolvedValue({ success: true, deletedCount: 0, error: null }), // Provide a default mockResolvedValue
}));

// We need to get a reference to the mocked function after jest.mock has run.
// const {
//   cleanOldDocxSessionFiles: mockCleanOldDocxSessionFiles,
// } = require("../../../utils/files/docxSessionCleanup"); // No longer needed

// Mock cleanOldUploadLogs as its internal logic is tested separately or is simple
// For this test, we just want to ensure cleanOldLogs calls it.
// This requires cleanOldLogs.js to NOT be in the same file as cleanOldUploadLogs or to structure imports differently.
// Assuming cleanOldUploadLogs might be part of the same module or needs specific mocking:
// We can't directly mock cleanOldUploadLogs if it's in the same file and not exported separately for mocking.
// Let's assume for now it's either simple enough not to mock its internals or tested elsewhere.
// A more robust way would be to spyOn if it were an exported method of an imported module.

afterAll(() => {
  if (originalStorageDirForCleanLogs === undefined) {
    delete process.env.STORAGE_DIR;
  } else {
    process.env.STORAGE_DIR = originalStorageDirForCleanLogs;
  }
});

describe("cleanOldLogs", () => {
  // UN-SKIPPED describe block
  const mockLogsDir = path.resolve(
    __dirname,
    "../../../../server/storage/logs"
  );
  const correctMockLogsDir = path.resolve(
    // Used for path.join in assertions
    __dirname,
    "../../../../server/storage/logs"
  );
  const twoDaysInMs = 2 * 24 * 60 * 60 * 1000;
  const now = Date.now();

  beforeEach(() => {
    fs.promises.access.mockReset();
    fs.promises.readdir.mockReset();
    fs.promises.stat.mockReset();
    fs.promises.unlink.mockReset();
    fs.promises.readFile.mockReset();
    fs.promises.writeFile.mockReset();
    fs.existsSync.mockReset();
    mockCleanOldDocxSessionFiles.mockClear();
    mockCleanOldDocxSessionFiles.mockResolvedValue({
      success: true,
      deletedCount: 0,
      error: null,
    });

    fs.promises.access.mockResolvedValue(undefined);
    fs.promises.readFile.mockResolvedValue(""); // Default for cleanOldUploadLogs
  });

  test("should do nothing if logs directory does not exist for general logs, but still try DOCX cleanup", async () => {
    // Mock all paths that cleanOldLogs checks to not exist.
    fs.existsSync.mockReturnValue(false);
    fs.promises.access.mockRejectedValue(new Error("ENOENT")); // When access is called on a non-existent dir
    await cleanOldLogs();

    // No file operations should occur if dirs don't exist.
    expect(fs.promises.readdir).not.toHaveBeenCalled();
    expect(fs.promises.unlink).not.toHaveBeenCalled();

    // DOCX cleanup should still be called as its path logic is internal.
    expect(mockCleanOldDocxSessionFiles).toHaveBeenCalledWith(14);
  });

  test("should delete old general log files and keep new ones", async () => {
    const logsDir = path.resolve(__dirname, "../../../../server/storage/logs");
    fs.existsSync.mockImplementation((p) => p === logsDir); // Only logs dir exists for this test.
    fs.promises.readdir.mockImplementation(async (p) => {
      if (p === logsDir) {
        return ["old.log", "new.log"];
      }
      return []; // Return no files for any other directory
    });
    fs.promises.stat.mockImplementation(async (filePath) => {
      if (path.basename(filePath) === "old.log")
        return { isFile: () => true, mtimeMs: now - twoDaysInMs - 1000 };
      if (path.basename(filePath) === "new.log")
        return { isFile: () => true, mtimeMs: now - 1000 };
      throw new Error("File not mocked: " + filePath);
    });
    fs.promises.unlink.mockResolvedValue(undefined);

    await cleanOldLogs();

    expect(fs.promises.unlink).toHaveBeenCalledWith(
      path.join(correctMockLogsDir, "old.log")
    );
    expect(fs.promises.unlink).toHaveBeenCalledTimes(1);
    expect(mockCleanOldDocxSessionFiles).toHaveBeenCalledWith(14);
  });

  test("should call cleanOldDocxSessionFiles with 14 days retention", async () => {
    fs.existsSync.mockReturnValue(false);
    fs.promises.readdir.mockResolvedValue([]);
    await cleanOldLogs();
    expect(mockCleanOldDocxSessionFiles).toHaveBeenCalledWith(14);
  });

  test("should log success of DOCX cleanup", async () => {
    fs.promises.readdir.mockResolvedValue([]);
    mockCleanOldDocxSessionFiles.mockResolvedValue({
      success: true,
      deletedCount: 3,
      error: null,
    });
    // console.log is mocked by setup.js, so direct spyOn might conflict or be unneeded if just checking call
    // For now, let's assume console.log calls are not critical to verify in this unit test
    await cleanOldLogs();
    expect(mockCleanOldDocxSessionFiles).toHaveBeenCalledWith(14);
    // Add specific console.log assertions if still needed and setup.js console mocks are adjusted
  });

  test("should log error if DOCX cleanup fails", async () => {
    fs.promises.readdir.mockResolvedValue([]);
    mockCleanOldDocxSessionFiles.mockResolvedValue({
      success: false,
      deletedCount: 0,
      error: "DOCX cleanup failed",
    });
    // console.error is mocked by setup.js
    await cleanOldLogs();
    expect(mockCleanOldDocxSessionFiles).toHaveBeenCalledWith(14);
    // Add specific console.error assertions if still needed
  });

  test("should handle error during general log file deletion and continue", async () => {
    const logsDir = path.resolve(__dirname, "../../../../server/storage/logs");
    fs.existsSync.mockImplementation((p) => p === logsDir); // Only logs dir exists for this test
    fs.promises.readdir.mockImplementation(async (p) => {
      if (p === logsDir) {
        return ["error.log", "good.log"];
      }
      return []; // Return no files for any other directory
    });
    fs.promises.stat.mockImplementation(async (filePath) => {
      return { isFile: () => true, mtimeMs: now - twoDaysInMs - 1000 };
    });
    fs.promises.unlink.mockImplementation(async (filePath) => {
      if (filePath.endsWith("error.log")) throw new Error("Delete failed");
    });

    await cleanOldLogs();

    expect(fs.promises.unlink).toHaveBeenCalledTimes(2);
    expect(mockCleanOldDocxSessionFiles).toHaveBeenCalledWith(14);
  });
});
