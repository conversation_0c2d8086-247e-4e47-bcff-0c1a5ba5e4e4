const { NewsMessage } = require("../models/newsMessage");
const { validatedRequest } = require("../utils/middleware/validatedRequest");
const { requireAdminRole } = require("../utils/middleware/requireAdminRole");
const { reqBody, safeJsonParse } = require("../utils/http");

// Validation utilities for news endpoints
function validateNewsId(newsId) {
  if (!newsId) {
    return { isValid: false, error: "newsId is required" };
  }

  const parsedId = parseInt(newsId);
  if (isNaN(parsedId) || parsedId <= 0) {
    return { isValid: false, error: "newsId must be a valid positive integer" };
  }

  return { isValid: true, value: parsedId };
}

function validateSystemNewsId(systemNewsId) {
  if (!systemNewsId) {
    return { isValid: false, error: "systemNewsId is required" };
  }

  // System news IDs can be strings (for system-generated news) or integers (for database news)
  // Check if it's a valid integer first
  const parsedId = parseInt(systemNewsId);
  if (!isNaN(parsedId) && parsedId > 0) {
    return { isValid: true, value: parsedId };
  }

  // If not a valid integer, check if it's a valid system news string ID
  if (typeof systemNewsId === "string" && systemNewsId.trim().length > 0) {
    // System news IDs typically follow patterns like "system-welcome-2024"
    const trimmedId = systemNewsId.trim();
    if (/^[a-zA-Z0-9\-_]+$/.test(trimmedId)) {
      return { isValid: true, value: trimmedId };
    }
  }

  return {
    isValid: false,
    error:
      "systemNewsId must be a valid positive integer or a valid string identifier",
  };
}

function newsEndpoints(app) {
  if (!app) return;

  // Get unread news for current user
  app.get("/news/unread", [validatedRequest], async (request, response) => {
    try {
      const user = response.locals.user;
      const unreadNews = await NewsMessage.getUnreadForUser(user.id);

      response.status(200).json({
        success: true,
        news: unreadNews,
      });
    } catch (error) {
      console.error("Error fetching unread news:", error);
      response.status(500).json({
        success: false,
        message: "Failed to fetch unread news",
      });
    }
  });

  // Get dismissed system news IDs for current user
  app.get(
    "/news/dismissed-system",
    [validatedRequest],
    async (request, response) => {
      try {
        const user = response.locals.user;
        const dismissedIds = await NewsMessage.getDismissedSystemNewsIds(
          user.id
        );

        response.status(200).json({
          success: true,
          dismissedIds,
        });
      } catch (error) {
        console.error("Error fetching dismissed system news:", error);
        response.status(500).json({
          success: false,
          message: "Failed to fetch dismissed system news",
        });
      }
    }
  );

  // Mark news as viewed
  app.post(
    "/news/:newsId/view",
    [validatedRequest],
    async (request, response) => {
      try {
        const user = response.locals.user;
        const { newsId } = request.params;

        // Validate newsId parameter
        const validation = validateNewsId(newsId);
        if (!validation.isValid) {
          return response.status(400).json({
            success: false,
            message: validation.error,
          });
        }

        const { view, message } = await NewsMessage.markAsViewed(
          user.id,
          validation.value
        );

        if (message) {
          return response.status(400).json({
            success: false,
            message,
          });
        }

        response.status(200).json({
          success: true,
          view,
        });
      } catch (error) {
        console.error("Error marking news as viewed:", error);
        response.status(500).json({
          success: false,
          message: "Failed to mark news as viewed",
        });
      }
    }
  );

  // Dismiss news
  app.post(
    "/news/:newsId/dismiss",
    [validatedRequest],
    async (request, response) => {
      try {
        const user = response.locals.user;
        const { newsId } = request.params;

        // Validate newsId parameter
        const validation = validateNewsId(newsId);
        if (!validation.isValid) {
          return response.status(400).json({
            success: false,
            message: validation.error,
          });
        }

        const { dismissal, message } = await NewsMessage.dismiss(
          user.id,
          validation.value,
          false // isSystemNews = false for local news
        );

        if (message) {
          return response.status(400).json({
            success: false,
            message,
          });
        }

        response.status(200).json({
          success: true,
          dismissal,
        });
      } catch (error) {
        console.error("Error dismissing news:", error);
        response.status(500).json({
          success: false,
          message: "Failed to dismiss news",
        });
      }
    }
  );

  // Dismiss system news
  app.post(
    "/news/system/:systemNewsId/dismiss",
    [validatedRequest],
    async (request, response) => {
      try {
        const user = response.locals.user;
        const { systemNewsId } = request.params;

        // Validate systemNewsId parameter
        const validation = validateSystemNewsId(systemNewsId);
        if (!validation.isValid) {
          return response.status(400).json({
            success: false,
            message: validation.error,
          });
        }

        const { dismissal, message } = await NewsMessage.dismiss(
          user.id,
          validation.value,
          true // isSystemNews = true for system news
        );

        if (message) {
          return response.status(400).json({
            success: false,
            message,
          });
        }

        response.status(200).json({
          success: true,
          message: "System news dismissed successfully",
          dismissal,
        });
      } catch (error) {
        console.error("Error dismissing system news:", error);
        response.status(500).json({
          success: false,
          message: "Failed to dismiss system news",
        });
      }
    }
  );

  // Get all active news for current user (including dismissed ones)
  app.get("/news/all-active", [validatedRequest], async (request, response) => {
    try {
      const user = response.locals.user;

      // Get all active database news with dismissal status
      const databaseNews = await NewsMessage.getAllActiveForUser(user.id);

      // Get dismissed system news IDs first
      const dismissedData = await NewsMessage.getDismissedNewsIds(user.id);
      const dismissedSystemIds = dismissedData.system || [];

      // Import system news items with proper translation keys
      const systemNewsItems = [
        {
          id: "system-welcome-2024",
          titleKey: "news-system-items.system-welcome-2024.title",
          contentKey: "news-system-items.system-welcome-2024.content",
          title: "Welcome to IST Legal Platform", // Fallback title
          content:
            "Welcome to the IST Legal platform! We're excited to have you on board.", // Fallback content
          priority: "medium",
          targetRoles: null, // Show to all users
          expiresAt: null, // Never expires
          isActive: true,
          isSystemNews: true,
          createdAt: "2024-01-01T00:00:00Z",
          version: "1.0.0",
          metadata: {
            author: "IST Legal Team",
            category: "welcome",
            tags: ["welcome", "getting-started"],
          },
        },
      ];

      // Filter system news by user roles and add dismissal status
      const userRoles = user?.role ? [user.role] : [];
      const activeSystemNews = systemNewsItems.filter((item) => {
        // Check if item is active
        if (!item.isActive) return false;

        // Check if item has expired
        if (item.expiresAt && new Date(item.expiresAt) < new Date())
          return false;

        // Check target roles
        if (!item.targetRoles || item.targetRoles.length === 0) return true;
        return userRoles.some((role) => item.targetRoles.includes(role));
      });

      // Add dismissal status to system news
      const systemNewsWithStatus = activeSystemNews.map((news) => ({
        ...news,
        isSystemNews: true,
        isDismissed: dismissedSystemIds.includes(news.id),
        dismissedAt: dismissedSystemIds.includes(news.id) ? new Date() : null,
      }));

      // Combine and sort all news
      const allNews = [...databaseNews, ...systemNewsWithStatus];

      // Sort by priority (urgent > high > medium > low) then by creation date
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      allNews.sort((a, b) => {
        const priorityDiff =
          (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
        if (priorityDiff !== 0) return priorityDiff;

        return new Date(b.createdAt) - new Date(a.createdAt);
      });

      response.status(200).json({
        success: true,
        news: allNews,
      });
    } catch (error) {
      console.error("Error fetching all active news:", error);
      response.status(500).json({
        success: false,
        message: "Failed to fetch all active news",
      });
    }
  });

  // Admin endpoints

  // Get all news messages (admin only)
  app.get(
    "/admin/news",
    [validatedRequest, requireAdminRole],
    async (request, response) => {
      try {
        const allNews = await NewsMessage.getAll();

        // Transform database field names to match frontend expectations
        const transformedNews = allNews.map((news) => ({
          ...news,
          isActive: news.is_active, // Transform is_active to isActive
          targetRoles: news.target_roles
            ? safeJsonParse(news.target_roles, null)
            : null, // Transform target_roles to targetRoles
          expiresAt: news.expires_at, // Transform expires_at to expiresAt
          createdBy: news.created_by, // Transform created_by to createdBy
        }));

        response.status(200).json({
          success: true,
          news: transformedNews,
        });
      } catch (error) {
        console.error("Error fetching all news:", error);
        response.status(500).json({
          success: false,
          message: "Failed to fetch news",
        });
      }
    }
  );

  // Create news message (admin only)
  app.post(
    "/admin/news",
    [validatedRequest, requireAdminRole],
    async (request, response) => {
      try {
        const user = response.locals.user;

        const { title, content, priority, targetRoles, expiresAt } =
          reqBody(request);

        if (!title || !content) {
          return response.status(400).json({
            success: false,
            message: "Title and content are required",
          });
        }

        const { newsMessage, message } = await NewsMessage.create({
          title,
          content,
          priority,
          targetRoles,
          expiresAt: expiresAt ? new Date(expiresAt) : null,
          createdBy: user.id,
        });

        if (message) {
          return response.status(400).json({
            success: false,
            message,
          });
        }

        // Transform database field names to match frontend expectations
        const transformedNewsMessage = {
          ...newsMessage,
          isActive: newsMessage.is_active,
          targetRoles: newsMessage.target_roles
            ? safeJsonParse(newsMessage.target_roles, null)
            : null,
          expiresAt: newsMessage.expires_at,
          createdBy: newsMessage.created_by,
        };

        response.status(201).json({
          success: true,
          newsMessage: transformedNewsMessage,
        });
      } catch (error) {
        console.error("Error creating news:", error);
        response.status(500).json({
          success: false,
          message: "Failed to create news",
        });
      }
    }
  );

  // Update news message (admin only)
  app.put(
    "/admin/news/:newsId",
    [validatedRequest, requireAdminRole],
    async (request, response) => {
      try {
        const { newsId } = request.params;
        const updates = reqBody(request);

        // Validate newsId parameter
        const validation = validateNewsId(newsId);
        if (!validation.isValid) {
          return response.status(400).json({
            success: false,
            message: validation.error,
          });
        }

        const { newsMessage, message } = await NewsMessage.update(
          validation.value,
          updates
        );

        if (message) {
          return response.status(400).json({
            success: false,
            message,
          });
        }

        // Transform database field names to match frontend expectations
        const transformedNewsMessage = {
          ...newsMessage,
          isActive: newsMessage.is_active,
          targetRoles: newsMessage.target_roles
            ? safeJsonParse(newsMessage.target_roles, null)
            : null,
          expiresAt: newsMessage.expires_at,
          createdBy: newsMessage.created_by,
        };

        response.status(200).json({
          success: true,
          newsMessage: transformedNewsMessage,
        });
      } catch (error) {
        console.error("Error updating news:", error);
        response.status(500).json({
          success: false,
          message: "Failed to update news",
        });
      }
    }
  );

  // Delete news message (admin only)
  app.delete(
    "/admin/news/:newsId",
    [validatedRequest, requireAdminRole],
    async (request, response) => {
      try {
        const { newsId } = request.params;

        // Validate newsId parameter
        const validation = validateNewsId(newsId);
        if (!validation.isValid) {
          return response.status(400).json({
            success: false,
            message: validation.error,
          });
        }

        const { success, message } = await NewsMessage.delete(validation.value);

        if (!success) {
          return response.status(400).json({
            success: false,
            message,
          });
        }

        response.status(200).json({
          success: true,
          message: "News message deleted successfully",
        });
      } catch (error) {
        console.error("Error deleting news:", error);
        response.status(500).json({
          success: false,
          message: "Failed to delete news",
        });
      }
    }
  );

  // Get news analytics (admin only)
  app.get(
    "/admin/news/:newsId/analytics",
    [validatedRequest, requireAdminRole],
    async (request, response) => {
      try {
        const { newsId } = request.params;

        // Validate newsId parameter
        const validation = validateNewsId(newsId);
        if (!validation.isValid) {
          return response.status(400).json({
            success: false,
            message: validation.error,
          });
        }

        const analytics = await NewsMessage.getAnalytics(validation.value);

        response.status(200).json({
          success: true,
          analytics,
        });
      } catch (error) {
        console.error("Error fetching news analytics:", error);
        response.status(500).json({
          success: false,
          message: "Failed to fetch analytics",
        });
      }
    }
  );
}

module.exports = { newsEndpoints };
