export default {
  // =========================
  // DOCUMENT DRAFTING
  // =========================
  "document-drafting": {
    title: "Tworzenie dokumentów",
    description: "Kontroluj swoje ustawienia tworzenia dokumentów.",
    configuration: "Konfiguracja",
    "drafting-model": "LLM do tworzenia dokumentów",
    enabled: "Tworzenie dokumentów jest włączone",
    disabled: "Tworzenie dokumentów jest wyłączone",
    "enabled-toast": "Tworzenie dokumentów włączone",
    "disabled-toast": "Tworzenie dokumentów wyłączone",
    "desc-settings":
      "Administrator może zmienić ustawienia tworzenia dokumentów dla wszystkich użytkowników.",
    "drafting-llm": "Preferencje LLM do tworzenia dokumentów",
    saving: "Zapisywanie...",
    save: "<PERSON><PERSON><PERSON><PERSON>mian<PERSON>",
    "chat-settings": "Ustawienia czatu",
    "drafting-chat-settings": "Ustawienia czatu dla tworzenia dokumentów",
    "chat-settings-desc":
      "Kontroluj zachowanie funkcji czatu dla tworzenia dokumentów.",
    "drafting-prompt": "Systemowe zapytanie do tworzenia dokumentów",
    "drafting-prompt-desc":
      "Systemowe zapytanie używane w tworzeniu dokumentów różni się od systemowego zapytania do prawnego Q&A. Definiuje ono kontekst i instrukcje dla AI, aby wygenerowała odpowiedź. Powinieneś dostarczyć starannie przygotowane zapytanie, aby AI mogło wygenerować odpowiednią i dokładną odpowiedź",
    linking: "Łączenie dokumentów",
    "legal-issues-prompt": "Zapytanie o kwestie prawne",
    "legal-issues-prompt-desc":
      "Wprowadź zapytanie dotyczące kwestii prawnych.",
    "memo-prompt": "Zapytanie o notatkę",
    "memo-prompt-desc": "Wprowadź zapytanie dotyczące notatki.",
    "desc-linkage":
      "Włącz dodawanie dalszego kontekstu prawnego poprzez wyszukiwania wektorowe/PDR w oparciu o pobieranie notatek",
    message: {
      title: "Sugerowane wiadomości przy tworzeniu dokumentów",
      description:
        "Dodaj sugerowane wiadomości, które użytkownicy mogą szybko wybrać przy tworzeniu dokumentów.",
      heading: "Domyślny nagłówek wiadomości",
      body: "Domyślna treść wiadomości",
      "new-heading": "Nagłówek wiadomości",
      message: "Wiadomość",
      add: "Dodaj wiadomość",
      save: "Zapisz wiadomości",
    },
    "combine-prompt": "Polecenie Łączenia",
    "combine-prompt-desc":
      "Podaj polecenie systemowe do łączenia wielu odpowiedzi w jedną odpowiedź. To polecenie jest używane zarówno do łączenia odpowiedzi i notatek DD Linkage, jak i do łączenia różnych odpowiedzi z przetwarzania Infinity Context.",
    "page-description":
      "Ta strona służy do dostosowywania różnych zapytań używanych w różnych funkcjach modułu tworzenia dokumentów. W każdym polu wprowadzania pokazane jest domyślne zapytanie, które będzie używane, chyba że na tej stronie zostanie zastosowane niestandardowe zapytanie.",
    "dd-linkage-steps": "Zapytania stosowane w krokach łączenia dokumentów",
    "general-combination-prompt": "Ogólne polecenie łączenia",
    "import-memo": {
      title: "Importuj z Legal QA",
      "button-text": "Importuj notatkę",
      "search-placeholder": "Szukaj wątków...",
      import: "Importuj",
      importing: "Importowanie...",
      "no-threads": "Nie znaleziono wątków Legal QA",
      "no-matching-threads": "Żadne wątki nie pasują do twojego wyszukiwania",
      "thread-not-found": "Nie znaleziono wybranego wątku",
      "empty-thread": "Wybrany wątek nie ma żadnej treści do zaimportowania",
      "import-success": "Treść wątku została pomyślnie zaimportowana",
      "import-error": "Nie udało się zaimportować treści wątku",
      "import-error-details": "Błąd podczas importowania: {{details}}",
      "fetch-error": "Nie udało się pobrać wątków. Spróbuj ponownie później.",
      "imported-from": "Zaimportowano z wątku Legal QA",
      "unnamed-thread": "Nienazwany wątek",
      "unknown-workspace": "Nieznana przestrzeń robocza",
      "no-threads-available": "Brak wątków do zaimportowania",
      "create-conversations-first":
        "Najpierw utwórz konwersacje w przestrzeni roboczej Legal QA, a następnie będziesz mógł je tutaj zaimportować.",
      "no-legal-qa-workspaces":
        "Nie znaleziono przestrzeni roboczych Legal QA z aktywnymi wątkami. Utwórz najpierw konwersacje w przestrzeni roboczej Legal QA, aby je zaimportować.",
      "empty-workspaces-with-names":
        "Znaleziono przestrzenie robocze Legal QA ({{workspaceNames}}), ale nie zawierają one jeszcze aktywnych wątków. Utwórz najpierw konwersacje w tych przestrzeniach roboczych, aby je zaimportować.",
      "import-success-with-name":
        "Pomyślnie zaimportowano wątek: {{threadName}}",
    },
  },

  // =========================
  // DD SETTINGS (WORKSPACE LINKING SETTINGS)
  // =========================
  "dd-settings": {
    title: "Ustawienia łączenia obszarów roboczych",
    description:
      "Kontroluj limity tokenów i zachowanie powiązanych obszarów roboczych",
    "vector-search": {
      title: "Wyszukiwanie wektorowe",
      description:
        "Gdy ta funkcja jest włączona, semantyczne wyszukiwania wektorowe są wykonywane we wszystkich powiązanych obszarach roboczych w celu znalezienia odpowiednich dokumentów prawnych. System konwertuje zapytania użytkownika na osadzenia wektorowe i dopasowuje je do wektorów dokumentów w bazie danych każdego powiązanego obszaru roboczego. Ta funkcja działa jako rozwiązanie awaryjne, gdy generowanie notatek jest włączone, ale nie przynosi rezultatów. Gdy generowanie notatek jest wyłączone, wyszukiwanie wektorowe staje się podstawową metodą pobierania informacji z powiązanych obszarów roboczych. Głębokość wyszukiwania jest kontrolowana przez ustawienie limitu tokenów wektorowych.",
    },
    "memo-generation": {
      title: "Generowanie notatek",
      description:
        "Ta funkcja automatycznie generuje zwięzłe notatki prawne z dokumentów znalezionych w powiązanych obszarach roboczych. Po włączeniu system analizuje pobrane dokumenty, aby utworzyć ustrukturyzowane podsumowania kluczowych punktów prawnych, precedensów i odpowiedniego kontekstu. Te notatki służą jako podstawowa metoda włączania wiedzy z powiązanych obszarów roboczych. Jeśli generowanie notatek nie powiedzie się lub nie przyniesie rezultatów, system automatycznie przejdzie do wyszukiwania wektorowego (jeśli jest włączone), aby zapewnić, że odpowiednie informacje zostaną mimo to pobrane. Długość i poziom szczegółowości tych notatek są regulowane przez ustawienie limitu tokenów notatek.",
    },
    "base-generation": {
      title: "Podstawowa analiza prawna",
      description:
        "Umożliwia generowanie wstępnej analizy prawnej na podstawie początkowego zapytania użytkownika, przed włączeniem informacji z powiązanych obszarów roboczych. Gdy jest aktywna, system tworzy podstawowy framework analizy, który pomaga kierować kolejnymi procesami wyszukiwania dokumentów i generowania notatek. Ta podstawowa analiza pomaga zapewnić, że odpowiedzi pozostają skoncentrowane na głównych kwestiach prawnych, jednocześnie włączając informacje wspierające z powiązanych obszarów roboczych. Zakres tej analizy jest kontrolowany przez ustawienie limitu tokenów podstawowych.",
    },
    "linked-workspace-impact": {
      title: "Wpływ tokenów na powiązane obszary robocze",
      description:
        "Kontroluje, w jaki sposób system zarządza swoim budżetem tokenów w wielu powiązanych obszarach roboczych. Gdy ta funkcja jest włączona, system dynamicznie dostosowuje dostępne tokeny dla każdego obszaru roboczego w oparciu o całkowitą liczbę powiązanych obszarów roboczych, zapewniając sprawiedliwy podział zasobów danych. Zapobiega to dominacji jednego obszaru roboczego w oknie kontekstu, jednocześnie utrzymując kompleksowe pokrycie wszystkich odpowiednich obszarów prawnych. To ustawienie rezerwuje pojemność tokenów specjalnie dla wyników generowania notatek i/lub wyszukiwania wektorowego z każdego powiązanego obszaru roboczego, co może zmniejszyć całkowitą liczbę tokenów dostępnych dla głównego obszaru roboczego, gdy wiele obszarów roboczych jest powiązanych.",
    },
    "vector-token-limit": {
      title: "Limit tokenów wektorowych",
      description:
        "Określa maksymalną liczbę tokenów przydzielonych dla wyników wyszukiwania wektorowego z każdego powiązanego obszaru roboczego. Limit ten ma zastosowanie, gdy używane jest wyszukiwanie wektorowe, albo jako metoda podstawowa (gdy generowanie notatek jest wyłączone), albo jako rozwiązanie awaryjne (gdy generowanie notatek nie powiedzie się). Wyższe limity umożliwiają bardziej kompleksowe pobieranie dokumentów, ale zmniejszają liczbę tokenów dostępnych dla innych operacji.",
    },
    "memo-token-limit": {
      title: "Limit tokenów notatek",
      description:
        "Kontroluje maksymalną długość generowanych notatek prawnych z każdego powiązanego obszaru roboczego. Jako podstawowa metoda integracji wiedzy, notatki te podsumowują kluczowe punkty prawne z dokumentów powiązanego obszaru roboczego. Jeśli notatka przekroczy ten limit tokenów, zostanie odrzucona, a system przejdzie do wyszukiwania wektorowego (jeśli jest włączone). Wyższe limity umożliwiają bardziej szczegółową analizę prawną, ale mogą zmniejszyć liczbę powiązanych obszarów roboczych, które można włączyć.",
    },
    "base-token-limit": {
      title: "Limit tokenów podstawowych",
      description:
        "Określa maksymalną długość tokenów dla początkowego frameworka analizy prawnej. Ten limit wpływa na to, jak kompleksowa może być analiza podstawowa przed włączeniem informacji z powiązanych obszarów roboczych. Wyższy limit umożliwia bardziej szczegółową analizę początkową, ale pozostawia mniej miejsca na włączenie treści z powiązanych obszarów roboczych.",
    },
    "toast-success": "Ustawienia zaktualizowane pomyślnie",
    "toast-fail": "Nie udało się zaktualizować ustawień",
  },

  // =========================
  // WORKSPACE LINKING
  // =========================
  "workspace-linking": {
    title: "Ustawienia łączenia obszarów roboczych",
    description:
      "Kontroluj limity tokenów i zachowanie powiązanych obszarów roboczych",
    "vector-search": {
      title: "Wyszukiwanie wektorowe",
      description:
        "Metoda awaryjnego znajdowania odpowiednich dokumentów, gdy generowanie notatek nie powiedzie się lub jest wyłączone",
    },
    "memo-generation": {
      title: "Generowanie notatek",
      description:
        "Podstawowa metoda włączania wiedzy z powiązanych obszarów roboczych",
    },
    "base-generation": {
      title: "Podstawowa analiza prawna",
      description:
        "Generowanie początkowej analizy kwestii prawnych z zapytań użytkownika",
    },
    "linked-workspace-impact": {
      title: "Wpływ tokenów na powiązane obszary robocze",
      description:
        "Rezerwuj tokeny dla każdego powiązanego obszaru roboczego proporcjonalnie do ich liczby",
    },
    "vector-token-limit": {
      title: "Limit tokenów wektorowych",
      description:
        "Maksymalna liczba tokenów na powiązaną przestrzeń roboczą dla wyszukiwania wektorowego",
    },
    "memo-token-limit": {
      title: "Limit tokenów notatki",
      description:
        "Maksymalna liczba tokenów dla generowania notatek dotyczących kwestii prawnych",
    },
    "base-token-limit": {
      title: "Limit tokenów bazowych",
      description: "Maksymalna liczba tokenów dla pobierania treści bazowych",
    },
    "toast-success": "Ustawienia zaktualizowane pomyślnie",
    "toast-fail": "Nie udało się zaktualizować ustawień",
  },
  // =========================
  // BINARY LLM SELECTION
  // =========================
  binary_llm_selection: {
    "secondary-llm-toggle": "Wybór binarnego LLM",
    "secondary-llm-toggle-description":
      "Włącz tę opcję, aby administratorzy mogli wybierać pomiędzy dwoma modelami LLM w module tworzenia dokumentów.",
    "secondary-llm-toggle-status": "Status: ",
    "secondary-llm-user-level": "Poziom użytkownika dla drugorzędnego LLM",
    "secondary-llm-user-level-description":
      "Włącz tę opcję, aby WSZYSCY użytkownicy mogli wybierać między dwoma modelami LLM w przestrzeni roboczej tworzenia dokumentów.",
  },
};
