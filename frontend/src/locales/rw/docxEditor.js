export default {
  // =========================
  // DOCX EDITOR
  // =========================
  "docx-edit": {
    "edit-instructions":
      "Andika amabwiriza y'uburyo ushaka guhindura inyandiko. Garagaza neza impinduka ushaka gukora.",
    "instructions-placeholder":
      "<PERSON><PERSON><PERSON>, Kosora amakosa y'ururimi, gira imvugo isobanutse, ongeramo igika cya nyuma...",
    "process-button": "Kuramo inyandiko",
    "upload-docx": "Ohereza DOCX",
    "processing-upload": "Kurimo gukora...",
    "content-extracted": "Ibikubiye muri dosiye ya DOCX byakuwe",
    "file-type-note": "Dosiye za .docx gusa nizo zemewe",
    "upload-error": "Ikosa mu kohereza dosiye: ",
    "no-instructions": "And<PERSON> amabwiriza yo guhindura",
    "process-error": "Ikosa mu gukora inyandiko: ",
    "changes-highlighted": "Inyandiko n'impinduka zigaragara",
    "download-button": "Kuramo inyandiko",
    "start-over-button": "Tangira bundi bushya",
    "no-document": "Nta nyandiko iboneka yo gukuramo",
    "download-error": "Ikosa mu gukuramo inyandiko: ",
    "download-success": "Inyandiko yakuwe neza",
    processing: "Gukora inyandiko...",
    "instructions-used": "Amabwiriza yakoreshejwe",
    "import-success": "Ibikubiye muri DOCX byinjijwe neza",
    "edit-success": "Ibikubiye muri DOCX byavuguruwe neza",
    "canvas-document-title": "Inyandiko ya Canvas",
    "upload-button": "Ohereza DOCX",
    "download-as-docx": "Kuramo nka DOCX",
    "output-example": "Urugero rw'igisubizo",
    "output-example-desc":
      "Ohereza dosiye ya DOCX kugira ngo wongeremo urugero rw'ibikubiyemo kuri prompt yawe",
    "content-examples-tag-open": "<INGERO_Y_IBIKUBIYEMO>",
    "content-examples-tag-close": "</INGERO_Y_IBIKUBIYEMO>",
    "content-examples-info":
      "<INFO>Iki ni urugero rw'ibikubiyemo bigomba gukurwa, biturutse ku kibazo cy'amategeko gisa n'iki. Menya ko uru rugero rushobora kuba rugufi cyangwa rurerure kuruta ibikubiyemo bigomba gukurwa ubu.</INFO>",
    "contains-example-content": "[Irimo urugero rw'ibikubiyemo]",
  },
};
