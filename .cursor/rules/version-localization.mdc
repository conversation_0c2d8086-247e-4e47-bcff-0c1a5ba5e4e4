---
description:
globs:
alwaysApply: false
---
# Version Localization System

## Critical Difference from Standard I18n

**IMPORTANT:** The version system uses a unique localization approach that differs from the standard i18n system used throughout the platform.

### Standard I18n vs Version Localization

| Feature | Standard I18n | Version System |
|---------|---------------|----------------|
| **Location** | `frontend/src/locales/[lang]/` files | `version.json` root file |
| **Structure** | Separate files per language | Inline language keys |
| **Detection** | Frontend-based | Backend server-side |
| **Fallback** | Frontend translation system | Backend language detection |

## Version Localization Rules

### 1. Never Use Standard Locale Files

**❌ WRONG - Do NOT do this:**
```javascript
// frontend/src/locales/en/common.js
version: {
  "v1.1.0-description": "Added new features...",
}
```

**✅ CORRECT - Use version.json:**
```json
{
  "version": "1.1.0",
  "description": "Added new features...",
  "description-sv": "Lade till nya funktioner...",
  "description-no": "La til nye funksjoner...",
  "description-fr": "Ajout de nouvelles fonctionnalités...",
  "description-de": "Neue Funktionen hinzugefügt...",
  "description-pl": "Dodano nowe funkcje...",
  "description-rw": "Byongerewe ibintu bishya..."
}
```

### 2. Required Language Keys

When adding version descriptions, **MUST** include ALL supported languages:

```json
{
  "description": "English text (required default)",
  "description-sv": "Swedish text (required)",
  "description-no": "Norwegian text (required)",
  "description-fr": "French text (required)",
  "description-de": "German text (required)",
  "description-pl": "Polish text (required)",
  "description-rw": "Kinyarwanda text (required)"
}
```

### 3. Language Detection Priority

The backend automatically detects language in this order:

1. **System Language Setting** - From admin panel database
2. **Browser Accept-Language Header** - Automatically parsed
3. **English Fallback** - Default if no supported language found

### 4. Supported Version Formats

#### Single Version Format
```json
{
  "version": "1.1.0",
  "description": "English description",
  "description-sv": "Swedish description",
  "description-no": "Norwegian description",
  "description-fr": "French description",
  "description-de": "German description",
  "description-pl": "Polish description",
  "description-rw": "Kinyarwanda description",
  "timestamp": "2025-01-01T10:00:00Z"
}
```

#### Multi-Version Format (Recommended)
```json
{
  "versions": [
    {
      "version": "1.0.0",
      "description": "Initial release",
      "description-sv": "Första utgivningen",
      "description-no": "Første utgivelse",
      "description-fr": "Version initiale",
      "description-de": "Erste Veröffentlichung",
      "description-pl": "Wersja początkowa",
      "description-rw": "Sohoka ya mbere",
      "timestamp": "2025-01-01T10:00:00Z"
    },
    {
      "version": "1.1.0",
      "description": "Added new features",
      "description-sv": "Lade till nya funktioner",
      "description-no": "La til nye funksjoner",
      "description-fr": "Ajout de nouvelles fonctionnalités",
      "description-de": "Neue Funktionen hinzugefügt",
      "description-pl": "Dodano nowe funkcje",
      "description-rw": "Byongerewe ibintu bishya",
      "timestamp": "2025-05-31T10:30:00Z"
    }
  ]
}
```

### 5. Implementation Details

#### Backend Components
- **Language Detection**: `server/utils/helpers/languageDetection.js`
- **Version Endpoint**: `server/endpoints/system.js` (`GET /version`)
- **Version Processing**: `server/utils/helpers/versionComparison.js`

#### Frontend Components
- **Version Model**: `frontend/src/models/version.js`
- **Version Display**: `frontend/src/components/VersionDisplay/`
- **Tooltip Title Only**: Uses standard i18n (`frontend/src/locales/[lang]/common.js`)

### 6. Adding New Versions

When adding a new version:

1. **Update version.json with ALL languages**
2. **Test in different browser languages**
3. **Verify admin language settings work**
4. **Ensure fallbacks work correctly**

#### Example New Version
```json
{
  "version": "1.2.0",
  "description": "Enhanced search functionality and new legal templates",
  "description-sv": "Förbättrad sökfunktionalitet och nya juridiska mallar",
  "description-no": "Forbedret søkefunksjonalitet og nye juridiske maler",
  "description-fr": "Fonctionnalité de recherche améliorée et nouveaux modèles juridiques",
  "description-de": "Verbesserte Suchfunktionen und neue juristische Vorlagen",
  "description-pl": "Ulepszona funkcjonalność wyszukiwania i nowe szablony prawne",
  "description-rw": "Ibikorwa by'ubushakashatsi byatewe imbere n'inyandikokorugero nshya z'amategeko",
  "timestamp": "2025-06-01T10:00:00Z"
}
```

### 7. Common Mistakes to Avoid

❌ **Adding version descriptions to locale files**
❌ **Missing language descriptions**
❌ **Using wrong language codes**
❌ **Mixing standard i18n with version localization**
❌ **Forgetting to test language detection**

✅ **Always use version.json for version descriptions**
✅ **Include ALL supported languages**
✅ **Use exact language codes: sv, no, fr, de, pl, rw**
✅ **Test with different browser languages**
✅ **Test with system language settings**

### 8. Why This Special System?

1. **Centralized Management**: All version info in one file
2. **Backend Localization**: Server-side language detection
3. **Automatic Fallbacks**: Built-in language detection priority
4. **Simplified Deployment**: No need to update multiple locale files
5. **Better Performance**: Direct language selection without frontend processing

## Testing Localization

### Test Cases

1. **System Language**: Set Swedish in admin panel → Should show Swedish
2. **Browser Language**: Clear system setting, set browser to Swedish → Should show Swedish
3. **Fallback**: No system setting, English browser → Should show English
4. **Multi-version**: Should always return highest version with correct language

### Browser Testing

Change browser language preferences:
- Chrome: Settings → Languages
- Firefox: Preferences → Language
- Safari: System Preferences → Language & Region

### Admin Panel Testing

Test system language setting:
1. Admin Panel → System Settings
2. Set Language to Swedish
3. Save settings
4. Test version tooltip

## Documentation References

- **Detailed Documentation**: `frontend/docs/version-control-system.md`
- **Backend Tests**: `server/tests/unit/endpoints/version.test.js`
- **Language Detection Tests**: `server/utils/helpers/__tests__/languageDetection.test.js`
- **Frontend Tests**: `frontend/src/components/VersionDisplay/__tests__/index.test.jsx`
