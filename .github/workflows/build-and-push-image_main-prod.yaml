# This Github action is for publishing of the PRODUCTION image for IST Legal
name: Publish IST Legal PRODUCTION Docker image

concurrency:
  group: build-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches: ["main-prod"]
    paths-ignore:
      - "**.md"
      - "cloud-deployments/**/*"
      - "images/**/*"
      - ".vscode/**/*"
      - "**/.env.example"
      - ".github/ISSUE_TEMPLATE/**/*"
      - ".devcontainer/**/*"
      - "embed/**/*" # Embed is submodule
      - "browser-extension/**/*" # Chrome extension is submodule
      - "server/utils/agents/aibitat/example/**/*" # Do not push new image for local dev testing of new aibitat images.
      - "docker/vex/*" # CVE exceptions we know are not in risk

  workflow_dispatch:

jobs:
  push_to_production:
    name: Push PRODUCTION Docker image to registry
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@65b78e6e13532edd9afa3aa52ac7964289d1a9c1
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.ISTLEGAL_GITHUB_TOKEN }}

      - name: Get lowercase repository name
        id: repo_lc
        run: echo "name=$(echo '${{ github.repository }}' | tr '[:upper:]' '[:lower:]')" >> $GITHUB_OUTPUT

      - name: Get version information
        id: version
        run: |
          # Try to get version from version.json
          if [ -f "version.json" ]; then
            VERSION=$(cat version.json | jq -r '.version' 2>/dev/null || echo "")
            DESCRIPTION=$(cat version.json | jq -r '.description' 2>/dev/null || echo "")
          fi

          # Fallback to package.json
          if [ -z "$VERSION" ]; then
            VERSION=$(cat package.json | jq -r '.version' 2>/dev/null || echo "unknown")
          fi

          # Get git information
          GIT_COMMIT=$(git rev-parse HEAD)
          GIT_SHORT_COMMIT=$(git rev-parse --short HEAD)

          echo "version=${VERSION}" >> $GITHUB_OUTPUT
          echo "description=${DESCRIPTION}" >> $GITHUB_OUTPUT
          echo "git_commit=${GIT_COMMIT}" >> $GITHUB_OUTPUT
          echo "git_short_commit=${GIT_SHORT_COMMIT}" >> $GITHUB_OUTPUT

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@9ec57ed1fcdbf14dcef7dfbe97b2010124a938b7
        with:
          images: |
            ghcr.io/${{ github.repository }}
          tags: |
            type=raw,value=prod,enable={{is_default_branch}}
            type=raw,value=production
            type=raw,value=${{ steps.version.outputs.version }},enable=${{ steps.version.outputs.version != 'unknown' }}
            type=ref,event=branch
            type=ref,event=tag
            type=ref,event=pr
          labels: |
            org.opencontainers.image.title=IST Legal
            org.opencontainers.image.description=IST Legal Platform - Production Build
            org.opencontainers.image.version=${{ steps.version.outputs.version }}
            org.opencontainers.image.revision=${{ steps.version.outputs.git_commit }}
            org.opencontainers.image.source=https://github.com/${{ github.repository }}

      - name: Build and push multi-platform Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/Dockerfile
          sbom: true
          provenance: mode=max
          push: true
          # platforms: linux/amd64,linux/arm64
          platforms: linux/amd64
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=registry,ref=ghcr.io/${{ steps.repo_lc.outputs.name }}:buildcache-main-prod
          cache-to: type=registry,ref=ghcr.io/${{ steps.repo_lc.outputs.name }}:buildcache-main-prod,mode=max
          build-args: |
            VERSION=${{ steps.version.outputs.version }}
            GIT_COMMIT=${{ steps.version.outputs.git_commit }}
            BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}

      - name: Production deployment summary
        run: |
          echo "## 🚀 Production Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "| Property | Value |" >> $GITHUB_STEP_SUMMARY
          echo "|----------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| Version | \`${{ steps.version.outputs.version }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| Git Commit | \`${{ steps.version.outputs.git_short_commit }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| Branch | \`${{ github.ref_name }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| Registry | \`ghcr.io/${{ steps.repo_lc.outputs.name }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| Tags | \`prod\`, \`production\`, \`${{ steps.version.outputs.version }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📋 Next Steps" >> $GITHUB_STEP_SUMMARY
          echo "1. Verify deployment in production environment" >> $GITHUB_STEP_SUMMARY
          echo "2. Run post-deployment tests" >> $GITHUB_STEP_SUMMARY
          echo "3. Monitor application health and metrics" >> $GITHUB_STEP_SUMMARY

#      - name: Deploy to AWS
#        uses: appleboy/ssh-action@master
#        with:
#          host: ${{ secrets.AWS_HOST || '**************' }}
#          username: ${{ secrets.AWS_USERNAME || 'ec2-user' }}
#          key: ${{ secrets.AWS_SSH_KEY }}
#          script: |
#              sudo docker stop istlegal || true
#              sudo docker rm istlegal || true
#              sudo docker pull ghcr.io/rahswe/istlegal:prod
#              sudo docker run -d -p 80:3001 -p 443:3001 -p 3001:3001 --cap-add SYS_ADMIN --name istlegal -v /home/<USER>/istlegal:/app/server/storage -v /home/<USER>/istlegal/.env:/app/server/.env -e STORAGE_DIR=/app/server/storage ghcr.io/rahswe/istlegal:prod
#              ! lsof -i :8080 > /dev/null && cd mkdir -p /home/<USER>/istlegal/hotdir && cd /home/<USER>/istlegal/hotdir && python3 -m http.server 8080

