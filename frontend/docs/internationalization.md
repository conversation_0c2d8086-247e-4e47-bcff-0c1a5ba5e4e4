# Internationalization (i18n) File Organization and Management

This document explains how to structure translation keys across multiple files per locale, configure the loader, migrate existing keys from `common.js`, and maintain consistency across all locale files.

## Directory Structure

Organize your locale files as follows:

```text
frontend/src/locales/
  en/
    common.js
    recentUploads.js
    sidebar.js
    legalTemplates.js
  fr/
    common.js
    recentUploads.js
    sidebar.js
    legalTemplates.js
  de/
    common.js
    recentUploads.js
    sidebar.js
    legalTemplates.js
  ...
```

## File Naming Conventions

- Filenames should reflect the feature or component (e.g., `recentUploads.js`, `sidebar.js`, `legalTemplates.js`).
- Filenames can be arbitrary; the loader doesn't depend on them.
- Each file must export a plain object of translation key-value pairs.

## Loader Behavior

The static loader in `frontend/src/locales/resources.js` automatically imports and merges all translation files for each supported language:

```js
export const resources = {
  en: {
    common: mergeDeep(
      {},
      Common,
      RecentUploads,
      Sidebar,
      LegalTemplates /*, ... feature bundles */
    ),
  },
  fr: {
    common: mergeDeep(
      {},
      FrCommon,
      FrRecentUploads,
      FrSidebar,
      FrLegalTemplates /*, ... */
    ),
  },
  // ... other languages
};
```

- `loadLanguageAsync(lang)` returns `null` when `lang` is the default (`en`).
- For other languages, it returns the merged bundle (e.g., `resources.fr.common`).

## Migration Guide

To migrate keys from the existing `common.js` into feature-specific files:

1. **Identify Related Keys**
   - Review `common.js` and group keys by feature or component (e.g., uploads, sidebar, legal templates).
2. **Create New Locale Files**
   - For each group, create a new file in each locale directory (e.g., `recentUploads.js`, `legalTemplates.js`).
3. **Move Key-Value Pairs**
   - Copy the relevant keys from `common.js` into the new feature file.
4. **Verify Loader Imports**
   - The `resources.js` loader dynamically merges all files—no changes required if using static imports; otherwise, import new files in `resources.js`.
5. **Run Tests**
   - Ensure existing tests pass and write new tests for moved keys if necessary.
6. **Clean Up**
   - Remove migrated keys from `common.js` and delete any now-empty files.

## Adding New Locale Files

When adding a new feature-specific locale file (e.g., `legalTemplates.js`):

1. **Create the File in English First**

   - Start by creating the file with all necessary keys in the English locale directory.
   - Ensure the structure is clear and well-organized.

2. **Create Corresponding Files in All Other Locales**

   - Create the same file with the same structure in all other supported locale directories.
   - Translate all keys appropriately for each language.

3. **Update the Loader**

   - If using static imports, update `resources.js` to include the new files.
   - If using dynamic imports, no changes are needed.

4. **Verify Consistency**
   - Ensure all keys present in the English version exist in all other language versions.
   - Check for any missing or extra keys across all locale files.

## Maintaining Consistency Across Locale Files

To ensure all locale files have the same keys and structure:

1. **Use the English File as Reference**

   - The English locale file should be the source of truth for key structure.
   - All other locale files should mirror this structure with translated values.

2. **Regular Audits**

   - Periodically check that all keys in the English files exist in all other locale files.
   - Use simple grep commands to identify missing keys:
     ```bash
     grep -l "key-name" frontend/src/locales/*/file.js
     ```

3. **Automated Checks**

   - Consider implementing automated checks in your CI/CD pipeline to verify consistency.
   - These checks can compare key structures across all locale files.

4. **When Adding New Keys**

   - Always add new keys to ALL locale files simultaneously.
   - Never add a key to just one locale file.

5. **Verification Process**
   - After updating locale files, verify that all locale files have the same structure.
   - Check for any missing keys or sections that might have been overlooked.

## Testing

Ensure `frontend/src/locales/__tests__/resources.test.js` covers the new bundles:

```js
test("fr bundle includes legalTemplates keys", async () => {
  const bundle = await loadLanguageAsync("fr");
  expect(bundle.legalTemplates.someKey).toBeDefined();
});
```

## Notes

- All translations must still use the `useTranslation` hook for runtime lookups.
- Add new translation keys to all locale files to keep translations in sync.
- Reserve `common.js` for generic or cross-feature strings.
