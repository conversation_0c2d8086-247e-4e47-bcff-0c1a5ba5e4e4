export default {
  embeder: {
    allm: "Utilisez le fournisseur d'intégration intégré. Pas de configuration requise !",
    openai:
      "L'option standard pour la plupart des utilisations non commerciales.",
    azure: "L'option entreprise d'OpenAI hébergée sur les services Azure.",
    localai:
      "Exécutez des modèles d'intégration localement sur votre propre machine.",
    ollama:
      "Exécutez des modèles d'intégration localement sur votre propre machine.",
    lmstudio:
      "Découvrez, téléchargez et exécutez en quelques clics des milliers de modèles LLM de pointe.",
    cohere: "Exécutez des modèles d'intégration de pointe de Cohere.",
    voyageai: "Exécutez des modèles d'intégration de pointe de Voyage AI.",
    "generic-openai": "Utilisez un modèle d'intégration OpenAI générique.",
    "default.embedder": "Fournisseur d'intégration par défaut",
    jina: "Modèles d'intégration textuels de Jina AI pour les intégrations multilingues et de haute performance.",
    litellm: "Exécutez des modèles d'intégration de pointe de LiteLLM.",
  },
  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Common strings
    "provider-logo": "Logo de {{provider}}",

    // LMStudio Embedding Options
    lmstudio: {
      "model-label": "Modèle d'intégration LM Studio",
      "max-chunk-length": "Longueur maximale du segment",
      "max-chunk-length-help":
        "Longueur maximale des segments de texte pour l'intégration.",
      "hide-endpoint": "Masquer la saisie manuelle du point de terminaison",
      "show-endpoint": "Afficher la saisie manuelle du point de terminaison",
      "base-url": "URL de base de LM Studio",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help": "Entrez l'URL sur laquelle LM Studio fonctionne.",
      "auto-detect": "Détection automatique",
      "loading-models": "--chargement des modèles disponibles--",
      "enter-url-first": "Entrez d'abord l'URL de LM Studio",
      "model-help":
        "Sélectionnez le modèle LM Studio pour l'intégration. Les modèles se chargeront après avoir saisi une URL LM Studio valide.",
      "loaded-models": "Vos modèles chargés",
    },
    // Ollama Embedding Options
    ollama: {
      "model-label": "Modèle d'intégration Ollama",
      "max-chunk-length": "Longueur maximale du segment",
      "max-chunk-length-help":
        "Longueur maximale des segments de texte pour l'intégration.",
      "hide-endpoint": "Masquer la saisie manuelle du point de terminaison",
      "show-endpoint": "Afficher la saisie manuelle du point de terminaison",
      "base-url": "URL de base d'Ollama",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help": "Entrez l'URL sur laquelle Ollama fonctionne.",
      "auto-detect": "Détection automatique",
      "loading-models": "--chargement des modèles disponibles--",
      "enter-url-first": "Entrez d'abord l'URL d'Ollama",
      "model-help":
        "Sélectionnez le modèle Ollama pour l'intégration. Les modèles se chargeront après avoir saisi une URL d'Ollama valide.",
      "loaded-models": "Vos modèles chargés",
    },
    // LiteLLM Embedding Options
    litellm: {
      "model-label": "Sélection du modèle d'intégration",
      "max-chunk-length": "Longueur maximale du segment",
      "max-chunk-length-help":
        "Longueur maximale des segments de texte pour l'intégration.",
      "api-key": "Clé API",
      optional: "optionnel",
      "api-key-placeholder": "sk-mysecretkey",
      "loading-models": "-- chargement des modèles disponibles --",
      "waiting-url": "-- en attente de l'URL --",
      "loaded-models": "Vos modèles chargés",
      "model-tooltip": "Consultez les modèles d'intégration supportés sur",
      "model-tooltip-link": "la documentation de LiteLLM",
      "model-tooltip-more":
        "pour plus d'informations sur les modèles disponibles.",
    },
    // Cohere Embedding Options
    cohere: {
      "api-key": "Clé API Cohere",
      "api-key-placeholder": "Entrez votre clé API Cohere",
      "model-label": "Sélection du modèle",
      "available-models": "Modèles d'intégration disponibles",
    },
    // Jina Embedding Options
    jina: {
      "api-key": "Clé API Jina",
      "api-key-format": "La clé API Jina doit commencer par 'jina_'",
      "api-key-placeholder": "Entrez votre clé API Jina",
      "api-key-error": "La clé API doit commencer par 'jina_'",
      "model-label": "Sélection du modèle",
      "available-models": "Modèles d'intégration disponibles",
      "embedding-type": "Type d'intégration",
      "available-types": "Types d'intégration disponibles",
      dimensions: "Dimensions",
      "available-dimensions": "Dimensions disponibles",
      task: "Tâche",
      "available-tasks": "Tâches disponibles",
      "late-chunking": "Découpage tardif",
      "late-chunking-help":
        "Activez le découpage tardif pour le traitement des documents",
    },
    // LocalAI Embedding Options
    localai: {
      "model-label": "Nom du modèle d'intégration",
      "hide-endpoint": "Masquer les paramètres avancés",
      "show-endpoint": "Afficher les paramètres avancés",
      "base-url": "URL de base LocalAI",
      "base-url-placeholder": "http://localhost:8080/v1",
      "base-url-help": "Entrez l'URL sur laquelle LocalAI fonctionne.",
      "auto-detect": "Détection automatique",
      "loading-models": "-- chargement des modèles disponibles --",
      "waiting-url": "-- en attente de l'URL --",
      "loaded-models": "Vos modèles chargés",
    },
    // Generic OpenAI-Compatible Embedding Options
    generic: {
      "base-url": "URL de base",
      "base-url-placeholder": "https://api.openai.com/v1",
      "base-url-help":
        "Entrez l'URL de base de votre point d'accès API compatible OpenAI.",
      "model-label": "Modèle d'intégration",
      "model-placeholder":
        "Entrez le nom du modèle (par ex. text-embedding-ada-002)",
      "model-help":
        "Précisez l'identifiant du modèle pour générer des intégrations.",
      "api-key": "Clé API",
      "api-key-placeholder": "sk-mysecretkey",
      "api-key-help": "Entrez votre clé API pour l'authentification.",
    },
    // OpenAI Embedding Options
    openai: {
      "api-key": "Clé API OpenAI",
      "api-key-placeholder": "Entrez votre clé API OpenAI",
      "model-label": "Sélection du modèle",
      "available-models": "Modèles d'intégration disponibles",
    },
    // VoyageAI Embedding Options
    voyageai: {
      "api-key": "Clé API VoyageAI",
      "api-key-placeholder": "Entrez votre clé API VoyageAI",
      "model-label": "Sélection du modèle",
      "available-models": "Modèles d'intégration disponibles",
    },
    // Azure OpenAI Embedding Options
    azureai: {
      "service-endpoint": "Point de terminaison du service Azure OpenAI",
      "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
      "service-endpoint-help":
        "Entrez l'URL de votre point de terminaison du service Azure OpenAI",
      "api-key": "Clé API Azure OpenAI",
      "api-key-placeholder": "Entrez votre clé API Azure OpenAI",
      "api-key-help":
        "Entrez votre clé API Azure OpenAI pour l'authentification",
      "deployment-name": "Nom de déploiement du modèle d'intégration",
      "deployment-name-placeholder":
        "Entrez le nom de déploiement de votre modèle d'intégration Azure OpenAI",
      "deployment-name-help":
        "Le nom de déploiement pour votre modèle d'intégration Azure OpenAI",
    },
    // Native Embedding Options
    native: {
      description:
        "Utilisation du fournisseur d'intégration natif pour le traitement de texte",
    },
  },
  // =========================
  // JINA EMBEDDING
  // =========================
  jina: {
    "api-key": "Clé API Jina",
    "api-key-placeholder": "Entrez votre clé API Jina",
    "api-key-format": "La clé API Jina doit commencer par 'jina_'",
    "model-preference": "Préférence de modèle",
  },

  // =========================
  // OLLAMA EMBEDDING
  // =========================
  ollama: {
    "max-embedding-chunk-length": "Longueur maximale du segment d'intégration",
  },

  // =========================
  // VOYAGEAI EMBEDDING
  // =========================
  voyageai: {
    "api-key": "Clé API VoyageAI",
    "api-key-placeholder": "Entrez votre clé API VoyageAI",
    "model-preference": "Préférence de modèle",
  },
};
