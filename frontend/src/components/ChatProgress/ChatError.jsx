import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Warning } from "@phosphor-icons/react";
import { Button } from "@/components/Button";
import { LuChevronDown } from "react-icons/lu";

export default function ChatError({ error, onDismiss }) {
  const { t } = useTranslation();
  const [showDetails, setShowDetails] = useState(false);

  if (!error) return null;

  return (
    <div
      className="relative flex flex-col gap-4 w-full max-w-[32rem] px-5 pt-5 pb-6 rounded-xl border  bg-red-50 border-red-200"
      role="alert"
      aria-live="assertive"
      aria-atomic="true"
    >
      <div className="flex items-start justify-between gap-4">
        <div className="flex gap-3 w-full">
          <div className="flex flex-col gap-2 w-full">
            <h4 className="flex items-center gap-2 text-lg font-medium">
              <Warning className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
              <span className="pt-0.5">{t("chatProgress.error.title")}</span>
            </h4>
            <p className="font-medium text-muted">
              {t("chatProgress.error.description")}
            </p>
          </div>
        </div>
      </div>

      <div className="flex gap-6 mt-2">
        <Button
          variant="outline"
          size="sm"
          className="w-full [&_svg]:size-4"
          onClick={() => setShowDetails(!showDetails)}
        >
          <>
            {showDetails
              ? t("chatProgress.error.hideDetails")
              : t("chatProgress.error.showDetails")}
            <LuChevronDown className={`${showDetails ? "-rotate-90" : ""}`} />
          </>
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => onDismiss?.()}
          className="w-full"
        >
          {t("chatProgress.error.dismiss")}
        </Button>
      </div>

      {showDetails && (
        <div className="mt-2">
          <p className="font-medium text-sm line-clamp-5 py-2 px-3 h-fit rounded-lg border font-mono bg-white overflow-y-auto">
            {error}
          </p>
        </div>
      )}
    </div>
  );
}
