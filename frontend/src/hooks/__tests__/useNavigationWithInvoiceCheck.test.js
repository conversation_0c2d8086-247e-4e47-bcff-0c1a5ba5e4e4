import { renderHook, act } from "@testing-library/react";
import useNavigationWithInvoiceCheck from "../useNavigationWithInvoiceCheck";

// Mock the translation hook
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key) => {
      const translations = {
        "invoice-reference-navigation.success":
          "Invoice reference cleared successfully",
      };
      return translations[key] || key;
    },
  }),
}));

// Mock the constants utility
jest.mock("@/utils/constants", () => ({
  getActiveReference: jest.fn(),
}));

// Mock localStorage
const mockLocalStorage = {
  removeItem: jest.fn(),
};
Object.defineProperty(window, "localStorage", {
  value: mockLocalStorage,
  writable: true,
});

// Mock showToast
jest.mock("@/utils/toast", () => ({
  __esModule: true,
  default: jest.fn(),
}));

// Mock the settings store
jest.mock("@/stores/settingsStore", () => ({
  useInvoiceLogging: jest.fn(),
}));

// Import mocked modules
const { getActiveReference } = require("@/utils/constants");
const showToast = require("@/utils/toast").default;
const { useInvoiceLogging } = require("@/stores/settingsStore");

describe("useNavigationWithInvoiceCheck", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.removeItem.mockClear();
  });

  it("should initialize with correct default values", () => {
    useInvoiceLogging.mockReturnValue({ invoice: true });
    getActiveReference.mockReturnValue(null);

    const { result } = renderHook(() => useNavigationWithInvoiceCheck());

    expect(result.current.showModal).toBe(false);
    expect(result.current.destinationType).toBe("");
    expect(typeof result.current.checkAndNavigate).toBe("function");
    expect(typeof result.current.handleClearAndContinue).toBe("function");
    expect(typeof result.current.handleKeepAndContinue).toBe("function");
    expect(typeof result.current.handleCloseModal).toBe("function");
  });

  it("should navigate immediately when invoice logging is disabled", () => {
    useInvoiceLogging.mockReturnValue({ invoice: false });
    getActiveReference.mockReturnValue("REF-12345"); // Even with reference, should navigate

    const mockNavigationFn = jest.fn();

    const { result } = renderHook(() => useNavigationWithInvoiceCheck());

    act(() => {
      result.current.checkAndNavigate(mockNavigationFn, "workspace");
    });

    expect(mockNavigationFn).toHaveBeenCalledTimes(1);
    expect(result.current.showModal).toBe(false);
  });

  it("should navigate immediately when no active reference exists", () => {
    useInvoiceLogging.mockReturnValue({ invoice: true });
    getActiveReference.mockReturnValue(null);

    const mockNavigationFn = jest.fn();

    const { result } = renderHook(() => useNavigationWithInvoiceCheck());

    act(() => {
      result.current.checkAndNavigate(mockNavigationFn, "workspace");
    });

    expect(mockNavigationFn).toHaveBeenCalledTimes(1);
    expect(result.current.showModal).toBe(false);
  });

  it("should show modal when active reference exists and invoice logging is enabled", () => {
    useInvoiceLogging.mockReturnValue({ invoice: true });
    getActiveReference.mockReturnValue("REF-12345");

    const { result } = renderHook(() => useNavigationWithInvoiceCheck());
    const mockNavigationFn = jest.fn();

    act(() => {
      result.current.checkAndNavigate(mockNavigationFn, "workspace");
    });

    // If the hook is working correctly, navigation should not be called and modal should show
    expect(result.current.showModal).toBe(true);
    expect(result.current.destinationType).toBe("workspace");
    // Note: We'll check navigation calls in the action tests below
  });

  it("should clear reference and navigate when handleClearAndContinue is called", () => {
    useInvoiceLogging.mockReturnValue({ invoice: true });
    getActiveReference.mockReturnValue("REF-12345");

    const { result } = renderHook(() => useNavigationWithInvoiceCheck());
    const mockNavigationFn = jest.fn();

    // First, trigger the modal
    act(() => {
      result.current.checkAndNavigate(mockNavigationFn, "workspace");
    });

    expect(result.current.showModal).toBe(true);

    // Then clear and continue
    act(() => {
      result.current.handleClearAndContinue();
    });

    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith("activeReference");
    expect(showToast).toHaveBeenCalledWith(
      "Invoice reference cleared successfully",
      "success"
    );
    expect(result.current.showModal).toBe(false);
  });

  it("should navigate without clearing when handleKeepAndContinue is called", () => {
    useInvoiceLogging.mockReturnValue({ invoice: true });
    getActiveReference.mockReturnValue("REF-12345");

    const { result } = renderHook(() => useNavigationWithInvoiceCheck());
    const mockNavigationFn = jest.fn();

    // First, trigger the modal
    act(() => {
      result.current.checkAndNavigate(mockNavigationFn, "workspace");
    });

    expect(result.current.showModal).toBe(true);

    // Then keep and continue
    act(() => {
      result.current.handleKeepAndContinue();
    });

    expect(mockLocalStorage.removeItem).not.toHaveBeenCalled();
    expect(showToast).not.toHaveBeenCalled();
    expect(result.current.showModal).toBe(false);
  });

  it("should close modal without navigating when handleCloseModal is called", () => {
    useInvoiceLogging.mockReturnValue({ invoice: true });
    getActiveReference.mockReturnValue("REF-12345");

    const { result } = renderHook(() => useNavigationWithInvoiceCheck());
    const mockNavigationFn = jest.fn();

    // First, trigger the modal
    act(() => {
      result.current.checkAndNavigate(mockNavigationFn, "workspace");
    });

    expect(result.current.showModal).toBe(true);

    // Then close modal
    act(() => {
      result.current.handleCloseModal();
    });

    expect(mockLocalStorage.removeItem).not.toHaveBeenCalled();
    expect(showToast).not.toHaveBeenCalled();
    expect(result.current.showModal).toBe(false);
  });

  it("should handle different destination types", () => {
    useInvoiceLogging.mockReturnValue({ invoice: true });
    getActiveReference.mockReturnValue("REF-12345");

    const { result } = renderHook(() => useNavigationWithInvoiceCheck());
    const mockNavigationFn = jest.fn();

    // Test thread destination
    act(() => {
      result.current.checkAndNavigate(mockNavigationFn, "thread");
    });
    expect(result.current.destinationType).toBe("thread");

    // Reset modal
    act(() => {
      result.current.handleCloseModal();
    });

    // Test module destination
    act(() => {
      result.current.checkAndNavigate(mockNavigationFn, "module");
    });
    expect(result.current.destinationType).toBe("module");
  });
});
