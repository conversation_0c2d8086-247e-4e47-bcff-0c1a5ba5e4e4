import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { TbSettings } from "react-icons/tb";
import { Button } from "@/components/Button";
import { Tooltip } from "react-tooltip";
import SystemPromptEditModal from "../SystemPromptEditModal";
import useUser from "@/hooks/useUser";

export default function SystemPromptEditButton({ workspace }) {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { user } = useUser();

  const hasUserCustomPrompt = user?.custom_system_prompt;

  return (
    <>
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={() => setIsModalOpen(true)}
        data-tooltip-id="system-prompt-edit"
        data-tooltip-content={t("system-prompt-edit.button-tooltip")}
        className={hasUserCustomPrompt ? "bg-secondary-hover" : ""}
        aria-label={t("system-prompt-edit.button-label")}
      >
        <TbSettings />
        {t("system-prompt-edit.button-label")}
      </Button>
      <Tooltip id="system-prompt-edit" delayShow={300} className="tooltip" />

      <SystemPromptEditModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        workspace={workspace}
      />
    </>
  );
}
