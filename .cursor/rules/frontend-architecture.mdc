---
description: Guidelines for frontend architecture and implementation
globs: frontend/**/*.js,frontend/**/*.jsx
alwaysApply: false
---

# Frontend Architecture

## Entry Points

- `frontend/index.html`: Main HTML entry for the React app
- `frontend/src/main.jsx`: Root React component, sets up Router and providers
- `frontend/src/App.jsx`: Main application component

## Key Directories

- `frontend/src/components/`: Reusable UI components
- `frontend/src/pages/`: Route-based page components
- `frontend/src/context/`: React context providers
- `frontend/src/hooks/`: Custom React hooks
- `frontend/src/utils/`: Utility functions
- `frontend/src/models/`: Data models and API calls
- `frontend/src/locales/`: Internationalization files
- `frontend/src/media/`: Images, icons, and other media

## Global Contexts

- `AuthContext.jsx`: User authentication context
- `LogoContext.jsx`: Logo image context

## Key Components

### Chat Components

- `WorkspaceChat/`: Workspace-specific chat interface

### Configuration Components

- `EmbeddingSelection/`: Embedding model selection
- `LLMSelection/`: LLM provider selection
- `VectorDBSelection/`: Vector database selection
- `TextToSpeech/`: Text-to-speech configuration
- `TranscriptionSelection/`: Transcription configuration

### Workspace Components

- `Sidebar/`: Workspace listing and navigation
- `Modals/ManageWorkspace/`: Workspace management
- `HeaderWorkspace/`: Workspace header

### Settings Pages

- `GeneralSettings/`: System-wide settings
- `WorkspaceSettings/`: Workspace-specific settings
- `Admin/`: Admin console

## Page Structure

- `Main/`: Main landing page
- `Login/`: Authentication page
- `OnboardingFlow/`: New user onboarding
- `Invite/`: User invitation handling

## Best Practices

- Follow the established component structure
- Use appropriate contexts for state management
- Implement proper error handling and loading states
- Consider performance implications of component rendering
- Document complex component logic
- Test components with various inputs and edge cases
- Use appropriate hooks for data fetching and state management
