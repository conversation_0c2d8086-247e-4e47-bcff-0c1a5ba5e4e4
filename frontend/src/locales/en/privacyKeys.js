export default {
  // =========================
  // PRIVACY & DATA-HANDLING
  // =========================
  privacy: {
    title: "Privacy & Data-Handling",
    description:
      "This is your configuration for how connected third party providers and our platform handle your data.",
    llm: "LLM Selection",
    embedding: "Embedding Preference",
    vector: "Vector Database",
    anonymous: "Anonymous Telemetry Enabled",
    "desc-event": "All events do not record IP-address and contain",
    "desc-id": "no identifying",
    "desc-cont":
      "content, settings, chats, or other non-usage based information. To see the list of event tags collected you can look on",
    "desc-git": "Github here",
    "desc-end":
      "We are dedicated to building the best solution for integrating AI and documents privately and securely. If you do decide to turn off telemetry all we ask is to consider sending us feedback and thoughts so that we can continue to improve the platform for you",
  },
  // =========================
  // LLM SELECTION PRIVACY
  // =========================
  "llm-selection-privacy": {
    openai: {
      description: [
        "Your chats will not be used for training",
        "Your prompts and document text used in response creation are visible to OpenAI",
      ],
    },
    azure: {
      description: [
        "Your chats will not be used for training",
        "Your text and embedding text are not visible to OpenAI or Microsoft",
      ],
    },
    anthropic: {
      description: [
        "Your chats will not be used for training",
        "Your prompts and document text used in response creation are visible to Anthropic",
      ],
    },
    gemini: {
      description: [
        "Your chats are de-identified and used in training",
        "Your prompts and document text used in response creation are visible to Google",
      ],
    },
    lmstudio: {
      description: [
        "Your model and chats are only accessible on the server running LMStudio",
      ],
    },
    localai: {
      description: [
        "Your model and chats are only accessible on the server running LocalAI",
      ],
    },
    ollama: {
      description: [
        "Your model and chats are only accessible on the machine running Ollama models",
      ],
    },
    native: {
      description: [
        "Your model and chats are only accessible on this instance",
      ],
    },
    togetherai: {
      description: [
        "Your chats will not be used for training",
        "Your prompts and document text used in response creation are visible to TogetherAI",
      ],
    },
    mistral: {
      description: [
        "Your prompts and document text used in response creation are visible to Mistral",
      ],
    },
    huggingface: {
      description: [
        "Your prompts and document text used in response are sent to your HuggingFace managed endpoint",
      ],
    },
    perplexity: {
      description: [
        "Your chats will not be used for training",
        "Your prompts and document text used in response creation are visible to Perplexity AI",
      ],
    },
    openrouter: {
      description: [
        "Your chats will not be used for training",
        "Your prompts and document text used in response creation are visible to OpenRouter",
      ],
    },
    groq: {
      description: [
        "Your chats will not be used for training",
        "Your prompts and document text used in response creation are visible to Groq",
      ],
    },
    koboldcpp: {
      description: [
        "Your model and chats are only accessible on the server running KoboldCPP",
      ],
    },
    textgenwebui: {
      description: [
        "Your model and chats are only accessible on the server running the Oobabooga Text Generation Web UI",
      ],
    },
    "generic-openai": {
      description: [
        "Data is shared according to the terms of service applicable with your generic endpoint provider.",
      ],
    },
    cohere: {
      description: [
        "Data is shared according to the terms of service of cohere.com and your localities privacy laws.",
      ],
    },
    litellm: {
      description: [
        "Your model and chats are only accessible on the server running LiteLLM",
      ],
    },
  },

  // =========================
  // VECTOR DATABASE PRIVACY
  // =========================
  "vector-db-privacy": {
    chroma: {
      description: [
        "Your vectors and document text are stored on your Chroma instance",
        "Access to your instance is managed by you",
      ],
    },
    pinecone: {
      description: [
        "Your vectors and document text are stored on Pinecone's servers",
        "Access to your data is managed by Pinecone",
      ],
    },
    qdrant: {
      description: [
        "Your vectors and document text are stored on your Qdrant instance (cloud or self-hosted)",
      ],
    },
    weaviate: {
      description: [
        "Your vectors and document text are stored on your Weaviate instance (cloud or self-hosted)",
      ],
    },
    milvus: {
      description: [
        "Your vectors and document text are stored on your Milvus instance (cloud or self-hosted)",
      ],
    },
    zilliz: {
      description: [
        "Your vectors and document text are stored on your Zilliz cloud cluster.",
      ],
    },
    astra: {
      description: [
        "Your vectors and document text are stored on your cloud AstraDB database.",
      ],
    },
    lancedb: {
      description: [
        "Your vectors and document text are stored privately on this instance of the platform",
      ],
    },
  },

  // =========================
  // EMBEDDING ENGINE PRIVACY
  // =========================
  "embedding-engine-privacy": {
    native: {
      description: [
        "Your document text is embedded privately on this instance of the platform",
      ],
    },
    openai: {
      description: [
        "Your document text is sent to OpenAI servers",
        "Your documents are not used for training",
      ],
    },
    azure: {
      description: [
        "Your document text is sent to your Microsoft Azure service",
        "Your documents are not used for training",
      ],
    },
    localai: {
      description: [
        "Your document text is embedded privately on the server running LocalAI",
      ],
    },
    ollama: {
      description: [
        "Your document text is embedded privately on the server running Ollama",
      ],
    },
    lmstudio: {
      description: [
        "Your document text is embedded privately on the server running LMStudio",
      ],
    },
    cohere: {
      description: [
        "Data is shared according to the terms of service of cohere.com and your localities privacy laws.",
      ],
    },
    voyageai: {
      description: [
        "Data sent to Voyage AI's servers is shared according to the terms of service of voyageai.com.",
      ],
    },
  },
};
