import useProgressStore from "@/stores/progressStore";

export default function useThreadProgress(threadSlug) {
  const safeThreadSlug =
    typeof threadSlug === "string" && threadSlug.trim()
      ? threadSlug.trim()
      : "";

  const threadState = useProgressStore((state) =>
    safeThreadSlug ? state.threads.get(safeThreadSlug) : null
  );

  const start = (totalSteps = 7, flowType = null) => {
    if (!safeThreadSlug) return;
    useProgressStore
      .getState()
      .startProcess(safeThreadSlug, totalSteps, flowType);
  };

  const update = (event) => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().updateProgress(event, safeThreadSlug);
  };

  const finish = () => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().finishProcess(safeThreadSlug);
  };

  const cancel = () => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().cancelProcess(safeThreadSlug);
  };

  const setError = (errorMessage) => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().setError(safeThreadSlug, errorMessage);
  };

  const clearError = () => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().clearError(safeThreadSlug);
  };

  const forceCleanup = () => {
    if (!safeThreadSlug) return;
    useProgressStore.getState().forceCleanup(safeThreadSlug);
  };

  const getAbortSignal = () => {
    if (!safeThreadSlug) return null;
    const controller = useProgressStore
      .getState()
      .getAbortController(safeThreadSlug);
    return controller?.signal || null;
  };

  return {
    isActive: threadState?.isActive || false,
    currentStep: threadState?.currentStep || 1,
    totalSteps: threadState?.totalSteps || 7,
    startTime: threadState?.startTime || null,
    flowType: threadState?.flowType || null,
    currentSubStep: threadState?.currentSubStep || null,
    stepStatus: threadState?.stepStatus || "pending",
    stepMessage: threadState?.stepMessage || null,
    stepDetails: threadState?.stepDetails || [],
    error: threadState?.error || null,

    start,
    update,
    finish,
    cancel,
    setError,
    clearError,
    forceCleanup,
    getAbortSignal,
  };
}

// Clean selector hook to check if a process is active for a specific thread
export function useIsProcessActive(threadSlug) {
  const safeThreadSlug =
    typeof threadSlug === "string" && threadSlug.trim()
      ? threadSlug.trim()
      : "";

  return useProgressStore((state) => {
    if (!safeThreadSlug) return false;
    const threadState = state.threads.get(safeThreadSlug);
    return threadState?.isActive || false;
  });
}
