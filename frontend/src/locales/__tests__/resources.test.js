import {
  initializeLanguageAsync,
  loadLanguageAsync,
  resources,
  supportedLanguageCodes,
} from "../resources";

// Helper to clear localStorage between tests
beforeEach(() => {
  localStorage.clear();
  jest.restoreAllMocks();
});

describe("loadLanguageAsync (static bundles)", () => {
  test("returns null for default language en", async () => {
    const result = await loadLanguageAsync("en");
    expect(result).toBeNull();
  });

  supportedLanguageCodes
    .filter((lang) => lang !== "en")
    .forEach((lang) => {
      test(`returns static bundle for language ${lang}`, async () => {
        const bundle = await loadLanguageAsync(lang);
        expect(bundle).toBe(resources[lang]?.common);
        expect(bundle).toBeDefined();
      });
    });

  test("returns null for unsupported language", async () => {
    const result = await loadLanguageAsync("xx");
    expect(result).toBeNull();
  });
});

describe("initializeLanguageAsync (system default)", () => {
  test("uses existing userLanguage from localStorage when present and not en", async () => {
    localStorage.setItem("language", "fr");
    const result = await initializeLanguageAsync();
    expect(result).toBe("fr");
  });

  test("returns en when userLanguage is exactly en", async () => {
    localStorage.setItem("language", "en");
    const result = await initializeLanguageAsync();
    expect(result).toBe("en");
  });

  test("fetches admin default language when no userLanguage and uses it", async () => {
    // No existing language in localStorage
    localStorage.removeItem("language");
    // Mock fetch to return JSON with language 'de'
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: async () => ({ results: { language: "de" } }),
    });
    const result = await initializeLanguageAsync();
    expect(global.fetch).toHaveBeenCalledWith(
      `${window.location.origin}/api/setup-complete`
    );
    expect(localStorage.getItem("language")).toBe("de");
    expect(result).toBe("de");
  });

  test("defaults to en when fetch returns en", async () => {
    localStorage.removeItem("language");
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: async () => ({ results: { language: "en" } }),
    });
    const result = await initializeLanguageAsync();
    expect(localStorage.getItem("language")).toBe("en");
    expect(result).toBe("en");
  });

  test("fallbacks to en and sets localStorage when fetch fails or returns non-ok", async () => {
    localStorage.removeItem("language");
    global.fetch = jest.fn().mockRejectedValue(new Error("Network error"));
    const result = await initializeLanguageAsync();
    expect(localStorage.getItem("language")).toBe("en");
    expect(result).toBe("en");
  });
});
