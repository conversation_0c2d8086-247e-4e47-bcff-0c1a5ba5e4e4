# Case Document Builder (CDB) - Stream Documentation

**Important Note:** The filename `streamCDB.md` is referenced by a backend API endpoint (`GET /api/system/cdb-documentation`) that serves its content. This endpoint is defined in `server/endpoints/system.js`. Do not rename this file or alter the endpoint without updating both locations to prevent breaking the dynamic documentation fetching for legal task prompt generation.

The Streamlined Case Document Builder (StreamCDB) is a powerful feature within the ISTLegal platform designed to assist legal professionals by automating the generation and drafting of various legal documents. It leverages Large Language Models (LLMs) and a sophisticated multi-stage processing workflow to analyze provided source documents and produce structured, relevant, and coherent output based on a specified legal task.

## Core Functionality & Workflow Stages

StreamCDB orchestrates a refined, multi-step pipeline to transform user tasks and source documents into complete legal drafts. The pipeline varies by flow type:

- **Main Document Flow & No Main Document Flow**: 7-step pipeline
- **Reference Files Comparison Flow**: 5-step pipeline

### Main Document Flow & No Main Document Flow (7 Steps)

1.  **Stage 1: Create List-of-Sections**:

    - **Goal**: Draft a structured JSON array detailing every section of the final document.
    - **Inputs**: The legal task description, content from the _main document_ (if `mainDoc` flow), document _summaries_ (if `noMainDoc` flow), and any optional custom instructions.
    - **Process**: An LLM call generates the section list, where each entry includes an index, title, description, relevant circumstances, and legal issues to address.
    - **Output**: `document-sections-[uuid].json` in `server/storage/document-builder/`.
    - **Helper**: `createSectionList()` in `server/utils/chats/streamCDB.js`.

2.  **Stage 2: Review & Describe Source Documents**:

    - **Goal**: For every uploaded/provided source document, generate a concise description and determine its relevance to the legal task.
    - **Process**: Iterates through each document, making an LLM call to generate its description and a true/false relevance flag.
    - **Output**: `document-descriptions-[uuid].json` containing an array of these document metadata objects.

3.  **Stage 3: Map Documents to Sections**:

    - **Goal**: Map each section (from Stage 1) to a relevant subset of the source documents (from Stage 2).
    - **Process**: This involves determining which documents contain information pertinent to each planned section. Initially, this might use pre-calculated per-file section indices. If these are not available or insufficient, a small LLM call might be made.
    - **Output**: The mapping information is typically merged back into `document-sections-[uuid].json`.
    - **Helper**: `assignDocsToSections()` in `server/utils/chats/streamCDB.js`.

4.  **Stage 4: Identify Legal Issues per Section**:

    - **Goal**: For each section defined in Stage 1, generate a list of specific legal issues or questions that need to be addressed during drafting.
    - **Process**: An LLM call is made for each section, considering its title, description, and allocated documents.
    - **Output**: The list of legal issues is stored within each section's entry in `document-sections-[uuid].json`.

5.  **Stage 5: Fetch/Generate Legal Memos (Optional)**:

    - **Goal**: For each section, retrieve or generate legal memoranda concerning the legal issues identified in Stage 4.
    - **Process**: This can involve querying linked "Legal Q&A" workspaces or making dedicated LLM calls to generate memo content based on the identified issues and relevant documents.
    - **Output**: Memo text is primarily held in memory for use in the next stage; it might not always be written to a separate temporary file.

6.  **Stage 6: Draft Section Content**:

    - **Goal**: Compose the full text for each document section.
    - **Process**: Iteratively, for each section:
      - An LLM call is made, providing context such as the main document (if any), the section's metadata (title, description, issues from Stage 4), content from documents allocated in Stage 3, relevant legal memos (from Stage 5), and potentially content from neighboring (already drafted) sections to ensure flow.
      - The generated text for the section is streamed to the client as it completes.
    - **Output**: Section text is streamed and collected for final assembly.

7.  **Stage 7: Finalize & Cleanup**:
    - **Goal**: Assemble the complete document and clean up temporary files.
    - **Process**: All drafted sections are concatenated. The final combined document is emitted via Server-Sent Events (SSE). All temporary files created in `server/storage/document-builder/` during the process are purged.
    - **Helper**: `purgeDocumentBuilder()` in `server/utils/files.js`.

### Reference Files Comparison Flow (5 Steps)

1.  **Stage 1: Process Reference Files**:

    - **Goal**: Analyze and summarize reference files containing rules, regulations, or standards.
    - **Process**: Each reference file is processed to extract key rules and requirements relevant to the legal task.
    - **Output**: Descriptions and summaries of reference standards.

2.  **Stage 2: Process Review Files**:

    - **Goal**: Analyze review files that need to be evaluated for compliance.
    - **Process**: Each review file is analyzed to identify potential compliance issues or areas that need to be checked against the reference standards.
    - **Output**: Descriptions and analysis of review documents.

3.  **Stage 3: Generate Section List**:

    - **Goal**: Create a structured compliance report outline based on the reference and review file analysis.
    - **Process**: An LLM call generates a section list focused on compliance areas, mapping relevant documents to each section.
    - **Output**: `document-sections-[uuid].json` with compliance-focused sections.

4.  **Stage 4: Draft Compliance Sections**:

    - **Goal**: Draft each section of the compliance report.
    - **Process**: For each section, compare review documents against reference standards, highlighting violations, gaps, and areas of concern with specific document references.
    - **Output**: Section content is generated and streamed to the client.

5.  **Stage 5: Finalize Compliance Report**:
    - **Goal**: Assemble the complete compliance report and clean up temporary files.
    - **Process**: All drafted sections are combined into a comprehensive compliance report. Temporary files are cleaned up.
    - **Output**: Final compliance report delivered via Server-Sent Events (SSE).

Throughout this pipeline, progress events are sent to the frontend, allowing the user to track the CDB's operation in real-time.

## Key Flows

StreamCDB operates through three distinct flows, each optimized for different types of legal document drafting scenarios. The choice of flow significantly influences Stage 1 (Create List-of-Sections) and the relevance of Stage 3 (Identify Main Document).

### 1. Main Document Flow (`mainDoc` flow)

- **Trigger**: Activated when the legal task inherently revolves around a single, primary source document, often specified by the user.
- **Description**: This flow uses the specified main document as the primary basis for generating the structure (section list) of the output document. Other documents serve as supporting material.
- **Typical Use Cases**:
  - Analyzing a specific contract and drafting a response or addendum.
  - Reviewing a particular court filing and preparing a counter-argument.
  - Summarizing or elaborating on a key piece of evidence.
- **Process Highlights**:
  - **Stage 1 (List-of-Sections)**: Primarily analyzes the main document's content. Prompts like `DEFAULT_SECTION_LIST_FROM_MAIN` (from `server/utils/chats/prompts/legalDrafting.js`) are key here.
  - **Stage 3 (Identify Main Document)**: This stage is central, confirming or identifying the main document.
  - Subsequent stages use the main document as a primary reference, augmented by other relevant sources.

### 2. No Main Document Flow (`noMainDoc` flow)

- **Trigger**: Used when the task doesn't center on one primary document, but rather involves synthesizing information from multiple sources or creating a new structure thematically.
- **Description**: Generates the document structure by analyzing summaries of all relevant documents (from Stage 2) in conjunction with the overall legal task description.
- **Typical Use Cases**:
  - Drafting a legal memo from multiple case files and statutes.
  - Creating a report from various pieces of evidence.
  - Generating a summary of arguments from different legal opinions.
- **Process Highlights**:
  - **Stage 1 (List-of-Sections)**: Relies on summaries of all relevant documents and the legal task itself. Prompts like `DEFAULT_SECTION_LIST_FROM_SUMMARIES` (from `server/utils/chats/prompts/legalDrafting.js`) are used.
  - **Stage 3 (Identify Main Document)**: This stage is less critical or skipped, as there isn't a single designated main document.
  - All relevant documents contribute more broadly to the content of generated sections.

### 3. Reference Files Comparison Flow (`referenceFiles` flow)

- **Trigger**: Used when the task involves comparing a set of documents against reference rules, regulations, or standards to identify compliance issues or breaches.
- **Description**: This flow categorizes documents into two groups: reference files (containing rules, regulations, or standards) and review files (documents to be analyzed for compliance). It generates a compliance report by comparing the review files against the reference standards.
- **Typical Use Cases**:
  - Regulatory compliance audits comparing company policies against legal requirements.
  - Contract review against standard terms and conditions.
  - Quality assurance reviews comparing procedures against established standards.
  - Due diligence reviews comparing documents against regulatory frameworks.
- **Process Highlights**:
  - **Stage 1 (Document Processing)**: Documents are categorized into reference files and review files based on user selection.
  - **Stage 2 (Document Analysis)**: Reference files are analyzed to extract rules and standards, while review files are analyzed for potential compliance issues.
  - **Stage 3 (Section Generation)**: Creates a structured report format that systematically compares review documents against reference standards.
  - **Stage 4 (Compliance Drafting)**: Each section focuses on specific compliance areas, highlighting violations, gaps, or areas of concern with specific document references.
  - **Stage 5 (Report Assembly)**: Combines all sections into a comprehensive compliance report.

## Prompt Customization

Many prompts used in the StreamCDB pipeline are customizable via System Settings, allowing administrators to tailor LLM behavior. Defaults are defined in `server/utils/chats/prompts/legalDrafting.js`.

Key customizable prompts for the current pipeline include:

**For Main Document Flow & No Main Document Flow (7 steps):**

- `DEFAULT_DOCUMENT_SUMMARY` (for Stage 2)
- `DEFAULT_DOCUMENT_RELEVANCE` (for Stage 2)
- `DEFAULT_SELECT_MAIN_DOCUMENT` (used in Stage 3, mainly for `mainDoc` flow)
- `DEFAULT_SECTION_LIST_FROM_MAIN` (for Stage 1 in `mainDoc` flow)
- `DEFAULT_SECTION_LIST_FROM_SUMMARIES` (for Stage 1 in `noMainDoc` flow)
- `DEFAULT_SECTION_DRAFTING` (for Stage 6)
- `DEFAULT_SECTION_LEGAL_ISSUES` (for Stage 4)
- `DEFAULT_MEMO_CREATION` (for Stage 5)

**For Reference Files Comparison Flow (5 steps):**

- `DEFAULT_DOCUMENT_SUMMARY` (for Stages 1 & 2)
- `DEFAULT_SECTION_LIST_FROM_SUMMARIES` (for Stage 3)
- `DEFAULT_SECTION_DRAFTING` (for Stage 4, with compliance-specific context)

**Note on Legacy Prompts**: Older prompt settings like `action_plan_prompt`, `action_steps_system_prompt`, `final_steps_system_prompt`, etc., (and their `Combine` versions) are largely related to a previous CDB implementation. While some, like `action_plan_prompt` or `legal_task_prompt`, might be referenced for initial task interpretation by `streamCDB.js` or by UI features like the "Legal Task User Prompt Generator", they do **not** directly control the core section-by-section drafting logic of the current pipeline. Configuration should focus on the `DEFAULT_` prompts listed above for controlling the main drafting process.

## Integration with Legal Task Prompt Generation

When a user generates a prompt for a new legal task, the system provides context to the LLM about StreamCDB. This context includes:

- An overview of StreamCDB (this document).
- A list of all _default_ prompts used by StreamCDB (appended programmatically to this overview when served via API).
- An explicit statement indicating whether the task is intended for the "Main Document Flow", "No Main Document Flow", or "Reference Files Comparison Flow".
- A list of the _currently active_ (customized or default) prompt templates relevant to the specified flow and its key stages.

This comprehensive context helps the LLM generate a high-quality, effective initial prompt for the user that is well-suited for the StreamCDB system.
