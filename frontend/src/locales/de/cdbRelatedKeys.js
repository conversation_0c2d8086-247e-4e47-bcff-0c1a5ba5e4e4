export default {
  // =========================
  // DOCUMENT BUILDER SETTINGS
  // =========================
  "document-builder": {
    title: "Dokumentenersteller-Einstellungen",
    description: "Steuern Sie die Einstellungen für den Dokumentenersteller.",
    "override-prompt-placeholder":
      "Prompt eingeben, um den Standard-System-Prompt zu überschreiben",
    saving: "Speichern...",
    save: "Prompt-Einstellungen speichern",
    "toast-success": "Dokumentenersteller-Prompts erfolgreich gespeichert.",
    "toast-fail": "Fehler beim Speichern der Dokumentenersteller-Prompts.",

    // =========================
    // LEGAL DRAFTING PROMPTS
    // =========================
    prompts: {
      group: {
        document_summary: {
          title: "Dokumentzusammenfassung-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Dokumentzusammenfassung.",
        },
        document_relevance: {
          title: "Dokumentrelevanz-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Dokumentrelevanz.",
        },
        section_drafting: {
          title: "Abschnittserstellung-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Abschnittserstellung.",
        },
        section_legal_issues: {
          title: "Rechtliche Probleme für Abschnitt-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für rechtliche Probleme in Abschnitten.",
        },
        memo_creation: {
          title: "Memo-Erstellung-Prompts",
          description: "Konfigurieren Sie Prompts für die Memo-Erstellung.",
        },
        section_index: {
          title: "Abschnittsindex-Prompts",
          description: "Konfigurieren Sie Prompts für den Abschnittsindex.",
        },
        select_main_document: {
          title: "Hauptdokument-Auswahl-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Hauptdokument-Auswahl.",
        },
        section_list_from_main: {
          title: "Abschnittsliste aus Hauptdokument-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Abschnittsliste aus dem Hauptdokument.",
        },
        section_list_from_summaries: {
          title: "Abschnittsliste aus Zusammenfassungen-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Abschnittsliste aus Zusammenfassungen.",
        },
        reference_files_description: {
          title: "Referenzdatei-Beschreibungs-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Referenzdatei-Beschreibung.",
        },
        review_files_description: {
          title: "Überprüfungsdatei-Beschreibungs-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Überprüfungsdatei-Beschreibung.",
        },
        reference_review_sections: {
          title: "Referenz-/Überprüfungsabschnitt-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für Referenz-/Überprüfungsabschnitte.",
        },
      },
      // Document Summary
      "document-summary-system-label": "Dokumentzusammenfassung (System)",
      "document-summary-system-description":
        "System-Prompt zur Anweisung der KI, wie der Inhalt eines Dokuments und seine Relevanz für eine juristische Aufgabe zusammengefasst werden soll.",
      "document-summary-user-label": "Dokumentzusammenfassung (Benutzer)",
      "document-summary-user-description":
        "Benutzer-Prompt-Vorlage zur Erstellung einer detaillierten Zusammenfassung des Dokumentinhalts in Bezug auf eine bestimmte juristische Aufgabe.",
      // Reference Files Description Prompts
      "reference-files-description-system-label":
        "Referenzdatei-Beschreibung (System)",
      "reference-files-description-system-description":
        "System-Prompt zur Beschreibung von Referenzdateien.",
      "reference-files-description-user-label":
        "Referenzdatei-Beschreibung (Benutzer)",
      "reference-files-description-user-description":
        "Benutzer-Prompt-Vorlage zur Beschreibung von Referenzdateien.",

      // Document Relevance
      "document-relevance-system-label": "Dokumentrelevanz (System)",
      "document-relevance-system-description":
        "System-Prompt zur Bewertung, ob ein Dokument für eine juristische Aufgabe relevant ist, wobei eine Wahr/Falsch-Antwort erwartet wird.",
      "document-relevance-user-label": "Dokumentrelevanz (Benutzer)",
      "document-relevance-user-description":
        "Benutzer-Prompt-Vorlage zur Überprüfung, ob der Dokumentinhalt für eine bestimmte juristische Aufgabe relevant ist.",

      // Section Drafting
      "section-drafting-system-label": "Abschnittserstellung (System)",
      "section-drafting-system-description":
        "System-Prompt zur Erstellung eines einzelnen Dokumentabschnitts in professionellem juristischen Stil unter Verwendung spezifizierter Dokumente und Kontext.",
      "section-drafting-user-label": "Abschnittserstellung (Benutzer)",
      "section-drafting-user-description":
        "Benutzer-Prompt-Vorlage zur Erstellung eines bestimmten Abschnitts eines juristischen Dokuments unter Berücksichtigung von Titel, Aufgabe, Quelldokumenten und benachbarten Abschnitten.",

      // Section Legal Issues
      "section-legal-issues-system-label":
        "Identifizierung rechtlicher Probleme für Abschnitt (System)",
      "section-legal-issues-system-description":
        "System-Prompt zur Identifizierung spezifischer rechtlicher Themen, für die Sachinformationen zur Unterstützung bei der Erstellung eines Dokumentabschnitts abgerufen werden sollten.",
      "section-legal-issues-user-label":
        "Identifizierung rechtlicher Probleme für Abschnitt (Benutzer)",
      "section-legal-issues-user-description":
        "Benutzer-Prompt-Vorlage zum Auflisten rechtlicher Themen oder Datenpunkte zum Abrufen von Hintergrundinformationen, die für einen bestimmten Dokumentabschnitt und eine juristische Aufgabe relevant sind.",

      // Memo Creation
      "memo-creation-template-label": "Standard-Memo-Erstellungsvorlage",
      "memo-creation-template-description":
        "Prompt-Vorlage zur Erstellung eines juristischen Memorandums zu einem bestimmten rechtlichen Problem unter Berücksichtigung der bereitgestellten Dokumente und des Aufgabenkontexts.",

      // Section Index
      "section-index-system-label": "Abschnittsindex (System)",
      "section-index-system-description":
        "System-Prompt zur Erstellung eines strukturierten Index von Abschnitten für ein juristisches Dokument.",

      // Select Main Document
      "select-main-document-system-label": "Hauptdokument auswählen (System)",
      "select-main-document-system-description":
        "System-Prompt zur Identifizierung des relevantesten Hauptdokuments für eine juristische Aufgabe aus mehreren Dokumentzusammenfassungen.",
      "select-main-document-user-label": "Hauptdokument auswählen (Benutzer)",
      "select-main-document-user-description":
        "Benutzer-Prompt-Vorlage zur Identifizierung des Hauptdokuments für eine juristische Aufgabe anhand von Zusammenfassungen mehrerer Dokumente.",

      // Section List from Main Document
      "section-list-from-main-system-label":
        "Abschnittsliste aus Hauptdokument (System)",
      "section-list-from-main-system-description":
        "System-Prompt zur Erstellung einer JSON-strukturierten Liste von Abschnitten für ein juristisches Dokument basierend auf dem Inhalt des Hauptdokuments und der juristischen Aufgabe.",
      "section-list-from-main-user-label":
        "Abschnittsliste aus Hauptdokument (Benutzer)",
      "section-list-from-main-user-description":
        "Benutzer-Prompt-Vorlage zur Bereitstellung der juristischen Aufgabe und des Hauptdokumentinhalts zur Generierung einer Abschnittsliste.",

      // Section List from Summaries
      "section-list-from-summaries-system-label":
        "Abschnittsliste aus Zusammenfassungen (System)",
      "section-list-from-summaries-system-description":
        "System-Prompt zur Erstellung einer JSON-strukturierten Liste von Abschnitten basierend auf Dokumentzusammenfassungen und der juristischen Aufgabe, wenn kein Hauptdokument existiert.",
      "section-list-from-summaries-user-label":
        "Abschnittsliste aus Zusammenfassungen (Benutzer)",
      "section-list-from-summaries-user-description":
        "Benutzer-Prompt-Vorlage zur Bereitstellung der juristischen Aufgabe und Dokumentzusammenfassungen zur Generierung einer Abschnittsliste, wenn kein Hauptdokument existiert.",
      // Review Files Description Prompts
      "review-files-description-system-label":
        "Überprüfungsdatei-Beschreibung (System)",
      "review-files-description-system-description":
        "System-Prompt zur Beschreibung von Überprüfungsdateien.",
      "review-files-description-user-label":
        "Überprüfungsdatei-Beschreibung (Benutzer)",
      "review-files-description-user-description":
        "Benutzer-Prompt-Vorlage zur Beschreibung von Überprüfungsdateien.",

      // Reference/Review Sections Prompts
      "reference-review-sections-system-label":
        "Referenz-/Überprüfungsabschnitte (System)",
      "reference-review-sections-system-description":
        "System-Prompt zur Definition von Referenz-/Überprüfungsabschnitten.",
      "reference-review-sections-user-label":
        "Referenz-/Überprüfungsabschnitte (Benutzer)",
      "reference-review-sections-user-description":
        "Benutzer-Prompt-Vorlage zur Definition von Referenz-/Überprüfungsabschnitten.",
    },

    // =========================
    // ADD/EDIT LEGAL TASK KEYS
    // =========================
    "view-categories": "Alle Kategorien anzeigen",
    "hide-categories": "Liste ausblenden",
    "add-task": "Juristische Aufgabe hinzufügen",
    loading: "Laden...",
    table: {
      title: "Juristische Aufgaben",
      name: "Name",
      "sub-category": "Unterkategorie",
      description: "Beschreibung",
      prompt: "Juristischer Aufgabenprompt",
      actions: "Aktionen",
      delete: "Löschen",
      "delete-confirm":
        "Sind Sie sicher, dass Sie diese Kategorie löschen möchten?",
      "delete-success": "Kategorie wurde gelöscht",
      "delete-error": "Kategorie konnte nicht gelöscht werden",
    },
    "create-task-title": "Juristische Aufgabe erstellen",
    "category-name": "Kategoriename",
    "category-name-desc": "Geben Sie den Namen der Hauptkategorie ein.",
    "category-name-placeholder": "Kategoriename eingeben",
    "subcategory-name": "Name der Unterkategorie",
    "subcategory-name-desc": "Geben Sie den Namen der Unterkategorie ein.",
    "subcategory-name-placeholder": "Name der Unterkategorie eingeben",
    "description-desc":
      "Geben Sie eine Beschreibung der Kategorie und Unterkategorie ein.",
    "description-placeholder": "Kurze Beschreibung eingeben",
    submitting: "Wird gesendet...",
    submit: "Absenden",
    validation: {
      "category-required": "Kategoriename ist erforderlich.",
      "subcategory-required": "Name der Unterkategorie ist erforderlich.",
      "description-required": "Beschreibung ist erforderlich.",
      "prompt-required": "Juristischer Aufgabenprompt ist erforderlich.",
    },
    "create-task": {
      title: "Juristische Aufgabe erstellen",
      category: {
        name: "Kategoriename",
        desc: "Geben Sie den Namen der Kategorie ein.",
        placeholder: "Kategoriename eingeben",
        type: "Kategorietyp",
        new: "Neue Kategorie erstellen",
        existing: "Bestehende Kategorie verwenden",
        select: "Kategorie auswählen",
        "select-placeholder": "Wählen Sie eine bestehende Kategorie",
      },
      subcategory: {
        name: "Name der Unterkategorie",
        desc: "Geben Sie den Namen der Unterkategorie ein.",
        placeholder: "Name der Unterkategorie eingeben",
      },
      description: {
        name: "Beschreibung und Benutzeranweisungen",
        desc: "Informationen und Anweisungen, die der Benutzer sehen wird.",
        placeholder:
          "Beschreiben Sie die Art der Dokumente, die in den Arbeitsbereich hochgeladen werden müssen, um das bestmögliche Ergebnis zu erzielen",
      },
      prompt: {
        name: "Juristischer Aufgabenprompt",
        desc: "Geben Sie den Prompt ein, der für diese juristische Aufgabe verwendet werden soll. Sie können auch Beispieldokumente hochladen, um Inhaltsbeispiele zu Ihrem Prompt hinzuzufügen.",
        placeholder:
          "Geben Sie den juristischen Aufgabenprompt ein oder laden Sie Beispieldokumente hoch, um Ihren Prompt zu verbessern...",
      },
      submitting: "Wird gesendet...",
      submit: "Absenden",
      validation: {
        "category-required": "Kategoriename ist erforderlich.",
        "subcategory-required": "Name der Unterkategorie ist erforderlich.",
        "description-required": "Beschreibung ist erforderlich.",
        "prompt-required": "Juristischer Aufgabenprompt ist erforderlich.",
        "legal-task-type-required":
          "Juristischer Aufgabentyp ist erforderlich.",
      },
    },
    "edit-task": {
      title: "Juristische Aufgabe bearbeiten",
      submitting: "Wird aktualisiert...",
      submit: "Aufgabe aktualisieren",
      subcategory: {
        name: "Name der Unterkategorie",
        desc: "Geben Sie einen neuen Namen für diese juristische Aufgabe ein",
        placeholder: "Juristische Aufgabe eingeben...",
      },
      description: {
        name: "Beschreibung und Benutzeranweisungen",
        desc: "Geben Sie eine Beschreibung und Benutzeranweisungen für diese juristische Aufgabe ein",
        placeholder: "Beschreibung und Benutzeranweisungen eingeben...",
      },
      prompt: {
        name: "Juristischer Aufgabenprompt",
        desc: "Geben Sie den Prompt ein, der für diese juristische Aufgabe verwendet werden soll. Sie können auch Beispieldokumente hochladen, um Inhaltsbeispiele zu Ihrem Prompt hinzuzufügen.",
        placeholder:
          "Geben Sie den juristischen Aufgabenprompt ein oder laden Sie Beispieldokumente hoch, um Ihren Prompt zu verbessern...",
      },
      validation: {
        "subcategory-required":
          "Name der juristischen Aufgabe ist erforderlich",
        "description-required": "Beschreibung ist erforderlich",
        "prompt-required": "Juristischer Aufgabenprompt ist erforderlich",
        "legal-task-type-required": "Juristischer Aufgabentyp ist erforderlich",
      },
    },
    "task-form": {
      "requires-main-doc-label": "Hauptdokument-Auswahl erforderlich",
      "requires-main-doc-description":
        "Wenn markiert, muss der Benutzer das Hauptdokument aus den hochgeladenen Dateien auswählen, wenn diese Aufgabe ausgeführt wird. Dies wird dringend für juristische Aufgaben empfohlen, die das Beantworten eines Briefes oder einer Gerichtsentscheidung oder ähnlichem beinhalten, da es das Ergebnis basierend auf dem zu beantwortenden Dokument strukturiert.",
      "requires-main-doc-placeholder": "Ja oder Nein",
      "requires-main-doc-explanation-default":
        "Eine Auswahl ist erforderlich, da dies bestimmt, wie das Dokument erstellt wird.",
      "requires-main-doc-explanation-yes":
        "Wenn 'Ja', muss der Benutzer ein Hauptdokument auswählen, wenn diese juristische Aufgabe gestartet wird. Dieses Dokument wird zentral für den Arbeitsablauf der Aufgabe sein.",
      "requires-main-doc-explanation-no":
        "Wenn 'Nein', wird die juristische Aufgabe fortgesetzt, ohne ein Standard-Hauptdokument zu benötigen. Die Aufgabe wird ein Ergebnis dynamischer basierend auf allen hochgeladenen Dokumenten und der juristischen Aufgabe erstellen.",
      "legal-task-type-label": "Juristischer Aufgabentyp",
      "legal-task-type-placeholder": "Juristischen Aufgabentyp auswählen",
      option: {
        mainDoc: "Hauptdokument-Ablauf",
        noMainDoc: "Kein Hauptdokument-Ablauf",
        referenceFiles: "Referenzdateien-Vergleich",
      },
      "legal-task-type-explanation":
        "Wählen Sie aus, wie die juristische Aufgabe Dokumente behandeln soll.",
      "legal-task-type-explanation-mainDoc":
        "Dieser Ablauf erfordert die Auswahl eines Hauptdokuments, bevor Sie fortfahren.",
      "legal-task-type-explanation-noMainDoc":
        "Dieser Ablauf läuft ohne ein primäres Dokument.",
      "legal-task-type-explanation-referenceFiles":
        "Dieser Ablauf verarbeitet Gruppen von Regeln und Vorschriften gegen Prüfdokumente.",
    },

    // New keys for Review Generator Prompt feature
    reviewGeneratorPromptButton: "Generator-Prompt Überprüfen",
    reviewGeneratorPromptButtonTooltip:
      "Die genaue Prompt-Vorlage anzeigen, die zur Generierung des juristischen Aufgabenvorschlags verwendet wurde. (Nur Admin)",
    reviewGeneratorPromptTitle: "Generator-Prompt Überprüfung",
    reviewPromptLabel:
      "Der folgende Prompt wurde für die Generierung verwendet:",
    reviewPromptTextareaLabel: "Generator-Prompt Inhalt",
  },

  // =========================
  // PERFORM LEGAL TASK
  // =========================
  performLegalTask: {
    title: "Juristische Aufgabe ausführen",
    noTaskfund: "Keine juristischen Aufgaben verfügbar.",
    noSubtskfund: "Keine Unterkategorien verfügbar.",
    "loading-subcategory": "Unterkategorien werden geladen...",
    "select-category": "Kategorie auswählen",
    "choose-task": "Juristische Aufgabe zum Ausführen auswählen",
    "duration-info":
      "Die Zeit für die Ausführung einer juristischen Aufgabe hängt von der Anzahl der Dokumente im Arbeitsbereich ab. Bei vielen Dokumenten und einer komplexen Aufgabe kann dies sehr lange dauern.",
    description:
      "Aktivieren oder deaktivieren Sie die Schaltfläche für juristische Aufgaben in der Dokumentenerstellung.",
    successMessage: "Juristische Aufgabe wurde {{status}}",
    failureUpdateMessage:
      "Fehler beim Aktualisieren der Einstellung für juristische Aufgaben.",
    errorSubmitting:
      "Fehler beim Übermitteln der Einstellungen für juristische Aufgaben.",
    "additional-instructions-label": "Zusätzliche Anweisungen:",
    "custom-instructions-label": "Benutzerdefinierte Anweisungen",
    "custom-instructions-placeholder":
      "Geben Sie zusätzliche Anweisungen für die juristische Aufgabe ein (optional)...",
    "select-main-document-label": "Hauptdokument auswählen (erforderlich)",
    "select-document-placeholder": "-- Ein Dokument auswählen --",
    selectReferenceFilesLabel:
      "Referenzdateien auswählen (Regeln und Vorschriften)",
    "warning-title": "Warnung",
    "no-files-title": "Keine Dateien verfügbar",
    "no-files-description":
      "Es gibt keine Dateien in diesem Arbeitsbereich. Bitte laden Sie mindestens eine Datei hoch, bevor Sie eine juristische Aufgabe ausführen.",
    "settings-button":
      "Verfügbare juristische Aufgaben hinzufügen oder bearbeiten",
    settings: "Einstellungen für juristische Aufgaben",
    subStep: "Laufender oder wartender Schritt",
  },

  // =========================
  // LEGAL TASK PROMPT GENERATOR
  // =========================
  "legal-task-prompt-generator": {
    title: "Generator für juristische Aufgaben-Prompts",
    description:
      "Automatischer Vorschlag des angepassten Prompts für eine juristische Aufgabe",
    "task-description": "Beschreibung der juristischen Aufgabe",
    "task-description-placeholder":
      "Beschreiben Sie die juristische Aufgabe, die Sie durchführen möchten...",
    "specific-instructions": "Spezifische Anweisungen oder Know-how",
    "specific-instructions-description":
      "Fügen Sie spezielle Anweisungen oder Fachwissen hinzu, das spezifisch für diese juristische Aufgabe ist",
    "specific-instructions-placeholder":
      "Fügen Sie spezifische Anweisungen, Fachwissen oder Know-how für die Bearbeitung dieser juristischen Aufgabe hinzu...",
    "suggested-prompt": "Vorgeschlagener Benutzer-Prompt",
    "generation-prompt": "Prompt für die Generierung",
    "create-task":
      "Juristische Aufgabe basierend auf diesem Vorschlag erstellen",
    generating: "Wird generiert...",
    generate: "Vorschlag generieren",
    "toast-success": "Prompt erfolgreich generiert",
    "toast-fail": "Fehler beim Generieren des Prompts",
    button: "Prompt generieren",
    success: "Prompt erfolgreich generiert",
    error: "Bitte geben Sie zuerst einen Namen oder eine Unterkategorie ein",
    failed: "Fehler beim Generieren des Prompts",
  },
};
