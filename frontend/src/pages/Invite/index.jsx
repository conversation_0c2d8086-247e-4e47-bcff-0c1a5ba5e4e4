import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { FullScreenLoader } from "@/components/Preloader";
import Invite from "@/models/invite";
import { useTranslation } from "react-i18next";
import NewUserModal from "./NewUserModal";
import brokenLink from "@/media/logo/brokenLink.png";

export default function InvitePage() {
  const { t } = useTranslation();
  const { code } = useParams();
  const [result, setResult] = useState({
    status: "loading",
    message: null,
  });

  useEffect(() => {
    async function checkInvite() {
      if (!code) {
        setResult({
          status: "invalid",
          message: "No invite code provided.",
        });
        return;
      }
      const { invite, error } = await Invite.checkInvite(code);
      setResult({
        status: invite ? "valid" : "invalid",
        message: error,
      });
    }
    checkInvite();
  }, []);

  if (result.status === "loading") {
    return (
      <div className="w-screen h-screen overflow-hidden flex">
        <FullScreenLoader />
      </div>
    );
  }

  if (result.status === "invalid") {
    return (
      <div className="w-screen h-screen overflow-hidden flex items-center justify-center">
        <div className="bg-card flex flex-col items-center rounded-lg shadow-lg max-w-lg w-full mx-4 py-6 transition-opacity duration-300">
          <img src={brokenLink} alt="broken link icon" className="w-48" />
          <div className="flex flex-col items-center justify-center"></div>
          <p className="text-xs md:text-2xl font-semibold pb-6">
            {result.message}
          </p>
          <p className="danger-alert text-xs md:text-sm">
            {t("invitation.invalid-link")}
          </p>
        </div>
        <p className="text-[12px] md:text-sm absolute bottom-3 text-foreground">
          &copy; {new Date().getFullYear()} IST Legal
        </p>
      </div>
    );
  }

  return <NewUserModal isOpen={true} preventClose={true} onClose={() => {}} />;
}
