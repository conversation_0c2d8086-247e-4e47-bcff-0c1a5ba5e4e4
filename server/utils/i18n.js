const path = require("path");

// Supported languages
const SUPPORTED_LANGUAGES = ["en", "pl", "sv", "rw", "no", "fr", "de"];
const DEFAULT_LANGUAGE = "en";

// Cache for loaded translations and current language
const translationCache = new Map();
let cachedLanguage = null; // Global cache for the current language

// Clear the translation cache
function clearTranslationCache() {
  translationCache.clear();
}

// Load translations for a specific language
function loadTranslations(lang) {
  if (translationCache.has(lang)) {
    return translationCache.get(lang);
  }

  try {
    const translationPath = path.join(
      __dirname,
      "../locales",
      lang,
      "server.js"
    );
    const translations = require(translationPath);
    translationCache.set(lang, translations);
    return translations;
  } catch (error) {
    console.warn(
      `Failed to load translations for ${lang}, falling back to English`
    );
    if (lang !== DEFAULT_LANGUAGE) {
      return loadTranslations(DEFAULT_LANGUAGE);
    }
    return {};
  }
}

// Get the current language from system settings
async function getCurrentLanguage() {
  try {
    const { SystemSettings } = require("../models/systemSettings");
    const setting = await SystemSettings.get({ label: "language" });
    const language = setting?.value;
    if (language && SUPPORTED_LANGUAGES.includes(language)) {
      // Clear the cache when the language changes
      if (cachedLanguage !== language) {
        clearTranslationCache();
      }
      // Update the cached language
      cachedLanguage = language;
      return language;
    }
  } catch (error) {
    console.warn("Failed to get language from system settings:", error.message);
  }
  return DEFAULT_LANGUAGE;
}

// Replace template variables in a string
function replaceTemplateVariables(str, variables) {
  if (!variables) return str;
  return str.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return variables[key] !== undefined ? variables[key] : match;
  });
}

// Get translation for a key
async function t(key, variables = null) {
  const lang = await getCurrentLanguage();
  const translations = loadTranslations(lang);

  // Split the key into parts and traverse the translations object
  const parts = key.split(".");
  let value = translations;
  for (const part of parts) {
    value = value?.[part];
    if (!value) break;
  }

  // If no translation found, try English, then return the key
  if (!value && lang !== DEFAULT_LANGUAGE) {
    const defaultTranslations = loadTranslations(DEFAULT_LANGUAGE);
    value = defaultTranslations;
    for (const part of parts) {
      value = value?.[part];
      if (!value) break;
    }
  }

  const translation = value || key;
  return replaceTemplateVariables(translation, variables);
}

// Get translation for a key synchronously (uses cached language)
function tSync(key, variables = null) {
  // Use the global cached language if available, otherwise default to English
  let lang = cachedLanguage || DEFAULT_LANGUAGE;

  const translations = loadTranslations(lang);

  // Split the key into parts and traverse the translations object
  const parts = key.split(".");
  let value = translations;
  for (const part of parts) {
    value = value?.[part];
    if (!value) break;
  }

  // If no translation found, try English, then return the key
  if (!value && lang !== DEFAULT_LANGUAGE) {
    const defaultTranslations = loadTranslations(DEFAULT_LANGUAGE);
    value = defaultTranslations;
    for (const part of parts) {
      value = value?.[part];
      if (!value) break;
    }
  }

  const translation = value || key;
  return replaceTemplateVariables(translation, variables);
}

/**
 * Initialize the language system by preloading the current language
 * This should be called at server startup
 */
async function initializeLanguage() {
  try {
    cachedLanguage = await getCurrentLanguage();
    console.log(`Initialized language system with language: ${cachedLanguage}`);
    // Preload translations for current language
    loadTranslations(cachedLanguage);
    // Also preload English as fallback
    if (cachedLanguage !== DEFAULT_LANGUAGE) {
      loadTranslations(DEFAULT_LANGUAGE);
    }
    return cachedLanguage;
  } catch (error) {
    console.error("Failed to initialize language system:", error);
    cachedLanguage = DEFAULT_LANGUAGE;
    return DEFAULT_LANGUAGE;
  }
}

module.exports = {
  getCurrentLanguage,
  t,
  tSync,
  SUPPORTED_LANGUAGES,
  DEFAULT_LANGUAGE,
  clearTranslationCache,
  initializeLanguage,
};
