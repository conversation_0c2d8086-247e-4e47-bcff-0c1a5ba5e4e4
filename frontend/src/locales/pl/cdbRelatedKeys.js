export default {
  // =========================
  // DOCUMENT BUILDER SETTINGS
  // =========================
  "document-builder": {
    title: "Prompty Konstruktora Dokumentów",
    description:
      "Dostosuj domyślne prompty używane przez funkcję Konstruktora Dokumentów.",
    "override-prompt-placeholder":
      "Wprowad<PERSON> prompt, aby zastąpić domyślny prompt systemowy",
    saving: "Zapisywanie...",
    save: "Zapisz Ustawienia Promptów",
    "toast-success": "Prompty konstruktora dokumentów zapisane pomyślnie.",
    "toast-fail": "Nie udało się zapisać promptów konstruktora dokumentów.",

    // =========================
    // LEGAL DRAFTING PROMPTS
    // =========================
    prompts: {
      group: {
        document_summary: {
          title: "Prompty Podsumowania Dokumentu",
          description:
            "Konfiguruj prompty systemowe i użytkownika dla Podsumowania Dokumentu.",
        },
        document_relevance: {
          title: "Prompty Istotności Dokumentu",
          description:
            "Konfiguruj prompty systemowe i użytkownika dla Istotności Dokumentu.",
        },
        section_drafting: {
          title: "Prompty Tworzenia Sekcji",
          description:
            "Konfiguruj prompty systemowe i użytkownika dla Tworzenia Sekcji.",
        },
        section_legal_issues: {
          title: "Prompty Kwestii Prawnych Sekcji",
          description:
            "Konfiguruj prompty systemowe i użytkownika dla Kwestii Prawnych Sekcji.",
        },
        memo_creation: {
          title: "Prompty Tworzenia Notatki",
          description: "Konfiguruj prompty dla Tworzenia Notatki.",
        },
        section_index: {
          title: "Prompty Indeksu Sekcji",
          description: "Konfiguruj prompty dla Indeksu Sekcji.",
        },
        select_main_document: {
          title: "Prompty Wyboru Głównego Dokumentu",
          description:
            "Konfiguruj prompty systemowe i użytkownika dla Wyboru Głównego Dokumentu.",
        },
        section_list_from_main: {
          title: "Prompty Listy Sekcji z Głównego Dokumentu",
          description:
            "Konfiguruj prompty systemowe i użytkownika dla Listy Sekcji z Głównego Dokumentu.",
        },
        section_list_from_summaries: {
          title: "Prompty Lista Sekcji z Podsumowań",
          description:
            "Skonfiguruj prompty systemowe i użytkownika dla Lista Sekcji z Podsumowań.",
        },
        reference_files_description: {
          title: "Prompty Opis Plików Referencyjnych",
          description:
            "Skonfiguruj prompty systemowe i użytkownika dla Opis Plików Referencyjnych.",
        },
        review_files_description: {
          title: "Prompty Opis Plików Przeglądu",
          description:
            "Skonfiguruj prompty systemowe i użytkownika dla Opis Plików Przeglądu.",
        },
        reference_review_sections: {
          title: "Prompty Sekcje Referencyjne/Przeglądu",
          description:
            "Skonfiguruj prompty systemowe i użytkownika dla Sekcje Referencyjne/Przeglądu.",
        },
      },
      // Document Summary
      "document-summary-system-label": "Podsumowanie Dokumentu (System)",
      "document-summary-system-description":
        "Prompt systemowy instruujący AI, jak podsumować zawartość dokumentu i jego znaczenie dla zadania prawnego.",
      "document-summary-user-label": "Podsumowanie Dokumentu (Użytkownik)",
      "document-summary-user-description":
        "Szablon promptu użytkownika do generowania szczegółowego podsumowania zawartości dokumentu w odniesieniu do konkretnego zadania prawnego.",

      // Reference Files Description Prompts
      "reference-files-description-system-label":
        "Opis Plików Referencyjnych (System)",
      "reference-files-description-system-description":
        "Prompt systemowy do opisywania plików referencyjnych.",
      "reference-files-description-user-label":
        "Opis Plików Referencyjnych (Użytkownik)",
      "reference-files-description-user-description":
        "Szablon prompta użytkownika do opisywania plików referencyjnych.",

      // Document Relevance
      "document-relevance-system-label": "Istotność Dokumentu (System)",
      "document-relevance-system-description":
        "Prompt systemowy do oceny, czy dokument jest istotny dla zadania prawnego, oczekujący odpowiedzi prawda/fałsz.",
      "document-relevance-user-label": "Istotność Dokumentu (Użytkownik)",
      "document-relevance-user-description":
        "Szablon promptu użytkownika do sprawdzenia, czy zawartość dokumentu jest istotna dla danego zadania prawnego.",

      // Section Drafting
      "section-drafting-system-label": "Tworzenie Sekcji (System)",
      "section-drafting-system-description":
        "Prompt systemowy do generowania pojedynczej sekcji dokumentu w profesjonalnym stylu prawniczym, wykorzystujący określone dokumenty i kontekst.",
      "section-drafting-user-label": "Tworzenie Sekcji (Użytkownik)",
      "section-drafting-user-description":
        "Szablon promptu użytkownika do generowania konkretnej sekcji dokumentu prawnego, uwzględniający tytuł, zadanie, dokumenty źródłowe i sąsiednie sekcje.",

      // Section Legal Issues
      "section-legal-issues-system-label":
        "Identyfikacja Kwestii Prawnych Sekcji (System)",
      "section-legal-issues-system-description":
        "Prompt systemowy do identyfikacji konkretnych tematów prawnych, dla których należy pozyskać informacje faktyczne wspierające tworzenie sekcji dokumentu.",

      "section-legal-issues-user-label":
        "Identyfikacja Kwestii Prawnych Sekcji (Użytkownik)",
      "section-legal-issues-user-description":
        "Szablon promptu użytkownika do wylistowania tematów prawnych lub punktów danych do pozyskania informacji kontekstowych istotnych dla konkretnej sekcji dokumentu i zadania prawnego.",

      // Memo Creation
      "memo-creation-template-label": "Domyślny Szablon Tworzenia Notatki",
      "memo-creation-template-description":
        "Szablon promptu do tworzenia notatki prawnej dotyczącej konkretnego problemu prawnego, uwzględniający dostarczone dokumenty i kontekst zadania.",
      // Section Index
      "section-index-system-label": "Indeks Sekcji (System)",
      "section-index-system-description":
        "Prompt systemowy do generowania strukturalnego indeksu sekcji dla dokumentu prawnego.",

      // Select Main Document
      "select-main-document-system-label": "Wybór Głównego Dokumentu (System)",
      "select-main-document-system-description":
        "Prompt systemowy do identyfikacji najbardziej istotnego głównego dokumentu dla zadania prawnego z wielu podsumowań dokumentów.",
      "select-main-document-user-label":
        "Wybór Głównego Dokumentu (Użytkownik)",
      "select-main-document-user-description":
        "Szablon promptu użytkownika do identyfikacji głównego dokumentu dla zadania prawnego na podstawie podsumowań wielu dokumentów.",

      // Section List from Main Document
      "section-list-from-main-system-label":
        "Lista Sekcji z Głównego Dokumentu (System)",
      "section-list-from-main-system-description":
        "Prompt systemowy do tworzenia strukturalnej listy sekcji w formacie JSON dla dokumentu prawnego na podstawie zawartości głównego dokumentu i zadania prawnego.",
      "section-list-from-main-user-label":
        "Lista Sekcji z Głównego Dokumentu (Użytkownik)",
      "section-list-from-main-user-description":
        "Szablon promptu użytkownika do dostarczenia zadania prawnego i zawartości głównego dokumentu w celu wygenerowania listy sekcji.",
      // Section List from Summaries
      "section-list-from-summaries-system-label":
        "Lista Sekcji z Podsumowań (System)",
      "section-list-from-summaries-system-description":
        "Prompt systemowy do tworzenia strukturalnej listy sekcji w formacie JSON na podstawie podsumowań dokumentów i zadania prawnego, gdy nie istnieje główny dokument.",
      "section-list-from-summaries-user-label":
        "Lista Sekcji z Podsumowań (Użytkownik)",
      "section-list-from-summaries-user-description":
        "Szablon prompta użytkownika do dostarczenia zadania prawnego i podsumowań dokumentów w celu wygenerowania listy sekcji, gdy nie istnieje dokument główny.",

      // Review Files Description Prompts
      "review-files-description-system-label": "Opis Plików Przeglądu (System)",
      "review-files-description-system-description":
        "Prompt systemowy do opisywania plików przeglądu.",
      "review-files-description-user-label":
        "Opis Plików Przeglądu (Użytkownik)",
      "review-files-description-user-description":
        "Szablon prompta użytkownika do opisywania plików przeglądu.",

      // Reference/Review Sections Prompts
      "reference-review-sections-system-label":
        "Sekcje Referencyjne/Przeglądu (System)",
      "reference-review-sections-system-description":
        "Prompt systemowy do definiowania sekcji referencyjnych/przeglądu.",
      "reference-review-sections-user-label":
        "Sekcje Referencyjne/Przeglądu (Użytkownik)",
      "reference-review-sections-user-description":
        "Szablon prompta użytkownika do definiowania sekcji referencyjnych/przeglądu.",
    },

    // =========================
    // ADD/EDIT LEGAL TASK KEYS
    // =========================
    "view-categories": "Zobacz wszystkie kategorie",
    "hide-categories": "Ukryj listę",
    "add-task": "Dodaj zadanie prawne",
    loading: "Ładowanie...",
    table: {
      title: "Zadania Prawne",
      name: "Nazwa",
      "sub-category": "Podkategoria",
      description: "Opis",
      prompt: "Prompt Zadania Prawnego",
      actions: "Akcje",
      delete: "Usuń",
      "delete-confirm": "Czy na pewno chcesz usunąć tę kategorię?",
      "delete-success": "Kategoria została usunięta",
      "delete-error": "Nie można usunąć kategorii",
    },
    "create-task-title": "Utwórz zadanie prawne",
    "category-name": "Nazwa Kategorii",
    "category-name-desc": "Wprowadź nazwę głównej kategorii.",
    "category-name-placeholder": "Wprowadź nazwę kategorii",
    "subcategory-name": "Nazwa Podkategorii",
    "subcategory-name-desc": "Wprowadź nazwę podkategorii.",
    "subcategory-name-placeholder": "Wprowadź nazwę podkategorii",
    "description-desc": "Wprowadź opis kategorii i podkategorii.",
    "description-placeholder": "Wprowadź krótki opis",
    submitting: "Wysyłanie...",
    submit: "Wyślij",
    validation: {
      "category-required": "Nazwa kategorii jest wymagana.",
      "subcategory-required": "Nazwa podkategorii jest wymagana.",
      "description-required": "Opis jest wymagany.",
      "prompt-required": "Prompt zadania prawnego jest wymagany.",
    },
    "create-task": {
      title: "Utwórz zadanie prawne",
      category: {
        name: "Nazwa Kategorii",
        desc: "Wprowadź nazwę kategorii.",
        placeholder: "Wprowadź nazwę kategorii",
        type: "Typ Kategorii",
        new: "Utwórz nową kategorię",
        existing: "Użyj istniejącej kategorii",
        select: "Wybierz kategorię",
        "select-placeholder": "Wybierz istniejącą kategorię",
      },
      subcategory: {
        name: "Nazwa Podkategorii",
        desc: "Wprowadź nazwę podkategorii.",
        placeholder: "Wprowadź nazwę podkategorii",
      },
      description: {
        name: "Opis i instrukcje użytkownika",
        desc: "Informacje i instrukcje, które zobaczy użytkownik.",
        placeholder:
          "Opisz typ dokumentów, które muszą zostać przesłane do obszaru roboczego, aby osiągnąć najlepszy możliwy wynik",
      },
      prompt: {
        name: "Prompt Zadania Prawnego",
        desc: "Wprowadź prompt, który zostanie użyty dla tego zadania prawnego. Możesz również przesłać przykładowe dokumenty za pomocą przycisków, aby dodać przykłady treści do swojego promptu.",
        placeholder:
          "Wprowadź prompt zadania prawnego lub prześlij przykładowe dokumenty, aby poprawić swój prompt...",
      },
      submitting: "Wysyłanie...",
      submit: "Wyślij",
      validation: {
        "category-required": "Nazwa kategorii jest wymagana.",
        "subcategory-required": "Nazwa podkategorii jest wymagana.",
        "description-required": "Opis jest wymagany.",
        "prompt-required": "Prompt zadania prawnego jest wymagany.",
        "legal-task-type-required": "Typ zadania prawnego jest wymagany.",
      },
    },
    "edit-task": {
      title: "Edytuj zadanie prawne",
      submitting: "Aktualizowanie...",
      submit: "Aktualizuj zadanie",
      subcategory: {
        name: "Nazwa Podkategorii",
        desc: "Wprowadź nową nazwę dla tego zadania prawnego",
        placeholder: "Wprowadź zadanie prawne...",
      },
      description: {
        name: "Opis i instrukcje użytkownika",
        desc: "Wprowadź opis i instrukcje użytkownika dla tego zadania prawnego",
        placeholder: "Wprowadź opis i instrukcje użytkownika...",
      },
      prompt: {
        name: "Prompt Zadania Prawnego",
        desc: "Wprowadź prompt, który zostanie użyty dla tego zadania prawnego. Możesz również przesłać przykładowe dokumenty za pomocą przycisków, aby dodać przykłady treści do swojego promptu.",
        placeholder:
          "Wprowadź prompt zadania prawnego lub prześlij przykładowe dokumenty, aby poprawić swój prompt...",
      },
      validation: {
        "subcategory-required": "Nazwa zadania prawnego jest wymagana",
        "description-required": "Opis jest wymagany",
        "prompt-required": "Prompt zadania prawnego jest wymagany",
        "legal-task-type-required": "Typ zadania prawnego jest wymagany",
      },
    },
    "task-form": {
      "requires-main-doc-label": "Wymagany Wybór Głównego Dokumentu",
      "requires-main-doc-description":
        "Jeśli zaznaczone, użytkownik musi wybrać główny dokument spośród przesłanych plików podczas wykonywania tego zadania. Jest to szczególnie zalecane w przypadku zadań prawnych związanych z odpowiedzią na pismo lub orzeczenie sądowe lub podobne, ponieważ strukturyzuje wynik w oparciu o dokument, na który udzielana jest odpowiedź.",
      "requires-main-doc-placeholder": "Tak lub Nie",
      "requires-main-doc-explanation-default":
        "Wybór jest wymagany, ponieważ określa sposób budowania dokumentu.",
      "requires-main-doc-explanation-yes":
        "Jeśli 'Tak', użytkownik będzie musiał wybrać główny dokument podczas rozpoczynania tego zadania prawnego. Ten dokument będzie centralnym elementem przepływu pracy zadania.",
      "requires-main-doc-explanation-no":
        "Jeśli 'Nie', zadanie prawne będzie kontynuowane bez wymagania domyślnego głównego dokumentu. Zadanie będzie tworzyć wynik bardziej dynamicznie w oparciu o wszystkie przesłane dokumenty i zadanie prawne.",
      "legal-task-type-label": "Typ Zadania Prawnego",
      "legal-task-type-placeholder": "Wybierz typ zadania prawnego",
      option: {
        mainDoc: "Przepływ z Głównym Dokumentem",
        noMainDoc: "Przepływ bez Głównego Dokumentu",
        referenceFiles: "Porównanie Plików Referencyjnych",
      },
      "legal-task-type-explanation":
        "Wybierz, jak zadanie prawne ma obsługiwać dokumenty.",
      "legal-task-type-explanation-mainDoc":
        "Ten przepływ wymaga wyboru głównego dokumentu przed kontynuowaniem.",
      "legal-task-type-explanation-noMainDoc":
        "Ten przepływ działa bez głównego dokumentu.",
      "legal-task-type-explanation-referenceFiles":
        "Ten przepływ przetwarza grupy zasad i przepisów względem dokumentów do przeglądu.",
    },

    // New keys for Review Generator Prompt feature
    reviewGeneratorPromptButton: "Przejrzyj Prompt Generatora",
    reviewGeneratorPromptButtonTooltip:
      "Zobacz dokładny szablon promptu użyty do wygenerowania sugestii zadania prawnego. (Tylko admin)",
    reviewGeneratorPromptTitle: "Przegląd Promptu Generatora",
    reviewPromptLabel: "Następujący prompt został użyty do generowania:",
    reviewPromptTextareaLabel: "Zawartość Promptu Generatora",
  },

  // =========================
  // PERFORM LEGAL TASK
  // =========================
  performLegalTask: {
    title: "Wykonaj Zadanie Prawne",
    noTaskfund: "Brak dostępnych zadań prawnych.",
    noSubtskfund: "Brak dostępnych podkategorii.",
    "loading-subcategory": "Ładowanie podkategorii...",
    "select-category": "Wybierz kategorię",
    "choose-task": "Wybierz zadanie prawne do wykonania",
    "duration-info":
      "Czas wykonywania zadania prawnego zależy od liczby dokumentów w obszarze roboczym. Przy wielu dokumentach i złożonym zadaniu może to zająć bardzo dużo czasu.",
    description:
      "Włącz lub wyłącz przycisk wykonywania zadania prawnego w Tworzeniu Dokumentów.",
    successMessage: "Wykonywanie zadania prawnego zostało {{status}}",
    failureUpdateMessage:
      "Nie udało się zaktualizować ustawienia wykonywania zadania prawnego.",
    errorSubmitting:
      "Błąd podczas przesyłania ustawień wykonywania zadania prawnego.",
    "additional-instructions-label": "Dodatkowe Instrukcje:",
    "custom-instructions-label": "Niestandardowe Instrukcje",
    "custom-instructions-placeholder":
      "Wprowadź dodatkowe instrukcje dla zadania prawnego (opcjonalne)...",
    "select-main-document-label": "Wybierz Główny Dokument (Wymagane)",
    "select-document-placeholder": "-- Wybierz dokument --",
    selectReferenceFilesLabel: "Wybierz Pliki Referencyjne (Zasady i Przepisy)",
    "warning-title": "Ostrzeżenie",
    "no-files-title": "Brak Dostępnych Plików",
    "no-files-description":
      "W tym obszarze roboczym nie ma plików. Proszę przesłać co najmniej jeden plik przed wykonaniem zadania prawnego.",
    "settings-button": "Dodaj lub edytuj dostępne zadania prawne",
    settings: "Ustawienia Zadań Prawnych",
    subStep: "Trwający lub oczekujący krok",
  },

  // =========================
  // LEGAL TASK PROMPT GENERATOR
  // =========================
  "legal-task-prompt-generator": {
    title: "Generator promptu użytkownika zadania prawnego",
    description:
      "Automatyczna propozycja dostosowanego promptu dla zadania prawnego",
    "task-description": "Opis zadania prawnego",
    "task-description-placeholder":
      "Opisz zadanie prawne, które chcesz wykonać...",
    "specific-instructions": "Konkretne instrukcje lub wiedza specjalistyczna",
    "specific-instructions-description":
      "Uwzględnij wszelkie specjalne instrukcje lub wiedzę specjalistyczną specyficzną dla tego zadania prawnego",
    "specific-instructions-placeholder":
      "Dodaj konkretne instrukcje, wiedzę specjalistyczną lub know-how do obsługi tego zadania prawnego...",
    "suggested-prompt": "Sugerowany prompt użytkownika",
    "generation-prompt": "Prompt do generowania",
    "create-task": "Utwórz zadanie prawne na podstawie tej sugestii",
    generating: "Generowanie...",
    generate: "Generuj propozycję",
    "toast-success": "Prompt wygenerowany pomyślnie",
    "toast-fail": "Nie udało się wygenerować promptu",
    button: "Generuj Prompt",
    success: "Prompt wygenerowany pomyślnie",
    error: "Proszę najpierw wprowadzić nazwę lub podkategorię",
    failed: "Nie udało się wygenerować promptu",
  },
};
