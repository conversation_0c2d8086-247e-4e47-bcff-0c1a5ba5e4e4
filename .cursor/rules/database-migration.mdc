---
description:
globs:
alwaysApply: false
---

### Database Migration:
Creating good migrations is essential for a reliable application, especially in containerized environments like Docker. Here are best practices for creating robust migrations:

#### Creating Good Migrations with Prisma
### Database Migration:
Creating good migrations is essential for a reliable application, especially in containerized environments like Docker. Here are best practices for creating robust migrations:

#### Creating Good Migrations with Prisma

### Database Migration:
Creating good migrations is essential for a reliable application, especially in containerized environments like Docker. Here are best practices for creating robust migrations:

#### Creating Good Migrations with Prisma

1. **Create new migrations with:**
```
npx prisma migrate dev --name descriptive_name
```

2. **Always use conditional creation:**
- For tables: Use `CREATE TABLE IF NOT EXISTS`
- For indexes: Check if the index exists before creating `CREATE INDEX IF NOT EXISTS`
- For columns: Check if the column exists including it in your migration

3. **Handle schema dependencies properly:**
- Create tables before creating indexes or foreign keys
- When adding foreign keys, ensure referenced tables exist

4. **Command to inspect migrations in the database:**

cd /home/<USER>/istlegal (for server)
cd /server/storage/istlegal (for local)
sqlite3 istlegal.db

## View All Non-Completed Migrations

```sql
-- Display all migrations that haven't been completed
SELECT * FROM _prisma_migrations WHERE finished_at IS NULL ORDER BY started_at;
```

## View All Completed Migrations

```sql
-- Display all migrations that have been completed
SELECT * FROM _prisma_migrations WHERE finished_at IS NOT NULL ORDER BY started_at;
```

## Mark All Incomplete Migrations as Done

```sql
-- Mark all incomplete migrations as completed
UPDATE _prisma_migrations SET finished_at = CURRENT_TIMESTAMP, applied_steps_count = 1, logs = 'Manually marked as completed' WHERE finished_at IS NULL;
```

## Delete All Incomplete Migrations

```sql
-- Permanently delete all incomplete migration records
DELETE FROM _prisma_migrations WHERE finished_at IS NULL;
```

## Delete All Completed Migrations

```sql
-- Permanently delete all completed migration records
DELETE FROM _prisma_migrations WHERE finished_at IS NOT NULL;
```

## Delete All Migrations (Both Complete and Incomplete)

```sql
-- Delete all migration records from the table
DELETE FROM _prisma_migrations;
```

5. **Common SQL commands for migration troubleshooting:**
- View incomplete migrations
- View completed migrations
- Mark incomplete migrations as done
- Delete migration records as needed

6. **Common Issues**
- Failed migrations often occur when trying to create already existing objects
- Docker container restarts can trigger repeated migration attempts
- SQLite has limitations with IF NOT EXISTS for certain operations
- Schema dependencies must be properly ordered

Example for SQLite:
```sh
sqlite3 istlegal.db
.schema > schema.sql
```
