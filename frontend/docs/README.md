# Frontend Documentation

This directory contains comprehensive documentation for the IST Legal frontend application, covering architecture, features, components, and development guidelines.

## Table of Contents

### Core Architecture

- [Chat System Architecture](./chat-system-architecture.md) - Complete chat system implementation and architecture
- [State Management](./state-management.md) - Frontend state management patterns and best practices
- [UI Component Architecture](./ui-component-architecture.md) - UI component system and design patterns
- [LLM Component Architecture](./llm-component-architecture.md) - LLM integration and component structure

### User Interface Components

- [UI Components](./ui-components.md) - Standard UI component library and usage
- [User Guide Feature](./user-guide-feature.md) - In-app user guide system documentation
- [Attachments](./attachments.md) - File attachment system and handling
- [Document Drafting Progress Modal](./document-drafting-progress-modal.md) - Progress tracking for document generation

### Feature Documentation

- [Legal Tasks Components](./legal-tasks-components.md) - Legal task management system
- [Custom Legal Templates](./custom-legal-templates.md) - Template creation and management
- [Document Builder Page](./document-builder-page.md) - Complex document builder interface
- [Manual Work Estimator](./manual-work-estimator.md) - Time estimation feature for legal work
- [Response Validation](./response-validation.md) - AI response validation system
- [Sharing Functionality](./sharing-functionality.md) - Document and conversation sharing features
- [DeepSearch Settings](./deepsearch-settings.md) - Advanced search configuration

### Development and Maintenance

- [Testing](./testing.md) - Frontend testing strategies and implementation
- [Internationalization](./internationalization.md) - i18n implementation and guidelines
- [Error Handling and Translations](./error-handling-and-translations.md) - Error management and translation patterns
- [Documentation Requirements](./documentation-requirements.md) - Guidelines for maintaining documentation
- [Locale Scripts](./locale-scripts.md) - Translation verification and management scripts

### System Integration

- [Legal Task Prompt Generation](./legal-task-prompt-generation.md) - Prompt generation system for legal tasks
- [System Prompt Customization](./system_prompt_customization.md) - System prompt configuration and customization
- [Lawyer Individualizer](./lawyer_individualizer.md) - Lawyer profile and individualization system

### Legacy and Migration

- [Legacy Agent WebSocket Removal](./legacy-agent-websocket-removal.md) - WebSocket system migration documentation

### Development Resources

- [Prompt Examples](./prompt-examples.md) - Example prompts for development and testing

## Quick Reference

### Most Important Documents for New Developers

1. [Chat System Architecture](./chat-system-architecture.md) - Understanding the core chat functionality
2. [UI Component Architecture](./ui-component-architecture.md) - Working with the component system
3. [State Management](./state-management.md) - Managing application state
4. [Testing](./testing.md) - Writing and running tests
5. [Internationalization](./internationalization.md) - Adding translations and i18n support

### Feature-Specific Documentation

- **User Help**: [User Guide Feature](./user-guide-feature.md)
- **Document Creation**: [Document Builder Page](./document-builder-page.md), [Custom Legal Templates](./custom-legal-templates.md)
- **Legal Tasks**: [Legal Tasks Components](./legal-tasks-components.md), [Legal Task Prompt Generation](./legal-task-prompt-generation.md)
- **File Handling**: [Attachments](./attachments.md)
- **Sharing**: [Sharing Functionality](./sharing-functionality.md)

### Maintenance and Operations

- **Documentation**: [Documentation Requirements](./documentation-requirements.md)
- **Translations**: [Internationalization](./internationalization.md), [Locale Scripts](./locale-scripts.md)
- **Error Handling**: [Error Handling and Translations](./error-handling-and-translations.md)

## Contributing to Documentation

When adding new features or modifying existing ones:

1. **Update relevant documentation** - Modify existing docs that are affected by your changes
2. **Create new documentation** - Add new docs for significant features following the established patterns
3. **Update this README** - Add references to new documentation files
4. **Follow documentation standards** - See [Documentation Requirements](./documentation-requirements.md)

## Documentation Standards

- Use clear, descriptive titles and section headers
- Include code examples where appropriate
- Document both implementation details and usage patterns
- Maintain consistent formatting and structure
- Update documentation as part of feature development, not as an afterthought

## Getting Help

If you need help understanding any aspect of the frontend:

1. Check the relevant documentation in this directory
2. Look at the code examples and patterns in the documentation
3. Review the actual implementation in the codebase
4. Consult with the development team for complex architectural questions

---

_This documentation is maintained as part of the IST Legal frontend development process. Keep it current and comprehensive to support effective development and maintenance._
