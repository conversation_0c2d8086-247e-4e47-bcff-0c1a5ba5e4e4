---
description: Guidelines for hooks and utilities
globs: frontend/src/hooks/**/*.js,frontend/src/utils/**/*.js
alwaysApply: false
---

# Hooks and Utilities

## Custom Hooks

The `frontend/src/hooks/` directory contains custom React hooks:

- `useUser.js`: User session management
- `useLoginMode.js`: Login mode detection
- `useQuery.js`: URL query parameter handling
- And others for specific functionality

## Utility Functions

The `frontend/src/utils/` directory contains utility functions:

- `constants.js`: Application constants
- `request.js`: API request handling with auth tokens
- `session.js`: Session management and local storage
- `directories.js`: File-related formatting utilities
- `chat/`: Chat-specific utilities
- `piperTTS/`: Text-to-speech utilities
- `AiProviders/`: AI provider utilities

## Best Practices for Hooks

1. Keep hooks focused on a single responsibility
2. Follow the React hooks naming convention (`useSomething`)
3. Handle errors appropriately
4. Clean up resources in useEffect cleanup functions
5. Memoize expensive calculations
6. Document complex hook logic

## Best Practices for Utilities

1. Keep utility functions pure when possible
2. Group related functions in the same file or directory
3. Use descriptive names for functions and parameters
4. Handle errors appropriately
5. Document complex utility functions
6. Test utilities with various inputs and edge cases

## Common Patterns

- Use `request.js` for API calls with proper authentication
- Use `constants.js` for shared constants
- Use `session.js` for local storage operations
- Use custom hooks to encapsulate complex logic
