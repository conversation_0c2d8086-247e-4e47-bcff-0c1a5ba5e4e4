-- Manual Migration: Add Streamlined News System
-- This migration adds the streamlined news system with unified dismissal tracking
-- Date: 2025-05-31
-- Description: Replaces complex news tracking with simple dismissal-only system

-- CreateTable: News messages for admin-created local news
CREATE TABLE "news_messages" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "priority" TEXT NOT NULL DEFAULT 'medium' CHECK ("priority" IN ('urgent', 'high', 'medium', 'low')),
    "target_roles" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "expires_at" DATETIME,
    "created_by" INTEGER NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "news_messages_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable: Unified dismissal tracking for both local and system news
-- news_id format: "local-{id}" for database news, direct ID for system news (e.g., "system-welcome-2024")
CREATE TABLE "user_news_dismissals" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "user_id" INTEGER NOT NULL,
    "news_id" TEXT NOT NULL,
    "dismissed_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "user_news_dismissals_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateIndex: Optimize news_messages queries
CREATE INDEX "news_messages_is_active_createdAt_idx" ON "news_messages"("is_active", "createdAt");
CREATE INDEX "news_messages_expires_at_idx" ON "news_messages"("expires_at");

-- CreateIndex: Optimize user_news_dismissals queries
CREATE UNIQUE INDEX "user_news_dismissals_user_id_news_id_key" ON "user_news_dismissals"("user_id", "news_id");
CREATE INDEX "user_news_dismissals_user_id_idx" ON "user_news_dismissals"("user_id");
CREATE INDEX "user_news_dismissals_news_id_idx" ON "user_news_dismissals"("news_id");

-- Migration Notes:
-- 1. This replaces the previous complex system with separate user_news_views and user_system_news_dismissals tables
-- 2. Only tracks dismissals (permanent "don't show again" actions), not views
-- 3. Uses consistent ID format for both local and system news
-- 4. Eliminates hash-based system news tracking
-- 5. Simplifies backend logic with unified dismissal handling
-- 6. The users table already has the news_dismissals relation field added in the schema
-- 7. This migration creates the tables and indexes to support the streamlined news system
