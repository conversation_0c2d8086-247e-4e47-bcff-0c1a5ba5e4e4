---
description:
globs:
alwaysApply: false
---
# Document Builder Page Guidelines

## Overview
The **Document Builder** page (frontend path `frontend/src/pages/GeneralSettings/DocumentBuilder/`) provides an admin UI for configuring the prompts used by the Case Document Builder (CDB) pipeline.
Its React component renders a set of dynamic text areas generated from a prompt-configuration payload fetched from the backend.

A REST endpoint exposed by the server returns the list of prompt descriptors:
```
GET /system/document-builder-prompts
```
and accepts updates:
```
POST /system/update-document-builder-prompts
```
The frontend consumes these via the helper methods `System.getDocumentBuilderPrompts()` and `System.updateDocumentBuilderPrompts()`.

Each prompt descriptor has the shape:
```ts
{
  promptKey: string;          // Logical group id (e.g. "SUMMARY")
  promptField: string;        // SYSTEM_PROMPT | USER_PROMPT | PROMPT_TEMPLATE
  label: string;              // i18n-ready label for <Label>
  description: string;        // i18n-ready helper text
  defaultContent: string;     // Placeholder text
  systemSettingName: string;  // The server side system-settings key
  currentValue: string;       // Existing override value ("" ⇒ uses default)
}
```

## UI Behaviour
1. **Initial Load**
   On mount the page calls `System.getDocumentBuilderPrompts()`.
   While the promise is pending a skeleton loader is shown.
   Failure triggers `showToast(t("document_builder_page.toast-fail-load-prompts"), "error")` and renders an empty state.

2. **Dynamic Rendering**
   The component groups descriptors by `promptKey` and shows a card per group.
   A descriptor with `promptField === 'PROMPT_TEMPLATE'` is rendered as a single textarea; others are rendered as a *System/User* pair.

3. **Editing & Saving**
   • Inputs are fully controlled using React-Hook-Form + `zod` (see `form-guidelines`).
   • Clicking the *Save* button bundles the current values into a `{ [systemSettingName]: value }` map and calls `System.updateDocumentBuilderPrompts(updates)`.
   • Success ⇒ green toast and resets dirty flag; Failure ⇒ red toast and leaves values untouched.

4. **Integration Tests**
   The page is covered by a Jest + React-Testing-Library test (`__tests__/DocumentBuilder.test.jsx`) that mocks the System API and verifies:
   • Successful populate and rendering of groups
   • Error handling on fetch failure
   • Happy-path save flow
   • Error toast on save failure

## Backend Expectations
The server endpoint `GET /system/document-builder-prompts` returns:
```json
{ "success": true, "prompts": [/* …descriptor objects… */] }
```
`POST /system/update-document-builder-prompts` accepts `{ "<systemSettingName>": "<value>" }` and returns `{ "success": true }` or `{ "success": false, "error": "…" }`.

The server builds the prompt list from `server/utils/chats/prompts/legalDrafting.js -> exportedLegalPrompts` so that frontend and backend stay in sync.

## Internationalisation
All labels, descriptions, button text and toast messages **must** use translation keys (see `internationalization-with-i18n` rule).  Add new keys under the `document-builder.*` namespace.

## Do & Don't
✅ Do generate UI fields dynamically; avoid hard-coding prompt names.
✅ Do keep the component free of server logic; delegate to `models/system.js`.
✅ Do unit + integration test the major flows.

❌ Don't introduce new global state; keep it local to the page.
❌ Don't commit hardcoded user-facing strings.
❌ Don't call the legacy `/system/get-document-builder` endpoint – it has been removed.
