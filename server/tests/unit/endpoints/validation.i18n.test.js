const { tSync } = require("../../../utils/i18n");

// Mock the i18n module to control the cached language
jest.mock("../../../utils/i18n", () => {
  const originalModule = jest.requireActual("../../../utils/i18n");

  // Create a mock that allows us to control the cached language
  let mockCachedLanguage = "en";

  return {
    ...originalModule,
    tSync: jest.fn((key, variables = null) => {
      // Call the original tSync but with our controlled language
      const translations = originalModule.loadTranslations
        ? originalModule.loadTranslations(mockCachedLanguage)
        : require(`../../../locales/${mockCachedLanguage}/server.js`);

      // Split the key into parts and traverse the translations object
      const parts = key.split(".");
      let value = translations;
      for (const part of parts) {
        value = value?.[part];
        if (!value) break;
      }

      // If no translation found, try English, then return the key
      if (!value && mockCachedLanguage !== "en") {
        const defaultTranslations = require("../../../locales/en/server.js");
        value = defaultTranslations;
        for (const part of parts) {
          value = value?.[part];
          if (!value) break;
        }
      }

      const translation = value || key;
      return translation;
    }),
    setMockLanguage: (lang) => {
      mockCachedLanguage = lang;
    },
  };
});

const { setMockLanguage } = require("../../../utils/i18n");

describe("Validation Prompt Internationalization", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("should translate validation content headers synchronously in English", () => {
    setMockLanguage("en");

    const userQuestionHeader = tSync(
      "validation.content.user-question-sources-header"
    );
    const systemResponseHeader = tSync(
      "validation.content.system-response-header"
    );
    const notProvided = tSync("validation.content.not-provided");

    // Verify translations are correct
    expect(userQuestionHeader).toBe("USER QUESTION AND SOURCES");
    expect(systemResponseHeader).toBe("SYSTEM RESPONSE");
    expect(notProvided).toBe("Not provided");

    // Verify they are synchronous (not promises)
    expect(typeof userQuestionHeader).toBe("string");
    expect(userQuestionHeader).not.toBeInstanceOf(Promise);
  });

  test("should translate validation content headers synchronously in Swedish", () => {
    setMockLanguage("sv");

    const userQuestionHeader = tSync(
      "validation.content.user-question-sources-header"
    );
    const systemResponseHeader = tSync(
      "validation.content.system-response-header"
    );
    const notProvided = tSync("validation.content.not-provided");

    // Verify translations are correct
    expect(userQuestionHeader).toBe("ANVÄNDARFRÅGA OCH KÄLLOR");
    expect(systemResponseHeader).toBe("SYSTEMSVAR");
    expect(notProvided).toBe("Ej tillhandahållet");

    // Verify they are synchronous (not promises)
    expect(typeof userQuestionHeader).toBe("string");
    expect(userQuestionHeader).not.toBeInstanceOf(Promise);
  });

  test("should translate manual work estimator headers synchronously", () => {
    setMockLanguage("en");

    const userQuestionHeader = tSync(
      "manualWorkEstimator.content.user-question-header"
    );
    const systemResponseHeader = tSync(
      "manualWorkEstimator.content.system-response-header"
    );

    // Verify translations are correct
    expect(userQuestionHeader).toBe("USER QUESTION");
    expect(systemResponseHeader).toBe("SYSTEM RESPONSE");

    // Verify they are synchronous (not promises)
    expect(typeof userQuestionHeader).toBe("string");
    expect(userQuestionHeader).not.toBeInstanceOf(Promise);
  });

  test("should perform translations quickly and synchronously", () => {
    setMockLanguage("en");

    const start = Date.now();
    const result = tSync("validation.content.user-question-sources-header");
    const end = Date.now();

    // Should be fast and synchronous
    expect(result).toBe("USER QUESTION AND SOURCES");
    expect(end - start).toBeLessThan(10); // Should complete in less than 10ms
    expect(typeof result).toBe("string");
    expect(result).not.toBeInstanceOf(Promise);
  });
});
