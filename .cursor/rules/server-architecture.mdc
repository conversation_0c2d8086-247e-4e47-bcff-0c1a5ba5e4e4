---
description: Guidelines for server architecture and implementation
globs: server/**/*.js
alwaysApply: false
---

# Server Architecture

## Server Entry Point

The server entry point is `server/index.js`, which:

1. Loads environment variables
2. Sets up Express and an API router
3. Configures body parser and CORS
4. Sets up SSL or non-SSL server based on configuration
5. Registers endpoints
6. Serves static files in production
7. Starts the server on the configured port

## Key Directories

- `server/endpoints/`: API endpoints for various features
- `server/models/`: Data models wrapping Prisma client
- `server/prisma/`: Database schema and migrations
- `server/utils/`: Utilities and helpers
- `server/swagger/`: OpenAPI documentation
- `server/jobs/`: Background jobs
- `server/storage/`: Storage for documents, models, and logs

## Endpoint Organization

- Internal endpoints are placed outside the `/api/` directory
- Standard REST API endpoints are in `/api/` subdirectories
- Main endpoint files:
  - `workspaces.js`: Workspace management
  - `chat.js`: Chat functionality
  - `system.js`: System-level operations
  - `document.js`: Document management
  - `admin.js`: Admin operations
  - `extensions/index.js`: Proxies requests to collector

## RAG Workflow

1. **Ingest Documents**: Documents are chunked and embedded in vector databases
2. **Query Execution**: User queries are vectorized and matched against stored vectors
3. **LLM Response**: Retrieved documents are combined with user prompts and sent to LLMs
4. **Chat & History**: Q&A pairs are stored for future reference

## Background Jobs

The `server/jobs/` directory contains background jobs, including:

- `sync-watched.documents.js`: Synchronizes external documents (GitHub, Confluence, etc.)

## Utilities

The `server/utils/` directory contains various utilities:

- `AiProviders/`: LLM provider integrations
- `EmbeddingEngines/`: Embedding model integrations
- `vectorDbProviders/`: Vector database integrations
- `chats/`: Chat flow orchestration
- `DocumentManager/`: Document management
- `helpers/chat/`: Prompt construction and token management
- `files/`: File operations
- `boot/`: Server boot logic
- `middleware/`: Route-level middleware
- `agents/`: Agent plugins for advanced queries

## Best Practices

- Follow the established patterns for endpoint implementation
- Use the appropriate models for database operations
- Implement proper validation and error handling
- Consider performance implications of operations
- Document complex logic and APIs
- Test endpoints with various inputs and edge cases
