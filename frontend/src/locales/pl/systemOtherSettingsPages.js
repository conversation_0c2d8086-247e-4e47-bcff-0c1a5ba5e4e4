export default {
  // Bsed on this order of pages in system settings:
  // Customization (Appearance)
  //Admin: System settings
  //Admin: Users
  //Admin: Invites
  //Admin: Workspaces
  //Admin: MCP Servers
  //Admin: Feedback List
  //Admin: Workspace chats
  //Admin: Default system
  //Admin: Document Builder
  //Admin: DD Prompt settings
  //Admin: Chat UI settings
  //AI-Providers: LLM
  //AI-Providers: Prompt Upgrade LLM
  //AI-Providers: Validation LLM
  //AI-Providers: CDB LLM
  //AI-Providers: Custom User AI
  //AI-Providers:  Vector Database
  //AI-Providers: Re-ranking settings
  //AI-Providers: Deep search
  //AI-Providers: Embedder
  //AI-Providers: Text Splitter & Chunking
  //AI-Providers: Voice & Speech
  //AI-Providers: transcription
  //AI-Providers: PDR System
  //Agent skills
  //Tools: Chat Embed History
  //Tools: Chat Embed
  //Tools: Event logs
  //Tools: Developer API
  //Tools: Browser Extension
  //Security
  //Document Drafting: Configuration
  //Document Drafting: Drafting LLM
  //Document Drafting: Chat Settings

  // =========================
  // LLM PREFERENCE PAGE
  // =========================
  llm: {
    title: "Preferencje LLM",
    description:
      "Są to dane uwierzytelniające i ustawienia dla preferowanego dostawcy czatu i osadzania LLM. Ważne, aby te klucze były aktualne i poprawne, w przeciwnym razie system nie będzie działał poprawnie.",
    provider: "Dostawca LLM",
    "secondary-provider": "Drugorzędny dostawca LLM",
    "none-selected": "Nic nie wybrano",
    "select-llm":
      "Agenci nie będą działać, dopóki nie zostanie dokonany ważny wybór.",
    "search-llm": "Szukaj wszystkich dostawców LLM",
    "context-window-warning":
      "Ostrzeżenie: Nie udało się pobrać okna kontekstu dla wybranego modelu.",
    "context-window-waiting":
      " -- oczekiwanie na informacje o oknie kontekstu -- ",
    "validation-prompt": {
      disable: {
        label: "Wyłącz prompt walidacyjny",
        description:
          "Po włączeniu przycisk walidacji nie będzie widoczny w interfejsie użytkownika.",
      },
    },
    "prompt-upgrade": {
      title: "Dostawca LLM do ulepszania promptów",
      description:
        "Konkretny dostawca LLM i model, który będzie używany do ulepszania promptów użytkownika. Domyślnie używa systemowego dostawcy LLM i ustawień.",
      search: "Wyszukaj dostępnych dostawców LLM dla tej funkcji",
      template: "Szablon ulepszania promptów",
      "template-description":
        "Ten szablon będzie używany podczas ulepszania promptów. Użyj {{prompt}}, aby odnieść się do tekstu, który ma zostać ulepszony.",
      "template-placeholder":
        "Wprowadź szablon, który będzie używany do ulepszania promptów...",
      "template-hint":
        "Przykład: Proszę ulepszyć następujący tekst zachowując jego znaczenie: {{prompt}}",
    },
    "logo-alt": "{{name}} logo",
    "context-window": "Okno kontekstu",
    "default-context-window": "(domyślny rozmiar dla tego dostawcy)",
    tokens: "tokeny",
    "save-error": "Nie udało się zapisać ustawień LLM",
  },
  // =========================
  // LLM PROVIDER DESCRIPTIONS
  // =========================
  "llm-provider": {
    openai: "Standardowa opcja dla większości zastosowań niekomercyjnych.",
    azure: "Opcja korporacyjna OpenAI hostowana na usługach Azure.",
    anthropic: "Przyjazny asystent AI hostowany przez Anthropic.",
    gemini: "Największy i najbardziej zaawansowany model AI Google.",
    huggingface:
      "Dostęp do ponad 150 000 otwartoźródłowych LLM oraz globalnej społeczności AI.",
    ollama: "Uruchamiaj LLM lokalnie na własnym komputerze.",
    lmstudio:
      "Odkrywaj, pobieraj i uruchamiaj tysiące nowoczesnych LLM kilkoma kliknięciami.",
    localai: "Uruchamiaj LLM lokalnie na własnym komputerze.",
    togetherai: "Uruchamiaj modele open source od Together AI.",
    mistral: "Uruchamiaj modele open source od Mistral AI.",
    perplexityai:
      "Uruchamiaj potężne modele połączone z internetem, hostowane przez Perplexity AI.",
    openrouter: "Zunifikowany interfejs dla LLM.",
    groq: "Najszybsze inferencje LLM dostępne dla aplikacji AI w czasie rzeczywistym.",
    koboldcpp: "Uruchamiaj lokalne LLM przy użyciu koboldcpp.",
    oobabooga:
      "Uruchamiaj lokalne LLM przy użyciu interfejsu Text Generation Web UI Oobabooga.",
    cohere: "Uruchamiaj potężne modele osadzania od Cohere.",
    lite: "Uruchamiaj kompatybilny z OpenAI proxy LiteLLM dla różnych LLM.",
    "generic-openai":
      "Połącz się z dowolną usługą kompatybilną z OpenAI za pomocą niestandardowej konfiguracji",
    native:
      "Użyj pobranego niestandardowego modelu Llama do czatowania w tej instancji.",
    xai: "Uruchamiaj potężne LLM xAI, takie jak Grok-2 i inne.",
    "aws-bedrock":
      "Uruchamiaj potężne modele podstawowe prywatnie z AWS Bedrock.",
    deepseek: "Uruchamiaj potężne modele LLM od DeepSeek.",
    fireworksai:
      "Najszybszy i najbardziej wydajny silnik inferencji do tworzenia gotowych do produkcji, złożonych systemów AI.",
    bedrock: "Uruchamiaj potężne modele podstawowe prywatnie z AWS Bedrock.",
  },
  "custom-user-ai": {
    title: "Niestandardowe AI użytkownika",
    settings: "Niestandardowe AI użytkownika",
    description: "Skonfiguruj dostawcę niestandardowego AI",
    "custom-model-reference": "Niestandardowa nazwa i opis modelu",
    "custom-model-reference-description":
      "Dodaj niestandardową referencję dla tego modelu. Będzie ona widoczna podczas używania selektora niestandardowego silnika AI użytkownika w panelu podpowiedzi.",
    "custom-model-reference-name": "Niestandardowa nazwa modelu",
    "custom-model-reference-description-label": "Opis modelu (opcjonalnie)",
    "custom-model-reference-description-placeholder":
      "Wpisz opcjonalny opis dla tego modelu",
    "custom-model-reference-name-placeholder":
      "Wprowadź niestandardową nazwę dla tego modelu",
    "model-ref-placeholder":
      "Wprowadź niestandardową nazwę lub opis dla tej konfiguracji modelu",
    "enter-custom-model-reference":
      "Wprowadź niestandardową nazwę dla tego modelu",
    "standard-engine": "Standardowy silnik AI",
    "standard-engine-description":
      "Nasz domyślny silnik, przydatny do większości zadań",
    "dynamic-context-window-percentage":
      "Procent dynamicznego okna kontekstowego",
    "dynamic-context-window-percentage-desc":
      "Kontroluje, ile okna kontekstowego LLM może być używane dla dodatkowych źródeł (10-100%)",
    "no-alternative-title": "Nie wybrano modelu alternatywnego",
    "no-alternative-desc":
      "Gdy ta opcja jest wybrana, użytkownicy nie mają możliwości wyboru modelu alternatywnego.",
    "select-option": "Wybierz niestandardowy profil AI",
    tab: {
      "custom-1": "Niestandardowy silnik 1",
      "custom-2": "Niestandardowy silnik 2",
      "custom-3": "Niestandardowy silnik 3",
      "custom-4": "Niestandardowy silnik 4",
      "custom-5": "Niestandardowy silnik 5",
      "custom-6": "Niestandardowy silnik 6",
    },
    engine: {
      "custom-1": "Niestandardowy silnik 1",
      "custom-2": "Niestandardowy silnik 2",
      "custom-3": "Niestandardowy silnik 3",
      "custom-4": "Niestandardowy silnik 4",
      "custom-5": "Niestandardowy silnik 5",
      "custom-6": "Niestandardowy silnik 6",
      "custom-1-title": "Niestandardowy silnik 1",
      "custom-2-title": "Niestandardowy silnik 2",
      "custom-3-title": "Niestandardowy silnik 3",
      "custom-4-title": "Niestandardowy silnik 4",
      "custom-5-title": "Niestandardowy silnik 5",
      "custom-6-title": "Niestandardowy silnik 6",
      "custom-1-description":
        "Skonfiguruj ustawienia dla Niestandardowego silnika 1",
      "custom-2-description":
        "Skonfiguruj ustawienia dla Niestandardowego silnika 2",
      "custom-3-description":
        "Skonfiguruj ustawienia dla Niestandardowego silnika 3",
      "custom-4-description":
        "Skonfiguruj ustawienia dla Niestandardowego silnika 4",
      "custom-5-description":
        "Skonfiguruj ustawienia dla Niestandardowego silnika 5",
      "custom-6-description":
        "Skonfiguruj ustawienia dla Niestandardowego silnika 6",
    },
    "option-number": "Opcja {{number}}",
    "llm-provider-selection": "Wybór dostawcy LLM",
    "llm-provider-selection-desc":
      "Wybierz dostawcę LLM dla tej niestandardowej konfiguracji AI",
    "custom-option": "Opcja niestandardowa",
    saving: "Zapisywanie...",
    "save-changes": "Zapisz zmiany",
    "model-ref-saved": "Pomyślnie zapisano niestandardowe ustawienia modelu",
    "model-ref-save-failed":
      "Nie udało się zapisać niestandardowych ustawień modelu: {{error}}",
    "llm-settings-save-failed": "Nie udało się zapisać ustawień LLM: {{error}}",
    "settings-fetch-failed": "Nie udało się pobrać ustawień",
    "llm-saved": "Pomyślnie zapisano ustawienia LLM",
    "select-provider-first":
      "Wybierz dostawcę LLM, aby skonfigurować ustawienia modelu. Po skonfigurowaniu ta opcja będzie dostępna jako niestandardowy silnik AI w interfejsie użytkownika.",
  },
  // =========================
  // CDB LLM PREFERENCE
  // =========================
  "cdb-llm-preference": {
    title: "Preferencje LLM CDB",
    settings: "LLM CDB",
    description: "Konfiguruj dostawcę LLM dla CDB",
  },
  // =========================
  // TEMPLATE LLM PREFERENCE
  // =========================
  "template-llm-preference": {
    title: "Preferencje LLM szablonu",
    settings: "Szablon LLM",
    description:
      "Wybierz dostawcę LLM używanego do generowania szablonów dokumentów. Domyślnie używany jest dostawca systemowy.",
    "toast-success": "Zaktualizowano ustawienia LLM szablonu",
    "toast-fail": "Nie udało się zaktualizować ustawień LLM szablonu",
    saving: "Zapisywanie...",
    "save-changes": "Zapisz zmiany",
  },

  // =========================
  // AUDIO PREFERENCE
  // =========================
  audio: {
    title: "Preferencje mowy na tekst",
    provider: "Dostawca",
    "system-native": "System natywny",
    "desc-speech":
      "Tutaj możesz określić, jakich dostawców tekstu na mowę i mowy na tekst chcesz używać w swojej platformie. Domyślnie używamy wbudowanego wsparcia przeglądarki dla tych usług, ale możesz chcieć użyć innych.",
    "title-text": "Preferencje tekstu na mowę",
    "desc-text":
      "Tutaj możesz określić, jakich dostawców tekstu na mowę chcesz używać w swojej platformie. Domyślnie używamy wbudowanego wsparcia przeglądarki dla tych usług, ale możesz chcieć użyć innych.",
    "desc-config":
      "Nie jest wymagana żadna konfiguracja dla natywnego tekstu na mowę w przeglądarce.",
    "placeholder-stt": "Szukaj dostawców mowy na tekst",
    "placeholder-tts": "Szukaj dostawców tekstu na mowę",
    "native-stt":
      "Używa wbudowanej usługi STT przeglądarki, jeśli jest obsługiwana.",
    "native-tts":
      "Używa wbudowanej usługi TTS przeglądarki, jeśli jest obsługiwana.",
    "piper-tts":
      "Uruchamiaj modele TTS lokalnie w swojej przeglądarce prywatnie.",
    "openai-description": "Używaj głosów i technologii tekstu na mowę OpenAI.",
    openai: {
      "api-key": "Klucz API",
      "api-key-placeholder": "Klucz API OpenAI",
      "voice-model": "Model głosowy",
    },
    elevenlabs: "Użyj głosów i technologii ElevenLabs dla tekstu na mowę.",
  },

  // =========================
  // TRANSCRIPTION PREFERENCE
  // =========================
  transcription: {
    title: "Preferencje modelu transkrypcji",
    description:
      "Są to dane uwierzytelniające i ustawienia dla preferowanego dostawcy modelu transkrypcji. Ważne, aby te klucze były aktualne i poprawne, w przeciwnym razie pliki multimedialne i audio nie zostaną przetranskrybowane.",
    provider: "Dostawca transkrypcji",
    "warn-start":
      "Użycie lokalnego modelu whisper na maszynach z ograniczoną pamięcią RAM lub CPU może zatrzymać platformę podczas przetwarzania plików multimedialnych.",
    "warn-recommend":
      "Zalecamy co najmniej 2GB pamięci RAM i przesyłanie plików <10Mb.",
    "warn-end":
      "Wbudowany model zostanie automatycznie pobrany przy pierwszym użyciu.",
    "search-audio": "Szukaj dostawców transkrypcji audio",
    "api-key": "Klucz API",
    "api-key-placeholder": "Klucz API OpenAI",
    "whisper-model": "Model Whisper",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Domyślny Wbudowany",
    "default-built-in-desc":
      "Uruchom wbudowany model whisper prywatnie na tej instancji.",
    "openai-name": "OpenAI",
    "openai-desc":
      "Wykorzystaj model OpenAI Whisper-large używając swojego klucza API.",
    "model-turbo": "openai/whisper-large-v3-turbo", // Nowa nazwa modelu
    "model-size-turbo": "(~810mb)", // Nowy rozmiar modelu
  },

  // =========================
  // EMBEDDING PREFERENCE
  // =========================
  embedding: {
    title: "Preferencje osadzania",
    "desc-start":
      "Kiedy używasz LLM, który nie obsługuje natywnie silnika osadzania – możesz dodatkowo musieć podać dane uwierzytelniające do osadzania tekstu.",
    "desc-end":
      "Osadzanie to proces przekształcania tekstu w wektory. Te dane uwierzytelniające są wymagane, aby przekształcić Twoje pliki i zapytania w format, który platforma może przetwarzać.",
    provider: {
      title: "Dostawca osadzania",
      description:
        "Nie jest wymagana żadna konfiguracja podczas korzystania z natywnego silnika osadzania platformy.",
      "search-embed": "Szukaj wszystkich dostawców osadzania",
      search: "Szukaj wszystkich dostawców osadzania",
      select: "Wybierz dostawcę osadzania",
    },
    workspace: {
      title: "Preferencje osadzania dla przestrzeni roboczej",
      description:
        "Określony dostawca i model osadzania, który będzie używany dla tej przestrzeni roboczej. Domyślnie używane są ustawienia i dostawca systemowy.",
      "multi-model":
        "Wsparcie dla wielu modeli nie jest jeszcze obsługiwane dla tego dostawcy.",
      "workspace-use": "Ta przestrzeń robocza będzie używać",
      "model-set": "zestawu modeli dla systemu.",
      embedding: "Model osadzania dla przestrzeni roboczej",
      model:
        "Określony model osadzania, który będzie używany dla tej przestrzeni roboczej. Jeśli pozostanie pusty, użyte zostaną systemowe preferencje osadzania.",
      wait: "-- oczekiwanie na modele --",
      setup: "Konfiguracja",
      use: "Aby użyć",
      "need-setup":
        "jako osadzacz dla tej przestrzeni, musisz go najpierw skonfigurować.",
      cancel: "Anuluj",
      save: "zapisz",
      settings: "ustawienia",
      search: "Szukaj wszystkich dostawców osadzania",
      "need-llm":
        "jako LLM dla tej przestrzeni, musisz go najpierw skonfigurować.",
      "save-error": "Nie udało się zapisać ustawień {{provider}}: {{error}}",
      "system-default": "Ustawienia systemowe",
      "system-default-desc":
        "Użyj systemowych preferencji osadzania dla tej przestrzeni roboczej.",
    },
    warning: {
      "switch-model":
        "Zmiana modelu osadzania spowoduje, że poprzednio osadzone dokumenty przestaną działać podczas czatu. Musisz je usunąć z każdej przestrzeni roboczej i całkowicie usunąć i ponownie przesłać, aby mogły być osadzone przy użyciu nowego modelu osadzania.",
    },
  },
  // =========================
  // TEXT SPLITTING & CHUNKING
  // =========================
  text: {
    title: "Preferencje dzielenia tekstu i fragmentacji",
    "desc-start":
      "Czasami możesz chcieć zmienić domyślny sposób, w jaki nowe dokumenty są dzielone i fragmentowane przed dodaniem do bazy danych wektorowych.",
    "desc-end":
      "Modyfikuj to ustawienie tylko, jeśli rozumiesz, jak działa dzielenie tekstu i jakie ma skutki uboczne.",
    "warn-start": "Zmiany tutaj będą miały zastosowanie tylko do",
    "warn-center": "nowo osadzonych dokumentów",
    "warn-end": ", a nie do już istniejących dokumentów.",
    method: {
      title: "Metoda dzielenia tekstu",

      "native-explain":
        "Użyj lokalnego rozmiaru i nakładania się fragmentów do podziału.",

      "jina-explain":
        "Deleguj fragmentację/segmentację do wbudowanej metody Jina.",

      "jina-info": "Fragmentacja Jina aktywna.",

      jina: {
        api_key: "Klucz API Jina",
        api_key_desc:
          "Wymagany do korzystania z usługi segmentacji Jina. Klucz zostanie zapisany w twoim środowisku.",
        max_tokens: "Jina: Maksymalna liczba tokenów na fragment",
        max_tokens_desc:
          "Określa maksymalną liczbę tokenów w każdym fragmencie dla segmentatora Jina (maksymalnie 2000 tokenów).",
        return_tokens: "Zwróć informacje o tokenach",
        return_tokens_desc:
          "Dołącz liczbę tokenów i informacje o tokenizatorze w odpowiedzi.",
        return_chunks: "Zwróć informacje o fragmentach",
        return_chunks_desc:
          "Dołącz szczegółowe informacje o wygenerowanych fragmentach w odpowiedzi.",
      },
    },
    size: {
      title: "Rozmiar fragmentu tekstu",
      description:
        "To maksymalna liczba znaków, która może być zawarta w pojedynczym wektorze.",
      recommend: "Maksymalna długość modelu osadzania wynosi",
    },
    overlap: {
      title: "Nakładanie się fragmentów tekstu",
      description:
        "To maksymalne nakładanie się znaków, które występuje podczas fragmentacji pomiędzy dwoma przylegającymi fragmentami tekstu.",
      error:
        "Nakładanie się fragmentów nie może być większe lub równe rozmiarowi fragmentu.",
    },
  },
  // =========================
  // CONTEXTUAL SETTINGS
  // =========================
  contextual: {
    checkbox: {
      label: "Osadzanie kontekstowe",
      hint: "Włącz osadzanie kontekstowe, aby ulepszyć proces osadzania dodatkowymi parametrami",
    },
    systemPrompt: {
      label: "Polecenie systemowe",
      placeholder: "Wprowadź wartość...",
      description:
        "Przykład: Podaj krótki, zwięzły kontekst, który umiejscowi ten fragment w całym dokumencie w celu poprawy wyszukiwania tego fragmentu. Odpowiedz tylko zwięźle, nic więcej.",
    },
    userPrompt: {
      label: "Polecenie użytkownika",
      placeholder: "Wprowadź wartość...",
      description:
        "Przykład: <document>\n{file}\n</document>\nOto fragment, który chcemy umiejscowić w całym dokumencie\n<chunk>\n{chunk}\n</chunk>",
    },
  },
  // =========================
  // CHAT UI SETTINGS
  // =========================
  "chat-ui-settings": {
    title: "Ustawienia interfejsu czatu",
    description: "Skonfiguruj ustawienia czatu.",
    auto_submit: {
      title: "Automatyczne wysyłanie wprowadzania głosowego",
      description:
        "Automatycznie wysyłaj wprowadzanie głosowe po okresie ciszy",
    },
    auto_speak: {
      title: "Automatyczne odczytywanie odpowiedzi",
      description: "Automatycznie odczytuj odpowiedzi AI",
    },
  },
  // =========================
  // PIPER TTS OPTIONS
  // =========================
  piperTTS: {
    description:
      "Wszystkie modele PiperTTS będą uruchamiane lokalnie w Twojej przeglądarce. Może to być wymagające zasobów na słabszych urządzeniach.",
    "voice-model": "Wybór modelu głosu",
    "loading-models": "-- ładowanie dostępnych modeli --",
    "stored-indicator":
      'Symbol "✔" oznacza, że ten model jest już przechowywany lokalnie i nie trzeba go pobierać przed uruchomieniem.',
    "flush-cache": "Wyczyść pamięć głosów",
    "flush-success": "Wszystkie głosy wyczyszczone z pamięci przeglądarki",
    demo: {
      stop: "Zatrzymaj demo",
      loading: "Ładowanie głosu",
      play: "Odtwórz próbkę",
      text: "Cześć, witamy w IST Legal!",
    },
  },

  // =========================
  // VECTOR DATABASE (SYSTEM)
  // =========================
  vector: {
    title: "Baza danych wektorowych",
    description:
      "To są poświadczenia i ustawienia określające sposób działania Twojej instancji platformy. Ważne jest, aby te klucze były aktualne i poprawne.",
    provider: {
      title: "Dostawca bazy danych wektorowych",
      description: "Dla LanceDB nie jest wymagana konfiguracja.",
      "search-db": "Przeszukaj wszystkich dostawców baz danych wektorowych",
      search: "Przeszukaj wszystkie bazy danych wektorowych",
      select: "Wybierz dostawcę bazy danych wektorowych",
    },
    warning:
      "Zmiana bazy danych wektorowych będzie wymagała ponownego osadzenia wszystkich dokumentów we wszystkich odpowiednich obszarach roboczych. Może to zająć trochę czasu.",
    search: {
      title: "Tryb wyszukiwania wektorowego",
      mode: {
        "globally-enabled":
          "To ustawienie jest kontrolowane globalnie w ustawieniach systemowych. Odwiedź ustawienia systemowe, aby zmienić zachowanie ponownego rankingu.",
        default: "Wyszukiwanie standardowe",
        "default-desc":
          "Standardowe wyszukiwanie podobieństwa wektorowego bez ponownego rankingu.",
        "accuracy-optimized": "Zoptymalizowane pod kątem dokładności",
        "accuracy-desc":
          "Ponownie szereguje wyniki, aby poprawić dokładność przy użyciu uwagi krzyżowej.",
      },
    },
  },
  // =========================
  // VECTOR DATABASE PROVIDER DESCRIPTIONS
  // =========================
  vectordb: {
    lancedb:
      "W 100% lokalna baza danych wektorowych, działająca na tej samej instancji co platforma.",
    chroma:
      "Otwarta baza danych wektorowych, którą możesz hostować samodzielnie lub w chmurze.",
    pinecone:
      "W pełni oparta na chmurze baza danych wektorowych dla zastosowań korporacyjnych.",
    zilliz:
      "Baza danych wektorowych hostowana w chmurze, stworzona dla przedsiębiorstw z zgodnością SOC 2.",
    qdrant: "Otwarta, lokalna i rozproszona chmurowa baza danych wektorowych.",
    weaviate:
      "Otwarta, lokalna i chmurowa baza danych wektorowych multimodalnych.",
    milvus: "Otwarty kod, wysoce skalowalny i niesamowicie szybki.",
    astra: "Wyszukiwanie wektorowe dla rzeczywistego GenAI.",
  },
  // =========================
  // DEEP SEARCH SETTINGS
  // =========================
  deep_search: {
    title: "Wyszukiwanie zaawansowane",
    description:
      "Skonfiguruj możliwości wyszukiwania w sieci dla odpowiedzi w czacie. Po włączeniu system może przeszukiwać sieć w poszukiwaniu informacji, aby ulepszyć odpowiedzi.",
    enable: "Włącz wyszukiwanie zaawansowane",
    enable_description:
      "Pozwól systemowi wyszukiwać informacje w sieci podczas odpowiadania na zapytania.",
    provider_settings: "Ustawienia dostawcy",
    provider: "Dostawca wyszukiwania",
    model: "Model",
    api_key: "Klucz API",
    api_key_placeholder: "Wprowadź swój klucz API",
    api_key_placeholder_set:
      "Klucz API jest ustawiony (wprowadź nowy klucz, aby zmienić)",
    api_key_help:
      "Twój klucz API jest przechowywany bezpiecznie i używany tylko do zapytań wyszukiwania w sieci.",
    context_percentage: "Procent kontekstu",
    context_percentage_help:
      "Procent okna kontekstu LLM przeznaczony na wyniki wyszukiwania w sieci (5-20%).",
    fetch_error: "Nie udało się pobrać ustawień wyszukiwania zaawansowanego",
    save_success: "Ustawienia wyszukiwania zaawansowanego zapisane pomyślnie",
    save_error:
      "Nie udało się zapisać ustawień wyszukiwania zaawansowanego: {{error}}",
    toast_success: "Ustawienia wyszukiwania zaawansowanego zapisane pomyślnie",
    toast_error:
      "Nie udało się zapisać ustawień wyszukiwania zaawansowanego: {{error}}",
    brave_recommended:
      "Brave Search jest obecnie zalecanym i najbardziej niezawodnym dostawcą wyszukiwania.",
  },
  // =========================
  // PDR SETTINGS
  // =========================
  "pdr-settings": {
    title: "Ustawienia PDR",

    description:
      "Skonfiguruj ustawienia wyszukiwania dokumentów nadrzędnych (PDR) dla Twoich przestrzeni roboczych.",

    "desc-end":
      "Te ustawienia wpływają na sposób przetwarzania dokumentów PDR i ich wykorzystanie w odpowiedziach czatu.",

    "global-override": {
      title: "Globalne nadpisanie dynamicznego PDR",
      description:
        "Po włączeniu ta opcja wymusi włączenie dynamicznego PDR dla wszystkich przestrzeni roboczych, niezależnie od ich indywidualnych ustawień.",
    },

    "toast-success": "Ustawienia PDR zaktualizowane",
    "toast-fail": "Nie udało się zaktualizować ustawień PDR",
    "adjacent-vector-limit": "Limit sąsiednich wektorów",
    "adjacent-vector-limit-desc": "Limit dla sąsiednich wektorów.",

    "adjacent-vector-limit-placeholder":
      "Wprowadź limit dla sąsiednich wektorów",

    "keep-pdr-vectors": "Zachowaj wektory PDR",
    "keep-pdr-vectors-desc": "Opcja zachowania wektorów PDR.",
  },
  // =========================
  // ADMIN AGENTS
  // =========================
  agents: {
    title: "Umiejętności Agenta",
    "agent-skills": "Konfiguruj i zarządzaj możliwościami agenta",
    "custom-skills": "Niestandardowe Umiejętności",
    back: "Powrót",
    "select-skill": "Wybierz umiejętność do konfiguracji",
    "preferences-saved": "Pomyślnie zapisano preferencje agenta",
    "preferences-failed": "Nie udało się zapisać preferencji agenta",
    "skill-status": {
      on: "Włączone",
      off: "Wyłączone",
    },
    "skill-config-updated":
      "Konfiguracja umiejętności została pomyślnie zaktualizowana",
  },
  // =========================
  // AGENT MENU
  // =========================
  "agent-menu.default-agent": "Domyślny agent",
  "agent-menu.ability.rag-search": "RAG Search",
  "agent-menu.ability.web-scraping": "Pobieranie zawartości stron",
  "agent-menu.ability.web-browsing": "Przeglądanie stron",
  "agent-menu.ability.save-file-to-browser": "Zapisz plik w przeglądarce",
  "agent-menu.ability.list-documents": "Wyświetl dokumenty",
  "agent-menu.ability.summarize-document": "Podsumuj dokument",
  "agent-menu.ability.chart-generation": "Generowanie wykresów",
  // =========================
  // BADGES AGENT PAGE
  // =========================
  badges: {
    default: {
      text: "Domyślny",
      tooltip:
        "Ta umiejętność jest włączona domyślnie i nie może zostać wyłączona.",
    },
  },
  // =========================
  // EMBED CHATS
  // =========================
  "embed-chats": {
    title: "Osadzone czaty",
    export: "Eksportuj",
    description:
      "To są wszystkie zapisane czaty i wiadomości z każdego osadzenia, które opublikowałeś.",
    table: {
      embed: "Osadzenie",
      sender: "Nadawca",
      message: "Wiadomość",
      response: "Odpowiedź",
      at: "Wysłane o",
    },
    delete: {
      title: "Usuń czat",
      message: "Czy na pewno chcesz usunąć ten czat?",
    },
    config: {
      "delete-title": "Usuń osadzenie",
      "delete-message":
        "Czy na pewno chcesz usunąć to osadzenie?\n\nTa akcja jest nieodwracalna.",
      "disable-title": "Dezaktywuj osadzenie",
      "disable-message":
        "Czy na pewno chcesz dezaktywować to osadzenie?\n\nTa akcja jest nieodwracalna.",
      "enable-title": "Aktywuj osadzenie",
      "enable-message":
        "Czy na pewno chcesz aktywować to osadzenie?\n\nTa akcja jest nieodwracalna.",
    },
  },
  // =========================
  // EMBEDDABLE CHAT WIDGETS
  // =========================
  embeddable: {
    title: "Osadzalne widżety czatu",
    description:
      "Osadzalne widżety czatu to interfejsy czatu dostępne publicznie, powiązane z pojedynczą przestrzenią roboczą. Pozwalają one tworzyć przestrzenie, które następnie możesz opublikować na świecie.",
    create: "Utwórz osadzenie",
    table: {
      workspace: "Przestrzeń robocza",
      chats: "Wysłane czaty",
      Active: "Aktywne domeny",
    },
  },
  // =========================
  // NEW EMBED
  // =========================
  "new-embed": {
    title: "Utwórz nowe osadzenie dla przestrzeni roboczej",
    error: "Błąd: ",
    "desc-start":
      "Po utworzeniu osadzenia otrzymasz link, który możesz opublikować na swojej stronie internetowej za pomocą prostego",
    script: "skryptu",
    tag: "tagu.",
    cancel: "Anuluj",
    "create-embed": "Utwórz osadzenie",
    workspace: "Przestrzeń robocza",
    "desc-workspace":
      "To jest przestrzeń robocza, na której opiera się Twoje okno czatu. Wszystkie ustawienia domyślne zostaną odziedziczone z przestrzeni, chyba że zostaną zmienione w tej konfiguracji.",
    "allowed-chat": "Dozwolona metoda czatu",
    "desc-query":
      "Ustaw, jak powinien działać Twój chatbot. Zapytanie oznacza, że odpowie tylko, jeśli dokument pomoże odpowiedzieć na zapytanie.",
    "desc-chat":
      "Czat otwiera czat nawet dla ogólnych pytań i może odpowiadać na zupełnie niezwiązane zapytania dotyczące Twojej przestrzeni roboczej.",
    "desc-response":
      "Czat: Odpowiadaj na wszystkie pytania niezależnie od kontekstu",
    "query-response":
      "Zapytanie: Odpowiadaj tylko na czaty związane z dokumentami w przestrzeni roboczej",
    restrict: "Ogranicz żądania z domen",
    filter:
      "Ten filtr zablokuje wszystkie żądania pochodzące z domen innych niż te wymienione poniżej.",
    "use-embed":
      "Pozostawienie tego pustego oznacza, że każdy może użyć Twojego osadzenia na dowolnej stronie.",
    "max-chats": "Maksymalna liczba czatów na dzień",
    "limit-chats":
      "Ogranicz liczbę czatów, które to osadzone czat może obsłużyć w ciągu 24 godzin. Zero oznacza brak ograniczeń.",
    "chats-session": "Maksymalna liczba czatów na sesję",
    "limit-chats-session":
      "Ogranicz liczbę czatów, które użytkownik sesji może wysłać przy użyciu tego osadzenia w ciągu 24 godzin. Zero oznacza brak ograniczeń.",
    "enable-dynamic": "Włącz dynamiczne użycie modelu",
    "llm-override":
      "Pozwól na ustawienie preferowanego modelu LLM, aby zastąpić domyślny model przestrzeni roboczej.",
    "llm-temp": "Włącz dynamiczną temperaturę LLM",
    "desc-temp":
      "Pozwól na ustawienie temperatury LLM, aby zastąpić domyślną wartość przestrzeni roboczej.",
    "prompt-override": "Włącz nadpisanie zapytania",
    "desc-override":
      "Pozwól na ustawienie zapytania systemowego, aby zastąpić domyślne ustawienia przestrzeni roboczej.",
  },
  // =========================
  // EMBED CONFIGS
  // =========================
  embedConfigs: {
    "show-code": "Pokaż kod",
    enable: "Włącz",
    disable: "Wyłącz",
    "all-domains": "wszystkie",
    "disable-confirm":
      "Czy na pewno chcesz wyłączyć to osadzenie?\nPo wyłączeniu osadzenie nie będzie już odpowiadać na żadne żądania czatu.",
    "delete-confirm":
      "Czy na pewno chcesz usunąć to osadzenie?\nPo usunięciu to osadzenie nie będzie już odpowiadać na czaty ani być aktywne.\n\nTa operacja jest nieodwracalna.",
    "disabled-toast": "Osadzenie zostało wyłączone",
    "enabled-toast": "Osadzenie jest aktywne",
  },
  // =========================
  // EVENT LOGS
  // =========================
  event: {
    title: "Logi zdarzeń",
    description:
      "Przeglądaj wszystkie działania i zdarzenia występujące w tej instancji w celach monitorowania.",
    clear: "Wyczyść logi zdarzeń",
    table: {
      type: "Typ zdarzenia",
      user: "Użytkownik",
      occurred: "Wystąpiło",
    },
  },
  // =========================
  // API KEYS
  // =========================
  api: {
    title: "Klucze API",
    description:
      "Klucze API umożliwiają posiadaczowi programowy dostęp do tej instancji oraz jej zarządzanie.",
    link: "Przeczytaj dokumentację API",
    generate: "Wygeneruj nowy klucz API",
    table: {
      key: "Klucz API",
      by: "Utworzony przez",
      created: "Utworzono",
    },
    new: {
      title: "Utwórz nowy klucz API",
      description:
        "Po utworzeniu klucz API może być używany do programowego dostępu i konfiguracji tej instancji.",
      doc: "Przeczytaj dokumentację API",
      cancel: "Anuluj",
      "create-api": "Utwórz klucz API",
    },
  },
  // =========================
  // BROWSER EXTENSION API KEYS
  // =========================
  "browser-extension-api": {
    title: "Klucze API",
    description: "Zarządzaj kluczami API do łączenia się z tą instancją.",
    "generate-key": "Wygeneruj nowy klucz API",
    "table-headers": {
      "connection-string": "Ciąg połączenia",
      "created-by": "Utworzony przez",
      "created-at": "Data utworzenia",
      actions: "Akcje",
    },
    "no-keys": "Nie znaleziono kluczy API",
    modal: {
      title: "Nowy klucz API rozszerzenia przeglądarki",
      "multi-user-warning":
        "Uwaga: Jesteś w trybie wieloużytkownikowym. Ten klucz API umożliwi dostęp do wszystkich przestrzeni roboczych powiązanych z Twoim kontem. Prosimy o ostrożne udostępnianie.",
      "create-description":
        'Po kliknięciu "Utwórz klucz API", ta instancja spróbuje utworzyć nowy klucz API dla rozszerzenia przeglądarki.',
      "connection-help":
        'Jeśli widzisz "Połączono z IST Legal" w rozszerzeniu, połączenie powiodło się. Jeśli nie, skopiuj ciąg połączenia i wklej go ręcznie do rozszerzenia.',
      cancel: "Anuluj",
      "create-key": "Utwórz klucz API",
      "copy-key": "Kopiuj klucz API",
      "key-copied": "Klucz API skopiowany!",
    },
    tooltips: {
      "copy-connection": "Kopiuj ciąg połączenia",
      "auto-connect": "Automatycznie połącz z rozszerzeniem",
    },
    confirm: {
      revoke:
        "Czy na pewno chcesz odwołać ten klucz API rozszerzenia przeglądarki?\nPo tej operacji nie będzie już możliwe jego użycie.\n\nTa akcja jest nieodwracalna.",
    },
    toasts: {
      "key-revoked":
        "Klucz API rozszerzenia przeglądarki został trwale odwołany",
      "revoke-failed": "Nie udało się odwołać klucza API",
      copied: "Ciąg połączenia skopiowany do schowka",
      connecting: "Próba połączenia z rozszerzeniem przeglądarki...",
    },
    "revoke-title": "Odwołaj klucz API rozszerzenia przeglądarki",
    "revoke-message":
      "Czy na pewno chcesz odwołać ten klucz API rozszerzenia przeglądarki?\nPo tej operacji nie będzie już możliwe jego użycie.\n\nTa akcja jest nieodwracalna.",
  },
  // =========================
  // SECURITY
  // =========================
  security: {
    "multi-user-mode-permanent":
      "Tryb wieloużytkownikowy jest trwale włączony ze względów bezpieczeństwa",
    "password-validation": {
      "restricted-chars":
        "Twoje hasło zawiera niedozwolone znaki. Dozwolone symbole to _,-,!,@,$,%,^,&,*,(,),;",
    },
    "public-workspace": {
      "access-description":
        "Po włączeniu każdy użytkownik może uzyskać dostęp do publicznych przestrzeni roboczych bez logowania.",
    },
    button: {
      saving: "Zapisywanie...",
      "save-changes": "Zapisz zmiany",
    },
  },
  // =========================
  // MULTI-USER MODE
  // =========================
  multi: {
    title: "Tryb wieloużytkownikowy",
    description:
      "Skonfiguruj swoją instancję, aby wspierała Twój zespół, aktywując Multi-User Mode.",
    enable: {
      "is-enable": "Tryb wieloużytkownikowy jest włączony",
      enable: "Włącz tryb wieloużytkownikowy",
      description:
        "Domyślnie będziesz jedynym administratorem. Jako administrator będziesz musiał utworzyć konta dla wszystkich nowych użytkowników lub administratorów. Nie zgub swojego hasła, ponieważ tylko administrator może resetować hasła.",
      username: "E-mail konta administratora",
      password: "Hasło konta administratora",
      "username-placeholder": "Twój e-mail administratora",
      "password-placeholder": "Twoje hasło administratora",
    },
    password: {
      title: "Ochrona hasłem",
      description:
        "Chroń swoją instancję hasłem. Jeśli je zapomnisz, nie ma możliwości odzyskania, więc upewnij się, że je zapiszesz.",
    },
    instance: {
      title: "Chroń instancję hasłem",
      description:
        "Domyślnie będziesz jedynym administratorem. Jako administrator będziesz musiał tworzyć konta dla wszystkich nowych użytkowników lub administratorów. Nie zgub swojego hasła, ponieważ tylko administrator może resetować hasła.",
      password: "Hasło instancji",
    },
  },
  // =========================
  // EXPERIMENTAL FEATURES
  // =========================
  experimental: {
    title: "Funkcje Eksperymentalne",
    description: "Funkcje obecnie w fazie testów beta",
    "live-sync": {
      title: "Synchronizacja Dokumentów na Żywo",
      description:
        "Włącz automatyczną synchronizację treści ze źródeł zewnętrznych",
      "manage-title": "Monitorowane dokumenty",
      "manage-description":
        "To są wszystkie dokumenty obecnie monitorowane w Twojej instancji.",
      "document-name": "Nazwa dokumentu",
      "last-synced": "Ostatnia synchronizacja",
      "next-refresh": "Czas do następnej aktualizacji",
      "created-on": "Utworzono",
      "auto-sync": "Automatyczna Synchronizacja Treści",
      "sync-description":
        "Włącz możliwość określenia źródła treści do monitorowania.",
      "sync-workspace-note":
        "Monitorowana treść będzie automatycznie aktualizowana.",
      "sync-limitation": "Ta funkcja dotyczy tylko treści internetowych.",
      documentation: "Dokumentacja funkcji i ostrzeżenia",
      "manage-content": "Zarządzaj monitorowaną treścią",
    },
    tos: {
      title: "Warunki użytkowania",
      description: "Funkcje w fazie testów.",
      "possibilities-title": "Możliwe skutki:",
      possibilities: {
        "data-loss": "Utrata danych",
        "quality-change": "Zmiana jakości",
        "storage-increase": "Więcej pamięci",
        "resource-consumption": "Więcej zasobów",
        "cost-increase": "Wyższe koszty",
        "potential-bugs": "Możliwe błędy",
      },
      "conditions-title": "Warunki:",
      conditions: {
        "future-updates": "Może zniknąć",
        stability: "Niestabilne",
        availability: "Ograniczona dostępność",
        privacy: "Prywatność zachowana",
        changes: "Zmienne warunki",
      },
      "read-more": "Więcej",
      contact: "Kontakt",
      reject: "Odrzuć",
      accept: "OK",
    },
    "update-failed": "Nie udało się zaktualizować statusu funkcji",
  },
};
