const { v4: uuidv4 } = require("uuid");
const { reqBody, userFromSession, multiUserMode } = require("../utils/http");
const { validatedRequest } = require("../utils/middleware/validatedRequest");
const { WorkspaceChats } = require("../models/workspaceChats");
const { SystemSettings } = require("../models/systemSettings");
const { Telemetry } = require("../models/telemetry");
const { streamChatWithWorkspace } = require("../utils/chats/stream");
const {
  ROLES,
  flexUserRoleValid,
} = require("../utils/middleware/multiUserProtected");
const { EventLogs } = require("../models/eventLogs");
const {
  validWorkspaceAndThreadSlug,
  validWorkspaceSlug,
} = require("../utils/middleware/validWorkspace");
const { writeResponseChunk } = require("../utils/helpers/chat/responses");
const { readChatLog } = require("../utils/helpers/chat/logs");
const { WorkspaceThread } = require("../models/workspaceThread");
const truncate = require("truncate");
const express = require("express");
const router = express.Router();
const officegen = require("officegen");
const fs = require("fs");
const path = require("path");
const os = require("os");
const i18n = require("../utils/i18n");
const { TokenManager } = require("../utils/helpers/tiktoken");
const {
  extractFirstSentence,
} = require("../utils/helpers/thread/textProcessing");

function chatEndpoints(app) {
  if (!app) return;

  app.post(
    "/workspace/:slug/stream-chat/:slugModule",
    [validatedRequest, flexUserRoleValid([ROLES.all]), validWorkspaceSlug],
    async (request, response) => {
      try {
        // Parse and validate message early, then start stream headers
        const {
          message,
          attachments = [],
          chatId = null,
          isCanvasChat = false,
          preventChatCreation = false,
          llmSelected = 0,
          invoice_ref = null,
          hasUploadedFile = false,
          docxContent = null,
          displayMessage = null,
          useDeepSearch = false,
          cdbOptions = [],
          legalTaskConfig = {},
          settings_suffix: settingsSuffix = "",
          styleAlignment = null,
        } = reqBody(request);
        if (!message?.length) {
          response.status(400).json({
            uuid: uuidv4(),
            type: "abort",
            textResponse: null,
            sources: [],
            close: true,
            error: !message?.length ? "Message is empty." : null,
          });
          return;
        }
        // Early SSE headers to reduce perceived latency
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Content-Type", "text/event-stream");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Connection", "keep-alive");
        response.flushHeaders();
        // Now authenticate and proceed
        const user = await userFromSession(request, response);
        const { slug, slugModule = null } = request.params;
        const workspace = response.locals.workspace;

        if (multiUserMode(response) && user.role !== ROLES.admin) {
          const limitMessagesSetting = await SystemSettings.get({
            label: "limit_user_messages",
          });
          const limitMessages = limitMessagesSetting?.value === "true";

          if (limitMessages) {
            const messageLimitSetting = await SystemSettings.get({
              label: "message_limit",
            });
            const systemLimit = Number(messageLimitSetting?.value);

            if (systemLimit) {
              const currentChatCount = await WorkspaceChats.count({
                user_id: user.id,
                createdAt: {
                  gte: new Date(new Date() - 24 * 60 * 60 * 1000),
                },
              });

              if (currentChatCount >= systemLimit) {
                writeResponseChunk(response, {
                  uuid: uuidv4(),
                  type: "abort",
                  textResponse: null,
                  sources: [],
                  close: true,
                  error: `You have met your maximum 24 hour chat quota of ${systemLimit} chats set by the instance administrators. Try again later.`,
                });
                return;
              }
            }
          }
        }
        let LLMSelection;
        let Embedder;
        let VectorDbSelection;
        let settings_suffix = settingsSuffix || "";
        if (slugModule === "document-drafting") {
          if (llmSelected === "1" && process.env.BINARY_LLM_DD === "on") {
            LLMSelection = process.env.LLM_PROVIDER_DD_2;
            settings_suffix = "_DD_2";
          } else {
            LLMSelection = process.env.LLM_PROVIDER_DD;
            settings_suffix = "_DD";
          }
          Embedder = process.env.EMBEDDING_ENGINE_DD;
          VectorDbSelection = process.env.VECTOR_DB_DD;
        } else {
          LLMSelection = process.env.LLM_PROVIDER;
          Embedder = process.env.EMBEDDING_ENGINE;
          VectorDbSelection = process.env.VECTOR_DB;
        }

        await streamChatWithWorkspace(
          request,
          response,
          workspace,
          message,
          workspace?.chatMode,
          user,
          null,
          attachments,
          chatId,
          isCanvasChat,
          preventChatCreation,
          settings_suffix,
          invoice_ref,
          workspace?.vectorSearchMode,
          hasUploadedFile,
          docxContent,
          displayMessage,
          useDeepSearch,
          cdbOptions,
          legalTaskConfig,
          styleAlignment
        );

        await Telemetry.sendTelemetry("sent_chat", {
          multiUserMode: multiUserMode(response),
          LLMSelection: LLMSelection || "openai",
          Embedder: Embedder || "inherit",
          VectorDbSelection: VectorDbSelection || "lancedb",
          multiModal: Array.isArray(attachments) && attachments?.length !== 0,
          TTSSelection: process.env.TTS_PROVIDER || "native",
        });

        await EventLogs.logEvent(
          "sent_chat",
          {
            workspaceName: workspace?.name,
            chatModel: workspace?.chatModel || "System Default",
          },
          user?.id
        );
        response.end();
      } catch (e) {
        console.error(e);
        writeResponseChunk(response, {
          uuid: uuidv4(),
          type: "abort",
          textResponse: null,
          sources: [],
          close: true,
          error: e.message,
        });
        response.end();
      }
    }
  );

  app.post(
    "/workspace/:slug/thread/:threadSlug/stream-chat/:slugModule",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (request, response) => {
      try {
        // Early parse and validation
        const {
          message,
          attachments = [],
          chatId = null,
          isCanvasChat = false,
          preventChatCreation = false,
          llmSelected = 0,
          invoice_ref = null,
          hasUploadedFile = false,
          docxContent = null,
          displayMessage = null,
          useDeepSearch = false,
          cdbOptions = [],
          legalTaskConfig = {},
          settings_suffix: settingsSuffix = "",
          styleAlignment = null,
        } = reqBody(request);
        if (!message?.length) {
          response.status(400).json({
            uuid: uuidv4(),
            type: "abort",
            textResponse: null,
            sources: [],
            close: true,
            error: !message?.length ? "Message is empty." : null,
          });
          return;
        }
        // Send SSE headers immediately
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Content-Type", "text/event-stream");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Connection", "keep-alive");
        response.flushHeaders();

        // Now authenticate and load context
        const user = await userFromSession(request, response);
        const { slug, slugModule = null } = request.params;
        const workspace = response.locals.workspace;
        let thread = response.locals.thread;

        if (multiUserMode(response) && user.role !== ROLES.admin) {
          const limitMessagesSetting = await SystemSettings.get({
            label: "limit_user_messages",
          });
          const limitMessages = limitMessagesSetting?.value === "true";

          if (limitMessages) {
            const messageLimitSetting = await SystemSettings.get({
              label: "message_limit",
            });
            const systemLimit = Number(messageLimitSetting?.value);

            if (systemLimit) {
              // Chat qty includes all threads because any user can freely
              // create threads and would bypass this rule.
              const currentChatCount = await WorkspaceChats.count({
                user_id: user.id,
                createdAt: {
                  gte: new Date(new Date() - 24 * 60 * 60 * 1000),
                },
              });

              if (currentChatCount >= systemLimit) {
                writeResponseChunk(response, {
                  uuid: uuidv4(),
                  type: "abort",
                  textResponse: null,
                  sources: [],
                  close: true,
                  error: `You have met your maximum 24 hour chat quota of ${systemLimit} chats set by the instance administrators. Try again later.`,
                });
                return;
              }
            }
          }
        }

        let LLMSelection;
        let Embedder;
        let VectorDbSelection;
        let settings_suffix = settingsSuffix || "";
        if (slugModule === "document-drafting") {
          if (llmSelected === "1" && process.env.BINARY_LLM_DD === "on") {
            LLMSelection = process.env.LLM_PROVIDER_DD_2;
            settings_suffix = "_DD_2";
          } else {
            LLMSelection = process.env.LLM_PROVIDER_DD;
            settings_suffix = "_DD";
          }
          Embedder = process.env.EMBEDDING_ENGINE_DD;
          VectorDbSelection = process.env.VECTOR_DB_DD;
        } else {
          LLMSelection = process.env.LLM_PROVIDER;
          Embedder = process.env.EMBEDDING_ENGINE;
          VectorDbSelection = process.env.VECTOR_DB;
        }

        // The thread slug endpoint needs to first check if thread needs renaming
        // before streaming content
        if (thread && thread.name === WorkspaceThread.defaultName) {
          try {
            // Rename thread on first message
            const { thread: updatedThread } = await WorkspaceThread.update(
              thread,
              {
                name: extractFirstSentence(message),
              }
            );

            if (updatedThread) {
              // Update the local thread variable with the new name
              thread = updatedThread;
              // After updating locally, we'll send a thread rename notification
              // during the stream to ensure the client knows about it
            }
          } catch (error) {
            console.error("Error in pre-stream thread renaming:", error);
          }
        }

        await streamChatWithWorkspace(
          request,
          response,
          workspace,
          message,
          workspace?.chatMode,
          user,
          thread,
          attachments,
          chatId,
          isCanvasChat,
          preventChatCreation,
          settings_suffix,
          invoice_ref,
          workspace?.vectorSearchMode,
          hasUploadedFile,
          docxContent,
          displayMessage,
          useDeepSearch,
          cdbOptions,
          legalTaskConfig,
          styleAlignment
        );

        // Send the rename_thread event after stream is sent but before ending
        if (thread && thread.name !== WorkspaceThread.defaultName) {
          try {
            writeResponseChunk(response, {
              action: "rename_thread",
              thread: {
                slug: thread.slug,
                name: thread.name,
              },
            });
          } catch (error) {
            console.error("Error sending thread rename event:", error);
          }
        }

        await Telemetry.sendTelemetry("sent_chat", {
          multiUserMode: multiUserMode(response),
          LLMSelection: LLMSelection || "openai",
          Embedder: Embedder || "inherit",
          VectorDbSelection: VectorDbSelection || "lancedb",
          multiModal: Array.isArray(attachments) && attachments?.length !== 0,
          TTSSelection: process.env.TTS_PROVIDER || "native",
        });

        await EventLogs.logEvent(
          "sent_chat",
          {
            workspaceName: workspace.name,
            thread: thread.name,
            chatModel: workspace?.chatModel || "System Default",
          },
          user?.id
        );
        response.end();
      } catch (e) {
        console.error(e);
        writeResponseChunk(response, {
          uuid: uuidv4(),
          type: "abort",
          textResponse: null,
          sources: [],
          close: true,
          error: e.message,
        });
        response.end();
      }
    }
  );

  app.get(
    "/workspace/chat-log/:chatId",
    [validatedRequest, flexUserRoleValid([ROLES.all])],
    async (request, response) => {
      try {
        const { chatId } = request.params;
        const chat = await WorkspaceChats.get({ id: Number(chatId) });
        if (!chat) {
          response.status(404).json({
            message: "Chat not found",
          });
          return;
        }
        const chatLog = await readChatLog(Number(chatId));
        response.json({ success: true, chatLog });
      } catch (error) {
        console.error("Error retrieving chat log:", error);
        response.status(error.message.includes("not found") ? 404 : 500).json({
          success: false,
          message: error.message,
        });
      }
    }
  );

  app.post(
    "/chat/export-docx",
    [validatedRequest, flexUserRoleValid([ROLES.all])],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);
        const { content } = reqBody(request);

        if (!content) {
          return response.status(400).json({
            error: await i18n.t("docx.errors.contentRequired"),
          });
        }

        // Get translations first
        const [
          titleText,
          exportedOnText,
          keywordsText,
          descriptionText,
          errorWritingText,
          errorGeneratingText,
          tokenCountText,
        ] = await Promise.all([
          i18n.t("docx.title"),
          i18n.t("docx.exportedOn"),
          i18n.t("docx.keywords"),
          i18n.t("docx.description"),
          i18n.t("docx.errors.writingFile"),
          i18n.t("docx.errors.generating"),
          i18n.t("docx.tokenCount"),
        ]);

        // Create Word document with better formatting
        const docx = officegen("docx");

        // Set document properties
        docx.setDocSubject(titleText);
        docx.setDocKeywords(keywordsText);
        docx.setDescription(descriptionText);

        // Add a title
        const title = docx.createP();
        title.addText(titleText, {
          bold: true,
          font_size: 14,
          font_face: "Calibri",
        });
        title.addLineBreak();
        title.addLineBreak();

        // Add timestamp
        const timestamp = docx.createP();
        timestamp.addText(
          `${exportedOnText}: ${new Date().toLocaleString("en-US", {
            year: "numeric",
            month: "numeric",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
            hour12: false,
          })}`,
          {
            font_size: 10,
            font_face: "Calibri",
          }
        );
        timestamp.addLineBreak();
        timestamp.addLineBreak();

        // Add token count
        const tokenManager = new TokenManager();
        const tokenCount = tokenManager.countFromString(content);
        const tokenInfo = docx.createP();
        tokenInfo.addText(`${tokenCountText}: ${tokenCount}`, {
          font_size: 10,
          font_face: "Calibri",
        });
        tokenInfo.addLineBreak();
        tokenInfo.addLineBreak();

        // Add content with proper formatting
        convertMarkdownToDocx(docx, content);

        // Create temporary file
        const tempFilePath = path.join(os.tmpdir(), `${uuidv4()}.docx`);
        const output = fs.createWriteStream(tempFilePath);

        // Handle potential errors during generation
        output.on("error", (err) => {
          console.error("Error writing temporary file:", err);
          return response.status(500).json({ error: errorWritingText });
        });

        docx.on("error", (err) => {
          console.error("Error generating document:", err);
          return response.status(500).json({ error: errorGeneratingText });
        });

        // Generate and send the document
        docx.generate(output);

        output.on("close", () => {
          response.download(
            tempFilePath,
            `chat-export-${new Date().toLocaleDateString().replace(/\//g, "-")}.docx`,
            (err) => {
              // Clean up temporary file
              fs.unlink(tempFilePath, (unlinkErr) => {
                if (unlinkErr) {
                  console.error("Error deleting temporary file:", unlinkErr);
                }
              });

              if (err) {
                console.error("Error sending file:", err);
                return response.status(500).json({ error: errorWritingText });
              }
            }
          );
        });
      } catch (error) {
        console.error("Error in export-docx endpoint:", error);
        response.status(500).json({
          error: error,
          details: error.message,
        });
      }
    }
  );
}

// Convert markdown to docx formatting
function convertMarkdownToDocx(docx, markdown) {
  const lines = markdown.split("\n");
  let inCodeBlock = false;
  let inList = false;
  let listLevel = 0;

  for (const line of lines) {
    // Skip empty lines
    if (!line.trim()) {
      const p = docx.createP();
      p.addLineBreak();
      continue;
    }

    // Handle code blocks
    if (line.startsWith("```")) {
      inCodeBlock = !inCodeBlock;
      if (!inCodeBlock) {
        const p = docx.createP();
        p.addLineBreak();
      }
      continue;
    }

    if (inCodeBlock) {
      const p = docx.createP();
      p.addText(line, {
        font_face: "Courier New",
        font_size: 10,
        color: "666666",
      });
      continue;
    }

    // Handle headers
    if (line.startsWith("#")) {
      const level = line.match(/^#+/)[0].length;
      const text = line.replace(/^#+\s+/, "");
      const p = docx.createP();
      p.addText(text, {
        bold: true,
        font_size: 20 - level * 2,
        font_face: "Calibri",
      });
      p.addLineBreak();
      continue;
    }

    // Handle lists
    if (line.match(/^(\s*[-*+]|\s*\d+\.)\s/)) {
      const text = line.replace(/^(\s*[-*+]|\s*\d+\.)\s+/, "");
      const p = docx.createP();

      // Process inline formatting in list items without forcing bold
      processInlineFormatting(p, text, false);
      continue;
    }

    // Handle regular paragraphs with inline formatting
    const p = docx.createP();
    processInlineFormatting(p, line, false);
  }
}

// Process inline markdown formatting
function processInlineFormatting(p, text, isList = false) {
  let currentText = "";
  let inBold = false;
  let inItalic = false;
  let inCode = false;

  // Helper function to escape special characters
  const escapeSpecialChars = (str) => {
    return str.replace(/[<>]/g, (char) => {
      return char === "<" ? "&lt;" : "&gt;";
    });
  };

  for (let i = 0; i < text.length; i++) {
    if (text[i] === "`" && !inBold && !inItalic) {
      if (currentText) {
        p.addText(escapeSpecialChars(currentText), {
          font_face: inCode ? "Courier New" : "Calibri",
          font_size: 11,
          bold: inBold,
          italic: inItalic,
          color: inCode ? "666666" : "000000",
        });
        currentText = "";
      }
      inCode = !inCode;
      continue;
    }

    if (text[i] === "*" || text[i] === "_") {
      const isDouble = text[i + 1] === text[i];

      if (currentText) {
        p.addText(escapeSpecialChars(currentText), {
          font_face: inCode ? "Courier New" : "Calibri",
          font_size: 11,
          bold: inBold,
          italic: inItalic,
          color: inCode ? "666666" : "000000",
        });
        currentText = "";
      }

      if (isDouble) {
        inBold = !inBold;
        i++; // Skip next character
      } else {
        inItalic = !inItalic;
      }
      continue;
    }

    currentText += text[i];
  }

  if (currentText) {
    p.addText(escapeSpecialChars(currentText), {
      font_face: inCode ? "Courier New" : "Calibri",
      font_size: 11,
      bold: inBold,
      italic: inItalic,
      color: inCode ? "666666" : "000000",
    });
  }
}

module.exports = { chatEndpoints, router };
