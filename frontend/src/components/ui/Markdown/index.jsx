import React, { useMemo } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/esm/styles/prism";
import { v4 as uuidv4 } from "uuid";
import "./markdown.css";

/**
 * Process markdown content to ensure tables are properly formatted
 *
 * @param {string} text - Markdown content to process
 * @returns {string} Processed markdown content
 */
function processTableMarkdown(text) {
  if (!text || typeof text !== "string") return text || "";

  // If the text doesn't contain potential table elements, return as is
  if (!text.includes("|")) return text;

  const lines = text.split("\n");
  let inTable = false;
  let tableStart = -1;
  let tableEnd = -1;

  // Find all complete tables in the message
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Detect table headers and separators
    if (
      !inTable &&
      line.startsWith("|") &&
      i < lines.length - 1 &&
      (lines[i + 1].includes("| --- |") ||
        lines[i + 1].includes("|---|") ||
        lines[i + 1].includes("| -- |"))
    ) {
      inTable = true;
      tableStart = i;
    }
    // Detect end of table
    else if (inTable && (!line.startsWith("|") || i === lines.length - 1)) {
      inTable = false;
      tableEnd = i;

      // The table is complete, so don't modify it
      if (tableStart !== -1 && tableEnd !== -1) {
        // Continue from after this table
        i = tableEnd;
        tableStart = -1;
        tableEnd = -1;
      }
    }
  }

  // If we're still in a table at the end, it might be incomplete
  if (inTable && tableStart !== -1) {
    // Count columns in the header row
    const headerRow = lines[tableStart].trim();
    const columnCount = (headerRow.match(/\|/g) || []).length - 1;

    // Check if we have the separator row
    if (
      tableStart + 1 < lines.length &&
      (lines[tableStart + 1].includes("| --- |") ||
        lines[tableStart + 1].includes("|---|") ||
        lines[tableStart + 1].includes("| -- |"))
    ) {
      // We have a header and separator but no data rows
      if (
        tableStart + 2 >= lines.length ||
        !lines[tableStart + 2].trim().startsWith("|")
      ) {
        // Add an empty data row to complete the table
        lines.push("| " + " | ".repeat(columnCount) + " |");
      }
    } else {
      // We only have a header row, add a separator and data row
      const separatorParts = [];
      for (let i = 0; i < columnCount; i++) {
        separatorParts.push("---");
      }
      const separatorRow = "| " + separatorParts.join(" | ") + " |";
      lines.push(separatorRow);
      lines.push("| " + " | ".repeat(columnCount) + " |");
    }
  }

  return lines.join("\n");
}

/**
 * Centralized Markdown component for consistent rendering across the application
 *
 * @param {Object} props - Component props
 * @param {string} props.content - Markdown content to render
 * @param {boolean} props.className - Additional CSS classes
 * @returns {JSX.Element} Rendered markdown content
 */
const Markdown = ({ content, className = "" }) => {
  // Process content to ensure tables are properly formatted
  const processedContent = useMemo(() => {
    // Ensure content is a string before processing
    const stringContent =
      typeof content === "string" ? content : String(content || "");
    return processTableMarkdown(stringContent);
  }, [content]);
  return (
    <div className={`prose prose-headings:font-bold max-w-none ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={{
          code: ({ inline, className, children, ...props }) => {
            const match = /language-(\w+)/.exec(className || "");
            const codeId = uuidv4();

            return !inline && match ? (
              <div className="relative">
                <div className="flex items-center justify-between px-4 py-2 text-xs rounded-t-md bg-gray-800 text-gray-200">
                  <span>{match[1]}</span>
                  <button
                    data-code-snippet
                    data-code={`code-${codeId}`}
                    className="flex items-center gap-x-2"
                    onClick={() => {
                      navigator.clipboard.writeText(
                        String(children).replace(/\n$/, "")
                      );
                    }}
                  >
                    <svg
                      stroke="currentColor"
                      fill="none"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                      height="1em"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                      <rect
                        x="8"
                        y="2"
                        width="8"
                        height="4"
                        rx="1"
                        ry="1"
                      ></rect>
                    </svg>
                    <p>Copy code</p>
                  </button>
                </div>
                <SyntaxHighlighter
                  style={oneDark}
                  language={match[1]}
                  PreTag="div"
                  className="rounded-b-md !mt-0"
                  {...props}
                >
                  {String(children).replace(/\n$/, "")}
                </SyntaxHighlighter>
              </div>
            ) : (
              <code className={className} {...props}>
                {children}
              </code>
            );
          },
          img: ({ ...props }) => {
            return (
              <div className="w-full max-w-[800px]">
                <img className="w-full h-auto" {...props} />
              </div>
            );
          },
          ul: ({ ...props }) => {
            return <ul className="markdown-list" {...props} />;
          },
          li: ({ ...props }) => {
            return <li className="markdown-list-item" {...props} />;
          },
          p: ({ children, ...props }) => {
            return (
              <p className="whitespace-pre-line" {...props}>
                {children}
              </p>
            );
          },
        }}
      >
        {processedContent || ""}
      </ReactMarkdown>
    </div>
  );
};

export default Markdown;
