---
description: Guidelines for frontend event handling in React components
globs: "{frontend/src/components/**/*.{js,jsx},frontend/src/stores/**/*.{js,jsx}}"
alwaysApply: true
---

# Frontend Event Handling Guidelines

## Overview

This document outlines best practices for handling events and state changes in React components, particularly focusing on the use of Zustand for state management.

## Core Principles

1. **React's Reactivity Model**: React is built around the concept of reactivity. Components declare what state they depend on, and <PERSON>act automatically re-renders them when that state changes.

2. **Avoid Custom Event Systems**: Avoid implementing custom event systems that run outside React's update cycle. Instead, leverage React's built-in reactivity model.

3. **Zustand Integration**: Zustand hooks integrate directly with React's reactivity model. When state changes in a Zustand store, components that select that state will automatically re-render.

## Guidelines

### 1. Direct State Observation

Use Zustand selectors to directly observe state changes:

```javascript
// Recommended
const attachments = useAttachmentStore(state => state.attachments);

// Component will automatically re-render when attachments change
return <div>{attachments.length} files attached</div>;
```

### 2. Side Effects on State Changes

Use `useEffect` to perform side effects when state changes:

```javascript
const attachments = useAttachmentStore(state => state.attachments);
const prevAttachmentsRef = useRef(attachments);

useEffect(() => {
  // Compare with previous state to determine what changed
  if (prevAttachmentsRef.current.length > attachments.length) {
    console.log("An attachment was removed");
    // Perform side effects like cleanup or notifications
  } else if (prevAttachmentsRef.current.length < attachments.length) {
    console.log("An attachment was added");
    // Perform side effects like analytics or notifications
  }
  
  // Update ref for next comparison
  prevAttachmentsRef.current = attachments;
}, [attachments]);
```

### 3. Non-Component Code

For code outside React components:

```javascript
// Read state
const currentAttachments = useAttachmentStore.getState().attachments;

// Update state
useAttachmentStore.getState().clearAttachments();

// Subscribe to changes (only when necessary)
const unsubscribe = useAttachmentStore.subscribe(
  (state, prevState) => {
    // This runs whenever the store updates
    console.log('Store updated:', state, prevState);
  }
);

// Don't forget to unsubscribe when done
unsubscribe();
```

### 4. Avoid Custom Event Systems

Avoid implementing custom event systems that duplicate Zustand's functionality:

```javascript
// Not recommended
store.subscribe((eventType, data) => {
  if (eventType === "ATTACHMENT_REMOVE") {
    // Handle removal
  }
});

// Recommended
useEffect(() => {
  // React to state changes directly
}, [stateToWatch]);
```

### 5. Memoization for Performance

Use memoization to prevent unnecessary re-renders:

```javascript
// Memoize derived values
const fileCount = useMemo(() => {
  return attachments.length;
}, [attachments]);

// Memoize callbacks
const handleRemove = useCallback((id) => {
  removeAttachment({ id });
}, [removeAttachment]);
```

## Migration from Custom Event Systems

When migrating from custom event systems to React's reactivity model:

1. Identify components that subscribe to events
2. Replace event subscriptions with direct state observations
3. Use `useEffect` to perform side effects when state changes
4. Use `useRef` to compare previous and current state values

Example migration:

```javascript
// Before
useEffect(() => {
  const unsubscribe = store.subscribe((eventType, data) => {
    if (eventType === "ATTACHMENT_REMOVE") {
      handleRemove(data);
    }
  });
  return unsubscribe;
}, []);

// After
const attachments = useStore(state => state.attachments);
const prevAttachmentsRef = useRef(attachments);

useEffect(() => {
  // Compare with previous state
  const removedAttachments = prevAttachmentsRef.current.filter(
    prev => !attachments.some(curr => curr.id === prev.id)
  );
  
  // Handle removed attachments
  removedAttachments.forEach(handleRemove);
  
  // Update ref for next comparison
  prevAttachmentsRef.current = attachments;
}, [attachments, handleRemove]);
```

## Documentation

For more information, see the [Zustand documentation](https://github.com/pmndrs/zustand) and [React documentation on useEffect](https://reactjs.org/docs/hooks-effect.html).
