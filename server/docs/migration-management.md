# Migration Management Guidelines

## Overview

This document outlines best practices for managing Prisma migrations in the ISTLegal project and explains the migration timestamp fix that was implemented.

## Migration Timestamp Issue Resolution

### Background

In early 2025, we encountered a migration timestamp ordering issue where:

- `20250429215629_sharing_db_changes/` (April 29, 2025) - Added sharing functionality
- `20250509100710_initial_schema/` (May 9, 2025) - **Base schema creation** (should have been first)

This created a logical dependency inversion where feature migrations appeared before the base schema migration.

### Solution Implemented

To fix this issue without affecting production environments, we implemented a **bootstrap migration approach**:

1. **Created Bootstrap Migration**: `20250401000000_bootstrap_schema/`

   - Contains all base schema creation with early timestamp (April 1, 2025)
   - Uses `IF NOT EXISTS` clauses for backwards compatibility
   - Ensures new deployments have correct logical order

2. **Kept Original Migration Names**: `20250509100710_/` (unchanged)

   - **IMPORTANT**: We did NOT rename this migration to avoid breaking existing environments
   - Prisma tracks migrations by exact name - renaming would cause deployment issues
   - The migration remains as-is for backwards compatibility

3. **Updated Conflicting Migrations**: Made dependent migrations safe
   - `20250509155110_add_requires_main_document_to_category/` converted to no-op
   - Added documentation explaining the bootstrap approach

### Benefits

- **Production Safe**: Existing environments unaffected
- **Logical Order**: New deployments follow correct dependency order
- **Future Proof**: Prevents similar issues going forward
- **Clear History**: Migration names are descriptive and self-documenting

## Best Practices for Future Migrations

### 1. Timestamp Management

- **Always use correct timestamps**: Ensure new migrations have timestamps later than existing ones
- **Check dependencies**: Verify that your migration doesn't depend on tables/columns from future migrations
- **Use descriptive names**: Always include a clear description in migration names
- **⚠️ NEVER rename applied migrations**: Once a migration has been applied in any environment, never rename its directory or file. Prisma tracks migrations by exact name.

### 2. Migration Safety

- **Use IF NOT EXISTS**: When creating tables or indexes that might already exist
- **Test on fresh database**: Always test migrations on a clean database
- **Test on existing database**: Verify migrations work on databases with existing data

### 3. Conflict Resolution

If you encounter migration conflicts:

1. **Never modify applied migrations** in production environments
2. **Use resolution commands**:

   ```bash
   # Mark migration as applied (if it was successfully applied manually)
   npx prisma migrate resolve --applied <migration_name>

   # Mark migration as rolled back (if it failed and needs to be re-applied)
   npx prisma migrate resolve --rolled-back <migration_name>
   ```

3. **Create corrective migrations** instead of modifying existing ones

### 4. Testing Migrations

```bash
# Check migration status
npx prisma migrate status

# Apply migrations in development
npx prisma migrate dev

# Apply migrations in production
npx prisma migrate deploy

# Reset database for testing (DESTRUCTIVE)
npx prisma db push --force-reset --accept-data-loss
```

## Migration Directory Structure

```
prisma/migrations/
├── 20250401000000_bootstrap_schema/          # Bootstrap (base schema)
├── 20250429215629_sharing_db_changes/        # Feature: Sharing
├── 20250509100710_/                          # Historical base schema (kept for compatibility)
├── 20250509155110_add_requires_main_document_to_category/  # Feature (no-op)
├── 20250510093555_feedback_option/           # Feature: Feedback
└── 20250528002620_add_user_style_profiles/   # Feature: User styles
```

## Troubleshooting

### Common Issues

1. **Duplicate column errors**: Usually indicates a migration is trying to add a column that already exists

   - Solution: Make the migration conditional or convert to no-op

2. **Foreign key constraint failures**: Usually indicates table creation order issues

   - Solution: Ensure referenced tables are created first

3. **Migration marked as failed**: Migration started but didn't complete
   - Solution: Use `npx prisma migrate resolve` to mark as rolled-back, then re-apply

### Emergency Procedures

If migrations cause production issues:

1. **Immediate**: Stop application deployment
2. **Assess**: Check migration status and error messages
3. **Resolve**: Use appropriate resolve commands
4. **Test**: Verify database integrity
5. **Deploy**: Resume application deployment

## References

- [Prisma Migration Documentation](https://www.prisma.io/docs/concepts/components/prisma-migrate)
- [Migration Troubleshooting](https://www.prisma.io/docs/concepts/components/prisma-migrate/migrate-development-production#troubleshooting)
- [Bootstrap Migration Implementation](../AgentTemp/prisma-migration-fix-strategy.md)
