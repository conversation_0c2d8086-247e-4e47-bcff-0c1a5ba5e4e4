---
description:
globs:
alwaysApply: false
---
# Vector Context Implementation Details

This document provides detailed implementation specifics for vector fetching and context window management. For a high-level overview, see [vector-search-overview.mdc](mdc:.cursor/rules/vector-search-overview.mdc). For details on backfilling, see [context-backfilling.mdc](mdc:.cursor/rules/context-backfilling.mdc).

## Vector Fetching Process

### 1. Initial Checks

Before fetching vectors, the system performs several validation checks:

- Verifies if the workspace has vectorized space (`hasVectorizedSpace`)
- Checks the total embeddings count (`namespaceCount`)
- Validates if the chat mode is compatible with the available data

### 2. Pinned Documents

The system first handles pinned documents through `DocumentManager`:

```javascript
await new DocumentManager({
    workspace,
    maxTokens: LLMConnector.promptWindowLimit(), // Adjusted by dynamic settings
}).pinnedDocs()
```

These documents are given priority in the context window and are tracked using:

- `pinnedDocIdentifiers`: To prevent duplicates
- `contextTexts`: For the actual content
- `sources`: For metadata and truncated content

### 3. Vector Search

If embeddings exist, the system performs a similarity search using `VectorDb.performSimilaritySearch` with the following parameters:

- `namespace`: Workspace slug
- `input`: User message
- `similarityThreshold`: Workspace-defined threshold
- `topN`: Number of results to return
- `filterIdentifiers`: Pinned document IDs to prevent duplicates
- `rerank`: Optional reranking based on workspace settings

## Context Window Management

### 1. Window Size Calculation

The context window is managed based on the LLM model's limits and dynamic settings:

- System prompt: Maximum 15% of token capacity
- History: Maximum 15% of token capacity
- User prompt: Maximum 70% of token capacity
- Response buffer: 600 tokens reserved

### 2. Message Array Compression

The system uses a "cannonball" approach for compression through `messageArrayCompressor`:

1.  **System Message Handling**:
    *   Splits system prompt and context
    *   Compresses if exceeding limits
    *   Maintains 25% for prompt, 75% for context
2.  **User Prompt Handling**:
    *   Given priority in the window
    *   Can "hijack" entire thread if needed
    *   Limited to 70% of window size
3.  **History Compression**:
    *   Most aggressive compression
    *   Prioritizes recent messages
    *   Limited to 15% of window

## Source Management within Window

The `fillSourceWindow` function manages source context with the following priority:

1.  **Pinned Documents**: Always included first.
2.  **Vector Search Results**: Added after pinned, filtered for duplicates.
3.  **Historical Sources (Backfilling)**: Added if vector results < `nDocs`. See [context-backfilling.mdc](mdc:.cursor/rules/context-backfilling.mdc).

### Source Window Configuration

```javascript
{
    nDocs: 4,              // Default number of documents
    searchResults: [],     // Vector search results
    history: [],           // Raw chat history
    filterIdentifiers: [], // Pinned document identifiers
}
```

## Message Compression Details

The system uses a unique "cannonball" compression technique:

### 1. Cannonball Algorithm

- Deletes content from middle-out bi-directionally
- Preserves start and end context
- Used when content exceeds token limits

### 2. Compression Triggers

- Very large user prompts (>70% of window)
- Exceeded context window in regular use
- System prompts exceeding 15% allocation
- History exceeding 15% allocation

### 3. Compression Priority

1.  History (most aggressive compression)
2.  System prompt (moderate compression)
3.  User prompt (least compression)

## Dynamic Context Window Management

### Global System Settings

The system provides a configurable setting (`SystemSettings.getDynamicContextSettings()`) to control what percentage of the LLM's context window (default 70%) can be used for additional sources (pinned docs, vector results).

This setting allows administrators to:

- Control context enrichment space
- Balance core content vs. sources
- Adjust based on LLM characteristics

### Context Window Allocation

- Pinned documents and vector search results share the allocation within the dynamic context window percentage.
- This is managed via the `maxTokens` parameter passed to `DocumentManager`.

  ```javascript
  constructor({ workspace = null, maxTokens = null }) {
    this.maxTokens = maxTokens || Number.POSITIVE_INFINITY; // maxTokens is calculated based on dynamic setting %
  }
  ```

### Token Management within Dynamic Window

1.  **Token Tracking**: The system adds pinned docs and then vector results, summing `token_count_estimate` until the calculated `maxTokens` (based on the dynamic percentage) is reached.
2.  **Priority Order**: Pinned > Vector Search Results.
3.  **Context Window Distribution**: System vs. User vs. Attachment allocations all work within the total available token space defined by the dynamic percentage.
4.  **Compression Behavior**: If content exceeds the calculated available tokens, compression is applied based on the priorities defined earlier.
