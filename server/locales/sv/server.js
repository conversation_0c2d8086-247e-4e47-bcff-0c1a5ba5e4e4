const translations = {
  common: {
    thinking: "Tänker...",
  },
  errors: {
    auth: {
      "authentication-required": "Autentisering krävs",
      "insufficient-permissions": "Otillräckliga behörigheter",
    },
    ai: {
      ollama: {
        "connection-failed":
          "Din Ollama-instans kunde inte nås eller svarar inte. Se till att API-servern körs och att din anslutningsinformation är korrekt.",
        "empty-response": "Ollamas textsvar var tomt.",
        "stream-failed": "Kunde inte strömma chatten. {{message}}",
        "no-endpoint": "Ingen Ollama API-endpoint har angetts.",
        "no-token-limit": "Ingen Ollama-token-kontextgräns har angetts.",
        "provider-not-responding":
          "Den lokala AI-motorn svarar inte, antingen på grund av att servern är offline eller överbelastad.",
        "provider-unavailable":
          "Den lokala AI-servern är för närvarande inte tillgänglig. Vänligen kontrollera installationen och se till att den är uppstartad och inte är överbelastad.",
      },
      azure: {
        "no-endpoint": "Ingen Azure API-endpoint har angetts.",
        "no-key": "Ingen Azure API-nyckel har angetts.",
        "no-model": "Ingen Azure OpenAI-modell har angetts.",
        "no-model-pref":
          "Ingen OPEN_MODEL_PREF ENV definierad. Detta måste vara namnet på en distribution på ditt Azure-konto för en LLM-chattmodell som GPT-3.5.",
      },
      openai: {
        "no-key": "Ingen OpenAI API-nyckel har angetts.",
      },
      mistral: {
        "no-key": "Ingen Mistral API-nyckel har angetts.",
      },
      litellm: {
        "no-base-path":
          "LiteLLM måste ha en giltig bassökväg för att använda API:et.",
        "no-model": "LiteLLM måste ha en giltig modell inställd.",
        "no-token-limit": "Ingen token-kontextgräns har angetts.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI måste ha en giltig bassökväg för att använda API:et.",
        "no-token-limit": "Ingen token-kontextgräns har angetts.",
      },
      localai: {
        "no-token-limit": "Ingen LocalAi-token-kontextgräns har angetts.",
      },
      lmstudio: {
        "no-token-limit": "Ingen LMStudio-token-kontextgräns har angetts.",
      },
      fireworks: {
        "invalid-model":
          "FireworksAI chatt: {{model}} är inte giltig för chattfullbordan!",
      },
      context: {
        header: "Källa(or)",
      },
    },
    system: {
      "ssl-boot-failed":
        "SSL-start misslyckades: {{message}} - återgår till HTTP-start.",
      "no-app": 'Ingen "app" definierad - kraschar!',
      validation: {
        "invalid-transcription-provider":
          "{{input}} är inte en giltig transkriptionsmodelleverantör.",
        "invalid-safety-setting":
          "Ogiltig säkerhetsinställning. Måste vara en av {{modes}}.",
        "invalid-model-type": "Ogiltig modelltyp. Måste vara en av {{models}}.",
        "invalid-embedding-model":
          "Ogiltig inbäddningsmodelltyp. Måste vara en av {{supported}}.",
      },
    },
    document: {
      "sync-failed":
        "Kunde inte synkronisera dokument {{filename}} efter {{attempts}} försök.",
      "invalid-metadata":
        "Dokument {{filename}} har ogiltig eller saknad metadata.",
      removed:
        "Dokument {{filename}} har tagits bort från uppsättningen övervakade dokument.",
    },
    validation: {
      "value-not-number": "Värdet är inte ett nummer.",
      "value-must-be-nonzero": "Värdet måste vara skilt från noll.",
      "value-cannot-be-negative": "Värdet kan inte vara mindre än 0.",
      "invalid-serp-provider": "Ogiltig SERP-leverantör.",
      "max-rerank-limit-min":
        "Maximal omrangordningsgräns måste vara minst 10.",
      "max-rerank-limit-max":
        "Maximal omrangordningsgräns får inte överstiga 200.",
    },
    embedding: {
      "lmstudio-failed": "LMStudio-inbäddning misslyckades: {{errors}}",
      "jina-failed": "Jina-inbäddning misslyckades: {{errors}}",
    },
    env: {
      "jina-api-key-error": "Jina API-nyckeln måste börja med jina_",
      "empty-value": "Värdet kan inte vara tomt",
      "non-zero-required": "Värdet måste vara större än noll",
      "must-be-number": "Värdet måste vara ett nummer",
      "invalid-url": "URL:en är inte en giltig URL",
      "openai-key-format": "OpenAI-nyckeln måste börja med sk-",
      "contextual-prompt-format":
        "Kontextuell användarfråga måste innehålla {file} & {chunk}",
      "anthropic-key-format": "Anthropic-nyckeln måste börja med sk-ant-",
      "external-llm-url-format": "URL:en måste innehålla /v1",
      "url-no-trailing-slash": "URL:en kan inte sluta med ett snedstreck",
      "invalid-tts-provider": "{{input}} är inte en giltig TTS-leverantör",
      "invalid-whisper-model":
        "{{input}} är inte ett giltigt val av Whisper-modell",
      "invalid-llm-provider": "{{input}} är inte en giltig LLM-leverantör",
    },
    // Workspace sharing errors
    workspace: {
      onlyOwnerShare: "Endast arbetsområdets ägare kan dela arbetsområden",
      onlyOwnerRevoke:
        "Endast arbetsområdets ägare kan återkalla delning av arbetsområden",
      shareFailed: "Det gick inte att dela arbetsområdet",
      revokeFailed: "Det gick inte att återkalla delning av arbetsområdet",
      getStatusFailed:
        "Det gick inte att hämta delningsstatus för arbetsområdet",
    },
    // Thread sharing errors
    thread: {
      onlyOwnerShare: "Endast ägare av tråden kan dela trådar",
      onlyOwnerRevoke: "Endast ägare av tråden kan återkalla delning av trådar",
      shareFailed: "Det gick inte att dela tråden",
      revokeFailed: "Det gick inte att återkalla delning av tråden",
      getStatusFailed: "Det gick inte att hämta delningsstatus för tråden",
    },
  },
  validation: {
    responseHeader: "Genererat Svar",
    contextHeader: "Ursprunglig kontext och källor",
    content: {
      "user-question-sources-header": "ANVÄNDARFRÅGA OCH KÄLLOR",
      "system-response-header": "SYSTEMSVAR",
      "not-provided": "Ej tillhandahållet",
    },
  },
  manualWorkEstimator: {
    content: {
      "user-question-header": "ANVÄNDARFRÅGA",
      "system-response-header": "SYSTEMSVAR",
    },
  },
  notifications: {
    document: {
      invalid:
        "Dokument {{filename}} har ingen metadata, är trasigt eller ogiltigt och har tagits bort.",
      removed:
        "Dokument {{filename}} har tagits bort från uppsättningen övervakade dokument.",
    },
  },
  docx: {
    title: "Chatt-export",
    exportedOn: "Exporterad",
    downloadedOn: "Nedladdad",
    keywords: "chatt, export",
    description: "Exporterat chattmeddelande",
    tokenCount: "Antal tokens",
    canvasDocumentTitle: "Canvas-dokument",
    errors: {
      contentRequired: "Innehåll krävs",
      writingFile: "Fel vid skrivning av temporär fil",
      generating: "Fel vid generering av dokument",
    },
  },
  deep_search: {
    instruction:
      "Om några DeepSearch-källor är relevanta för användarens fråga och används i svaret, ange klickbara länkar till dessa källor i slutet av meddelandet i exakt detta format: 'Källor: [Källtitel 1](URL1), [Källtitel 2](URL2)'. Den klickbara texten för varje länk måste vara en kortfattad beskrivning av källans innehåll (t.ex. sidtitel eller sammanfattning). Referera aldrig till DeepSearch-källor med nummer i texten (som [1] eller [Källa 1]); hänvisa alltid till källan med dess namn eller innehåll direkt. Använd inte akademiska citatformat.",
  },
};

module.exports = translations;
