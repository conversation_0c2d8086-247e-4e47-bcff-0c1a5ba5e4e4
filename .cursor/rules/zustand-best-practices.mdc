---
description: Best practices for using Zustand state management in React applications
globs: "{frontend/src/stores/**/*.{js,jsx},frontend/src/**/*.{js,jsx}}"
alwaysApply: false
---

# Zustand Best Practices

## Understanding Zustand Fundamentals

Zustand is a lightweight state management library for React that provides a simple API for creating global state stores without complex reducers or context providers.

### Basic Store Creation

```javascript
import { create } from 'zustand';

const useStore = create((set) => ({
  // State
  count: 0,
  
  // Actions
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
  reset: () => set({ count: 0 }),
}));
```

## Core Best Practices

### 1. Keep Stores Small and Focused

Create multiple domain-specific stores instead of a single monolithic store. This improves maintainability and performance.

```javascript
// Good: Separate domain-specific stores
const useAuthStore = create((set) => ({
  user: null,
  isAuthenticated: false,
  login: (userData) => set({ user: userData, isAuthenticated: true }),
  logout: () => set({ user: null, isAuthenticated: false }),
}));

const useThemeStore = create((set) => ({
  theme: 'light',
  toggleTheme: () => set((state) => ({ 
    theme: state.theme === 'light' ? 'dark' : 'light' 
  })),
}));

// Bad: Monolithic store with unrelated concerns
const useAppStore = create((set) => ({
  user: null,
  isAuthenticated: false,
  theme: 'light',
  // ...many unrelated state properties and actions
}));
```

### 2. Use Function Updates for State Changes

Always use function updates when the new state depends on the previous state to avoid race conditions.

```javascript
// Good: Function update ensures latest state
const increment = () => set((state) => ({ count: state.count + 1 }));

// Bad: May use stale state
const increment = () => set({ count: useStore.getState().count + 1 });
```

### 3. Optimize with Selectors

Use selectors to subscribe to only the specific state slices your component needs, preventing unnecessary re-renders.

```javascript
// Good: Component only re-renders when count changes
const count = useStore((state) => state.count);

// Bad: Component re-renders on any state change
const store = useStore();
const { count } = store;
```

### 4. Memoize Complex Selectors

For selectors that compute derived state, use memoization to avoid unnecessary recalculations.

```javascript
import { useMemo } from 'react';

// In component:
const items = useStore((state) => state.items);
const totalPrice = useMemo(() => 
  items.reduce((sum, item) => sum + item.price, 0),
  [items]
);

// Or in custom hook:
export const useTotalPrice = () => {
  const items = useStore((state) => state.items);
  return useMemo(() => 
    items.reduce((sum, item) => sum + item.price, 0),
    [items]
  );
};
```

### 5. Use Middleware for Enhanced Functionality

Leverage Zustand middleware for persistence, immutability, and debugging.

```javascript
import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Persist state to localStorage
const useSettingsStore = create(
  persist(
    (set) => ({
      theme: 'light',
      setTheme: (theme) => set({ theme }),
    }),
    { name: 'user-settings' }
  )
);

// DevTools for debugging
const useDebugStore = create(
  devtools(
    (set) => ({
      count: 0,
      increment: () => set((state) => ({ count: state.count + 1 })),
    }),
    { name: 'DebugStore' }
  )
);

// Immer for easier state updates
const useImmerStore = create(
  immer((set) => ({
    user: { name: 'John', settings: { theme: 'light' } },
    updateTheme: (theme) => set((state) => {
      // Can "mutate" state with Immer
      state.user.settings.theme = theme;
    }),
  }))
);
```

## Advanced Patterns

### 1. Handling Side Effects

Keep side effects (API calls, etc.) within actions but separate complex logic.

```javascript
const useUserStore = create((set, get) => ({
  user: null,
  loading: false,
  error: null,
  
  fetchUser: async (id) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(`/api/users/${id}`);
      const data = await response.json();
      set({ user: data, loading: false });
    } catch (error) {
      set({ error: error.message, loading: false });
    }
  },
}));
```

### 2. Combining Multiple Stores

Access other stores from within a store when needed.

```javascript
const useCartStore = create((set, get) => ({
  items: [],
  addItem: (item) => set((state) => ({ 
    items: [...state.items, item] 
  })),
  
  // Access another store
  checkout: async () => {
    const { user } = useUserStore.getState();
    if (!user) return;
    
    // Process checkout with user data
    // ...
  },
}));
```

### 3. TypeScript Integration

Use TypeScript for type safety in your stores.

```typescript
interface UserState {
  user: User | null;
  isAuthenticated: boolean;
  login: (userData: User) => void;
  logout: () => void;
}

interface User {
  id: string;
  name: string;
  email: string;
}

const useUserStore = create<UserState>((set) => ({
  user: null,
  isAuthenticated: false,
  login: (userData) => set({ user: userData, isAuthenticated: true }),
  logout: () => set({ user: null, isAuthenticated: false }),
}));
```

## Common Pitfalls to Avoid

### 1. Returning Objects from Selectors Without Memoization

When a selector returns an object, it creates a new reference on each render, potentially causing infinite re-renders.

```javascript
// Problematic: Returns new object reference on each render
const userData = useUserStore((state) => ({
  name: state.user?.name,
  email: state.user?.email,
}));

// Solution 1: Use equality function
const userData = useUserStore(
  (state) => ({
    name: state.user?.name,
    email: state.user?.email,
  }),
  (prev, next) => 
    prev.name === next.name && prev.email === next.email
);

// Solution 2: Select primitives separately
const userName = useUserStore((state) => state.user?.name);
const userEmail = useUserStore((state) => state.user?.email);
```

### 2. Mutating State Directly

Zustand requires immutable state updates. Direct mutations won't trigger re-renders.

```javascript
// Bad: Direct mutation
const addItem = (item) => {
  const state = get();
  state.items.push(item); // Won't trigger re-renders
};

// Good: Immutable update
const addItem = (item) => set((state) => ({
  items: [...state.items, item]
}));

// Alternative: Use Immer middleware
const addItem = (item) => set((state) => {
  state.items.push(item); // OK with Immer middleware
});
```

### 3. Over-selecting State

Selecting more state than needed causes unnecessary re-renders.

```javascript
// Bad: Component re-renders on any user change
const user = useUserStore((state) => state.user);

// Good: Component only re-renders when relevant property changes
const userName = useUserStore((state) => state.user?.name);
```

### 4. Using getState() in React Components

Avoid using `getState()` in components as it doesn't subscribe to state changes.

```javascript
// Bad: Won't re-render on state changes
const count = useStore.getState().count;

// Good: Subscribes to state changes
const count = useStore((state) => state.count);
```

## Testing Zustand Stores

### Basic Store Testing

```javascript
import { renderHook, act } from '@testing-library/react-hooks';
import useCounterStore from './counterStore';

describe('counterStore', () => {
  // Reset store before each test
  beforeEach(() => {
    act(() => {
      useCounterStore.setState({ count: 0 });
    });
  });

  it('should increment counter', () => {
    // Act
    act(() => {
      useCounterStore.getState().increment();
    });

    // Assert
    expect(useCounterStore.getState().count).toBe(1);
  });
});
```

### Testing Async Actions

```javascript
it('should fetch user data', async () => {
  // Mock fetch
  global.fetch = jest.fn().mockResolvedValue({
    json: jest.fn().mockResolvedValue({ id: '1', name: 'John' }),
  });

  // Act
  act(() => {
    useUserStore.getState().fetchUser('1');
  });

  // Wait for async action
  await waitFor(() => {
    expect(useUserStore.getState().loading).toBe(false);
  });

  // Assert
  expect(useUserStore.getState().user).toEqual({ id: '1', name: 'John' });
});
```

## Summary

- Create small, focused stores organized by domain
- Use function updates for state changes that depend on previous state
- Use selectors to minimize re-renders
- Leverage middleware for persistence, immutability, and debugging
- Keep side effects contained within actions
- Use TypeScript for type safety
- Avoid common pitfalls like returning new object references from selectors
- Test stores thoroughly, including async actions
