/**
 * Utility functions for handling starred documents in vector searches
 */

const { Document } = require("../../models/documents");

/**
 * Applies a similarity score bump to starred documents
 * @param {Array} sourceDocuments - Array of source documents from vector search
 * @param {string} workspaceId - The workspace ID
 * @param {number} similarityBump - The amount to bump similarity score (default: 0.1)
 * @returns {Promise<Array>} - Array of source documents with adjusted scores
 */
async function applySimilarityBumpToStarredDocuments(
  sourceDocuments,
  workspaceId,
  similarityBump = 0.1
) {
  if (!sourceDocuments || sourceDocuments.length === 0 || !workspaceId) {
    return sourceDocuments;
  }

  try {
    // Get all starred documents for this workspace
    const starredDocs = await Document.where({
      workspaceId: Number(workspaceId),
      starred: true,
    });

    if (!starredDocs || starredDocs.length === 0) {
      console.log(
        `[STAR] No starred documents found for workspace ${workspaceId}`
      );
      return sourceDocuments;
    }

    console.log(
      `[STAR] Found ${starredDocs.length} starred documents for workspace ${workspaceId}:`,
      starredDocs.map((doc) => doc.docId).join(", ")
    );

    // Create a set of docIds for faster lookup
    const starredDocIds = new Set(starredDocs.map((doc) => doc.docId));

    console.log(
      `[STAR] Starred docIds: ${Array.from(starredDocIds).join(", ")}`
    );

    // Prepare starred paths and filenames for matching
    const starredDocPaths = starredDocs.map((doc) => doc.docpath);
    const starredFilenames = starredDocs.map((doc) => doc.filename);
    console.log(`[STAR] Starred document paths: ${starredDocPaths.join(", ")}`);
    console.log(`[STAR] Starred filenames: ${starredFilenames.join(", ")}`);

    // Try to match by path or filename
    const matchedByPath = sourceDocuments.filter((doc) => {
      const docPath =
        doc.docpath ||
        doc.metadata?.source ||
        doc.metadata?.docpath ||
        doc.source;
      const filename = doc.filename || doc.metadata?.filename;
      return (
        // Match by full path
        (docPath &&
          starredDocPaths.some((starredPath) =>
            docPath.includes(starredPath)
          )) ||
        // Match by filename
        (filename && starredFilenames.includes(filename))
      );
    });

    if (matchedByPath.length > 0) {
      // Deduplicate matches by docId to list each document only once
      const uniqueMatches = matchedByPath.reduce((acc, doc) => {
        if (!acc.find((d) => d.docId === doc.docId)) acc.push(doc);
        return acc;
      }, []);
      console.log(
        `[STAR] Found ${uniqueMatches.length} unique documents that match starred paths or filenames:`,
        uniqueMatches.map((doc) => {
          const docPath =
            doc.docpath || doc.metadata?.source || doc.metadata?.docpath;
          const filename = doc.filename || doc.metadata?.filename;
          return {
            docId: doc.docId,
            title: doc.title || doc.metadata?.title,
            path: docPath,
            filename,
          };
        })
      );
    } else {
      console.log(
        `[STAR] No documents in search results match starred paths or filenames`
      );
    }

    // Log chunks to be star-boosted
    const chunksToBoost = sourceDocuments.filter(
      (doc) => doc.docId && starredDocIds.has(doc.docId)
    );
    console.log(
      `[STAR] Chunks belonging to a star-marked parent document, for similarity score boosting:`,
      chunksToBoost.map((doc) => {
        const docPath =
          doc.docpath ||
          doc.metadata?.source ||
          doc.metadata?.docpath ||
          doc.source;
        const filename = doc.filename || doc.metadata?.filename;
        return {
          docId: doc.docId,
          title: doc.title || doc.metadata?.title,
          score: doc.score,
          path: docPath,
          filename,
        };
      })
    );

    // Apply similarity bump to starred documents
    const boostedDocs = [];
    const result = sourceDocuments.map((doc) => {
      // First try to match by docId
      if (doc.docId && starredDocIds.has(doc.docId)) {
        // Apply similarity bump, ensuring it doesn't exceed 1.0
        const oldScore = doc.score || 0;
        const newScore = Math.min(1.0, oldScore + similarityBump);
        boostedDocs.push({
          docId: doc.docId,
          oldScore,
          newScore,
          boost: similarityBump,
          matchType: "docId",
        });
        return { ...doc, score: newScore };
      }

      // If docId match fails, try to match by path
      const docPath = doc.docpath || doc.metadata?.source || doc.source;
      if (docPath) {
        const matchingStarredDoc = starredDocs.find((starredDoc) => {
          if (!starredDoc.docpath) return false;

          // Check for full path match
          if (docPath.includes(starredDoc.docpath)) return true;

          // Check for filename match (extract filename from starred document path)
          const starredFilename = starredDoc.docpath.split("/").pop();
          return docPath.includes(starredFilename);
        });

        if (matchingStarredDoc) {
          const oldScore = doc.score || 0;
          const newScore = Math.min(1.0, oldScore + similarityBump);
          boostedDocs.push({
            docId: matchingStarredDoc.docId,
            docPath: docPath,
            starredPath: matchingStarredDoc.docpath,
            oldScore,
            newScore,
            boost: similarityBump,
            matchType: "path",
          });
          // Add the docId to the document so it can be found in future searches
          return { ...doc, score: newScore, docId: matchingStarredDoc.docId };
        }
      }

      return doc;
    });

    if (boostedDocs.length > 0) {
      console.log(
        `[STAR] Applied similarity boost to ${boostedDocs.length} documents:`
      );
      boostedDocs.forEach((doc) => {
        if (doc.matchType === "path") {
          console.log(
            `[STAR] DocID: ${doc.docId}, Score: ${doc.oldScore.toFixed(4)} → ${doc.newScore.toFixed(4)} (boost: +${doc.boost}, matched by path)`
          );
          console.log(
            `[STAR] Path match details: Document path "${doc.docPath}" matched with starred path "${doc.starredPath}"`
          );
        } else {
          console.log(
            `[STAR] DocID: ${doc.docId}, Score: ${doc.oldScore.toFixed(4)} → ${doc.newScore.toFixed(4)} (boost: +${doc.boost}, matched by docId)`
          );
        }
      });
    } else {
      console.log(`[STAR] No documents in the search results were starred`);
    }

    return result;
  } catch (error) {
    console.error(
      "Error applying similarity bump to starred documents:",
      error
    );
    return sourceDocuments;
  }
}

module.exports = {
  applySimilarityBumpToStarredDocuments,
};
