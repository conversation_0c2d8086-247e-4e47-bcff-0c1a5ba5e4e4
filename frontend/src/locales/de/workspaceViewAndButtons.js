export default {
  // =========================
  // WORKSPACE CHATS
  // =========================
  "workspace-chats": {
    welcome: "Willkommen in Ihrem neuen Arbeitsbereich.",
    "desc-start": "Um zu beginnen, können Si<PERSON> entweder",
    "desc-mid": "ein Dokument hochladen",
    "desc-or": "oder",
    start: "Um zu beginnen",
    "desc-end": "eine Nachricht senden.",
    "attached-file": "Angehängte Datei",
    "attached-files": "Angehängte Dateien",
    "token-count": "Token-Anzahl",
    "total-tokens": "Gesamtanzahl Tokens",
    "context-window": "Kontextfenster",
    "remaining-tokens": "Verbleibend",
    "view-files": "Angehängte Dateien anzeigen",
    prompt: {
      send: "Senden",
      "send-message": "Nachricht senden",
      placeholder: "Rechtliche Informationen anfragen",
      "change-size": "Textgröße ändern",
      reset: "Chat zurücksetzen",
      clear: "Chatverlauf löschen und neu beginnen",
      command: "Befehl",
      description: "Beschreibung",
      save: "speichern",
      small: "Klein",
      normal: "Normal",
      large: "Groß",
      larger: "Größer",
      attach: "Datei an diesen Chat anhängen",
      upgrade: "Prompt aktualisieren",
      upgrading: "Prompt wird aktualisiert...",
      "original-prompt": "Original Prompt:",
      "upgraded-prompt": "Aktualisierter Prompt:",
      "edit-prompt": "Du kannst den neuen Prompt vor dem Absenden bearbeiten",
      "shortcut-tip":
        "Tipp: Drücke Enter um Änderungen zu übernehmen. Verwende Shift+Enter für neue Zeilen.",
      "speak-prompt": "Sprich deinen Prompt",
      "view-agents": "Alle verfügbaren Agenten anzeigen",
      "deep-search": "Websuche",
      "deep-search-tooltip":
        "Im Web nach Informationen suchen, um Antworten zu verbessern",
      "ability-tag": "Fähigkeit",
      "workspace-chats.prompt.view-agents": "Agenten anzeigen",
      "workspace-chats.prompt.ability-tag": "Fähigkeit",
      "workspace-chats.prompt.speak-prompt": "Sprich deinen Prompt",
      "total-tokens": "Gesamtanzahl Tokens",
    },
  },
  // =========================
  // NEW WORKSPACE
  // =========================
  "new-workspace": {
    title: "Neuer Arbeitsbereich",
    placeholder: "Mein Arbeitsbereich",
    "legal-areas": "Rechtsgebiete",
    create: {
      title: "Neuen Arbeitsbereich erstellen",
      description:
        "Nach der Erstellung wird nur den Administratoren der Zugriff angezeigt. Du kannst nachträglich Benutzer hinzufügen.",
      error: "Fehler: ",
      cancel: "Abbrechen",
      "create-workspace": "Arbeitsbereich erstellen",
    },
  },
  // =========================
  // WORKSPACE NAMES (LEGAL AREAS)
  // =========================
  "workspace-names": {
    "Administrative Law": "Verwaltungsrecht",
    "Business Law": "Wirtschaftsrecht",
    "Civil Law": "Zivilrecht",
    "Criminal Law": "Strafrecht",
    "Diplomatic Law": "Diplomatisches Recht",
    "Fundamental Law": "Grundgesetz",
    "Human Rights Law": "Menschenrechte",
    "Judicial Laws": "Gerichtsrecht",
    "Security Laws": "Sicherheitsrecht",
    "Taxation Laws": "Steuerrecht",
  },
  // =========================
  // PRESETS
  // =========================
  presets: {
    "edit-title": "Standard-Prompt bearbeiten",
    description: "Beschreibung des Prompts",
    "description-placeholder":
      "Macht einen Zusammenfassung der angehängten Dateien.",
    deleting: "Wird gelöscht...",
    "delete-preset": "Standard-Prompt löschen",
    cancel: "Abbrechen",
    save: "Speichern",
    "add-title": "Standard-Prompt hinzufügen",
    "command-label": "Name des Prompts, ein einzelner Wort",
    "command-placeholder": "Zusammenfassung",
    "command-desc":
      "Der Name ist auch der Chatbox-Shortcut, der mit / beginnt, um dieses Prompt ohne Buttons zu verwenden.",
    "prompt-label": "Prompt",
    "prompt-placeholder":
      "Produziere eine Zusammenfassung der angehängten Dateien.",
    "prompt-desc":
      "Das Prompt, das gesendet wird, wenn dieses Standard-Prompt verwendet wird.",
    "tooltip-add": "Neues Standard-Prompt hinzufügen",
    "tooltip-hover": "Deine eigenen Standard-Prompts ansehen.",
    "confirm-delete": "Bestätige das Löschen der Standard-Prompt-Vorlage.",
  },
  // =========================
  // QURA BUTTONS
  // =========================
  qura: {
    "copy-to-cora": "Qura Quellencheck",
    "qura-status": "Qura-Button ist ",
    "copy-option": "Option kopieren",
    "option-quest": "Frage",
    "option-resp": "Antwort",
    "role-description":
      "Füge einen Qura-Button hinzu, um auf Qura.law abzustimmen",
  },
  // =========================
  // CHAT BOX DRAG & DROP COMPONENT
  // =========================
  chatboxdnd: {
    title: "Füge dieser Eingabeaufforderung eine Datei hinzu",
    description:
      "Ziehe deine Datei hierher, um sie dieser Eingabeaufforderung hinzuzufügen. Sie wird nicht dauerhaft im Arbeitsbereich gespeichert.",
    "file-prefix": "Datei:",
    "attachment-tooltip":
      "Diese Datei wird an deine Nachricht angehängt. Sie wird nicht dauerhaft im Arbeitsbereich gespeichert.",
    "uploaded-file-tag": "HOCHGELADENE BENUTZERDATEI",
  },
  // =========================
  // CONTEXT WINDOW DISPLAY
  // =========================
  context_window: {
    "context-window": "Kontextfenster",
    "max-output-tokens": "Max Ausgabe-Tokens",
    "output-limit": "Ausgabelimit",
    tokens: "Tokens",
    "fallback-value": "Fallback-Wert verwendet",
  },
  // =========================
  // PROMPT VALIDATION
  // =========================
  "prompt-validate": {
    edit: "Bearbeiten",
    response: "Antwort",
    prompt: "Prompt",
    regenerate: "Antwort neu generieren",
    good: "Gute Antwort",
    bad: "Schlechte Antwort",
    copy: "Kopieren",
    more: "Weitere Aktionen",
    fork: "Abzweigen",
    delete: "Löschen",
    cancel: "Abbrechen",
    save: "Speichern & Absenden",
    "export-word": "Als Word exportieren",
    exporting: "Exportiere...",
  },
  // =========================
  // VALIDATE ANSWER
  // =========================
  "validate-answer": {
    setting: "Validierungs-LLM",
    title: "Validierungs-LLM-Voreinstellung",
    description:
      "Dies sind die Zugangsdaten und Einstellungen für deinen bevorzugten Validierungs-LLM-Chat- und Embedding-Anbieter. Es ist wichtig, dass diese Schlüssel aktuell und korrekt sind, sonst funktioniert das System nicht ordnungsgemäß.",
    "toast-success": "Validierungs-LLM-Einstellungen aktualisiert",
    "toast-fail":
      "Fehler beim Aktualisieren der Validierungs-LLM-Einstellungen",
    saving: "Wird gespeichert...",
    "save-changes": "Änderungen speichern",
  },
  // =========================
  // VALIDATION
  // =========================
  validation: {
    responseHeader: "Hier ist die generierte Antwort",
    contextHeader: "Ursprünglicher Kontext und Quellen",
  },
  // =========================
  // CITATIONS
  // =========================
  citations: {
    show: "Zitate anzeigen",
    hide: "Zitate ausblenden",
    chunk: "Zitatteile",
    pdr: "Hauptdokument",
    "pdr-h": "Dokumenthervorhebung",
    referenced: "Referenziert",
    times: "mal.",
    citation: "Zitat",
    match: "Treffer",
    download:
      "Dieser Browser unterstützt keine PDFs. Bitte laden Sie das PDF herunter, um es anzuzeigen:",
    "download-btn": "PDF herunterladen",
    view: "Zitate anzeigen",
    sources: "Quellen und Zitate anzeigen",
    "pdf-collapse-tip":
      "Tipp: Sie können diese PDF-Registerkarte mit der Schaltfläche in der oberen linken Ecke minimieren",
    "open-in-browser": "Im Browser öffnen",
    "loading-pdf": "-- PDF wird geladen --",
    "error-loading": "Fehler beim Laden des PDFs",
    "no-valid-path": "Kein gültiger PDF-Pfad gefunden",
    "view-details": "Details anzeigen",
    "web-search": "Websuche",
    "web-search-summary": "Websuche-Zusammenfassung",
    "web-search-results": "Websuche-Ergebnisse",
    "no-web-search-results": "Keine Websuche-Ergebnisse gefunden",
    "previous-highlight": "Vorherige Hervorhebung",
    "next-highlight": "Nächste Hervorhebung",
    "try-alternative-view": "Alternative Ansicht versuchen",
  },
  // =========================
  // MANUAL WORK ESTIMATOR
  // =========================
  "manual-work-estimator": {
    title: "Schätzung der manuellen Arbeit",
    button: "Zeitschätzung",
    "show-prompt": "Prompt anzeigen",
    "hide-prompt": "Prompt ausblenden",
    "prompt-title": "Für die Schätzung verwendeter Prompt",
    "system-prompt": "System-Prompt",
    "user-content": "Benutzerinhalt",
    "provider-info": "Anbieterinformationen",
    model: "Modell",
    provider: "Anbieter",
  },
};
