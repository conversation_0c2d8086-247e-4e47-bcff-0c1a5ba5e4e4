const { v4: uuidv4 } = require("uuid");
const { DocumentManager } = require("../DocumentManager");
const { WorkspaceChats } = require("../../models/workspaceChats");
const { getVectorDbClass, getLLMProvider } = require("../helpers");
const { writeResponseChunk } = require("../helpers/chat/responses");
const { grepAgents } = require("./agents");
const {
  grepCommand,
  VALID_COMMANDS,
  chatPrompt,
  recentChatHistory,
  sourceIdentifier,
} = require("./index");
const { SystemSettings } = require("../../models/systemSettings");
const { TokenManager } = require("../helpers/tiktoken");
const path = require("path");
const { fillSourceWindow, fetchDeepSearchResults } = require("../helpers/chat");
const { getTruncatedTextMessage } = require("../helpers/documentDisplay");

const VALID_CHAT_MODE = ["chat", "query"];

// Default Standard Settings prompts
const DEFAULT_SYSTEM_PROMPT =
  "You are a legal assistant with expertise in law. Your goal is to provide accurate and helpful legal information based on the sources available to you. When answering questions, cite relevant sources where possible, and clearly state when you're providing general information versus specific legal advice. Always clarify that the response is intended as a first draft and that sources need verification. When you don't know or aren't sure about something, acknowledge the limitations clearly rather than making assumptions. Provide the response in the same language as the user's question.";

const DEFAULT_VALIDATION_PROMPT = `You are a legal quality assurance expert. Your task is to evaluate the accuracy, completeness, and helpfulness of AI-generated legal responses. Analyze both the response and the sources cited to ensure the information is presented accurately. Assess whether the response adequately addresses the user's question while properly acknowledging any limitations in the available information. Provide the response in the same language as the user's question.

Please validate the following AI-generated legal response by following these steps:

1. Identification of Deviations:
   - Read through the generated response and compare it carefully with the provided context.
   - Summarize if there are any discrepancies between the response and the source material. If so, describe what deviation has been identified.

2. Validation of Sources:
   - For each source mentioned in the generated response, confirm that the information provided is actually supported by the provided context.
   - Explain where support for each source has been found in the provided information, and note any differences or inaccuracies.

3. Fulfillment of Instructions:
   - Verify that the system prompt has been followed, including that the generated response contains proper source references using meaningful metadata (such as document titles, filenames, or other descriptive identifiers) within bracket format [Source Name] rather than generic numbered references.
   - Check that the response does not make any unsubstantiated claims or lack references for important parts.

4. Reporting:
   - Present the results of the validation checks without giving any personal interpretations or further investigations.
   - If everything is correct, confirm that the response is validated without deviations.
   - If everything is not correct, provide a bullet point list with instructions on what needs to be corrected.

Base your validation solely on the information provided and do not make any assumptions or additions of your own. Ensure the report is precisely about the reliability of the reviewed analysis and not a new independent investigation.`;

const DEFAULT_VECTOR_SEARCH_TOP_N = "30";

const llmConnectorCache = new Map();
const isDev = process.env.NODE_ENV !== "production";
function logDebug(...args) {
  if (isDev) console.log(...args);
}

async function streamChatWithWorkspaceLQA(
  request,
  response,
  workspace,
  message,
  chatMode = "chat",
  user = null,
  thread = null,
  attachments = [],
  chatId,
  isCanvasChat = false,
  preventChatCreation = false,
  settings_suffix = "",
  invoice_ref,
  vectorSearchMode = "default",
  hasUploadedFile = false,
  docxContent = null,
  displayMessage = null,
  useDeepSearch = false,
  cdbOptions = [],
  legalTaskConfig = {},
  styleAlignment = null
) {
  console.log(
    `[streamLQA DEBUG] Received useDeepSearch parameter: ${useDeepSearch}`
  );
  const uuid = uuidv4();

  // Add abort detection to handle when frontend cancels the request
  const startTime = Date.now();
  let cleanupExecuted = false;
  let streamAborted = false;

  const executeCleanup = (reason) => {
    if (cleanupExecuted) return;
    cleanupExecuted = true;
    streamAborted = true;

    const connectionDuration = Date.now() - startTime;
    console.log(
      `[streamLQA ABORTED] Reason: ${reason}, ChatId: ${chatId || uuid}, Workspace: ${workspace?.slug || "unknown"}, Thread: ${thread?.slug || "none"}, User: ${user?.id || "unknown"}, Duration: ${connectionDuration}ms`
    );
  };

  // Setup abort detection - when frontend calls abortController.abort(), these events fire
  request.on("close", () => executeCleanup("request.close"));
  request.on("aborted", () => executeCleanup("request.aborted"));
  response.on("close", () => executeCleanup("response.close"));

  // Helper function to check if stream was aborted
  const checkAborted = () => {
    if (streamAborted) {
      console.log(`[streamLQA] Stream aborted, exiting early`);
      return true;
    }
    return false;
  };

  const updatedMessage = await grepCommand(message, user);

  // Check for abort after each major operation
  if (checkAborted()) return;

  // fetch systemSettings and context window in parallel
  const modelSuffix = user?.custom_ai_userselected
    ? user.custom_ai_selected_engine || "_CUAI"
    : "";
  const [systemSettings, contextWindowPercentage] = await Promise.all([
    SystemSettings.currentSettings(),
    SystemSettings.getDynamicContextSettings(modelSuffix),
  ]);
  const attachmentContextPercentage =
    systemSettings.attachment_context_percentage || 70;
  logDebug(
    `[DEBUG_CUAI] Model suffix: "${modelSuffix}", context windowPct: ${contextWindowPercentage}%`
  );

  if (Object.keys(VALID_COMMANDS).includes(updatedMessage)) {
    const data = await VALID_COMMANDS[updatedMessage](
      workspace,
      message,
      uuid,
      user,
      thread,
      invoice_ref
    );
    writeResponseChunk(response, data);
    return;
  }

  // If is agent enabled chat we will exit this flow early.
  const isAgentChat = await grepAgents({
    uuid,
    response,
    message,
    user,
    workspace,
    thread,
  });
  if (isAgentChat) return;

  let chatProvider;
  let chatModel;

  // Get system settings first
  logDebug(
    `Model suffix determined: ${modelSuffix}, custom_ai_userselected=${user?.custom_ai_userselected}`
  );
  logDebug(
    `Fetched context windowPct: ${contextWindowPercentage}% for suffix ${modelSuffix}`
  );

  // Priority 1: Explicit settings_suffix overrides (e.g., _TM for templates)
  if (settings_suffix) {
    const providerKey = `LLMProvider${settings_suffix}`;
    const llmProviderSetting = systemSettings[providerKey]; // Renamed to avoid conflict
    logDebug(
      `[DEBUG_SUFFIX] Looking up ${providerKey}: "${llmProviderSetting}"`
    );

    if (llmProviderSetting && llmProviderSetting !== "none") {
      let modelPrefKey;
      const providerLower = llmProviderSetting.toLowerCase();

      switch (providerLower) {
        case "gemini":
          modelPrefKey = `GeminiLLMModelPref${settings_suffix}`;
          break;
        case "ollama":
          modelPrefKey = `OllamaLLMModelPref${settings_suffix}`;
          break;
        case "native":
          modelPrefKey = `NativeLLMModelPref${settings_suffix}`;
          break;
        case "litellm":
          modelPrefKey = `LiteLLMModelPref${settings_suffix}`;
          break;
        case "openai":
          modelPrefKey = `OpenAiModelPref${settings_suffix}`;
          break;
        case "azureopenai":
          modelPrefKey = `AzureOpenAiModelPref${settings_suffix}`;
          break;
        case "genericopenai":
          modelPrefKey = `GenericOpenAiModelPref${settings_suffix}`;
          break;
        default:
          const providerPrefix =
            llmProviderSetting.charAt(0).toUpperCase() +
            llmProviderSetting.slice(1).toLowerCase();
          modelPrefKey = `${providerPrefix}ModelPref${settings_suffix}`;
      }

      logDebug(
        `[DEBUG_SUFFIX] Generated model preference key: "${modelPrefKey}"`
      );
      const modelSetting = systemSettings[modelPrefKey]; // Renamed to avoid conflict
      if (modelSetting) {
        chatProvider = llmProviderSetting;
        chatModel = modelSetting;
        logDebug(
          `[DEBUG_SUFFIX] Selected provider (from suffix): "${chatProvider}", model: "${chatModel}"`
        );
      } else {
        logDebug(
          `[DEBUG_SUFFIX] No model found for key ${modelPrefKey} with suffix ${settings_suffix}`
        );
      }
    }
  }

  // Priority 2: Check if user has custom AI selected, only if not already set by settings_suffix
  if ((!chatProvider || !chatModel) && user?.custom_ai_userselected) {
    const cuaiSuffix = user.custom_ai_selected_engine || "_CUAI";
    logDebug(`[DEBUG_CUAI] CUAI suffix: "${cuaiSuffix}"`);
    const llmProviderCUAI = systemSettings[`LLMProvider${cuaiSuffix}`]; // Renamed
    logDebug(
      `[DEBUG_CUAI] Looking up LLMProvider${cuaiSuffix}: "${llmProviderCUAI}"`
    );
    if (llmProviderCUAI && llmProviderCUAI !== "none") {
      let modelPrefKey;
      const providerLower = llmProviderCUAI.toLowerCase();

      switch (providerLower) {
        case "gemini":
          modelPrefKey = `GeminiLLMModelPref${cuaiSuffix}`;
          break;
        case "ollama":
          modelPrefKey = `OllamaLLMModelPref${cuaiSuffix}`;
          break;
        case "native":
          modelPrefKey = `NativeLLMModelPref${cuaiSuffix}`;
          break;
        case "litellm":
          modelPrefKey = `LiteLLMModelPref${cuaiSuffix}`;
          break;
        case "openai":
          modelPrefKey = `OpenAiModelPref${cuaiSuffix}`;
          break;
        case "azureopenai":
          modelPrefKey = `AzureOpenAiModelPref${cuaiSuffix}`;
          break;
        case "genericopenai":
          modelPrefKey = `GenericOpenAiModelPref${cuaiSuffix}`;
          break;
        default:
          const providerPrefix =
            llmProviderCUAI.charAt(0).toUpperCase() +
            llmProviderCUAI.slice(1).toLowerCase();
          modelPrefKey = `${providerPrefix}ModelPref${cuaiSuffix}`;
      }

      logDebug(
        `[DEBUG_CUAI] Generated model preference key: "${modelPrefKey}"`
      );
      const modelCUAI = systemSettings[modelPrefKey]; // Renamed
      if (modelCUAI) {
        chatProvider = llmProviderCUAI;
        chatModel = modelCUAI;
        logDebug(
          `[DEBUG_CUAI] Selected CUAI provider: "${chatProvider}", model: "${chatModel}"`
        );
      } else {
        logDebug(
          `[DEBUG_CUAI] No model found for key ${modelPrefKey} with CUAI suffix ${cuaiSuffix}`
        );
      }
    } else {
      logDebug(
        `[DEBUG_CUAI] No valid LLM provider for suffix "${cuaiSuffix}", using default`
      );
    }
  }
  // Removed settings_suffix check from here as it's moved to be Priority 1

  // Priority 3: Fallback to workspace defaults or environment variables
  if (!chatProvider || !chatModel) {
    chatProvider = workspace?.chatProvider || process.env.LLM_PROVIDER;
    chatModel = workspace?.chatModel;

    // If we still don't have a model, check the environment variables based on the provider
    if (!chatModel && chatProvider) {
      const providerLower = chatProvider.toLowerCase();
      let modelPrefKey;

      switch (providerLower) {
        case "gemini":
          modelPrefKey = "GEMINI_LLM_MODEL_PREF";
          break;
        case "ollama":
          modelPrefKey = "OLLAMA_MODEL_PREF";
          break;
        case "native":
          modelPrefKey = "NATIVE_LLM_MODEL_PREF";
          break;
        case "litellm":
          modelPrefKey = "LITE_LLM_MODEL_PREF";
          break;
        case "openai":
          modelPrefKey = "OPEN_AI_MODEL_PREF";
          break;
        case "azureopenai":
          modelPrefKey = "AZURE_OPEN_AI_MODEL_PREF";
          break;
        case "genericopenai":
          modelPrefKey = "GENERIC_OPEN_AI_MODEL_PREF";
          break;
        default:
          // For most providers: uppercase first letter, then "MODEL_PREF"
          const providerPrefix =
            chatProvider.charAt(0).toUpperCase() +
            chatProvider.slice(1).toLowerCase();
          modelPrefKey = `${providerPrefix.toUpperCase()}_MODEL_PREF`;
      }

      if (modelPrefKey && process.env[modelPrefKey]) {
        chatModel = process.env[modelPrefKey];
      }
    }
    logDebug(`Fallback defaults: provider=${chatProvider}, model=${chatModel}`);
  }

  // cache or create LLMConnector
  const LLMConnector = (() => {
    const key = [
      chatProvider,
      chatModel,
      workspace.id,
      settings_suffix || modelSuffix,
    ].join("|");
    if (llmConnectorCache.has(key)) {
      return llmConnectorCache.get(key);
    }
    const connector = getLLMProvider({
      provider: chatProvider,
      model: chatModel,
      workspace,
      settings_suffix:
        settings_suffix ||
        (user?.custom_ai_userselected
          ? user.custom_ai_selected_engine || "_CUAI"
          : ""),
      useDeepSearch,
    });
    llmConnectorCache.set(key, connector);
    return connector;
  })();
  logDebug(
    `[DEBUG_CUAI] LLMConnector model: ${LLMConnector.model || "unknown"}`
  );

  const VectorDb = getVectorDbClass();
  const messageLimit = isCanvasChat ? 1 : workspace?.openAiHistory || 20;

  let completeText;
  function getEffectivePromptWindowLimit() {
    const useCustomLimit =
      user?.custom_ai_userselected &&
      typeof LLMConnector.customPromptWindowLimit === "function";

    let promptWindowLimit;
    if (useCustomLimit) {
      promptWindowLimit = LLMConnector.customPromptWindowLimit();
    } else {
      promptWindowLimit = LLMConnector.promptWindowLimit();
    }

    logDebug(
      `[DEBUG_CUAI] Prompt window limit: ${promptWindowLimit}, custom=${useCustomLimit}, contextPct=${contextWindowPercentage}%`
    );
    return promptWindowLimit;
  }

  let metrics = {};
  let contextTexts = [];
  let sources = [];
  let sourcesSave = [];
  let pinnedDocIdentifiers = [];
  const [hasVectorizedSpace, totalEmbeddingsCount] = await Promise.all([
    VectorDb.hasNamespace(workspace.slug),
    VectorDb.namespaceCount(workspace.slug),
  ]);

  // If we are here we know that we are in a workspace that is:
  // 1. Chatting in "chat" mode and may or may _not_ have embeddings
  // 2. Chatting in "query" mode and has at least 1 embedding
  // User is trying to query-mode chat a workspace that has no data in it - so
  // we should exit early as no information can be found under these conditions.
  if (
    (!hasVectorizedSpace || totalEmbeddingsCount === 0) &&
    chatMode === "query"
  ) {
    const textResponse =
      workspace?.queryRefusalResponse ??
      "There is no relevant information in this workspace to answer your query.";
    writeResponseChunk(response, {
      uuid,
      type: "textResponse",
      textResponse,
      sources: [],
      attachments,
      close: true,
      error: null,
    });
    await WorkspaceChats.new({
      workspaceId: workspace.id,
      prompt: displayMessage || message,
      response: {
        text: textResponse,
        sources: [],
        attachments,
        type: chatMode,
      },
      threadId: thread?.id || null,
      include: false,
      user,
      invoice_ref,
    });
    return;
  }

  const { rawHistory, chatHistory } = await recentChatHistory({
    user,
    workspace,
    thread,
    messageLimit,
  });

  // Removed incorrect fillSourceWindow call that was passing contextWindowPercentage directly
  // The context window percentage is used later when calculating maxAllowedTokens

  // Look for pinned documents and see if the user decided to use this feature. We will also do a vector search
  // as pinning is a supplemental tool but it should be used with caution since it can easily blow up a context window.
  // However, we limit the maximum of appended context to 80% of its overall size, mostly because if it expands beyond this
  // it will undergo prompt compression anyway to make it work. If there is so much pinned that the context here is bigger than
  // what the model can support - it would get compressed anyway and that really is not the point of pinning. It is really best
  // suited for high-context models.
  // DynamicContextWindowUsage Implementation
  // This section implements dynamic token allocation to maximize context utilization
  // by reallocating unused tokens from core content to additional sources

  // Step 1.1: Initialize token management
  const tokenManager = new TokenManager(LLMConnector.model);

  // Step 1.2: Get prompt window limit
  const promptWindowLimit = getEffectivePromptWindowLimit();
  // Step 2.1: Calculate token usage for mandatory content
  const systemPrompt = chatPrompt(workspace, user, styleAlignment);
  const systemTokens = tokenManager.countFromString(systemPrompt);
  const usedUserTokens = tokenManager.countFromString(message);

  const chatHistoryString = chatHistory
    .map((history) => `${history.role}: ${history.content}`)
    .join("\n\n");
  const usedHistoryTokens = tokenManager.countFromString(chatHistoryString);

  // Step 2.2: Process attachments first as they have highest priority
  const attachmentTokens = attachments?.length
    ? tokenManager.countFromString(
        attachments.map((a) => a.contentString || "").join("\n")
      )
    : 0;

  // Step 2.3: Calculate essential tokens (system, user, attachments)
  const essentialTokens = systemTokens + usedUserTokens + usedHistoryTokens;

  // Step 3: Apply dynamic context window percentage and calculate available tokens
  const maxAllowedTokens = Math.floor(
    promptWindowLimit * (contextWindowPercentage / 100)
  );
  logDebug(
    `[DEBUG_CUAI] Max allowed tokens: ${promptWindowLimit} * (${contextWindowPercentage}%/100) = ${maxAllowedTokens}`
  );

  // Use system setting for attachment context percentage with validation
  const validatedAttachmentPercentage = Math.min(
    Math.max(attachmentContextPercentage, 10),
    100
  );
  const maximumAttachmentAllowed =
    maxAllowedTokens * (validatedAttachmentPercentage / 100);

  const shouldCompressAttachments = attachmentTokens > maximumAttachmentAllowed;

  // Step 3.1: Reserve space for attachments first
  let remainingAfterAttachments;
  if (shouldCompressAttachments) {
    remainingAfterAttachments = maximumAttachmentAllowed;
  } else {
    remainingAfterAttachments = Math.max(
      0,
      maxAllowedTokens - attachmentTokens
    );
  }
  let extraTokens = Math.max(0, remainingAfterAttachments - essentialTokens);

  // Step 5: Use remaining tokens for pinned documents and vector search results
  // Only proceed with PDR if we have room
  let pinnedTokens = 0;
  let vectorTokens = 0;
  let remainingForPDR = extraTokens - vectorTokens;

  logDebug(
    `[streamLQA] Preparing content retrieval; extraTokens=${extraTokens}`
  );
  console.log(
    `[streamLQA] DeepSearch check: useDeepSearch=${useDeepSearch}, extraTokens=${extraTokens}`
  );

  // If DeepSearch is enabled, fetch DeepSearch results first
  if (useDeepSearch && extraTokens > 0) {
    // Get the LLM's context window size to calculate DeepSearch token allocation
    const llmContextWindow = LLMConnector.promptWindowLimit();

    // Get DeepSearch settings to determine percentage of context window
    const dsSettings = await SystemSettings.getDeepSearchSettings();
    const dsContextPercentage = dsSettings.contextPercentage || 15;

    // Calculate max tokens for DeepSearch based on LLM context window
    const dsMaxTokens = Math.floor(
      llmContextWindow * (dsContextPercentage / 100)
    );

    // Use the smaller of available tokens or calculated DeepSearch allocation
    const effectiveDsMaxTokens = Math.min(extraTokens, dsMaxTokens);

    console.log(
      `[streamLQA] Running DeepSearch with ${effectiveDsMaxTokens} tokens (${dsContextPercentage}% of ${llmContextWindow} context window)`
    );

    const deepResult = await fetchDeepSearchResults({
      query: message,
      maxTokens: effectiveDsMaxTokens,
      useDeepSearch,
      llmContextWindow: llmContextWindow,
    });

    if (deepResult) {
      // Log and include DeepSearch content
      const dsTokens = new TokenManager().countFromString(deepResult);
      console.log(`[streamLQA] DeepSearch added ${dsTokens} tokens of context`);
      contextTexts.push(deepResult);

      // Reduce available tokens for subsequent vector/pinned contexts
      remainingForPDR = Math.max(0, extraTokens - dsTokens);
      // Also adjust extraTokens so vector search uses less
      extraTokens = Math.max(0, extraTokens - dsTokens);
    }
  }

  // Perform vector search in the main workspace
  // Validate the parameters for performSimilaritySearch
  if (!workspace.slug || !message) {
    console.error("Invalid parameters for performSimilaritySearch");
  } else {
    let vectorSearchResults;

    if (
      isCanvasChat ||
      (chatMode === "chat" &&
        (!hasVectorizedSpace || totalEmbeddingsCount === 0))
    ) {
      vectorSearchResults = {
        contextTexts: [],
        sources: [],
        message: false,
      };
    } else {
      // Check for abort before vector search
      if (checkAborted()) return;

      // Perform vector search on the message text
      // Calculate remaining tokens for vector search
      const remainingForVectors = extraTokens;
      if (remainingForVectors > 0) {
        vectorSearchResults = await VectorDb.performSimilaritySearch({
          namespace: workspace.slug,
          input: message,
          LLMConnector,
          similarityThreshold: workspace?.similarityThreshold,
          topN: workspace?.topN,
          filterIdentifiers: pinnedDocIdentifiers,
          rerank: workspace?.vectorSearchMode === "rerank",
          workspace,
        });

        // Check for abort after vector search
        if (checkAborted()) return;

        vectorTokens = tokenManager.countFromString(
          vectorSearchResults.contextTexts.join("\n")
        );
      } else {
        vectorSearchResults = { contextTexts: [], sources: [], message: false };
      }
    }

    // Check for abort before processing sources
    if (checkAborted()) return;

    if (remainingForPDR > 0) {
      // Inline DocumentManager for pinned docs
      const pinnedManager = new DocumentManager({
        workspace,
        maxTokens: LLMConnector.promptWindowLimit(),
      });
      const pinnedDocs = await pinnedManager.pinnedDocs();

      // Check for abort after pinned docs
      if (checkAborted()) return;

      for (const doc of pinnedDocs) {
        const { pageContent, ...metadata } = doc;
        pinnedDocIdentifiers.push(sourceIdentifier(doc));
        pinnedTokens += doc.token_count_estimate;
        contextTexts.push(doc.pageContent);
        sources.push({
          text: pageContent.slice(0, 1_000) + getTruncatedTextMessage(),
          ...metadata,
        });
      }
    }

    remainingForPDR = extraTokens - vectorTokens - pinnedTokens;

    if (vectorSearchResults.sources.length > 0 && remainingForPDR > 0) {
      const uniqueSourceTitles = new Set();
      let { adjacentVector, keepPdrVectors } =
        await SystemSettings.getPdrSettings();

      // Check for abort after settings
      if (checkAborted()) return;

      let totalTokenCount = 0;
      let promptTokenLimit = remainingForPDR;

      for (const source of vectorSearchResults.sources) {
        const currentTitle = source.title;
        const currentTokens = source.token_count_estimate;

        if (!uniqueSourceTitles.has(currentTitle)) {
          if (totalTokenCount + currentTokens < promptTokenLimit) {
            uniqueSourceTitles.add(currentTitle);
            totalTokenCount += currentTokens;
          } else {
            // Only try adjacent vectors if we're well under the limit and have space after attachments
            if (source.index && totalTokenCount < promptTokenLimit) {
              try {
                const adjacentVectors = await VectorDb.getAdjacentVectors(
                  workspace.slug,
                  source.title,
                  source.index,
                  adjacentVector
                );

                // Check for abort after adjacent vectors
                if (checkAborted()) return;

                if (
                  totalTokenCount + adjacentVectors.totalTokens <
                  promptTokenLimit
                ) {
                  if (
                    adjacentVectors.previous &&
                    adjacentVectors.previous.length > 0
                  ) {
                    adjacentVectors.previous.forEach((text) => {
                      vectorSearchResults.contextTexts.push(text);
                    });
                  }
                  if (adjacentVectors.next && adjacentVectors.next.length > 0) {
                    adjacentVectors.next.forEach((text) => {
                      vectorSearchResults.contextTexts.push(text);
                    });
                  }
                  totalTokenCount += adjacentVectors.totalTokens;
                }
              } catch (error) {
                // Log the error but continue processing other sources
                console.error(
                  `Error retrieving adjacent vectors: ${error.message}`
                );
                // Don't abort the entire chat process for adjacent vector errors
              }
            }
          }
        }
      }
      const acceptedSources = Array.from(uniqueSourceTitles);
      if (totalTokenCount < promptTokenLimit && acceptedSources.length > 0) {
        // Inline DocumentManager for PDR docs
        const pdrManager = new DocumentManager({ workspace });
        const pdrDocs = await pdrManager.pdrDocs();

        // Check for abort after PDR docs
        if (checkAborted()) return;

        for (const doc of pdrDocs) {
          if (acceptedSources.includes(doc.title)) {
            const { pageContent, ...metadata } = doc;

            if (!pinnedDocIdentifiers.includes(sourceIdentifier(doc))) {
              pinnedDocIdentifiers.push(sourceIdentifier(doc));
              contextTexts.push(doc.pageContent);
              sources.push({
                text: pageContent.slice(0, 1_000) + getTruncatedTextMessage(),
                ...metadata,
              });

              if (!keepPdrVectors) {
                // Instead of removing from vectorSearchResults.sources, we'll create a separate array
                // for context building that excludes the vectors for this document.
                // This preserves vector matches for citation display while still respecting the keepPdrVectors setting
                // for context building.
                vectorSearchResults.contextTexts =
                  vectorSearchResults.contextTexts.filter((text, index) => {
                    const correspondingSource =
                      vectorSearchResults.sources[index];
                    return (
                      correspondingSource &&
                      correspondingSource.title !== doc.title
                    );
                  });

                // Mark these vectors as filtered for context, but keep them in sources for citation
                vectorSearchResults.sources.forEach((source) => {
                  if (source.title === doc.title) {
                    source.excludedFromContext = true;
                  }
                });
              }
            }
          }
        }
      }
    }

    // Failed similarity search if it was run at all and failed.
    if (vectorSearchResults.message) {
      writeResponseChunk(response, {
        uuid,
        type: "abort",
        textResponse: null,
        sources: [],
        close: true,
        error: vectorSearchResults.message,
      });
      return;
    }

    // Check for abort before processing sources
    if (checkAborted()) return;

    // Filter out sources that were marked as excluded from context before filling the source window
    const contextFilteredSources = vectorSearchResults.sources.filter(
      (source) => !source.excludedFromContext
    );

    const filledSources = fillSourceWindow({
      nDocs: workspace?.topN || 4,
      searchResults: contextFilteredSources,
      history: rawHistory,
      filterIdentifiers: pinnedDocIdentifiers,
    });

    // Why does contextTexts get all the info, but sources only get current search?
    // This is to give the ability of the LLM to "comprehend" a contextual response without
    // populating the Citations under a response with documents the user "thinks" are irrelevant
    // due to how we manage backfilling of the context to keep chats with the LLM more correct in responses.
    // If a past citation was used to answer the question - that is visible in the history so it logically makes sense
    // and does not appear to the user that a new response used information that is otherwise irrelevant for a given prompt.
    // TLDR; reduces GitHub issues for "LLM citing document that has no answer in it" while keep answers highly accurate.
    contextTexts = [...contextTexts, ...filledSources.contextTexts];

    // Create two separate source arrays:
    // 1. sources - for context generation (only includes contextual sources)
    // 2. sourcesForCitation - for display in the citation modal (includes all sources)
    sources = [...sources, ...filledSources.sources];

    // Include all vector sources in sourcesForCitation, even those excluded from context
    const sourcesForCitation = [...sources, ...vectorSearchResults.sources];

    // For backward compatibility, keep sourcesSave with the same content as sourcesForCitation
    sourcesSave = sourcesForCitation;

    const documentsHotDirPath = path.resolve(process.env.STORAGE_DIR, `hotdir`);
    for (const source of sources) {
      const url = source.url;
      const match = url.match(/\w+:\/\/[^/]+\/(.*)/);
      source.url = match ? match[1] : url;
      source.path = "file:///" + documentsHotDirPath + "/" + url;
      if (!source.url.startsWith("/hotdir/")) {
        source.url = path.join("/hotdir/", source.url);
      }
    }

    // If in query mode and no context chunks are found from search, backfill, or pins -  do not
    // let the LLM try to hallucinate a response or use general knowledge and exit early
    if (chatMode === "query" && contextTexts.length === 0) {
      const textResponse =
        workspace?.queryRefusalResponse ??
        "There is no relevant information in this workspace to answer your query.";
      writeResponseChunk(response, {
        uuid,
        type: "textResponse",
        textResponse,
        sources: [],
        close: true,
        error: null,
      });

      if (!preventChatCreation) {
        await WorkspaceChats.new({
          workspaceId: workspace.id,
          prompt: String(displayMessage || message),
          response: {
            text: textResponse,
            sources: [],
            type: chatMode,
            attachments,
          },
          threadId: thread?.id || null,
          user,
          invoice_ref,
        });
      }
      return;
    }

    // Check for abort before LLM processing
    if (checkAborted()) return;

    // Check if LLMConnector is defined
    if (!LLMConnector) {
      console.error("LLMConnector is not defined. Check initialization.");
      return;
    }

    // Check if defaultTemp is defined
    if (typeof LLMConnector.defaultTemp === "undefined") {
      console.error(
        "LLMConnector.defaultTemp is not defined. Using fallback value."
      );
      LLMConnector.defaultTemp = 0.7; // Fallback value
    }

    // Use a default temperature if not provided
    const temperature = workspace?.openAiTemp ?? LLMConnector.defaultTemp;

    // Compress & Assemble message to ensure prompt passes token limit with room for response
    // and build system messages based on inputs and history.
    const maxAllowedTokens =
      getEffectivePromptWindowLimit() * (contextWindowPercentage / 100);

    // Enhanced logging to show exactly which suffix and key was used for dynamic context window
    const suffix = user?.custom_ai_userselected
      ? user?.custom_ai_selected_engine || "_CUAI"
      : "";
    const contextWindowKey =
      suffix === "_CUAI"
        ? "custom_dynamic_context_window_percentage"
        : `custom_dynamic_context_window_percentage_${suffix.replace("_CUAI", "")}`;

    logDebug(
      `[DEBUG_CUAI] Context window details: suffix="${suffix}", lookupKey="${contextWindowKey}", value=${contextWindowPercentage}%, effectiveTokens=${maxAllowedTokens}`
    );

    const messages = await LLMConnector.compressMessages(
      {
        systemPrompt: chatPrompt(workspace, user, styleAlignment),
        userPrompt: updatedMessage,
        contextTexts,
        chatHistory,
        attachments,
        maxAllowedTokens,
      },
      rawHistory
    );

    // Check for abort before starting LLM interaction
    if (checkAborted()) return;

    // If streaming is not explicitly enabled for connector
    // we do regular waiting of a response and send a single chunk.
    if (LLMConnector.streamingEnabled() !== true) {
      logDebug(
        `\x1b[31m[STREAMING DISABLED]\x1b[0m Streaming is not available for ${LLMConnector.constructor.name}. Will use regular chat method.`
      );
      try {
        const { textResponse, metrics: performanceMetrics } =
          await LLMConnector.getChatCompletion(messages, {
            temperature: workspace?.openAiTemp ?? LLMConnector.defaultTemp,
          });

        // Check for abort after LLM completion
        if (checkAborted()) return;

        completeText = textResponse;
        metrics = performanceMetrics;
        writeResponseChunk(response, {
          uuid,
          sources: sourcesForCitation,
          type: "textResponseChunk",
          textResponse: completeText,
          close: true,
          error: false,
          metrics,
        });
      } catch (error) {
        console.error(
          `Error getting chat completion for ${LLMConnector.constructor.name}: ${error.message}`
        );
        // Check for rate limit error (e.g., status code 429)
        // The specific error structure might depend on the underlying HTTP client library (e.g., axios, node-fetch)
        const statusCode =
          error?.response?.status || error?.status || error?.statusCode;
        let errorText = error.message;
        if (statusCode === 429) {
          errorText =
            "Rate limit exceeded with the configured AI provider. Please check your provider's limits or try again later.";
        }
        writeResponseChunk(response, {
          uuid,
          type: "abort",
          textResponse: null,
          sources: [],
          close: true,
          error: errorText,
        });
        return;
      }
    } else {
      try {
        const stream = await LLMConnector.streamGetChatCompletion(messages, {
          temperature: temperature,
        });

        // Check for abort before handling stream
        if (checkAborted()) return;

        // Create a wrapper for handling stream with abort checking
        const handleStreamWithAbortCheck = async (
          response,
          stream,
          options
        ) => {
          try {
            // Check if the LLM provider has a custom handleStream method that supports abort checking
            if (typeof LLMConnector.handleStreamWithAbort === "function") {
              return await LLMConnector.handleStreamWithAbort(
                response,
                stream,
                options,
                checkAborted
              );
            }

            // Fallback to standard handleStream but with periodic abort checks
            let completeText = "";
            let lastAbortCheck = Date.now();
            const abortCheckInterval = 100; // Check every 100ms

            const originalHandleStream =
              LLMConnector.handleStream.bind(LLMConnector);

            // Create a proxy response object that checks for aborts
            const responseProxy = new Proxy(response, {
              get(target, prop) {
                // Check for abort on write operations
                if (prop === "write" || prop === "end") {
                  const now = Date.now();
                  if (now - lastAbortCheck > abortCheckInterval) {
                    if (checkAborted()) {
                      console.log(
                        "[streamLQA] Aborting stream during write operation"
                      );
                      return () => {}; // Return no-op function
                    }
                    lastAbortCheck = now;
                  }
                }
                return target[prop];
              },
            });

            return await originalHandleStream(responseProxy, stream, options);
          } catch (error) {
            // If the error is due to abort, handle it gracefully
            if (streamAborted || error.name === "AbortError") {
              console.log("[streamLQA] Stream aborted during LLM streaming");
              return "";
            }
            throw error;
          }
        };

        completeText = await handleStreamWithAbortCheck(response, stream, {
          uuid,
          sources: sourcesForCitation,
        });
        metrics = stream.metrics;
      } catch (error) {
        console.error(
          `Error streaming chat completion for ${LLMConnector.constructor.name}: ${error.message}`
        );
        const statusCode =
          error?.response?.status || error?.status || error?.statusCode;
        let errorText = error.message;
        if (statusCode === 429) {
          errorText =
            "Rate limit exceeded with the configured AI provider. Please check your provider's limits or try again later.";
        }
        writeResponseChunk(response, {
          uuid,
          type: "abort",
          textResponse: null,
          sources: [],
          close: true,
          error: errorText,
        });
        return;
      }
    }

    // Check for abort before finalizing
    if (checkAborted()) return;

    if (completeText?.length > 0) {
      let newChatId = null;

      // Only create a new chat if we're allowed to and don't already have one
      if (!preventChatCreation) {
        try {
          const { chat } = await WorkspaceChats.new({
            workspaceId: workspace.id,
            prompt: String(displayMessage || message),
            response: {
              text: completeText,
              sources: sourcesForCitation,
              type: chatMode,
              attachments,
              metrics,
            },
            threadId: thread?.id || null,
            user,
            invoice_ref,
          });
          newChatId = chat?.id;
        } catch (error) {
          console.error("Error creating chat:", error);
        }
      } else if (chatId) {
        // If we have an existing chatId but are preventing new creation,
        // just update the existing chat
        const chat = await WorkspaceChats.get({ id: chatId });
        const response = JSON.parse(chat.response);
        response.text = completeText;
        await WorkspaceChats.update(chatId, {
          response: JSON.stringify(response),
        });
        newChatId = chatId;
      }

      // Use the newly created chat ID or the existing one
      chatId = newChatId || chatId;

      // Save chat logs if enabled
      if (chatId) {
        const saveChatLog = await SystemSettings.isPromptOutputLogging();
        if (saveChatLog) {
          const fs = require("fs");
          const logsDir = path.join(__dirname, "../../storage/logs");
          fs.mkdirSync(logsDir, { recursive: true });
          const filePath = path.join(logsDir, `workspace-chat-${chatId}.json`);
          fs.writeFile(filePath, JSON.stringify(messages, null, 2), (err) => {
            if (err) {
              console.error("Error saving chat log file:", err);
            }
          });
        }
      }

      // Check for abort before sending final response
      if (checkAborted()) {
        console.log("[streamLQA] Stream aborted before sending final response");
        return;
      }

      // Send a finalization message with the complete text, sources, and chatId
      // This ensures the frontend receives a single, complete message
      writeResponseChunk(response, {
        uuid,
        type: "finalizeResponseStream",
        textResponse: completeText.trim(),
        sources: sourcesForCitation,
        fileAttachments: attachments.map((a) => a.id),
        close: true,
        error: false,
        chatId,
        metrics,
      });
      return;
    }

    // Check for abort before fallback finalization
    if (checkAborted()) {
      console.log("[streamLQA] Stream aborted before fallback finalization");
      return;
    }

    // Final fallback finalization if no content was generated
    writeResponseChunk(response, {
      uuid,
      type: "finalizeResponseStream",
      textResponse: "", // Empty text for fallback
      sources: [],
      close: true,
      error: false,
      chatId: chatId || null, // Include chatId to ensure message tools appear
      metrics: metrics || {}, // Include metrics
    });
  }
}

module.exports = {
  VALID_CHAT_MODE,
  streamChatWithWorkspaceLQA,
  DEFAULT_SYSTEM_PROMPT,
  DEFAULT_VALIDATION_PROMPT,
  DEFAULT_VECTOR_SEARCH_TOP_N,
};
