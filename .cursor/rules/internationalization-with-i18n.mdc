---
description: Guidelines for internationalization (i18n) and translation handling
globs: "**/*.{js,jsx,ts,tsx}"
alwaysApply: true
---
# Internationalization (i18n) Guidelines

## Core Principles
- NEVER use hardcoded text strings for user interface elements
- ALWAYS use translation keys for ALL user-facing text
- NEVER replace existing translation keys with hardcoded text
- When adding new features or modifying existing ones, ensure ALL text is internationalized
- Add translation keys to ALL locale files, not just the English version

## Frontend Translation Keys
- Translation keys are located in feature-specific files in `frontend/src/locales/[lang]/`
- DO NOT use or create translation.json files
- Always use existing keys when possible
- Add new keys to the appropriate section or file
- Use the English translation as fallback together with the proper translation key
- Use singular form for internationalization keys (e.g., CONTENT_EXAMPLE instead of CONTENT_EXAMPLES)

## Backend Translation Keys
- Server and collector files have specific existing files for i18n keys
- Add new translation keys to the appropriate locale files
- Ensure error messages and system messages are internationalized

## Supported Languages
- English (en) - Default
- French (fr)
- Swedish (sv)
- Ki<PERSON>rwanda (rw)
- German (de)
- Norwegian (no)
- Polish (pl)

## Creating New Locale Files for New Features

When implementing a new feature that requires multiple translation keys, create a dedicated locale file for that feature:

### 1. File Naming and Organization
- Create a new file named after the feature (e.g., `manualWorkEstimator.js`, `documentBuilder.js`, `legalTemplates.js`)
- Place the file in each language directory: `frontend/src/locales/[lang]/featureName.js`
- Group all related translation keys for the feature in this single file

### 2. File Structure
Each new locale file should follow this structure:

```javascript
const TRANSLATIONS = {
  "feature-name": {
    "key-1": "Translation text",
    "key-2": "Another translation",
    "sub-section": {
      "nested-key": "Nested translation",
    },
  },
};

export default TRANSLATIONS;
```

### 3. Implementation Steps
1. **Identify all text strings** for the new feature that need translation
2. **Create the English locale file first** with all required keys and English translations
3. **Copy the structure to all other language directories** (fr, sv, rw, de, no, pl)
4. **Translate all strings** to the respective languages
5. **Update resources.js** to import and register the new locale files (see section 7)
6. **Test the feature** to ensure all translations are working correctly

### 4. Key Naming Conventions
- Use kebab-case for all keys (e.g., `manual-work-estimator`, `time-estimate`)
- Group related keys under logical sections
- Use descriptive names that indicate the context and purpose
- For buttons, modals, and UI elements, use consistent naming patterns:
  - `button-text`: For button labels
  - `modal-title`: For modal headers
  - `description`: For help text or descriptions
  - `error-message`: For error states
  - `success-message`: For success states

### 5. Example: Manual Work Estimator Feature
```javascript
// frontend/src/locales/en/manualWorkEstimator.js
const TRANSLATIONS = {
  "manual-work-estimator": {
    "button-text": "Time estimate",
    "modal-title": "Manual Work Time Estimate",
    "loading-text": "Estimating...",
    "error-message": "Failed to estimate manual work time",
    "close-button": "Close",
    "prompt-section": {
      "title": "System Prompt",
      "description": "The prompt used to generate this estimate",
    },
  },
};

export default TRANSLATIONS;
```

### 6. Automatic Loading
The static loader in `resources.js` automatically imports and merges all files for each language, so no additional configuration is needed when creating new locale files.

### 7. **CRITICAL: Update resources.js**
When adding new locale files, you **MUST** update `frontend/src/locales/resources.js` to include the new files:

#### Steps to Update resources.js:
1. **Add import statements** for each language at the top of the file:
   ```javascript
   // Import all English locale files
   import EnglishManualWorkEstimator from "./en/manualWorkEstimator.js";

   // Import all French locale files
   import FrManualWorkEstimator from "./fr/manualWorkEstimator.js";

   // Import all German locale files
   import DeManualWorkEstimator from "./de/manualWorkEstimator.js";

   // Import all Swedish locale files
   import SvManualWorkEstimator from "./sv/manualWorkEstimator.js";

   // Import all Kinyarwanda locale files
   import RwManualWorkEstimator from "./rw/manualWorkEstimator.js";

   // Import all Norwegian locale files
   import NoManualWorkEstimator from "./no/manualWorkEstimator.js";

   // Import all Polish locale files
   import PlManualWorkEstimator from "./pl/manualWorkEstimator.js";
   ```

2. **Add the files to the localeFiles object** for each language:
   ```javascript
   const localeFiles = {
     en: {
       // ... existing files
       manualWorkEstimator: EnglishManualWorkEstimator,
     },
     fr: {
       // ... existing files
       manualWorkEstimator: FrManualWorkEstimator,
     },
     de: {
       // ... existing files
       manualWorkEstimator: DeManualWorkEstimator,
     },
     sv: {
       // ... existing files
       manualWorkEstimator: SvManualWorkEstimator,
     },
     rw: {
       // ... existing files
       manualWorkEstimator: RwManualWorkEstimator,
     },
     no: {
       // ... existing files
       manualWorkEstimator: NoManualWorkEstimator,
     },
     pl: {
       // ... existing files
       manualWorkEstimator: PlManualWorkEstimator,
     },
   };
   ```

#### Important Notes:
- **Without updating resources.js, your new locale files will NOT be loaded**
- The import names should follow the pattern: `[Language][FeatureName]` (e.g., `EnglishManualWorkEstimator`)
- The property names in `localeFiles` should match your filename (e.g., `manualWorkEstimator`)
- Add the new file to **ALL** supported languages in the `localeFiles` object

## Special Considerations
- Internationalize content example tags with language-specific versions
- When English is set as system language, apply English as the default language
- Make language selector half-width in modals
- Code that checks for content example tags should use localized tag values from i18n

## Special Localization Systems

### Version System Exception

**IMPORTANT:** The version tooltip system uses a special localization approach that is different from the standard i18n system:

- **Version descriptions** are stored directly in `version.json` with inline language keys
- **Do NOT** add version descriptions to standard locale files
- **Backend language detection** automatically selects the appropriate language
- **Only the tooltip title** uses standard i18n locale files

See `version-localization.mdc` for detailed guidelines on this special system.

## Regional Branding Requirements

Due to different distributor relationships in different regions, the platform uses different branding based on locale:

### **Nordic Region (Foytech Branding)**
- **Swedish (sv)**: Use "Foytech" or "Foytechs plattform" branding
- **Norwegian (no)**: Use "Foytech" or "Foytech Admin" branding

### **Other Regions (IST Legal Branding)**
- **English (en)**: Use "IST Legal" or "IST Legal Platform" branding
- **French (fr)**: Use "IST Legal" branding
- **German (de)**: Use "IST Legal" branding
- **Polish (pl)**: Use "IST Legal" branding
- **Kinyarwanda (rw)**: Use "IST Legal" branding

### **Key Implementation Points**
- When updating locale files, maintain this regional branding distinction
- The `"ist-legal-team"` author key should be:
  - `"Foynet Admin"` or `"Foytech Admin"` for Swedish and Norwegian
  - `"IST Legal Team"` for all other locales
- Platform titles should reflect the appropriate regional branding:
  - Nordic: "Foytechs plattform" (Swedish), "Foytech's platform" (Norwegian)
  - Others: "IST Legal Platform" or similar variations
- News system items, welcome messages, and other branded content must follow this pattern
- When creating new features or updating existing ones, ensure branding consistency per region

### **Example: News System Items**
```javascript
// Swedish (sv) - Nordic branding
authors: {
  "ist-legal-team": "Foynet Admin",
},
"system-welcome-2024": {
  title: "Välkommen till Foytechs plattform",
  // ...
}

// English (en) - IST Legal branding
authors: {
  "ist-legal-team": "IST Legal Team",
},
"system-welcome-2024": {
  title: "Welcome to IST Legal Platform",
  // ...
}
```

This branding distinction must be maintained across all locale file updates to ensure proper regional representation.

## Common Mistakes to Avoid
- Using hardcoded strings like "Save", "Cancel", "Submit", etc. instead of translation keys
- Adding placeholder text with comments like `// Placeholder` that gets forgotten during development
- Using hardcoded error messages or success messages in toast notifications
- Forgetting to internationalize dynamic content (e.g., form validation messages)
- Adding new translation keys to only one locale file instead of all supported languages
- Using template literals with hardcoded text instead of translation keys with variables
- **Scattering related translation keys across multiple files instead of creating a dedicated feature file**
- **Forgetting to update resources.js when adding new locale files (translations will not load)**
- **Adding version descriptions to locale files instead of version.json (use the special version system)**

## Best Practices for Implementation
- Use the `useTranslation` hook in React components to access translation functions
- For conditional text, use translation keys with fallbacks: `t("custom-key", t("fallback-key"))`
- For dynamic content, use translation keys with variables: `t("key", { variable: value })`
- When adding new features, create all necessary translation keys before implementing the UI
- Run linting checks to identify hardcoded strings before committing code
- Review PRs specifically for internationalization compliance
- When fixing bugs, check if any hardcoded text was introduced and replace with translation keys
- **Create dedicated locale files for new features to keep related translations organized**
- **Always add the new locale file to ALL supported languages, not just English**
- **Always update resources.js when creating new locale files - this is mandatory for translations to work**

## Organizing Translation Keys in Multiple Files

To improve maintainability, you can split translation keys into multiple files under each locale directory (e.g., `common.js`, `recentUploads.js`, `sidebar.js`, `legalTemplates.js`). The static loader in `resources.js` automatically imports and merges all files for each language.

### File Naming Conventions
- Filenames should reflect the feature or component (e.g., `recentUploads.js`, `sidebar.js`, `legalTemplates.js`).
- Filenames can be arbitrary; the loader doesn't depend on filenames, only on content.

### Directory Structure Example
```text
frontend/src/locales/
  en/
    common.js
    recentUploads.js
    sidebar.js
    legalTemplates.js
    manualWorkEstimator.js
    documentBuilder.js
  fr/
    common.js
    recentUploads.js
    sidebar.js
    legalTemplates.js
    manualWorkEstimator.js
    documentBuilder.js
```

### Migration Steps
1. Identify related keys in `common.js` by feature or component.
2. Create a new file in each locale directory (e.g., `recentUploads.js`, `legalTemplates.js`).
3. Move the relevant key-value pairs from `common.js` into the new file.
4. Run tests to verify bundles include the new keys.
5. Remove migrated keys from `common.js` and delete any empty files.

### Adding New Locale Files
When adding support for a new language:
1. Create a new `[name≈].js` file in the new language's locale directory
2. Copy the structure from the English version
3. Translate all strings while maintaining the exact same key structure
4. Verify that all keys from the English version exist in the new locale file

## Document Builder Prompts Internationalization

When adding or modifying prompts for the Document Builder page (defined in `server/utils/chats/prompts/legalDrafting.js` and displayed in `frontend/src/pages/GeneralSettings/DocumentBuilder/index.jsx`), specific attention must be paid to internationalization:

1.  **Identify UI Text**: Determine which parts of your prompt configuration in `exportedLegalPrompts` will be displayed as UI text. This includes:
    *   Group titles (from `GROUP_TITLE` entries, using their `defaultContent` as English text for the `label` key).
    *   Group descriptions (from `GROUP_DESCRIPTION` entries, using their `defaultContent` as English text for the `label` key).
    *   Individual prompt labels/titles (from `SYSTEM_PROMPT`, `USER_PROMPT`, `PROMPT_TEMPLATE` entries, using their `label` field as the key and `defaultContent` as the English text).
    *   Individual prompt help text/descriptions (from `SYSTEM_PROMPT`, `USER_PROMPT`, `PROMPT_TEMPLATE` entries, using their `description` field as the key).

2.  **Define Translation Keys in `legalDrafting.js`**:
    *   For `GROUP_TITLE` and `GROUP_DESCRIPTION` objects in `exportedLegalPrompts`:
        *   The `label` field (e.g., `"document-builder.prompts.group.document_summary.title"`) **is the translation key**.
        *   The `defaultContent` field (e.g., `"Document Summary Prompts"`) is the **default English translation** for that key.
    *   For `SYSTEM_PROMPT`, `USER_PROMPT`, and `PROMPT_TEMPLATE` objects:
        *   The `label` field (e.g., `"document-builder.prompts.document-summary-system-label"`) is the **translation key for the UI title/label** of that prompt.
        *   The `description` field (e.g., `"document-builder.prompts.document-summary-system-description"`) is the **translation key for the UI help text/description** for that prompt.
        *   The `defaultContent` of these prompt objects is the actual prompt text, *not* directly a translation key's value, but it serves as the English version for the `label` key.

3.  **Add Keys and Translations to ALL Frontend Locale Files**:
    *   Take all translation keys identified from the `label` and `description` fields in `exportedLegalPrompts`.
    *   Add these keys and their corresponding English translations (derived from `defaultContent` for group titles/descriptions, and specific English text for prompt labels/descriptions) to `frontend/src/locales/en/common.js`.
    *   Crucially, add these same keys with their respective translations to **all other language files** (e.g., `sv/common.js`, `fr/common.js`, etc.).

**Example Snippet for `frontend/src/locales/en/common.js`:**

```javascript
// ... other translations
"document-builder": {
  "prompts": {
    "group": {
      "document_summary": {
        "title": "Document Summary Prompts", // from defaultContent of GROUP_TITLE
        "description": "Configure system and user prompts for Document Summary." // from defaultContent of GROUP_DESCRIPTION
      },
      // ... other groups
    },
    "document-summary-system-label": "Document Summary (System)", // English label for the system prompt
    "document-summary-system-description": "System prompt for instructing the AI on how to summarize a document's content and relevance to a legal task.", // English help text
    // ... other prompt labels and descriptions
  }
},
// ...
```

**Key Takeaway**: Every piece of text from `legalDrafting.js` that appears in the Document Builder UI must be represented by a translation key in `exportedLegalPrompts` and then defined in all frontend `common.js` locale files. Refer to the comments in `server/utils/chats/prompts/legalDrafting.js` and the documentation in `server/docs/document_builder_prompts.md` for more detailed examples.
