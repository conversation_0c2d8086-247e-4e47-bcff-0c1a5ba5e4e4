---
description:
globs:
alwaysApply: false
---
# Mermaid Diagramming Guidelines

## Overview

This document provides guidelines for creating Mermaid diagrams within this project, particularly focusing on avoiding common parsing errors. When encountering parsing errors, especially those of the form `Expecting ..., got 'PS'`, the first step should almost always be to check for and appropriately quote labels containing special characters.

## Labeling Nodes with Special Characters or Complex Text

Mermaid can be sensitive to certain characters and structures within node labels. Errors frequently occur when labels contain:

*   **Parentheses:** `()`
*   **Ampersands:** `&`
*   **Percentage signs:** `%`
*   **Commas, periods, slashes, or other punctuation** used in a way that might conflict with <PERSON><PERSON>'s own syntax delimiters.
*   **Any other non-alphanumeric characters** (excluding spaces if the label is simple).

These errors often manifest as `Expecting 'SQE', 'DOUBLECIRCLEEND', 'PE', '-)', 'STADIUMEND', 'SUBROUTINEEND', 'PIPE', 'CYLINDEREND', 'DIAMOND_STOP', 'TAGEND', 'TRAPEND', 'INVTRAPEND', 'UNICODE_TEXT', 'TEXT', 'TAGSTART', got 'PS'` or variations thereof, indicating the parser encountered an unexpected character sequence where it expected part of its own syntax.

## The Primary Solution: Quote Your Labels

The most robust and generally applicable solution to prevent these parsing errors is to **enclose the entire node label in double quotes (`"`)**. This explicitly tells the Mermaid parser to treat the content within the quotes as literal text for the label.

### Examples

**Problematic (may cause parsing errors with many Mermaid renderers):**

```mermaid
graph TD
    A[Request In (message, workspace, user, etc.)];
    B{Determine LLM Provider & Model (System/User/Suffix Settings)};
    C{Calculate Token Limits (Dynamic Context % )};
    D[Fetch Adjacent Vectors (if applicable)];
    E[LLMConnector.handleStream (Send Chunks)];
    F{Save Chat Logs (if enabled)};
    G[Compress Messages (System, User, Memo Results, Chunk Content, History, Attachments)];
```

**Corrected (enclosed in double quotes):**

```mermaid
graph TD
    A["Request In (message, workspace, user, etc.)"];
    B{"Determine LLM Provider & Model (System/User/Suffix Settings)"};
    C{"Calculate Token Limits (Dynamic Context % )"};
    D["Fetch Adjacent Vectors (if applicable)"];
    E["LLMConnector.handleStream (Send Chunks)"];
    F{"Save Chat Logs (if enabled)"};
    G["Compress Messages (System, User, Memo Results, Chunk Content, History, Attachments)"];
```

**General Rule of Thumb:**

*   **If a label contains *any* character that is not a letter, number, or space, try quoting it first.**
*   This is especially true for parentheses `()`, percentage signs `%`, ampersands `&`, and other punctuation.
*   When in doubt, quote the label. It rarely hurts and often solves parsing issues.

This quoting strategy applies to all node shapes:

*   Rectangular (default): `NodeId["Label with (special) chars"]`
*   Rhombus (decision): `NodeId{"Label with (special) chars"}`
*   Round edges: `NodeId("Label with (special) chars")`
*   Stadium-shaped: `NodeId(["Label with (special) chars"])`
*   Circle: `NodeId(("Label with (special) chars"))`
*   Asymmetric: `NodeId>"Label with (special) chars"]`
*   Hexagon: `NodeId{{"Label with (special) chars"}}`
*   Parallelogram: `NodeId[/"Label with (special) chars"/]` or `NodeId[\"Label with (special) chars"\]`
*   Trapezoid: `NodeId[/"Label with (special) chars"\]` or `NodeId[\"Label with (special) chars"/] `

By consistently quoting labels that might be problematic, you can significantly reduce the occurrence of frustrating parsing errors when working with Mermaid diagrams.
