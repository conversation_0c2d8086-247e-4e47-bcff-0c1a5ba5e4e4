const { systemEndpoints } = require("../../../endpoints/system");
const {
  exportedLegalPrompts: mockExportedLegalPrompts,
} = require("../../../utils/chats/prompts/legalDrafting");
const {
  PROMPT_MAPPINGS: mockPromptMappings,
} = require("../../../utils/chats/helpers/promptManager");

// Mock dependencies of system.js that might cause issues if not mocked
jest.mock("../../../models/vectors", () => ({
  DocumentVectors: {
    // Add mock implementations for any functions used by system.js if necessary
    // For now, a simple mock object might suffice if only its existence is checked.
    find: jest.fn().mockResolvedValue([]),
    count: jest.fn().mockResolvedValue(0),
    delete: jest.fn().mockResolvedValue({ count: 0 }),
    createMany: jest.fn().mockResolvedValue({ count: 0 }),
    create: jest.fn().mockResolvedValue({}),
  },
}));

jest.mock("../../../models/organization", () => ({
  Organization: jest.fn(),
}));

// jest.mock('../../../models/organizationConnection', () => ({ // Commented out as file does not exist
//   OrganizationConnection: jest.fn()
// }));

// jest.mock('../../../models/auditLog', () => ({ // Commented out as file does not exist and/or not used
//   AuditLog: jest.fn()
// }));

// jest.mock('../../../models/queue', () => ({ // Commented out as file does not exist and/or not used
//     Queue: {
//         create: jest.fn().mockResolvedValue({}),
//         get: jest.fn().mockResolvedValue(null),
//         update: jest.fn().mockResolvedValue({}),
//         delete: jest.fn().mockResolvedValue({}),
//         where: jest.fn().mockResolvedValue([]),
//         count: jest.fn().mockResolvedValue(0),
//         complete: jest.fn().mockResolvedValue({}),
//         failed: jest.fn().mockResolvedValue({}),
//     }
// }));

// Mock the actual modules to control their output during tests
jest.mock("../../../utils/chats/prompts/legalDrafting", () => ({
  // Keep other exports if systemEndpoints relies on them for other routes, or mock them as needed
  ...jest.requireActual("../../../utils/chats/prompts/legalDrafting"),
  exportedLegalPrompts: [
    {
      promptKey: "DEFAULT_DOCUMENT_SUMMARY",
      promptField: "SYSTEM_PROMPT",
      label: "Test Summary System",
      defaultContent: "Test summary system content.",
      systemSettingName: "cdb_document_summary_system_prompt",
      description: "Test desc for summary system.",
    },
    {
      promptKey: "DEFAULT_DOCUMENT_SUMMARY",
      promptField: "USER_PROMPT",
      label: "Test Summary User",
      defaultContent: "Test summary user content.",
      systemSettingName: "cdb_document_summary_user_prompt",
      description: "Test desc for summary user.",
    },
    // Add more mock prompts if needed to test edge cases or different structures
  ],
}));

jest.mock("../../../utils/chats/helpers/promptManager", () => ({
  ...jest.requireActual("../../../utils/chats/helpers/promptManager"),
  PROMPT_MAPPINGS: {
    summarySystemPrompt: "cdb_document_summary_system_prompt",
    summaryUserPrompt: "cdb_document_summary_user_prompt",
    // Add other mappings if your test prompts require them
  },
}));

const mockRequest = (sessionData = {}, body = {}, query = {}, params = {}) => ({
  session: { ...sessionData },
  body,
  query: { ...query },
  params: { ...params },
  get: jest.fn(),
  header: jest.fn(),
  ip: "127.0.0.1",
  locals: { user: { id: 1, role: "admin", username: "testadmin" } },
});

const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  res.sendStatus = jest.fn().mockReturnValue(res);
  res.end = jest.fn().mockReturnValue(res);
  res.locals = {};
  return res;
};

let app;

describe("System Endpoints - Document Builder Prompts", () => {
  beforeEach(() => {
    app = {
      get: jest.fn(),
      post: jest.fn(),
      delete: jest.fn(),
      put: jest.fn(),
    };
    systemEndpoints(app);
  });

  afterEach(() => {
    jest.clearAllMocks(); // Clear mocks after each test
  });

  describe("GET /system/document-builder-prompts", () => {
    let routeHandler;

    beforeEach(() => {
      const getCall = app.get.mock.calls.find(
        (call) => call[0] === "/system/document-builder-prompts"
      );
      if (getCall && getCall.length > 1 && typeof getCall[1] === "function") {
        routeHandler = getCall[1];
      } else {
        console.error(
          "Route /system/document-builder-prompts not found or handler is not a function.",
          getCall
        );
        routeHandler = async (req, res) =>
          res.status(404).json({ error: "Route not found or misconfigured" });
      }
    });

    test("should return 200 and the list of prompts with camelCaseKeys from mocks", async () => {
      const req = mockRequest();
      const res = mockResponse();

      // Use the mocked version of exportedLegalPrompts and PROMPT_MAPPINGS for this test
      const {
        exportedLegalPrompts,
      } = require("../../../utils/chats/prompts/legalDrafting");
      const {
        PROMPT_MAPPINGS,
      } = require("../../../utils/chats/helpers/promptManager");

      await routeHandler(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          prompts: expect.any(Array),
        })
      );

      const responseJson = res.json.mock.calls[0][0];
      expect(responseJson.prompts.length).toBe(exportedLegalPrompts.length);

      responseJson.prompts.forEach((prompt) => {
        expect(prompt).toHaveProperty("camelCaseKey");
        let expectedCamelKey = null;
        for (const [camelKey, pKey] of Object.entries(PROMPT_MAPPINGS)) {
          if (pKey === prompt.systemSettingName) {
            expectedCamelKey = camelKey;
            break;
          }
        }
        expect(prompt.camelCaseKey).toBe(expectedCamelKey);
        // Ensure all original properties are also present
        const originalMockedPrompt = exportedLegalPrompts.find(
          (p) => p.systemSettingName === prompt.systemSettingName
        );
        expect(prompt.promptKey).toBe(originalMockedPrompt.promptKey);
        expect(prompt.label).toBe(originalMockedPrompt.label);
      });
    });

    test("should handle errors gracefully and return 500 if PROMPT_MAPPINGS is undefined", async () => {
      const req = mockRequest();
      const res = mockResponse();

      jest.resetModules(); // Ensure clean state for this test

      // Mock promptManager for this specific test to make PROMPT_MAPPINGS undefined
      jest.doMock("../../../utils/chats/helpers/promptManager", () => ({
        PROMPT_MAPPINGS: undefined,
        // Assuming no other exports from promptManager are critical for system.js loading
        // or for this specific error path in the route handler.
      }));

      // Mock legalDrafting as system.js requires it at its top level.
      // Provide a minimal valid structure for exportedLegalPrompts.
      jest.doMock("../../../utils/chats/prompts/legalDrafting", () => ({
        exportedLegalPrompts: [], // Default to empty or minimal mock
        // Add other necessary exports from legalDrafting if system.js depends on them globally
      }));

      // IMPORTANT: Require the module under test *after* the mocks are set up
      const {
        systemEndpoints: rewiredSystemEndpoints,
      } = require("../../../endpoints/system");

      const testApp = {
        get: jest.fn(),
        post: jest.fn(),
        delete: jest.fn(),
        put: jest.fn(),
      };
      rewiredSystemEndpoints(testApp); // Register routes with the fresh systemEndpoints

      const rewiredRouteCall = testApp.get.mock.calls.find(
        (call) => call[0] === "/system/document-builder-prompts"
      );
      const rewiredRouteHandler = rewiredRouteCall ? rewiredRouteCall[1] : null;

      if (!rewiredRouteHandler) {
        throw new Error(
          "Route handler for /system/document-builder-prompts was not found after rewiring."
        );
      }

      await rewiredRouteHandler(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: "Failed to fetch document builder prompts.",
        })
      );

      jest.resetModules(); // Clean up for the next test
    });

    test("should return null for camelCaseKey if a systemSettingName is not in PROMPT_MAPPINGS", async () => {
      const req = mockRequest();
      const res = mockResponse();

      jest.resetModules(); // Ensure clean state for this test

      // Explicitly mock promptManager with a defined PROMPT_MAPPINGS for this test
      jest.doMock("../../../utils/chats/helpers/promptManager", () => ({
        ...jest.requireActual("../../../utils/chats/helpers/promptManager"),
        PROMPT_MAPPINGS: {
          summarySystemPrompt: "cdb_document_summary_system_prompt",
          summaryUserPrompt: "cdb_document_summary_user_prompt",
        },
      }));

      // Explicitly define exportedLegalPrompts for this test
      jest.doMock("../../../utils/chats/prompts/legalDrafting", () => ({
        ...jest.requireActual("../../../utils/chats/prompts/legalDrafting"),
        exportedLegalPrompts: [
          {
            promptKey: "DEFAULT_DOCUMENT_SUMMARY",
            promptField: "SYSTEM_PROMPT",
            label: "Test Summary System",
            defaultContent: "Test summary system content.",
            systemSettingName: "cdb_document_summary_system_prompt",
            description: "Test desc for summary system.",
          },
          {
            promptKey: "DEFAULT_DOCUMENT_SUMMARY",
            promptField: "USER_PROMPT",
            label: "Test Summary User",
            defaultContent: "Test summary user content.",
            systemSettingName: "cdb_document_summary_user_prompt",
            description: "Test desc for summary user.",
          },
          {
            promptKey: "UNMAPPED_KEY",
            promptField: "SYSTEM_PROMPT",
            label: "Unmapped Test Prompt",
            defaultContent: "Unmapped content.",
            systemSettingName: "cdb_unmapped_test_system_prompt", // This key is intentionally not in PROMPT_MAPPINGS
            description: "An unmapped test prompt.",
          },
        ],
      }));

      const {
        systemEndpoints: rewiredSystemEndpointsForUnmapped,
      } = require("../../../endpoints/system");

      const testAppUnmapped = {
        get: jest.fn(),
        post: jest.fn(),
        delete: jest.fn(),
        put: jest.fn(),
      };
      rewiredSystemEndpointsForUnmapped(testAppUnmapped);

      const rewiredRouteCallUnmapped = testAppUnmapped.get.mock.calls.find(
        (call) => call[0] === "/system/document-builder-prompts"
      );
      const rewiredRouteHandlerUnmapped = rewiredRouteCallUnmapped
        ? rewiredRouteCallUnmapped[1]
        : null;

      if (!rewiredRouteHandlerUnmapped) {
        throw new Error(
          "Route handler for /system/document-builder-prompts was not found after rewiring for unmapped test."
        );
      }

      await rewiredRouteHandlerUnmapped(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      const responseJson = res.json.mock.calls[0][0];
      const unmappedPrompt = responseJson.prompts.find(
        (p) => p.promptKey === "UNMAPPED_KEY"
      );
      expect(unmappedPrompt).toBeDefined();
      expect(unmappedPrompt.camelCaseKey).toBeNull();

      jest.resetModules(); // Clean up for the next test
    });
  });
});

// Top-level mocks should be defined before any imports that might use them.
const { SystemSettings } = require("../../../models/systemSettings");
jest.mock("../../../models/systemSettings", () => ({
  SystemSettings: {
    getValueOrFallback: jest.fn(),
    updateSettings: jest.fn(),
    isMultiUserMode: jest.fn().mockResolvedValue(true),
    isPublicUserMode: jest.fn().mockResolvedValue(false),
    isDocumentDrafting: jest.fn().mockResolvedValue(true),
    isQura: jest.fn().mockResolvedValue(false),
    get: jest.fn().mockResolvedValue(null),
    _updateSettings: jest.fn().mockResolvedValue({ success: true }),
    isPromptOutputLogging: jest.fn().mockResolvedValue(false),
    isPerformLegalTask: jest
      .fn()
      .mockResolvedValue({ enabled: false, allowUserAccess: false }),
    isFeedbackEnabled: jest.fn().mockResolvedValue({ enabled: false }),
    getCopyOption: jest.fn().mockResolvedValue(null),
    getSystemWebsiteLinkAndText: jest.fn().mockResolvedValue({
      websiteLink: null,
      displayText: "Visit the website",
    }),
    getSystemTabNames: jest
      .fn()
      .mockResolvedValue({ tabName1: null, tabName2: null, tabName3: null }),
    isCitation: jest.fn().mockResolvedValue(false),
    isInvoiceEnabled: jest.fn().mockResolvedValue(false),
    isForcedInvoiceLoggingEnabled: jest.fn().mockResolvedValue(false),
    isRexorLinkageEnabled: jest.fn().mockResolvedValue(false),
    currentLogoLight: jest.fn().mockResolvedValue(null),
    currentLogoDark: jest.fn().mockResolvedValue(null),
    getRequestLegalAssistanceSettings: jest
      .fn()
      .mockResolvedValue({ enabled: false, lawFirmName: "", email: "" }),
    currentSettings: jest.fn().mockResolvedValue({}),
    vectorDBPreferenceKeys: jest.fn().mockReturnValue({}),
    llmPreferenceKeys: jest.fn().mockReturnValue({}),
    hasEmbeddings: jest.fn().mockResolvedValue(false),
  },
}));

// Import the handlers to be tested
const {
  getDocumentBuilderPromptsHandler,
  updateDocumentBuilderPromptsHandler,
} = require("../../../endpoints/system");

// Mock other dependencies of system.js that are not directly part of the handlers but might be at module scope
jest.mock("../../../utils/prisma", () => ({}));
jest.mock("../../../models/vectors", () => ({ DocumentVectors: {} }));
jest.mock("../../../models/organization", () => ({ Organization: jest.fn() }));
jest.mock("multer", () => {
  const multer = jest.fn(() => ({
    single: jest.fn((field) => (req, res, next) => {
      if (next) next();
    }),
    fields: jest.fn((fields) => (req, res, next) => {
      if (next) next();
    }),
  }));
  // diskStorage should be a static method on the multer module itself
  multer.diskStorage = jest.fn((options) => {
    // Return a dummy storage engine object. Its actual methods (e.g., _handleFile, _removeFile)
    // won't be called in these unit tests if we are only testing endpoint logic that uses multer's setup.
    return {
      _handleFile: jest.fn((req, file, cb) =>
        cb(null, { path: "dummy-path", filename: "dummy-filename" })
      ),
      _removeFile: jest.fn((req, file, cb) => cb(null)),
    };
  });
  return multer;
});
// validatedRequest and flexUserRoleValid are not directly called by the handlers themselves,
// but by Express when routing. For direct handler tests, we manually set req.locals.user if needed.

describe("Dynamic Document Builder Prompt Endpoint Handlers", () => {
  let req;
  let res;
  let TestMockedSystemSettings;

  const DYNAMIC_TEST_PROMPTS_CONFIG = [
    {
      promptKey: "TEST_SUMMARY_DYNAMIC",
      promptField: "SYSTEM_PROMPT",
      label: "Dynamic Summary System",
      defaultContent: "Default dynamic summary system content.",
      systemSettingName: "cdb_dyn_summary_system_prompt",
      description: "Test desc dynamic summary system.",
    },
    {
      promptKey: "TEST_RELEVANCE_DYNAMIC",
      promptField: "USER_PROMPT",
      label: "Dynamic Relevance User",
      defaultContent: "Default dynamic relevance user content.",
      systemSettingName: "cdb_dyn_relevance_user_prompt",
      description: "Test desc dynamic relevance user.",
    },
  ];

  beforeEach(() => {
    jest.resetModules();

    jest.mock("../../../models/systemSettings", () => ({
      SystemSettings: {
        getValueOrFallback: jest.fn(),
        updateSettings: jest.fn(),
        isMultiUserMode: jest.fn().mockResolvedValue(true),
        isPublicUserMode: jest.fn().mockResolvedValue(false),
      },
    }));
    TestMockedSystemSettings =
      require("../../../models/systemSettings").SystemSettings;

    jest.doMock("../../../utils/chats/prompts/legalDrafting", () => ({
      ...jest.requireActual("../../../utils/chats/prompts/legalDrafting"),
      exportedLegalPrompts: DYNAMIC_TEST_PROMPTS_CONFIG,
    }));

    req = mockRequest();
    res = mockResponse();

    TestMockedSystemSettings.getValueOrFallback.mockReset();
    TestMockedSystemSettings.updateSettings.mockReset();
  });

  afterAll(() => {
    jest.resetModules();
  });

  describe("getDocumentBuilderPromptsHandler", () => {
    test("should fetch and return enriched prompts as an ARRAY", async () => {
      TestMockedSystemSettings.getValueOrFallback
        .mockResolvedValueOnce("custom dynamic summary system content")
        .mockResolvedValueOnce("custom dynamic relevance user content");

      await getDocumentBuilderPromptsHandler(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      const responseData = res.json.mock.calls[0][0];
      expect(responseData).toBeInstanceOf(Array);
      expect(responseData.length).toBe(DYNAMIC_TEST_PROMPTS_CONFIG.length);

      expect(responseData[0]).toEqual(
        expect.objectContaining({
          ...DYNAMIC_TEST_PROMPTS_CONFIG[0],
          currentValue: "custom dynamic summary system content",
        })
      );
      expect(responseData[1]).toEqual(
        expect.objectContaining({
          ...DYNAMIC_TEST_PROMPTS_CONFIG[1],
          currentValue: "custom dynamic relevance user content",
        })
      );

      expect(TestMockedSystemSettings.getValueOrFallback).toHaveBeenCalledTimes(
        2
      );
      expect(TestMockedSystemSettings.getValueOrFallback).toHaveBeenCalledWith(
        { label: "cdb_dyn_summary_system_prompt" },
        ""
      );
      expect(TestMockedSystemSettings.getValueOrFallback).toHaveBeenCalledWith(
        { label: "cdb_dyn_relevance_user_prompt" },
        ""
      );
    });

    test("should handle errors from SystemSettings.getValueOrFallback", async () => {
      TestMockedSystemSettings.getValueOrFallback.mockRejectedValue(
        new Error("DB error dynamic")
      );
      await getDocumentBuilderPromptsHandler(req, res);
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: "DB error dynamic",
      });
    });
  });

  describe("updateDocumentBuilderPromptsHandler", () => {
    test("should update valid prompts and return success", async () => {
      const validUpdates = {
        cdb_dyn_summary_system_prompt: "new dynamic summary system",
        cdb_dyn_relevance_user_prompt: "new dynamic relevance user",
      };
      req.body = validUpdates;
      TestMockedSystemSettings.updateSettings.mockResolvedValue({
        success: true,
      });

      await updateDocumentBuilderPromptsHandler(req, res);
      expect(TestMockedSystemSettings.updateSettings).toHaveBeenCalledWith(
        validUpdates
      );
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: "Document builder prompts updated successfully.",
      });
    });

    test("should filter out invalid prompt keys", async () => {
      const mixedUpdates = {
        cdb_dyn_summary_system_prompt: "new dynamic summary system",
        invalid_key: "some value",
        cdb_dyn_relevance_user_prompt: "new dynamic relevance user",
      };
      const expectedFilteredUpdates = {
        cdb_dyn_summary_system_prompt: "new dynamic summary system",
        cdb_dyn_relevance_user_prompt: "new dynamic relevance user",
      };
      req.body = mixedUpdates;
      TestMockedSystemSettings.updateSettings.mockResolvedValue({
        success: true,
      });
      await updateDocumentBuilderPromptsHandler(req, res);
      expect(TestMockedSystemSettings.updateSettings).toHaveBeenCalledWith(
        expectedFilteredUpdates
      );
    });

    test("should return 400 if only invalid keys provided", async () => {
      req.body = { invalid_key1: "value1" };
      await updateDocumentBuilderPromptsHandler(req, res);
      expect(TestMockedSystemSettings.updateSettings).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: "No valid prompt settings provided for update.",
      });
    });

    test("should return 200 if body is empty", async () => {
      req.body = {};
      await updateDocumentBuilderPromptsHandler(req, res);
      expect(TestMockedSystemSettings.updateSettings).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: "No settings provided for update.",
      });
    });

    test("should return 400 if SystemSettings.updateSettings fails", async () => {
      req.body = { cdb_dyn_summary_system_prompt: "any" };
      TestMockedSystemSettings.updateSettings.mockResolvedValue({
        success: false,
        error: "DB update error dynamic",
      });
      await updateDocumentBuilderPromptsHandler(req, res);
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: "DB update error dynamic",
      });
    });

    test("should handle general errors during processing", async () => {
      req.body = { cdb_dyn_summary_system_prompt: "any" };
      TestMockedSystemSettings.updateSettings.mockRejectedValue(
        new Error("Unexpected error dynamic")
      );
      await updateDocumentBuilderPromptsHandler(req, res);
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: "Unexpected error dynamic",
      });
    });
  });
});
