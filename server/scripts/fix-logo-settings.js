#!/usr/bin/env node

/**
 * Logo Settings Migration Script
 *
 * This script fixes logo database settings to ensure proper logo loading.
 * It updates the dark logo setting to use the correct filename.
 *
 * Run with: node server/scripts/fix-logo-settings.js
 */

const path = require("path");
const fs = require("fs");

// Set up environment
process.env.NODE_ENV = process.env.NODE_ENV || "development";
require("dotenv").config({ path: `.env.${process.env.NODE_ENV}` });

async function fixLogoSettings() {
  try {
    console.log("🔧 Starting logo settings migration...");

    // Import SystemSettings after environment is set up
    const { SystemSettings } = require("../models/systemSettings");

    // Check current settings
    const currentLightLogo = await SystemSettings.currentLogoLight();
    const currentDarkLogo = await SystemSettings.currentLogoDark();

    console.log("📋 Current logo settings:");
    console.log(`  Light logo: ${currentLightLogo || "null"}`);
    console.log(`  Dark logo: ${currentDarkLogo || "null"}`);

    // Check if assets directory exists
    const assetsDir = process.env.STORAGE_DIR
      ? path.join(process.env.STORAGE_DIR, "assets")
      : path.join(__dirname, "../storage/assets");

    if (!fs.existsSync(assetsDir)) {
      console.log("📁 Creating assets directory...");
      fs.mkdirSync(assetsDir, { recursive: true });
    }

    // Check if logo files exist
    const lightLogoPath = path.join(assetsDir, "ISTLogo.png");
    const darkLogoPath = path.join(assetsDir, "logo-dark.png");

    console.log("🔍 Checking logo files:");
    console.log(`  Light logo exists: ${fs.existsSync(lightLogoPath)}`);
    console.log(`  Dark logo exists: ${fs.existsSync(darkLogoPath)}`);

    // Update dark logo setting if needed
    if (currentDarkLogo === "ISTLogo.png" && fs.existsSync(darkLogoPath)) {
      console.log("🔄 Updating dark logo setting to use logo-dark.png...");
      await SystemSettings.updateSettings({ logo_dark: "logo-dark.png" });
      console.log("✅ Dark logo setting updated successfully");
    } else if (currentDarkLogo === "logo-dark.png") {
      console.log("✅ Dark logo setting is already correct");
    } else {
      console.log(
        "ℹ️  Dark logo setting unchanged (custom configuration detected)"
      );
    }

    // Verify final settings
    const finalLightLogo = await SystemSettings.currentLogoLight();
    const finalDarkLogo = await SystemSettings.currentLogoDark();

    console.log("📋 Final logo settings:");
    console.log(`  Light logo: ${finalLightLogo || "null"}`);
    console.log(`  Dark logo: ${finalDarkLogo || "null"}`);

    console.log("🎉 Logo settings migration completed successfully!");
  } catch (error) {
    console.error("❌ Error during logo settings migration:", error);
    process.exit(1);
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  fixLogoSettings()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error("❌ Migration failed:", error);
      process.exit(1);
    });
}

module.exports = { fixLogoSettings };
