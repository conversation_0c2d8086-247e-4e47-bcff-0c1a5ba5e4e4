import PreLoader from "@/components/Preloader";
import Workspace from "@/models/workspace";
import showToast from "@/utils/toast";
import { useEffect, useState } from "react";
import { Plus, X } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";

export default function SuggestedChatMessages({ slug }) {
  const { t } = useTranslation();
  const [suggestedMessages, setSuggestedMessages] = useState([]);
  const [editingIndex, setEditingIndex] = useState(-1);
  const [newMessage, setNewMessage] = useState({ heading: "", message: "" });
  const [hasChanges, setHasChanges] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchWorkspace() {
      if (!slug) return;
      const suggestedMessages = await Workspace.getSuggestedMessages(slug);
      setSuggestedMessages(suggestedMessages);
      setLoading(false);
    }
    fetchWorkspace();
  }, [slug]);

  const handleSaveSuggestedMessages = async () => {
    const validMessages = suggestedMessages.filter(
      (msg) =>
        msg?.heading?.trim()?.length > 0 || msg?.message?.trim()?.length > 0
    );
    const { success, error } = await Workspace.setSuggestedMessages(
      slug,
      validMessages
    );
    if (!success) {
      showToast(t("toast.welcome-messages.update-failed", { error }), "error");
      return;
    }
    showToast(t("show-toast.updated-welcome"), "success");
    setHasChanges(false);
  };

  const addMessage = () => {
    setEditingIndex(-1);
    if (suggestedMessages.length >= 4) {
      showToast(t("show-toast.maximum-messages"), "warning");
      return;
    }
    const defaultMessage = {
      heading: t("general.message.heading"),
      message: t("general.message.body"),
    };
    setNewMessage(defaultMessage);
    setSuggestedMessages([...suggestedMessages, { ...defaultMessage }]);
    setHasChanges(true);
  };

  const removeMessage = (index) => {
    const messages = [...suggestedMessages];
    messages.splice(index, 1);
    setSuggestedMessages(messages);
    setHasChanges(true);
  };

  const startEditing = (e, index) => {
    e.preventDefault();
    setEditingIndex(index);
    setNewMessage({ ...suggestedMessages[index] });
  };

  const handleRemoveMessage = (index) => {
    removeMessage(index);
    setEditingIndex(-1);
  };

  const onEditChange = (e) => {
    const updatedNewMessage = {
      ...newMessage,
      [e.target.name]: e.target.value,
    };
    setNewMessage(updatedNewMessage);
    const updatedMessages = suggestedMessages.map((message, index) => {
      if (index === editingIndex) {
        return { ...message, [e.target.name]: e.target.value };
      }
      return message;
    });

    setSuggestedMessages(updatedMessages);
    setHasChanges(true);
  };

  if (loading) {
    return (
      <div className="flex flex-col">
        <label className="block input-label">
          {t("general.message.title")}
        </label>
        <div className="text-foreground text-opacity-60 text-xs font-medium py-1.5">
          {t("general.message.description")}
        </div>
        <div className="text-foreground text-opacity-60 text-sm font-medium mt-6">
          <PreLoader size="4" />
        </div>
      </div>
    );
  }

  return (
    <div className="pb-2 border-top">
      <div className="flex flex-col">
        <label className="block input-label text-foreground">
          {t("general.message.title")}
        </label>
        <div className="text-foreground text-opacity-60 text-xs font-medium py-1.5">
          {t("general.message.description")}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-foreground text-xs mt-4 w-full justify-center max-w-[600px]">
        {suggestedMessages.map((suggestion, index) => (
          <div key={index} className="relative w-full">
            <button
              className="transition-all duration-300 absolute z-10 text-white primary-bg rounded-md ml-2"
              style={{
                top: -8,
                left: 265,
              }}
              onClick={() => handleRemoveMessage(index)}
            >
              <X className="m-[1px]" size={20} />
            </button>
            <button
              key={index}
              onClick={(e) => startEditing(e, index)}
              className={`text-left p-2.5 rounded-xl w-full border-2 border-border hover:border-primary mb-4 ${
                editingIndex === index ? "border-primary" : ""
              }`}
            >
              <div className="font-semibold">{suggestion.heading}</div>
              <div>{suggestion.message}</div>
            </button>
          </div>
        ))}
      </div>

      {editingIndex >= 0 && (
        <div className="flex flex-col gap-y-4 mr-2 mt-8">
          <div className="w-1/2">
            <label className="text-foreground text-sm font-semibold block mb-2">
              {t("general.message.new-heading")}
            </label>
            <input
              placeholder="Message heading"
              value={newMessage.heading}
              name="heading"
              onChange={onEditChange}
              className="dark-input-mdl w-full text-foreground block p-2.5"
            />
          </div>
          <div className="w-1/2">
            <label className="text-foreground text-sm font-semibold block mb-2">
              {t("general.message.message")}
            </label>
            <input
              placeholder="Message"
              onChange={onEditChange}
              value={newMessage.message}
              name="message"
              className="dark-input-mdl w-full text-foreground block p-2.5"
            />
          </div>
        </div>
      )}

      {suggestedMessages.length < 4 && (
        <button
          type="button"
          onClick={addMessage}
          className="flex gap-x-2 items-center justify-center px-2 py-1 rounded-md primary-o-btn text-foreground font-normal text-sm"
        >
          {t("general.message.add")}{" "}
          <Plus className="" size={24} weight="fill" />
        </button>
      )}

      {hasChanges && (
        <div className="flex justify-start mt-6">
          <button
            type="button"
            className="primary-bg text-white transition-all duration-300 px-4 py-2 rounded-lg text-sm items-center flex gap-x-2"
            onClick={handleSaveSuggestedMessages}
          >
            {t("general.message.save")}
          </button>
        </div>
      )}
    </div>
  );
}
