export default {
  // =========================
  // UPLOAD MODAL LEGALQ&A
  // =========================
  documents: {
    "pin-info-button": "About pinning",
    "pin-title": "What is document pinning?",
    "pin-desc-1":
      "When you pin a document the platform we will inject the entire content of the document into your prompt window for your LLM to fully comprehend.",
    "pin-desc-2":
      "This works best with large-context models or small files that are critical to its knowledge-base.",
    "pin-desc-3":
      "If you are not getting the answers you desire by default then pinning is a great way to get higher quality answers in a click.",
    "pin-add": "Pin to workspace",
    "pin-unpin": "Un-Pin from workspace",
    "remove-document": "Remove document from workspace",
    "remove-folder": "Remove folder and all contained files",
    "document-removed": "Document removed from workspace",
    "folder-removed": "Folder and all contained files removed",
    "for-all-files-in-folder": "for all files in folder",

    "star-title": "What is document starring?",
    "star-desc-1":
      "When you star a document, it will receive a similarity boost in vector searches.",
    "star-desc-2":
      "This helps ensure that content from starred documents is more likely to be included in responses.",
    "star-desc-3":
      "Use this feature for important documents that should have higher priority in search results.",
    "star-mark": "Star mark document",
    "star-unmark": "Remove star mark",
    "star-added": "Document has been star marked",
    "star-removed": "Star mark removed from document",
    "star-failed": "Failed to update star status",
    "requires-metadata-update":
      "Document requires metadata update before it can be starred",
    "metadata-update-title": "Metadata Update Required",
    "metadata-update-confirm":
      "This document needs a metadata update before it can be starred. This process only updates metadata and does not re-embed content. Would you like to update it now?",
    "metadata-update-success": "Document metadata successfully updated",
    "metadata-update-failed": "Failed to update document metadata",
    "metadata-update-confirm-button": "Update Metadata",
    "metadata-update-inprogress": "Updating document metadata...",
    "copy-citation": "Copy Citation",
    "copy-citations": "Copy Citations",
    "open-link": "Open Link",
    "open-source": "Open Source Document",
    "open-link-new-tab": "Open Link in new tab",
    "stop-watching-confirm":
      "Are you sure you want to stop watching this document for changes?",
    "unknown-error": "Unknown error occurred",
    "watch-title": "What does watching a document do?",
    "watch-desc-1":
      "When you watch a document we will automatically sync your document content from its original source on regular intervals. This will automatically update the content in every workspace where this file is managed.",
    "watch-desc-2":
      "This feature currently supports online-based content and will not be available for manually uploaded documents.",
    "watch-desc-3": "You can manage what documents are watched from the",
    "file-manager": "File manager",
    "admin-view": "admin view",
    "pdr-add": "Added all documents to Parent Document Retrieval",
    "pdr-remove": "Removed all documents from Parent Document Retrieval",
    empty: "No documents found",
    tooltip: { date: "Date: ", type: "Type: ", cached: "Cached" },
    actions: {
      removing: "Removing file from workspace",
      "removing-folder":
        "Removing folder and all contained files from workspace",
      "removing-folder-contents": "Removing all files in folder from workspace",
    },
    costs: { estimate: "Estimated Cost: $", minimum: "< $0.01" },
    "new-folder": {
      title: "Create New Folder",
      "name-label": "Folder Name",
      "name-placeholder": "Enter folder name",
      create: "Create Folder",
    },
    error: {
      "create-folder": "Failed to create folder",
      "remove-folder": "Failed to remove folder",
      "remove-generic": "Failed to remove item",
    },
  },
};
