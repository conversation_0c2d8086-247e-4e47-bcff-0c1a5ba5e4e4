import { useState, useEffect } from "react";
import {
  loginToRexor,
  registerProject,
  writeArticleTransaction,
  updateArticleTransaction,
} from "@/services/rexorService";
import { encodePassword, decodePassword } from "@/utils/encryption";
import { useTranslation } from "react-i18next";
import showToast from "@/utils/toast";

export default function useRexor() {
  const { t } = useTranslation();

  const [rexorToken, setRexorToken] = useState(
    () => localStorage.getItem("rexor_token") || null
  );
  const [isLoggedIn, setIsLoggedIn] = useState(!!rexorToken);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const logout = () => {
    localStorage.removeItem("rexor_token");
    localStorage.removeItem("rexor_credentials");
    localStorage.removeItem("activeReference");
    setError(null);
    setSavedCredentials(null);
    setRexorToken(null);
    setIsLoggedIn(false);
  };

  const isAuthorizationError = (error) => {
    const errorMsg = error.message || "";
    const errorResponse = error.response?.data?.error || "";

    if (errorMsg.includes("401") || errorMsg.includes("403")) {
      return true;
    }

    const isAuthError =
      errorMsg.includes("Unauthorized") ||
      errorMsg.includes("Token might be invalid") ||
      errorMsg.includes("Authorization has been denied") ||
      errorMsg.includes("Authentication failed") ||
      errorResponse.includes("Unauthorized") ||
      errorResponse.includes("Authorization has been denied") ||
      errorResponse.includes("Authentication failed");

    return isAuthError;
  };

  const handleAuthError = (error, setShowLoginModal) => {
    logout();
    showToast(t("rexor.auth-error"), error.message);

    if (setShowLoginModal) {
      setShowLoginModal(true);
    }
  };

  const [savedCredentials, setSavedCredentials] = useState(() => {
    const saved = localStorage.getItem("rexor_credentials");
    if (!saved) return null;
    const creds = JSON.parse(saved);
    return {
      username: creds.username,
      password: decodePassword(creds.password),
    };
  });

  useEffect(() => {
    setIsLoggedIn(!!rexorToken);
  }, [rexorToken]);

  useEffect(() => {
    const handleStorageChange = () => {
      const token = localStorage.getItem("rexor_token");
      setRexorToken(token);
      setIsLoggedIn(!!token);
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []);

  const checkLoginStatus = () => {
    const token = localStorage.getItem("rexor_token");
    setRexorToken(token);
    setIsLoggedIn(!!token);
  };

  const login = async (username, password) => {
    setLoading(true);
    try {
      setError(null);
      const data = await loginToRexor(username, password);

      if (data.success && data.access_token) {
        localStorage.setItem("rexor_token", data.access_token);
        localStorage.setItem(
          "rexor_credentials",
          JSON.stringify({
            username,
            password: encodePassword(password),
          })
        );
        setSavedCredentials({ username, password });
        setRexorToken(data.access_token);
        setIsLoggedIn(true);
        return data.access_token;
      } else {
        setError(t("rexor.account.no-token"));
        return null;
      }
    } catch (err) {
      setError(err.message);
      setIsLoggedIn(false);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const registerNewProject = async (projectData) => {
    if (!rexorToken) {
      throw new Error(t("rexor.account.no-user"));
    }
    try {
      const projectResponse = await registerProject(projectData, rexorToken);
      return projectResponse;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const writeNewArticleTransaction = async (
    transactionData,
    setShowLoginModal
  ) => {
    if (!rexorToken) {
      throw new Error("User is not logged in");
    }
    try {
      const transactionResponse = await writeArticleTransaction(
        transactionData,
        rexorToken
      );
      localStorage.setItem(
        "rexor_transaction_uid",
        JSON.stringify(transactionResponse.UID)
      );
      return transactionResponse;
    } catch (err) {
      setError(err.message);

      if (isAuthorizationError(err)) {
        handleAuthError(err, setShowLoginModal);
        return null;
      }

      throw err;
    }
  };

  const updateTransactionForachPrompt = async (
    transactionData,
    setShowLoginModal
  ) => {
    if (!rexorToken) {
      console.log("No Rexor token available");
      return null;
    }

    try {
      const result = await updateArticleTransaction(
        transactionData,
        rexorToken
      );
      return result;
    } catch (err) {
      setError(err.message);

      if (isAuthorizationError(err)) {
        handleAuthError(err, setShowLoginModal);
        return null;
      }

      return null;
    }
  };

  const writeOrUpdateTransaction = async (setShowLoginModal) => {
    try {
      const projectDetailsString = localStorage.getItem(
        "rexor_registered_project"
      );

      if (!projectDetailsString) {
        console.log("No registered project available");
        return;
      }

      const projectDetails = JSON.parse(projectDetailsString);

      if (!isLoggedIn) {
        if (setShowLoginModal) {
          setShowLoginModal(true);
        }
        return;
      }

      if (!projectDetails.UID) {
        return await writeNewArticleTransaction(
          projectDetails,
          setShowLoginModal
        );
      } else {
        return await updateTransactionForachPrompt(
          projectDetails,
          setShowLoginModal
        );
      }
    } catch (error) {
      console.error("Error in writeOrUpdateTransaction:", error);
      const errorMsg = error.message || "";
      const errorResponse = error.response?.data?.error || "";
      setError(errorMsg || errorResponse);

      if (isAuthorizationError(error)) {
        handleAuthError(error, setShowLoginModal);
      }

      return null;
    }
  };

  return {
    isLoggedIn,
    loading,
    error,
    login,
    logout,
    savedCredentials,
    checkLoginStatus,
    registerNewProject,
    writeNewArticleTransaction,
    updateTransactionForachPrompt,
    writeOrUpdateTransaction,
  };
}
