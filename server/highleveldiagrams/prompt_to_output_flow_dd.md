```mermaid
graph TD
    A["Request In (message, workspace, user, etc.)"] --> B{Initiate streamChatWithWorkspaceDD};
    B --> C{Generate UUID, Check Commands/Agents};
    C --> D{"Determine LLM Provider & Model (settings_suffix: _DD or _DD_2)"};
    D --> E[Initialize Main LLMConnector];
    E --> F{"Fetch DD Settings (Vector/Memo/Base Enabled, Token Limits, Linking)"};
    F --> G{Linked Workspaces?};
    G -- Yes --> H{Process Linked Workspaces};
    H --> H1[For each Linked Workspace];
    H1 -- If ddMemoEnabled --> H2{"Generate Legal Memo (LLM Call)"};
    H2 --> H3[Accumulate Memo Results & Tokens];
    H1 -- If ddVectorEnabled AND No Memos --> H4{Vector Search Linked Workspace};
    H4 --> H5[Accumulate Vector Results & Tokens];
    H3 --> H6[Adjust Available Tokens];
    H5 --> H6;
    G -- No --> H6;

    H6 --> I{"Perform Base Legal Issues Analysis (Optional LLM Call if ddBaseEnabled)"};
    I --> J["Calculate Max Chunk Content Size based on Model Window & Overhead"];
    J --> K{Read Documents from Workspace Storage};
    K --> L{"Chunk Documents (with Overlap if needed)"};
    L --> M{Process Document Chunks};
    M -- For Each Chunk --> N["Compress Messages (System, User, Memo Results, Chunk Content, History, Attachments)"];
    N --> O{"LLM Call for Chunk (Streaming or Regular)"};
    O --> P[Collect Partial Responses];
    P --> M;
    M -- All Chunks Processed --> Q{Combine Partial Responses};
    Q -- If Multiple Responses --> R["Format Responses & Check if Hierarchical Combination Needed"];
    R -- Yes (Large Response Set) --> S{"Hierarchical Combination (Batched LLM Calls)"};
    S --> T["Final LLM Call to Synthesize Intermediate Combinations (Streaming)"];
    R -- No --> U["Standard Combination (Single LLM Call - Streaming if possible)"];
    Q -- If Single Chunk or No Chunks --> V["Direct LLM Call (Streaming if possible)"];

    T --> W[Capture Final Combined Text & Metrics];
    U --> W;
    V --> W;

    W --> X{Create/Update WorkspaceChat Entry};
    X --> Y{"Save Chat Logs (if enabled)"};
    Y --> Z["Send finalizeResponseStream to Client (with Progress Updates throughout)"];
    Z --> AA[End Request];

    subgraph ErrorHandling
        Err1[Catch LLM Errors/Rate Limits]
        Err1 --> ErrEnd[Send Abort Message & Exit]
    end

    C -.-> |Agent Flow| AA;
    C -.-> |Command Flow| AA;
    O -.-> |LLM Error| ErrEnd;
    T -.-> |LLM Error| ErrEnd;
    U -.-> |LLM Error| ErrEnd;
    V -.-> |LLM Error| ErrEnd;
```
