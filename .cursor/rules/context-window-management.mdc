---
description: Guidelines for vector fetching and context window management
globs: server/utils/chats/**/*.js,server/utils/helpers/chat/**/*.js
alwaysApply: false
---

# Vector Fetching and Context Window Management

## Vector Fetching Process

### 1. Initial Checks

Before fetching vectors, the system performs several validation checks:

- Verifies if the workspace has vectorized space (`hasVectorizedSpace`)
- Checks the total embeddings count (`namespaceCount`)
- Validates if the chat mode is compatible with the available data

### 2. Pinned Documents

The system first handles pinned documents through `DocumentManager`:

```javascript
await new DocumentManager({
    workspace,
    maxTokens: LLMConnector.promptWindowLimit(),
}).pinnedDocs()
```

These documents are given priority in the context window and are tracked using:

- `pinnedDocIdentifiers`: To prevent duplicates
- `contextTexts`: For the actual content
- `sources`: For metadata and truncated content

### 3. Vector Search

If embeddings exist, the system performs a similarity search using `VectorDb.performSimilaritySearch` with the following parameters:

- `namespace`: Workspace slug
- `input`: User message
- `similarityThreshold`: Workspace-defined threshold
- `topN`: Number of results to return
- `filterIdentifiers`: Pinned document IDs to prevent duplicates
- `rerank`: Optional reranking based on workspace settings

## Context Window Management

### 1. Window Size Calculation

The context window is managed based on the LLM model's limits:

- System prompt: Maximum 15% of token capacity
- History: Maximum 15% of token capacity
- User prompt: Maximum 70% of token capacity
- Response buffer: 600 tokens reserved

### 2. Message Array Compression

The system uses a "cannonball" approach for compression through `messageArrayCompressor`:

1. **System Message Handling**:
   - Splits system prompt and context
   - Compresses if exceeding limits
   - Maintains 25% for prompt, 75% for context

2. **User Prompt Handling**:
   - Given priority in the window
   - Can "hijack" entire thread if needed
   - Limited to 70% of window size

3. **History Compression**:
   - Most aggressive compression
   - Prioritizes recent messages
   - Limited to 15% of window

## Source Management

The `fillSourceWindow` function manages source context with the following priority:

1. **Pinned Documents**:
   - Always included first
   - Cannot be displaced by other sources

2. **Vector Search Results**:
   - Added after pinned documents
   - Filtered to prevent duplicates
   - Limited by workspace settings

3. **Historical Sources**:
   - Used for context continuity
   - Added when vector search yields insufficient results
   - Prioritizes recent chat history

### Source Window Configuration

```javascript
{
    nDocs: 4,              // Default number of documents
    searchResults: [],     // Vector search results
    history: [],           // Raw chat history
    filterIdentifiers: [], // Pinned document identifiers
}
```

## Message Compression

The system uses a unique "cannonball" compression technique:

### 1. Cannonball Algorithm

- Deletes content from middle-out bi-directionally
- Preserves start and end context
- Used when content exceeds token limits

### 2. Compression Triggers

- Very large user prompts (>70% of window)
- Exceeded context window in regular use
- System prompts exceeding 15% allocation
- History exceeding 15% allocation

### 3. Compression Priority

1. History (most aggressive compression)
2. System prompt (moderate compression)
3. User prompt (least compression)

## Dynamic Context Window Management

### Global System Settings

The system provides a configurable setting to control what percentage of the LLM's context window can be used for additional sources:

```javascript
// In system settings
const contextWindowPercentage = await SystemSettings.getDynamicContextSettings();
// Default is 70% if not configured
```

This setting allows administrators to:

- Control how much of the context window is available for enrichment
- Balance between core content and additional sources
- Adjust based on specific LLM provider characteristics

### Context Window Allocation

- PDR and pinned documents share allocation within the dynamic context window percentage
- The allocation is managed through the `maxTokens` parameter in `DocumentManager`:

  ```javascript
  constructor({ workspace = null, maxTokens = null }) {
    this.maxTokens = maxTokens || Number.POSITIVE_INFINITY;
  }
  ```

### Token Management for PDR

1. **Token Tracking**:

   ```javascript
   let tokens = 0;
   const pdrDocs = [];
   // Add documents until token limit is reached
   if (tokens >= this.maxTokens) {
     continue;
   }
   tokens += data.token_count_estimate || 0;
   ```

2. **Priority Order**:
   - Pinned documents are processed first
   - PDR documents are added next
   - Remaining space is used for vector search results

3. **Document Selection**:
   - In global PDR mode: All workspace documents are eligible
   - In standard mode: Only documents marked with `pdr: true`
   - Documents from linked workspaces are included based on mode

4. **Context Window Distribution with Dynamic Percentages**:
   - System calculates available tokens: `promptWindowLimit * (contextWindowPercentage / 100)`
   - This available token space is then distributed across different content types
   - Custom AI users can adjust their own context window percentage to optimize for their specific use cases
   - System vs. User vs. Attachment allocations all work within this total available token space

5. **Compression Behavior**:
   - If content exceeds the calculated available tokens, compression is applied
   - The system tries to preserve critical content while reducing less important information
   - Dynamic context percentages help balance between including enough context and avoiding excessive compression

## Context Backfilling

Backfilling is a smart context management feature that maintains conversation coherence by reusing relevant context from chat history.

### 1. How Backfilling Works

```javascript
function fillSourceWindow({
  nDocs = 4,           // Target number of documents
  searchResults = [],  // Current vector search results
  history = [],        // Chat history
  filterIdentifiers = [] // To prevent duplicates
})
```

1. **Initial Process**:
   - System first uses current vector search results
   - If results < `nDocs`, backfilling begins
   - Looks through recent chat history (default: 20 messages)
   - Adds previously used sources until `nDocs` is reached

2. **Priority Order**:

   ```javascript
   const sources = [...searchResults];  // First priority
   // Then backfill from history if needed
   for (const chat of history.reverse()) {
     if (sources.length >= nDocs) break;
     // Add relevant historical sources
   }
   ```

### 2. Use Cases and Benefits

1. **Follow-up Questions**:

   ```plaintext
   User: "What is IST Legal?"  -> 4 relevant sources
   User: "Tell me its features"   -> 1 new source + 3 backfilled
   ```

   - Maintains context continuity
   - Improves response quality
   - Handles topic transitions

2. **Zero-Result Queries**:
   - Prevents empty context windows
   - Maintains conversation flow
   - Uses relevant historical context
