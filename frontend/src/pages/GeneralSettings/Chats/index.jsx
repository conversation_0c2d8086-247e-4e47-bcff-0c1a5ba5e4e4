import { useEffect, useRef, useState, useC<PERSON>back, useMemo } from "react";
import Sidebar from "@/components/SettingsSidebar";
import * as Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import useQuery from "@/hooks/useQuery";
import ChatRow from "./ChatRow";
import showToast from "@/utils/toast";
import System from "@/models/system";
import {
  ArrowLeft,
  ArrowRight,
  CaretDown,
  Download,
  TrashSimple,
  Info,
  Database,
  FunnelSimple,
  X,
} from "@phosphor-icons/react";
import { saveAs } from "file-saver";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import paths from "@/utils/paths";
import { Button } from "@/components/Button";

const exportOptions = {
  csv: {
    name: "CSV",
    mimeType: "text/csv",
    fileExtension: "csv",
    filenameFunc: () => {
      return `istLegal-chats-${new Date().toLocaleDateString()}`;
    },
  },
  json: {
    name: "JSO<PERSON>",
    mimeType: "application/json",
    fileExtension: "json",
    filenameFunc: () => {
      return `istLegal-chats-${new Date().toLocaleDateString()}`;
    },
  },
  jsonl: {
    name: "JSONL",
    mimeType: "application/jsonl",
    fileExtension: "jsonl",
    filenameFunc: () => {
      return `istLegal-chats-${new Date().toLocaleDateString()}-lines`;
    },
  },
  jsonAlpaca: {
    name: "JSON (Alpaca)",
    mimeType: "application/json",
    fileExtension: "json",
    filenameFunc: () => {
      return `istLegal-chats-${new Date().toLocaleDateString()}-alpaca`;
    },
  },
};

export default function WorkspaceChats() {
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef();
  const openMenuButton = useRef();
  const query = useQuery();
  const [loading, setLoading] = useState(true);
  const [chats, setChats] = useState([]);
  const [filteredChats, setFilteredChats] = useState([]);
  const [offset, setOffset] = useState(Number(query.get("offset") || 0));
  const [canNext, setCanNext] = useState(false);
  const { t } = useTranslation();
  const [userFilter, setUserFilter] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [invoiceRef, setInvoiceRef] = useState("");
  const [totalChatsCount, setTotalChatsCount] = useState(0);
  const [isFiltering, setIsFiltering] = useState(false);

  const filters = useMemo(() => {
    const filterObject = {};
    if (userFilter) filterObject.user = userFilter;
    if (startDate) filterObject.startDate = startDate;
    if (endDate) filterObject.endDate = endDate;
    if (invoiceRef) filterObject.referenceNumber = invoiceRef;
    return filterObject;
  }, [userFilter, startDate, endDate, invoiceRef]);

  // Fetch the total chat count from the server, independent of filters
  const fetchTotalCount = useCallback(async () => {
    try {
      const totalCount = await System.totalChatCount();
      setTotalChatsCount(totalCount);
    } catch (error) {
      console.error("Error fetching total chat count:", error);
      setTotalChatsCount(0);
    }
  }, []);

  // Fetch chats based on current offset and filters
  const fetchChats = useCallback(async () => {
    setLoading(true);
    try {
      let allChats = [];
      let currentOffset = 0;
      let hasMorePages = true;

      // If we're filtering, fetch all pages
      if (isFiltering) {
        while (hasMorePages) {
          const { chats: _chats, hasPages = false } = await System.chats(
            currentOffset,
            filters
          );
          allChats = [...allChats, ...(_chats || [])];
          hasMorePages = hasPages;
          currentOffset++;
        }
        setChats(allChats);
        setFilteredChats(allChats);
        setCanNext(false); // No more pages when showing all filtered results
      } else {
        // Normal pagination when not filtering
        const { chats: _chats, hasPages = false } = await System.chats(
          offset,
          filters
        );
        setChats(_chats || []);
        setFilteredChats(_chats || []);
        setCanNext(hasPages);
      }
    } catch (error) {
      console.error("Error fetching chats:", error);
      setChats([]);
      setFilteredChats([]);
      setCanNext(false);
    } finally {
      setLoading(false);
    }
  }, [offset, filters, isFiltering]);

  // Initial data loading
  useEffect(() => {
    fetchTotalCount(); // Fetch total count once when component mounts
  }, [fetchTotalCount]);

  // Apply client-side filtering when filter inputs change
  useEffect(() => {
    // Track if we are filtering
    setIsFiltering(!!userFilter || !!startDate || !!endDate || !!invoiceRef);

    // Reset to first page when filters change - this ensures
    // we get fresh filtered results from the server
    setOffset(0);
    // Note: We rely entirely on server-side filtering, which is passed via
    // the filters object in the fetchChats call
  }, [userFilter, startDate, endDate, invoiceRef]);

  // Fetch filtered chats when offset or filters change
  useEffect(() => {
    fetchChats();
  }, [fetchChats, offset]);

  const handleDumpChats = async (exportType) => {
    const exported = await System.exportChats(exportType, "workspace", filters);
    if (exported) {
      const { name, mimeType, fileExtension, filenameFunc } =
        exportOptions[exportType];
      const blob = new Blob([exported], { type: mimeType });
      saveAs(blob, `${filenameFunc()}.${fileExtension}`);
      showToast(t("show-toast.chats-exported", { name: name }), "success");
    } else {
      showToast(t("show-toast.failed-chats-export"), "error");
    }
  };

  const toggleMenu = () => {
    setShowMenu(!showMenu);
  };

  useEffect(() => {
    function handleClickOutside(event) {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        !openMenuButton.current.contains(event.target)
      ) {
        setShowMenu(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Add a reset filter function
  const resetFilters = () => {
    setUserFilter("");
    setStartDate("");
    setEndDate("");
    setInvoiceRef("");
    // Filters will be automatically cleared based on the above state changes
  };

  // Listen for refresh events triggered by the chat deletion panel
  useEffect(() => {
    const refreshHandler = () => {
      fetchChats();
    };

    window.addEventListener("refresh-chats", refreshHandler);

    return () => {
      window.removeEventListener("refresh-chats", refreshHandler);
    };
  }, [fetchChats]);

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="relative rounded-md w-full h-full overflow-y-scroll">
          <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-2">
            <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-4 border-bottom">
              <Link to={paths.home()}>
                <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
              </Link>
              <div className="flex flex-col w-full">
                <div>
                  <p className="text-lg leading-6 font-bold text-foreground">
                    {t("recorded.title")}
                  </p>
                </div>
                <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                  {t("recorded.description")}
                </p>
              </div>
            </div>
            <div className="flex flex-col items-end pl-2 gap-2">
              <div className="flex flex-row gap-2 mt-1">
                <div className="relative">
                  <Button ref={openMenuButton} onClick={toggleMenu}>
                    <Download size={18} weight="bold" />
                    {t("recorded.export")}
                    <CaretDown size={18} weight="bold" />
                  </Button>
                  <div
                    ref={menuRef}
                    className={`${
                      showMenu ? "slide-down" : "slide-up hidden"
                    } z-20 w-fit rounded-lg absolute top-full right-0 shadow-md`}
                  >
                    <div className="primary-bg">
                      {Object.entries(exportOptions).map(([key, data]) => (
                        <Button
                          key={key}
                          onClick={() => {
                            handleDumpChats(key);
                            setShowMenu(false);
                          }}
                          variant="ghost"
                          className="w-full text-left px-4 py-2 text-white text-sm hover:bg-[#1a4250] justify-start"
                        >
                          {data.name}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
                <CompactChatDeletionPanel
                  setChats={setChats}
                  fetchChats={fetchChats}
                  fetchTotalCount={fetchTotalCount}
                />
              </div>
              <div className="flex flex-row gap-2 mt-1">
                {isFiltering && (
                  <Button
                    onClick={resetFilters}
                    variant="destructive"
                    size="sm"
                    className="gap-1"
                  >
                    <X size={14} weight="bold" />
                    {t("recorded.reset_filters")}
                  </Button>
                )}
                <input
                  type="text"
                  placeholder={t("recorded.filters.by-name")}
                  value={userFilter}
                  onChange={(e) => setUserFilter(e.target.value)}
                  className="dark-input-mdl text-foreground text-sm rounded-md block p-2.5"
                />
                <input
                  type="text"
                  placeholder={t("recorded.filters.by-reference")}
                  value={invoiceRef}
                  onChange={(e) => setInvoiceRef(e.target.value)}
                  className="dark-input-mdl text-foreground text-sm rounded-md block p-2.5"
                />
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="dark-input-mdl text-foreground text-sm rounded-md block p-2.5"
                />
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="dark-input-mdl text-foreground text-sm rounded-md block p-2.5"
                />
              </div>
              <div className="flex flex-row items-center justify-end mt-2 mb-1 w-full">
                <div className="flex items-center gap-2 text-sm text-subtle">
                  <span className="flex items-center">
                    <Database size={16} className="mr-1" />
                    {t("recorded.total_logs")}:{" "}
                    <span className="font-mono ml-1">{totalChatsCount}</span>
                  </span>
                  <span className="flex items-center ml-4">
                    <FunnelSimple size={16} className="mr-1" />
                    {t("recorded.filtered_logs")}:{" "}
                    <span className="font-mono ml-1">
                      {filteredChats.length}
                    </span>
                  </span>
                </div>
              </div>
            </div>
            <ChatsContainer
              loading={loading}
              chats={filteredChats}
              setChats={setChats}
              offset={offset}
              setOffset={setOffset}
              canNext={canNext}
              t={t}
              fetchTotalCount={fetchTotalCount}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

// Create a more compact version of the delete panel for the top toolbar
function CompactChatDeletionPanel({ fetchChats, fetchTotalCount }) {
  const { t } = useTranslation();
  const [selectedOption, setSelectedOption] = useState("months-1");
  const [isDeleting, setIsDeleting] = useState(false);
  const [deletionCount, setDeletionCount] = useState(0);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showPanel, setShowPanel] = useState(false);
  const panelRef = useRef();
  const buttonRef = useRef();

  // Fetch the deletion count on component mount
  useEffect(() => {
    async function fetchDeletionCount() {
      const count = await System.getChatDeletionCount();
      setDeletionCount(count);
    }
    fetchDeletionCount();
  }, []);

  useEffect(() => {
    function handleClickOutside(event) {
      if (
        panelRef.current &&
        !panelRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setShowPanel(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleDelete = async () => {
    if (!showConfirmation) {
      setShowConfirmation(true);
      return;
    }

    setIsDeleting(true);
    try {
      if (selectedOption === "all") {
        // Delete all chats
        const result = await System.deleteChat(-1);
        if (result.success) {
          setDeletionCount(result.totalDeleted || 0);
          // Refresh the chat list instead of just emptying it
          fetchChats();
          fetchTotalCount();
          showToast(t("show-toast.cleared-chats"), "success");
        } else {
          console.error("Failed to delete all chats:", result.error);
          alert(`Failed to delete chats: ${result.error}`);
        }
      } else {
        // Delete chats older than selected timeframe
        const [timeframe, value] = selectedOption.split("-");
        const result = await System.deleteOldChats(
          timeframe,
          parseInt(value, 10)
        );
        if (result.success) {
          setDeletionCount(result.totalDeleted);
          // Refresh the chat list
          fetchChats();
          fetchTotalCount();
          showToast(
            t("show-toast.deleted-old-chats", {
              count: result.deletedCount,
              fallback: `Deleted ${result.deletedCount} old chat(s)`,
            }),
            "success"
          );
        } else {
          console.error("Failed to delete old chats:", result.error);
          alert(`Failed to delete old chats: ${result.error}`);
        }
      }
    } catch (error) {
      console.error("Error deleting chats:", error);
    } finally {
      setIsDeleting(false);
      setShowConfirmation(false);
      setShowPanel(false);
    }
  };

  const handleCancel = () => {
    setShowConfirmation(false);
  };

  const getButtonText = () => {
    if (selectedOption === "all") {
      return t("recorded.clear-chats");
    } else {
      return t("recorded.delete_old_chats");
    }
  };

  return (
    <div className="relative">
      <Button
        ref={buttonRef}
        onClick={() => setShowPanel(!showPanel)}
        variant="destructive"
        className="text-sm"
      >
        <TrashSimple size={14} weight="bold" />
        {t("recorded.delete_old_chats")}
      </Button>

      {showPanel && (
        <div
          ref={panelRef}
          className="absolute right-0 top-full mt-2 z-20 bg-background border border-border rounded-md shadow-lg p-4 w-80"
        >
          <div className="flex flex-col gap-3">
            <div>
              <h3 className="text-sm font-medium text-foreground mb-1">
                {t("recorded.bulk_delete_title")}
              </h3>
              <p className="text-xs text-foreground text-opacity-70 mb-1">
                {t("recorded.bulk_delete_description")}
              </p>
              <p className="text-xs italic text-foreground text-opacity-70">
                {t("recorded.total_deleted")}:{" "}
                <span className="font-mono">{deletionCount}</span>
              </p>
            </div>

            <div className="flex flex-col gap-2">
              <select
                value={selectedOption}
                onChange={(e) => setSelectedOption(e.target.value)}
                className="px-3 py-2 border text-sm rounded-md bg-background text-foreground border-border w-full"
                disabled={isDeleting || showConfirmation}
              >
                <optgroup label={t("common.timeframes")}>
                  <option value="days-2">{t("recorded.two_days")}</option>
                  <option value="weeks-1">{t("recorded.one_week")}</option>
                  <option value="weeks-2">{t("recorded.two_weeks")}</option>
                  <option value="months-1">{t("recorded.one_month")}</option>
                  <option value="months-2">{t("recorded.two_months")}</option>
                  <option value="months-3">{t("recorded.three_months")}</option>
                </optgroup>
                <optgroup label={t("common.other")}>
                  <option value="all">{t("recorded.clear-chats")}</option>
                </optgroup>
              </select>

              {showConfirmation ? (
                <div className="flex gap-2 mt-2">
                  <Button
                    onClick={handleCancel}
                    variant="secondary"
                    className="flex-1"
                    disabled={isDeleting}
                  >
                    {t("common.cancel")}
                  </Button>
                  <Button
                    onClick={handleDelete}
                    variant="destructive"
                    className="flex-1"
                    disabled={isDeleting}
                  >
                    {isDeleting ? (
                      <>
                        <div className="animate-spin h-4 w-4 border-2 border-white border-r-transparent rounded-full"></div>
                        {t("presets.deleting")}
                      </>
                    ) : (
                      <>
                        <TrashSimple size={16} weight="bold" />
                        {t("common.confirm")}
                      </>
                    )}
                  </Button>
                </div>
              ) : (
                <Button
                  onClick={handleDelete}
                  variant="destructive"
                  className="w-full"
                >
                  <TrashSimple size={16} weight="bold" />
                  {getButtonText()}
                </Button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function ChatsContainer({
  loading,
  chats,
  setChats,
  offset,
  setOffset,
  canNext,
  t,
  fetchTotalCount,
}) {
  const handlePrevious = () => {
    setOffset(Math.max(offset - 1, 0));
  };
  const handleNext = () => {
    setOffset(offset + 1);
  };

  const handleDeleteChat = async (chatId) => {
    await System.deleteChat(chatId);
    setChats((prevChats) => prevChats.filter((chat) => chat.id !== chatId));
    fetchTotalCount();
  };

  if (loading) {
    return (
      <Skeleton.default
        height="80vh"
        width="100%"
        highlightColor="#fff"
        baseColor="#e2f3fa"
        count={1}
        className="w-full p-4 rounded-b-2xl rounded-tr-2xl rounded-tl-sm mt-6"
        containerClassName="flex w-full"
      />
    );
  }

  // Show a message when there are no chats
  if (!chats || chats.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center w-full p-10 mt-6 border border-border rounded-lg">
        <Info size={48} className="text-gray-400 mb-4" />
        <p className="text-lg font-medium text-muted-foreground">
          {t("recorded.no-chats-found")}
        </p>
        <p className="text-sm text-muted-foreground mt-2 text-center max-w-md">
          {t("recorded.no-chats-description", {
            fallback:
              "No chat logs found matching your filters. Try changing your search criteria or delete older time period.",
          })}
        </p>
      </div>
    );
  }

  return (
    <>
      <table className="w-full text-sm text-left rounded-lg mt-2">
        <thead className="text-foreground text-opacity-80 text-xs leading-[18px] font-bold uppercase border-white border-b border-opacity-60">
          <tr>
            <th scope="col" className="px-6 py-3 rounded-tl-lg">
              {t("recorded.table.id")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("recorded.table.by")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("recorded.table.workspace")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("recorded.table.prompt")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("recorded.table.response")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("recorded.table.at")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("recorded.table.invoice")}
            </th>
            <th scope="col" className="px-6 py-3 text-center">
              {t("recorded.table.completion-token")}
            </th>
            <th scope="col" className="px-6 py-3 text-center">
              {t("recorded.table.prompt-token")}
            </th>
            <th scope="col" className="px-6 py-3 rounded-tr-lg" />
          </tr>
        </thead>
        <tbody>
          {!!chats &&
            chats.map((chat) => (
              <ChatRow key={chat.id} chat={chat} onDelete={handleDeleteChat} />
            ))}
        </tbody>
      </table>
      <div className="flex w-full justify-between items-center mt-6">
        <Button onClick={handlePrevious} disabled={offset === 0}>
          <ArrowLeft size={20} />
          {t("recorded.previous")}
        </Button>
        <Button onClick={handleNext} disabled={!canNext}>
          {t("recorded.next")}
          <ArrowRight size={20} />
        </Button>
      </div>
    </>
  );
}
