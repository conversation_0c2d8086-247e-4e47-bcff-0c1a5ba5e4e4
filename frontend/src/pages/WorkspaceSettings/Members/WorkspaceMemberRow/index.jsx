import { capitalCase } from "change-case";

export default function WorkspaceMemberRow({ user }) {
  return (
    <>
      <tr className="bg-transparent text-foreground text-opacity-80 text-sm font-medium table-tr-item">
        <th scope="row" className="px-6 py-4 whitespace-nowrap">
          {user.username}
        </th>
        <td className="px-6 py-4">
          <p className="text-sm font-medium text-foreground">
            {capitalCase(user.role)}
          </p>
        </td>
        <td className="px-6 py-4">
          {new Date(user.lastUpdatedAt).toLocaleString()}
        </td>
      </tr>
    </>
  );
}
