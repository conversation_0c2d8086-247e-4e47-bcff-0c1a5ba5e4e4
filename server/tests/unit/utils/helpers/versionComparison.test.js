const {
  compareVersions,
  normalizeVersion,
  findHighestVersion,
  processVersionData,
} = require("../../../../utils/helpers/versionComparison");

describe("Version Comparison Utilities", () => {
  describe("normalizeVersion", () => {
    it("should remove v prefix", () => {
      expect(normalizeVersion("v1.2.3")).toBe("1.2.3");
      expect(normalizeVersion("V2.0.0")).toBe("2.0.0");
    });

    it("should remove pre-release suffixes", () => {
      expect(normalizeVersion("1.2.3-beta.1")).toBe("1.2.3");
      expect(normalizeVersion("1.2.3-alpha")).toBe("1.2.3");
      expect(normalizeVersion("1.2.3+build.123")).toBe("1.2.3");
    });

    it("should handle versions without prefix or suffix", () => {
      expect(normalizeVersion("1.2.3")).toBe("1.2.3");
      expect(normalizeVersion("0.1.0")).toBe("0.1.0");
    });

    it("should throw error for non-string input", () => {
      expect(() => normalizeVersion(123)).toThrow("Version must be a string");
      expect(() => normalizeVersion(null)).toThrow("Version must be a string");
      expect(() => normalizeVersion(undefined)).toThrow(
        "Version must be a string"
      );
    });
  });

  describe("compareVersions", () => {
    it("should correctly compare major versions", () => {
      expect(compareVersions("2.0.0", "1.0.0")).toBeGreaterThan(0);
      expect(compareVersions("1.0.0", "2.0.0")).toBeLessThan(0);
      expect(compareVersions("1.0.0", "1.0.0")).toBe(0);
    });

    it("should correctly compare minor versions", () => {
      expect(compareVersions("1.2.0", "1.1.0")).toBeGreaterThan(0);
      expect(compareVersions("1.1.0", "1.2.0")).toBeLessThan(0);
      expect(compareVersions("1.1.0", "1.1.0")).toBe(0);
    });

    it("should correctly compare patch versions", () => {
      expect(compareVersions("1.1.2", "1.1.1")).toBeGreaterThan(0);
      expect(compareVersions("1.1.1", "1.1.2")).toBeLessThan(0);
      expect(compareVersions("1.1.1", "1.1.1")).toBe(0);
    });

    it("should handle versions with different number of parts", () => {
      expect(compareVersions("1.1", "1.1.0")).toBe(0);
      expect(compareVersions("1.1.1", "1.1")).toBeGreaterThan(0);
      expect(compareVersions("1.0", "1.0.1")).toBeLessThan(0);
    });

    it("should handle v prefix and pre-release versions", () => {
      expect(compareVersions("v1.2.0", "1.1.0")).toBeGreaterThan(0);
      expect(compareVersions("1.2.0-beta", "1.1.0")).toBeGreaterThan(0);
      expect(compareVersions("1.0.0-beta", "1.0.0")).toBeLessThan(0);
    });

    it("should fall back to string comparison for malformed versions", () => {
      // Mock console.warn to avoid noise in test output
      const consoleSpy = jest
        .spyOn(console, "warn")
        .mockImplementation(() => {});

      const result = compareVersions("invalid", "also-invalid");
      expect(typeof result).toBe("number");

      consoleSpy.mockRestore();
    });
  });

  describe("findHighestVersion", () => {
    it("should return highest version from array", () => {
      const versions = [
        { version: "1.0.0", description: "First" },
        { version: "1.2.0", description: "Highest" },
        { version: "1.1.0", description: "Middle" },
      ];

      const result = findHighestVersion(versions);
      expect(result.version).toBe("1.2.0");
      expect(result.description).toBe("Highest");
    });

    it("should handle single version array", () => {
      const versions = [{ version: "1.0.0", description: "Only one" }];

      const result = findHighestVersion(versions);
      expect(result.version).toBe("1.0.0");
      expect(result.description).toBe("Only one");
    });

    it("should return null for empty array", () => {
      expect(findHighestVersion([])).toBeNull();
    });

    it("should return null for non-array input", () => {
      expect(findHighestVersion(null)).toBeNull();
      expect(findHighestVersion(undefined)).toBeNull();
      expect(findHighestVersion("not an array")).toBeNull();
    });

    it("should filter out versions without version property", () => {
      const versions = [
        { description: "No version" },
        { version: "1.0.0", description: "Valid" },
        { version: null, description: "Null version" },
      ];

      const result = findHighestVersion(versions);
      expect(result.version).toBe("1.0.0");
      expect(result.description).toBe("Valid");
    });

    it("should return null if no valid versions", () => {
      const versions = [
        { description: "No version" },
        { version: null, description: "Null version" },
      ];

      expect(findHighestVersion(versions)).toBeNull();
    });

    it("should handle complex version comparisons", () => {
      const versions = [
        { version: "1.0.0", description: "First" },
        { version: "1.10.0", description: "Should be highest" },
        { version: "1.2.0", description: "Middle" },
        { version: "1.9.0", description: "High but not highest" },
      ];

      const result = findHighestVersion(versions);
      expect(result.version).toBe("1.10.0");
      expect(result.description).toBe("Should be highest");
    });
  });

  describe("processVersionData", () => {
    it("should handle new format with versions array", () => {
      const versionData = {
        versions: [
          {
            version: "1.0.0",
            description: "First",
            timestamp: "2025-01-01T10:00:00Z",
          },
          {
            version: "1.1.0",
            description: "Latest",
            timestamp: "2025-01-15T10:00:00Z",
          },
        ],
      };

      const result = processVersionData(versionData);
      expect(result.version).toBe("1.1.0");
      expect(result.description).toBe("Latest");
      expect(result.timestamp).toBe("2025-01-15T10:00:00Z");
    });

    it("should handle old format with single version", () => {
      const versionData = {
        version: "1.0.0",
        description: "Old format",
        timestamp: "2025-01-01T10:00:00Z",
      };

      const result = processVersionData(versionData);
      expect(result.version).toBe("1.0.0");
      expect(result.description).toBe("Old format");
      expect(result.timestamp).toBe("2025-01-01T10:00:00Z");
    });

    it("should return null for invalid input", () => {
      expect(processVersionData(null)).toBeNull();
      expect(processVersionData(undefined)).toBeNull();
      expect(processVersionData("string")).toBeNull();
      expect(processVersionData(123)).toBeNull();
    });

    it("should return null for empty versions array", () => {
      const versionData = { versions: [] };
      expect(processVersionData(versionData)).toBeNull();
    });

    it("should return null for object without version info", () => {
      const versionData = { someOtherProperty: "value" };
      expect(processVersionData(versionData)).toBeNull();
    });

    it("should handle old format with missing description", () => {
      const versionData = {
        version: "1.0.0",
        timestamp: "2025-01-01T10:00:00Z",
      };

      const result = processVersionData(versionData);
      expect(result.version).toBe("1.0.0");
      expect(result.description).toBeUndefined();
      expect(result.timestamp).toBe("2025-01-01T10:00:00Z");
    });

    it("should handle new format with invalid versions in array", () => {
      const versionData = {
        versions: [
          { description: "No version" },
          { version: "1.0.0", description: "Valid" },
        ],
      };

      const result = processVersionData(versionData);
      expect(result.version).toBe("1.0.0");
      expect(result.description).toBe("Valid");
    });
  });
});
