# Locale Scripts Documentation

This document describes the locale verification and fixing scripts used to maintain consistency across all internationalization files in the project.

## Overview

The project supports multiple languages and uses a structured approach to manage translations. Two scripts help maintain consistency across all locale files:

1. **`verifyLocaleFiles.mjs`** - Identifies inconsistencies between locale files
2. **`fixLocaleStructure.mjs`** - Automatically fixes key ordering and structure

## Supported Languages

- **English (en)** - Template/source language
- **French (fr)** - Français
- **German (de)** - Deutsch
- **Norwegian (no)** - Norsk
- **Polish (pl)** - Polski
- **Kinyarwanda (rw)** - Ikinyarwanda
- **Swedish (sv)** - Svenska

## Script Locations

Both scripts are located in the `scripts/` directory:
- `scripts/verifyLocaleFiles.mjs`
- `scripts/fixLocaleStructure.mjs`

## Verification Script (`verifyLocaleFiles.mjs`)

### Purpose

This script verifies that all locale files are consistent with the English template files. It checks for structural consistency across all supported languages.

### What It Checks

1. **Missing keys**: Keys that exist in English but are missing in other locales
2. **Extra keys**: Keys that exist in other locales but not in English
3. **Key order**: Ensures keys appear in the same order as the English template
4. **Nested structure**: Recursively validates nested objects

### What It Does NOT Do

- Does not automatically add missing keys (requires manual translation)
- Does not validate translation quality or accuracy
- Does not check for unused keys in the codebase

### Usage

```bash
node scripts/verifyLocaleFiles.mjs
```

### Sample Output

**When issues are found:**
```
🔍 Verifying locale file consistency...
📁 Checking 7 locales: de, en, fr, no, pl, rw, sv
📄 Validating 15 files: answerUpgrade.js, cdbRelatedKeys.js, ...

❌ Errors found during locale verification:

 - [workspaceViewAndButtons.js] Missing key 'workspace-chats.prompt.total-tokens' in locale 'fr' - requires manual translation
 - [workspaceViewAndButtons.js] Key order mismatch at 'workspace-chats.prompt.view-agents' in locale 'fr': expected index 25, found 'total-tokens' - run fixLocaleStructure.mjs to fix

🔧 To fix these issues:
   1. Run 'node scripts/fixLocaleStructure.mjs' to fix key ordering
   2. Manually add missing keys with proper translations
   3. Remove extra keys that don't exist in English template
   4. Re-run this script to verify fixes
```

**When all files are consistent:**
```
🔍 Verifying locale file consistency...
📁 Checking 7 locales: de, en, fr, no, pl, rw, sv
📄 Validating 15 files: answerUpgrade.js, cdbRelatedKeys.js, ...

✅ All locale files are present and consistent with English files.
🌍 All supported languages have matching key structures.
```

## Fix Script (`fixLocaleStructure.mjs`)

### Purpose

This script fixes the structure and key ordering of locale files by ensuring that keys are aligned with the English template files. It uses AST-based reordering with recast and Babel parser to preserve comments, then formats with Prettier.

### What It Does

1. **Reorders keys** to match the English template file order
2. **Preserves existing translations** and comments
3. **Adds missing keys** with empty string values (requires manual translation)
4. **Maintains nested object structure**
5. **Formats files** with Prettier for consistency

### What It Does NOT Do

- Does not translate missing keys (leaves them as empty strings)
- Does not remove extra keys (manual removal required)
- Does not validate translation quality or accuracy

### Usage

```bash
node scripts/fixLocaleStructure.mjs
```

### Sample Output

```
🔧 Fixing locale file structure and key ordering...
📁 Processing 6 locales: de, fr, no, pl, rw, sv
📄 Fixing 15 files: answerUpgrade.js, cdbRelatedKeys.js, ...

⚠️  Note: Missing keys will be added with empty strings and require manual translation

Fixed structure for de - answerUpgrade.js
Fixed structure for de - cdbRelatedKeys.js
...
Fixed structure for sv - workspaceViewAndButtons.js

✨ Formatting locale files with Prettier...
frontend/src/locales/de/answerUpgrade.js 19ms (unchanged)
...

✅ Locale structure fix completed!

📋 Next steps:
   1. Review any empty string values and add proper translations
   2. Run 'node scripts/verifyLocaleFiles.mjs' to verify fixes
   3. Test the application to ensure translations work correctly
```

## Recommended Workflow

### 1. Regular Verification

Run the verification script regularly to catch inconsistencies early:

```bash
node scripts/verifyLocaleFiles.mjs
```

### 2. Fix Structure Issues

If issues are found, run the fix script to resolve ordering and add missing key placeholders:

```bash
node scripts/fixLocaleStructure.mjs
```

### 3. Manual Translation

After running the fix script, manually translate any empty string values that were added:

1. Search for empty strings (`""`) in locale files
2. Replace with appropriate translations for each language
3. Ensure translations maintain the same meaning and context as the English version

### 4. Verify Fixes

Run the verification script again to confirm all issues are resolved:

```bash
node scripts/verifyLocaleFiles.mjs
```

### 5. Test Application

Test the application in different languages to ensure translations work correctly and display properly in the UI.

## Important Notes

### Manual Translation Requirement

**Critical**: Missing keys are added with empty string values and MUST be manually translated. The scripts cannot automatically translate content because:

1. **Context matters**: Translations need to consider the specific context and usage
2. **Cultural adaptation**: Some phrases may need cultural adaptation beyond literal translation
3. **Technical accuracy**: Legal and technical terms require precise translation
4. **UI constraints**: Translations must fit within UI space constraints

### Key Ordering

The scripts ensure that keys appear in the same order across all locale files. This:

1. Makes files easier to compare and review
2. Reduces merge conflicts in version control
3. Ensures consistent structure across languages
4. Makes it easier to spot missing or extra keys

### File Structure

Locale files are organized as follows:

```
frontend/src/locales/
├── en/           # English (template)
│   ├── common.js
│   ├── workspaceViewAndButtons.js
│   └── ...
├── fr/           # French
│   ├── common.js
│   ├── workspaceViewAndButtons.js
│   └── ...
└── [other languages]/
```

### Best Practices

1. **Always run verification before committing** locale changes
2. **Use meaningful translation keys** that describe the content
3. **Keep translations concise** while maintaining meaning
4. **Test in multiple languages** to ensure UI layout works
5. **Document context** for complex translations in comments
6. **Review translations** with native speakers when possible

## Troubleshooting

### Common Issues

**Script fails to run:**
- Ensure you're in the project root directory
- Check that Node.js is installed and up to date
- Verify that all dependencies are installed (`npm install`)

**Prettier formatting fails:**
- Ensure Prettier is installed in the project
- Check that locale files have valid JavaScript syntax
- Run Prettier manually: `npx prettier --write "frontend/src/locales/**/*.js"`

**Missing translations not detected:**
- Ensure the English template file has the correct keys
- Check that the locale file exists and is properly formatted
- Verify that the key structure matches exactly (including nesting)

### Getting Help

If you encounter issues with the locale scripts:

1. Check the console output for specific error messages
2. Verify that all locale files have valid JavaScript syntax
3. Ensure that the English template files are correct and complete
4. Run the scripts with verbose output if available
5. Check the project documentation for additional guidance

## Integration with Development Workflow

### Pre-commit Hooks

Consider adding locale verification to pre-commit hooks:

```bash
# In .husky/pre-commit or similar
node scripts/verifyLocaleFiles.mjs
```

### CI/CD Pipeline

Include locale verification in your CI/CD pipeline to catch issues early:

```yaml
# Example GitHub Actions step
- name: Verify Locale Consistency
  run: node scripts/verifyLocaleFiles.mjs
```

### Code Review

During code reviews, pay special attention to:

1. New translation keys are added to all locale files
2. Translations are appropriate and contextually correct
3. Key ordering is consistent across files
4. No hardcoded strings are introduced in the UI code
