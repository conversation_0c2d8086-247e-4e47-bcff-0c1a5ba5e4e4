export default {
  // =========================
  // DOCUMENT BUILDER SETTINGS
  // =========================
  "document-builder": {
    title: "Dokumentbygger-prompter",
    description:
      "Tilpass standardprompter som brukes av Dokumentbygger-funksjonen.",
    "override-prompt-placeholder":
      "Skriv inn prompt for å overstyre standard systemprompten",
    saving: "Lagrer...",
    save: "Lagre prompt-innstillinger",
    "toast-success": "Dokumentbygger-prompter lagret vellykket.",
    "toast-fail": "Kunne ikke lagre dokumentbygger-prompter.",

    // =========================
    // LEGAL DRAFTING PROMPTS
    // =========================
    prompts: {
      group: {
        document_summary: {
          title: "Prompter for dokumentsammendrag",
          description:
            "Konfigurer system- og brukerprompter for dokumentsammendrag.",
        },
        document_relevance: {
          title: "Prompter for dokumentrelevans",
          description:
            "Konfigurer system- og brukerprompter for dokumentrelevans.",
        },
        section_drafting: {
          title: "Prompter for seksjonsutkast",
          description:
            "Konfigurer system- og brukerprompter for seksjonsutkast.",
        },
        section_legal_issues: {
          title: "Prompter for juridiske problemer i seksjoner",
          description:
            "Konfigurer system- og brukerprompter for juridiske problemer i seksjoner.",
        },
        memo_creation: {
          title: "Prompter for memo-opprettelse",
          description: "Konfigurer prompter for memo-opprettelse.",
        },
        section_index: {
          title: "Prompter for seksjonsindeks",
          description: "Konfigurer prompter for seksjonsindeks.",
        },
        select_main_document: {
          title: "Prompter for valg av hoveddokument",
          description:
            "Konfigurer system- og brukerprompter for valg av hoveddokument.",
        },
        section_list_from_main: {
          title: "Prompter for seksjonsliste fra hoveddokument",
          description:
            "Konfigurer system- og brukerprompter for seksjonsliste fra hoveddokument.",
        },
        section_list_from_summaries: {
          title: "Seksjonsliste fra Sammendrag Prompts",
          description:
            "Konfigurer system- og brukerprompts for Seksjonsliste fra Sammendrag.",
        },
        reference_files_description: {
          title: "Referansefiler Beskrivelse Prompts",
          description:
            "Konfigurer system- og brukerprompts for Referansefiler Beskrivelse.",
        },
        review_files_description: {
          title: "Gjennomgangsfiler Beskrivelse Prompts",
          description:
            "Konfigurer system- og brukerprompts for Gjennomgangsfiler Beskrivelse.",
        },
        reference_review_sections: {
          title: "Referanse-/Gjennomgangsseksjoner Prompts",
          description:
            "Konfigurer system- og brukerprompts for Referanse-/Gjennomgangsseksjoner.",
        },
      },
      // Document Summary
      "document-summary-system-label": "Dokumentsammendrag (System)",
      "document-summary-system-description":
        "System-prompt som instruerer AI om hvordan man oppsummerer innholdet i et dokument og dets relevans for en juridisk oppgave.",
      "document-summary-user-label": "Dokumentsammendrag (Bruker)",
      "document-summary-user-description":
        "Bruker-prompt-mal for å generere en detaljert oppsummering av dokumentinnhold i forhold til en spesifikk juridisk oppgave.",

      // Reference Files Description Prompts
      "reference-files-description-system-label":
        "Referansefiler Beskrivelse (System)",
      "reference-files-description-system-description":
        "Systemprompt for å beskrive referansefiler.",
      "reference-files-description-user-label":
        "Referansefiler Beskrivelse (Bruker)",
      "reference-files-description-user-description":
        "Brukerprompt-mal for å beskrive referansefiler.",

      // Document Relevance
      "document-relevance-system-label": "Dokumentrelevans (System)",
      "document-relevance-system-description":
        "System-prompt for å vurdere om et dokument er relevant for en juridisk oppgave, med forventning om et sant/usant svar.",
      "document-relevance-user-label": "Dokumentrelevans (Bruker)",
      "document-relevance-user-description":
        "Bruker-prompt-mal for å sjekke om dokumentinnholdet er relevant for en gitt juridisk oppgave.",

      // Section Drafting
      "section-drafting-system-label": "Seksjonsutkast (System)",
      "section-drafting-system-description":
        "System-prompt for å generere en enkelt dokumentseksjon i profesjonell juridisk stil ved bruk av spesifiserte dokumenter og kontekst.",
      "section-drafting-user-label": "Seksjonsutkast (Bruker)",
      "section-drafting-user-description":
        "Bruker-prompt-mal for å generere en spesifikk seksjon av et juridisk dokument, med hensyn til tittel, oppgave, kildedokumenter og tilstøtende seksjoner.",

      // Section Legal Issues
      "section-legal-issues-system-label":
        "Identifisering av juridiske problemer for seksjon (System)",
      "section-legal-issues-system-description":
        "System-prompt for å identifisere spesifikke juridiske temaer som det bør hentes faktainformasjon for å støtte utarbeidelsen av en dokumentseksjon.",

      "section-legal-issues-user-label":
        "Identifisering av juridiske problemer for seksjon (Bruker)",
      "section-legal-issues-user-description":
        "Bruker-prompt-mal for å liste opp juridiske temaer eller datapunkter for å hente kontekstuell informasjon relevant for en spesifikk dokumentseksjon og juridisk oppgave.",

      // Memo Creation
      "memo-creation-template-label": "Standard memo-opprettelsesmal",
      "memo-creation-template-description":
        "Prompt-mal for å lage et juridisk notat som adresserer et spesifikt juridisk problem, med hensyn til de medfølgende dokumentene og oppgavekonteksten.",
      // Section Index
      "section-index-system-label": "Seksjonsindeks (System)",
      "section-index-system-description":
        "System-prompt for å generere en strukturert indeks av seksjoner for et juridisk dokument.",

      // Select Main Document
      "select-main-document-system-label": "Velg hoveddokument (System)",
      "select-main-document-system-description":
        "System-prompt for å identifisere det mest relevante hoveddokumentet for en juridisk oppgave fra flere dokumentsammendrag.",
      "select-main-document-user-label": "Velg hoveddokument (Bruker)",
      "select-main-document-user-description":
        "Bruker-prompt-mal for å identifisere hoveddokumentet for en juridisk oppgave basert på sammendrag av flere dokumenter.",

      // Section List from Main Document
      "section-list-from-main-system-label":
        "Seksjonsliste fra hoveddokument (System)",
      "section-list-from-main-system-description":
        "System-prompt for å lage en JSON-strukturert liste over seksjoner for et juridisk dokument basert på innholdet i hoveddokumentet og den juridiske oppgaven.",
      "section-list-from-main-user-label":
        "Seksjonsliste fra hoveddokument (Bruker)",
      "section-list-from-main-user-description":
        "Bruker-prompt-mal for å gi den juridiske oppgaven og hoveddokumentinnholdet for å generere en seksjonsliste.",
      // Section List from Summaries
      "section-list-from-summaries-system-label":
        "Seksjonsliste fra sammendrag (System)",
      "section-list-from-summaries-system-description":
        "System-prompt for å lage en JSON-strukturert liste over seksjoner basert på dokumentsammendrag og den juridiske oppgaven, når det ikke finnes et hoveddokument.",
      "section-list-from-summaries-user-label":
        "Seksjonsliste fra sammendrag (Bruker)",
      "section-list-from-summaries-user-description":
        "Brukerprompt-mal for å levere den juridiske oppgaven og dokumentsammendrag for å generere en seksjonsliste, når ingen hoveddokument eksisterer.",

      // Review Files Description Prompts
      "review-files-description-system-label":
        "Gjennomgangsfiler Beskrivelse (System)",
      "review-files-description-system-description":
        "Systemprompt for å beskrive gjennomgangsfiler.",
      "review-files-description-user-label":
        "Gjennomgangsfiler Beskrivelse (Bruker)",
      "review-files-description-user-description":
        "Brukerprompt-mal for å beskrive gjennomgangsfiler.",

      // Reference/Review Sections Prompts
      "reference-review-sections-system-label":
        "Referanse-/Gjennomgangsseksjoner (System)",
      "reference-review-sections-system-description":
        "Systemprompt for å definere referanse-/gjennomgangsseksjoner.",
      "reference-review-sections-user-label":
        "Referanse-/Gjennomgangsseksjoner (Bruker)",
      "reference-review-sections-user-description":
        "Brukerprompt-mal for å definere referanse-/gjennomgangsseksjoner.",
    },

    // =========================
    // ADD/EDIT LEGAL TASK KEYS
    // =========================
    "view-categories": "Vis alle kategorier",
    "hide-categories": "Skjul listen",
    "add-task": "Legg til juridisk oppgave",
    loading: "Laster...",
    table: {
      title: "Juridiske oppgaver",
      name: "Navn",
      "sub-category": "Underkategori",
      description: "Beskrivelse",
      prompt: "Juridisk oppgaveprompt",
      actions: "Handlinger",
      delete: "Slett",
      "delete-confirm": "Er du sikker på at du vil slette denne kategorien?",
      "delete-success": "Kategorien ble slettet",
      "delete-error": "Kunne ikke slette kategorien",
    },
    "create-task-title": "Opprett juridisk oppgave",
    "category-name": "Kategorinavn",
    "category-name-desc": "Angi hovedkategoriens navn.",
    "category-name-placeholder": "Skriv inn kategorinavn",
    "subcategory-name": "Underkategorinavn",
    "subcategory-name-desc": "Angi underkategoriens navn.",
    "subcategory-name-placeholder": "Skriv inn underkategorinavn",
    "description-desc": "Angi en beskrivelse av kategorien og underkategorien.",
    "description-placeholder": "Skriv inn en kort beskrivelse",
    submitting: "Sender...",
    submit: "Send",
    validation: {
      "category-required": "Kategorinavn er påkrevd.",
      "subcategory-required": "Underkategorinavn er påkrevd.",
      "description-required": "Beskrivelse er påkrevd.",
      "prompt-required": "Juridisk oppgaveprompt er påkrevd.",
    },
    "create-task": {
      title: "Opprett juridisk oppgave",
      category: {
        name: "Kategorinavn",
        desc: "Angi kategoriens navn.",
        placeholder: "Skriv inn kategorinavn",
        type: "Kategoritype",
        new: "Opprett ny kategori",
        existing: "Bruk eksisterende kategori",
        select: "Velg kategori",
        "select-placeholder": "Velg en eksisterende kategori",
      },
      subcategory: {
        name: "Underkategorinavn",
        desc: "Angi underkategoriens navn.",
        placeholder: "Skriv inn underkategorinavn",
      },
      description: {
        name: "Beskrivelse og brukerinstruksjoner",
        desc: "Informasjon og instruksjoner som brukeren vil se.",
        placeholder:
          "Beskriv hvilke typer dokumenter som må lastes opp til arbeidsområdet for å oppnå best mulig resultat",
      },
      prompt: {
        name: "Juridisk oppgaveprompt",
        desc: "Skriv inn prompten som skal brukes for denne juridiske oppgaven. Du kan også laste opp eksempeldokumenter med knappene for å legge til innholdseksempler i prompten din.",
        placeholder:
          "Skriv inn juridisk oppgaveprompt eller last opp eksempeldokumenter for å forbedre prompten...",
      },
      submitting: "Sender...",
      submit: "Send",
      validation: {
        "category-required": "Kategorinavn er påkrevd.",
        "subcategory-required": "Underkategorinavn er påkrevd.",
        "description-required": "Beskrivelse er påkrevd.",
        "prompt-required": "Juridisk oppgaveprompt er påkrevd.",
        "legal-task-type-required": "Type juridisk oppgave er påkrevd.",
      },
    },
    "edit-task": {
      title: "Rediger juridisk oppgave",
      submitting: "Oppdaterer...",
      submit: "Oppdater oppgave",
      subcategory: {
        name: "Underkategorinavn",
        desc: "Angi et nytt navn for denne juridiske oppgaven",
        placeholder: "Skriv inn juridisk oppgave...",
      },
      description: {
        name: "Beskrivelse og brukerinstruksjoner",
        desc: "Angi en beskrivelse og brukerinstruksjoner for denne juridiske oppgaven",
        placeholder: "Skriv inn beskrivelse og brukerinstruksjoner...",
      },
      prompt: {
        name: "Juridisk oppgaveprompt",
        desc: "Skriv inn prompten som skal brukes for denne juridiske oppgaven. Du kan også laste opp eksempeldokumenter med knappene for å legge til innholdseksempler i prompten din.",
        placeholder:
          "Skriv inn juridisk oppgaveprompt eller last opp eksempeldokumenter for å forbedre prompten...",
      },
      validation: {
        "subcategory-required": "Juridisk oppgavenavn er påkrevd",
        "description-required": "Beskrivelse er påkrevd",
        "prompt-required": "Juridisk oppgaveprompt er påkrevd",
        "legal-task-type-required": "Type juridisk oppgave er påkrevd",
      },
    },
    "task-form": {
      "requires-main-doc-label": "Valg av hoveddokument påkrevd",
      "requires-main-doc-description":
        "Hvis merket av, må brukeren velge hoveddokumentet fra de opplastede filene når denne oppgaven utføres. Dette anbefales sterkt for juridiske oppgaver som innebærer å svare på et brev eller en rettsavgjørelse eller lignende, da det strukturerer resultatet basert på dokumentet som besvares.",
      "requires-main-doc-placeholder": "Ja eller Nei",
      "requires-main-doc-explanation-default":
        "Et valg er påkrevd fordi dette bestemmer hvordan dokumentet vil bli bygget.",
      "requires-main-doc-explanation-yes":
        "Hvis 'Ja', må brukeren velge et hoveddokument når denne juridiske oppgaven startes. Dette dokumentet vil være sentralt i oppgavens arbeidsflyt.",
      "requires-main-doc-explanation-no":
        "Hvis 'Nei', vil den juridiske oppgaven fortsette uten å kreve et standard hoveddokument. Oppgaven vil skape et resultat mer dynamisk basert på alle opplastede dokumenter og den juridiske oppgaven.",
      "legal-task-type-label": "Type Juridisk Oppgave",
      "legal-task-type-placeholder": "Velg type juridisk oppgave",
      option: {
        mainDoc: "Hoveddokument-flyt",
        noMainDoc: "Ingen Hoveddokument-flyt",
        referenceFiles: "Referansefil-sammenligning",
      },
      "legal-task-type-explanation":
        "Velg hvordan den juridiske oppgaven skal håndtere dokumenter.",
      "legal-task-type-explanation-mainDoc":
        "Denne flyten krever at du velger et hoveddokument før du fortsetter.",
      "legal-task-type-explanation-noMainDoc":
        "Denne flyten fortsetter uten et primært dokument.",
      "legal-task-type-explanation-referenceFiles":
        "Denne flyten behandler grupper av regler og forskrifter mot gjennomgangsdokumenter.",
    },

    // New keys for Review Generator Prompt feature
    reviewGeneratorPromptButton: "Gjennomgå Generatorprompt",
    reviewGeneratorPromptButtonTooltip:
      "Se den nøyaktige promptmalen som ble brukt til å generere forslaget til juridisk oppgave. (Kun admin)",
    reviewGeneratorPromptTitle: "Gjennomgang av Generatorprompt",
    reviewPromptLabel: "Følgende prompt ble brukt for generering:",
    reviewPromptTextareaLabel: "Innhold i Generatorprompt",
  },

  // =========================
  // PERFORM LEGAL TASK
  // =========================
  performLegalTask: {
    title: "Utfør juridisk oppgave",
    noTaskfund: "Ingen juridiske oppgaver tilgjengelige.",
    noSubtskfund: "Ingen underkategorier tilgjengelige.",
    "loading-subcategory": "Laster underkategorier...",
    "select-category": "Velg kategori",
    "choose-task": "Velg juridisk oppgave å utføre",
    "duration-info":
      "Tiden for å utføre en juridisk oppgave avhenger av antall dokumenter i arbeidsområdet. Med mange dokumenter og en kompleks oppgave kan dette ta svært lang tid.",
    description:
      "Aktiver eller deaktiver knappen for juridisk oppgave i dokumentutkast.",
    successMessage: "Juridisk oppgave har blitt {{status}}",
    failureUpdateMessage:
      "Kunne ikke oppdatere innstilling for juridisk oppgave.",
    errorSubmitting:
      "Feil ved innsending av innstillinger for juridisk oppgave.",
    "additional-instructions-label": "Tilleggsinstruksjoner:",
    "custom-instructions-label": "Tilpassede instruksjoner",
    "custom-instructions-placeholder":
      "Skriv inn tilleggsinstruksjoner for den juridiske oppgaven (valgfritt)...",
    "select-main-document-label": "Velg hoveddokument (påkrevd)",
    "select-document-placeholder": "-- Velg et dokument --",
    selectReferenceFilesLabel: "Velg referansefiler (regler og forskrifter)",
    "warning-title": "Advarsel",
    "no-files-title": "Ingen filer tilgjengelige",
    "no-files-description":
      "Det er ingen filer i dette arbeidsområdet. Vennligst last opp minst én fil før du utfører en juridisk oppgave.",
    "settings-button":
      "Legg til eller rediger tilgjengelige juridiske oppgaver",
    settings: "Innstillinger for juridiske oppgaver",
    subStep: "Pågående eller køet trinn",
  },

  // =========================
  // LEGAL TASK PROMPT GENERATOR
  // =========================
  "legal-task-prompt-generator": {
    title: "Generator for juridisk oppgave-brukerprompt",
    description:
      "Automatisk forslag til tilpasset prompt for en juridisk oppgave",
    "task-description": "Beskrivelse av juridisk oppgave",
    "task-description-placeholder":
      "Beskriv den juridiske oppgaven du ønsker å utføre...",
    "specific-instructions": "Spesifikke instruksjoner eller kunnskap",
    "specific-instructions-description":
      "Inkluder spesielle instruksjoner eller ekspertise spesifikk for denne juridiske oppgaven",
    "specific-instructions-placeholder":
      "Legg til spesifikke instruksjoner, ekspertise eller kunnskap for å håndtere denne juridiske oppgaven...",
    "suggested-prompt": "Foreslått brukerprompt",
    "generation-prompt": "Prompt for generering",
    "create-task": "Opprett juridisk oppgave basert på dette forslaget",
    generating: "Genererer...",
    generate: "Generer forslag",
    "toast-success": "Prompt generert vellykket",
    "toast-fail": "Kunne ikke generere prompt",
    button: "Generer prompt",
    success: "Prompt generert vellykket",
    error: "Vennligst skriv inn et navn eller underkategori først",
    failed: "Kunne ikke generere prompt",
  },
};
