---
description: Guidelines for markdown formatting and rendering
globs: frontend/**/*.js,frontend/**/*.jsx,server/**/*.js
alwaysApply: false
---

# Markdown Formatting and Rendering

## Markdown Rendering Component
- Create centralized markdown rendering component based on DOCX document rendering
- Ensure consistent rendering across streaming and finalized messages
- Handle proper formatting of headers, lists, and paragraphs

## Line Breaks and Spacing
- Maintain proper line breaks before headers and lists
- Ensure bullet points appear on separate rows
- Ensure line breaks before numbered paragraphs
- Provide proper spacing between paragraphs

## Heading Formatting
- Format legal document headings with markdown numbering
- Ensure numbered paragraphs are aligned with numbered headings
- Include linebreaks after numbered paragraphs
- In Swedish locale, ensure headings have empty lines between numbered paragraphs

## List Formatting
- Ensure bullet points appear on separate rows
- Maintain consistent indentation for nested lists
- Use appropriate list markers (-, *, numbers) based on context
- Preserve spacing between list items

## Paragraph Alignment
- Align paragraph numbering with heading numbering
- Align text with headings
- Ensure consistent indentation across the document
- Handle text overflow appropriately

## Code Blocks
- Format code blocks with appropriate syntax highlighting
- Maintain proper indentation in code blocks
- Ensure code blocks are properly contained
- Handle long lines in code blocks appropriately

## Tables
- Render markdown tables with consistent column widths
- Handle cell alignment appropriately
- Ensure table headers are properly distinguished
- Handle complex tables with merged cells
