export default {
  // =========================
  // HEADER
  // =========================
  header: {
    account: "Account",
    login: "Login",
    "sign-out": "Sign out",
    "style-alignment": "Personal style alignment",
  },

  // =========================
  // FEATURE CARDS
  // =========================
  featureCards: {
    "draft-from-template-title": "Create document draft from template",
    "draft-from-template-description":
      "Use the function to, for example, create an AML policy, minutes for a general meeting, or a standardized arbitration agreement.",
    "complex-document-builder-title": "Perform complex legal task",
    "complex-document-builder-description":
      "Perfect when you, for example, need to review hundreds of documents before a company acquisition or draft a detailed statement of claim.",
  },
  // =========================
  // DEFAULT CHAT
  // =========================
  "default-chat": {
    welcome: "Welcome to IST Legal.",
    "choose-legal":
      "To get started, read the User Guide at the top of the page",
  },
  // =========================
  // MODULE DEFINITIONS
  // =========================
  module: {
    "legal-qa": "Legal Q&A",
    "document-drafting": "Document Drafting",
    "active-case": "Active Case",
  },
  // =========================
  // MOBILE DISCLAIMER
  // =========================
  mobile: {
    disclaimer:
      "DISCLAIMER: For the best experience and full access to all features, please use a computer to access the app.",
  },
  // =========================
  // ACTIVE CASE
  // =========================
  "active-case": {
    title: "Active Case Reference",
    placeholder: "Enter case reference number",
    "select-reference": "Select Reference",
    "warning-title": "Missing Reference Number",
    "warning-message":
      "No reference number has been set. Do you want to proceed without a reference number?",
  },
  // =========================
  // PROMPT ERRORS
  // =========================
  prompt: {
    error: {
      empty: "Prompt cannot be empty",
      upgrade: "Error upgrading prompt",
    },
    decline: "Decline",
  },
};
