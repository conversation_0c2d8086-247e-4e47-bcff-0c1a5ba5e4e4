import Sidebar from "@/components/SettingsSidebar";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { ArrowLeft } from "@phosphor-icons/react";
import paths from "@/utils/paths";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import AutoSubmit from "../components/AutoSubmit";
import AutoSpeak from "../components/AutoSpeak";
import ScrollThresholds from "../components/ScrollThresholds";

export default function ChatUISettings() {
  const { t } = useTranslation();

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="relative rounded-md w-full h-full overflow-y-scroll">
          <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[86px] md:py-6">
            <div className="w-full flex flex-row items-start gap-3.5 gap-y-1 pb-6 border-white light:border-theme-sidebar-border border-b-2 border-opacity-10">
              <Link to={paths.home()}>
                <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
              </Link>
              <div className="flex flex-col">
                <div className="items-center">
                  <p className="text-lg leading-6 font-bold text-foreground">
                    {t("chat-ui-settings.title")}
                  </p>
                </div>
                <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                  {t("chat-ui-settings.description")}
                </p>
              </div>
            </div>
            <AutoSubmit />
            <AutoSpeak />
            <ScrollThresholds />
          </div>
        </div>
      </div>
    </div>
  );
}
