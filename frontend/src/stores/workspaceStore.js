import { create } from "zustand";
import { persist } from "zustand/middleware";
import Workspace from "@/models/workspace";
import useUserStore from "@/stores/userStore";

// 3-hour refresh interval to allow admin workspace
// updates to be reflected more frequently
const REFRESH_INTERVAL = 3 * 60 * 60 * 1000;

const useWorkspaceStore = create(
  persist(
    (set, get) => ({
      moduleWorkspaces: {},
      lastFetchedModule: {},
      loadingModule: {},

      populatedWorkspaces: [],
      loadingPopulated: false,

      errors: {},

      refreshInterval: REFRESH_INTERVAL,

      fetchModuleWorkspaces: async (force = false) => {
        const moduleSlug = useUserStore.getState().selectedModule;
        const { moduleWorkspaces, lastFetchedModule, refreshInterval } = get();
        const last = lastFetchedModule[moduleSlug] || 0;
        if (
          !force &&
          Date.now() - last < refreshInterval &&
          Array.isArray(moduleWorkspaces[moduleSlug]) &&
          moduleWorkspaces[moduleSlug].length > 0
        ) {
          return moduleWorkspaces[moduleSlug];
        }
        set((state) => ({
          loadingModule: { ...state.loadingModule, [moduleSlug]: true },
          errors: { ...state.errors, [moduleSlug]: null },
        }));
        try {
          const list = await Workspace.all();
          set((state) => ({
            moduleWorkspaces: { ...state.moduleWorkspaces, [moduleSlug]: list },
            lastFetchedModule: {
              ...state.lastFetchedModule,
              [moduleSlug]: Date.now(),
            },
            loadingModule: { ...state.loadingModule, [moduleSlug]: false },
            errors: { ...state.errors, [moduleSlug]: null },
          }));
          return list;
        } catch (error) {
          set((state) => ({
            loadingModule: { ...state.loadingModule, [moduleSlug]: false },
            errors: { ...state.errors, [moduleSlug]: error.message },
          }));
          return [];
        }
      },

      fetchPopulatedWorkspaces: async () => {
        set((state) => ({
          loadingPopulated: true,
          errors: { ...state.errors, populated: null },
        }));
        try {
          const list = await Workspace.getPopulatedWorkspaces();
          set((state) => ({
            populatedWorkspaces: list,
            loadingPopulated: false,
            errors: { ...state.errors, populated: null },
          }));
          return list;
        } catch (error) {
          set((state) => ({
            loadingPopulated: false,
            errors: { ...state.errors, populated: error.message },
          }));
          return [];
        }
      },

      addWorkspace: (workspace) =>
        set((state) => {
          const moduleSlug = workspace.type;
          const modules = { ...state.moduleWorkspaces };
          modules[moduleSlug] = modules[moduleSlug]
            ? [...modules[moduleSlug], workspace]
            : [workspace];
          return { moduleWorkspaces: modules };
        }),

      removeWorkspace: (workspaceSlug) =>
        set((state) => {
          const modules = Object.fromEntries(
            Object.entries(state.moduleWorkspaces).map(([mod, list]) => [
              mod,
              list.filter((w) => w.slug !== workspaceSlug),
            ])
          );
          const populated = state.populatedWorkspaces.filter(
            (w) => w.slug !== workspaceSlug
          );
          return { moduleWorkspaces: modules, populatedWorkspaces: populated };
        }),

      updateWorkspace: (updatedWorkspace) =>
        set((state) => {
          const moduleSlug = updatedWorkspace.type;
          const newModuleWorkspaces = { ...state.moduleWorkspaces };
          if (newModuleWorkspaces[moduleSlug]) {
            newModuleWorkspaces[moduleSlug] = newModuleWorkspaces[
              moduleSlug
            ].map((ws) =>
              ws.id === updatedWorkspace.id ? updatedWorkspace : ws
            );
          }

          const newPopulatedWorkspaces = state.populatedWorkspaces.map((ws) =>
            ws.id === updatedWorkspace.id ? updatedWorkspace : ws
          );

          return {
            moduleWorkspaces: newModuleWorkspaces,
            populatedWorkspaces: newPopulatedWorkspaces,
          };
        }),

      markWorkspacePopulated: (workspace) =>
        set((state) => {
          // only add if not already present
          if (state.populatedWorkspaces.some((w) => w.id === workspace.id)) {
            return {};
          }
          return {
            populatedWorkspaces: [...state.populatedWorkspaces, workspace],
          };
        }),
    }),
    {
      name: "workspace-store",
      partialize: (state) => ({
        moduleWorkspaces: state.moduleWorkspaces,
        lastFetchedModule: state.lastFetchedModule,
        populatedWorkspaces: state.populatedWorkspaces,
        refreshInterval: state.refreshInterval,
      }),
    }
  )
);

export const useModuleWorkspaces = () =>
  useWorkspaceStore((state) => state.moduleWorkspaces);

export const useRemoveWorkspace = () =>
  useWorkspaceStore((state) => state.removeWorkspace);

export const useUpdateWorkspace = () =>
  useWorkspaceStore((state) => state.updateWorkspace);

export default useWorkspaceStore;
