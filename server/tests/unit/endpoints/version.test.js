const request = require("supertest");
const express = require("express");
const fs = require("fs");
const path = require("path");
const { systemEndpoints } = require("../../../endpoints/system");

jest.mock("../../../utils/PasswordRecovery");

// Mock fs module
jest.mock("fs");

// Mock language detection
jest.mock("../../../utils/helpers/languageDetection", () => ({
  getPreferredLanguage: jest.fn().mockResolvedValue("en"),
  getLocalizedDescription: jest.fn(
    (versionObj) => versionObj?.description || "Version information available"
  ),
}));

describe("Version Endpoint", () => {
  let app;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    systemEndpoints(app);
    jest.clearAllMocks();
    // Suppress console.error for tests
    jest.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("GET /version", () => {
    it("should return version data when file exists and is valid (old format)", async () => {
      const mockVersionData = {
        version: "1.1.0",
        description:
          "Added new document builder features and improved chat performance",
        timestamp: "2024-01-15T10:30:00Z",
      };

      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue(JSON.stringify(mockVersionData));

      // Mock console.log to capture backend logging
      const consoleSpy = jest
        .spyOn(console, "log")
        .mockImplementation(() => {});

      const response = await request(app).get("/version").expect(200);

      // Should return only version and description (timestamp excluded)
      expect(response.body).toEqual({
        success: true,
        version: "1.1.0",
        description:
          "Added new document builder features and improved chat performance",
      });

      // Should log the full version data including timestamp for backend tracking
      expect(consoleSpy).toHaveBeenCalledWith("Version info accessed:", {
        version: "1.1.0",
        timestamp: "2024-01-15T10:30:00Z",
        description:
          "Added new document builder features and improved chat performance",
        language: "en",
      });

      expect(fs.existsSync).toHaveBeenCalledWith(
        expect.stringContaining("version.json")
      );
      expect(fs.readFileSync).toHaveBeenCalledWith(
        expect.stringContaining("version.json"),
        "utf8"
      );

      consoleSpy.mockRestore();
    });

    it("should return highest version data when file contains multiple versions (new format)", async () => {
      const mockVersionData = {
        versions: [
          {
            version: "1.0.0",
            description: "Initial release",
            timestamp: "2024-01-01T10:00:00Z",
          },
          {
            version: "1.2.0",
            description: "Latest features and improvements",
            timestamp: "2024-01-20T10:00:00Z",
          },
          {
            version: "1.1.0",
            description: "Middle version",
            timestamp: "2024-01-10T10:00:00Z",
          },
        ],
      };

      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue(JSON.stringify(mockVersionData));

      // Mock console.log to capture backend logging
      const consoleSpy = jest
        .spyOn(console, "log")
        .mockImplementation(() => {});

      const response = await request(app).get("/version").expect(200);

      // Should return the highest version (1.2.0)
      expect(response.body).toEqual({
        success: true,
        version: "1.2.0",
        description: "Latest features and improvements",
      });

      // Should log the highest version data
      expect(consoleSpy).toHaveBeenCalledWith("Version info accessed:", {
        version: "1.2.0",
        timestamp: "2024-01-20T10:00:00Z",
        description: "Latest features and improvements",
        language: "en",
      });

      consoleSpy.mockRestore();
    });

    it("should return 500 when no valid version data is found", async () => {
      const mockInvalidData = {
        versions: [],
      };

      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue(JSON.stringify(mockInvalidData));

      const response = await request(app).get("/version").expect(500);

      expect(response.body).toEqual({
        success: false,
        error: "No valid version data found",
      });
    });

    it("should return 404 when version file does not exist", async () => {
      fs.existsSync.mockReturnValue(false);

      const response = await request(app).get("/version").expect(404);

      expect(response.body).toEqual({
        success: false,
        error: "Version file not found",
      });

      expect(fs.existsSync).toHaveBeenCalled();
      expect(fs.readFileSync).not.toHaveBeenCalled();
    });

    it("should return 500 when file exists but contains invalid JSON", async () => {
      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue("invalid json content");

      const response = await request(app).get("/version").expect(500);

      expect(response.body).toEqual({
        success: false,
        error: "Failed to read version information",
      });

      expect(console.error).toHaveBeenCalledWith(
        "Error reading version file:",
        expect.any(Error)
      );
    });

    it("should return 500 when file read operation fails", async () => {
      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockImplementation(() => {
        throw new Error("File read error");
      });

      const response = await request(app).get("/version").expect(500);

      expect(response.body).toEqual({
        success: false,
        error: "Failed to read version information",
      });

      expect(console.error).toHaveBeenCalledWith(
        "Error reading version file:",
        expect.any(Error)
      );
    });

    it("should handle version file with minimal data", async () => {
      const mockMinimalData = {
        version: "1.0.0",
        // description is missing
      };

      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue(JSON.stringify(mockMinimalData));

      const response = await request(app).get("/version").expect(200);

      expect(response.body).toEqual({
        success: true,
        version: "1.0.0",
        description: "Version information available", // Mock returns this fallback
      });
    });

    it("should handle version file with extra properties", async () => {
      const mockExtendedData = {
        version: "1.2.0",
        description: "Test version",
        timestamp: "2024-01-15T10:30:00Z",
        buildDate: "2024-01-01",
        commit: "abc123",
      };

      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue(JSON.stringify(mockExtendedData));

      // Mock console.log to capture backend logging
      const consoleSpy = jest
        .spyOn(console, "log")
        .mockImplementation(() => {});

      const response = await request(app).get("/version").expect(200);

      // Should return only version and description (other fields excluded)
      expect(response.body).toEqual({
        success: true,
        version: "1.2.0",
        description: "Test version",
      });

      // Should log the full version data including timestamp for backend tracking
      expect(consoleSpy).toHaveBeenCalledWith("Version info accessed:", {
        version: "1.2.0",
        timestamp: "2024-01-15T10:30:00Z",
        description: "Test version",
        language: "en",
      });

      consoleSpy.mockRestore();
    });

    it("should handle version file without timestamp", async () => {
      const mockDataWithoutTimestamp = {
        version: "1.3.0",
        description: "Version without timestamp",
      };

      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue(JSON.stringify(mockDataWithoutTimestamp));

      // Mock console.log to capture backend logging
      const consoleSpy = jest
        .spyOn(console, "log")
        .mockImplementation(() => {});

      const response = await request(app).get("/version").expect(200);

      expect(response.body).toEqual({
        success: true,
        version: "1.3.0",
        description: "Version without timestamp",
      });

      // Should log version data even without timestamp
      expect(consoleSpy).toHaveBeenCalledWith("Version info accessed:", {
        version: "1.3.0",
        timestamp: undefined,
        description: "Version without timestamp",
        language: "en",
      });

      consoleSpy.mockRestore();
    });

    it("should use correct file path", async () => {
      const mockVersionData = { version: "1.0.0" };

      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue(JSON.stringify(mockVersionData));

      await request(app).get("/version").expect(200);

      // Verify the path construction
      const expectedPath = path.join(
        __dirname,
        "..",
        "..",
        "..",
        "..",
        "version.json"
      );
      expect(fs.existsSync).toHaveBeenCalledWith(expectedPath);
      expect(fs.readFileSync).toHaveBeenCalledWith(expectedPath, "utf8");
    });

    it("should return proper content-type header", async () => {
      const mockVersionData = { version: "1.0.0" };

      fs.existsSync.mockReturnValue(true);
      fs.readFileSync.mockReturnValue(JSON.stringify(mockVersionData));

      const response = await request(app).get("/version").expect(200);

      expect(response.headers["content-type"]).toMatch(/application\/json/);
    });

    describe("Localized Descriptions", () => {
      const {
        getPreferredLanguage,
        getLocalizedDescription,
      } = require("../../../utils/helpers/languageDetection");

      beforeEach(() => {
        // Reset mocks for each test
        getPreferredLanguage.mockClear();
        getLocalizedDescription.mockClear();
      });

      it("should return localized description when system language is Swedish", async () => {
        const mockVersionData = {
          version: "1.1.0",
          description: "Added new features",
          "description-sv": "Lade till nya funktioner",
          timestamp: "2024-01-15T10:30:00Z",
        };

        fs.existsSync.mockReturnValue(true);
        fs.readFileSync.mockReturnValue(JSON.stringify(mockVersionData));

        // Mock Swedish language preference
        getPreferredLanguage.mockResolvedValue("sv");
        getLocalizedDescription.mockReturnValue("Lade till nya funktioner");

        const response = await request(app).get("/version").expect(200);

        expect(response.body).toEqual({
          success: true,
          version: "1.1.0",
          description: "Lade till nya funktioner",
        });

        expect(getPreferredLanguage).toHaveBeenCalled();
        expect(getLocalizedDescription).toHaveBeenCalledWith(
          mockVersionData,
          "sv"
        );
      });

      it("should return localized description for French system language", async () => {
        const mockVersionData = {
          versions: [
            {
              version: "1.2.0",
              description: "Latest improvements",
              "description-fr": "Dernières améliorations",
              timestamp: "2024-01-20T10:00:00Z",
            },
          ],
        };

        fs.existsSync.mockReturnValue(true);
        fs.readFileSync.mockReturnValue(JSON.stringify(mockVersionData));

        // Mock French language preference
        getPreferredLanguage.mockResolvedValue("fr");
        getLocalizedDescription.mockReturnValue("Dernières améliorations");

        const response = await request(app).get("/version").expect(200);

        expect(response.body).toEqual({
          success: true,
          version: "1.2.0",
          description: "Dernières améliorations",
        });

        expect(getPreferredLanguage).toHaveBeenCalled();
        expect(getLocalizedDescription).toHaveBeenCalledWith(
          expect.objectContaining({
            version: "1.2.0",
            description: "Latest improvements",
            "description-fr": "Dernières améliorations",
          }),
          "fr"
        );
      });

      it("should fall back to default description when localized version not available", async () => {
        const mockVersionData = {
          version: "1.0.0",
          description: "Initial release",
          "description-sv": "Första utgivningen",
          timestamp: "2024-01-01T10:00:00Z",
        };

        fs.existsSync.mockReturnValue(true);
        fs.readFileSync.mockReturnValue(JSON.stringify(mockVersionData));

        // Mock German language preference (not available in data)
        getPreferredLanguage.mockResolvedValue("de");
        getLocalizedDescription.mockReturnValue("Initial release"); // Falls back to default

        const response = await request(app).get("/version").expect(200);

        expect(response.body).toEqual({
          success: true,
          version: "1.0.0",
          description: "Initial release",
        });

        expect(getLocalizedDescription).toHaveBeenCalledWith(
          mockVersionData,
          "de"
        );
      });

      it("should handle language detection errors gracefully", async () => {
        const mockVersionData = {
          version: "1.0.0",
          description: "Default description",
          timestamp: "2024-01-01T10:00:00Z",
        };

        fs.existsSync.mockReturnValue(true);
        fs.readFileSync.mockReturnValue(JSON.stringify(mockVersionData));

        // Mock language detection falling back to default language
        getPreferredLanguage.mockResolvedValue("en"); // Should still resolve, not reject
        getLocalizedDescription.mockReturnValue("Default description");

        const response = await request(app).get("/version").expect(200);

        expect(response.body).toEqual({
          success: true,
          version: "1.0.0",
          description: "Default description",
        });
      });
    });
  });
});
