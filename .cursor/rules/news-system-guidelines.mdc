---
description:
globs:
alwaysApply: false
---
# News System Guidelines

## Overview

The IST Legal platform includes a comprehensive news system that informs users about new features, updates, and important announcements. When implementing major features or significant changes, you **MUST** consider adding a system news item to inform users.

## When to Add System News Items

### Always Add News Items For:
- **New major features** (e.g., new modules, significant UI changes, new integrations)
- **Breaking changes** that affect user workflows
- **Important security updates** or policy changes
- **New user-facing tools or capabilities**
- **Significant performance improvements** that users will notice
- **Changes to existing features** that alter user experience

### Consider Adding News Items For:
- **Bug fixes** that resolve major user pain points
- **UI/UX improvements** that enhance usability
- **New configuration options** that users should know about
- **Integration updates** that expand platform capabilities

### Don't Add News Items For:
- **Internal refactoring** that doesn't affect user experience
- **Minor bug fixes** that users wouldn't notice
- **Development tooling changes**
- **Code quality improvements** without user impact

## System News Architecture

### Server as Single Source of Truth
- **System News Data**: `server/data/systemNewsItems.js` - **PRIMARY** source for all system news items
- **API Endpoint**: `/api/news/system` - Server endpoint that serves system news data

### Frontend Components
- **News Data**: `frontend/src/data/news/index.js` - Fetches system news from server API
- **News Hook**: `frontend/src/hooks/useNews.js` - React hook for managing news state
- **News Display**: Various components for showing news to users

### Backend Components
- **News Model**: `server/models/newsMessage.js` - Database operations for local news
- **News Endpoints**: `server/endpoints/news.js` - API endpoints, serves system news from server data
- **System News Module**: `server/data/systemNewsItems.js` - Contains all system news items and helper functions

### Translation Files
- **Localization**: `frontend/src/locales/[lang]/newsSystemItems.js` - Translations for all supported languages

### Integration Testing
- **Server News Tests**: `server/tests/integration/serverNewsSystem.test.js` - Comprehensive tests for server-based news system

## How to Create a System News Item

### Step 1: Add News Item to Server Data File

**IMPORTANT**: Add your news item to the server data file `server/data/systemNewsItems.js` (NOT the frontend data file):

```javascript
// In server/data/systemNewsItems.js
const systemNewsItems = [
  // ... existing items
  {
    id: "system-new-feature-2024", // Use descriptive, unique ID with year
    title: "New Feature Available", // Fallback English title
    content: "We've added an exciting new feature...", // Fallback English content
    titleKey: "news-system-items.system-new-feature-2024.title", // Translation key for title
    contentKey: "news-system-items.system-new-feature-2024.content", // Translation key for content
    priority: "high", // Priority: "low", "medium", "high", "urgent"
    isActive: true, // Set to true to make it visible
    isSystemNews: true, // Always true for system news
    targetRoles: null, // null for all users, or ["admin", "manager"] for specific roles
    expiresAt: null, // null for no expiration, or Date string for expiration
    createdAt: "2024-01-15T00:00:00Z", // ISO date string
  },
];

module.exports = {
  systemNewsItems,
  // ... helper functions
};
```

**Why Server as Source of Truth?**
- **True Single Source**: Server completely owns the data
- **Standard Architecture**: Follows typical REST API patterns
- **Clean Separation**: Server handles data, frontend consumes via API
- **Better Caching**: Frontend can implement proper cache invalidation
- **Easier Testing**: Clear separation between server logic and frontend consumption

### Step 2: Add Translations for ALL Languages

**CRITICAL**: You must add translations to ALL supported language files. Missing translations will cause errors.

#### Supported Languages:
- English (`en`)
- Swedish (`sv`)
- French (`fr`)
- German (`de`)
- Norwegian (`no`)
- Polish (`pl`)
- Kinyarwanda (`rw`)

#### Translation File Structure:

For each language, add to `frontend/src/locales/[lang]/newsSystemItems.js`:

```javascript
// Example: frontend/src/locales/en/newsSystemItems.js
const TRANSLATIONS = {
  "news-system-items": {
    "system-new-feature-2024": {
      title: "New Feature Available",
      content: "We've added an exciting new feature that allows you to [describe feature]. This enhancement will help you [explain benefits]. To access this feature, go to [location] and [instructions]."
    },
    // ... other news items
  },
};

export default TRANSLATIONS;
```

#### Translation Guidelines:

1. **Use Professional Language**: Formal, clear, and professional tone
2. **Be Specific**: Explain what the feature does and how to use it
3. **Include Instructions**: Tell users where to find and how to use new features
4. **Maintain Consistency**: Use consistent terminology across languages
5. **Preserve Markdown**: Keep any markdown formatting (links, bold text, lists)
6. **Cultural Sensitivity**: Ensure translations are appropriate for each locale

#### Example Translations:

**English (`en/newsSystemItems.js`):**
```javascript
"system-document-builder-2024": {
  title: "New Document Builder Available",
  content: "We've introduced a powerful Document Builder that allows you to create complex legal documents using AI assistance. Access it from **General Settings > Document Builder** to configure custom prompts and templates for your legal drafting needs."
}
```

**Swedish (`sv/newsSystemItems.js`):**
```javascript
"system-document-builder-2024": {
  title: "Ny Dokumentbyggare Tillgänglig",
  content: "Vi har introducerat en kraftfull Dokumentbyggare som låter dig skapa komplexa juridiska dokument med AI-assistans. Kom åt den från **Allmänna Inställningar > Dokumentbyggare** för att konfigurera anpassade prompter och mallar för dina juridiska skrivbehov."
}
```

**French (`fr/newsSystemItems.js`):**
```javascript
"system-document-builder-2024": {
  title: "Nouveau Générateur de Documents Disponible",
  content: "Nous avons introduit un puissant Générateur de Documents qui vous permet de créer des documents juridiques complexes avec l'assistance de l'IA. Accédez-y depuis **Paramètres Généraux > Générateur de Documents** pour configurer des invites et modèles personnalisés pour vos besoins de rédaction juridique."
}
```

### Step 3: Test Your News Item

1. **Verify Data Structure**: Ensure your news item has all required fields
2. **Test Translations**: Check that all translation keys resolve correctly
3. **Test Display**: Verify the news appears in the UI correctly
4. **Test Dismissal**: Ensure users can dismiss the news item
5. **Test Filtering**: If using `targetRoles`, verify role-based filtering works

### Step 4: Update Tests (If Needed)

If your news item introduces new patterns or edge cases, update the relevant tests:
- `server/tests/integration/serverNewsSystem.test.js` - **PRIMARY** test for server-based news system
- `frontend/src/__tests__/newsSystem.test.js` - Frontend-specific news functionality
- `frontend/src/__tests__/newsTranslations.test.js` - Translation validation tests

**Note**: The server news integration test automatically validates that your new news item:
- Is served correctly by the API endpoint
- Has consistent data between server module and API response
- Includes all required fields
- Uses proper translation keys
- Follows the expected structure

## News Item Configuration Options

### Priority Levels
- **`"urgent"`**: Red badge, appears first, for critical announcements
- **`"high"`**: Orange badge, high visibility, for important features
- **`"medium"`**: Blue badge, standard priority, for regular updates
- **`"low"`**: Gray badge, low visibility, for minor announcements

### Target Roles
- **`null`**: Visible to all users (default)
- **`["admin"]`**: Only visible to admin users
- **`["admin", "manager"]`**: Visible to admin and manager users
- **`["default"]`**: Only visible to default users

### Expiration
- **`null`**: Never expires (default)
- **`"2024-12-31T23:59:59Z"`**: Expires on specific date

## Best Practices

### Content Guidelines
1. **Be Concise**: Keep titles under 60 characters, content under 300 words
2. **Use Action Words**: "New", "Improved", "Enhanced", "Added"
3. **Include Benefits**: Explain how the feature helps users
4. **Provide Context**: Link to documentation or help resources
5. **Use Markdown**: Format important text with **bold** or [links](mdc:url)

### Technical Guidelines
1. **Unique IDs**: Use descriptive IDs with year suffix (e.g., `system-feature-name-2024`)
2. **Consistent Naming**: Follow the pattern `system-[feature-name]-[year]`
3. **Translation Keys**: Use the pattern `news-system-items.[id].title/content`
4. **Date Format**: Use ISO 8601 format for dates (`YYYY-MM-DDTHH:mm:ssZ`)

### Localization Guidelines
1. **Complete Coverage**: Add translations to ALL 7 supported languages
2. **Professional Tone**: Use formal, business-appropriate language
3. **Cultural Adaptation**: Adapt content for local business practices when needed
4. **Consistent Terminology**: Use the same terms as the UI in each language
5. **Review Process**: Have native speakers review translations when possible

## Common Mistakes to Avoid

1. **Wrong File Location**: Adding news items to `frontend/src/data/news/index.js` instead of `server/data/systemNewsItems.js`
2. **Missing Translations**: Forgetting to add translations for all languages
3. **Incorrect Translation Keys**: Mismatched keys between data and translation files
4. **Wrong Priority**: Using inappropriate priority levels
5. **Vague Content**: Not explaining what the feature does or how to use it
6. **Broken Markdown**: Malformed links or formatting
7. **Inconsistent Dates**: Using wrong date formats or inconsistent timestamps
8. **Missing Tests**: Not running integration tests to verify server-based news system
9. **Data Duplication**: Trying to maintain news data in multiple places instead of using server as single source

## Review Checklist

Before submitting your PR with a new system news item:

- [ ] Added news item to `server/data/systemNewsItems.js` (**NOT** frontend data file)
- [ ] Added translations to ALL 7 language files
- [ ] Used appropriate priority level
- [ ] Included clear, actionable content
- [ ] Used proper markdown formatting
- [ ] Set correct target roles (if applicable)
- [ ] Used ISO date format for timestamps
- [ ] Tested news item display in UI
- [ ] Tested news item dismissal
- [ ] Ran server news system integration tests (`npm test server/tests/integration/serverNewsSystem.test.js`)
- [ ] Updated tests if needed
- [ ] Verified all translation keys resolve correctly
- [ ] Confirmed both frontend and backend show identical news data

## Examples

### Simple Feature Announcement
```javascript
{
  id: "system-dark-mode-2024",
  title: "Dark Mode Now Available",
  content: "Switch to dark mode for a better viewing experience in low-light environments.",
  titleKey: "news-system-items.system-dark-mode-2024.title",
  contentKey: "news-system-items.system-dark-mode-2024.content",
  priority: "medium",
  isActive: true,
  isSystemNews: true,
  targetRoles: null,
  expiresAt: null,
  createdAt: "2024-01-15T00:00:00Z",
}
```

### Admin-Only Security Update
```javascript
{
  id: "system-security-update-2024",
  title: "Important Security Update",
  content: "New security features have been added to protect user data. Please review the updated security settings.",
  titleKey: "news-system-items.system-security-update-2024.title",
  contentKey: "news-system-items.system-security-update-2024.content",
  priority: "urgent",
  isActive: true,
  isSystemNews: true,
  targetRoles: ["admin"],
  expiresAt: "2024-06-01T00:00:00Z",
  createdAt: "2024-01-15T00:00:00Z",
}
```

## Related Documentation

- [Internationalization Guidelines](mdc:internationalization-with-i18n.mdc)
- [User Guide Documentation](mdc:user-guide-documentation.mdc)
- [Frontend Development Guidelines](mdc:frontend-development.mdc)

---

**Remember**: System news items are often the first way users learn about new features. Take time to craft clear, helpful announcements that enhance the user experience and drive feature adoption.
