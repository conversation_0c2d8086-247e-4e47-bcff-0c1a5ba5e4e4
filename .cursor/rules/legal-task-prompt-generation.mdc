---
description: Guidelines for implementing legal task prompt generation
globs: "frontend/src/components/Modals/**/*.{js,jsx,ts,tsx}"
alwaysApply: false
---
# Legal Task Prompt Generation

## Overview

The legal task prompt generation feature allows users to automatically generate high-quality prompts for legal tasks based on the task name and description. This feature is available in both the Create New Legal Task and Edit Legal Task modals.

## Implementation Guidelines

### Button Placement

The "Generate Prompt" button should be placed next to the legal prompt field and should only appear when there is text in the name/subcategory field:

```jsx
{(subCategory || name || selectedCategory) && (
  <Button
    type="button"
    variant="outline"
    onClick={handleGeneratePrompt}
    disabled={isGeneratingPrompt}
    className="flex items-center gap-2"
  >
    {isGeneratingPrompt ? (
      <ImSpinner2 className="animate-spin-fast" />
    ) : (
      <MagicWand size={16} />
    )}
    {t("document-builder.create-task.generate-prompt", "Generate Prompt")}
  </Button>
)}
```

### Prompt Generation Function

The prompt generation function should:

1. Check if there is a task description (name or subcategory)
2. Determine if the task requires a main document (e.g., from a state variable like `requiresMainDocument`).
3. Show a loading state while generating.
4. Get the document builder prompts for context by calling `System.getDocumentBuilder({ flowType })`, where `flowType` is `'mainDoc'` or `'noMainDoc'` based on the determination in step 2. This API call now returns not only the resolved prompt values but also a `promptFlowStructure` array that defines which prompts are relevant for the given `flowType` and how they should be labeled.
5. Create a prompt template with enhanced context using `buildCDBPromptTemplate`, passing the `requiresMainDocument` flag to it. This utility now also embeds:
    - Comprehensive CDB documentation (including an overview from `streamCDB.md`, flow details from `legal_drafting_flows.md`, and a list of all default prompts), which is always fetched from a server endpoint (`System.getCDBDocumentation()`).
    - An explicit statement about whether the task is for the "Main Document Flow" or "No Main Document Flow".
    - A dynamically generated list of the current prompt templates relevant to the active flow (including shared prompts like Document Summary, Relevance, Section Drafting, Section Legal Issues Identification, and flow-specific ones like Select Main Document or flow-specific Topics & Sections), using the `promptFlowStructure` and resolved values from the `System.getDocumentBuilder` call.
6. Call the `System.generateLegalTaskPrompt` method with the task description, the constructed prompt template, and the `requiresMainDocument` flag.
7. Update the legal prompt field with the generated prompt.
8. Show a success or error message.

```jsx
const handleGeneratePrompt = async () => {
  // Get the task description from either the name or subCategory field
  const taskDescription = subCategory || (isNewCategory ? name : selectedCategory) || "";

  // Assume `requiresMainDocument` (boolean) is available in the component's scope/state

  if (!taskDescription) {
    showToast(t("document-builder.create-task.generate-prompt-error", "Please enter a name or subcategory first"), "error");
    return;
  }

  setIsGeneratingPrompt(true);

  try {
    // The buildCDBPromptTemplate utility now handles calling System.getDocumentBuilder with the correct flowType
    const promptTemplate = await buildCDBPromptTemplate({
      taskDescription,
      description: currentDescription, // Assuming `currentDescription` holds the task's detailed description
      requiresMainDocumentFlag: requiresMainDocument,
    });

    const response = await System.generateLegalTaskPrompt(
      taskDescription,
      promptTemplate,
      requiresMainDocument // Pass this to the backend so it can also use flow-specific logic if needed
    );

    if (response.success) {
      setLegalPrompt(response.prompt);

      // Auto-resize the textarea
      setTimeout(() => {
        const textarea = document.getElementById("legalPrompt");
        if (textarea) {
          const event = { target: textarea };
          autoResizeTextarea(event);
        }
      }, 0);

      showToast(t("document-builder.create-task.generate-prompt-success", "Prompt generated successfully"), "success");
    } else {
      throw new Error(response.error || "Failed to generate prompt");
    }
  } catch (error) {
    console.error("Error generating legal task prompt:", error);
    showToast(
      error.message || t("document-builder.create-task.generate-prompt-error", "Failed to generate prompt"),
      "error"
    );
  } finally {
    setIsGeneratingPrompt(false);
  }
};
```

### Internationalization

Always use translation keys for all user-facing text:

```jsx
t("document-builder.create-task.generate-prompt", "Generate Prompt")
t("document-builder.create-task.generate-prompt-success", "Prompt generated successfully")
t("document-builder.create-task.generate-prompt-error", "Please enter a name or subcategory first")
```

### State Management

Include state for tracking the loading state:

```jsx
const [isGeneratingPrompt, setIsGeneratingPrompt] = useState(false);
```

## Best Practices

1. **Conditional Rendering**: Only show the button when there is text in the name/subcategory field
2. **Loading State**: Show a loading spinner while generating the prompt
3. **Error Handling**: Display clear error messages if the generation fails
4. **Auto-Resize**: Automatically resize the textarea after inserting the generated prompt
5. **Internationalization**: Use translation keys for all user-facing text
6. **Context**: Include relevant context in the prompt template to improve the quality of the generated prompt

## Developer Note: Adding New Configurable Prompts for Legal Drafting

When adding a new prompt to `server/utils/chats/prompts/legalDrafting.js` that is intended to be configurable via a system setting (i.e., it will have a `systemSettingName`):

1.  **Define in `SystemSettings`**: Ensure the `systemSettingName` (e.g., `"cdb_new_prompt_system_prompt"`) is added to the `SystemSettings.protectedFields` array (or `supportedFields` as appropriate) in `server/models/systemSettings.js`.
2.  **Add Validation (if needed)**: If the new setting requires specific validation (e.g., it must be a number, boolean, or follow a particular string format), add a corresponding validation function to the `SystemSettings.validations` object in `server/models/systemSettings.js`.
3.  **Ensure Retrievability**: Verify that the new setting key will be correctly retrieved by `SystemSettings.currentSettings()` and other relevant settings aggregation methods (like `llmPreferenceKeys` if it's a provider-specific setting). This usually means ensuring it's included in the internal `KEY_MAPPING` or has a default value defined in `SystemSettings.settings()`.
4.  **Use Helper for Consistency**: The `generateSystemSettingPKeyForLegalDrafting` helper in `legalDrafting.js` can be used to create `systemSettingName` values with a consistent pattern (e.g., `cdb_yourpromptkey_system_prompt`).
5.  **Automated Test Verification**: The test suite `server/utils/chats/prompts/__tests__/legalDrafting.test.js` dynamically loads the known setting keys from `SystemSettings` (using `jest.isolateModulesAsync` and mocking of `SystemSettings` dependencies) and will automatically verify that any new `systemSettingName` corresponds to a key in `SystemSettings.protectedFields` or `SystemSettings.supportedFields`. Ensure your new key is correctly defined in `systemSettings.js` for this test to pass.

Following these steps will ensure that your new configurable prompt is properly integrated into the system settings and is testable.
