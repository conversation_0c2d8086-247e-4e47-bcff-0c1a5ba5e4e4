const fs = require("fs");
const path = require("path");
const { Workspace } = require("../../../models/workspace");
const { getUserDocumentPathName } = require("../../../endpoints/document");
const { getLL<PERSON>rovider } = require("../../helpers");
const { writeResponseChunk } = require("../../helpers/chat/responses");
const { getResolvedPrompts } = require("../helpers/promptManager");

const {
  generateDocumentDescription,
  generateDocumentRelevance,
  saveDocumentDescriptions,
  generateSectionListFromSummaries,
  generateDocumentSectionIndices,
  fillTemplate,
  combineSectionOutputs,
} = require("../helpers/documentProcessing");

async function getSnippetsForSection(
  documentNames,
  workspacePath,
  maxLengthPerDoc = 1000
) {
  let snippets = "";
  for (const docName of documentNames) {
    const docPath = path.join(
      workspacePath,
      docName.replace(/\.json$/, "") + ".json"
    );
    if (fs.existsSync(docPath)) {
      try {
        const raw = fs.readFileSync(docPath, "utf8");
        const parsed = JSON.parse(raw);
        snippets += `Document: ${docName}\n${(parsed.pageContent || "").substring(0, maxLengthPerDoc)}\n\n`;
      } catch (e) {
        console.warn(
          `Could not read/parse ${docName} for snippet: ${e.message}`
        );
      }
    }
  }
  return snippets.trim();
}

// Utility: sanitize potential slug strings from LLM
const sanitizeSlug = (slugStr) => {
  if (!slugStr || typeof slugStr !== "string") return null;
  return slugStr
    .trim()
    .toLowerCase()
    .replace(/\s+/g, "-")
    .replace(/[^a-z0-9-_]/g, "");
};

async function runReferenceFilesFlow(options) {
  const { abortSignal = null } = options;

  const AllPrompts = await getResolvedPrompts();

  const checkAbort = () => {
    if (abortSignal && abortSignal.aborted) {
      throw new Error("Client aborted - terminating reference files flow.");
    }
  };
  const {
    request,
    response,
    workspace,
    message: legalTask,
    chatMode = "chat",
    user = null,
    thread = null,
    attachments = [],
    chatId,
    isCanvasChat = false,
    preventChatCreation = false,
    settings_suffix = "",
    invoice_ref,
    vectorSearchMode = "default",
    hasUploadedFile = false,
    displayMessage = null,
    useDeepSearch = false,
    cdbOptions = [],
  } = options;

  const customInstructions = cdbOptions[1] || "";
  const metrics = {};

  const sendProgress = (data = {}) => {
    // Early exit if client has aborted
    try {
      checkAbort();
    } catch (e) {
      throw e;
    }

    const translated = { ...data };

    if (
      translated.progress === undefined &&
      typeof translated.status === "string"
    ) {
      switch (translated.status) {
        case "starting":
        case "in_progress":
          translated.progress = -1; // loading state
          break;
        case "complete":
          translated.progress = 100; // done
          break;
        case "error":
          translated.progress = -2; // failed
          break;
        default:
          // keep undefined for unrecognised status
          break;
      }
    }

    try {
      writeResponseChunk(response, {
        uuid: chatId,
        type: "cdbProgress",
        flowType: "referenceFiles",
        ...translated,
      });
    } catch (err) {
      console.error(
        "[REFERENCE FILES FLOW] Failed to send progress chunk",
        err
      );
    }
  };

  let LLMConnector;
  if (process.env.LLM_PROVIDER_CDB) {
    let modelPrefCDB;
    switch (process.env.LLM_PROVIDER_CDB.toLowerCase()) {
      case "openai":
        modelPrefCDB = process.env.OPEN_MODEL_PREF_CDB;
        break;
      case "anthropic":
        modelPrefCDB = process.env.ANTHROPIC_MODEL_PREF_CDB;
        break;
      case "gemini":
        modelPrefCDB = process.env.GEMINI_LLM_MODEL_PREF_CDB;
        break;
      case "lmstudio":
        modelPrefCDB = process.env.LMSTUDIO_MODEL_PREF_CDB;
        break;
      // Add other cases for different CDB providers if supported
      default:
        modelPrefCDB = process.env.OPEN_MODEL_PREF_CDB; // Fallback model preference for CDB
    }
    LLMConnector = getLLMProvider({
      provider: process.env.LLM_PROVIDER_CDB,
      model: modelPrefCDB,
    });
  } else {
    LLMConnector = getLLMProvider(); // Standard system LLM
  }
  const temperature = workspace?.openAiTemp ?? LLMConnector.defaultTemp;

  const documentBuilderBasePath = path.join(
    __dirname,
    "../../../storage/document-builder"
  );

  // Ensure the directory exists before we start writing files.
  if (!fs.existsSync(documentBuilderBasePath)) {
    fs.mkdirSync(documentBuilderBasePath, { recursive: true });
  }

  // Workspace path setup
  const folderName = getUserDocumentPathName(user, true, workspace.slug);
  const workspacePath = path.join(
    __dirname,
    "../../../storage/documents",
    folderName
  );

  if (!fs.existsSync(workspacePath)) {
    console.error(
      "[REFERENCE FILES FLOW] Workspace path not found:",
      workspacePath
    );
    sendProgress({
      step: 0,
      status: "error",
      message: "Workspace documents not found.",
    });
    writeResponseChunk(response, {
      uuid: chatId,
      type: "text",
      text: "Error: Workspace documents not found.",
      sources: [],
      close: true,
    });
    return;
  }

  // Read all JSON files and split into reference vs review sets
  const allDocFiles = fs
    .readdirSync(workspacePath)
    .filter((file) => file.endsWith(".json"));
  const refFiles = Array.isArray(cdbOptions[4]) ? cdbOptions[4] : [];
  const refSet = new Set(refFiles);
  const reviewFiles = allDocFiles.filter((file) => !refSet.has(file));

  sendProgress({
    step: 1,
    status: "starting",
    message: "Processing reference files...",
  });
  // Generate descriptions for reference files
  const refDescriptions = await Promise.all(
    refFiles.map(async (file) => {
      const filePath = path.join(workspacePath, file);
      if (!fs.existsSync(filePath)) return null;
      const content = fs.readFileSync(filePath, "utf8");
      const description = await generateDocumentDescription(
        file,
        content,
        legalTask,
        LLMConnector,
        {
          customSystemPrompt:
            AllPrompts.CURRENT_DEFAULT_REFERENCE_FILES_DESCRIPTION
              .SYSTEM_PROMPT,
          customUserPromptTemplate:
            AllPrompts.CURRENT_DEFAULT_REFERENCE_FILES_DESCRIPTION.USER_PROMPT,
          temperature,
        },
        chatId,
        "referenceFiles"
      );
      return {
        "Doc Name": file,
        DisplayName: file.replace(/\.json$/, ""),
        Description: description,
      };
    })
  );
  sendProgress({
    step: 1,
    status: "complete",
    message: "Reference files processed.",
  });
  sendProgress({
    step: 2,
    status: "starting",
    message: "Processing review files...",
  });
  // Generate descriptions for review files
  const reviewDescriptions = await Promise.all(
    reviewFiles.map(async (file) => {
      const filePath = path.join(workspacePath, file);
      if (!fs.existsSync(filePath)) return null;
      const content = fs.readFileSync(filePath, "utf8");
      const description = await generateDocumentDescription(
        file,
        content,
        legalTask,
        LLMConnector,
        {
          customSystemPrompt:
            AllPrompts.CURRENT_DEFAULT_REVIEW_FILES_DESCRIPTION.SYSTEM_PROMPT,
          customUserPromptTemplate:
            AllPrompts.CURRENT_DEFAULT_REVIEW_FILES_DESCRIPTION.USER_PROMPT,
          temperature,
        },
        chatId,
        "referenceFiles"
      );
      return {
        "Doc Name": file,
        DisplayName: file.replace(/\.json$/, ""),
        Description: description,
      };
    })
  );
  sendProgress({
    step: 2,
    progress: 100,
    status: "complete",
    message: "Review files processed.",
  });

  // Combine reference and review descriptions into a single list
  const docDescriptions = [
    ...refDescriptions.filter(Boolean),
    ...reviewDescriptions.filter(Boolean),
  ];
  // Generate section list from summaries for reference flow
  sendProgress({
    step: 3,
    status: "starting",
    message: "Generating section list...",
  });
  const sectionList = await generateSectionListFromSummaries(
    docDescriptions,
    legalTask,
    LLMConnector,
    customInstructions,
    {
      customSystemPrompt:
        AllPrompts.CURRENT_DEFAULT_REFERENCE_REVIEW_SECTIONS.SYSTEM_PROMPT,
      customUserPromptTemplate:
        AllPrompts.CURRENT_DEFAULT_REFERENCE_REVIEW_SECTIONS.USER_PROMPT,
      temperature,
    }
  );
  sendProgress({
    step: 3,
    progress: 100,
    status: "complete",
    message: "Section list generated.",
  });

  let finalReport = "";
  let previouslyDraftedSections = "";
  sendProgress({
    step: 4,
    status: "starting",
    message: "Drafting sections...",
  });
  for (const section of sectionList) {
    if (!section || !section.title || !section.relevant_documents) continue;

    let sectionContext = "";
    for (const docName of section.relevant_documents) {
      const docPath = path.join(workspacePath, docName);
      if (fs.existsSync(docPath)) {
        try {
          const rawContent = fs.readFileSync(docPath, "utf8");
          // Assuming the .json files store the actual content in a known structure, e.g., parsed.pageContent
          // For this example, I'll assume the raw content of the JSON is the text.
          // Adjust if your JSONs have a specific schema like { "pageContent": "..." }
          sectionContext += `Content from ${docName}:\n${rawContent}\n\n`;
        } catch (e) {
          console.warn(
            `Could not read ${docName} for section ${section.title}: ${e.message}`
          );
        }
      }
    }

    // Use standardized reference section drafting prompts
    const sectionNumber = section.index_number || 1;
    const systemPrompt =
      AllPrompts.CURRENT_DEFAULT_REFERENCE_SECTION_DRAFTING.SYSTEM_PROMPT;
    const userPrompt = fillTemplate(
      AllPrompts.CURRENT_DEFAULT_REFERENCE_SECTION_DRAFTING.USER_PROMPT,
      {
        sectionNumber: sectionNumber,
        title: section.title,
        task: legalTask,
        description: section.description || "",
        docs: sectionContext,
        previousSections:
          previouslyDraftedSections || "This is the first section.",
      }
    );

    const compressedMessages = await LLMConnector.compressMessages({
      systemPrompt: systemPrompt,
      userPrompt: userPrompt,
    });
    const sectionDraftResult = await LLMConnector.getChatCompletion(
      compressedMessages,
      { temperature }
    );

    const draftedContent =
      sectionDraftResult.textResponse ||
      "(Content could not be generated for this section)";
    finalReport += `## ${section.title}\n\n${draftedContent}\n\n`;
    previouslyDraftedSections += `## ${section.title}\n\n${draftedContent}\n\n`; // Update for the next iteration
  }
  sendProgress({
    step: 4,
    progress: 100,
    status: "complete",
    message: "Sections drafted.",
  });
  sendProgress({
    step: 5,
    status: "starting",
    message: "Generating report...",
  });
  sendProgress({
    step: 5,
    progress: 100,
    status: "complete",
    message: "Report generated.",
  });

  return finalReport;
}

module.exports = {
  runReferenceFilesFlow,
};
