{"cSpell.words": ["adoc", "aibitat", "AIbitat", "Astra", "Chartable", "cleancss", "comkey", "cooldown", "cooldowns", "datafile", "Deduplicator", "Dockerized", "doc<PERSON>", "elevenlabs", "Embeddable", "epub", "GROQ", "hljs", "huggingface", "inferencing", "kobold<PERSON><PERSON>", "Langchain", "lmstudio", "localai", "mbox", "<PERSON><PERSON><PERSON><PERSON>", "Mintplex", "mixtral", "moderations", "numpages", "Ollama", "Oobabooga", "openai", "opendocument", "openrouter", "pagerender", "Qdrant", "royalblue", "searxng", "SearchApi", "<PERSON><PERSON>", "Ser<PERSON>ly", "streamable", "textgenwebui", "<PERSON><PERSON>", "fireworksai", "Unembed", "vectordbs", "Weaviate", "XAILLM", "<PERSON><PERSON><PERSON>"], "eslint.useFlatConfig": true, "docker.languageserver.formatter.ignoreMultilineInstructions": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "editor.formatOnSave": true}