#!/bin/bash
# This script checks for the legacy DB file (anythingllm.db) and renames it
# to the new name (istlegal.db) if it exists.
# Run this script manually once from the project root directory if you are upgrading
# an existing instance.

DB_PATH_LEGACY="server/storage/anythingllm.db"
DB_PATH_NEW="server/storage/istlegal.db"

# Check if the legacy DB file exists
if [ -f "$DB_PATH_LEGACY" ]; then
  echo "[DB Rename] Legacy database '$DB_PATH_LEGACY' found."
  # Check if the new file already exists to prevent accidental overwrite
  if [ -f "$DB_PATH_NEW" ]; then
    echo "[DB Rename] Error: New database '$DB_PATH_NEW' already exists. Cannot rename."
    echo "Please manually resolve the conflict (backup or remove one of the files) and run again if needed."
    exit 1
  fi

  echo "[DB Rename] Renaming '$DB_PATH_LEGACY' to '$DB_PATH_NEW'..."
  mv "$DB_PATH_LEGACY" "$DB_PATH_NEW"
  if [ $? -eq 0 ]; then
    echo "[<PERSON>ame] Successfully renamed database file."
  else
    echo "[<PERSON> Rename] Error: Failed to rename database file."
    exit 1
  fi
else
  echo "[DB Rename] No legacy database file ('$DB_PATH_LEGACY') found. Nothing to rename."
fi

exit 0