---
description: Guidelines for the chat system architecture
globs: frontend/src/components/WorkspaceChat/**/*.js,frontend/src/components/WorkspaceChat/**/*.jsx,frontend/src/utils/chat/**/*.js
alwaysApply: false
---

# Chat System Architecture

## Core Components

### Main Components
1. **ChatContainer** (`frontend/src/components/WorkspaceChat/ChatContainer/index.jsx`)
   - Primary container handling chat UI and state
   - Manages chat history, message streaming, and user interactions
   - Handles file attachments and message composition

2. **ChatHistory** (`frontend/src/components/WorkspaceChat/ChatContainer/ChatHistory/index.jsx`)
   - Renders chat history (both user and assistant messages)
   - Manages scrolling and viewport handling

3. **HistoricalMessage** (`frontend/src/components/WorkspaceChat/ChatContainer/ChatHistory/HistoricalMessage/index.jsx`)
   - Renders individual messages in the chat
   - Conditionally renders message actions based on message type and state
   - Handles the display of user vs. assistant messages

4. **Actions** (`frontend/src/components/WorkspaceChat/ChatContainer/ChatHistory/HistoricalMessage/Actions/index.jsx`)
   - Contains action buttons for messages (copy, regenerate, edit, etc.)
   - Conditionally renders different buttons based on message properties
   - Handles message-specific operations

### Utility Components
5. **Citations** (`frontend/src/components/WorkspaceChat/ChatContainer/ChatHistory/Citation/index.jsx`)
   - Manages citation display for RAG (Retrieval-Augmented Generation) results
   - Displays source information and document previews
   - Only renders when sources are available and streaming is complete (chatId exists)

6. **PromptInput** (`frontend/src/components/WorkspaceChat/ChatContainer/PromptInput/index.jsx`)
   - Handles user input for messaging
   - Manages text entry, suggestions, and command submission

## Data Flow

The chat system follows this data flow pattern:

1. **User Input**
   - User types message in PromptInput
   - Optional attachments are added through AttachmentWrapper
   - Message is submitted via handleSubmit

2. **Message Processing**
   - User message is added to chat history
   - Placeholder assistant message is added to indicate loading
   - Workspace.streamChat or Workspace.multiplexStream is called

3. **Backend Communication**
   - Message is sent to backend via API
   - Backend processes message and starts streaming response
   - Response chunks are sent back to frontend

4. **Message Streaming**
   - Response chunks are processed by handleChatResponse
   - Chat history is updated incrementally with new chunks
   - UI updates in real-time to show streaming text

5. **Message Completion**
   - When streaming is complete, finalizeResponseStream event occurs
   - Chat message is saved with chatId
   - Citations and other UI elements are displayed when appropriate
   - Message actions become available

## Message Streaming

### Streaming Process
1. **Initiation**
   - Stream is initiated through either:
     - `Workspace.streamChat()` in `workspace.js`
     - `Workspace.multiplexStream()` in more complex scenarios

2. **Stream Setup**
   - Uses `fetchEventSource` to establish a connection to the streaming endpoint
   - Sets up event handlers for messages, errors, and aborts
   - Creates an AbortController for stream cancellation

3. **Chunk Processing**
   - Incoming message chunks are processed via `handleChatResponse` in `utils/chat/index.js`
   - Different message types are handled differently:
     - `textResponseChunk`: Incremental updates during streaming
     - `finalizeResponseStream`: Marks the completion of streaming
     - `abort`: Handles error conditions
   - Chat history is updated with each chunk

4. **Stream Completion**
   - Stream is marked complete with `close: true` flag
   - `chatId` is assigned when the message is saved to the database
   - UI components update based on streaming completion

### Streaming States
Messages go through several states during streaming:
- **Pending**: Initial state when streaming begins (`pending: true`)
- **Streaming**: Content is updated with each chunk, `closed: false`
- **Complete**: Stream is complete, `closed: true`, `chatId` is assigned
- **Error**: Something went wrong, `error` property contains details

## Chat History Management

### Chat History State
1. **State Initialization**
   - `const [chatHistory, setChatHistory] = useState(knownHistory);`
   - Initial history can be provided via the `knownHistory` prop

2. **History Updates**
   - Updates occur through the `setChatHistory` state setter
   - Main update locations:
     - User message submission: `handleSubmit`
     - Streaming message chunks: `handleChatResponse`
     - Message deletion, editing, regeneration

3. **History Format**
   - Each chat message is an object with properties:
     - `uuid`: Unique identifier
     - `content`: Message text content
     - `role`: "user" or "assistant"
     - `sources`: Array of document sources (for RAG)
     - `closed`: Boolean indicating if streaming is complete
     - `animate`: Boolean for UI animation
     - `pending`: Boolean indicating if message is pending
     - `chatId`: ID once saved to database (only present after completion)
     - Additional properties for specific features

### User Messages vs. Assistant Messages
User and assistant messages are handled differently:
1. **User Messages**
   - Added immediately upon submission
   - Include attachments when needed
   - Have minimal state changes after submission

2. **Assistant Messages**
   - Start with `pending: true` and empty content
   - Content updates incrementally during streaming
   - Complete with `closed: true` and assigned `chatId`
   - Have various action buttons after completion

## Citations and Sources

### Source Handling
1. **Source Collection**
   - Sources are collected on the backend during RAG operations
   - Sources are attached to message chunks during streaming
   - Complete source list is available when streaming is complete

2. **Source Format**
   - Each source contains:
     - `text`: Document excerpt
     - `title`: Document title
     - `url`: Document location
     - `score`: Relevance score
     - Additional metadata

### Citations Component
The Citations component is particularly important:
- Only renders if sources exist AND streaming is complete (chatId exists)
- Combines like sources to avoid duplication
- Provides a modal with document previews
- Highlights relevant text passages
