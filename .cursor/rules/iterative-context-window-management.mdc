---
description:
globs:
alwaysApply: false
---
# Iterative Context Window Management

## Overview

The iterative context window management system handles LLM token limits through sophisticated chunking and progressive processing strategies. Use this system when processing large documents or complex tasks that may exceed context window limits.

## Core Components

### ContextWindowManager
- Main utility for token-aware processing
- Handles chunking, budget allocation, and iterative processing
- Integrates with TokenTracker for comprehensive metrics

### TokenTracker
- Comprehensive token metrics collection
- Real-time budget monitoring and utilization tracking
- Stage-based processing analysis and optimization recommendations

## When to Use

Use iterative processing when:
- Documents exceed ~70% of the LLM's context window
- Complex multi-document analysis is needed
- Section drafting requires extensive supporting content
- Token budget optimization is important

## Key Functions

### Document Processing
```javascript
// Automatic iterative fallback for large documents
const description = await generateDocumentDescriptionIterative(
  docName, content, legalTask, LLMConnector, options
);

const isRelevant = await generateDocumentRelevanceIterative(
  docName, content, legalTask, LLMConnector, options
);
```

### Section Drafting
```javascript
// Iterative section drafting with token management
const result = await contextManager.processIteratively({
  processor: processIterativeSectionDrafting,
  documents, memos, budget, context
});
```

### Token Budget Management
```javascript
const budget = contextManager.calculateTokenBudget({
  systemPrompt, userPromptTemplate, reservedTokens
});
```

## Best Practices

### 1. Token Budget Planning
- Calculate budgets before processing begins
- Reserve 20-30% of context window for LLM output
- Monitor utilization through TokenTracker metrics

### 2. Content Prioritization
- Process most relevant documents first
- Use relevance checking to filter content
- Prioritize main documents over supporting materials

### 3. Chunking Strategy
- Use default 100-200 token overlap between chunks
- Adjust chunk size based on content complexity
- Monitor chunk metrics for optimization

### 4. Error Handling
- Implement graceful degradation for budget overruns
- Use partial results when full processing fails
- Log comprehensive metrics for debugging

### 5. Performance Monitoring
- Enable TokenTracker for optimization insights
- Review budget recommendations regularly
- Use stage tracking to identify bottlenecks

## Configuration Options

### ContextWindowManager
- `maxIterations`: Maximum iterations before stopping (default: 10)
- `reservedOutputTokens`: Tokens reserved for LLM output (default: 4000)
- `enableTokenTracking`: Enable comprehensive tracking (default: true)
- `defaultOverlapTokens`: Default overlap between chunks (default: 100)

### Processing Options
- `temperature`: LLM generation temperature (default: 0.7)
- `reservedTokens`: Operation-specific token reservation
- `overlapTokens`: Chunk overlap for this operation
- `maxTokensPerChunk`: Maximum tokens per content chunk

## Integration Patterns

### Replace Standard Processing
```javascript
// Before
const description = await generateDocumentDescription(name, content, task, LLM);

// After (backward compatible with automatic fallback)
const description = await generateDocumentDescriptionIterative(name, content, task, LLM);
```

### Enhanced Section Processing
```javascript
// Create context manager
const contextManager = new ContextWindowManager(LLMConnector, {
  maxIterations: 8,
  reservedOutputTokens: 6000
});

// Process with automatic iteration
const result = await contextManager.processIteratively({
  processor: myProcessor,
  documents, memos, budget, context
});
```

## Token Tracking and Analysis

### Basic Tracking
```javascript
const tokenTracker = new TokenTracker(LLMConnector, {
  enableDetailedTracking: true,
  trackContentTypes: true
});

// Track content by type
tokenTracker.trackContentTokens(content, 'documents', docName);
tokenTracker.trackLLMResponse(llmResult, 'stageName', 'operationName');

// Generate comprehensive report
const report = tokenTracker.generateReport();
```

### Optimization Analysis
```javascript
// Get budget recommendations
report.budgetAnalysis.recommendations.forEach(rec => {
  console.log(`Optimization: ${rec}`);
});

// Monitor efficiency metrics
const efficiency = report.efficiency;
console.log(`Tokens/second: ${efficiency.tokensPerSecond}`);
console.log(`Budget utilization: ${report.summary.budgetUtilization}%`);
```

## Common Patterns

### Document Processing Loop
```javascript
for (const doc of documents) {
  const description = await generateDocumentDescriptionIterative(
    doc.name, doc.content, legalTask, LLMConnector, {
      logTokenUsage: true,
      reservedTokens: 2000
    }
  );

  const isRelevant = await generateDocumentRelevanceIterative(
    doc.name, doc.content, legalTask, LLMConnector, {
      reservedTokens: 500
    }
  );
}
```

### Section Drafting with Metrics
```javascript
const contextManager = new ContextWindowManager(LLMConnector, {
  enableTokenTracking: true,
  logTokenUsage: true
});

const result = await contextManager.processIteratively({
  processor: processIterativeSectionDrafting,
  documents, memos, budget, context
});

// Get detailed metrics
const tokenReport = contextManager.generateTokenReport();
console.log(`Total iterations: ${result.totalIterations}`);
console.log(`Token efficiency: ${tokenReport.efficiency.iterativeEfficiency}`);
```

## Testing

Unit tests are available at:
- `server/tests/unit/utils/contextWindowManager.test.js`
- `server/tests/unit/utils/tokenTracker.test.js`

Run tests with:
```bash
cd server
npm test -- --testNamePattern="ContextWindowManager|TokenTracker"
```

## Documentation

Comprehensive documentation is available at:
- `server/docs/iterative_context_window_management.md`

## Backward Compatibility

The iterative system is designed to be fully backward compatible:
- Small documents process normally without chunking
- Large documents automatically use iterative processing
- Existing progress reporting continues to work
- Metrics collection is additive and non-breaking

This ensures smooth integration with minimal changes to existing code while providing powerful new capabilities for handling large content and complex processing tasks.
