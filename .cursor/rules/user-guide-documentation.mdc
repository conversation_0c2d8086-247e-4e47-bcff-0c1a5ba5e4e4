---
description:
globs:
alwaysApply: true
---
# User Guide Documentation Guidelines

## Overview

The user guide is a critical component of the IST Legal platform that helps users understand and effectively use all features. When adding new features or modifying existing ones, the user guide must be updated to reflect these changes.

## When to Update the User Guide

### New Features
- **Always** update the user guide when adding new features
- Add comprehensive documentation for new UI components, tools, or workflows
- Include step-by-step instructions for using new functionality
- Provide context about when and why users would use the new feature

### Feature Modifications
- Update documentation when changing existing feature behavior
- Revise instructions when UI elements are moved or redesigned
- Update screenshots or descriptions when visual elements change
- Modify usage instructions when workflows are altered

### Feature Removal
- Remove documentation for deprecated features
- Update related sections that reference removed functionality
- Ensure no broken references remain in the guide

## User Guide Structure

The user guide is implemented as internationalized content in:
- **Location**: `frontend/src/locales/[lang]/useGuide.js`
- **Component**: `frontend/src/components/HeaderWorkspace/UseGuide/UseGuideModal/index.jsx`

### Main Sections
1. **Platform Overview** - High-level platform description
2. **Platform Modules** - Legal Q&A and Document Drafting modules
3. **Template Generation** - Template creation and management
4. **Complex Document Builder** - Advanced document creation
5. **Workspace Management** - Workspace creation and file management
6. **Message Tools** - Chat message interaction tools
7. **Prompt Input Features** - Input area tools and functions
8. **User Account & Settings** - Account management and preferences

## Documentation Requirements

### For New Features

1. **Identify the Appropriate Section**
   - Determine which existing section the feature belongs to
   - Create a new section if the feature doesn't fit existing categories

2. **Required Information**
   - **Title**: Clear, descriptive name for the feature
   - **Description**: What the feature does and its purpose
   - **Icon**: Description of the UI icon (if applicable)
   - **Usage**: Step-by-step instructions on how to use the feature
   - **Requirements**: Any prerequisites for using the feature (if applicable)
   - **Availability**: When/where the feature is available (if limited)
   - **Note**: Important additional information (if applicable)

3. **Translation Requirements**
   - Add content to **ALL** supported language files:
     - English (`en/useGuide.js`)
     - Swedish (`sv/useGuide.js`)
     - French (`fr/useGuide.js`)
     - German (`de/useGuide.js`)
     - Norwegian (`no/useGuide.js`)
     - Polish (`pl/useGuide.js`)
     - Kinyarwanda (`rw/useGuide.js`)

### For Message Tools

When adding new message tools (buttons/actions on chat messages):

```javascript
newTool: {
  title: "Tool Name",
  description: "What the tool does",
  icon: "Icon description",
  usage: "How to use the tool",
  note: "Important notes (if any)",
  availability: "Availability restrictions (if any)",
},
```

### For Prompt Input Features

When adding new prompt input features (tools in the input area):

```javascript
newFeature: {
  title: "Feature Name",
  description: "What the feature does",
  icon: "Icon description",
  usage: "How to use the feature",
  shortcut: "Keyboard shortcut (if any)",
  requirements: "Prerequisites (if any)",
  availability: "Availability restrictions (if any)",
},
```

## Implementation Steps

### 1. Update English Content First
- Add the new feature documentation to `frontend/src/locales/en/useGuide.js`
- Follow the existing structure and naming conventions
- Use clear, professional language

### 2. Update Component (if needed)
- If adding a new section, update `UseGuideModal/index.jsx`
- Add the new section to the `expandedSections` state
- Add the UI rendering for the new section
- Ensure proper translation key usage

### 3. Translate to All Languages
- Copy the structure from English to all other language files
- Translate all text content appropriately
- Maintain the same key structure across all languages

### 4. Test the Implementation
- Verify the user guide displays correctly in all languages
- Test that all new sections expand/collapse properly
- Ensure all translation keys are working

## Best Practices

### Content Guidelines
- **Be Specific**: Provide clear, actionable instructions
- **Use Consistent Terminology**: Match the language used in the UI
- **Include Context**: Explain when and why users would use features
- **Keep It Current**: Remove outdated information promptly

### Translation Guidelines
- **Maintain Structure**: Keep the same object structure across all languages
- **Professional Tone**: Use formal, professional language appropriate for legal software
- **Cultural Sensitivity**: Ensure translations are appropriate for each locale
- **Consistency**: Use consistent terminology within each language

### Technical Guidelines
- **Follow Naming Conventions**: Use camelCase for keys, descriptive names
- **Proper Nesting**: Organize content logically within the existing structure
- **Translation Keys**: Use the common labels for "Requirements", "Availability", "Note"
- **Component Updates**: Only modify the component when adding new sections

## Common Mistakes to Avoid

1. **Forgetting Translations**: Only updating English and forgetting other languages
2. **Inconsistent Structure**: Using different key names across languages
3. **Missing Component Updates**: Adding content but not updating the UI component
4. **Outdated Information**: Leaving old documentation when features change
5. **Poor Organization**: Adding content to inappropriate sections

## Review Checklist

Before submitting changes:

- [ ] Added documentation to all 7 language files
- [ ] Used consistent key structure across all languages
- [ ] Updated component if new sections were added
- [ ] Tested user guide in multiple languages
- [ ] Verified all translation keys work correctly
- [ ] Removed any outdated information
- [ ] Used appropriate professional language
- [ ] Followed existing formatting and structure

## Enforcement

This is a **mandatory requirement** for all feature development. Pull requests that add or modify features without corresponding user guide updates will be returned for revision.

The user guide is often the first place users look for help, making it critical for user adoption and satisfaction. Keeping it current and comprehensive is everyone's responsibility.
