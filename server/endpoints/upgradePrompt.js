const { getLLMProvider } = require("../utils/helpers");
const { validatedRequest } = require("../utils/middleware/validatedRequest");
const { reqBody } = require("../utils/http");
const { SystemSettings } = require("../models/systemSettings");

// The prompt upgrade system uses three locations for the default template:
// 1. Database (via prisma/seed.js): Sets the initial value for new installations
// 2. Frontend (PromptUpgradeLLMPreference/index.jsx): Provides immediate UI feedback
// 3. This endpoint: Provides the runtime template for processing
//
// While this creates some duplication, each serves a specific purpose in the system's
// resilience and user experience. Any changes to the default template should be
// synchronized across all three locations.

/**
 * Upgrade the user prompt using the AI model.
 * @param {string} prompt - The original user prompt.
 * @param {string} settings_suffix - Optional settings suffix (e.g., "_TM" for templates).
 * @returns {string} - The upgraded prompt.
 */
async function upgradeUserPrompt(prompt, settings_suffix = "") {
  let LLMProvider;
  let providerChoice;
  let suffixForProvider;

  if (settings_suffix) {
    if (process.env.NODE_ENV === "development") {
      console.log(
        `UpgradePrompt using suffix-specific LLM with suffix: ${settings_suffix}`
      );
    }
    const currentSettings = await SystemSettings.currentSettings();
    const suffixProviderChoice =
      currentSettings[`LLMProvider${settings_suffix}`];
    if (process.env.NODE_ENV === "development") {
      console.log(
        `[DEBUG_SUFFIX] Looking up LLMProvider${settings_suffix}: "${suffixProviderChoice}"`
      );
      console.log(
        `[DEBUG_SUFFIX] Environment LLM_PROVIDER${settings_suffix}: "${process.env[`LLM_PROVIDER${settings_suffix}`]}"`
      );
    }

    providerChoice =
      suffixProviderChoice && suffixProviderChoice !== "system-standard"
        ? suffixProviderChoice
        : (process.env.LLM_PROVIDER ?? "openai");
    suffixForProvider = settings_suffix;
    if (process.env.NODE_ENV === "development") {
      console.log(
        `[DEBUG_SUFFIX] Using provider: ${providerChoice} with suffix ${suffixForProvider}`
      );
    }
  } else {
    const puProviderSetting = await SystemSettings.get({
      label: "LLMProvider_PU",
    });
    const dbProviderChoice = puProviderSetting?.value;

    if (dbProviderChoice && dbProviderChoice !== "system-standard") {
      providerChoice = dbProviderChoice;
      suffixForProvider = "_PU";
      if (process.env.NODE_ENV === "development") {
        console.log(
          `UpgradePrompt using specific setting: ${providerChoice} with suffix _PU`
        );
      }
    } else {
      providerChoice = process.env.LLM_PROVIDER ?? "openai";
      suffixForProvider = null; // No suffix for system-standard
      if (process.env.NODE_ENV === "development") {
        console.log(
          "UpgradePrompt using system-standard, resolving to default LLM."
        );
      }
    }
  }

  LLMProvider = getLLMProvider({
    provider: providerChoice,
    settings_suffix: suffixForProvider,
  });

  try {
    // Validate the resolved connector/model
    if (!LLMProvider) {
      console.error(`Upgrade Prompt: Could not determine LLM provider.`);
      throw new Error(`Could not determine LLM provider`);
    }
    if (!LLMProvider.isValidChatCompletionModel(LLMProvider.model)) {
      throw new Error(`${LLMProvider.model} is not valid for chat completion!`);
    }

    const template = await SystemSettings.getValueOrFallback(
      { label: "prompt_upgrade_template" },
      `Your task is to refine a proposed prompt from a user who is a legal professional.\nThe prompt is intended for use in a legal task, but the user is not an expert in crafting optimal prompts for AI handling.\nThe prompt will also be used to search a vector database for relevant sources using semantic search.\nImprove the prompt for clarity, detail, and specificity.\nEnsure that the prompt is designed to generate results that are engaging, comprehensive, and specified according to professional standards in the legal domain.\nGenerate the response in the same language provided in original prompt.\nDo not respond to the prompt but only provide a suggestion for an improved prompt.\nInclude no introductory text, just respond with the replacement prompt suggestion.\nThis is the prompt to be refined: <ORIGINALPROMPT> {{prompt}} </ORIGINALPROMPT>`
    );

    const systemPrompt = template.replace("{{prompt}}", prompt);

    const props = {
      model: LLMProvider.model,
      max_tokens: Math.floor(LLMProvider.promptWindowLimit() * 0.5), // Use 50% of available context for response
      messages: [
        {
          role: "user",
          content: systemPrompt,
        },
      ],
      temperature: 0,
    };

    const response = await LLMProvider.getChatCompletion(props.messages, {
      temperature: props.temperature,
    });
    return response?.textResponse;
  } catch (error) {
    console.error("Error upgrading prompt:", error);
    // Ensure the specific provider resolution error is caught if it happens early
    const errorMsg = error?.message || "Failed to upgrade prompt";
    throw new Error(errorMsg);
  }
}

/**
 * Process the AI response to extract the upgraded prompt.
 * @param {string} responseContent
 * @returns {string}
 */
function processResponse(responseContent) {
  const extractedResponse = responseContent.match(/["]([\s\S]*?)["]/);

  if (extractedResponse && extractedResponse[1]) {
    return extractedResponse[1].trim();
  }
  return responseContent;
}

/**
 * Update prompt endpoints in the application.
 * @param {object} app - The Express application instance.
 */
function updatePrompts(app) {
  if (!app) return;

  app.post("/upgrade-prompt", [validatedRequest], async (request, response) => {
    try {
      const { prompt, settings_suffix } = reqBody(request);
      const upgradedPrompt = await upgradeUserPrompt(prompt, settings_suffix);

      return response
        .status(200)
        .json({ success: true, upgradedPrompt: upgradedPrompt });
    } catch (error) {
      console.error(error);
      response
        .status(500)
        .json({ success: false, error: "Internal Server Error" });
    }
  });
}

module.exports = { updatePrompts };
