import React from "react";
import Sidebar from "@/components/SettingsSidebar";
import CustomLogo from "./CustomLogo";
import ColorPalette from "./ColorPalette";
import SupportEmail from "./SupportEmail";
import CustomWelcomeMessages from "./CustomWelcomeMessages";
import { ArrowLeft } from "@phosphor-icons/react";
import FooterCustomization from "./FooterCustomization";
import ManageLoginUI from "./ManageLoginUI";
import LanguagePreference from "./LanguagePreference";
import { Link } from "react-router-dom";
import paths from "@/utils/paths";
import { useTranslation } from "react-i18next";
import CustomSiteSettings from "./CustomSiteSettings";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import LoginChangeWebsite from "./LoginChangeWebsite";
import CustomTabName from "./CustomTabName";
import PromptExamples from "./PromptExamples";
import DocxTemplateSetting from "./DocxTemplate";

export default function Appearance() {
  const { t } = useTranslation();

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="relative rounded-md w-full h-full overflow-y-scroll">
          <div className="flex flex-col md:pl-6 md:pr-[86px] md:py-4 px-5 md:px-0 py-3">
            <div className="flex flex-row items-center gap-3.5 gap-y-1 pb-4">
              <Link to={paths.home()}>
                <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
              </Link>
              <div className="flex flex-col">
                <div className="items-center">
                  <p className="text-lg leading-6 font-bold text-foreground">
                    {t("appearance.title")}
                  </p>
                </div>
                <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                  {t("appearance.description")}
                </p>
              </div>
            </div>

            <ColorPalette />
            <CustomLogo />
            <DocxTemplateSetting />
            <CustomTabName />
            <div className="flex flex-col w-full md:flex-row items-center gap-x-10 md:items-center justify-between border-bottom pb-4">
              <LanguagePreference />
              <LoginChangeWebsite />
            </div>
            <div className="flex flex-col md:flex-row items-end border-top border-bottom gap-y-5 gap-x-10 pb-4 pt-4">
              <CustomSiteSettings />
              <SupportEmail />
            </div>
            <CustomWelcomeMessages />
            <FooterCustomization />
            <ManageLoginUI />
            <PromptExamples />
          </div>
        </div>
      </div>
    </div>
  );
}
