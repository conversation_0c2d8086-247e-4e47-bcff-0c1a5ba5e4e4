const {
  isDocumentProperlyVectorized,
  addDocIdToExistingVectors,
  reVectorizeDocument,
} = require("../vectorizationCheck");
const { getVectorDbClass } = require("../../helpers");

// Mock the getVectorDbClass function
jest.mock("../../helpers", () => ({
  getVectorDbClass: jest.fn(),
}));

// Mock the fileData function
jest.mock("../../files", () => ({
  fileData: jest.fn(),
}));

// Mock the DocumentVectors model
jest.mock("../../../models/vectors", () => ({
  DocumentVectors: { where: jest.fn() },
}));

describe("vectorizationCheck", () => {
  let mockVectorDb;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create a mock VectorDb class with required methods
    mockVectorDb = {
      checkDocumentHasDocId: jest.fn(),
      deleteDocumentFromNamespace: jest.fn(),
      addDocumentToNamespace: jest.fn(),
      connect: jest.fn(),
      namespaceExists: jest.fn(),
    };

    // Set up the mock to return our mockVectorDb
    getVectorDbClass.mockReturnValue(mockVectorDb);

    // Stub DocumentVectors.where to empty array so addDocIdToExistingVectors returns false by default
    const { DocumentVectors } = require("../../../models/vectors");
    DocumentVectors.where.mockResolvedValue([]);
  });

  describe("isDocumentProperlyVectorized", () => {
    it("should return false if workspaceSlug is missing", async () => {
      const result = await isDocumentProperlyVectorized(null, "doc123");
      expect(result).toBe(false);
      expect(mockVectorDb.checkDocumentHasDocId).not.toHaveBeenCalled();
    });

    it("should return false if docId is missing", async () => {
      const result = await isDocumentProperlyVectorized("workspace-slug", null);
      expect(result).toBe(false);
      expect(mockVectorDb.checkDocumentHasDocId).not.toHaveBeenCalled();
    });

    it("should return true if document has docId in vector database", async () => {
      mockVectorDb.checkDocumentHasDocId.mockResolvedValue(true);

      const result = await isDocumentProperlyVectorized(
        "workspace-slug",
        "doc123"
      );

      expect(mockVectorDb.checkDocumentHasDocId).toHaveBeenCalledWith(
        "workspace-slug",
        "doc123"
      );
      expect(result).toBe(true);
    });

    it("should return false if document does not have docId in vector database", async () => {
      mockVectorDb.checkDocumentHasDocId.mockResolvedValue(false);

      const result = await isDocumentProperlyVectorized(
        "workspace-slug",
        "doc123"
      );

      expect(mockVectorDb.checkDocumentHasDocId).toHaveBeenCalledWith(
        "workspace-slug",
        "doc123"
      );
      expect(result).toBe(false);
    });

    it("should handle errors gracefully", async () => {
      mockVectorDb.checkDocumentHasDocId.mockRejectedValue(
        new Error("Database error")
      );

      // Spy on console.error
      const consoleSpy = jest.spyOn(console, "error").mockImplementation();

      const result = await isDocumentProperlyVectorized(
        "workspace-slug",
        "doc123"
      );

      expect(mockVectorDb.checkDocumentHasDocId).toHaveBeenCalledWith(
        "workspace-slug",
        "doc123"
      );
      expect(consoleSpy).toHaveBeenCalled();
      expect(result).toBe(false);

      // Restore console.error
      consoleSpy.mockRestore();
    });
  });

  describe("reVectorizeDocument", () => {
    const fileData = require("../../files").fileData;

    it("should return false if any required parameter is missing", async () => {
      // Test with missing workspaceSlug
      let result = await reVectorizeDocument(null, "doc123", {
        docpath: "path/to/doc.json",
      });
      expect(result).toBe(false);

      // Test with missing docId
      result = await reVectorizeDocument("workspace-slug", null, {
        docpath: "path/to/doc.json",
      });
      expect(result).toBe(false);

      // Test with missing document
      result = await reVectorizeDocument("workspace-slug", "doc123", null);
      expect(result).toBe(false);

      expect(mockVectorDb.deleteDocumentFromNamespace).not.toHaveBeenCalled();
      expect(mockVectorDb.addDocumentToNamespace).not.toHaveBeenCalled();
    });

    it("should successfully re-vectorize a document", async () => {
      // Mock document data
      const document = {
        docId: "doc123",
        docpath: "path/to/doc.json",
      };

      // Mock file data
      const mockFileData = {
        title: "Test Document",
        pageContent: "This is a test document",
      };
      fileData.mockResolvedValue(mockFileData);

      // Mock successful vectorization
      mockVectorDb.deleteDocumentFromNamespace.mockResolvedValue(true);
      mockVectorDb.addDocumentToNamespace.mockResolvedValue({
        vectorized: true,
        error: null,
      });

      const result = await reVectorizeDocument(
        "workspace-slug",
        "doc123",
        document
      );

      // Verify the document was deleted and re-added
      expect(mockVectorDb.deleteDocumentFromNamespace).toHaveBeenCalledWith(
        "workspace-slug",
        "doc123"
      );
      expect(fileData).toHaveBeenCalledWith("path/to/doc.json");
      expect(mockVectorDb.addDocumentToNamespace).toHaveBeenCalledWith(
        "workspace-slug",
        { ...mockFileData, docId: "doc123" },
        "path/to/doc.json"
      );

      expect(result).toBe(true);
    });

    it("should return false if file data cannot be read", async () => {
      // Mock document data
      const document = {
        docId: "doc123",
        docpath: "path/to/doc.json",
      };

      // Mock file data failure
      fileData.mockResolvedValue(null);

      // Spy on console.error
      const consoleSpy = jest.spyOn(console, "error").mockImplementation();

      const result = await reVectorizeDocument(
        "workspace-slug",
        "doc123",
        document
      );

      // The improved implementation now has safety checks - deletion should NOT happen
      // when file data cannot be read, to prevent data loss
      expect(mockVectorDb.deleteDocumentFromNamespace).not.toHaveBeenCalled();
      expect(fileData).toHaveBeenCalledWith("path/to/doc.json");
      expect(mockVectorDb.addDocumentToNamespace).not.toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalled();
      expect(result).toBe(false);

      // Restore console.error
      consoleSpy.mockRestore();
    });

    it("should return false if vectorization fails", async () => {
      // Mock document data
      const document = {
        docId: "doc123",
        docpath: "path/to/doc.json",
      };

      // Mock file data
      const mockFileData = {
        title: "Test Document",
        pageContent: "This is a test document",
      };
      fileData.mockResolvedValue(mockFileData);

      // Mock failed vectorization
      mockVectorDb.deleteDocumentFromNamespace.mockResolvedValue(true);
      mockVectorDb.addDocumentToNamespace.mockResolvedValue({
        vectorized: false,
        error: "Vectorization failed",
      });

      // Spy on console.error
      const consoleSpy = jest.spyOn(console, "error").mockImplementation();

      const result = await reVectorizeDocument(
        "workspace-slug",
        "doc123",
        document
      );

      expect(mockVectorDb.deleteDocumentFromNamespace).toHaveBeenCalledWith(
        "workspace-slug",
        "doc123"
      );
      expect(fileData).toHaveBeenCalledWith("path/to/doc.json");
      expect(mockVectorDb.addDocumentToNamespace).toHaveBeenCalledWith(
        "workspace-slug",
        { ...mockFileData, docId: "doc123" },
        "path/to/doc.json"
      );
      expect(consoleSpy).toHaveBeenCalled();
      expect(result).toBe(false);

      // Restore console.error
      consoleSpy.mockRestore();
    });

    it("should handle errors gracefully", async () => {
      // Mock document data
      const document = {
        docId: "doc123",
        docpath: "path/to/doc.json",
      };

      // Mock error during deletion
      mockVectorDb.deleteDocumentFromNamespace.mockRejectedValue(
        new Error("Database error")
      );

      // Spy on console.error
      const consoleSpy = jest.spyOn(console, "error").mockImplementation();

      const result = await reVectorizeDocument(
        "workspace-slug",
        "doc123",
        document
      );

      expect(mockVectorDb.deleteDocumentFromNamespace).toHaveBeenCalledWith(
        "workspace-slug",
        "doc123"
      );
      expect(consoleSpy).toHaveBeenCalled();
      expect(result).toBe(false);

      // Restore console.error
      consoleSpy.mockRestore();
    });
  });

  // Add tests for addDocIdToExistingVectors
  describe("addDocIdToExistingVectors", () => {
    const { DocumentVectors } = require("../../../models/vectors");
    it("should return true and update vectors when vectors exist and schema supports docId", async () => {
      DocumentVectors.where.mockResolvedValue([
        { vectorId: "vec1" },
        { vectorId: "vec2" },
      ]);
      const table = {
        schema: jest.fn().mockResolvedValue({ fields: [{ name: "docId" }] }),
        update: jest.fn(),
      };
      const client = { openTable: jest.fn().mockResolvedValue(table) };
      mockVectorDb.connect.mockResolvedValue({ client });
      mockVectorDb.namespaceExists.mockResolvedValue(true);
      const result = await addDocIdToExistingVectors(
        "workspace-slug",
        "doc123"
      );
      expect(DocumentVectors.where).toHaveBeenCalledWith({ docId: "doc123" });
      expect(mockVectorDb.connect).toHaveBeenCalled();
      expect(mockVectorDb.namespaceExists).toHaveBeenCalledWith(
        client,
        "workspace-slug"
      );
      expect(client.openTable).toHaveBeenCalledWith("workspace-slug");
      expect(table.update).toHaveBeenCalledTimes(2);
      expect(table.update).toHaveBeenNthCalledWith(1, {
        where: "id = 'vec1'",
        values: { docId: "doc123" },
      });
      expect(table.update).toHaveBeenNthCalledWith(2, {
        where: "id = 'vec2'",
        values: { docId: "doc123" },
      });
      expect(result).toBe(true);
    });

    it("should return false when no vectors are found", async () => {
      DocumentVectors.where.mockResolvedValue([]);
      const result = await addDocIdToExistingVectors(
        "workspace-slug",
        "doc123"
      );
      expect(DocumentVectors.where).toHaveBeenCalledWith({ docId: "doc123" });
      expect(result).toBe(false);
    });
  });
});
