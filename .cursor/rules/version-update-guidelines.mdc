---
description:
globs:
alwaysApply: false
---
# Version Update Guidelines

## Overview

When implementing major features, bug fixes, or significant changes to the IST Legal platform, the version number should be updated to reflect the changes. This ensures users and administrators can track platform evolution and understand what's new.

## When to Update Version

### Major Version (X.0.0)
Update the major version when implementing:
- **Breaking Changes**: Changes that break backward compatibility
- **Major New Features**: Significant new functionality that changes core platform behavior
- **Architecture Overhauls**: Major refactoring or architectural changes
- **API Breaking Changes**: Changes to public APIs that require client updates

**Examples:**
- Complete UI redesign
- New authentication system
- Major database schema changes
- Removal of deprecated features

### Minor Version (1.X.0)
Update the minor version when implementing:
- **New Features**: New functionality that's backward compatible
- **New Components**: New UI components or pages
- **Enhanced Functionality**: Improvements to existing features
- **New Integrations**: Adding new LLM providers, vector databases, etc.

**Examples:**
- New document builder features
- Additional language support
- New legal templates
- Enhanced chat functionality
- New admin features

### Patch Version (1.1.X)
Update the patch version when implementing:
- **Bug Fixes**: Fixing existing functionality
- **Security Patches**: Security-related fixes
- **Performance Improvements**: Optimizations without new features
- **Documentation Updates**: Significant documentation improvements
- **Minor UI Tweaks**: Small visual improvements

**Examples:**
- Fixing form validation issues
- Performance optimizations
- Security vulnerability fixes
- Translation corrections

## Implementation Process

### 1. Determine Version Type
Before implementing a feature, assess its impact:
- Does it break existing functionality? → **Major**
- Does it add new functionality? → **Minor**
- Does it fix existing issues? → **Patch**

### 2. Update version.json
When completing the implementation, update the version file:

```json
{
  "version": "1.2.0",
  "description": "Added new legal templates and improved search functionality",
  "timestamp": "2024-02-01T14:30:00Z"
}
```

**Required Fields:**
- `version`: New semantic version number
- `description`: Clear, user-friendly description of changes
- `timestamp`: Current ISO 8601 timestamp

### 3. Description Guidelines
Write clear, user-focused descriptions:

**Good Examples:**
- "Added new document builder features and improved chat performance"
- "Enhanced legal templates with custom field support"
- "Fixed workspace synchronization issues and improved error handling"

**Avoid:**
- Technical jargon: "Refactored DocumentManager class"
- Vague descriptions: "Various improvements"
- Internal references: "Fixed issue #123"

### 4. Update Documentation
When updating versions, also update:
- User guide if new features are user-facing
- API documentation if backend changes affect APIs
- Deployment guides if deployment process changes

## Agent Instructions

### For Major Features
When implementing significant new functionality:

1. **Assess Impact**: Determine if this is a major, minor, or patch change
2. **Suggest Version Update**: Recommend appropriate version number increment
3. **Provide Description**: Suggest user-friendly description of changes
4. **Update version.json**: Include version update in implementation
5. **Update Documentation**: Ensure relevant docs are updated

### Example Prompts
When suggesting version updates:

```
"This implementation adds significant new functionality to the platform. I recommend updating the version number:

Current version: 1.1.0
Suggested version: 1.2.0
Description: "Added manual work estimator and enhanced legal task templates"

Should I update version.json as part of this implementation?"
```

### Implementation Checklist
For each major feature implementation:

- [ ] Assess version impact (major/minor/patch)
- [ ] Suggest appropriate version number
- [ ] Provide user-friendly description
- [ ] Update version.json file
- [ ] Update relevant documentation
- [ ] Test version display in UI
- [ ] Verify backend logging works

## Version History Tracking

### Maintain Change Log
Consider maintaining a CHANGELOG.md file for detailed version history:

```markdown
# Changelog

## [1.2.0] - 2024-02-01
### Added
- Manual work estimator for legal tasks
- Enhanced legal task templates
- New document builder features

### Fixed
- Workspace synchronization issues
- Chat performance improvements

## [1.1.0] - 2024-01-15
### Added
- Version control system
- User guide improvements
```

### Documentation Updates
When updating versions, ensure:
- User guide reflects new features
- API documentation is current
- Deployment guides are accurate
- Cursor rules are updated if needed

## Best Practices

### Timing
- Update version as part of feature completion
- Don't update version for work-in-progress
- Include version update in the same commit/PR as the feature

### Communication
- Use clear, non-technical language in descriptions
- Focus on user benefits, not implementation details
- Keep descriptions concise but informative

### Testing
- Test version display after updates
- Verify backend logging includes new version
- Ensure Docker deployments work with new version

### Automation
Consider automating version updates:
- CI/CD pipeline integration
- Automatic timestamp generation
- Version validation checks

## Common Mistakes to Avoid

1. **Forgetting to Update**: Always update version for significant changes
2. **Wrong Version Type**: Assess impact correctly (major vs minor vs patch)
3. **Poor Descriptions**: Avoid technical jargon or vague descriptions
4. **Missing Timestamp**: Always include current timestamp
5. **Inconsistent Format**: Follow semantic versioning strictly
6. **No Documentation**: Update relevant docs with version changes

## Integration with Development Workflow

### Feature Development
1. Plan feature implementation
2. Assess version impact
3. Implement feature
4. Update version.json
5. Update documentation
6. Test version display
7. Deploy with new version

### Code Review
During code review, check:
- Is version update appropriate for the changes?
- Is the description clear and user-friendly?
- Are all required fields present in version.json?
- Is relevant documentation updated?

### Deployment
Before deployment:
- Verify version.json is updated
- Test version endpoint
- Confirm version displays correctly in UI
- Check backend logging works

## Related Guidelines

- [Implementation Strategy Guidelines](./implementation-strategy.mdc)
- [Documentation Update Requirements](./documentation-updates.mdc)
- [User Guide Documentation Guidelines](./user-guide-documentation.mdc)
- [News System Guidelines](./news-system-guidelines.mdc)

## Enforcement

This guideline is **mandatory** for all significant feature implementations. The agent should:

1. **Always suggest version updates** for major features
2. **Provide specific version recommendations** based on change impact
3. **Include version updates in implementation plans**
4. **Remind about documentation updates** when suggesting version changes
5. **Verify version system works** after updates

Version updates help maintain transparency and communication with users about platform improvements and changes.
