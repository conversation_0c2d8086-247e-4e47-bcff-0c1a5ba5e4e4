# LanceDB Integration Documentation

This document provides an overview of how LanceDB is integrated into the ISTLegal platform, describing key operations and referencing related source files.

## Key Server Files and Operations

- **server/utils/vectorDbProviders/lance/index.js**

  - `connect(uri)` – Establishes a LanceDB client connection to the storage directory.
  - `updateOrCreateCollection(client, data, namespace)` – Creates a new table (namespace) or updates existing records with `add`.
  - `addDocumentToNamespace(namespace, documentData, fullFilePath, skipCache)` – Inserts or updates vector chunks and metadata:
    - Generates `vectorId`, stores `vector`, `index`, `docId`, `text`, and spread `chunk.metadata` fields.
    - Performs bulk insert into primary database via `models/vectors`.
  - `deleteVectorsInNamespace(client, namespace)` – Drops an entire namespace table.
  - `deleteDocumentFromNamespace(namespace, docId)` – Deletes specific vector records based on `docId`.
  - `getAdjacentVectors(namespace, title, index, adjacentCount)` – Retrieves chunks before and after a given index for contextual retrieval.
  - `similarityResponse(...)` – Performs a basic vector search and assembles results.
  - `rerankedSimilarityResponse(...)` – Performs a 3-step similarity search with re-ranking via `EmbeddingRerankers`.

- **server/models/vectors.js**

  - Defines the `DocumentVectors` model for persisting the relationship between `docId` and `vectorId` in the primary database.

- **server/jobs/sync-watched.documents.js**

  - Background job that synchronizes watched external documents:
    - Calls `vectorDatabase.addDocumentToNamespace()` for new/updated files.
    - Calls `vectorDatabase.deleteDocumentFromNamespace()` for removed files.

- **server/utils/helpers/vectorizationCheck.js**
  - Helper that determines whether vectorization is needed:
    - Calls `VectorDb.addDocumentToNamespace()` if a document has changed or is new.

## Collector Integration

- **collector/processRawText/test.js**
  - Integration tests for LanceDB functionality using the `LanceDb` provider:
    - `LanceDb.connect()`
    - `LanceDb.namespaceExists()`
    - `LanceDb.deleteVectorsInNamespace()`
    - `LanceDb.addDocumentToNamespace()`
    - `LanceDb.performSimilaritySearch()`
    - `LanceDb.deleteDocumentFromNamespace()`

## Maintenance Scripts

- **scripts/add-docid-to-lancedb.js**
  - Node.js script to backfill the `docId` metadata on existing LanceDB records based on the `title` field and primary database lookup.

## Reference

For detailed guidelines on vector record structure, metadata requirements, and contextual retrieval, see:

- `.cursor/rules/lancedb-integration.mdc`
