const TRANSLATIONS = {
  "news-system": {
    title: "Aktualności i Ogłoszenia",
    previous: "Poprzedni",
    next: "Nast<PERSON><PERSON><PERSON>",
    of: "z",
    close: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    dismissThis: "Nie pokazuj ponownie",
    dismissAll: "Odr<PERSON>ć wszystkie",
    management: {
      title: "Zarządzanie Aktualnościami",
      description:
        "Zarządzaj ogłoszeniami systemowymi i aktualnościami. Aktualności systemowe są wdrażane z aplikacją i nie można ich tutaj edytować.",
      create: "Utwórz Aktualno<PERSON>ć",
      edit: "Edytuj <PERSON>ktual<PERSON>",
      loading: "Ładowanie...",
      table: {
        title: "Tytuł",
        priority: "Priorytet",
        targetRoles: "Role Docelowe",
        created: "Utworzono",
        expires: "Wygasa",
        status: "Status",
        source: "Źródło",
        actions: "Akcje",
        allUsers: "<PERSON><PERSON><PERSON><PERSON> użytkownicy",
        never: "Nigdy",
        active: "Aktywny",
        inactive: "Nieaktywny",
        systemNews: "System",
        localNews: "Lokalne",
      },
      source: {
        system: "Aktualności Systemowe",
        local: "Aktualności Lokalne",
        systemTooltip:
          "Ta aktualność jest wdrażana z aplikacją i nie może być edytowana",
        localTooltip:
          "Ta aktualność została utworzona lokalnie i może być edytowana",
      },
      form: {
        title: "Tytuł",
        titlePlaceholder: "Wprowadź tytuł aktualności",
        content: "Treść",
        contentPlaceholder: "Wprowadź treść aktualności",
        priority: "Priorytet",
        targetRoles:
          "Role Docelowe (pozostaw puste dla wszystkich użytkowników)",
        expiresAt: "Wygasa (opcjonalne)",
        priorities: {
          low: "Niski",
          medium: "Średni",
          high: "Wysoki",
          urgent: "Pilny",
        },
        roles: {
          admin: "Administrator",
          manager: "Menedżer",
          default: "Domyślny",
        },
      },
      actions: {
        create: "Utwórz",
        update: "Aktualizuj",
        cancel: "Anuluj",
        delete: "Usuń",
        edit: "Edytuj",
        view: "Zobacz",
        cannotEdit: "Nie można edytować aktualności systemowych",
        cannotDelete: "Nie można usunąć aktualności systemowych",
      },
      confirmations: {
        delete: "Czy na pewno chcesz usunąć tę aktualność?",
      },
      messages: {
        createSuccess: "Aktualność utworzona pomyślnie",
        updateSuccess: "Aktualność zaktualizowana pomyślnie",
        deleteSuccess: "Aktualność usunięta pomyślnie",
        createError: "Nie udało się utworzyć aktualności",
        updateError: "Nie udało się zaktualizować aktualności",
        deleteError: "Nie udało się usunąć aktualności",
        fetchError: "Nie udało się pobrać aktualności",
        systemNewsInfo:
          "Aktualności systemowe są tylko do odczytu i wdrażane z aplikacją",
      },
    },
    error: {
      dismiss: "Nie udało się odrzucić aktualności",
      dismissAll: "Nie udało się odrzucić wszystkich aktualności",
      fetch: "Nie udało się pobrać aktualności",
    },
  },

  // News header and list functionality (moved from common.js)
  news: {
    header: {
      viewAll: "Zobacz wszystkie wiadomości",
      buttonText: "Aktualności",
    },
    list: {
      title: "Wiadomości i Ogłoszenia",
      empty: "Brak wiadomości do wyświetlenia",
      active: "Aktywne",
      dismissed: "Odrzucone",
      dismiss: "Odrzuć",
      dismissSuccess: "Wiadomość odrzucona pomyślnie",
      dismissError: "Nie udało się odrzucić wiadomości",
      dismissedAt: "Odrzucone {{date}}",
      systemNews: "System",
      viewFull: "Zobacz pełną wiadomość",
      filter: {
        all: "Wszystkie",
        unread: "Aktywne",
        read: "Odrzucone",
      },
    },
    priority: {
      low: "Niski",
      medium: "Średni",
      high: "Wysoki",
      urgent: "Pilny",
    },
  },
};

export default TRANSLATIONS;
