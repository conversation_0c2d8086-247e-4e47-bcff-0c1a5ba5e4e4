const { NativeEmbedder } = require("../../EmbeddingEngines/native");
const {
  LLMPerformanceMonitor,
} = require("../../helpers/chat/LLMPerformanceMonitor");
const {
  writeResponseChunk,
  clientAbortedHandler,
} = require("../../helpers/chat/responses");
const { MODEL_MAP } = require("../modelMap");
const { defaultGeminiModels, v1BetaModels } = require("./defaultModals");
const {
  getEmbeddingEngineSelection,
  formatContextTexts,
} = require("../../helpers");
const { v4: uuidv4 } = require("uuid");

class GeminiLLM {
  constructor(embedder = null, modelPreference = null, settings_suffix = null) {
    if (!process.env.GEMINI_API_KEY && !process.env.GEMINI_API_KEY_DD)
      throw new Error("No Gemini API key was set.");

    if (process.env.NODE_ENV === "development") {
      try {
        const stackTrace = new Error().stack;
        if (typeof stackTrace === "string") {
          const stackLines = stackTrace.split("\n");
          let caller = "Unknown";

          if (stackLines.length > 2) {
            const callerLine = stackLines[2].trim();
            const match =
              callerLine.match(/at (.+?) \(/) || callerLine.match(/at (.*)/);
            if (match && match[1]) {
              caller = match[1];
            }
          }

          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m ============================================`
          );
          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m Initialization triggered by: ${caller}`
          );
          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m Settings suffix: "${
              settings_suffix || "none"
            }"`
          );
          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m Model preference: "${
              modelPreference || "none"
            }"`
          );
          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m Embedder type: ${
              embedder
                ? embedder.constructor.name
                : "none (will use NativeEmbedder)"
            }"`
          );
          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m API Key suffix: "${
              settings_suffix || ""
            }"`
          );
          console.log(`\x1b[32m[GeminiLLM INIT]\x1b[0m Caller stack:`);
          const relevantStack = stackLines.slice(1, 6);
          relevantStack.forEach((line, index) => {
            console.log(
              `\x1b[32m[GeminiLLM INIT]\x1b[0m   ${index + 1}. ${line.trim()}`
            );
          });
          console.log(
            `\x1b[32m[GeminiLLM INIT]\x1b[0m ============================================`
          );
        }
      } catch (e) {
        // Fails silently to avoid impacting production.
      }
    }

    // Initialize GenAI client
    const { GoogleGenAI } = require("@google/genai");
    const ai = new GoogleGenAI({
      apiKey: process.env[`GEMINI_API_KEY${settings_suffix || ""}`],
    });
    this.ai = ai;
    this.model =
      modelPreference ||
      process.env[`GEMINI_LLM_MODEL_PREF${settings_suffix || ""}`] ||
      "gemini-pro";
    this.limits = {
      history: this.promptWindowLimit() * 0.15,
      system: this.promptWindowLimit() * 0.15,
      user: this.promptWindowLimit() * 0.7,
    };

    this.embedder = embedder ?? new NativeEmbedder();
    this.defaultTemp = MODEL_MAP.gemini.defaults.temperature; // not used for Gemini
    this.safetyThreshold = this.#fetchSafetyThreshold();
    this.#log(`Initialized with model: ${this.model}`);
  }
  #log(text, ...args) {
    console.log(`\x1b[32m[GeminiLLM]\x1b[0m ${text}`, ...args);
  }

  async #appendContext(contextTexts = []) {
    if (!contextTexts || !contextTexts.length) return "";
    return await formatContextTexts(contextTexts);
  }

  // BLOCK_NONE can be a special candidate for some fields
  // https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/configure-safety-attributes#how_to_remove_automated_response_blocking_for_select_safety_attributes
  // so if you are wondering why BLOCK_NONE still failed, the link above will explain why.
  #fetchSafetyThreshold() {
    const threshold =
      process.env.GEMINI_SAFETY_SETTING ?? "BLOCK_MEDIUM_AND_ABOVE";
    const safetyThresholds = [
      "BLOCK_NONE",
      "BLOCK_ONLY_HIGH",
      "BLOCK_MEDIUM_AND_ABOVE",
      "BLOCK_LOW_AND_ABOVE",
    ];
    return safetyThresholds.includes(threshold)
      ? threshold
      : "BLOCK_MEDIUM_AND_ABOVE";
  }

  #safetySettings() {
    return [
      {
        category: "HARM_CATEGORY_HATE_SPEECH",
        threshold: this.safetyThreshold,
      },
      {
        category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        threshold: this.safetyThreshold,
      },
      { category: "HARM_CATEGORY_HARASSMENT", threshold: this.safetyThreshold },
      {
        category: "HARM_CATEGORY_DANGEROUS_CONTENT",
        threshold: this.safetyThreshold,
      },
    ];
  }

  streamingEnabled() {
    return "streamGetChatCompletion" in this;
  }

  static promptWindowLimit(modelName) {
    if (modelName && MODEL_MAP.gemini?.models?.[modelName]) {
      return MODEL_MAP.gemini.models[modelName].context;
    }
    return MODEL_MAP.gemini.defaults.contextWindow;
  }

  promptWindowLimit() {
    if (MODEL_MAP.gemini?.models?.[this.model]) {
      return MODEL_MAP.gemini.models[this.model].context;
    }
    return MODEL_MAP.gemini.defaults.contextWindow;
  }

  /**
   * Used when custom AI configurations are active.
   * Returns the standard prompt window limit for the current model.
   * This allows us to provide consistent token limits across different configurations.
   */
  customPromptWindowLimit() {
    return this.promptWindowLimit();
  }

  /**
   * Fetches Gemini models from the Google Generative AI API
   * @param {string} apiKey - The API key to use for the request
   * @param {number} limit - The maximum number of models to fetch
   * @param {string} pageToken - The page token to use for pagination
   * @returns {Promise<[{id: string, name: string, contextWindow: number, experimental: boolean}]>} A promise that resolves to an array of Gemini models
   */
  static async fetchModels(apiKey, limit = 1_000, pageToken = null) {
    const url = new URL(
      "https://generativelanguage.googleapis.com/v1beta/models"
    );
    url.searchParams.set("pageSize", limit);
    url.searchParams.set("key", apiKey);
    if (pageToken) url.searchParams.set("pageToken", pageToken);
    return fetch(url.toString(), {
      method: "GET",
      headers: { "Content-Type": "application/json" },
    })
      .then((res) => res.json())
      .then((data) => {
        if (data.error) throw new Error(data.error.message);
        return data.models ?? [];
      })
      .then((models) => {
        // First get models from API response
        const apiModels = models
          .filter((model) =>
            model.supportedGenerationMethods.some(
              (method) =>
                method.includes("generate") || method.includes("vision")
            )
          )
          .map((model) => {
            const modelId = model.name.split("/").pop();
            const displayName = model.displayName || modelId;

            // Improved name formatting logic
            let formattedName = displayName;
            if (displayName.startsWith("gemini-exp-")) {
              formattedName = `Gemini Experimental ${displayName.split("gemini-exp-")[1]}`;
            } else if (modelId.includes("exp")) {
              // Handle cases where exp is in the middle of the name
              const datePart = modelId.match(/\d{4}/)?.[0];
              if (datePart) {
                formattedName = `Gemini Experimental ${datePart}`;
              }
            }

            return {
              id: modelId,
              name: formattedName,
              contextWindow: model.inputTokenLimit,
              experimental:
                modelId.includes("exp") || displayName.includes("exp"),
            };
          });

        // If API call succeeded but returned no models, use defaults
        if (apiModels.length === 0) {
          return defaultGeminiModels;
        }

        // Create a Set of model IDs to track what we've seen
        const seenModelIds = new Set();
        const uniqueModels = [];

        // Process API models first
        for (const model of apiModels) {
          if (!seenModelIds.has(model.id)) {
            seenModelIds.add(model.id);
            uniqueModels.push(model);
          }
        }

        return uniqueModels;
      })
      .catch((e) => {
        console.error(`Gemini:getGeminiModels`, e.message);
        return defaultGeminiModels;
      });
  }

  /**
   * Checks if a model is valid for chat completion (unused)
   * @deprecated
   * @param {string} modelName - The name of the model to check
   * @returns {Promise<boolean>} A promise that resolves to a boolean indicating if the model is valid
   */
  async isValidChatCompletionModel(modelName = "") {
    // Validation disabled for Gemini as it's not needed
    return true;
  }

  /**
   * Generates appropriate content array for a message + attachments.
   * @param {{userPrompt:string, attachments: import("../../helpers").Attachment[]}}
   * @returns {string|object[]}
   */
  #generateContent({ userPrompt, attachments = [] }) {
    if (!attachments.length) {
      return userPrompt;
    }

    // For Gemini, we need to combine all text content into a single string
    let combinedText = userPrompt;
    const imageAttachments = [];

    // Process attachments
    attachments.forEach((attachment, index) => {
      if (attachment.mime?.startsWith("image/")) {
        // Collect image attachments separately
        imageAttachments.push({
          inlineData: {
            data: attachment.contentString.split("base64,")[1],
            mimeType: attachment.mime,
          },
        });
      } else {
        // Add text attachments to combined text
        const fileListEntry =
          index === 0
            ? `\n\nAttached file${attachments.length > 1 ? "s" : ""}: ${attachment.name}\n`
            : `${attachment.name}\n`;
        combinedText += fileListEntry + attachment.contentString;
      }
    });

    // If we have image attachments, return array with both text and images
    if (imageAttachments.length > 0) {
      return [{ text: combinedText }, ...imageAttachments];
    }

    // Otherwise just return the combined text
    return combinedText;
  }

  async constructPrompt({
    systemPrompt = "",
    contextTexts = [],
    chatHistory = [],
    userPrompt = "",
    attachments = [],
  }) {
    const formattedContext = await this.#appendContext(contextTexts);
    const prompt = {
      role: "system",
      content: `${systemPrompt}${formattedContext}`,
    };
    return [
      prompt,
      {
        role: "user",
        content: this.#generateContent({ userPrompt, attachments }),
      },
      ...chatHistory,
      { role: "user", content: userPrompt },
    ];
  }

  // This will take an OpenAi format message array and only pluck valid roles from it.
  formatMessages(messages = []) {
    // Gemini roles are either user || model.
    // and all "content" is relabeled to "parts"
    const allMessages = messages
      .map((message) => {
        if (message.role === "system")
          return { role: "user", parts: [{ text: message.content }] };
        if (message.role === "user")
          return { role: "user", parts: [{ text: message.content }] };
        if (message.role === "assistant")
          return { role: "model", parts: [{ text: message.content }] };
        return null;
      })
      .filter((msg) => !!msg);

    // Specifically, Google cannot have the last sent message be from a user with no assistant reply
    // otherwise it will crash. So if the last item is from the user, it was not completed so pop it off
    // the history.
    if (
      allMessages.length > 0 &&
      allMessages[allMessages.length - 1].role === "user"
    )
      allMessages.pop();

    // Validate that after every user message, there is a model message
    // sometimes when using gemini we try to compress messages in order to retain as
    // much context as possible but this may mess up the order of the messages that the gemini model expects
    // we do this check to work around the edge case where 2 user prompts may be next to each other, in the message array
    for (let i = 0; i < allMessages.length; i++) {
      if (
        allMessages[i].role === "user" &&
        i < allMessages.length - 1 &&
        allMessages[i + 1].role !== "model"
      ) {
        allMessages.splice(i + 1, 0, {
          role: "model",
          parts: [{ text: "Okay." }],
        });
      }
    }

    return allMessages;
  }

  async getChatCompletion(messages = [], _opts = {}) {
    const prompt = messages.find((chat) => chat.role === "user")?.content;
    const chat = this.ai.chats.create({
      model: this.model,
      history: this.formatMessages(messages),
      safetySettings: this.#safetySettings(),
    });

    const { output: responseObj, duration } =
      await LLMPerformanceMonitor.measureAsyncFunction(
        chat.sendMessage({ message: prompt })
      );
    const responseText = responseObj.text;

    if (!responseText) throw new Error("Gemini: No response could be parsed.");

    const promptTokens = LLMPerformanceMonitor.countTokens(messages);
    const completionTokens = LLMPerformanceMonitor.countTokens([
      { content: responseText },
    ]);
    return {
      textResponse: responseText,
      metrics: {
        prompt_tokens: promptTokens,
        completion_tokens: completionTokens,
        total_tokens: promptTokens + completionTokens,
        outputTps: (promptTokens + completionTokens) / duration,
        duration,
      },
    };
  }

  async streamGetChatCompletion(messages = [], _opts = {}) {
    const prompt = messages.find((chat) => chat.role === "user")?.content;
    const chat = this.ai.chats.create({
      model: this.model,
      history: this.formatMessages(messages),
      safetySettings: this.#safetySettings(),
    });

    try {
      // Start streaming and monitor performance (pass the raw stream directly)
      const rawStream = await chat.sendMessageStream({ message: prompt });
      const responseStream = await LLMPerformanceMonitor.measureStream(
        rawStream,
        messages
      );

      if (!responseStream)
        throw new Error("Could not stream response stream from Gemini.");
      return responseStream;
    } catch (error) {
      this.#log(`Stream error: ${error.message}`);
      // Fall back to non-streaming response if streaming fails
      const responseObj = await chat.sendMessage({ message: prompt });
      const text = responseObj.text;

      // Create a mock stream with the full response
      const mockStream = {
        [Symbol.asyncIterator]: async function* () {
          yield { text: () => text };
        },
        endMeasurement: () => {},
      };

      return mockStream;
    }
  }

  async compressMessages(promptArgs = {}, rawHistory = []) {
    const { messageArrayCompressor } = require("../../helpers/chat");
    const messageArray = await this.constructPrompt(promptArgs);
    return await messageArrayCompressor(
      this,
      messageArray,
      rawHistory,
      [],
      [],
      promptArgs.maxAllowedTokens
    );
  }

  handleStream(response, stream, responseProps) {
    const { uuid = uuidv4(), sources = [] } = responseProps;
    // Usage is not available for Gemini streams
    // so we need to calculate the completion tokens manually
    // because 1 chunk != 1 token in gemini responses and it buffers
    // many tokens before sending them to the client as a "chunk"

    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve) => {
      let fullText = "";

      // Establish listener to early-abort a streaming response
      // in case things go sideways or the user does not like the response.
      // We preserve the generated text but continue as if chat was completed
      // to preserve previously generated content.
      const handleAbort = () => {
        stream?.endMeasurement({
          completion_tokens: LLMPerformanceMonitor.countTokens([
            { content: fullText },
          ]),
        });
        clientAbortedHandler(resolve, fullText);
      };
      response.on("close", handleAbort);

      try {
        for await (const chunk of stream) {
          let chunkText;
          // Extract text from chunk: support .text() method or .text property
          try {
            if (typeof chunk.text === "function") {
              chunkText = chunk.text();
            } else if (typeof chunk.text === "string") {
              chunkText = chunk.text;
            } else if (typeof chunk.textContent === "string") {
              chunkText = chunk.textContent;
            } else {
              // Unknown chunk format, default to empty string
              chunkText = "";
            }
          } catch (e) {
            // If text() throws due to safety protocols, abort streaming
            chunkText = e.message;
            writeResponseChunk(response, {
              uuid,
              sources: [],
              type: "abort",
              textResponse: null,
              close: true,
              error: e.message,
            });
            stream?.endMeasurement({ completion_tokens: 0 });
            resolve(e.message);
            return;
          }

          fullText += chunkText;
          writeResponseChunk(response, {
            uuid,
            sources: [],
            type: "textResponseChunk",
            textResponse: chunkText,
            close: false,
            error: false,
          });
        }

        writeResponseChunk(response, {
          uuid,
          sources,
          type: "textResponseChunk",
          textResponse: "",
          close: true,
          error: false,
        });
        response.removeListener("close", handleAbort);
        stream?.endMeasurement({
          completion_tokens: LLMPerformanceMonitor.countTokens([
            { content: fullText },
          ]),
        });
        resolve(fullText);
      } catch (error) {
        this.#log(`Stream handling error: ${error.message}`);

        // If we have some text already, send it as the final response
        if (fullText) {
          writeResponseChunk(response, {
            uuid,
            sources,
            type: "textResponseChunk",
            textResponse: "",
            close: true,
            error: false,
          });
          response.removeListener("close", handleAbort);
          stream?.endMeasurement({
            completion_tokens: LLMPerformanceMonitor.countTokens([
              { content: fullText },
            ]),
          });
          resolve(fullText);
        } else {
          // If we have no text, send an error message
          writeResponseChunk(response, {
            uuid,
            sources: [],
            type: "abort",
            textResponse: null,
            close: true,
            error:
              "Failed to process Gemini response stream. Please try again.",
          });
          stream?.endMeasurement({ completion_tokens: 0 });
          resolve(
            "Failed to process Gemini response stream. Please try again."
          );
        }
      }
    });
  }

  // Simple wrapper for dynamic embedder & normalize interface for all LLM implementations
  async embedTextInput(textInput) {
    return await this.embedder.embedTextInput(textInput);
  }
  async embedChunks(textChunks = []) {
    return await this.embedder.embedChunks(textChunks);
  }
}

module.exports = {
  GeminiLLM,
};
