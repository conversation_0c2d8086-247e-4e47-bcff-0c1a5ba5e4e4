# <PERSON>‑Driven Deployments to AWS EC2

This guide walks you through everything needed to
build, parameterise, and run the **istlegal** container deployments from
Jenkins – blending an on‑demand EC2‑instance dropdown and a flexible branch
selector.

**Jenkins Server**: http://16.16.186.162
**Login Information**: Available in Slack

---

## 1  Prerequisites

| Requirement                                                       | Why it matters                                                               |
| ----------------------------------------------------------------- | ---------------------------------------------------------------------------- |
| IAM user with **ec2\:DescribeInstances**                          | <PERSON> uses it to populate the EC2 dropdown & resolve instance IPs.         |
| Long‑lived access key stored in <PERSON>   (`aws-prod`)            | Injected at build time via _AmazonWebServicesCredentialsBinding_.            |
| SSH key pair   (`ssh-prod` in Jenkins & public half on instances) | Allows <PERSON> to `ssh ec2-user@<target>` for the docker commands.           |
| AWS CLI inside the Jenkins agent image                            | Used by the dropdown Groovy **and** the pipeline stage that resolves the IP. |
| _Active Choices Plugin_                                           | Renders the dynamic dropdown & conditional branch textbox.                   |
| Docker installed on the target EC2 hosts                          | Pipeline stops/pulls/runs the container remotely.                            |

---

## 2  Job Configuration (GUI)

### 2.1  Parameters

1. **EC2_INSTANCE**   *(Active Choice ▸ Single Select)*

   ```groovy
   // returns running instances in eu-north-1
   return ['groovy‑script‑omitted‑for‑brevity']
   ```

2. **BRANCH_PRESET**   *(Active Choice ▸ Single Select)*

   ```groovy
   return ['develop','main-dev','main-stage','main-prod','main','Custom…']
   ```

---

## 3  Running the job

1. Click **Build with Parameters** on the job called Deployment.
2. Pick your **EC2 instance** (auto‑updated list).
3. Choose one of the common branches.
4. Hit **Build** – Jenkins will:

   - inject AWS creds 🡒 resolve the public IP;
   - SSH with `ssh-prod` 🡒 stop, pull, run the container with corrected mounts;
   - stream logs in the build console.

---

## 4  Troubleshooting quick‑ref

| Symptom                                | Likely cause                              | Fix                                                                                    |
| -------------------------------------- | ----------------------------------------- | -------------------------------------------------------------------------------------- |
| Dropdown empty                         | Script blocked / CLI missing / IAM denied | Check _Script Approval_; verify `aws` on PATH; attach `ec2:DescribeInstances`.         |
| `AuthFailure` in pipeline              | `aws-prod` key inactive / wrong account   | Re‑create key, update credential.                                                      |
| SSH `Permission denied`                | New EC2 lacks Jenkins public key          | Append key to `/home/<USER>/.ssh/authorized_keys`.                                   |
| Container forgets users                | Wrong or read‑only bind mount             | Ensure `-v /home/<USER>/anythingllm:/app/server/storage` exists & owned by UID 1000. |
| `attempt to write a readonly database` | Storage dir permissions                   | `sudo chown -R 1000:1000 /home/<USER>/anythingllm`.                                  |

---

## 5  Extending / maintaining

- **Regions** – //TODO add a region selector Active Choice param and pass it into both dropdown‑script and pipeline `AWS_REGION`.
- **Key rotation** – //TODO create a new AWS key, update `aws-prod`, run a test build; disable old key after a soak period.
- **Multisellect instances** – //TODO Add functionallity to select multiple instances in one job run.
- **Fallback to old docker image** – //TODO Add a safeguard for failed deployments where the job reverts to the old docker image.

---

© 2025 IST Legal – Last updated: 6 Jun 2025
