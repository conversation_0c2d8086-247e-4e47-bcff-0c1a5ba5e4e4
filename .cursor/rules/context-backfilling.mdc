---
description: Guidelines for context backfilling in chat
globs: server/utils/helpers/chat/**/*.js,server/utils/chats/**/*.js
alwaysApply: false
---
# Context Backfilling

Backfilling is a smart context management feature that maintains conversation coherence by reusing relevant context from chat history.

## 1. How Backfilling Works

```javascript
function fillSourceWindow({
  nDocs = 4,           // Target number of documents
  searchResults = [],  // Current vector search results
  history = [],        // Chat history
  filterIdentifiers = [] // To prevent duplicates
})
```

1. **Initial Process**:
   - System first uses current vector search results
   - If results < `nDocs`, backfilling begins
   - Looks through recent chat history (default: 20 messages)
   - Adds previously used sources until `nDocs` is reached

2. **Priority Order**:

   ```javascript
   const sources = [...searchResults];  // First priority
   // Then backfill from history if needed
   for (const chat of history.reverse()) {
     if (sources.length >= nDocs) break;
     // Add relevant historical sources
   }
   ```

## 2. Use Cases and Benefits

1. **Follow-up Questions**:

   ```plaintext
   User: "What is IST Legal?"  -> 4 relevant sources
   User: "Tell me its features"   -> 1 new source + 3 backfilled
   ```

   - Maintains context continuity
   - Improves response quality
   - Handles topic transitions

2. **Zero-Result Queries**:
   - Prevents empty context windows
   - Maintains conversation flow
   - Uses relevant historical context

## Implementation

### Core Function

The core of backfilling is implemented in the `fillSourceWindow` function:

```javascript
function fillSourceWindow({
  nDocs = 4,           // Target number of documents
  searchResults = [],  // Current vector search results
  history = [],        // Chat history
  filterIdentifiers = [] // To prevent duplicates
})
```

### Backfilling Process

1. **Initial Vector Results**:
   - First, the system uses the current vector search results
   - These are the most relevant documents for the current query

2. **Check for Deficit**:
   - If the number of results is less than the target (`nDocs`), backfilling begins
   - This ensures we always have a consistent number of sources when possible

3. **History Traversal**:
   - The system looks through recent chat history (default: last 20 messages)
   - It processes messages in reverse chronological order (newest first)
   - For each message, it checks if it has sources that aren't already included

4. **Source Addition**:
   - Previously used sources are added until we reach the target number
   - Duplicate prevention is handled via the `filterIdentifiers` parameter
   - Sources are added in order of recency, not relevance

### Implementation Code

```javascript
// Simplified implementation
function fillSourceWindow({ nDocs = 4, searchResults = [], history = [], filterIdentifiers = [] }) {
  // Start with current search results
  const sources = [...searchResults];
  const identifiers = new Set(filterIdentifiers);

  // Add sources from each result to the identifiers set
  searchResults.forEach(result => {
    if (result.metadata?.identifier) {
      identifiers.add(result.metadata.identifier);
    }
  });

  // If we already have enough sources, return them
  if (sources.length >= nDocs) {
    return sources;
  }

  // Otherwise, backfill from history
  const recentHistory = history.slice(-20).reverse(); // Last 20 messages, newest first

  for (const chat of recentHistory) {
    // Skip if we have enough sources
    if (sources.length >= nDocs) break;

    // Skip messages without sources
    if (!chat.sources || chat.sources.length === 0) continue;

    // Add sources that aren't already included
    for (const source of chat.sources) {
      if (sources.length >= nDocs) break;

      const identifier = source.metadata?.identifier;

      // Skip if this source is already included
      if (identifier && identifiers.has(identifier)) continue;

      // Add the source
      sources.push(source);
      if (identifier) identifiers.add(identifier);
    }
  }

  return sources;
}
```

## Provider Support

Different LLM providers have varying levels of support for backfilling:

1. **Full Support**:
   - Native implementation
   - Anthropic
   - Gemini
   - xAI

2. **Limited Support**:
   - OpenAI Compatible: No backfilling, uses aggregated results

## Configuration

Backfilling behavior can be configured through several parameters:

```javascript
// Adjust backfilling behavior
const config = {
  nDocs: 4,              // Target source count
  historyLimit: 20,      // Chat history depth
  preventDuplicates: true // Via filterIdentifiers
};
```

## Best Practices

1. **Maintain Reasonable Limits**:
   - Keep `nDocs` between 3-5 for most use cases
   - Limit history depth to 20-30 messages to prevent performance issues
   - Always implement duplicate prevention

2. **Balance with Vector Search**:
   - Backfilling should complement, not replace, vector search
   - Prioritize fresh vector results over historical sources
   - Use backfilling primarily for continuity, not as the main source of information

3. **Performance Considerations**:
   - Implement early exit when target count is reached
   - Use efficient data structures (Set) for duplicate checking
   - Consider caching frequently used sources
