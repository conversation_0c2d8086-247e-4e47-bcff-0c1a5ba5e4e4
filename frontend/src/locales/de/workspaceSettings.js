export default {
  // =========================
  // WORKSPACES SETTINGS MENU
  // =========================

  "workspaces-settings": {
    general: "Allgemeine Einstellungen",
    chat: "Chat-Einstellungen",
    vector: "Vektordatenbank",
    members: "Mitglieder",
    agent: "Agent-Konfiguration",
    "general-settings": {
      "workspace-name": "Name des Arbeitsbereichs",
      "desc-name": "Dies ändert nur den Anzeigenamen Ihres Arbeitsbereichs.",
      "assistant-profile": "Assistenten-Profilbild",
      "assistant-image":
        "Passen Sie das Profilbild des Assistenten für diesen Arbeitsbereich an.",
      "workspace-image": "Arbeitsbereich-Bild",
      "remove-image": "Arbeitsbereich-Bild entfernen",
      delete: "Arbeitsbereich löschen",
      deleting: "Arbeitsbereich wird gelöscht...",
      update: "Arbeitsbereich aktualisieren",
      updating: "Arbeitsbereich wird aktualisiert...",
    },
    "chat-settings": {
      type: "Chat-Typ",
      private: "Privat",
      standard: "Standard",
      "private-desc-start": "wird manuell Zugang gewähren zu",
      "private-desc-mid": "nur",
      "private-desc-end": "bestimmten Benutzern.",
      "standard-desc-start": "wird automatisch Zugang gewähren zu",
      "standard-desc-mid": "allen",
      "standard-desc-end": "neuen Benutzern.",
    },
    users: {
      manage: "Benutzer verwalten",
      "workspace-member": "Keine Arbeitsbereich-Mitglieder",
      username: "E-Mail-Adresse",
      role: "Rolle",
      default: "Standard",
      manager: "Manager",
      admin: "Administrator",
      superuser: "Superuser",
      "date-added": "Hinzugefügt am",
      users: "Benutzer",
      search: "Nach einem Benutzer suchen",
      "no-user": "Keine Benutzer gefunden",
      select: "Alle auswählen",
      unselect: "Auswahl aufheben",
      save: "Speichern",
    },
    "linked-workspaces": {
      title: "Verknüpfte Arbeitsbereiche",
      description:
        "Wenn Arbeitsbereiche verknüpft sind, werden rechtliche Daten, die für die Eingabeaufforderung relevant sind, automatisch aus jedem verknüpften Rechtsbereich abgerufen. Beachten Sie, dass verknüpfte Arbeitsbereiche die Verarbeitungszeit erhöhen",
      "linked-workspace": "Keine verknüpften Arbeitsbereiche",
      manage: "Arbeitsbereiche verwalten",
      name: "Name",
      slug: "Slug",
      date: "Hinzugefügt am",
      workspaces: "Arbeitsbereiche",
      search: "Nach einem Arbeitsbereich suchen",
      "no-workspace": "Keine Arbeitsbereiche gefunden",
      select: "Alle auswählen",
      unselect: "Auswahl aufheben",
      save: "Speichern",
    },
    "delete-workspace": "Arbeitsbereich löschen",
    "delete-workspace-message":
      "Sie sind dabei, Ihren gesamten {{workspace}}-Arbeitsbereich zu löschen. Dies entfernt alle Vektor-Einbettungen in Ihrer Vektordatenbank.\n\nDie ursprünglichen Quelldateien bleiben unberührt. Diese Aktion ist unumkehrbar.",
    "vector-database": {
      reset: {
        title: "Vektordatenbank zurücksetzen",
        message:
          "Sie sind dabei, die Vektordatenbank dieses Arbeitsbereichs zurückzusetzen. Dies entfernt alle aktuell eingebetteten Vektor-Einbettungen.\n\nDie ursprünglichen Quelldateien bleiben unberührt. Diese Aktion ist unumkehrbar.",
      },
    },
  },

  // =========================
  // GENERAL APPEARANCE & CUSTOMIZATION
  // =========================
  general: {
    vector: {
      title: "Vektor-Anzahl",
      description: "Gesamtanzahl der Vektoren in Ihrer Vektordatenbank.",
      vectors: "Anzahl der Vektoren",
    },
    names: {
      description: "Dies ändert nur den Anzeigenamen Ihres Arbeitsbereichs.",
    },
    message: {
      title: "Vorgeschlagene Chat-Nachrichten",
      description:
        "Passen Sie die Nachrichten an, die Ihren Arbeitsbereich-Benutzern vorgeschlagen werden.",
      add: "Neue Nachricht hinzufügen",
      save: "Nachrichten speichern",
      heading: "Erkläre mir",
      body: "die Vorteile der Plattform",
      message: "Nachricht",
      "new-heading": "Überschrift",
    },
    pfp: {
      title: "Assistenten-Profilbild",
      description:
        "Passen Sie das Profilbild des Assistenten für diesen Arbeitsbereich an.",
      image: "Arbeitsbereich-Bild",
      remove: "Arbeitsbereich-Bild entfernen",
    },
    delete: {
      delete: "Arbeitsbereich löschen",
      deleting: "Arbeitsbereich wird gelöscht...",
      "confirm-start": "Sie sind dabei, Ihren gesamten",
      "confirm-end":
        "Arbeitsbereich zu löschen. Dies entfernt alle Vektor-Einbettungen in Ihrer Vektordatenbank.\n\nDie ursprünglichen Quelldateien bleiben unberührt. Diese Aktion ist unumkehrbar.",
    },
  },

  // =========================
  // CHAT SETTINGS
  // =========================
  chat: {
    llm: {
      title: "Arbeitsbereich-LLM-Anbieter",
      description:
        "Der spezifische LLM-Anbieter und das Modell, das für diesen Arbeitsbereich verwendet wird. Standardmäßig verwendet es den System-LLM-Anbieter und die Einstellungen.",
      search: "Alle LLM-Anbieter durchsuchen",
      "save-error":
        "Fehler beim Speichern der {{provider}}-Einstellungen: {{error}}",
      setup: "Einrichten",
      use: "Um zu verwenden",
      "need-setup": "müssen Sie die folgenden Anmeldedaten einrichten.",
      cancel: "Abbrechen",
      save: "Speichern",
      settings: "Einstellungen",
      "multi-model": "Dieser Anbieter unterstützt keine mehreren Modelle.",
      "workspace-use": "Ihr Arbeitsbereich verwendet das in",
      "model-set": "Systemeinstellungen konfigurierte Modell",
      "system-default": "Systemstandard",
      "system-default-desc":
        "Verwenden Sie die System-LLM-Präferenz für diesen Arbeitsbereich.",
      "no-selection": "Kein LLM ausgewählt",
      "select-provider": "Wählen Sie einen LLM-Anbieter",
      "system-standard-name": "System Standard",
      "system-standard-desc":
        "Verwenden Sie das primäre LLM, das in den Systemeinstellungen definiert ist.",
    },
    "speak-prompt": "Sprechen Sie Ihre Eingabeaufforderung",
    "view-agents":
      "Alle verfügbaren Agenten anzeigen, die Sie zum Chatten verwenden können",
    "ability-tag": "Fähigkeit",
    "change-text-size": "Textgröße ändern",
    "aria-text-size": "Textgröße ändern",

    model: {
      title: "Arbeitsbereich-Chat-Modell",
      description:
        "Das spezifische Chat-Modell, das für diesen Arbeitsbereich verwendet wird. Wenn leer, wird die System-LLM-Präferenz verwendet.",
      wait: "-- warte auf Modelle --",
      general: "Allgemeine Modelle",
      custom: "Benutzerdefinierte Modelle",
    },
    mode: {
      title: "Chat-Modus",
      chat: {
        title: "Chat",
        "desc-start": "wird Antworten mit dem allgemeinen Wissen des LLM",
        and: "und",
        "desc-end": "gefundenem Dokumentkontext liefern.",
      },
      query: {
        title: "Abfrage",
        "desc-start": "wird Antworten liefern",
        only: "nur",
        "desc-end": "wenn Dokumentkontext gefunden wird.",
      },
    },
    history: {
      title: "Chat-Verlauf",
      "desc-start":
        "Die Anzahl der vorherigen Chats, die im Kurzzeitgedächtnis der Antwort enthalten sein werden.",
      recommend: "Empfohlen 20. ",
      "desc-end":
        "Alles über 45 führt wahrscheinlich zu kontinuierlichen Chat-Fehlern, abhängig von der Nachrichtengröße.",
    },
    prompt: {
      title: "Eingabeaufforderung",
      description:
        "Die Eingabeaufforderung, die in diesem Arbeitsbereich verwendet wird. Definieren Sie den Kontext und die Anweisungen für die KI, um eine Antwort zu generieren. Sie sollten eine sorgfältig ausgearbeitete Eingabeaufforderung bereitstellen, damit die KI eine relevante und genaue Antwort generieren kann.",
    },
    refusal: {
      title: "Ablehnungsantwort im Abfrage-Modus",
      "desc-start": "Im",
      query: "Abfrage",
      "desc-end":
        "-Modus möchten Sie möglicherweise eine benutzerdefinierte Ablehnungsantwort zurückgeben, wenn kein Kontext gefunden wird.",
    },
    temperature: {
      title: "LLM-Temperatur",
      "desc-start":
        'Diese Einstellung steuert, wie "kreativ" Ihre LLM-Antworten sein werden.',
      "desc-end":
        "Je höher die Zahl, desto kreativer. Bei einigen Modellen kann dies zu inkohärenten Antworten führen, wenn sie zu hoch eingestellt ist.",
      hint: "Die meisten LLMs haben verschiedene akzeptable Bereiche gültiger Werte. Konsultieren Sie Ihren LLM-Anbieter für diese Informationen.",
    },
    "dynamic-pdr": {
      title: "Dynamisches PDR für Arbeitsbereich",
      description:
        "Aktivieren oder deaktivieren Sie dynamisches PDR für diesen Arbeitsbereich.",
      "global-enabled":
        "Dynamisches PDR ist global aktiviert und kann nicht für einzelne Arbeitsbereiche deaktiviert werden.",
    },
    display_prompt_output_description:
      "Anzeige der Eingabeaufforderungsausgabe-Protokollierung, Datei öffnen und herunterladen",
    display_prompt_output: "Eingabeaufforderungsausgabe anzeigen",
    loading_prompt_output: "Eingabeaufforderungsausgabe wird geladen...",
    prompt_output_not_available:
      "*** Eingabeaufforderungsausgabe ist für diesen Chat nicht verfügbar.",
    open_in_new_tab: "In neuem Tab öffnen",
  },

  // =========================
  // VECTOR DATABASE (WORKSPACE)
  // =========================
  "vector-workspace": {
    identifier: "Vektordatenbank-Kennung",
    snippets: {
      title: "Max. Kontext-Schnipsel",
      description:
        "Diese Einstellung steuert die maximale Anzahl von Kontext-Schnipseln, die pro Chat oder Abfrage an das LLM gesendet werden.",
      recommend:
        "Empfohlener Wert ist mindestens 30. Das Einstellen viel höherer Zahlen erhöht die Verarbeitungszeit, ohne notwendigerweise die Präzision zu verbessern, abhängig von der Kapazität des verwendeten LLM.",
    },
    doc: {
      title: "Dokument-Ähnlichkeitsschwelle",
      description:
        "Der minimale Ähnlichkeitswert, der erforderlich ist, damit eine Quelle als mit dem Chat verwandt betrachtet wird. Je höher die Zahl, desto ähnlicher muss die Quelle zum Chat sein.",
      zero: "Keine Beschränkung",
      low: "Niedrig (Ähnlichkeitswert ≥ .25)",
      medium: "Mittel (Ähnlichkeitswert ≥ .50)",
      high: "Hoch (Ähnlichkeitswert ≥ .75)",
    },
    reset: {
      reset: "Vektordatenbank zurücksetzen",
      resetting: "Vektoren werden gelöscht...",
      confirm:
        "Sie sind dabei, die Vektordatenbank dieses Arbeitsbereichs zurückzusetzen. Dies entfernt alle aktuell eingebetteten Vektor-Einbettungen.\n\nDie ursprünglichen Quelldateien bleiben unberührt. Diese Aktion ist unumkehrbar.",
      error:
        "Die Arbeitsbereich-Vektordatenbank konnte nicht zurückgesetzt werden!",
      success: "Die Arbeitsbereich-Vektordatenbank wurde zurückgesetzt!",
    },
    prompt: { placeholder: "Stellen Sie hier Ihre Frage..." },
    refusal: {
      placeholder: "Entschuldigung, ich kann diese Frage nicht beantworten",
    },
  },

  // =========================
  // AGENT CONFIGURATION
  // =========================
  agent: {
    "performance-warning":
      "Die Leistung von LLMs, die Tool-Calling nicht explizit unterstützen, hängt stark von den Fähigkeiten und der Genauigkeit des Modells ab. Einige Fähigkeiten können eingeschränkt oder nicht funktional sein.",
    provider: {
      title: "Arbeitsbereich-Agent-LLM-Anbieter",
      description:
        "Der spezifische LLM-Anbieter und das Modell, das für den @agent-Agenten dieses Arbeitsbereichs verwendet wird.",
      "need-setup":
        "Um {{name}} als Agent-LLM für diesen Arbeitsbereich zu verwenden, müssen Sie es zuerst einrichten.",
    },
    mode: {
      chat: {
        title: "Arbeitsbereich-Agent-Chat-Modell",
        description:
          "Das spezifische Chat-Modell, das für den @agent-Agenten dieses Arbeitsbereichs verwendet wird.",
      },
      title: "Arbeitsbereich-Agent-Modell",
      description:
        "Das spezifische LLM-Modell, das für den @agent-Agenten dieses Arbeitsbereichs verwendet wird.",
      wait: "-- warte auf Modelle --",
    },
    skill: {
      title: "Standard-Agent-Fähigkeiten",
      description:
        "Verbessern Sie die natürlichen Fähigkeiten des Standard-Agenten mit diesen vorgefertigten Fähigkeiten. Diese Einrichtung gilt für alle Arbeitsbereiche.",
      rag: {
        title: "RAG & Langzeitgedächtnis",
        description:
          'Erlauben Sie dem Agenten, Ihre lokalen Dokumente zu nutzen, um eine Abfrage zu beantworten, oder bitten Sie den Agenten, Inhaltsteile für den Langzeitgedächtnisabruf zu "merken".',
      },
      configure: {
        title: "Agent-Fähigkeiten konfigurieren",
        description:
          "Passen Sie die Fähigkeiten des Standard-Agenten an und erweitern Sie sie, indem Sie spezifische Fähigkeiten aktivieren oder deaktivieren. Diese Einstellungen werden auf alle Arbeitsbereiche angewendet.",
      },
      view: {
        title: "Dokumente anzeigen & zusammenfassen",
        description:
          "Erlauben Sie dem Agenten, den Inhalt der aktuell eingebetteten Arbeitsbereich-Dateien aufzulisten und zusammenzufassen.",
      },
      scrape: {
        title: "Websites scrapen",
        description:
          "Erlauben Sie dem Agenten, Websites zu besuchen und deren Inhalt zu scrapen.",
      },
      generate: {
        title: "Diagramme generieren",
        description:
          "Aktivieren Sie den Standard-Agenten, um verschiedene Arten von Diagrammen aus bereitgestellten oder im Chat gegebenen Daten zu generieren.",
      },
      save: {
        title: "Dateien generieren & im Browser speichern",
        description:
          "Aktivieren Sie den Standard-Agenten, um Dateien zu generieren und zu schreiben, die gespeichert und in Ihrem Browser heruntergeladen werden können.",
      },
      web: {
        title: "Live-Websuche und -browsing",
        "desc-start":
          "Aktivieren Sie Ihren Agenten, um das Web zu durchsuchen, um Ihre Fragen zu beantworten, indem Sie sich mit einem Websuch-(SERP-)Anbieter verbinden.",
        "desc-end":
          "Die Websuche während Agent-Sitzungen funktioniert nicht, bis dies eingerichtet ist.",
      },
    },
  },
};
