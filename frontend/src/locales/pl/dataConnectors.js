export default {
  // =========================
  // DATA CONNECTORS
  // =========================
  dataConnectors: {
    github: {
      name: "Repozytorium GitHub",
      description:
        "Zaimportuj całe publiczne lub prywatne repozytorium GitHub jednym kliknięciem.",
      url: "URL repozytorium GitHub",
      "collect-url": "URL repozytorium GitHub, które chcesz zebrać.",
      "access-token": "Token dostępu GitHub",
      optional: "opcjonalnie",
      "rate-limiting": "Token dostępu, aby zapobiec ograniczeniom szybkości.",
      "desc-picker":
        "Po zakończeniu wszystkie pliki będą dostępne do osadzania w przestrzeniach roboczych w przeglądarce dokumentów.",
      branch: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      "branch-desc": "<PERSON><PERSON><PERSON><PERSON><PERSON>, z której chcesz zebrać pliki.",
      "branch-loading": "-- ładowanie dostępnych gałęzi --",
      "desc-start": "Bez wypełnienia",
      "desc-token": "Tokena dostępu GitHub",
      "desc-connector": "ten łącznik danych będzie mógł zebrać tylko",
      "desc-level": "pliki z najwyższego poziomu",
      "desc-end": "repozytorium, ze względów publicznego API GitHub.",
      "personal-token":
        "Uzyskaj darmowy token dostępu osobistego za pomocą konta GitHub tutaj.",
      without: "Bez",
      "personal-token-access": "dostępu do osobistego tokena",
      "desc-api":
        ", publiczne API GitHub może ograniczyć liczbę plików, które można zebrać ze względu na ograniczenia szybkości. Możesz",
      "temp-token": "utworzyć tymczasowy token dostępu",
      "avoid-issue": "aby uniknąć tego problemu.",
      submit: "Wyślij",
      "collecting-files": "Zbieranie plików...",
    },
    "youtube-transcript": {
      name: "Transkrypcja YouTube",
      description:
        "Zaimportuj transkrypcję całego filmu z YouTube za pomocą linku.",
      url: "URL filmu z YouTube",
      "url-video": "URL filmu z YouTube, którego transkrypcję chcesz uzyskać.",
      collect: "Pobierz transkrypcję",
      collecting: "Pobieranie transkrypcji...",
      "desc-end":
        "po zakończeniu transkrypcja będzie dostępna do osadzania w przestrzeniach roboczych w przeglądarce dokumentów.",
    },
    "website-depth": {
      name: "Bulk Link Scraper",
      description:
        "Zeskanuj stronę internetową i jej podlinki do określonej głębokości.",
      url: "URL strony internetowej",
      "url-scrape": "URL strony, którą chcesz zeskanować.",
      depth: "Głębokość",
      "child-links":
        "To liczba podlinków, które robot powinien śledzić od URL źródłowego.",
      "max-links": "Maksymalna liczba linków",
      "links-scrape": "Maksymalna liczba linków do zeskanowania.",
      scraping: "Skanowanie strony...",
      submit: "Wyślij",
      "desc-scrap":
        "Po zakończeniu wszystkie zeskanowane strony będą dostępne do osadzania w przestrzeniach roboczych w przeglądarce dokumentów.",
    },
    confluence: {
      name: "Confluence",
      description: "Zaimportuj całą stronę Confluence jednym kliknięciem.",
      url: "URL strony Confluence",
      "url-page": "URL strony w przestrzeni Confluence.",
      username: "Nazwa użytkownika Confluence",
      "own-username": "Twoja nazwa użytkownika Confluence.",
      token: "Token dostępu Confluence",
      "desc-start":
        "Musisz podać token dostępu do uwierzytelnienia. Możesz wygenerować token dostępu",
      here: "tutaj",
      access: "Token dostępu do uwierzytelnienia.",
      collecting: "Zbieranie stron...",
      submit: "Wyślij",
      "desc-end":
        "Po zakończeniu wszystkie strony będą dostępne do osadzania w przestrzeniach roboczych.",
    },
  },
  // =========================
  // CONFLUENCE CONNECTOR COMPONENT
  // =========================
  confluence: {
    "space-key": "Klucz przestrzeni Confluence",
    "space-key-desc":
      "Jest to klucz przestrzeni Twojej instancji Confluence, który będzie używany. Zwykle zaczyna się od ~",
    "space-key-placeholder": "np.: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "np.: https://example.atlassian.net, http://localhost:8211, itd...",
    "token-tooltip": "Możesz utworzyć token API",
    "token-tooltip-here": "tutaj",
  },
};
