const TRANSLATIONS = {
  // =========================
  // SYSTEM PROMPT EDIT
  // =========================
  "system-prompt-edit": {
    "button-label": "System prompt edit",
    "button-tooltip": "Customize your system prompt",
    "modal-title": "Edit System Prompt",
    "modal-description":
      "Customize the AI assistant's behavior by changing the system's basic settings. With a modified system prompt, you can, for example, instruct for shorter or longer responses, not limit yourself to retrieved sources but include the AI engine's inherent knowledge, change tone, and so on. For more information, see for example https://www.promptingguide.ai/.",
    "current-prompt-label": "Current System Prompt",
    "base-prompt-label": "Base System Prompt (without your customization)",
    "custom-prompt-label": "Your Custom System Prompt",
    "custom-prompt-placeholder": "Enter your custom system prompt...",
    "save-button": "Save Custom Prompt",
    "cancel-button": "Cancel",
    "clear-button": "Clear Custom Prompt",
    "library-button": "Library",
    "success-message": "Custom system prompt saved successfully",
    "clear-success": "Custom system prompt cleared successfully",
    "error-message": "Failed to save custom system prompt",
    "error-loading": "Failed to load system prompt settings",
    "using-custom-prompt": "Using your custom system prompt",
    "your-custom-overrides":
      "Your custom prompt below overrides this base prompt",
    "using-workspace-prompt": "Using workspace prompt",
    "using-default-prompt": "Using system default prompt",
    "character-limit": "Maximum 10,000 characters",
  },

  // =========================
  // PROMPT LIBRARY
  // =========================
  "prompt-library": {
    "modal-title": "Prompt Library",
    "modal-description":
      "Manage your saved prompts. Save frequently used prompts, organize them with descriptions, and quickly apply them to your system prompt.",
    "empty-state": "No saved prompts yet",
    "empty-description":
      "Save your current prompt or create a new one to get started",
    "save-current-button": "Save Current Prompt",
    "create-new-button": "Create New Prompt",
    "close-button": "Close",
    loading: "Loading prompts...",
    "error-loading": "Failed to load prompt library",

    // Prompt item actions
    "apply-button": "Apply",
    "edit-button": "Edit",
    "delete-button": "Delete",
    "preview-button": "Preview",
    "show-preview": "Show Preview",
    "hide-preview": "Hide Preview",

    // Form labels
    "name-label": "Prompt Name",
    "name-placeholder": "Enter a name for this prompt...",
    "description-label": "Description (optional)",
    "description-placeholder": "Brief description of this prompt...",
    "prompt-text-label": "Prompt Text",
    "prompt-text-placeholder": "Enter your prompt text...",

    // Actions
    "save-button": "Save Prompt",
    "update-button": "Update Prompt",
    "cancel-button": "Cancel",

    // Messages
    "save-success": "Prompt saved successfully",
    "update-success": "Prompt updated successfully",
    "delete-success": "Prompt deleted successfully",
    "apply-success": "Prompt applied successfully",
    "save-error": "Failed to save prompt",
    "update-error": "Failed to update prompt",
    "delete-error": "Failed to delete prompt",
    "apply-error": "Failed to apply prompt",

    // Validation
    "name-required": "Prompt name is required",
    "prompt-text-required": "Prompt text is required",
    "name-too-long": "Name cannot exceed 100 characters",
    "description-too-long": "Description cannot exceed 500 characters",
    "prompt-text-too-long": "Prompt text cannot exceed 10,000 characters",

    // Confirmation
    "delete-confirm-title": "Delete Prompt",
    "delete-confirm-message":
      "Are you sure you want to delete this prompt? This action cannot be undone.",
    "delete-confirm-button": "Delete",
    "delete-cancel-button": "Cancel",

    // Character limits
    "character-count": "{{count}} / {{max}} characters",
  },
};

export default TRANSLATIONS;
