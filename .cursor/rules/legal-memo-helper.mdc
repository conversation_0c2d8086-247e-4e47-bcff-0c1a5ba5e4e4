---
description:
globs:
alwaysApply: false
---
# Legal Memo Helper

## Overview

This rule provides **backend development guidelines** for generating or fetching legal memos throughout the code-base.  All backend modules that need a legal memo **must** call the central helper exported from `server/utils/helpers/legalMemo.js` instead of re-implementing memo logic.

> **Why?**  The helper already takes care of:
>
> 1. Re-using an existing `LLMConnector` instance so we do **not** duplicate costly model initialisation.
> 2. Token accounting & automatic enforcement of `memoTokenLimit`.
> 3. Source tracking for returned memos so they can be surfaced in the UI.
> 4. Uniform error handling and logging.

Following these guidelines guarantees consistent memo generation behaviour and keeps the code minimal, self-documenting, and secure.

---

## Usage

```js
const { generateLegalMemo } = require("../../utils/helpers/legalMemo");

const { memo, tokenCount, sources } = await generateLegalMemo({
  workspace,                      // Prisma workspace record (must include slug)
  systemPrompt: memoPrompt,       // String ‑ system message
  userPrompt: legalIssues,        // String ‑ user / issue description
  LLMConnector,                   // Optional — existing connector instance
  temperature,                    // Optional — defaults to 0.7
  tokenLimit: ddMemoTokenLimit,   // Optional — hard token limit
  settings: {
    isDocumentDraftingLinked,
    ddLinkedWorkspaceImpact,
    ddMemoEnabled,
    ddVectorEnabled,
  },
});
```

### Parameter Reference

| Name | Type | Required | Description |
| ---- | ---- | -------- | ----------- |
| `workspace` | `Object` | **Yes** | The workspace record that the memo belongs to (must contain `slug`). |
| `systemPrompt` | `string` | **Yes** | Prompt used as **system** message. |
| `userPrompt` | `string` | **Yes** | Prompt used as **user** message (usually the legal issue). |
| `LLMConnector` | `Object` | No | Existing LLM connector instance. If omitted, a default provider is resolved automatically. |
| `temperature` | `number` | No | Sampling temperature (default `0.7`). |
| `tokenLimit` | `number|null` | No | Hard token limit for the generated memo. `null` means no limit. |
| `settings` | `Object|null` | No | Extra settings passed straight through to `DocumentDrafting`. |

### Do's & Don'ts

✅ **Do** import and call `generateLegalMemo` whenever a backend module needs a memo.

✅ **Do** pass an existing `LLMConnector` instance when you already have one (e.g., inside streaming endpoints) to avoid re-initialisation.

✅ **Do** set a sensible `tokenLimit` if you need to guarantee upper bounds.

---

❌ **Don't** duplicate memo-generation logic directly inside other modules.

❌ **Don't** modify the helper in-place for special-case logic; extend via parameters instead.

---

## Testing

Unit tests for the helper reside in `server/tests/legalMemo.test.js`.  Whenever you extend the helper or its contract, update tests accordingly.

## Rule Metadata

- **Type**: Auto Attached
- **Globs**: `server/**/*.js`

Any server-side JavaScript file automatically receives this rule in context.
