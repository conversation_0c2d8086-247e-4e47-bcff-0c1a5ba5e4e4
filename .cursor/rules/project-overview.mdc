---
description: Project overview and high-level architecture
globs:
alwaysApply: true
---
# ISTLegal Project Overview

This codebase powers an application in which users can perform **Retrieval-Augmented Generation (RAG)** queries with various Large Language Model (LLM) providers, vector databases, and additional functionality such as:

- **Document Ingestion & Sync** (upload, watch external sources, auto-sync changed content)
- **Vector Database Integration** (Pinecone, Qdrant, Weaviate, LanceDB, etc.)
- **RAG-based Chat** (embedding relevant documents in conversation prompts)
- **LLM Management** (OpenAI, Anthropic, Azure, LocalAI, and more)
- **User & Workspace** multi-tenant architecture
- **Agent** plugins for advanced or tool-based queries (SQL, web scraping, memory, etc.)
- **Telemetry**, **Swagger (OpenAPI) Docs**, **Custom Settings**

## Development Environment

### Auto-Reloading Servers

**Critical for Development**: All servers in the development environment are configured to automatically restart/reload when code changes are detected. **You do NOT need to manually restart servers during development.**

- **Backend Server** (`server/`): Automatically restarts on file changes
- **Frontend Development Server** (`frontend/`): Hot Module Replacement (HMR) and auto-reload
- **Collector Service** (`collector/`): Automatically restarts on file changes

**Development Workflow:**
1. Make code changes and save files
2. Servers automatically detect changes and restart/reload
3. Test your changes immediately after auto-restart completes
4. No terminal commands needed for server management

**Exception**: Manual restart only required for environment variable changes or configuration file modifications.

## Architecture Components

The application consists of three main components:

1. **Frontend**: React application with components for chat, document management, and settings
2. **Server**: Express server handling API requests, LLM integration, and vector database management
3. **Collector**: Service for ingesting and processing documents from various sources

## Key Directories

- `frontend/`: React application with components, contexts, hooks, and pages
- `server/`: Express server with endpoints, models, and utilities
- `collector/`: Document ingestion and processing service
- `.cursor/rules`: Documentation generated for autocoding context
