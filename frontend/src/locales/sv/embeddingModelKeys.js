export default {
  // =========================
  // EMBEDDER (EMBEDDING PROVIDER NAMES)
  // =========================
  embeder: {
    allm: "Använd den inbyggda inbäddningsleverantören. Ingen konfiguration behövs!",
    openai: "Standardalternativ för de flesta icke-kommersiella användningar.",
    azure: "Företagsalternativ för OpenAI som är värd på Azure-tjänster.",
    localai: "Kör inbäddningsmodeller lokalt på din egen maskin.",
    ollama: "Kör inbäddningsmodeller lokalt på din egen maskin.",
    lmstudio:
      "Upptäck, ladda ner och kör tusentals av de senaste LLMs med ett klick.",
    cohere: "Kör kraftfulla inbäddningsmodeller från Cohere.",
    voyageai: "Kör kraftfulla inbäddningsmodeller från Voyage AI.",
    "generic-openai": "Använd en generisk OpenAI-inbäddningsmodell.",
    "default.embedder": "Standardinbäddningsleverantör",
    jina: "Jina AI's text-embedding models for multilingual and high-performance embeddings.", //TODO: Translate this
    litellm: "Kör kraftfulla inbäddningsmodeller från LiteLLM.",
  },
  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Vanliga strängar
    "provider-logo": "{{provider}} logotyp",

    // LMStudio-inbäddningsalternativ
    lmstudio: {
      "model-label": "LM Studio Inbäddningsmodell",
      "max-chunk-length": "Maximal segmentlängd",
      "max-chunk-length-help": "Maximal längd på textsegment för inbäddning.",
      "hide-endpoint": "Dölj manuell slutpunktsinmatning",
      "show-endpoint": "Visa manuell slutpunktsinmatning",
      "base-url": "LM Studio Bas-URL",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help": "Ange URL:en där LM Studio körs.",
      "auto-detect": "Upptäck automatiskt",
      "loading-models": "--laddar tillgängliga modeller--",
      "enter-url-first": "Ange först LM Studio-URL",
      "model-help":
        "Välj LM Studio-modellen för inbäddningar. Modeller laddas efter att en giltig LM Studio-URL har angetts.",
      "loaded-models": "Dina inlästa modeller",
    },

    // Ollama-inbäddningsalternativ
    ollama: {
      "model-label": "Ollama Inbäddningsmodell",
      "max-chunk-length": "Maximal segmentlängd",
      "max-chunk-length-help": "Maximal längd på textsegment för inbäddning.",
      "hide-endpoint": "Dölj manuell slutpunktsinmatning",
      "show-endpoint": "Visa manuell slutpunktsinmatning",
      "base-url": "Ollama Bas-URL",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help": "Ange URL:en där Ollama körs.",
      "auto-detect": "Upptäck automatiskt",
      "loading-models": "--laddar tillgängliga modeller--",
      "enter-url-first": "Ange först Ollama-URL",
      "model-help":
        "Välj Ollama-modellen för inbäddningar. Modeller laddas efter att en giltig Ollama-URL har angetts.",
      "loaded-models": "Dina inlästa modeller",
    },

    // LiteLLM-inbäddningsalternativ
    litellm: {
      "model-label": "Val av inbäddningsmodell",
      "max-chunk-length": "Maximal segmentlängd",
      "max-chunk-length-help": "Maximal längd på textsegment för inbäddning.",
      "api-key": "API-nyckel",
      optional: "valfritt",
      "api-key-placeholder": "sk-mysecretkey",
      "loading-models": "-- laddar tillgängliga modeller --",
      "waiting-url": "-- väntar på URL --",
      "loaded-models": "Dina inlästa modeller",
      "model-tooltip": "Visa stödda inbäddningsmodeller på",
      "model-tooltip-link": "LiteLLM:s dokumentation",
      "model-tooltip-more": "för mer information om tillgängliga modeller.",
    },

    // Cohere-inbäddningsalternativ
    cohere: {
      "api-key": "Cohere API-nyckel",
      "api-key-placeholder": "Ange din Cohere API-nyckel",
      "model-label": "Val av modell",
      "available-models": "Tillgängliga inbäddningsmodeller",
    },

    // Jina-inbäddningsalternativ
    jina: {
      "api-key": "Jina API-nyckel",
      "api-key-format": "Jina API-nyckel måste börja med 'jina_'",
      "api-key-placeholder": "Ange din Jina API-nyckel",
      "api-key-error": "API-nyckeln måste börja med 'jina_'",
      "model-label": "Val av modell",
      "available-models": "Tillgängliga inbäddningsmodeller",
      "embedding-type": "Inbäddningstyp",
      "available-types": "Tillgängliga inbäddningstyper",
      dimensions: "Dimensioner",
      "available-dimensions": "Tillgängliga dimensioner",
      task: "Uppgift",
      "available-tasks": "Tillgängliga uppgifter",
      "late-chunking": "Sen segmentering",
      "late-chunking-help": "Aktivera sen segmentering för dokumentbehandling",
    },

    // LocalAI-inbäddningsalternativ
    localai: {
      "model-label": "Namn på inbäddningsmodell",
      "hide-endpoint": "Dölj avancerade inställningar",
      "show-endpoint": "Visa avancerade inställningar",
      "base-url": "Local AI Bas-URL",
      "base-url-placeholder": "http://localhost:8080/v1",
      "base-url-help": "Ange URL:en där LocalAI körs.",
      "auto-detect": "Upptäck automatiskt",
      "loading-models": "-- laddar tillgängliga modeller --",
      "waiting-url": "-- väntar på URL --",
      "loaded-models": "Dina inlästa modeller",
    },

    // Generiska OpenAI-kompatibla inbäddningsalternativ
    generic: {
      "base-url": "Bas-URL",
      "base-url-placeholder": "https://api.openai.com/v1",
      "base-url-help": "Ange bas-URL för din OpenAI-kompatibla API-slutpunkt.",
      "model-label": "Inbäddningsmodell",
      "model-placeholder": "Ange modellnamn (t.ex. text-embedding-ada-002)",
      "model-help": "Ange modellidentifieraren för att generera inbäddningar.",
      "api-key": "API-nyckel",
      "api-key-placeholder": "sk-mysecretkey",
      "api-key-help": "Ange din API-nyckel för autentisering.",
    },

    // OpenAI-inbäddningsalternativ
    openai: {
      "api-key": "OpenAI API-nyckel",
      "api-key-placeholder": "Ange din OpenAI API-nyckel",
      "model-label": "Val av modell",
      "available-models": "Tillgängliga inbäddningsmodeller",
    },

    // VoyageAI-inbäddningsalternativ
    voyageai: {
      "api-key": "VoyageAI API-nyckel",
      "api-key-placeholder": "Ange din VoyageAI API-nyckel",
      "model-label": "Val av modell",
      "available-models": "Tillgängliga inbäddningsmodeller",
    },

    // Azure OpenAI-inbäddningsalternativ
    azureai: {
      "service-endpoint": "Azure OpenAI-tjänstens slutpunkt",
      "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
      "service-endpoint-help": "Ange URL för din Azure OpenAI-tjänst",
      "api-key": "Azure OpenAI API-nyckel",
      "api-key-placeholder": "Ange din Azure OpenAI API-nyckel",
      "api-key-help": "Ange din Azure OpenAI API-nyckel för autentisering",
      "deployment-name": "Distribueringsnamn för inbäddningsmodell",
      "deployment-name-placeholder":
        "Ange ditt distribueringsnamn för Azure OpenAI-inbäddningsmodell",
      "deployment-name-help":
        "Distribueringsnamnet för din Azure OpenAI-inbäddningsmodell",
    },

    // Inbyggd (native) inbäddningsleverantör
    native: {
      description: "Använder inbyggd inbäddningsleverantör för textbehandling",
    },
  },
  // =========================
  // JINA
  // =========================
  jina: {
    "api-key": "Jina API-nyckel",
    "api-key-placeholder": "Ange din Jina API-nyckel",
    "api-key-format": "Jina API-nyckel måste börja med 'jina_'",
    "model-preference": "Modellpreferens",
  },
  // =========================
  // OLLAMA
  // =========================
  ollama: {
    "max-embedding-chunk-length": "Maximal inbäddningschunklängd",
  },
  // =========================
  // VOYAGEAI
  // =========================
  voyageai: {
    "api-key": "VoyageAI API-nyckel",
    "api-key-placeholder": "Ange din VoyageAI API-nyckel",
    "model-preference": "Modellpreferens",
  },
};
