const TRANSLATIONS = {
  "scroll-threshold-settings": {
    title: "Scroll Behavior Settings",
    description:
      "Configure the sensitivity of auto-scrolling in the chat view. Lower values are more sensitive.",
    saved: "Scroll behavior settings have been updated.",
    saving: "Saving settings...",
    "save-changes": "Save Changes",
    "fetch-error": "Failed to load scroll settings.",
    error: "Failed to save scroll settings.",
    increase: "Increase",
    decrease: "Decrease",
    "bottom-threshold": {
      title: "Bottom Threshold (pixels)",
      description:
        "The distance from the bottom of the chat to be considered 'at the bottom'.",
    },
    "streaming-disable-threshold": {
      title: "Streaming Disable Threshold (pixels)",
      description:
        "How far the user must scroll up during message streaming to disable auto-scroll.",
    },
    "auto-scroll-threshold": {
      title: "Auto-scroll Threshold (pixels)",
      description:
        "The distance from the bottom where auto-scroll will re-engage.",
    },
  },
};
export default TRANSLATIONS;
