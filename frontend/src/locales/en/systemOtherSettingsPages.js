export default {
  // Bsed on this order of pages in system settings:
  // Customization (Appearance)
  //Admin: System settings
  //Admin: Users
  //Admin: Invites
  //Admin: Workspaces
  //Admin: MCP Servers
  //Admin: Feedback List
  //Admin: Workspace chats
  //Admin: Default system
  //Admin: Document Builder
  //Admin: DD Prompt settings
  //Admin: Chat UI settings
  //AI-Providers: LLM
  //AI-Providers: Prompt Upgrade LLM
  //AI-Providers: Validation LLM
  //AI-Providers: CDB LLM
  //AI-Providers: Custom User AI
  //AI-Providers:  Vector Database
  //AI-Providers: Re-ranking settings
  //AI-Providers: Deep search
  //AI-Providers: Embedder
  //AI-Providers: Text Splitter & Chunking
  //AI-Providers: Voice & Speech
  //AI-Providers: transcription
  //AI-Providers: PDR System
  //Agent skills
  //Tools: Chat Embed History
  //Tools: Chat Embed
  //Tools: Event logs
  //Tools: Developer API
  //Tools: Browser Extension
  //Security
  //Document Drafting: Configuration
  //Document Drafting: Drafting LLM
  //Document Drafting: Chat Settings

  // =========================
  // LLM PREFERENCE PAGE
  // =========================
  llm: {
    title: "LLM Preference",
    description:
      "These are the credentials and settings for your preferred LLM chat & embedding provider. Its important these keys are current and correct or else the system will not function properly.",
    provider: "LLM Provider",
    "secondary-provider": "Secondary LLM Provider",
    "none-selected": "None selected",
    "select-llm": "Agents will not work until a valid selection is made.",
    "search-llm": "Search all LLM providers",
    "context-window-warning":
      "Warning: Could not fetch context window for the selected model.",
    "context-window-waiting": " -- waiting for context window information -- ",
    "validation-prompt": {
      disable: {
        label: "Disable Validation Prompt",
        description:
          "When enabled, the validation button will not appear in the UI.",
      },
    },
    "prompt-upgrade": {
      title: "Prompt Upgrade LLM Provider",
      description:
        "The specific LLM provider & model that will be used for upgrading user prompt. By default, it uses the system LLM provider and settings.",
      search: "Search available LLM providers for the feature",
      template: "Prompt Upgrade Template",
      "template-description":
        "This template will be used when upgrading prompts. Use {{prompt}} to refer to the text that should be upgraded.",
      "template-placeholder":
        "Enter the template that will be used for upgrading prompts...",
      "template-hint":
        "Example: Please upgrade the following text while maintaining its meaning: {{prompt}}",
    },
    "logo-alt": "{{name}} logo",
    "context-window": "Context Window",
    "default-context-window": "(default size for this provider)",
    tokens: "tokens",
    "save-error": "Failed to save LLM settings",
  },
  // =========================
  // LLM PROVIDER DESCRIPTIONS
  // =========================
  "llm-provider": {
    openai: "The standard option for most non-commercial use.",
    azure: "The enterprise option of OpenAI hosted on Azure services.",
    anthropic: "A friendly AI Assistant hosted by Anthropic.",
    gemini: "Google's largest and most capable AI model",
    huggingface:
      "Access 150,000+ open-source LLMs and the world's AI community",
    ollama: "Run LLMs locally on your own machine.",
    lmstudio:
      "Discover, download, and run thousands of cutting edge LLMs in a few clicks.",
    localai: "Run LLMs locally on your own machine.",
    togetherai: "Run open source models from Together AI.",
    mistral: "Run open source models from Mistral AI.",
    perplexityai:
      "Run powerful and internet-connected models hosted by Perplexity AI.",
    openrouter: "A unified interface for LLMs.",
    groq: "The fastest LLM inferencing available for real-time AI applications.",
    koboldcpp: "Run local LLMs using koboldcpp.",
    oobabooga: "Run local LLMs using Oobabooga's Text Generation Web UI.",
    cohere: "Run Cohere's powerful Command models.",
    lite: "Run LiteLLM's OpenAI compatible proxy for various LLMs.",
    "generic-openai":
      "Connect to any OpenAI-compatible service via a custom configuration",
    native:
      "Use a downloaded custom Llama model for chatting on this instance.",
    xai: "Run xAI's powerful LLMs like Grok-2 and more.",
    "aws-bedrock": "Run powerful foundation models privately with AWS Bedrock.",
    deepseek: "Run DeepSeek's powerful LLMs.",
    fireworksai:
      "The fastest and most efficient inference engine to build production-ready, compound AI systems.",
    bedrock: "Run powerful foundation models privately with AWS Bedrock.",
  },
  // =========================
  // CUSTOM USER AI SETTINGSPAGE
  // =========================
  "custom-user-ai": {
    title: "Custom User AI",
    settings: "Custom User AI",
    description: "Configure the Custom User AI Provider",
    "custom-model-reference": "Custom Model Name & Description",
    "custom-model-reference-description":
      "Add a custom reference for this model. This will be visible when using the Custom User AI engine selector in the prompt panel.",
    "custom-model-reference-name": "Custom Model Name",
    "custom-model-reference-description-label": "Model Description (Optional)",
    "custom-model-reference-description-placeholder":
      "Enter an optional description for this model",
    "custom-model-reference-name-placeholder":
      "Enter a custom name for this model",
    "model-ref-placeholder":
      "Enter a custom name or description for this model setup",
    "enter-custom-model-reference": "Enter a custom name for this model",
    "standard-engine": "Standard AI Engine",
    "standard-engine-description": "Our default engine useful for most tasks",
    "dynamic-context-window-percentage": "Dynamic Context Window Percentage",
    "dynamic-context-window-percentage-desc":
      "Controls how much of this LLM's context window can be used for additional sources (10-100%)",
    "no-alternative-title": "No Alternative Model Selected",
    "no-alternative-desc":
      "When this option is selected, users do not have the ability to select an alternative model.",
    "select-option": "Select Custom AI Profile",
    tab: {
      "custom-1": "Custom Engine 1",
      "custom-2": "Custom Engine 2",
      "custom-3": "Custom Engine 3",
      "custom-4": "Custom Engine 4",
      "custom-5": "Custom Engine 5",
      "custom-6": "Custom Engine 6",
    },
    engine: {
      "custom-1": "Custom Engine 1",
      "custom-2": "Custom Engine 2",
      "custom-3": "Custom Engine 3",
      "custom-4": "Custom Engine 4",
      "custom-5": "Custom Engine 5",
      "custom-6": "Custom Engine 6",
      "custom-1-title": "Custom Engine 1",
      "custom-2-title": "Custom Engine 2",
      "custom-3-title": "Custom Engine 3",
      "custom-4-title": "Custom Engine 4",
      "custom-5-title": "Custom Engine 5",
      "custom-6-title": "Custom Engine 6",
      "custom-1-description": "Configure settings for Custom Engine 1",
      "custom-2-description": "Configure settings for Custom Engine 2",
      "custom-3-description": "Configure settings for Custom Engine 3",
      "custom-4-description": "Configure settings for Custom Engine 4",
      "custom-5-description": "Configure settings for Custom Engine 5",
      "custom-6-description": "Configure settings for Custom Engine 6",
    },
    "option-number": "Option {{number}}",
    "llm-provider-selection": "LLM Provider Selection",
    "llm-provider-selection-desc":
      "Choose the LLM provider for this custom AI configuration",
    "custom-option": "Custom Option",
    saving: "Saving...",
    "save-changes": "Save Changes",
    "model-ref-saved": "Custom model settings saved successfully",
    "model-ref-save-failed": "Failed to save custom model settings: {{error}}",
    "llm-settings-save-failed": "Failed to save LLM settings: {{error}}",
    "settings-fetch-failed": "Failed to fetch settings",
    "llm-saved": "LLM settings saved successfully",
    "select-provider-first":
      "Please select an LLM provider to configure model settings. Once configured, this option will be selectable as a custom AI engine in the user interface.",
  },
  // =========================
  // CDB LLM PREFERENCE
  // =========================
  "cdb-llm-preference": {
    title: "CDB LLM Preference",
    settings: "CDB LLM",
    description: "Configure the LLM provider for CDB",
  },
  // =========================
  // TEMPLATE LLM PREFERENCE
  // =========================
  "template-llm-preference": {
    title: "Template LLM Preference",
    settings: "Template LLM",
    description:
      "Select the LLM provider used when generating document templates. Defaults to the system provider if unset.",
    "toast-success": "Template LLM settings updated",
    "toast-fail": "Failed to update Template LLM settings",
    saving: "Saving...",
    "save-changes": "Save changes",
  },

  // =========================
  // AUDIO PREFERENCE
  // =========================
  audio: {
    title: "Speech-to-text Preference",
    provider: "Provider",
    "system-native": "System Native",
    "desc-speech":
      "Here you can specify what kind of text-to-speech and speech-to-text providers you would want to use in your platform experience. By default, we use the browser's built in support for these services, but you may want to use others.",
    "title-text": "Text-to-speech Preference",
    "desc-text":
      "Here you can specify what kind of text-to-speech providers you would want to use in your platform experience. By default, we use the browser's built in support for these services, but you may want to use others.",
    "desc-config": "No configuration needed for browser native text to speech.",
    "placeholder-stt": "Search speech to text providers",
    "placeholder-tts": "Search text to speech providers",
    "native-stt": "Uses your browser's built in STT service if supported.",
    "native-tts": "Uses your browser's built in TTS service if supported.",
    "piper-tts": "Run TTS models locally in your browser privately.",
    "openai-description": "Use OpenAI's text to speech voices and technology.",
    openai: {
      "api-key": "API Key",
      "api-key-placeholder": "OpenAI API Key",
      "voice-model": "Voice Model",
    },
    elevenlabs: "Use ElevenLabs's text to speech voices and technology.",
  },

  // =========================
  // TRANSCRIPTION PREFERENCE
  // =========================
  transcription: {
    title: "Transcription Model Preference",
    description:
      "These are the credentials and settings for your preferred transcription model provider. Its important these keys are current and correct or else media files and audio will not transcribe.",
    provider: "Transcription Provider",
    "warn-start":
      "Using the local whisper model on machines with limited RAM or CPU can stall the platform when processing media files.",
    "warn-recommend":
      "We recommend at least 2GB of RAM and upload files <10Mb.",
    "warn-end":
      "The built-in model will automatically download on the first use.",
    "search-audio": "Search audio transcription providers",
    "api-key": "API Key",
    "api-key-placeholder": "OpenAI API Key",
    "whisper-model": "Whisper Model",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Default Built-In",
    "default-built-in-desc":
      "Run a built-in whisper model on this instance privately.",
    "openai-name": "OpenAI",
    "openai-desc":
      "Leverage the OpenAI Whisper-large model using your API key.",
    "model-turbo": "openai/whisper-large-v3-turbo", // New model name
    "model-size-turbo": "(~810mb)", // New model size
  },

  // =========================
  // EMBEDDING PREFERENCE
  // =========================
  embedding: {
    title: "Embedding Preference",
    "desc-start":
      "When using an LLM that does not natively support an embedding engine - you may need to additionally specify credentials to for embedding text.",
    "desc-end":
      "Embedding is the process of turning text into vectors. These credentials are required to turn your files and prompts into a format which the platform can use to process.",
    provider: {
      title: "Embedding Provider",
      description:
        "There is no set up required when using the platform's native embedding engine.",
      "search-embed": "Search all embedding providers",
      search: "Search all embedding providers",
      select: "Select an embedding provider",
    },
    workspace: {
      title: "Workspace Embedding preference",
      description:
        "The specific embedding provider & model that will be used for this workspace. By default, it uses the system embedding provider and settings.",
      "multi-model":
        "Multi-model support is not supported for this provider yet.",
      "workspace-use": "This workspace will use",
      "model-set": "the model set for the system.",
      embedding: "Workspace Embedding model",
      model:
        "The specific embedding model that will be used for this workspace. If empty, will use the system embedding preference.",
      wait: "-- waiting for models --",
      setup: "Setup",
      use: "To use",
      "need-setup": "as this workspace embedder you need to set it up first.",
      cancel: "Cancel",
      save: "save",
      settings: "settings",
      search: "Search all embedding providers",
      "need-llm": "as this workspace LLM you need to set it up first.",
      "save-error": "Failed to save {{provider}} settings: {{error}}",
      "system-default": "System Default",
      "system-default-desc":
        "Use the system embedding preference for this workspace.",
    },
    warning: {
      "switch-model":
        "Switching the embedding model will break previously embedded documents from working during chat. They will need to un-embed from every workspace and fully removed and re-uploaded so they can be embed by the new embedding model.",
    },
  },

  // =========================
  // TEXT SPLITTING & CHUNKING
  // =========================
  text: {
    title: "Text Splitting & Chunking Settings",
    "desc-start":
      "Configure how your documents are split into chunks for processing.",
    "desc-end":
      "These settings affect how documents are processed and embedded.",
    "warn-start": "Warning:",
    "warn-center":
      "Changing these settings will only affect newly processed documents.",
    "warn-end":
      "Existing documents will need to be reprocessed to use the new settings.",
    method: {
      title: "Text Splitter Method",
      "native-explain": "Use local chunk size & overlap for splitting.",
      "jina-explain": "Delegate chunking/segmenting to Jina's built-in method.",
      "jina-info": "Jina chunking active.",
      jina: {
        api_key: "Jina API Key",
        api_key_desc:
          "Required for using Jina's segmentation service. The key will be stored in your environment.",
        max_tokens: "Jina: Max Tokens per Chunk",
        max_tokens_desc:
          "Defines the max tokens in each chunk for Jina's segmenter (maximum 2000 tokens).",
        return_tokens: "Return token information",
        return_tokens_desc:
          "Include token count and tokenizer information in the response.",
        return_chunks: "Return chunk information",
        return_chunks_desc:
          "Include chunk positions and metadata in the response.",
      },
    },
    size: {
      title: "Text Chunk Size",
      description:
        "This is the maximum length of characters that can be present in a single vector.",
      recommend: "Embed model maximum length is",
    },
    overlap: {
      title: "Text Chunk Overlap",
      description:
        "This is the maximum overlap of characters that occurs during chunking between two adjacent text chunks.",
      error: "Chunk overlap cannot be larger or equal to chunk size.",
    },
  },
  // =========================
  // CONTEXTUAL SETTINGS
  // =========================
  contextual: {
    checkbox: {
      label: "Contextual Embedding",
      hint: "Enable contextual embedding to enhance the embedding process with additional parameters",
    },
    systemPrompt: {
      label: "System Prompt",
      placeholder: "Enter a value...",
      description:
        "Example: Please give a short succinct context to situate this chunk within the overall document for the purposes of improving search retrieval of the chunk. Answer only with the succinct context and nothing else.",
    },
    userPrompt: {
      label: "User Prompt",
      placeholder: "Enter a value...",
      description:
        "Example: <document>\n{file}\n</document>\nHere is the chunk we want to situate within the whole document\n<chunk>\n{chunk}\n</chunk>",
    },
  },

  // =========================
  // CHAT UI SETTINGS
  // =========================
  "chat-ui-settings": {
    title: "Chat UI Settings",
    description: "Configure the chat settings.",
    auto_submit: {
      title: "Auto-Submit Speech Input",
      description:
        "Automatically submit speech input after a period of silence",
    },
    auto_speak: {
      title: "Auto-Speak Responses",
      description: "Automatically speak responses from the AI",
    },
  },
  // =========================
  // PIPER TTS OPTIONS (COMPONENT IN PAGE)
  // =========================
  piperTTS: {
    description:
      "All PiperTTS models will run in your browser locally. This can be resource intensive on lower-end devices.",
    "voice-model": "Voice Model Selection",
    "loading-models": "-- loading available models --",
    "stored-indicator":
      'The "✔" indicates this model is already stored locally and does not need to be downloaded when run.',
    "flush-cache": "Flush voice cache",
    "flush-success": "All voices flushed from browser storage",
    demo: {
      stop: "Stop demo",
      loading: "Loading voice",
      play: "Play sample",
      text: "Hello, welcome to IST Legal!",
    },
  },
  // =========================
  // VECTOR DATABASE (SYSTEM)
  // =========================
  vector: {
    title: "Vector Database",
    description:
      "These are the credentials and settings for how your platform instance will function. It's important these keys are current and correct.",
    provider: {
      title: "Vector Database Provider",
      description: "There is no configuration needed for LanceDB.",
      "search-db": "Search all vector database providers",
      search: "Search all vector databases",
      select: "Select Vector Database Provider",
    },
    warning: {
      "switch-db":
        "Switching the vector database will require you to re-embed all documents across all relevant workspaces. This may take some time.",
    },
    search: {
      title: "Vector Search Mode",
      mode: {
        "globally-enabled":
          "This setting is controlled globally in system settings. Visit system settings to change the re-rank behavior.",
        default: "Default Search",
        "default-desc": "Standard vector similarity search without re-ranking.",
        "accuracy-optimized": "Accuracy Optimized",
        "accuracy-desc":
          "Re-ranks results to improve accuracy using cross-attention.",
      },
    },
  },
  // =========================
  // VECTOR DATABASE PROVIDER DESCRIPTIONS
  // =========================
  vectordb: {
    lancedb:
      "100% local vector DB that runs on the same instance as the platform.",
    chroma:
      "Open source vector database you can host yourself or on the cloud.",
    pinecone: "100% cloud-based vector database for enterprise use cases.",
    zilliz:
      "Cloud hosted vector database built for enterprise with SOC 2 compliance.",
    qdrant: "Open source local and distributed cloud vector database.",
    weaviate: "Open source local and cloud hosted multi-modal vector database.",
    milvus: "Open-source, highly scalable, and blazing fast.",
    astra: "Vector Search for Real-world GenAI.",
  },
  // =========================
  // DEEP SEARCH SETTINGS PAGE
  // =========================
  deep_search: {
    title: "Deep Search",
    description:
      "Configure web search capabilities for chat responses. When enabled, the system can search the web for information to enhance responses.",
    enable: "Enable Deep Search",
    enable_description:
      "Allow the system to search the web for information when responding to queries.",
    provider_settings: "Provider Settings",
    provider: "Search Provider",
    model: "Model",
    api_key: "API Key",
    api_key_placeholder: "Enter your API key",
    api_key_placeholder_set: "API key is set (enter new key to change)",
    api_key_help:
      "Your API key is stored securely and used only for web search requests.",
    context_percentage: "Context Percentage",
    context_percentage_help:
      "Percentage of the LLM's context window to allocate for web search results (5-20%).",
    fetch_error: "Failed to fetch Deep Search settings",
    save_success: "Deep Search settings saved successfully",
    save_error: "Failed to save Deep Search settings: {{error}}",
    toast_success: "Deep Search settings saved successfully",
    toast_error: "Failed to save Deep Search settings: {{error}}",
    brave_recommended:
      "Brave Search is currently the recommended and most reliable provider option.",
  },
  // =========================
  // PDR SETTINGS PAGE
  // =========================
  "pdr-settings": {
    title: "PDR Settings",
    description:
      "Configure Parent Document Retrieval settings for your workspaces.",
    "desc-end":
      "These settings affect how PDR documents are processed and used in chat responses.",
    "global-override": {
      title: "Global Dynamic PDR Override",
      description:
        "When enabled, this will handle all workspace documents as PDR-enabled for context in responses. When disabled, only documents explicitly marked as PDR will be used, which may reduce available context and rely result in much lower quality answers since only vector chunks from search will be used as sources in those cases.",
    },
    "toast-success": "PDR settings updated",
    "toast-fail": "Failed to update PDR settings",
    "adjacent-vector-limit": "Adjacent Vector Limit",
    "adjacent-vector-limit-desc": "Limit for adjacent vectors.",
    "adjacent-vector-limit-placeholder": "Enter adjacent vector limit",
    "keep-pdr-vectors": "Keep PDR Vectors",
    "keep-pdr-vectors-desc": "Option to keep PDR vectors.",
  },
  // =========================
  // ADMIN AGENTS
  // =========================
  agents: {
    title: "Agent Skills",
    "agent-skills": "Configure and manage agent capabilities",
    "custom-skills": "Custom Skills",
    back: "Back",
    "select-skill": "Select a skill to configure",
    "preferences-saved": "Agent preferences saved successfully.",
    "preferences-failed": "Agent preferences failed to save.",
    "skill-status": { on: "On", off: "Off" },
    "skill-config-updated": "Skill config updated successfully",
  },
  // =========================
  // AGENT MENU
  // =========================
  "agent-menu.default-agent": "Default Agent",
  "agent-menu.ability.rag-search": "RAG Search",
  "agent-menu.ability.web-scraping": "Web Scraping",
  "agent-menu.ability.web-browsing": "Web Browsing",
  "agent-menu.ability.save-file-to-browser": "Save File to Browser",
  "agent-menu.ability.list-documents": "List Documents",
  "agent-menu.ability.summarize-document": "Summarize Document",
  "agent-menu.ability.chart-generation": "Chart Generation",
  // =========================
  // BADGES AGENT PAGE
  // =========================
  badges: {
    default: {
      text: "Default",
      tooltip: "This skill is enabled by default and cannot be turned off.",
    },
  },
  // =========================
  // EMBED CHATS HISTORY
  // =========================
  "embed-chats": {
    title: "Embed Chats",
    export: "Export",
    description:
      "These are all the recorded chats and messages from any embed that you have published.",
    table: {
      embed: "Embed",
      sender: "Sender",
      message: "Message",
      response: "Response",
      at: "Sent At",
    },
    delete: {
      title: "Delete Chat",
      message:
        "Are you sure you want to delete this chat?\n\nThis action is irreversible.",
    },
    config: {
      "delete-title": "Delete embed",
      "delete-message":
        "Are you sure you want to delete this embed? Once deleted this embed will no longer respond to chats or be active. This action is irreversible.",
      "disable-title": "Disable embed",
      "disable-message":
        "Are you sure you want to disable this embed?\n\nOnce disabled the embed will no longer respond to any chat requests.",
      "enable-title": "Enable embed",
      "enable-message":
        "Are you sure you want to enable this embed? Once enabled the embed will respond to chat requests again.",
    },
  },
  // =========================
  // EMBEDDABLE CHAT WIDGETS
  // =========================
  embeddable: {
    title: "Embeddable Chat Widgets",
    description:
      "Embeddable chat widgets are public facing chat interfaces that are tied to a single workspace. These allow you to build workspaces that then you can publish to the world.",
    create: "Create embed",
    table: {
      workspace: "Workspace",
      chats: "Sent Chats",
      Active: "Active Domains",
    },
  },
  // =========================
  // CHAT EMBED PLUGIN PAGE
  // =========================
  "new-embed": {
    title: "Create new embed for workspace",
    error: "Error: ",
    "desc-start":
      "After creating an embed you will be provided a link that you can publish on your website with a simple",
    script: "script",
    tag: "tag.",
    cancel: "Cancel",
    "create-embed": "Create embed",
    workspace: "Workspace",
    "desc-workspace":
      "This is the workspace your chat window will be based on. All defaults will be inherited from the workspace unless overridden by this config.",
    "allowed-chat": "Allowed chat method",
    "desc-query":
      "Set how your chatbot should operate. Query means it will only respond if a document helps answer the query.",
    "desc-chat":
      "Chat opens the chat to even general questions and can answer totally unrelated queries to your workspace.",
    "desc-response": "Chat: Respond to all questions regardless of context",
    "query-response":
      "Query: Only respond to chats related to documents in workspace",
    restrict: "Restrict requests from domains",
    filter:
      "This filter will block any requests that come from a domain other than the list below.",
    "use-embed":
      "Leaving this empty means anyone can use your embed on any site.",
    "max-chats": "Max chats per day",
    "limit-chats":
      "Limit the amount of chats this embedded chat can process in a 24 hour period. Zero is unlimited.",
    "chats-session": "Max chats per session",
    "limit-chats-session":
      "Limit the amount of chats a session user can send with this embed in a 24 hour period. Zero is unlimited.",
    "enable-dynamic": "Enable dynamic model use",
    "llm-override":
      "Allow setting of the preferred LLM model to override the workspace default.",
    "llm-temp": "Enable dynamic LLM temperature",
    "desc-temp":
      "Allow setting of the LLM temperature to override the workspace default.",
    "prompt-override": "Enable Prompt Override",
    "desc-override":
      "Allow setting of the system prompt to override the workspace default.",
  },
  // =========================
  // EMBED CONFIGS
  // =========================
  embedConfigs: {
    "show-code": "Show Code",
    enable: "Enable",
    disable: "Disable",
    "all-domains": "all",
    "disable-confirm":
      "Are you sure you want to disabled this embed?\nOnce disabled the embed will no longer respond to any chat requests.",
    "delete-confirm":
      "Are you sure you want to delete this embed?\nOnce deleted this embed will no longer respond to chats or be active.\n\nThis action is irreversible.",
    "disabled-toast": "Embed has been disabled",
    "enabled-toast": "Embed is active",
  },
  // =========================
  // EVENT LOGS
  // =========================
  event: {
    title: "Event Logs",
    description:
      "View all actions and events happening on this instance for monitoring.",
    clear: "Clear Event Logs",
    table: { type: "Event Type", user: "User", occurred: "Occurred At" },
  },
  // =========================
  // API KEYS
  // =========================
  api: {
    title: "API Keys",
    description:
      "API keys allow the holder to programmatically access and manage this instance.",
    link: "Read the API documentation",
    generate: "Generate New API Key",
    table: { key: "API Key", by: "Created By", created: "Created" },
    new: {
      title: "Create new API key",
      description:
        "Once created the API key can be used to programmatically access and configure this instance.",
      doc: "Read the API documentation",
      cancel: "Cancel",
      "create-api": "Create API key",
    },
  },

  // =========================
  // BROWSER EXTENSION API KEYS, SETTINGS PAGE
  // =========================
  "browser-extension-api": {
    title: "API Keys",
    description: "Manage API keys for connecting to this instance.",
    "generate-key": "Generate New API Key",
    "table-headers": {
      "connection-string": "Connection String",
      "created-by": "Created By",
      "created-at": "Created At",
      actions: "Actions",
    },
    "no-keys": "No API keys found",
    modal: {
      title: "New Browser Extension API Key",
      "multi-user-warning":
        "Warning: You are in multi-user mode, this API key will allow access to all workspaces associated with your account. Please share it cautiously.",
      "create-description":
        'After clicking "Create API Key", this instance will attempt to create a new API key for you to use with the browser extension.',
      "connection-help":
        'If you see "Connected to IST Legal" in the extension, the connection was successful. If not, please copy the connection string and paste it into the extension manually.',
      cancel: "Cancel",
      "create-key": "Create API Key",
      "copy-key": "Copy API Key",
      "key-copied": "API Key Copied!",
    },
    tooltips: {
      "copy-connection": "Copy connection string",
      "auto-connect": "Automatically connect to extension",
    },
    confirm: {
      revoke:
        "Are you sure you want to revoke this browser extension API key?\nAfter you do this it will no longer be useable.\n\nThis action is irreversible.",
    },
    toasts: {
      "key-revoked": "Browser Extension API Key permanently revoked",
      "revoke-failed": "Failed to revoke API Key",
      copied: "Connection string copied to clipboard",
      connecting: "Attempting to connect to browser extension...",
    },
    "revoke-title": "Revoke Browser Extension API Key",
    "revoke-message":
      "Are you sure you want to revoke this browser extension API key?\nAfter you do this it will no longer be useable.\n\nThis action is irreversible.",
  },

  // =========================
  // SECURITY
  // =========================
  security: {
    "multi-user-mode-permanent":
      "Multi-user mode is permanently enabled for security reasons",
    "password-validation": {
      "restricted-chars":
        "Your password has restricted characters in it. Allowed symbols are _,-,!,@,$,%,^,&,*,(,),;",
    },
    "public-workspace": {
      "access-description":
        "When enabled, any user can access the public workspaces without logging in.",
    },
    button: { saving: "Saving...", "save-changes": "Save changes" },
  },

  // =========================
  // MULTI-USER MODE
  // =========================
  multi: {
    title: "Multi-User Mode",
    description:
      "Set up your instance to support your team by activating Multi-User Mode.",
    enable: {
      "is-enable": "Multi-User Mode is Enabled",
      enable: "Enable Multi-User Mode",
      description:
        "By default, you will be the only admin. As an admin you will need to create accounts for all new users or admins. Do not lose your password as only an Admin user can reset passwords.",
      username: "Admin account email",
      password: "Admin account password",
      "username-placeholder": "Your admin username",
      "password-placeholder": "Your admin password",
    },
    password: {
      title: "Password Protection",
      description:
        "Protect your instance with a password. If you forget this there is no recovery method so ensure you save this password.",
    },
    instance: {
      title: "Password Protect Instance",
      description:
        "By default, you will be the only admin. As an admin you will need to create accounts for all new users or admins. Do not lose your password as only an Admin user can reset passwords.",
      password: "Instance password",
    },
  },
  // =========================
  // EXPERIMENTAL FEATURES
  // =========================
  experimental: {
    title: "Experimental Features",
    description: "Features that are currently in beta testing phase",
    "live-sync": {
      title: "Live Document Sync",
      description:
        "Enable automatic content synchronization from external sources",
      "manage-title": "Watched documents",
      "manage-description":
        "These are all the documents that are currently being watched in your instance. The content of these documents will be periodically synced.",
      "document-name": "Document Name",
      "last-synced": "Last Synced",
      "next-refresh": "Time until next refresh",
      "created-on": "Created On",
      "auto-sync": "Automatic Content Sync",
      "sync-description":
        'Enable the ability to specify a content source to be "watched". Watched content will be regularly fetched and updated in this instance.',
      "sync-workspace-note":
        "Watched content will automatically update in all workspaces they are referenced in at the same time of update.",
      "sync-limitation":
        "This feature only applies to web-based content, such as websites, Confluence, YouTube, and GitHub files.",
      documentation: "Feature Documentation and Warnings",
      "manage-content": "Manage Watched Content",
    },
    tos: {
      title: "Terms of use for experimental features",
      description:
        "Experimental features of this platform are features that we are piloting and are opt-in. We proactively will condition or warn you on any potential concerns should any exist prior to approval of any feature.",
      "possibilities-title":
        "Use of any feature on this page can result in, but not limited to, the following possibilities.",
      possibilities: {
        "data-loss": "Loss of data.",
        "quality-change": "Change in quality of results.",
        "storage-increase": "Increased storage.",
        "resource-consumption": "Increased resource consumption.",
        "cost-increase":
          "Increased cost or use of any connected LLM or embedding provider.",
        "potential-bugs": "Potential bugs or issues using this application.",
      },
      "conditions-title":
        "Use of an experimental feature also comes with the following list of non-exhaustive conditions.",
      conditions: {
        "future-updates": "Feature may not exist in future updates.",
        stability: "The feature being used is not currently stable.",
        availability:
          "The feature may not be available in future versions, configurations, or subscriptions of this instance.",
        privacy:
          "Your privacy settings will be honored with use of any beta feature.",
        changes: "These conditions may change in future updates.",
      },
      "read-more": "If you would like to read more you can refer to",
      contact: "or email",
      reject: "Reject & Close",
      accept: "I understand",
    },
    "update-failed": "Failed to update status of feature",
  },
};
