import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, jest, beforeEach } from "@jest/globals";
import SystemPromptEditButton from "../index";

// Mock dependencies
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key) => {
      const translations = {
        "system-prompt-edit.button-label": "System prompt edit",
        "system-prompt-edit.button-tooltip": "Customize your system prompt",
      };
      return translations[key] || key;
    },
  }),
}));

jest.mock("@/hooks/useUser", () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock("../../SystemPromptEditModal", () => {
  return function MockSystemPromptEditModal({ isOpen, onClose }) {
    return isOpen ? (
      <div data-testid="system-prompt-edit-modal">
        <button onClick={onClose}>Close Modal</button>
      </div>
    ) : null;
  };
});

jest.mock("@/components/Button", () => ({
  Button: ({ children, onClick, className, ...props }) => (
    <button onClick={onClick} className={className} {...props}>
      {children}
    </button>
  ),
}));

jest.mock("react-tooltip", () => ({
  Tooltip: ({ id }) => <div data-testid={`tooltip-${id}`} />,
}));

jest.mock("react-icons/tb", () => ({
  TbSettings: () => <span data-testid="settings-icon" />,
}));

const useUser = require("@/hooks/useUser").default;

describe("SystemPromptEditButton", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders button with correct text and icon", () => {
    useUser.mockReturnValue({
      user: { custom_system_prompt: null },
    });

    render(<SystemPromptEditButton />);

    expect(screen.getByRole("button")).toBeInTheDocument();
    expect(screen.getByText("System prompt edit")).toBeInTheDocument();
    expect(screen.getByTestId("settings-icon")).toBeInTheDocument();
    expect(
      screen.getByTestId("tooltip-system-prompt-edit")
    ).toBeInTheDocument();
  });

  it("applies different styling when user has custom prompt", () => {
    useUser.mockReturnValue({
      user: { custom_system_prompt: "My custom prompt" },
    });

    render(<SystemPromptEditButton />);

    const button = screen.getByRole("button");
    expect(button).toHaveClass("bg-secondary-hover");
  });

  it("does not apply special styling when user has no custom prompt", () => {
    useUser.mockReturnValue({
      user: { custom_system_prompt: null },
    });

    render(<SystemPromptEditButton />);

    const button = screen.getByRole("button");
    expect(button).not.toHaveClass("bg-secondary-hover");
  });

  it("does not apply special styling when user has empty custom prompt", () => {
    useUser.mockReturnValue({
      user: { custom_system_prompt: "" },
    });

    render(<SystemPromptEditButton />);

    const button = screen.getByRole("button");
    expect(button).not.toHaveClass("bg-secondary-hover");
  });

  it("opens modal when button is clicked", () => {
    useUser.mockReturnValue({
      user: { custom_system_prompt: null },
    });

    render(<SystemPromptEditButton />);

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(screen.getByTestId("system-prompt-edit-modal")).toBeInTheDocument();
  });

  it("closes modal when onClose is called", () => {
    useUser.mockReturnValue({
      user: { custom_system_prompt: null },
    });

    render(<SystemPromptEditButton />);

    // Open modal
    const button = screen.getByRole("button");
    fireEvent.click(button);
    expect(screen.getByTestId("system-prompt-edit-modal")).toBeInTheDocument();

    // Close modal
    const closeButton = screen.getByText("Close Modal");
    fireEvent.click(closeButton);
    expect(
      screen.queryByTestId("system-prompt-edit-modal")
    ).not.toBeInTheDocument();
  });

  it("handles user being null", () => {
    useUser.mockReturnValue({
      user: null,
    });

    render(<SystemPromptEditButton />);

    const button = screen.getByRole("button");
    expect(button).not.toHaveClass("bg-secondary-hover");
    expect(screen.getByText("System prompt edit")).toBeInTheDocument();
  });

  it("handles user being undefined", () => {
    useUser.mockReturnValue({
      user: undefined,
    });

    render(<SystemPromptEditButton />);

    const button = screen.getByRole("button");
    expect(button).not.toHaveClass("bg-secondary-hover");
    expect(screen.getByText("System prompt edit")).toBeInTheDocument();
  });

  it("has correct accessibility attributes", () => {
    useUser.mockReturnValue({
      user: { custom_system_prompt: null },
    });

    render(<SystemPromptEditButton />);

    const button = screen.getByRole("button");
    expect(button).toHaveAttribute("aria-label", "System prompt edit");
    expect(button).toHaveAttribute(
      "data-tooltip-content",
      "Customize your system prompt"
    );
  });
});
