import { useState, useCallback } from "react";
import { getActiveReference } from "@/utils/constants";
import { useInvoiceLogging } from "@/stores/settingsStore";
import showToast from "@/utils/toast";
import { useTranslation } from "react-i18next";

const clearActiveReference = () => {
  localStorage.removeItem("activeReference");
};

export default function useNavigationWithInvoiceCheck() {
  const { t } = useTranslation();
  const { invoice: invoiceLogging } = useInvoiceLogging();
  const [showModal, setShowModal] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);
  const [destinationType, setDestinationType] = useState("");

  const checkAndNavigate = useCallback(
    (navigationFn, destinationType = "location") => {
      const activeReference = getActiveReference();

      // Only check if invoice logging is enabled
      if (!invoiceLogging) {
        navigationFn();
        return;
      }

      // If no active reference, proceed with navigation
      if (!activeReference) {
        navigationFn();
        return;
      }

      // Store the pending navigation and show modal
      const wrappedNavigation = () => {
        navigationFn();
      };

      setPendingNavigation(() => wrappedNavigation);
      setDestinationType(destinationType);
      setShowModal(true);
    },
    [invoiceLogging]
  );

  const handleClearAndContinue = useCallback(() => {
    // Clear the active reference
    clearActiveReference();

    // Show success toast
    showToast(t("invoice-reference-navigation.success"), "success");

    // Execute the pending navigation
    if (pendingNavigation) {
      pendingNavigation();
    }

    // Reset state
    setShowModal(false);
    setPendingNavigation(null);
    setDestinationType("");
  }, [pendingNavigation, t]);

  const handleKeepAndContinue = useCallback(() => {
    // Execute the pending navigation without clearing reference
    if (pendingNavigation) {
      pendingNavigation();
    }

    // Reset state
    setShowModal(false);
    setPendingNavigation(null);
    setDestinationType("");
  }, [pendingNavigation]);

  const handleCloseModal = useCallback(() => {
    // Just close the modal without navigating
    setShowModal(false);
    setPendingNavigation(null);
    setDestinationType("");
  }, []);

  return {
    checkAndNavigate,
    showModal,
    destinationType,
    handleClearAndContinue,
    handleKeepAndContinue,
    handleCloseModal,
  };
}
