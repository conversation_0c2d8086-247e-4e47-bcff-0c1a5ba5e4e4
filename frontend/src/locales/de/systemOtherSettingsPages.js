export default {
  // Bsed on this order of pages in system settings:
  // Customization (Appearance)
  //Admin: System settings
  //Admin: Users
  //Admin: Invites
  //Admin: Workspaces
  //Admin: MCP Servers
  //Admin: Feedback List
  //Admin: Workspace chats
  //Admin: Default system
  //Admin: Document Builder
  //Admin: DD Prompt settings
  //Admin: Chat UI settings
  //AI-Providers: LLM
  //AI-Providers: Prompt Upgrade LLM
  //AI-Providers: Validation LLM
  //AI-Providers: CDB LLM
  //AI-Providers: Custom User AI
  //AI-Providers:  Vector Database
  //AI-Providers: Re-ranking settings
  //AI-Providers: Deep search
  //AI-Providers: Embedder
  //AI-Providers: Text Splitter & Chunking
  //AI-Providers: Voice & Speech
  //AI-Providers: transcription
  //AI-Providers: PDR System
  //Agent skills
  //Tools: Chat Embed History
  //Tools: Chat Embed
  //Tools: Event logs
  //Tools: Developer API
  //Tools: Browser Extension
  //Security
  //Document Drafting: Configuration
  //Document Drafting: Drafting LLM
  //Document Drafting: Chat Settings

  // =========================
  // LLM PREFERENCE PAGE
  // =========================
  llm: {
    title: "LLM-Voreinstellung",
    description:
      "Dies sind die Zugangsdaten und Einstellungen für deinen bevorzugten LLM-Chat- und Embedding-Anbieter. Es ist wichtig, dass diese Schlüssel aktuell und korrekt sind, sonst funktioniert das System nicht ordnungsgemäß.",
    provider: "LLM-Anbieter",
    "secondary-provider": "Sekundärer LLM-Anbieter",
    "none-selected": "Keiner ausgewählt",
    "select-llm":
      "Agenten funktionieren nicht, bis eine gültige Auswahl getroffen wurde.",
    "search-llm": "Durchsuche alle LLM-Anbieter",
    "context-window-warning":
      "Warnung: Kontextfenster für das ausgewählte Modell konnte nicht abgerufen werden.",
    "context-window-waiting": " -- warte auf Kontextfensterinformationen -- ",
    "validation-prompt": {
      disable: {
        label: "Validierungs-Prompt deaktivieren",
        description:
          "Wenn aktiviert, wird die Validierungsschaltfläche in der Benutzeroberfläche nicht angezeigt.",
      },
    },
    "prompt-upgrade": {
      title: "LLM-Anbieter für Prompt-Verbesserung",
      description:
        "Der spezifische LLM-Anbieter und das Modell, das für die Verbesserung von Benutzer-Prompts verwendet wird. Standardmäßig werden der System-LLM-Anbieter und dessen Einstellungen verwendet.",
      search: "Verfügbare LLM-Anbieter für diese Funktion durchsuchen",
      template: "Prompt-Verbesserungsvorlage",
      "template-description":
        "Diese Vorlage wird beim Verbessern von Prompts verwendet. Verwenden Sie {{prompt}}, um sich auf den zu verbessernden Text zu beziehen.",
      "template-placeholder":
        "Geben Sie die Vorlage ein, die für die Verbesserung von Prompts verwendet werden soll...",
      "template-hint":
        "Beispiel: Bitte verbessern Sie den folgenden Text unter Beibehaltung seiner Bedeutung: {{prompt}}",
    },
    "logo-alt": "{{name}} logo",
    "context-window": "Kontextfenster",
    "default-context-window": "(Standardgröße für diesen Anbieter)",
    tokens: "Tokens",
    "save-error": "LLM-Einstellungen konnten nicht gespeichert werden",
  },

  // =========================
  // LLM PROVIDER DESCRIPTIONS
  // =========================
  "llm-provider": {
    openai: "Die Standardoption für die meisten nicht-kommerziellen Einsätze.",
    azure: "Die Unternehmensoption von OpenAI, gehostet auf Azure-Diensten.",
    anthropic: "Ein freundlicher KI-Assistent von Anthropic.",
    gemini: "Googles größtes und leistungsstärkstes KI-Modell",
    huggingface:
      "Zugriff auf über 150.000 Open-Source-LLMs und die weltweite KI-Community",
    ollama: "Führe LLMs lokal auf deinem eigenen Rechner aus.",
    lmstudio:
      "Entdecke, lade herunter und starte tausende moderne LLMs mit wenigen Klicks.",
    localai: "Führe LLMs lokal auf deinem eigenen Rechner aus.",
    togetherai: "Führe Open-Source-Modelle von Together AI aus.",
    mistral: "Führe Open-Source-Modelle von Mistral AI aus.",
    perplexityai:
      "Nutze leistungsstarke, internetverbundene Modelle, gehostet von Perplexity AI.",
    openrouter: "Eine einheitliche Schnittstelle für LLMs.",
    groq: "Die schnellste LLM-Inferenz für Echtzeit-KI-Anwendungen.",
    koboldcpp: "Führe lokale LLMs über koboldcpp aus.",
    oobabooga: "Führe lokale LLMs über Oobaboogas Text Generation Web UI aus.",
    cohere: "Nutze Coheres leistungsstarke Command-Modelle.",
    lite: "Nutze LiteLLM's OpenAI-kompatiblen Proxy für verschiedene LLMs.",
    "generic-openai":
      "Verbinde dich mit jedem OpenAI-kompatiblen Service via einer benutzerdefinierten Konfiguration",
    native:
      "Verwende ein heruntergeladenes, benutzerdefiniertes Llama-Modell zum Chatten in dieser Instanz.",
    xai: "Nutze xAI's leistungsstarke LLMs wie Grok-2 und mehr.",
    "aws-bedrock":
      "Nutze leistungsstarke Foundation-Modelle privat mit AWS Bedrock.",
    deepseek: "Nutze DeepSeeks leistungsstarke LLMs.",
    fireworksai:
      "Die schnellste und effizienteste Inferenz-Engine für produktionsreife, zusammengesetzte KI-Systeme.",
    bedrock: "Nutze leistungsstarke Foundation-Modelle privat mit AWS Bedrock.",
  },
  // =========================
  // CUSTOM USER AI SETTINGSPAGE
  // =========================
  "custom-user-ai": {
    title: "Benutzerdefinierte KI",
    settings: "Benutzerdefinierte KI",
    description: "Benutzerdefinierten KI-Anbieter konfigurieren",
    "custom-model-reference": "Benutzerdefinierter Modellname & Beschreibung",
    "custom-model-reference-description":
      "Fügen Sie eine benutzerdefinierte Referenz für dieses Modell hinzu. Diese wird sichtbar, wenn Sie den benutzerdefinierten KI-Engine-Selektor im Prompt-Panel verwenden.",
    "custom-model-reference-name": "Benutzerdefinierter Modellname",
    "custom-model-reference-description-label": "Modellbeschreibung (Optional)",
    "custom-model-reference-description-placeholder":
      "Geben Sie eine optionale Beschreibung für dieses Modell ein",
    "custom-model-reference-name-placeholder":
      "Geben Sie einen benutzerdefinierten Namen für dieses Modell ein",
    "model-ref-placeholder":
      "Geben Sie einen benutzerdefinierten Namen oder eine Beschreibung für dieses Modell-Setup ein",
    "enter-custom-model-reference":
      "Geben Sie einen benutzerdefinierten Namen für dieses Modell ein",
    "standard-engine": "Standard KI-Engine",
    "standard-engine-description":
      "Unsere Standard-Engine, nützlich für die meisten Aufgaben",
    "dynamic-context-window-percentage":
      "Dynamischer Kontextfenster-Prozentsatz",
    "dynamic-context-window-percentage-desc":
      "Steuert, wie viel des LLM-Kontextfensters für zusätzliche Quellen verwendet werden kann (10-100%)",
    "no-alternative-title": "Kein alternatives Modell ausgewählt",
    "no-alternative-desc":
      "Wenn diese Option ausgewählt ist, haben Benutzer nicht die Möglichkeit, ein alternatives Modell auszuwählen.",
    "select-option": "Benutzerdefiniertes KI-Profil auswählen",
    tab: {
      "custom-1": "Benutzerdefinierte Engine 1",
      "custom-2": "Benutzerdefinierte Engine 2",
      "custom-3": "Benutzerdefinierte Engine 3",
      "custom-4": "Benutzerdefinierte Engine 4",
      "custom-5": "Benutzerdefinierte Engine 5",
      "custom-6": "Benutzerdefinierte Engine 6",
    },
    engine: {
      "custom-1": "Benutzerdefinierte Engine 1",
      "custom-2": "Benutzerdefinierte Engine 2",
      "custom-3": "Benutzerdefinierte Engine 3",
      "custom-4": "Benutzerdefinierte Engine 4",
      "custom-5": "Benutzerdefinierte Engine 5",
      "custom-6": "Benutzerdefinierte Engine 6",
      "custom-1-title": "Benutzerdefinierte Engine 1",
      "custom-2-title": "Benutzerdefinierte Engine 2",
      "custom-3-title": "Benutzerdefinierte Engine 3",
      "custom-4-title": "Benutzerdefinierte Engine 4",
      "custom-5-title": "Benutzerdefinierte Engine 5",
      "custom-6-title": "Benutzerdefinierte Engine 6",
      "custom-1-description":
        "Einstellungen für Benutzerdefinierte Engine 1 konfigurieren",
      "custom-2-description":
        "Einstellungen für Benutzerdefinierte Engine 2 konfigurieren",
      "custom-3-description":
        "Einstellungen für Benutzerdefinierte Engine 3 konfigurieren",
      "custom-4-description":
        "Einstellungen für Benutzerdefinierte Engine 4 konfigurieren",
      "custom-5-description":
        "Einstellungen für Benutzerdefinierte Engine 5 konfigurieren",
      "custom-6-description":
        "Einstellungen für Benutzerdefinierte Engine 6 konfigurieren",
    },
    "option-number": "Option {{number}}",
    "llm-provider-selection": "LLM-Anbieter-Auswahl",
    "llm-provider-selection-desc":
      "Wählen Sie den LLM-Anbieter für diese benutzerdefinierte KI-Konfiguration",
    "custom-option": "Benutzerdefinierte Option",
    saving: "Speichern...",
    "save-changes": "Änderungen speichern",
    "model-ref-saved":
      "Benutzerdefinierte Modelleinstellungen erfolgreich gespeichert",
    "model-ref-save-failed":
      "Fehler beim Speichern der benutzerdefinierten Modelleinstellungen: {{error}}",
    "llm-settings-save-failed":
      "Fehler beim Speichern der LLM-Einstellungen: {{error}}",
    "settings-fetch-failed": "Fehler beim Abrufen der Einstellungen",
    "llm-saved": "LLM-Einstellungen erfolgreich gespeichert",
    "select-provider-first":
      "Bitte wählen Sie einen LLM-Anbieter, um die Modelleinstellungen zu konfigurieren. Nach der Konfiguration kann diese Option als benutzerdefinierte KI-Engine in der Benutzeroberfläche ausgewählt werden.",
  },
  // =========================
  // CDB LLM PREFERENCE
  // =========================
  "cdb-llm-preference": {
    title: "CDB LLM-preferenz",
    settings: "CDB LLM",
    description: "Konfigurieren Sie den LLM-Anbieter für CDB",
  },
  // =========================
  // TEMPLATE LLM PREFERENCE
  // =========================
  "template-llm-preference": {
    title: "Vorlagen-LLM-Präferenz",
    settings: "Vorlagen-LLM",
    description:
      "Wählen Sie den LLM-Anbieter für die Dokumentvorlagenerstellung. Standard ist der Systemanbieter.",
    "toast-success": "Vorlagen-LLM-Einstellungen aktualisiert",
    "toast-fail": "Fehler beim Aktualisieren der Vorlagen-LLM-Einstellungen",
    saving: "Speichern...",
    "save-changes": "Änderungen speichern",
  },

  // =========================
  // AUDIO PREFERENCE
  // =========================
  audio: {
    title: "Sprach-zu-Text-Einstellungen",
    provider: "Anbieter",
    "system-native": "System-nativ",
    "desc-speech":
      "Hier kannst du festlegen, welchen Sprach-zu-Text- bzw. Text-zu-Sprache-Anbieter du nutzen möchtest. Standardmäßig verwendet der Browser seine eigene Unterstützung, aber du kannst auch andere wählen.",
    "title-text": "Text-zu-Sprache-Einstellungen",
    "desc-text":
      "Hier kannst du festlegen, welchen Text-zu-Sprache-Anbieter du nutzen möchtest. Standardmäßig verwendet der Browser seine eigene Unterstützung, aber du kannst auch andere wählen.",
    "desc-config":
      "Für den browsereigenen Text-zu-Sprache-Dienst ist keine Konfiguration nötig.",
    "placeholder-stt": "Nach Speech-to-Text-Anbietern suchen",
    "placeholder-tts": "Nach Text-to-Speech-Anbietern suchen",
    "native-stt":
      "Verwendet den eingebauten Speech-to-Text-Dienst des Browsers, falls verfügbar.",
    "native-tts":
      "Verwendet den eingebauten Text-to-Speech-Dienst des Browsers, falls verfügbar.",
    "piper-tts": "Führen Sie TTS-Modelle lokal in Ihrem Browser privat aus.",
    "openai-description":
      "Nutzen Sie OpenAIs Text-zu-Sprache-Stimmen und Technologie.",
    openai: {
      "api-key": "API-Schlüssel",
      "api-key-placeholder": "OpenAI API-Schlüssel",
      "voice-model": "Sprachmodell",
    },
    elevenlabs: "Nutze ElevenLabs' Text-to-Speech-Stimmen und Technologie.",
  },

  // =========================
  // TRANSCRIPTION PREFERENCE
  // =========================
  transcription: {
    title: "Transkriptionsmodelleinstellung",
    description:
      "Dies sind die Zugangsdaten und Einstellungen für deinen bevorzugten Transkriptionsmodell-Anbieter. Es ist wichtig, dass diese Schlüssel aktuell und korrekt sind, sonst werden Mediendateien und Audio nicht transkribiert.",
    provider: "Transkriptionsanbieter",
    "warn-start":
      "Die Nutzung des lokalen Whisper-Modells auf Maschinen mit wenig RAM oder CPU kann die Plattform bei der Verarbeitung von Mediendateien verlangsamen.",
    "warn-recommend":
      "Wir empfehlen mindestens 2GB RAM und Dateien unter 10 MB.",
    "warn-end":
      "Das eingebaute Modell wird beim ersten Einsatz automatisch heruntergeladen.",
    "search-audio": "Nach Anbietern für Audio-Transkription suchen",
    "api-key": "API-Schlüssel",
    "api-key-placeholder": "OpenAI API-Schlüssel",
    "whisper-model": "Whisper-Modell",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Standard Eingebaut",
    "default-built-in-desc":
      "Führen Sie ein eingebautes Whisper-Modell privat auf dieser Instanz aus.",
    "openai-name": "OpenAI",
    "openai-desc":
      "Nutzen Sie das OpenAI Whisper-large-Modell mit Ihrem API-Schlüssel.",
    "model-turbo": "openai/whisper-large-v3-turbo", // Neuer Modellname
    "model-size-turbo": "(~810mb)", // Neue Modellgröße
  },

  // =========================
  // EMBEDDING PREFERENCE
  // =========================
  embedding: {
    title: "Embedding-Einstellung",
    "desc-start":
      "Wenn du ein LLM verwendest, das keinen nativen Embedding-Dienst unterstützt, musst du eventuell zusätzliche Zugangsdaten angeben, um Text einbetten zu können.",
    "desc-end":
      "Embedding wandelt Text in Vektoren um. Diese Zugangsdaten werden benötigt, um deine Dateien und Prompts in ein für das System verwertbares Format zu bringen.",
    provider: {
      title: "Embedding-Anbieter",
      description:
        "Für den nativen Embedding-Dienst der Plattform ist keine Einrichtung notwendig.",
      "search-embed": "Alle Embedding-Anbieter durchsuchen",
      search: "Alle Embedding-Anbieter durchsuchen",
      select: "Embedding-Anbieter auswählen",
    },
    workspace: {
      title: "Embedding-Einstellung des Arbeitsbereichs",
      description:
        "Der spezifische Embedding-Anbieter & das Modell, das in diesem Arbeitsbereich verwendet wird. Standardmäßig wird die Systemvoreinstellung genutzt.",
      "multi-model":
        "Multi-Modell-Unterstützung ist für diesen Anbieter derzeit nicht verfügbar.",
      "workspace-use": "Dieser Arbeitsbereich verwendet",
      "model-set": "das im System eingestellte Modellset.",
      embedding: "Embedding-Modell des Arbeitsbereichs",
      model:
        "Das spezifische Embedding-Modell, das in diesem Arbeitsbereich verwendet wird. Ist nichts angegeben, wird die Systemvoreinstellung genutzt.",
      wait: "-- warte auf Modelle --",
      setup: "Einrichten",
      use: "Um zu verwenden",
      "need-setup":
        "muss als dieser Arbeitsbereichs-Embedder erst eingerichtet werden.",
      cancel: "Abbrechen",
      save: "speichern",
      settings: "Einstellungen",
      search: "Alle Embedding-Anbieter durchsuchen",
      "need-llm":
        "muss als dieser Arbeitsbereichs-LLM erst eingerichtet werden.",
      "save-error":
        "Fehler beim Speichern der {{provider}}-Einstellungen: {{error}}",
      "system-default": "Systemstandard",
      "system-default-desc":
        "Verwende die System-Embedding-Einstellung für diesen Arbeitsbereich.",
    },
    warning: {
      "switch-model":
        "Das Wechseln des Embedding-Modells zerstört vorher eingebettete Dokumente, die während des Chats funktionieren. Sie müssen aus jedem Arbeitsbereich entfernt und vollständig neu hochgeladen werden, damit sie mit dem neuen Embedding-Modell eingebettet werden können.",
    },
  },

  // =========================
  // TEXT SPLITTING & CHUNKING
  // =========================
  text: {
    title: "Textaufteilungs- & Chunking-Einstellungen",
    "desc-start":
      "Manchmal möchtest du vielleicht die Standardmethode ändern, wie neue Dokumente aufgeteilt und in Chunks zerlegt werden, bevor sie in deine Vektor-Datenbank eingefügt werden.",
    "desc-end":
      "Diese Einstellung solltest du nur ändern, wenn du die Funktionsweise des Text-Splittings und seine Nebeneffekte verstehst.",
    "warn-start": "Änderungen hier gelten nur für",
    "warn-center": "neu eingebettete Dokumente",
    "warn-end": ", nicht für bereits bestehende Dokumente.",
    method: {
      title: "Text-Splitter-Methode",

      "native-explain":
        "Verwende lokale Chunk-Größe & Überlappung für die Aufteilung.",

      "jina-explain":
        "Delegiere Chunking/Segmentierung an Jinas eingebaute Methode.",

      "jina-info": "Jina-Chunking aktiv.",

      jina: {
        api_key: "Jina API-Schlüssel",
        api_key_desc:
          "Erforderlich für die Nutzung von Jinas Segmentierungsdienst. Der Schlüssel wird in Ihrer Umgebung gespeichert.",
        max_tokens: "Jina: Maximale Tokens pro Chunk",
        max_tokens_desc:
          "Definiert die maximalen Tokens in jedem Chunk für Jinas Segmentierer (maximal 2000 Tokens).",
        return_tokens: "Token-Informationen zurückgeben",
        return_tokens_desc:
          "Token-Anzahl und Tokenizer-Informationen in der Antwort einschließen.",
        return_chunks: "Chunk-Informationen zurückgeben",
        return_chunks_desc:
          "Detaillierte Informationen über die erzeugten Chunks in der Antwort einschließen.",
      },
    },
    size: {
      title: "Größe der Text-Chunks",
      description:
        "Dies ist die maximale Anzahl an Zeichen, die in einem einzelnen Vektor vorkommen dürfen.",
      recommend: "Maximale Länge des Embed-Modells ist",
    },
    overlap: {
      title: "Überlappung der Text-Chunks",
      description:
        "Dies ist die maximale Überlappung (Anzahl Zeichen) zwischen zwei benachbarten Text-Chunks.",
      error:
        "Chunk-Überlappung kann nicht größer oder gleich der Chunk-Größe sein.",
    },
  },
  // =========================
  // CONTEXTUAL SETTINGS
  // =========================
  contextual: {
    checkbox: {
      label: "Kontextuelles Embedding",
      hint: "Aktiviere kontextuelles Embedding, um den Einbettungsprozess mit zusätzlichen Parametern zu verbessern",
    },
    systemPrompt: {
      label: "System Prompt",
      placeholder: "Gib einen Wert ein...",
      description:
        "Beispiel: Bitte gib einen kurzen, prägnanten Kontext an, der diesen Abschnitt im Gesamtdokument einordnet – ausschließlich der Kontext, sonst nichts.",
    },
    userPrompt: {
      label: "User Prompt",
      placeholder: "Gib einen Wert ein...",
      description:
        "Beispiel: <document>\n{file}\n</document>\nHier ist der Abschnitt, den wir im Gesamtdokument einordnen möchten\n<chunk>\n{chunk}\n</chunk>",
    },
  },
  // =========================
  // CHAT UI SETTINGS
  // =========================
  "chat-ui-settings": {
    title: "Chat-UI-Einstellungen",
    description: "Konfiguriere die Chat-Einstellungen.",
    auto_submit: {
      title: "Automatisches Absenden von Spracheingaben",
      description:
        "Spracheingaben nach einer Stille-Periode automatisch absenden",
    },
    auto_speak: {
      title: "Automatisches Vorlesen von Antworten",
      description: "Antworten der KI automatisch vorlesen",
    },
  },
  // =========================
  // PIPER TTS OPTIONS (COMPONENT IN PAGE)
  // =========================
  piperTTS: {
    description:
      "Alle PiperTTS-Modelle laufen lokal in Ihrem Browser. Dies kann auf schwächeren Geräten ressourcenintensiv sein.",
    "voice-model": "Sprachmodell-Auswahl",
    "loading-models": "-- verfügbare Modelle werden geladen --",
    "stored-indicator":
      'Das "✔" zeigt an, dass dieses Modell bereits lokal gespeichert ist und beim Ausführen nicht heruntergeladen werden muss.',
    "flush-cache": "Sprach-Cache leeren",
    "flush-success": "Alle Stimmen aus dem Browser-Speicher entfernt",
    demo: {
      stop: "Demo stoppen",
      loading: "Stimme wird geladen",
      play: "Beispiel abspielen",
      text: "Hallo, willkommen bei IST Legal!",
    },
  },

  // =========================
  // VECTOR DATABASE (SYSTEM)
  // =========================
  vector: {
    title: "Vektordatenbank",
    description:
      "Dies sind die Anmeldeinformationen und Einstellungen für die Funktionsweise Ihrer Plattforminstanz. Es ist wichtig, dass diese Schlüssel aktuell und korrekt sind.",
    provider: {
      title: "Vektordatenbank-Anbieter",
      description: "Für LanceDB ist keine Konfiguration erforderlich.",
      "search-db": "Alle Vektordatenbank-Anbieter durchsuchen",
      search: "Alle Vektordatenbanken durchsuchen",
      select: "Vektordatenbank-Anbieter auswählen",
    },
    warning:
      "Das Wechseln der Vektordatenbank erfordert, dass Sie alle Dokumente in allen relevanten Arbeitsbereichen neu einbetten. Dies kann einige Zeit dauern.",
    search: {
      title: "Vektorsuche-Modus",
      mode: {
        "globally-enabled":
          "Diese Einstellung wird global in den Systemeinstellungen gesteuert. Besuchen Sie die Systemeinstellungen, um das Neuranking-Verhalten zu ändern.",
        default: "Standardsuche",
        "default-desc": "Standard-Vektorähnlichkeitssuche ohne Neuranking.",
        "accuracy-optimized": "Genauigkeitsoptimiert",
        "accuracy-desc":
          "Ordnet Ergebnisse neu, um die Genauigkeit mithilfe von Kreuzaufmerksamkeit zu verbessern.",
      },
    },
  },
  // =========================
  // VECTOR DATABASE PROVIDER DESCRIPTIONS
  // =========================
  vectordb: {
    lancedb:
      "100% lokale Vektor-Datenbank, die auf derselben Instanz wie die Plattform läuft.",
    chroma:
      "Open-Source Vektor-Datenbank, die du selbst hosten oder in der Cloud betreiben kannst.",
    pinecone:
      "100% cloud-basierte Vektor-Datenbank für Unternehmensanwendungen.",
    zilliz:
      "Cloud-gehostete Vektor-Datenbank, entwickelt für Unternehmen mit SOC 2 Konformität.",
    qdrant: "Open-Source, lokal und verteilt einsetzbare Vektor-Datenbank.",
    weaviate: "Open-Source Vektor-Datenbank, lokal oder in der Cloud gehostet.",
    milvus: "Open-Source, hochskalierbar und ultraschnell.",
    astra: "Vektorbasierte Suche für reale GenAI-Anwendungen.",
  },
  // =========================
  // DEEP SEARCH SETTINGS
  // =========================
  deep_search: {
    title: "Deep Search",
    description:
      "Konfiguriere Websuche-Fähigkeiten für Chat-Antworten. Wenn aktiviert, kann das System im Web nach Informationen suchen, um Antworten zu verbessern.",
    enable: "Deep Search aktivieren",
    enable_description:
      "Erlaube dem System, bei der Beantwortung von Anfragen im Web nach Informationen zu suchen.",
    provider_settings: "Anbieter-Einstellungen",
    provider: "Suchanbieter",
    model: "Modell",
    api_key: "API-Schlüssel",
    api_key_placeholder: "Gib deinen API-Schlüssel ein",
    api_key_placeholder_set:
      "API-Schlüssel ist gesetzt (neuen Schlüssel eingeben, um zu ändern)",
    api_key_help:
      "Dein API-Schlüssel wird sicher gespeichert und nur für Websuche-Anfragen verwendet.",
    context_percentage: "Kontext-Prozentsatz",
    context_percentage_help:
      "Prozentsatz des LLM-Kontextfensters, der für Websuche-Ergebnisse reserviert wird (5-20%).",
    fetch_error: "Deep Search-Einstellungen konnten nicht abgerufen werden",
    save_success: "Deep Search-Einstellungen erfolgreich gespeichert",
    save_error:
      "Fehler beim Speichern der Deep Search-Einstellungen: {{error}}",
    toast_success: "Deep Search-Einstellungen erfolgreich gespeichert",
    toast_error:
      "Fehler beim Speichern der Deep Search-Einstellungen: {{error}}",
    brave_recommended:
      "Brave Search ist derzeit die empfohlene und zuverlässigste Anbieter-Option.",
  },

  // =========================
  // PDR SETTINGS
  // =========================
  "pdr-settings": {
    title: "PDR-Einstellungen",
    description:
      "Konfiguriere die Einstellungen für die Parent Document Retrieval (PDR) in deinen Arbeitsbereichen.",
    "desc-end":
      "Diese Einstellungen beeinflussen, wie PDR-Dokumente verarbeitet und in Chat-Antworten genutzt werden.",
    "global-override": {
      title: "Globales dynamisches PDR-Override",
      description:
        "Wenn aktiviert, werden alle Arbeitsbereich-Dokumente als PDR-aktiviert für den Kontext in Antworten behandelt. Wenn deaktiviert, werden nur Dokumente verwendet, die explizit als PDR markiert sind, was den verfügbaren Kontext reduzieren kann und zu deutlich schlechteren Antworten führen kann, da nur Vektorchunks aus der Suche als Quellen in diesen Fällen verwendet werden.",
    },
    "toast-success": "PDR-Einstellungen aktualisiert",
    "toast-fail": "Fehler beim Aktualisieren der PDR-Einstellungen",
    "adjacent-vector-limit": "Limit für benachbarte Vektoren",
    "adjacent-vector-limit-desc": "Begrenzung für benachbarte Vektoren.",
    "adjacent-vector-limit-placeholder":
      "Limit für benachbarte Vektoren eingeben",
    "keep-pdr-vectors": "PDR-Vektoren behalten",
    "keep-pdr-vectors-desc": "Einstellung zum Behalten der PDR-Vektoren.",
  },

  // =========================
  // AGENTS
  // =========================
  agents: {
    title: "Agent-Fähigkeiten",
    "agent-skills": "Agent-Fähigkeiten konfigurieren und verwalten",
    "custom-skills": "Benutzerdefinierte Fähigkeiten",
    back: "Zurück",
    "select-skill": "Wählen Sie eine Fähigkeit zum Konfigurieren",
    "preferences-saved": "Agent-Einstellungen erfolgreich gespeichert",
    "preferences-failed": "Fehler beim Speichern der Agent-Einstellungen",
    "skill-status": {
      on: "Ein",
      off: "Aus",
    },
    "skill-config-updated": "Skill-Konfiguration erfolgreich aktualisiert",
  },
  // =========================
  // AGENT MENU
  // =========================
  "agent-menu.default-agent": "Standard-Agent",
  "agent-menu.ability.rag-search": "RAG-Suche",
  "agent-menu.ability.web-scraping": "Web-Scraping",
  "agent-menu.ability.web-browsing": "Web-Browsing",
  "agent-menu.ability.save-file-to-browser": "Datei im Browser speichern",
  "agent-menu.ability.list-documents": "Dokumente auflisten",
  "agent-menu.ability.summarize-document": "Dokument zusammenfassen",
  "agent-menu.ability.chart-generation": "Diagramm-Generierung",
  // =========================
  // BADGES AGENT PAGE
  // =========================
  badges: {
    default: {
      text: "Standard",
      tooltip:
        "Diese Fähigkeit ist standardmäßig aktiviert und kann nicht deaktiviert werden.",
    },
  },
  // =========================
  // EMBED CHATS
  // =========================
  "embed-chats": {
    title: "Embed-Chats",
    export: "Exportieren",
    description:
      "Dies sind alle aufgezeichneten Chats und Nachrichten von jedem veröffentlichten Embed.",
    table: {
      embed: "Embed",
      sender: "Absender",
      message: "Nachricht",
      response: "Antwort",
      at: "Gesendet am",
    },
    delete: {
      title: "Chat löschen",
      message:
        "Bist du sicher, dass du diesen Chat löschen möchtest?\n\nDiese Aktion ist unwiderruflich.",
    },
    config: {
      "delete-title": "Embed löschen",
      "delete-message":
        "Bist du sicher, dass du dieses Embed löschen möchtest?\n\nDiese Aktion ist unwiderruflich.",
      "disable-title": "Embed deaktivieren",
      "disable-message":
        "Bist du sicher, dass du dieses Embed deaktivieren möchtest?\n\nDiese Aktion ist unwiderruflich.",
      "enable-title": "Embed aktivieren",
      "enable-message":
        "Bist du sicher, dass du dieses Embed aktivieren möchtest?\n\nDiese Aktion ist unwiderruflich.",
    },
  },
  // =========================
  // EMBEDDABLE CHAT WIDGETS
  // =========================
  embeddable: {
    title: "Einbettbare Chat-Widgets",
    description:
      "Einbettbare Chat-Widgets sind öffentliche Chat-Oberflächen, die an einen einzelnen Arbeitsbereich gebunden sind. Damit kannst du Arbeitsbereiche erstellen, die du dann der Öffentlichkeit präsentieren kannst.",
    create: "Embed erstellen",
    table: {
      workspace: "Arbeitsbereich",
      chats: "Gesendete Chats",
      Active: "Aktive Domains",
    },
  },
  // =========================
  // NEW EMBED
  // =========================
  "new-embed": {
    title: "Neues Embed für den Arbeitsbereich erstellen",
    error: "Fehler: ",
    "desc-start":
      "Nach der Erstellung erhältst du einen Link, den du auf deiner Website veröffentlichen kannst – mithilfe eines einfachen",
    script: "Script",
    tag: "Tag.",
    cancel: "Abbrechen",
    "create-embed": "Embed erstellen",
    workspace: "Arbeitsbereich",
    "desc-workspace":
      "Dies ist der Arbeitsbereich, auf dem dein Chatfenster basiert. Alle Standardeinstellungen werden von dort übernommen, sofern nicht explizit anders angegeben.",
    "allowed-chat": "Erlaubte Chat-Methode",
    "desc-query":
      "Lege fest, wie dein Chatbot arbeiten soll. Query bedeutet, dass er nur antwortet, wenn ein Dokument bei der Beantwortung hilft.",
    "desc-chat":
      "Chat ermöglicht Antworten auch auf allgemeine Fragen, selbst wenn kein spezifischer Arbeitsbereichskontext vorhanden ist.",
    "desc-response": "Chat: Beantworte alle Fragen, unabhängig vom Kontext",
    "query-response":
      "Query: Antworte nur, wenn der Chat in Zusammenhang mit Dokumenten steht",
    restrict: "Anfragen von Domains einschränken",
    filter:
      "Dieser Filter blockiert Anfragen von Domains, die nicht in der Liste stehen.",
    "use-embed":
      "Wenn dieses Feld leer bleibt, kann dein Embed von jeder Website genutzt werden.",
    "max-chats": "Max. Chats pro Tag",
    "limit-chats":
      "Begrenze die Anzahl der Chats, die dieser eingebettete Chat innerhalb von 24 Stunden verarbeiten darf. 0 bedeutet unbegrenzt.",
    "chats-session": "Max. Chats pro Sitzung",
    "limit-chats-session":
      "Begrenze die Anzahl der Chats, die ein Sitzungsbenutzer mit diesem Embed innerhalb von 24 Stunden senden darf. 0 bedeutet unbegrenzt.",
    "enable-dynamic": "Dynamische Modellnutzung aktivieren",
    "llm-override":
      "Erlaube es, das bevorzugte LLM-Modell festzulegen, um die Standardeinstellung des Arbeitsbereichs zu überschreiben.",
    "llm-temp": "Dynamische LLM-Temperatur aktivieren",
    "desc-temp":
      "Erlaube es, die LLM-Temperatur festzulegen, um die Standardeinstellung des Arbeitsbereichs zu überschreiben.",
    "prompt-override": "Prompt-Überschreibung aktivieren",
    "desc-override":
      "Erlaube es, den System-Prompt festzulegen, um die Standardeinstellung des Arbeitsbereichs zu überschreiben.",
  },
  // =========================
  // EMBED CONFIGS
  // =========================
  embedConfigs: {
    "show-code": "Code anzeigen",
    enable: "Aktivieren",
    disable: "Deaktivieren",
    "all-domains": "alle",
    "disable-confirm":
      "Sind Sie sicher, dass Sie dieses Embed deaktivieren möchten?\nSobald es deaktiviert ist, wird das Embed nicht mehr auf Chat-Anfragen antworten.",
    "delete-confirm":
      "Sind Sie sicher, dass Sie dieses Embed löschen möchten?\nSobald es gelöscht ist, wird das Embed nicht mehr auf Chats antworten oder aktiv sein.\n\nDiese Aktion ist unwiderruflich.",
    "disabled-toast": "Embed wurde deaktiviert",
    "enabled-toast": "Embed ist aktiv",
  },
  // =========================
  // EVENT LOGS
  // =========================
  event: {
    title: "Ereignisprotokolle",
    description:
      "Sieh alle Aktionen und Ereignisse, die in dieser Instanz stattfinden, zur Überwachung.",
    clear: "Ereignisprotokolle löschen",
    table: {
      type: "Ereignistyp",
      user: "Benutzer",
      occurred: "Aufgetreten am",
    },
  },
  // =========================
  // API KEYS
  // =========================
  api: {
    title: "API-Schlüssel",
    description:
      "API-Schlüssel ermöglichen dem Inhaber, programmgesteuert auf diese Instanz zuzugreifen und sie zu verwalten.",
    link: "Lies die API-Dokumentation",
    generate: "Neuen API-Schlüssel generieren",
    table: {
      key: "API-Schlüssel",
      by: "Erstellt von",
      created: "Erstellt am",
    },
    new: {
      title: "Neuen API-Schlüssel erstellen",
      description:
        "Nach der Erstellung kann der API-Schlüssel programmgesteuert zum Zugriff und zur Konfiguration dieser Instanz verwendet werden.",
      doc: "Lies die API-Dokumentation",
      cancel: "Abbrechen",
      "create-api": "API-Schlüssel erstellen",
    },
  },

  // =========================
  // BROWSER EXTENSION API KEYS
  // =========================
  "browser-extension-api": {
    title: "API-Schlüssel",
    description:
      "Verwalten Sie API-Schlüssel für die Verbindung mit dieser Instanz.",
    "generate-key": "Neuen API-Schlüssel generieren",
    "table-headers": {
      "connection-string": "Verbindungszeichenfolge",
      "created-by": "Erstellt von",
      "created-at": "Erstellt am",
      actions: "Aktionen",
    },
    "no-keys": "Keine API-Schlüssel gefunden",
    modal: {
      title: "Neuer Browser-Erweiterungs-API-Schlüssel",
      "multi-user-warning":
        "Warnung: Sie befinden sich im Mehrbenutzer-Modus. Dieser API-Schlüssel ermöglicht den Zugriff auf alle mit Ihrem Konto verknüpften Arbeitsbereiche. Bitte gehen Sie vorsichtig damit um.",
      "create-description":
        'Nach dem Klicken auf "API-Schlüssel erstellen" wird diese Instanz versuchen, einen neuen API-Schlüssel für die Browser-Erweiterung zu erstellen.',
      "connection-help":
        'Wenn Sie "Verbunden mit IST Legal" in der Erweiterung sehen, war die Verbindung erfolgreich. Wenn nicht, kopieren Sie bitte die Verbindungszeichenfolge und fügen Sie sie manuell in die Erweiterung ein.',
      cancel: "Abbrechen",
      "create-key": "API-Schlüssel erstellen",
      "copy-key": "API-Schlüssel kopieren",
      "key-copied": "API-Schlüssel kopiert!",
    },
    tooltips: {
      "copy-connection": "Verbindungszeichenfolge kopieren",
      "auto-connect": "Automatisch mit Erweiterung verbinden",
    },
    confirm: {
      revoke:
        "Sind Sie sicher, dass Sie diesen Browser-Erweiterungs-API-Schlüssel widerrufen möchten?\nDanach wird er nicht mehr verwendbar sein.\n\nDiese Aktion ist unwiderruflich.",
    },
    toasts: {
      "key-revoked":
        "Browser-Erweiterungs-API-Schlüssel wurde permanent widerrufen",
      "revoke-failed": "Fehler beim Widerrufen des API-Schlüssels",
      copied: "Verbindungszeichenfolge in die Zwischenablage kopiert",
      connecting: "Verbindung zur Browser-Erweiterung wird hergestellt...",
    },
    "revoke-title": "Browser-Erweiterungs-API-Schlüssel widerrufen",
    "revoke-message":
      "Sind Sie sicher, dass Sie diesen Browser-Erweiterungs-API-Schlüssel widerrufen möchten?\nDanach wird er nicht mehr verwendbar sein.\n\nDiese Aktion ist unwiderruflich.",
  },
  // =========================
  // SECURITY
  // =========================
  security: {
    "multi-user-mode-permanent":
      "Der Multi-User-Modus ist aus Sicherheitsgründen dauerhaft aktiviert",
    "password-validation": {
      "restricted-chars":
        "Ihr Passwort enthält unzulässige Zeichen. Erlaubte Symbole sind _,-,!,@,$,%,^,&,*,(,),;",
    },
    "public-workspace": {
      "access-description":
        "Wenn aktiviert, kann jeder Benutzer ohne Anmeldung auf die öffentlichen Arbeitsbereiche zugreifen.",
    },
    button: {
      saving: "Speichern...",
      "save-changes": "Änderungen speichern",
    },
  },

  // =========================
  // MULTI-USER MODE
  // =========================
  multi: {
    title: "Multi-User-Modus",
    description:
      "Richte deine Instanz so ein, dass sie dein Team unterstützt, indem du den Multi-User-Modus aktivierst.",
    enable: {
      "is-enable": "Multi-User-Modus ist aktiviert",
      enable: "Multi-User-Modus aktivieren",
      description:
        "Standardmäßig bist du der einzige Administrator. Als Administrator musst du Konten für alle neuen Benutzer oder Administratoren erstellen. Verliere dein Passwort nicht, da nur ein Administrator Passwörter zurücksetzen kann.",
      username: "Admin-Konto E-Mail",
      password: "Admin-Konto Passwort",
      "username-placeholder": "Dein Administrator-Benutzername",
      "password-placeholder": "Dein Administrator-Passwort",
    },
    password: {
      title: "Passwortschutz",
      description:
        "Schütze deine Instanz mit einem Passwort. Wenn du dieses vergisst, gibt es keine Wiederherstellungsmöglichkeit – speichere es also sicher ab.",
    },
    instance: {
      title: "Instanz passwortschützen",
      description:
        "Standardmäßig bist du der einzige Administrator. Als Administrator musst du Konten für alle neuen Benutzer oder Administratoren erstellen. Verliere dein Passwort nicht, da nur ein Administrator Passwörter zurücksetzen kann.",
      password: "Instanzpasswort",
    },
  },

  // =========================
  // EXPERIMENTAL FEATURES
  // =========================
  experimental: {
    title: "Experimentelle Funktionen",
    description: "Funktionen, die sich derzeit in der Beta-Testphase befinden",
    "live-sync": {
      title: "Live-Dokumentensynchronisation",
      description:
        "Aktivieren Sie die automatische Inhaltssynchronisation aus externen Quellen",
      "manage-title": "Überwachte Dokumente",
      "manage-description":
        "Dies sind alle Dokumente, die derzeit in Ihrer Instanz überwacht werden. Der Inhalt dieser Dokumente wird regelmäßig synchronisiert.",
      "document-name": "Dokumentname",
      "last-synced": "Zuletzt synchronisiert",
      "next-refresh": "Zeit bis zur nächsten Aktualisierung",
      "created-on": "Erstellt am",
      "auto-sync": "Automatische Inhaltssynchronisation",
      "sync-description":
        'Aktivieren Sie die Möglichkeit, eine Inhaltsquelle zur "Überwachung" festzulegen. Überwachte Inhalte werden regelmäßig abgerufen und in dieser Instanz aktualisiert.',
      "sync-workspace-note":
        "Überwachte Inhalte werden automatisch in allen Arbeitsbereichen aktualisiert, in denen sie referenziert werden.",
      "sync-limitation":
        "Diese Funktion gilt nur für webbasierte Inhalte wie Websites, Confluence, YouTube und GitHub-Dateien.",
      documentation: "Funktionsdokumentation und Warnhinweise",
      "manage-content": "Überwachte Inhalte verwalten",
    },
    tos: {
      title: "Nutzungsbedingungen für experimentelle Funktionen",
      description:
        "Experimentelle Funktionen dieser Plattform sind Funktionen, die wir testen und die optional sind. Wir weisen Sie proaktiv auf mögliche Bedenken hin, sollten diese vor der Genehmigung einer Funktion bestehen.",
      "possibilities-title":
        "Die Nutzung einer Funktion auf dieser Seite kann unter anderem zu folgenden Möglichkeiten führen:",
      possibilities: {
        "data-loss": "Datenverlust.",
        "quality-change": "Änderung der Ergebnisqualität.",
        "storage-increase": "Erhöhter Speicherverbrauch.",
        "resource-consumption": "Erhöhter Ressourcenverbrauch.",
        "cost-increase":
          "Erhöhte Kosten oder Nutzung verbundener LLM- oder Embedding-Provider.",
        "potential-bugs":
          "Mögliche Fehler oder Probleme bei der Nutzung dieser Anwendung.",
      },
      "conditions-title":
        "Die Nutzung einer experimentellen Funktion ist mit folgenden nicht erschöpfenden Bedingungen verbunden:",
      conditions: {
        "future-updates":
          "Die Funktion existiert möglicherweise in zukünftigen Updates nicht mehr.",
        stability: "Die verwendete Funktion ist derzeit nicht stabil.",
        availability:
          "Die Funktion ist möglicherweise in zukünftigen Versionen, Konfigurationen oder Abonnements dieser Instanz nicht verfügbar.",
        privacy:
          "Ihre Datenschutzeinstellungen werden bei der Nutzung einer Beta-Funktion berücksichtigt.",
        changes: "Diese Bedingungen können sich in zukünftigen Updates ändern.",
      },
      "read-more":
        "Wenn Sie mehr erfahren möchten, können Sie sich beziehen auf",
      contact: "oder kontaktieren Sie",
      reject: "Ablehnen & Schließen",
      accept: "Ich verstehe",
    },
    "update-failed": "Fehler beim Aktualisieren des Funktionsstatus",
  },
};
