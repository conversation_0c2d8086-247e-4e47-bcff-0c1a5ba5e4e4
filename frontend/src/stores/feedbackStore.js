import { create } from "zustand";
import { persist } from "zustand/middleware";
import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";

const useFeedbackStore = create(
  persist(
    (set, get) => ({
      feedbackCount: null,
      loading: false,
      error: null,
      lastFetched: null,
      feedbackLastSeenCount: null,
      refreshInterval: 5 * 60 * 1000, // 5 minutes

      fetchFeedbackCount: async (force = false) => {
        const { lastFetched, refreshInterval, loading } = get();
        const now = Date.now();

        // Skip fetch if not forced, within refresh interval, and not currently loading
        if (
          !force &&
          lastFetched &&
          now - lastFetched < refreshInterval &&
          !loading
        ) {
          return get().feedbackCount;
        }

        // Don't start a new fetch if one is already in progress
        if (loading && !force) {
          return get().feedbackCount;
        }

        set({ loading: true, error: null });

        try {
          const response = await fetch(`${API_BASE}/system/feedback/count`, {
            method: "GET",
            headers: baseHeaders(),
          });

          if (!response.ok) {
            throw new Error(
              `Failed to fetch feedback count: ${response.statusText}`
            );
          }

          const data = await response.json();

          if (data.success) {
            set({
              feedbackCount: data.count,
              loading: false,
              error: null,
              lastFetched: now,
            });
            return data.count;
          } else {
            throw new Error(data.error || "Failed to fetch feedback count");
          }
        } catch (error) {
          console.error("Error fetching feedback count:", error);
          set({
            loading: false,
            error: error.message,
            // Don't reset feedbackCount to 0 on error, keep the last known value
          });
          return get().feedbackCount;
        }
      },

      refreshFeedbackCount: () => {
        return get().fetchFeedbackCount(true);
      },

      // Method to be called when feedback is added/deleted
      invalidateFeedbackCount: () => {
        set({ lastFetched: null, feedbackCount: null });
        // Force a fresh fetch after a small delay to ensure backend consistency
        setTimeout(() => {
          get().fetchFeedbackCount(true);
        }, 100);
      },

      // Check if there are unseen feedback items
      hasUnseenFeedback: () => {
        const { feedbackCount, feedbackLastSeenCount } = get();

        // No feedback at all
        if (!feedbackCount || feedbackCount === 0) {
          return false;
        }

        // If no count has been marked as seen yet, consider all feedback as unseen
        if (
          feedbackLastSeenCount === null ||
          feedbackLastSeenCount === undefined
        ) {
          return true;
        }

        // Show notification if current count is greater than last seen count
        return feedbackCount > feedbackLastSeenCount;
      },

      // Mark current feedback as seen
      markFeedbackAsSeen: () => {
        const { feedbackCount } = get();
        set({ feedbackLastSeenCount: feedbackCount || 0 });
      },
    }),
    {
      name: "feedback-store",
      partialize: (state) => ({
        feedbackLastSeenCount: state.feedbackLastSeenCount,
      }),
    }
  )
);

// Selector hooks following the platform pattern
export const useFeedbackCount = () =>
  useFeedbackStore((state) => state.feedbackCount);

export const useFeedbackCountLoading = () =>
  useFeedbackStore((state) => state.loading);

export const useFeedbackCountError = () =>
  useFeedbackStore((state) => state.error);

export const useFetchFeedbackCount = () =>
  useFeedbackStore((state) => state.fetchFeedbackCount);

export const useRefreshFeedbackCount = () =>
  useFeedbackStore((state) => state.refreshFeedbackCount);

export const useInvalidateFeedbackCount = () =>
  useFeedbackStore((state) => state.invalidateFeedbackCount);

export const useHasUnseenFeedback = () =>
  useFeedbackStore((state) => state.hasUnseenFeedback);

export const useMarkFeedbackAsSeen = () =>
  useFeedbackStore((state) => state.markFeedbackAsSeen);

export default useFeedbackStore;
