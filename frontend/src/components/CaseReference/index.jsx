import React, { useState, useEffect } from "react";
import { MdOutlinePlaylistAdd } from "react-icons/md";
import { X } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";
import showToast from "@/utils/toast";
import ReferenceNumberModal from "@/components/Modals/ReferenceNumber";
import ProjectRegistrationModal from "@/components/Modals/RexorProjectRegistrationModal";
import RexorLoginModal from "@/components/Modals/RexorLoginModal";
import {
  useRexorIsLoggedIn,
  useRexorCheckLoginStatus,
  useRexorActiveReference,
  useRexorSetActiveReference,
  useRexorClearActiveReference,
} from "@/stores/rexorStore";
import {
  useTabNames,
  useInvoiceLogging,
  useForceInvoiceLogging,
  useRexorLinkage,
} from "@/stores/settingsStore";
import { Button } from "@/components/Button";
import { cn } from "@/utils/classes";

export default function CaseReference({ user, className }) {
  const { t } = useTranslation();
  const isLoggedIn = useRexorIsLoggedIn();
  const checkLoginStatus = useRexorCheckLoginStatus();
  const activeReference = useRexorActiveReference();
  const setActiveReference = useRexorSetActiveReference();
  const clearActiveReference = useRexorClearActiveReference();
  const tabNames = useTabNames();

  const invoiceLogging = useInvoiceLogging();
  const forcedInvoiceLogging = useForceInvoiceLogging();
  const rexorLinkage = useRexorLinkage();

  const [showReferenceModal, setShowReferenceModal] = useState(false);
  const [showProjectRegistrationModal, setShowProjectRegistrationModal] =
    useState(false);
  const [showRexorLoginModal, setShowRexorLoginModal] = useState(false);

  useEffect(() => {
    if (showRexorLoginModal || showProjectRegistrationModal) {
      checkLoginStatus();
    }
  }, [showRexorLoginModal, showProjectRegistrationModal, checkLoginStatus]);

  useEffect(() => {
    if (isLoggedIn && showRexorLoginModal) {
      setShowRexorLoginModal(false);
      setShowProjectRegistrationModal(true);
    }
  }, [isLoggedIn, showRexorLoginModal]);

  useEffect(() => {
    if (!invoiceLogging) {
      clearActiveReference();
    }
  }, [invoiceLogging, clearActiveReference]);

  const handleCaseReferenceClick = () => {
    if (rexorLinkage) {
      checkLoginStatus();
      if (!isLoggedIn) {
        setShowRexorLoginModal(true);
      } else {
        setShowProjectRegistrationModal(true);
      }
    } else {
      setShowReferenceModal(true);
    }
  };

  const handleRexorCaseSelect = (project) => {
    if (project[0]?.ProjectID) {
      setActiveReference(project[0].ProjectID);
    }
    setShowProjectRegistrationModal(false);
    showToast(t("show-toast.active-case.reference-updated"), "success");
  };

  const handleRexorArticleTransaction = () => {
    setShowProjectRegistrationModal(false);
    showToast(t("show-toast.article-transaction-registered"), "success");
  };

  const handleReferenceSubmit = (reference) => {
    setActiveReference(reference);
    setShowReferenceModal(false);
    showToast(t("show-toast.active-case.reference-updated"), "success", {
      position: "bottom-right",
    });
  };

  const handleRexorLoginSuccess = (token) => {
    if (!token) return;
    checkLoginStatus();
    setShowRexorLoginModal(false);
    setShowProjectRegistrationModal(true);
  };

  const canCloseModal =
    !forcedInvoiceLogging || activeReference !== "" || user?.role !== "default";

  if (!invoiceLogging) return null;

  return (
    <>
      <div
        className={cn(
          "relative flex items-center border rounded-md",
          activeReference && "pr-4",
          className
        )}
      >
        <Button
          variant="ghost"
          onClick={handleCaseReferenceClick}
          className={cn(
            "w-full",
            activeReference && "pr-2 hover:bg-transparent"
          )}
        >
          {!activeReference && <MdOutlinePlaylistAdd />}

          <span className="max-w-48 md:max-w-64 truncate">
            {activeReference
              ? (tabNames?.tabName3 && tabNames.tabName3.trim() !== ""
                  ? tabNames.tabName3
                  : t("module.active-case")) + `: ${activeReference}`
              : t("active-case.select-reference")}
          </span>
        </Button>

        {activeReference && (
          <Button
            variant="destructive"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              clearActiveReference();
              showToast(
                t("show-toast.active-case.reference-cleared"),
                "success",
                {
                  position: "bottom-right",
                }
              );
            }}
            className="w-8 h-8 bg-transparent text-red-600 hover:bg-red-100"
            aria-label={t("common.clear-reference")}
          >
            <X weight="bold" />
          </Button>
        )}
      </div>

      <ProjectRegistrationModal
        isOpen={showProjectRegistrationModal}
        onClose={() => canCloseModal && setShowProjectRegistrationModal(false)}
        onSelect={handleRexorCaseSelect}
        onArticleTransactionSubmit={handleRexorArticleTransaction}
        canClose={canCloseModal}
      />

      <RexorLoginModal
        show={showRexorLoginModal}
        onClose={() => canCloseModal && setShowRexorLoginModal(false)}
        onSuccess={handleRexorLoginSuccess}
      />

      <ReferenceNumberModal
        isOpen={showReferenceModal}
        onClose={() => canCloseModal && setShowReferenceModal(false)}
        onSubmit={handleReferenceSubmit}
        canClose={canCloseModal}
      />
    </>
  );
}
