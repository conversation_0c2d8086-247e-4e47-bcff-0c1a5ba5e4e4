```mermaid
graph TD
    A[Request In] --> B{Initiate streamChatWithWorkspaceLQA};
    B --> C{Generate UUID, Check Commands/Agents};
    C --> D{"Determine LLM Provider and Model (System/User/Suffix Settings)"};
    D --> E[Initialize LLMConnector];
    E --> F{"Calculate Token Limits (Dynamic Context % )"};
    F --> G{Process Attachments};
    G --> H{Fetch Chat History};
    H --> I{Content Retrieval Loop};
    I -- Optional --> J[Deep Search];
    J --> K[Vector Search];
    I --> K;
    K --> L[Fetch Pinned Documents];
    L --> M["Fetch Adjacent Vectors (if applicable)"];
    M --> N{Assemble contextTexts & sources};
    N --> O{Query Mode & No Context?};
    O -- Yes --> P[Send Refusal Message & Exit];
    O -- No --> Q[Assemble & Compress Messages for LLM];
    Q --> R{LLM Supports Streaming?};
    R -- Yes --> S[LLMConnector.streamGetChatCompletion];
    S --> T["LLMConnector.handleStream (Send Chunks)"];
    R -- No --> U[LLMConnector.getChatCompletion];
    U --> V[Send Single Response Chunk];
    T --> W{Capture Complete Text & Metrics};
    V --> W;
    W --> X{Create/Update WorkspaceChat Entry};
    X --> Y{"Save Chat Logs (if enabled)"};
    Y --> Z[Send finalizeResponseStream to Client];
    Z --> AA[End Request];

    subgraph ErrorHandling
        Err1[Catch Rate Limits]
        Err2[Catch LLM Errors]
        Err1 --> ErrEnd[Send Abort Message & Exit]
        Err2 --> ErrEnd
    end

    C -.-> |Agent Flow| AA;
    C -.-> |Command Flow| AA;
    K -.-> |Vector Search Fails| ErrEnd;

    J -.-> K;
```
