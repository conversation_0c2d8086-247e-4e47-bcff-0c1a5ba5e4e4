export default {
  // =========================
  // DOCUMENTS & PINNING
  // =========================
  documents: {
    "pin-info-button": "Ni iki ari cyo gukingira inyandiko?",
    "pin-title": "Ni iki ari cyo gukingira inyandiko?",
    "pin-desc-1":
      "<PERSON>yo ukingiye inyandiko, platform izashyira ibikubiye muri dosiye yose mu kiganiro cyawe.",
    "pin-desc-2":
      "Ibi bikora neza kuri LLM zifite context nini cyangwa dosiye ntoya.",
    "pin-desc-3":
      "Niba udahabwa ibisubizo byiza, gukingira inyandiko ni uburyo bwo kubona ibisubizo byiza.",
    "pin-add": "Kingira mu mwanya w'akazi",
    "pin-unpin": "Kuvanaho mu mwanya w'akazi",
    "remove-document": "Kuramo inyandiko mu mwanya w'akazi",
    "remove-folder": "Kuramo dosiye n'amadosiye yose irimo",
    "document-removed": "Inyandiko yakuwe mu mwanya w'akazi",
    "folder-removed": "Dosiye n'amadosiye yose irimo byakuwe",
    "for-all-files-in-folder": "ku madosiye yose ari muri dosiye",
    "star-title": "Ni iki ari cyo gushyira inyandiko inyenyeri?",
    "star-desc-1":
      "Iyo ushyize inyandiko inyenyeri, ihabwa ubushobozi bwo kugaragara mu bushakashatsi bw'amajwi.",
    "star-desc-2":
      "Ibi bifasha kugirango ibikubiye mu nyandiko zifite inyenyeri bigaragare kenshi mu bisubizo.",
    "star-desc-3":
      "Koresha iyi feature ku nyandiko z'ingenzi zigomba kugira agaciro kanini mu bisubizo by'ubushakashatsi.",
    "star-mark": "Shyira inyandiko inyenyeri",
    "star-unmark": "Kuramo inyenyeri",
    "star-added": "Inyandiko yashyizwe inyenyeri",
    "star-removed": "Inyenyeri yakuwe ku nyandiko",
    "star-failed": "Kuvugurura inyenyeri byananiye",
    "requires-metadata-update":
      "Dokumenti isaba kuvugururwa kwa metadata mbere yo gushirwa inyenyeri",
    "metadata-update-title": "Gusaba kuvugurura metadata",
    "metadata-update-confirm":
      "Iyi nyandiko isaba kuvugururwa kwa metadata mbere yo gushyirwa inyenyeri. Ni inzira yihuse itagombera kongera gushyira mu nkingi. Urashaka kuyivugurura ubu?",
    "metadata-update-success": "Metadata y'inyandiko yavuguruwe neza",
    "metadata-update-failed": "Kuvugurura metadata y'inyandiko byananiye",
    "metadata-update-confirm-button": "Vugurura metadata",
    "metadata-update-inprogress": "Guvugurura metadata y'inyandiko...",
    "copy-citation": "Gukoporora asubirajambo",
    "copy-citations": "Gukoporora amasubirajambo",
    "open-link": "Gufungura ihuza",
    "open-source": "Gufungura inyandiko y'inkomoko",
    "open-link-new-tab": "Gufungura ihuza mu idirishya rishya",
    "stop-watching-confirm":
      "Ese urashaka guhagarika gukurikirana iyi nyandiko ku mpinduka?",
    "unknown-error": "Habaye ikosa ritazwi",
    "watch-title": "Ni iki bivuze gukurikirana inyandiko?",
    "watch-desc-1":
      "Iyo ukurikira inyandiko, platform izajya ivugurura ibikubiye mu nyandiko kuva ku isoko ryayo.",
    "watch-desc-2":
      "Uyu mukorere ukurikirana inyandiko uboneka kuri content iri online.",
    "watch-desc-3": "Urashobora gucunga inyandiko zikurikirana muri",
    "file-manager": "File manager",
    "admin-view": "admin view",
    "pdr-add": "Ongeraho inyandiko zose muri Parent Document Retrieval",
    "pdr-remove": "Kuramo inyandiko zose muri Parent Document Retrieval",
    empty: "Nta nyandiko zabonetse",
    tooltip: {
      date: "Itariki: ",
      type: "Ubwoko: ",
      cached: "Byabitswe",
    },
    actions: {
      removing: "Gukuramo dosiye mu mwanya w'akazi",
      "removing-folder":
        "Gukuramo dosiye n'amadosiye yose irimo mu mwanya w'akazi",
      "removing-folder-contents":
        "Gukuramo amadosiye yose ari muri dosiye mu mwanya w'akazi",
    },
    costs: {
      estimate: "Igiciro kiteganijwe: $",
      minimum: "< $0.01",
    },
    "new-folder": {
      title: "Kurema dosiye nshya",
      "name-label": "Izina rya dosiye",
      "name-placeholder": "Andika izina rya dosiye",
      create: "Kurema dosiye",
    },
    error: {
      "create-folder": "Kunanirwa kurema dosiye",
      "remove-folder": "Kunanirwa gukuramo dosiye",
      "remove-generic": "Kunanirwa gukuramo ikintu",
    },
  },
};
