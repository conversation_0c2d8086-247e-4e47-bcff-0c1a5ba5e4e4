const { runFlow } = require("./flowDispatcher");
const { v4: uuidv4 } = require("uuid");
const { purgeDocumentBuilder } = require("../files");
const { writeResponseChunk } = require("../helpers/chat/responses");
const { WorkspaceChats } = require("../../models/workspaceChats");
// Note: stream processing helpers are handled within individual flows; no direct usage here.

/**
 * streamChatWithWorkspaceCDB - Main entry point for Complex Document Builder (CDB) flows.
 *
 * This function now acts as a thin wrapper around the flowDispatcher, which routes
 * to the appropriate drafting flow (main document or no main document) based on
 * legal task configuration and provided options.
 *
 * @param {Object} request - The Express request object.
 * @param {Object} response - The Express response object.
 * @param {Object} workspace - The current workspace object.
 * @param {string} message - The user's initial message or legal task description.
 * @param {string} [chatMode="chat"] - The mode of chat interaction (e.g., "chat", "query").
 * @param {Object} [user=null] - The user object.
 * @param {Object} [thread=null] - The current chat thread object.
 * @param {Array} [attachments=[]] - Any attachments to the message.
 * @param {string} chatIdInput - The unique ID for this chat interaction.
 * @param {boolean} [isCanvasChat=false] - Flag indicating if the chat is a canvas chat.
 * @param {boolean} [preventChatCreation=false] - Flag to prevent creation of a new chat record.
 * @param {string} [settings_suffix=""] - Suffix for system settings lookups.
 * @param {string} [invoice_ref] - Invoice reference number.
 * @param {string} [vectorSearchMode="default"] - Vector search mode to use.
 * @param {boolean} [hasUploadedFile=false] - Flag indicating if a file was uploaded with the request.
 * @param {string} [displayMessage=null] - A message to display to the user (overrides the main message).
 * @param {boolean} [useDeepSearch=false] - Flag to enable deep search capabilities.
 * @param {Array} [cdbOptions=[]] - Array of CDB-specific options:
 *                                  [0]: legalPrompt (string) - The core legal prompt.
 *                                  [1]: customInstructions (string) - Additional user instructions.
 *                                  [2]: mainDocNameFromOptions (string) - Name of the main document, if specified in options.
 * @param {Object} [legalTaskConfig=null] - Configuration object for the selected legal task, if any.
 *                                          This object can contain `flowType` and other task-specific settings.
 * @returns {Promise<void>} - The function streams responses and does not return a direct value.
 */
async function streamChatWithWorkspaceCDB(
  request,
  response,
  workspace,
  message,
  chatMode = "chat",
  user = null,
  thread = null,
  attachments = [],
  chatIdInput,
  isCanvasChat = false,
  preventChatCreation = false,
  settings_suffix = "",
  invoice_ref,
  vectorSearchMode = "default",
  hasUploadedFile = false,
  displayMessage = null,
  useDeepSearch = false,
  cdbOptions = [],
  legalTaskConfig = null
) {
  const resolvedChatId = chatIdInput || uuidv4();

  console.log(
    `[CDB PROCESS START] ChatId: ${resolvedChatId}, Workspace: ${workspace?.slug || "unknown"}, Thread: ${thread?.slug || "none"}, User: ${user?.id || "unknown"}, Task: ${legalTaskConfig?.name || displayMessage || "custom"}`
  );

  const abortController = new AbortController();
  const { signal: abortSignal } = abortController;

  const startTime = Date.now();
  let cleanupExecuted = false;

  const executeCleanup = () => {
    if (cleanupExecuted) return;
    cleanupExecuted = true;

    const connectionDuration = Date.now() - startTime;

    console.log(
      `[CDB PROCESS CANCELLED] ChatId: ${resolvedChatId}, Workspace: ${workspace?.slug || "unknown"}, Thread: ${thread?.slug || "none"}, User: ${user?.id || "unknown"}, Duration: ${connectionDuration}ms`
    );

    try {
      abortController.abort();
      purgeDocumentBuilder({ uuid: resolvedChatId });
    } catch (e) {
      console.error(
        `[CDB] Error during abort cleanup for ${resolvedChatId}:`,
        e
      );
    }
  };

  // Setup abort detection
  request.on("close", () => executeCleanup("request.close"));
  request.on("aborted", () => executeCleanup("request.aborted"));
  response.on("close", () => executeCleanup("response.close"));

  // Consolidate all options for the dispatcher
  const flowOptions = {
    request,
    response,
    workspace,
    message,
    chatMode,
    user,
    thread,
    attachments,
    chatId: resolvedChatId,
    isCanvasChat,
    preventChatCreation,
    settings_suffix,
    invoice_ref,
    vectorSearchMode,
    hasUploadedFile,
    displayMessage,
    useDeepSearch,
    cdbOptions,
    mainDocName: cdbOptions && cdbOptions[2] ? cdbOptions[2] : null,
    legalTask: legalTaskConfig,
    abortSignal,
  };

  // runFlow handles streaming and final document generation internally; no need to capture a return value here.
  const finalDocumentContent = await runFlow(flowOptions);

  // Final cleanup (example) - this should ideally be handled within the flows as well,
  // or in a finally block here if it's a general cleanup for the wrapper.
  // For now, let's assume flows handle their specific temp data, or this is a general cleanup.
  await purgeDocumentBuilder({ uuid: resolvedChatId });

  // Send the complete final document at once
  writeResponseChunk(response, {
    uuid: resolvedChatId,
    sources: [],
    type: "textResponseChunk",
    textResponse: finalDocumentContent,
    close: false,
    error: false,
  });

  try {
    // Only create a chat entry if we have a valid text response and we're not prevented from chat creation
    if (finalDocumentContent?.length > 0 && !preventChatCreation) {
      const { chat } = await WorkspaceChats.new({
        workspaceId: workspace.id,
        prompt: displayMessage || message,
        response: {
          text: finalDocumentContent,
          sources: [],
          type: chatMode,
          attachments,
        },
        threadId: thread?.id || null,
        user,
        invoice_ref,
      });
      chatIdInput = chat.id;
    }
  } catch (error) {
    console.error("Error creating chat entry:", error);
  } finally {
    // Always finalize the response stream to prevent UI from hanging
    writeResponseChunk(response, {
      uuid: resolvedChatId,
      type: "finalizeResponseStream",
      close: true,
      error: finalDocumentContent?.length === 0 || false, // Mark as error if empty response
      chatId: resolvedChatId,
      metrics: {},
    });
  }
}

module.exports = {
  streamChatWithWorkspaceCDB,
};
