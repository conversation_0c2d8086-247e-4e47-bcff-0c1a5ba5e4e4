export default {
  // Bsed on this order of pages in system settings:
  // Customization (Appearance)
  //Admin: System settings
  //Admin: Users
  //Admin: Invites
  //Admin: Workspaces
  //Admin: MCP Servers
  //Admin: Feedback List
  //Admin: Workspace chats
  //Admin: Default system
  //Admin: Document Builder
  //Admin: DD Prompt settings
  //Admin: Chat UI settings
  //AI-Providers: LLM
  //AI-Providers: Prompt Upgrade LLM
  //AI-Providers: Validation LLM
  //AI-Providers: CDB LLM
  //AI-Providers: Custom User AI
  //AI-Providers:  Vector Database
  //AI-Providers: Re-ranking settings
  //AI-Providers: Deep search
  //AI-Providers: Embedder
  //AI-Providers: Text Splitter & Chunking
  //AI-Providers: Voice & Speech
  //AI-Providers: transcription
  //AI-Providers: PDR System
  //Agent skills
  //Tools: Chat Embed History
  //Tools: Chat Embed
  //Tools: Event logs
  //Tools: Developer API
  //Tools: Browser Extension
  //Security
  //Document Drafting: Configuration
  //Document Drafting: Drafting LLM
  //Document Drafting: Chat Settings

  // =========================
  // APPEARANCE PAGE
  // =========================
  appearance: {
    title: "Apparence",
    description:
      "Personnalisez les paramètres d'apparence de votre plateforme.",
    logo: {
      title: "Personnaliser le logo",
      description: "Téléchargez votre logo personnalisé pour le mode clair.",
      add: "Ajouter un logo personnalisé",
      recommended: "Taille recommandée : 800 x 200",
      remove: "Supprimer",
    },
    logoDark: {
      title: "Personnaliser le logo pour le mode sombre",
      description: "Téléchargez votre logo personnalisé pour le mode sombre.",
    },
    "welcome-message": {
      title: "Personnaliser les messages de bienvenue",
      description:
        "Personnalisez l'en-tête et le texte affichés sur la page d'accueil.",
      heading: "En-tête",
      text: "Texte",
      save: "Enregistrer les messages",
    },
    icons: {
      title: "Icônes de pied de page personnalisées",
      description:
        "Personnalisez les icônes affichées en bas de la barre latérale.",
      icon: "Icône",
      link: "Lien",
    },
    display: {
      title: "Langue d'affichage",
      description: "Sélectionnez la langue préférée.",
    },
    color: {
      title: "Couleurs personnalisées",
      reset: "Réinitialiser",
      "desc-start": "Personnalisez les",
      "desc-mid": "*couleur de fond, *couleur principale",
      "desc-and": "et",
      "desc-end": "*couleur du texte",
      red: "Rouge",
      gray: "Gris",
      foynet: "Foynet",
      brown: "Marron",
      green: "Vert",
      yellow: "Jaune",
      cyan: "Cyan",
      magenta: "Magenta",
      orange: "TenderFlow",
      purple: "Violet",
      navy: "Bleu Marine",
      black: "Noir",
    },
    login: {
      title: "Texte de connexion personnalisé",
      description:
        "Personnalisez le paragraphe affiché sur la page de connexion.",
      placeholder: "Veuillez contacter l'administrateur du système",
      website: {
        title: "Personnaliser le site Web de l'application",
        description: "Personnalisez l'URL du site Web de votre application.",
        toggle: "Afficher le lien du site Web",
      },
      validation: { invalidURL: "Veuillez entrer une URL valide." },
    },
    siteSettings: {
      title: "Paramètres du site personnalisés",
      description:
        "Modifiez le contenu de l'onglet du navigateur pour la personnalisation et le branding.",
      tabTitle: "Titre de l'onglet",
      tabDescription:
        "Définissez un titre d'onglet personnalisé lorsque l'application est ouverte dans un navigateur.",
      tabIcon: "Favicon de l'onglet",
      fabIconUrl:
        "Définissez une URL vers une image à utiliser pour votre favicon",
      placeholder: "URL de votre image",
      "invalid-file-type":
        "Type de fichier invalide. Veuillez utiliser des fichiers PNG, JPEG, GIF, WebP, SVG ou ICO.",
      "file-too-large": "Fichier trop volumineux. Taille maximale 5MB.",
      "default-title": "IST Legal",
    },
    appName: {
      title: "Nom de l'application personnalisé",
      description:
        "Définissez un nom d'application personnalisé qui s'affiche sur la page de connexion.",
    },
    customTab: {
      title: "Personnaliser le nom des onglets",
      tab1: "Onglet 1",
      tab2: "Onglet 2",
      tab3: "Onglet 3",
    },
    template: {
      title: "Modèle DOCX système",
      description:
        "Téléchargez un modèle Word par défaut (.docx) qui sera utilisé pour les exportations Canvas.",
      tags: "Balises de fusion requises dans le document : {{title}} pour le titre du document et {{body}} pour le contenu principal. Balises optionnelles : {{author}}, {{date}}, {{createdAt}}, {{updatedAt}}, {{header}}, {{footer}}, {{pageNumber}}, {{totalPages}}, {{company}}, {{reference}}.",
      remove: "Supprimer",
      upload: "Télécharger le modèle",
      invalid: "Seuls les fichiers .docx sont autorisés",
    },
    "prompt-examples": {
      title: "Exemples de prompts",
      description:
        "Gérez les exemples de prompts affichés sur la page principale.",
      example: "Exemple {{number}}",
      remove: "Supprimer",
      "title-field": "Titre",
      area: "Domaine juridique",
      prompt: "Prompt",
      icon: "Sélectionner un icône",
      workspace: "Espace de travail",
      add: "Ajouter un exemple",
      save: "Enregistrer l'interface utilisateur",
    },
  },
  "appearance.siteSettings.tabIcon": "Icône de l'onglet",
  "appearance.siteSettings.fabIconUrl": "URL du favicon",
  "appearance.siteSettings.placeholder": "Entrez l'URL du favicon",
  "appearance.siteSettings.title-placeholder": "Entrez le titre du site",
  "appearance.welcome.heading": "Bienvenue sur IST Legal",
  "appearance.welcome.text": "Sélectionnez le domaine juridique à gauche",

  // ----------------------------
  // System Preferences
  // ----------------------------
  system: {
    title: "Préférences du système",
    "desc-start":
      "Voici les paramètres et configurations généraux de votre instance.",
    context_window: {
      title: "Fenêtre de contexte dynamique",
      desc: "Contrôlez la quantité de la fenêtre de contexte LLM utilisée pour les sources supplémentaires.",
      label: "Pourcentage de la fenêtre de contexte",
      help: "Pourcentage de la fenêtre de contexte qui peut être utilisé pour l'enrichissement (10-100%).",
      "toast-success":
        "Pourcentage de la fenêtre de contexte mis à jour avec succès.",
      "toast-error":
        "Échec de la mise à jour du pourcentage de la fenêtre de contexte.",
    },
    "change-login-ui": {
      title: "Sélectionner l'interface de connexion par défaut",
      status: "Actuel",
      subtitle:
        "L'interface sera appliquée comme interface de connexion par défaut pour l'application",
    },
    attachment_context: {
      title: "Fenêtre de Contexte des Pièces Jointes",
      desc: "Contrôlez la quantité de la fenêtre de contexte du LLM pouvant être utilisée pour les pièces jointes.",
      label: "Pourcentage de Contexte des Pièces Jointes",
      help: "Pourcentage de la fenêtre de contexte pouvant être utilisé pour les pièces jointes (10-80%).",
      "toast-success":
        "Pourcentage de contexte des pièces jointes mis à jour avec succès.",
      "toast-error":
        "Échec de la mise à jour du pourcentage de contexte des pièces jointes.",
      "validation-error":
        "Le pourcentage de contexte des pièces jointes doit être compris entre 10 et 80.",
    },
    user: "Les utilisateurs peuvent supprimer des espaces de travail",
    "desc-delete":
      "Permettre aux utilisateurs non administrateurs de supprimer des espaces de travail auxquels ils appartiennent. Cela supprimera l'espace de travail pour tout le monde.",
    limit: {
      title: "Limiter les messages par utilisateur par jour",
      "desc-limit":
        "Limitez le nombre de messages qu'un utilisateur peut envoyer en 24 heures afin d'éviter des coûts excessifs.",
      "per-day": "Messages par jour",
      label: "Limite de messages : ",
    },
    max_tokens: {
      title: "Tokens de connexion maximum par utilisateur",
      desc: "Définissez le nombre maximum de tokens d'authentification actifs que chaque utilisateur peut avoir simultanément. En cas de dépassement, les anciens tokens seront automatiquement supprimés.",
      label: "Tokens maximum",
      help: "La valeur doit être supérieure à 0",
    },
    state: {
      enabled: "Activé",
      disabled: "Désactivé",
    },
    "source-highlighting": {
      title: "Activer / Désactiver la mise en évidence des sources",
      description:
        "Masquer ou afficher la mise en évidence des sources pour les utilisateurs.",
      label: "Citation : ",
      "toast-success":
        "Le paramètre de mise en évidence des sources a été mis à jour",
      "toast-error":
        "Échec de la mise à jour du paramètre de mise en évidence des sources",
    },
    "usage-registration": {
      title: "Enregistrement de l'utilisation pour la facturation",
      description:
        "Activez ou désactivez l'enregistrement des factures pour la surveillance du système.",
      label: "La journalisation des factures est ",
    },
    "forced-invoice-logging": {
      title: "Journalisation des factures forcée",
      description:
        "Activez pour exiger une référence de facturation avant d'utiliser la plateforme.",
      label: "La journalisation forcée des factures est ",
    },
    "rexor-linkage": {
      title: "Liaison Rexor",
      description:
        "Activez la liaison Rexor pour obtenir les références des cas actifs du service Rexor.",
      label: "La liaison Rexor est ",
      "activity-id": "ID d'activité",
      "activity-id-description":
        "Entrez l'ID d'activité pour l'intégration Rexor",
    },
    save: "Enregistrer les modifications",
    rerank: {
      title: "Paramètres de reclassement",
      description:
        "Configurez les paramètres de reclassement pour améliorer la pertinence des résultats de recherche avec LanceDB.",
      "enable-title": "Activer le reclassement",
      "enable-description":
        "Activez le reclassement pour améliorer la pertinence des résultats de recherche en tenant compte de plus de contexte.",
      status: "Statut du reclassement",
      "vector-count-title": "Vecteurs supplémentaires pour le reclassement",
      "vector-count-description":
        "Nombre de vecteurs supplémentaires à récupérer au-delà du paramètre de nombre de vecteurs de l'espace de travail. Par exemple, si l'espace de travail est configuré pour récupérer 30 vecteurs et que ce paramètre est fixé à 50, un total de 80 vecteurs sera pris en compte pour le reclassement. Un nombre plus élevé peut améliorer la précision mais augmentera le temps de traitement.",
      "lancedb-only": "LanceDB uniquement",
      "lancedb-notice":
        "Cette fonctionnalité n'est disponible que lors de l'utilisation de LanceDB comme base de données vectorielle.",
    },
  },
  // =========================
  // ADMIN SYSTEM SETTINGS
  // =========================
  admin: {
    system: {
      universityMode: {
        title: "Mode Université",
        description:
          "Lorsque activé, masque les outils de validation, de mise à niveau de prompt, de modèle et de recherche web pour tous les utilisateurs.",
        enable: "Activer le mode université",
        saved: "Paramètres du mode université enregistrés.",
        error: "Échec de l'enregistrement des paramètres du mode université.",
        saveChanges: "Enregistrer les paramètres du mode université",
      },
    },
  },
  // ----------------------------
  // Public Mode
  // ----------------------------
  "public-mode": {
    enable: "Activer le Mode Public-Utilisateur",
    enabled: "Le Mode Public-Utilisateur est activé",
  },
  userAccess: {
    title: "Autoriser l'accès utilisateur",
    description:
      "Activez pour permettre aux utilisateurs réguliers d'accéder aux tâches juridiques. Par défaut, seuls les superusers, les gestionnaires et les administrateurs ont accès.",
    label: "Accès utilisateur : ",
    state: {
      enabled: "Activé",
      disabled: "Désactivé",
    },
  },
  // ----------------------------
  // Support Email
  // ----------------------------
  support: {
    title: "Email de Support",
    description:
      "Définissez l'adresse email de support qui apparaît dans le menu utilisateur lors de la connexion à cette instance.",
    clear: "Effacer",
    save: "Enregistrer",
  },
  promptLogging: {
    title: "Journalisation des sorties de prompts",
    description:
      "Activer ou désactiver la journalisation des sorties de prompts pour la surveillance du système.",
    label: "Journalisation des sorties de prompts : ",
    state: {
      enabled: "Activé",
      disabled: "Désactivé",
    },
  },
  // ----------------------------
  // User Settings (Administration)
  // ----------------------------
  "user-setting": {
    description:
      "Voici tous les comptes qui existent sur cette instance. La suppression d'un compte supprimera immédiatement leur accès.",
    "add-user": "Ajouter un utilisateur",
    username: "Adresse e-mail",
    role: "Rôle",
    "economy-id": "ID Économique",
    "economy-id-ph": "Entrez l'identifiant du système économique",
    "economy-id-hint":
      "ID utilisé pour les intégrations avec des systèmes économiques externes (par ex., Rexor)",
    default: "Par défaut",
    manager: "Responsable",
    admin: "Administrateur",
    superuser: "Superuser",
    "date-added": "Date d'ajout",
    "all-domains": "Tous les domaines",
    "other-users": "Autres utilisateurs (Aucun domaine)",
    // Options de tri pour la liste des utilisateurs
    "sort-username": "Trier par nom d'utilisateur",
    "sort-organization": "Trier par organisation",
    edit: "Modifier : ",
    "new-password": "Nouveau mot de passe",
    "password-rule": "Le mot de passe doit comporter au moins 8 caractères.",
    "update-user": "Mettre à jour l'utilisateur",
    placeholder: "Entrez l'adresse e-mail",
    cancel: "Annuler",
    "remove-user": "Supprimer l'utilisateur",
    "remove-user-title": "Supprimer l'utilisateur",
    "remove-user-confirmation":
      "Êtes-vous sûr de vouloir supprimer cet utilisateur ?",
    error: "Erreur : ",
  },
  // ----------------------------
  // New User (Creation)
  // ----------------------------
  "new-user": {
    title: "Ajouter un utilisateur à l'instance",
    username: "Adresse e-mail",
    "username-ph": "Entrez l'adresse e-mail de l'utilisateur",
    password: "Mot de passe",
    "password-ph": "Mot de passe initial de l'utilisateur",
    role: "Rôle",
    default: "Par défaut",
    manager: "Responsable",
    admin: "Administrateur",
    superuser: "Superuser",
    description:
      "Après création, l'utilisateur devra se connecter avec son mot de passe initial pour obtenir l'accès.",
    cancel: "Annuler",
    "add-User": "Ajouter un utilisateur",
    error:
      "Impossible de créer l'utilisateur. Cela peut être dû au fait que l'utilisateur existe déjà ou qu'une erreur système s'est produite.",
    "invalid-email": "Veuillez saisir une adresse e-mail valide.",
    permissions: {
      title: "Autorisations",
      default: [
        "Peut seulement envoyer des chats dans les espaces de travail auxquels il a été ajouté par un administrateur ou responsable.",
        "Ne peut modifier aucun paramètre.",
      ],
      manager: [
        "Peut voir, créer et supprimer des espaces de travail ainsi que modifier les paramètres spécifiques.",
        "Peut créer, mettre à jour et inviter de nouveaux utilisateurs.",
        "Ne peut pas modifier les connexions LLM, vectorDB, intégration, etc.",
      ],
      admin: [
        "Le niveau d'utilisateur le plus élevé.",
        "Peut tout voir et tout faire sur le système.",
      ],
      superuser: [
        "Peut accéder à des pages de paramètres spécifiques comme le Constructeur de Documents et l'Amélioration des Prompts.",
        "Ne peut pas modifier les paramètres globaux comme les configurations LLM, vectorDB.",
        "Peut envoyer des chats dans les espaces de travail auxquels il a été ajouté par un administrateur ou responsable.",
      ],
    },
  },
  // ----------------------------
  // Invitations
  // ----------------------------
  invites: {
    title: "Invitations",
    description:
      "Créez des liens d'invitation pour permettre aux personnes de votre organisation de s'inscrire. Chaque invitation ne peut être utilisée qu'une seule fois.",
    link: "Créer un lien d'invitation",
    accept: "Accepté par",
    usage: "Utilisation",
    "created-by": "Créé par",
    created: "Créé",
    new: {
      title: "Créer une nouvelle invitation",
      "desc-start":
        "Après création, vous pourrez copier l'invitation et l'envoyer à un nouvel utilisateur qui créera un compte avec le",
      "desc-mid": "rôle par défaut",
      "desc-end":
        "et sera automatiquement ajouté aux espaces de travail sélectionnés.",
      "auto-add": "Ajouter automatiquement l'invité aux espaces de travail",
      "desc-add":
        "Vous pouvez également assigner automatiquement l'utilisateur aux espaces de travail ci-dessous. Par défaut, aucun espace de travail ne sera visible. Vous pourrez assigner des espaces ultérieurement.",
      cancel: "Annuler",
      "create-invite": "Créer l'invitation",
      error: "Erreur : ",
    },
    "link-copied": "Lien d'invitation copié",
    "copy-link": "Copier le lien d'invitation",
    "delete-invite-title": "Désactiver l'invitation",
    "delete-invite-confirmation":
      "Êtes-vous sûr de vouloir désactiver cette invitation ?\nAprès cela, elle ne sera plus utilisable.\n\nCette action est irréversible.",
    status: {
      label: "Statut",
      pending: "En attente",
      disabled: "Désactivé",
      claimed: "Accepté",
    },
  },
  // =========================
  // INVITATION STRINGS
  // =========================
  invite: {
    "accept-button": "Accepter l'invitation",
    newUser: {
      title: "Créer un nouveau compte",
      usernameLabel: "Nom d'utilisateur",
      passwordLabel: "Mot de passe",
      description:
        "Après avoir créé votre compte, vous pourrez vous connecter avec ces identifiants et commencer à utiliser les espaces de travail.",
    },
  },
  // ----------------------------
  // Workspace Overview
  // ----------------------------
  workspace: {
    title: "Espaces de travail de l'instance",
    description:
      "Voici tous les espaces de travail existants sur cette instance. La suppression d'un espace de travail supprimera tous ses chats et paramètres associés.",
    "new-workspace": "Nouveau Espace de Travail",
    name: "Nom",
    link: "Lien",
    users: "Utilisateurs",
    type: "Type",
    "created-on": "Créé le",
    save: "Enregistrer les modifications",
    cancel: "Annuler",
    "sort-by-name": "Trier par nom",
    sort: "Trier par ordre alphabétique",
    unsort: "Restaurer l'ordre initial",
    deleted: {
      title: "Espace de travail introuvable !",
      description:
        "Il semble qu'un espace de travail portant ce nom ne soit pas disponible.",
      homepage: "Retour à l'accueil",
    },
    "no-workspace": {
      title: "Aucun espace de travail disponible",
      description: "Vous n'avez pas encore accès aux espaces de travail.",
      "contact-admin":
        "Veuillez contacter votre administrateur pour demander l'accès.",
      "learn-more": "En savoir plus sur les espaces de travail",
    },
    "no-workspaces":
      "Vous n'avez pas encore d'espaces de travail. Choisissez un domaine juridique à gauche pour commencer.",
    "my-workspaces": "Mes espaces de travail",
    "show-my": "Afficher mes espaces de travail",
    "show-all": "Afficher tous les espaces de travail",
    "creator-id": "Créé par l'utilisateur ID : {{id}}",
    "cloud-ai": "IA basée sur le cloud",
    "local-ai": "IA locale",
    "welcome-mobile":
      "Appuyez sur le bouton en haut à gauche pour sélectionner un domaine juridique.",
    "loading-username": "Chargement du nom d'utilisateur...",
    "ai-type": "Module",
    "latest-activity": "Dernière activité",
    "today-time": "Aujourd'hui, {{time}}",
    "date-time": "{{day}} {{month}}, {{time}}",
  },
  // =========================
  // CHAT LOGS & PREVIEW
  // =========================
  chat_logs: {
    display_description:
      "Afficher la journalisation des données brutes, ouvrir et télécharger le fichier",
    display_prompt_output: "Afficher les données brutes",
    loading_prompt_output: "Chargement des données brutes...",
    not_available:
      "*** Les données brutes ne sont pas disponibles pour ce chat.",
    token_count: "Tokens (dans toutes les données brutes) : {{count}}",
    token_count_detailed:
      "Tokens vers LLM : {{promptTokens}} | Tokens dans la réponse LLM : {{completionTokens}} | Total des tokens : {{totalTokens}}",
  },
  // =========================
  // LOGGING
  // =========================
  logging: {
    show: "afficher",
    hide: "masquer",
    "event-metadata": "Métadonnées d'événement",
  },

  // =========================
  // MCP SERVER PAGE
  // =========================
  mcp: {
    title: "Gestion des serveurs MCP",
    description:
      "Gérer les configurations des serveurs Multi-Component Processing (MCP).",
    currentServers: "Serveurs MCP actuels",
    noServers: "Aucun serveur MCP configuré.",
    fetchError: "Échec de la récupération des serveurs : {{error}}",
    addServerButton: "Ajouter un nouveau serveur",
    addServerModalTitle: "Ajouter un nouveau serveur MCP",
    addServerModalDesc:
      "Définir la configuration pour le nouveau processus serveur MCP.",
    serverName: "Nom du serveur (ID unique)",
    configJson: "Configuration (JSON)",
    addButton: "Ajouter le serveur",
    addSuccess: "Serveur ajouté avec succès.",
    addError: "Échec de l'ajout du serveur : {{error}}",
  },
  // ----------------------------
  // Recorded Workspace Chats
  // ----------------------------
  recorded: {
    title: "Conversations de l'espace de travail",
    description:
      "Voici toutes les conversations et messages enregistrés, classés par date de création.",
    export: "Exporter",
    table: {
      id: "Id",
      by: "Envoyé par",
      workspace: "Espace de travail",
      prompt: "Prompt",
      response: "Réponse",
      at: "Envoyé le",
      invoice: "Réf. facture",
      "completion-token": "Jeton de Complétion",
      "prompt-token": "Jeton de Prompt",
    },
    "clear-chats": "Supprimer toutes les conversations actuelles",
    "confirm-clear-chats":
      "Êtes-vous sûr de vouloir effacer toutes les conversations?\n\nCette action est irréversible.",
    "fine-tune-modal": "Commander un modèle Fine-Tune",
    "confirm-delete.chat":
      "Êtes-vous sûr de vouloir supprimer cette conversation?\n\nCette action est irréversible.",
    next: "Page suivante",
    previous: "Page précédente",
    filters: {
      "by-name": "Filtrer par nom d'utilisateur",
      "by-reference": "Numéro de référence",
    },
    bulk_delete_title: "Suppression en masse d'anciennes conversations",
    bulk_delete_description:
      "Supprimer tous les journaux de conversation plus anciens que la période sélectionnée.",
    delete_old_chats: "Supprimer les anciennes conversations",
    total_logs: "Total des journaux",
    filtered_logs: "Journaux filtrés",
    reset_filters: "Réinitialiser les filtres",
    "no-chats-found": "Aucune conversation trouvée",
    "no-chats-description":
      "Aucune conversation ne correspond à vos filtres. Essayez de modifier vos critères de recherche ou de supprimer une période plus ancienne.",
    "deleted-old-chats": "{{count}} ancienne(s) conversation(s) supprimée(s)",
    two_days: "2 jours",
    one_week: "1 semaine",
    two_weeks: "2 semaines",
    one_month: "1 mois",
    two_months: "2 mois",
    three_months: "3 mois",
    total_deleted: "Total des journaux de conversation supprimés",
  },
  // ----------------------------
  // Default Settings for Legal Q&A
  // ----------------------------
  "default-settings": {
    "canvas-prompt": "Invite système Canvas",
    "canvas-prompt-desc":
      "Invite pour le système de chat canvas. Utilisée comme invite système pour les interactions de chat canvas.",
    title: "Paramètres par défaut pour Questions/Réponses Juridiques",
    "default-desc":
      "Contrôlez le comportement par défaut des espaces de travail pour les Q&R Juridiques.",
    prompt: "Invite du système Q&R Juridiques",
    "prompt-desc":
      "Le prompt par défaut qui sera utilisé pour les nouveaux espaces de travail Legal Q&A. Définissez le contexte et les instructions pour que l'IA génère une réponse. Vous devez fournir un prompt soigneusement élaboré pour que l'IA puisse générer une réponse pertinente et précise. Pour appliquer ce paramètre à tous les espaces de travail existants et remplacer leurs paramètres personnalisés, utilisez le bouton ci-dessous.",
    "prompt-placeholder": "Saisissez votre prompt ici",
    "toast-success": "Prompt système par défaut mis à jour",
    "toast-fail": "Échec de la mise à jour du prompt système par défaut",
    "apply-all-confirm":
      "Êtes-vous sûr de vouloir appliquer ce prompt à tous les espaces de travail Legal Q&A existants ? Cette action ne peut pas être annulée et remplacera tous les paramètres personnalisés.",
    "apply-to-all":
      "Appliquer à tous les espaces de travail Legal Q&A existants",
    applying: "Application en cours...",
    "toast-apply-success":
      "Prompt par défaut appliqué à {{count}} espaces de travail",
    "toast-apply-fail":
      "Échec de l'application du prompt par défaut aux espaces de travail",
    snippets: {
      title: "Nombre maximal d'extraits de contexte par défaut",
      description:
        "Ce paramètre contrôle le nombre maximum d'extraits de contexte envoyés au LLM pour les nouveaux espaces de travail. Pour appliquer ce paramètre à tous les espaces de travail existants et remplacer leurs paramètres personnalisés, utilisez le bouton ci-dessous.",
      recommend:
        "La valeur recommandée est d'au moins 30. Définir des valeurs beaucoup plus élevées augmentera le temps de traitement sans nécessairement améliorer la précision selon la capacité du LLM utilisé.",
    },
    "rerank-limit": {
      title: "Limite maximale de reclassement",
      description:
        "Ce paramètre contrôle le nombre maximum de documents considérés pour le reclassement. Une valeur plus élevée peut améliorer les résultats mais augmente le temps de traitement.",
      recommend: "Recommandé : 50",
    },
    "validation-prompt": {
      title: "Invite de validation",
      description:
        "Ce paramètre contrôle l'invite de validation par défaut envoyée au LLM pour valider la réponse générée.",
      placeholder:
        "Veuillez valider la réponse suivante en vérifiant l'exactitude des références juridiques et citations par rapport au contexte fourni. Énumérez toute inexactitude ou erreur trouvée.",
    },
    "apply-vector-search-to-all":
      "Appliquer à tous les espaces de travail Legal Q&A existants",
    "apply-vector-search-all-confirm":
      "Êtes-vous sûr de vouloir appliquer ce paramètre de recherche vectorielle à tous les espaces de travail Legal Q&A existants ? Cette action ne peut pas être annulée.",
    "toast-vector-search-apply-success":
      "Paramètre de recherche vectorielle appliqué à {{count}} espaces de travail",
    "toast-vector-search-apply-fail":
      "Échec de l'application du paramètre de recherche vectorielle aux espaces de travail",
    "canvas-upload-prompt": "Invite système de téléchargement Canvas",
    "canvas-upload-prompt-desc":
      "L'invite système utilisée lors du traitement des fichiers téléchargés via le canvas. Cette invite guide le comportement du LLM pour le contenu téléchargé.",
    "manual-work-estimator-prompt": "Invite d'estimation du travail manuel",
    "manual-work-estimator-prompt-desc":
      "L'invite système utilisée pour estimer les heures de travail manuel nécessaires pour produire les réponses de l'IA. Cette invite guide le LLM pour fournir des estimations de temps réalistes pour le travail juridique.",
    "style-generation-prompt": "Invite de génération de style",
    "style-generation-prompt-desc":
      "L'invite utilisée pour analyser les documents téléchargés et générer des instructions de style personnalisées. Cette invite guide la façon dont l'IA analyse le style d'écriture et crée des instructions pour l'alignement de style.",
    saving: "Enregistrement...",
    save: "Enregistrer les modifications",
    "toast-fail-load-prompts":
      "Échec du chargement des configurations d'invites.",
  },
};
