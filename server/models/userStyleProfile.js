const prisma = require("../utils/prisma");

const UserStyleProfile = {
  // Create a new style profile for a user
  create: async function ({ userId, name, instructions }) {
    try {
      // Validate inputs
      if (!userId || !name || !instructions) {
        throw new Error("User ID, name, and instructions are required");
      }

      if (instructions.length > 10000) {
        throw new Error("Style instructions cannot exceed 10,000 characters");
      }

      // Create the profile
      const profile = await prisma.userStyleProfile.create({
        data: {
          user_id: parseInt(userId),
          name: String(name).trim(),
          instructions: String(instructions).trim(),
          is_active: false, // New profiles start inactive
        },
      });

      return { profile, error: null };
    } catch (error) {
      console.error("Error creating style profile:", error);
      return { profile: null, error: error.message };
    }
  },

  // Get all style profiles for a user
  getByUserId: async function (userId) {
    try {
      if (!userId) {
        throw new Error("User ID is required");
      }

      const profiles = await prisma.userStyleProfile.findMany({
        where: { user_id: parseInt(userId) },
        orderBy: { createdAt: "desc" },
      });

      return profiles;
    } catch (error) {
      console.error("Error fetching user style profiles:", error);
      return [];
    }
  },

  // Get a specific style profile by ID
  get: async function (profileId, userId = null) {
    try {
      if (!profileId) {
        throw new Error("Profile ID is required");
      }

      const whereClause = { id: parseInt(profileId) };
      if (userId) {
        whereClause.user_id = parseInt(userId);
      }

      const profile = await prisma.userStyleProfile.findFirst({
        where: whereClause,
      });

      return profile;
    } catch (error) {
      console.error("Error fetching style profile:", error);
      return null;
    }
  },

  // Get the active style profile for a user
  getActiveProfile: async function (userId) {
    try {
      if (!userId) {
        return null;
      }

      const profile = await prisma.userStyleProfile.findFirst({
        where: {
          user_id: parseInt(userId),
          is_active: true,
        },
      });

      return profile;
    } catch (error) {
      console.error("Error fetching active style profile:", error);
      return null;
    }
  },

  // Update a style profile
  update: async function (profileId, userId, updates = {}) {
    try {
      if (!profileId || !userId) {
        throw new Error("Profile ID and User ID are required");
      }

      // Validate the profile belongs to the user
      const existingProfile = await this.get(profileId, userId);
      if (!existingProfile) {
        throw new Error("Style profile not found or access denied");
      }

      // Prepare updates
      const validUpdates = {};
      if (updates.name !== undefined) {
        validUpdates.name = String(updates.name).trim();
      }
      if (updates.instructions !== undefined) {
        const instructions = String(updates.instructions).trim();
        if (instructions.length > 10000) {
          throw new Error("Style instructions cannot exceed 10,000 characters");
        }
        validUpdates.instructions = instructions;
      }

      if (Object.keys(validUpdates).length === 0) {
        throw new Error("No valid updates provided");
      }

      const profile = await prisma.userStyleProfile.update({
        where: { id: parseInt(profileId) },
        data: validUpdates,
      });

      return { profile, error: null };
    } catch (error) {
      console.error("Error updating style profile:", error);
      return { profile: null, error: error.message };
    }
  },

  // Delete a style profile
  delete: async function (profileId, userId) {
    try {
      if (!profileId || !userId) {
        throw new Error("Profile ID and User ID are required");
      }

      // Validate the profile belongs to the user
      const existingProfile = await this.get(profileId, userId);
      if (!existingProfile) {
        throw new Error("Style profile not found or access denied");
      }

      await prisma.userStyleProfile.delete({
        where: { id: parseInt(profileId) },
      });

      return { success: true, error: null };
    } catch (error) {
      console.error("Error deleting style profile:", error);
      return { success: false, error: error.message };
    }
  },

  // Activate a style profile (deactivates all others for the user)
  activate: async function (profileId, userId) {
    try {
      if (!profileId || !userId) {
        throw new Error("Profile ID and User ID are required");
      }

      // Validate the profile belongs to the user
      const existingProfile = await this.get(profileId, userId);
      if (!existingProfile) {
        throw new Error("Style profile not found or access denied");
      }

      // Use a transaction to ensure atomicity
      await prisma.$transaction(async (tx) => {
        // Deactivate all profiles for the user
        await tx.userStyleProfile.updateMany({
          where: { user_id: parseInt(userId) },
          data: { is_active: false },
        });

        // Activate the selected profile
        await tx.userStyleProfile.update({
          where: { id: parseInt(profileId) },
          data: { is_active: true },
        });
      });

      return { success: true, error: null };
    } catch (error) {
      console.error("Error activating style profile:", error);
      return { success: false, error: error.message };
    }
  },

  // Deactivate all style profiles for a user
  deactivateAll: async function (userId) {
    try {
      if (!userId) {
        throw new Error("User ID is required");
      }

      await prisma.userStyleProfile.updateMany({
        where: { user_id: parseInt(userId) },
        data: { is_active: false },
      });

      return { success: true, error: null };
    } catch (error) {
      console.error("Error deactivating style profiles:", error);
      return { success: false, error: error.message };
    }
  },

  // Count style profiles for a user
  count: async function (userId) {
    try {
      if (!userId) {
        return 0;
      }

      const count = await prisma.userStyleProfile.count({
        where: { user_id: parseInt(userId) },
      });

      return count;
    } catch (error) {
      console.error("Error counting style profiles:", error);
      return 0;
    }
  },
};

module.exports = { UserStyleProfile };
