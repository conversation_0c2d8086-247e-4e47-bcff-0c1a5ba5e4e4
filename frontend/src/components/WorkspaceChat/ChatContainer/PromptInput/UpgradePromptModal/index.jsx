import React, { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import Modal from "@/components/ui/Modal";

export default function UpgradePromptModal({
  isOpen,
  originalPrompt,
  upgradedPrompt,
  onDecline,
  onAccept,
  onChange,
}) {
  const { t } = useTranslation();
  const [editedPrompt, setEditedPrompt] = useState(upgradedPrompt);
  const textareaRef = useRef(null);

  useEffect(() => {
    onChange({ target: { value: upgradedPrompt } });
    setEditedPrompt(upgradedPrompt);
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [upgradedPrompt]);

  const handleTextAreaChange = (e) => {
    const newValue = e.target.value;
    setEditedPrompt(newValue);
    e.target.style.overflowY = e.target.scrollHeight > 200 ? "auto" : "hidden";
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onChange({ target: { value: editedPrompt } });
    onAccept(editedPrompt);
  };

  const handleDeclineWithUpdate = () => {
    onChange({ target: { value: originalPrompt } });
    onDecline();
  };

  // Simple keyboard handler - just use Enter to submit, Shift+Enter for new line
  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      onChange({ target: { value: editedPrompt } });
      onAccept(editedPrompt);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleDeclineWithUpdate}
      className="w-[48rem]"
      title={t("workspace-chats.prompt.upgrade")}
      footer={
        <>
          <div className="flex justify-end gap-4 p-4">
            <Button onClick={handleDeclineWithUpdate} variant="outline">
              {t("button.decline")}
            </Button>
            <Button type="submit" form="upgrade-prompt-form">
              {t("button.accept")}
            </Button>
          </div>
        </>
      }
    >
      <form
        role="form"
        id="upgrade-prompt-form"
        onSubmit={handleSubmit}
        className="flex flex-col flex-1"
      >
        <div className="p-6 flex-1 overflow-y-auto text-foreground">
          <div className="mb-4">
            <h3 className="font-medium mb-2">
              {t("workspace-chats.prompt.original-prompt")}
            </h3>
            <p className="break-words whitespace-pre-wrap">{originalPrompt}</p>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium">
                {t("workspace-chats.prompt.upgraded-prompt")}
              </h3>
            </div>
            <textarea
              ref={textareaRef}
              value={editedPrompt}
              onChange={handleTextAreaChange}
              onKeyDown={handleKeyDown}
              className="w-full p-3 rounded-md text-area resize-none h-auto min-h-52"
            />
          </div>
          <label className="border-none block mb-2 text-sm font-medium text-foreground">
            {t("workspace-chats.prompt.edit-prompt")}
          </label>
          <p className="text-xs text-muted-foreground">
            {t("workspace-chats.prompt.shortcut-tip", {
              defaultValue:
                "Tip: Press Enter to accept changes. Use Shift+Enter for new lines.",
            })}
          </p>
        </div>
      </form>
    </Modal>
  );
}
