export default {
  // ----------------------------
  // Workspace Chats
  // ----------------------------
  "workspace-chats": {
    welcome: "Bienvenue dans votre nouvel espace de travail.",
    "desc-start": "Pour commencer, vous pouvez soit",
    "desc-mid": "télécharger un document",
    "desc-or": "ou",
    start: "Pour commencer",
    "desc-end": "envoyer un message.",
    "attached-file": "Fichier joint",
    "attached-files": "Fichiers joints",
    "token-count": "Nombre de tokens",
    "total-tokens": "Nombre total de tokens",
    "context-window": "Fenêtre de contexte disponible",
    "remaining-tokens": "Restant",
    "view-files": "Voir les fichiers joints",
    prompt: {
      send: "Envoyer",
      "send-message": "Envoyer le message",
      placeholder: "Demander des informations juridiques",
      "change-size": "Changer la taille du texte",
      reset: "/réinitialiser",
      clear: "Effacer votre historique de chat et commencer un nouveau chat",
      command: "Commande",
      description: "Description",
      save: "sauver",
      small: "Petit",
      normal: "Normal",
      large: "Grand",
      larger: "Plus grand",
      attach: "Joindre un fichier à cette conversation",
      upgrade: "Améliorez votre prompt",
      upgrading: "Mise à jour de votre prompt",
      "original-prompt": "Invite original:",
      "upgraded-prompt": "Invite améliorée:",
      "edit-prompt":
        "Vous pouvez modifier le nouveau prompt avant de le soumettre",
      "shortcut-tip":
        "Astuce: Appuyez sur Entrée pour accepter les modifications. Utilisez Maj+Entrée pour les nouvelles lignes.",
      "speak-prompt": "Dictez votre prompt",
      "view-agents":
        "Voir tous les agents disponibles que vous pouvez utiliser pour chatter",
      "deep-search": "Recherche Web",
      "deep-search-tooltip":
        "Rechercher sur le web des informations pour améliorer les réponses",
      "ability-tag": "Capacité",
      "workspace-chats.prompt.view-agents": "Voir les agents",
      "workspace-chats.prompt.ability-tag": "Capacité",
      "workspace-chats.prompt.speak-prompt": "Dictez votre prompt",
      "total-tokens": "Nombre total de tokens",
    },
  },
  // ----------------------------
  // New Workspace
  // ----------------------------
  "new-workspace": {
    title: "Nouvel espace de travail",
    placeholder: "Mon espace de travail",
    "legal-areas": "Legal Areas",
    create: {
      title: "Créer un nouvel espace de travail",
      description:
        "Après avoir créé cet espace de travail, seuls les administrateurs pourront le voir. Vous pouvez ajouter des utilisateurs après sa création.",
      error: "Erreur : ",
      cancel: "Annuler",
      "create-workspace": "Créer un espace de travail",
    },
  },
  // ----------------------------
  // Workspace Names (Predefined)
  // ----------------------------
  "workspace-names": {
    "Administrative Law": "Droit administratif",
    "Business Law": "Droit des affaires",
    "Civil Law": "Droit civil",
    "Criminal Law": "Droit pénal",
    "Diplomatic Law": "Droit diplomatique",
    "Fundamental Law": "Droit fondamental",
    "Human Rights Law": "Droit des droits de l'homme",
    "Judicial Laws": "Lois judiciaires",
    "Security Laws": "Lois sur la sécurité",
    "Taxation Laws": "Lois fiscales",
  },
  // =========================
  // PRESETS BUTTON
  // =========================
  presets: {
    "edit-title": "Modifier l'invite standard",
    description: "Description de l'invite",
    "description-placeholder": "Fait un résumé des fichiers joints.",
    deleting: "Suppression...",
    "delete-preset": "Supprimer l'invite standard",
    cancel: "Annuler",
    save: "Sauvegarder",
    "add-title": "Ajouter une invite standard",
    "command-label": "Nom de l'invite, un seul mot",
    "command-placeholder": "Résumé",
    "command-desc":
      "Le nom est aussi le raccourci de la boîte de chat, commençant par /, pour utiliser cette invite sans appuyer sur les boutons.",
    "prompt-label": "Invite",
    "prompt-placeholder": "Produire un résumé des fichiers que j'ai joints.",
    "prompt-desc":
      "L'invite qui sera envoyée lorsque cette invite prédéfinie est utilisée.",
    "tooltip-add": "Ajouter une nouvelle invite standard",
    "tooltip-hover": "Voir vos propres invites standard.",
    "confirm-delete":
      "Confirmer la suppression de l'invite standard prédéfinie.",
  },
  // ----------------------------
  // Qura Buttons
  // ----------------------------
  qura: {
    "copy-to-cora": "Vérification des sources Qura",
    "qura-status": "Le bouton Qura est ",
    "copy-option": "Option de copie",
    "option-quest": "Question",
    "option-resp": "Reponse",
    "role-description":
      "Ajoutez un bouton Qura pour demander des réponses sur Qura.law",
  },
  // ----------------------------
  // Chat Box Drag and Drop
  // ----------------------------
  chatboxdnd: {
    title: "Ajouter un fichier",
    description:
      "Déposez votre fichier ici pour l'ajouter à ce message. Il ne sera pas sauvegardé dans l'espace de travail comme source permanente.",
    "file-prefix": "Fichier :",
    "attachment-tooltip":
      "Ce fichier sera joint à votre message. Il ne sera pas sauvegardé dans l'espace de travail comme source permanente.",
    "uploaded-file-tag": "FICHIER UTILISATEUR TÉLÉCHARGÉ",
  },
  // =========================
  // CONTEXT WINDOW DISPLAY
  // =========================
  context_window: {
    "context-window": "Fenêtre de contexte",
    "max-output-tokens": "Tokens de sortie max",
    "output-limit": "Limite de sortie",
    tokens: "tokens",
    "fallback-value": "Valeur par défaut utilisée",
  },
  // ----------------------------
  // Prompt Validation UI
  // ----------------------------
  "prompt-validate": {
    edit: "Modifier",
    response: "Réponse",
    prompt: "Invite",
    regenerate: "Régénérer la réponse",
    good: "Bonne réponse",
    bad: "Mauvaise réponse",
    copy: "Copier",
    more: "Plus d'actions",
    fork: "Dupliquer",
    delete: "Supprimer",
    cancel: "Annuler",
    save: "Enregistrer & Soumettre",
    "export-word": "Exporter vers Word",
    exporting: "Exportation...",
  },
  // ----------------------------
  // Validate Answer LLM Settings
  // ----------------------------
  "validate-answer": {
    setting: "Validation LLM",
    title: "Préférence de Validation LLM",
    description:
      "Ces identifiants et paramètres concernent votre fournisseur LLM pour la validation. Il est important que ces clés soient correctes pour le bon fonctionnement du système.",
    "toast-success": "Paramètres de validation LLM mis à jour",
    "toast-fail": "Échec de la mise à jour des paramètres de validation LLM",
    saving: "Enregistrement...",
    "save-changes": "Enregistrer les modifications",
  },
  // =========================
  // VALIDATION MODAL
  // =========================
  validation: {
    responseHeader: "Voici la réponse qui a été générée",
    contextHeader: "Contexte et sources originaux",
  },
  // ----------------------------
  // Citations
  // ----------------------------
  citations: {
    show: "Afficher les citations",
    hide: "Masquer les citations",
    chunk: "Extraits de citation",
    pdr: "Document parent",
    "pdr-h": "Surlignage du document",
    referenced: "Référencé",
    times: "fois.",
    citation: "Citation",
    match: "correspondance",
    download:
      "Ce navigateur ne prend pas en charge les PDF. Veuillez télécharger le PDF pour le visualiser :",
    "download-btn": "Télécharger le PDF",
    view: "Voir les citations",
    sources: "Voir les sources et citations",
    "pdf-collapse-tip":
      "Astuce : Vous pouvez réduire cet onglet PDF en utilisant le bouton dans le coin supérieur gauche",
    "open-in-browser": "Ouvrir dans le navigateur",
    "loading-pdf": "-- chargement du PDF --",
    "error-loading": "Erreur lors du chargement du PDF",
    "no-valid-path": "Aucun chemin PDF valide trouvé",
    "view-details": "Voir les détails",
    "web-search": "Recherche Web",
    "web-search-summary": "Résumé de la recherche Web",
    "web-search-results": "Résultats de la recherche Web",
    "no-web-search-results": "Aucun résultat de recherche Web trouvé",
    "previous-highlight": "Surlignage précédent",
    "next-highlight": "Surlignage suivant",
    "try-alternative-view": "Essayer la vue alternative",
  },
  // ----------------------------
  // Manual Work Estimator
  // ----------------------------
  "manual-work-estimator": {
    title: "Estimation du travail manuel",
    button: "Estimation temps",
    "show-prompt": "Afficher l'invite",
    "hide-prompt": "Masquer l'invite",
    "prompt-title": "Invite utilisée pour l'estimation",
    "system-prompt": "Invite système",
    "user-content": "Contenu utilisateur",
    "provider-info": "Informations du fournisseur",
    model: "Modèle",
    provider: "Fournisseur",
  },
};
