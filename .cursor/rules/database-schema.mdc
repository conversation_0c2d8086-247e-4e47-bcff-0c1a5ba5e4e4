---
description: Guidelines for database schema and models
globs: server/prisma/**/*.prisma,server/prisma/**/*.js,server/models/**/*.js
alwaysApply: false
---

# Database Schema and Models

## Prisma Schema

The database schema is defined in `server/prisma/schema.prisma` and includes models for:

- Users
- Workspaces
- Documents
- Workspace Chats
- Invites
- API Keys
- Event Logs
- Vector Databases
- Document Sync Queue
- System Settings
- And more

## Model Files

The model files in `server/models/` provide high-level data-access methods and domain logic that wrap the Prisma client:

- `documents.js`: CRUD for workspace documents, references to vector embedding
- `user.js`: User management and authentication
- `workspace.js`: Workspace creation, updates, and management
- `workspaceChats.js`: Chat history and message management
- `invite.js`: Invite-based user onboarding
- `apiKeys.js`: API key management
- `eventLogs.js`: Logging system events
- `vectors.js`: Vector database operations
- `systemSettings.js`: System-wide settings management
- `documentSyncQueue.js`: Document synchronization scheduling

## Database Operations

When working with the database:

1. Use the model files for database operations rather than direct Prisma calls
2. Follow the established patterns for CRUD operations
3. Use transactions for operations that modify multiple records
4. Handle errors appropriately and provide meaningful error messages
5. Consider performance implications of database queries

## Migrations

Database schema changes should be managed through Prisma migrations:

1. Update the schema.prisma file
2. Run `prisma db push` to update the local development database schema
3. For production, create a proper migration with `prisma migrate dev --name descriptive_name`

## Best Practices

- Keep model files focused on a single entity
- Implement proper validation before database operations
- Use appropriate indexes for performance
- Consider the impact of schema changes on existing data
- Document complex queries or operations
- Use appropriate error handling for database operations
