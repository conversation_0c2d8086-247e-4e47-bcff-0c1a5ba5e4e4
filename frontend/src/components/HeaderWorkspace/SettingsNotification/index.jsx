import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import useUser from "@/hooks/useUser";
import {
  useFetchFeedbackCount,
  useHasUnseenFeedback,
} from "@/stores/feedbackStore";
import { useFeedbackEnabled } from "@/stores/settingsStore";
import { cn } from "@/utils/classes";

const SettingsNotification = ({ className }) => {
  const { t } = useTranslation();
  const { user } = useUser();
  const fetchFeedbackCount = useFetchFeedbackCount();
  const hasUnseenFeedback = useHasUnseenFeedback();
  const feedbackEnabled = useFeedbackEnabled();

  const canSeeFeedbackCount =
    user?.role && ["admin", "manager", "superuser"].includes(user.role);

  useEffect(() => {
    if (canSeeFeedbackCount && feedbackEnabled?.enabled) {
      fetchFeedbackCount();

      const interval = setInterval(
        () => {
          fetchFeedbackCount();
        },
        2 * 60 * 1000
      );

      return () => clearInterval(interval);
    }
  }, [canSeeFeedbackCount, feedbackEnabled?.enabled, fetchFeedbackCount]);

  if (
    !feedbackEnabled?.enabled ||
    !canSeeFeedbackCount ||
    !hasUnseenFeedback()
  ) {
    return null;
  }

  return (
    <span
      className={cn(
        "inline-flex items-center justify-center w-2 h-2 bg-red-500 rounded-full",
        className
      )}
      title={t("feedback.badge.newFeedback")}
    />
  );
};

export default SettingsNotification;
