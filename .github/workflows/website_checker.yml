name: Website Status Check

on:
  schedule:
    # Run the script every 5 minutes
    - cron: "*/5 * * * *"

  # You can also manually trigger this workflow
  workflow_dispatch:

jobs:
  check_website:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.x'  # Use the version of Python your script requires

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install requests slack_sdk

    - name: Disable Logging
      run: echo "::stop-commands::no-log"

    - name: Run website check
      run: python scripts/website_check/check_website.py

    - name: Enable Logging
      run: echo "::no-log::"

