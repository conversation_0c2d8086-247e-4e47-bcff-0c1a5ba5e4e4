import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";

const Version = {
  /**
   * Get the current version information
   * @returns {Promise<{version: string, description: string} | null>}
   */
  getVersion: async function () {
    try {
      const response = await fetch(`${API_BASE}/version`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          ...baseHeaders(),
        },
      });

      if (!response.ok) {
        console.error("Failed to fetch version information");
        return null;
      }

      const data = await response.json();
      if (data.success) {
        return {
          version: data.version,
          description: data.description,
        };
      }

      return null;
    } catch (error) {
      console.error("Error fetching version:", error);
      return null;
    }
  },
};

export default Version;
