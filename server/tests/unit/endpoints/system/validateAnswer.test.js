const { SystemSettings } = require("../../../../models/systemSettings");
const { getLLMProvider } = require("../../../../utils/helpers");

// Mock the dependencies
jest.mock("../../../../models/systemSettings");
jest.mock("../../../../utils/helpers");

describe("ValidateAnswer Context Window Boost", () => {
  let mockLLMConnector;
  let mockSystemSettings;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock LLM connector
    mockLLMConnector = {
      promptWindowLimit: jest.fn().mockReturnValue(100000), // 100k tokens
      isValidChatCompletionModel: jest.fn().mockReturnValue(true),
      compressMessages: jest.fn().mockResolvedValue([
        { role: "system", content: "test system prompt" },
        { role: "user", content: "test user prompt" },
      ]),
      getChatCompletion: jest.fn().mockResolvedValue({
        textResponse: "Test validation response",
      }),
      constructor: { name: "OpenAiLLM" },
    };

    // Mock getLLMProvider
    getLLMProvider.mockReturnValue(mockLLMConnector);

    // Mock SystemSettings
    mockSystemSettings = {
      get: jest.fn(),
      getDynamicContextSettings: jest.fn(),
    };
    SystemSettings.get = mockSystemSettings.get;
    SystemSettings.getDynamicContextSettings =
      mockSystemSettings.getDynamicContextSettings;
  });

  test("should apply 5% boost to context window for validation prompts", async () => {
    // Test the logic directly since we can't easily extract the internal function
    const currentContextPercentage = 70;
    const boostedContextPercentage = Math.min(
      currentContextPercentage + 5,
      100
    );
    const baseContextWindow = 100000;
    const expectedBoostedContextWindow = Math.floor(
      baseContextWindow * (boostedContextPercentage / 100)
    );

    expect(boostedContextPercentage).toBe(75);
    expect(expectedBoostedContextWindow).toBe(75000);
  });

  test("should cap boost at 100% when base percentage is already high", async () => {
    const currentContextPercentage = 98;
    const boostedContextPercentage = Math.min(
      currentContextPercentage + 5,
      100
    );
    const baseContextWindow = 100000;
    const expectedBoostedContextWindow = Math.floor(
      baseContextWindow * (boostedContextPercentage / 100)
    );

    expect(boostedContextPercentage).toBe(100);
    expect(expectedBoostedContextWindow).toBe(100000);
  });

  test("should handle different base context window sizes", async () => {
    const testCases = [
      { base: 50000, percentage: 60, expected: 32500 }, // 65% of 50k
      { base: 200000, percentage: 80, expected: 170000 }, // 85% of 200k
      { base: 128000, percentage: 70, expected: 96000 }, // 75% of 128k
    ];

    testCases.forEach(({ base, percentage, expected }) => {
      const boostedPercentage = Math.min(percentage + 5, 100);
      const boostedWindow = Math.floor(base * (boostedPercentage / 100));
      expect(boostedWindow).toBe(expected);
    });
  });

  test("should pass boosted context window to compressMessages", async () => {
    // Mock system settings
    mockSystemSettings.get.mockResolvedValue({ value: "openai" });
    mockSystemSettings.getDynamicContextSettings.mockResolvedValue(70);

    // Mock the t function for internationalization
    global.t = jest.fn((key) => key);

    // Create a simplified version of ValidateAnswer for testing
    const testValidateAnswer = async (validationPrompt, message, llmInput) => {
      const LLMConnector = getLLMProvider({ provider: "openai" });

      const currentContextPercentage =
        await SystemSettings.getDynamicContextSettings("_VA");
      const boostedContextPercentage = Math.min(
        currentContextPercentage + 5,
        100
      );
      const baseContextWindow = LLMConnector.promptWindowLimit();
      const boostedContextWindow = Math.floor(
        baseContextWindow * (boostedContextPercentage / 100)
      );

      const messageContent = `<user-question-sources-header>\n${JSON.stringify(llmInput || "not-provided")}\n</user-question-sources-header>\n\n<system-response-header>\n${message}\n</system-response-header>`;

      await LLMConnector.compressMessages({
        systemPrompt: validationPrompt,
        userPrompt: messageContent,
        maxAllowedTokens: boostedContextWindow,
      });

      return {
        boostedContextWindow,
        currentContextPercentage,
        boostedContextPercentage,
      };
    };

    const result = await testValidateAnswer("Test prompt", "Test message", {
      test: "data",
    });

    expect(result.currentContextPercentage).toBe(70);
    expect(result.boostedContextPercentage).toBe(75);
    expect(result.boostedContextWindow).toBe(75000);
    expect(mockLLMConnector.compressMessages).toHaveBeenCalledWith({
      systemPrompt: "Test prompt",
      userPrompt: expect.stringContaining("Test message"),
      maxAllowedTokens: 75000,
    });
  });
});
