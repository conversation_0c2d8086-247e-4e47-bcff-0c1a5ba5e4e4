/* eslint-env jest */

import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import DocumentBuilder from "../index";
import System from "@/models/system";
import showToast from "@/utils/toast";

// Mock dependencies
jest.mock("@/models/system", () => ({
  getDocumentBuilderPrompts: jest.fn(),
  updateDocumentBuilderPrompts: jest.fn(),
  generateLegalTaskPrompt: jest.fn(),
}));
jest.mock("@/utils/toast");
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key, fallback) => {
      // A more specific mock for keys used in this component
      if (key === "document-builder.save") return "Save Prompt Settings";
      if (key === "document-builder.saving") return "Saving...";
      if (key === "document-builder.toast-success")
        return "Document builder prompts saved successfully.";
      if (key === "document-builder.toast-fail")
        return "Failed to save document builder prompts.";
      if (key === "document_builder_page.toast-fail-load-prompts")
        return "Failed to load prompt configurations.";
      if (key === "document-builder.override-prompt-placeholder")
        return "Enter prompt to override...";
      if (key.startsWith("document-builder.sections.")) return key; // Return key for section titles/desc if not critical

      // Added for dynamic group titles in tests
      if (key === "document-builder.prompts.group.prompt_group_1.title")
        return "Prompt Group 1 Prompts";
      if (key === "document-builder.prompts.group.prompt_group_1.description")
        return "Description for Prompt Group 1.";
      if (key === "document-builder.prompts.group.prompt_group_2_single.title")
        return "Prompt Group 2 Single Prompts";
      if (
        key ===
        "document-builder.prompts.group.prompt_group_2_single.description"
      )
        return "Description for Prompt Group 2 Single.";

      return fallback || key;
    },
  }),
}));

// Mock child components that are not relevant to the core logic being tested
jest.mock(
  "@/components/SettingsSidebar",
  () =>
    function MockSettingsSidebar() {
      return <div data-testid="mock-sidebar">Sidebar</div>;
    }
);
jest.mock(
  "@/components/HeaderWorkspace",
  () =>
    function MockHeaderWorkspace() {
      return <div data-testid="mock-header">Header</div>;
    }
);
jest.mock(
  "../LegalTasksTable",
  () =>
    function MockLegalTasksTable() {
      return <div data-testid="mock-legal-tasks-table">LegalTasksTable</div>;
    }
);
jest.mock(
  "@/components/Modals/CreateNewLegalTask",
  () =>
    function MockCreateNewLegalTask() {
      return <div data-testid="mock-create-task-modal">CreateNewLegalTask</div>;
    }
);

jest.mock("react-router-dom", () => {
  const ActualReactRouterDom = jest.requireActual("react-router-dom");
  const MockReactRouterDomLink = ({ children, to }) => (
    <a href={to}>{children}</a>
  );
  MockReactRouterDomLink.displayName = "MockReactRouterDomLink";
  return {
    ...ActualReactRouterDom,
    Link: MockReactRouterDomLink,
  };
});

const mockPromptsData = [
  {
    promptKey: "PROMPT_GROUP_1",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.prompt_group_1.title",
    defaultContent: "Prompt Group 1 Prompts", // Default English
  },
  {
    promptKey: "PROMPT_GROUP_1",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.prompt_group_1.description",
    defaultContent: "Description for Prompt Group 1.", // Default English
  },
  {
    promptKey: "PROMPT_GROUP_1",
    promptField: "SYSTEM_PROMPT",
    label: "Group 1 System Prompt Label",
    defaultContent: "Default G1 System Placeholder",
    systemSettingName: "g1_system_prompt",
    description: "Description for G1 System.",
    currentValue: "",
  },
  {
    promptKey: "PROMPT_GROUP_1",
    promptField: "USER_PROMPT",
    label: "Group 1 User Prompt Label",
    defaultContent: "Default G1 User Placeholder",
    systemSettingName: "g1_user_prompt",
    description: "Description for G1 User.",
    currentValue: "Custom G1 User Value",
  },
  {
    promptKey: "PROMPT_GROUP_2_SINGLE",
    promptField: "GROUP_TITLE",
    label: "document-builder.prompts.group.prompt_group_2_single.title",
    defaultContent: "Prompt Group 2 Single Prompts", // Default English
  },
  {
    promptKey: "PROMPT_GROUP_2_SINGLE",
    promptField: "GROUP_DESCRIPTION",
    label: "document-builder.prompts.group.prompt_group_2_single.description",
    defaultContent: "Description for Prompt Group 2 Single.", // Default English
  },
  {
    promptKey: "PROMPT_GROUP_2_SINGLE",
    promptField: "PROMPT_TEMPLATE",
    label: "Group 2 Template Prompt Label",
    defaultContent: "Default G2 Template Placeholder",
    systemSettingName: "g2_template_prompt",
    description: "Description for G2 Template.",
    currentValue: "",
  },
];

// Mock localStorage for AUTH_TOKEN used in fetchCDBDocumentation
beforeAll(() => {
  Storage.prototype.getItem = jest.fn((key) => {
    if (key === "AUTH_TOKEN") return "test-auth-token";
    return null;
  });
});

afterAll(() => {
  Storage.prototype.getItem.mockRestore();
});

describe("DocumentBuilder Page - Integration Tests", () => {
  beforeEach(() => {
    System.getDocumentBuilderPrompts.mockReset();
    System.updateDocumentBuilderPrompts.mockReset();
    System.generateLegalTaskPrompt.mockReset();
    System.generateLegalTaskPrompt.mockResolvedValue({
      prompt: "suggested prompt",
    });
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true, // Ensure fetch mock returns ok: true for successful parsing
        json: () =>
          Promise.resolve({ success: true, documentation: "Mock CDB Docs" }),
      })
    );
    showToast.mockClear();
  });

  test("loads and displays dynamic prompt fields correctly", async () => {
    System.getDocumentBuilderPrompts.mockResolvedValue(mockPromptsData);
    render(<DocumentBuilder />);

    await waitFor(() => {
      expect(System.getDocumentBuilderPrompts).toHaveBeenCalledTimes(1);
    });

    // Check for Group 1 elements
    expect(screen.getByText("Prompt Group 1 Prompts")).toBeInTheDocument();
    expect(
      screen.getByLabelText("Group 1 System Prompt Label")
    ).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText("Default G1 System Placeholder")
    ).toBeInTheDocument();
    const systemPromptDescription = screen.getByText(
      "Description for G1 System.",
      { selector: "p.text-xs" }
    );
    expect(systemPromptDescription).toBeInTheDocument();

    expect(
      screen.getByLabelText("Group 1 User Prompt Label")
    ).toBeInTheDocument();
    const g1UserTextarea = screen.getByPlaceholderText(
      "Default G1 User Placeholder"
    );
    expect(g1UserTextarea).toBeInTheDocument();
    expect(g1UserTextarea).toHaveValue("Custom G1 User Value");
    const userPromptDescription = screen.getByText("Description for G1 User.", {
      selector: "p.text-xs",
    });
    expect(userPromptDescription).toBeInTheDocument();

    // Check for Group 2 elements
    expect(
      screen.getByText("Prompt Group 2 Single Prompts")
    ).toBeInTheDocument();
    expect(
      screen.getByLabelText("Group 2 Template Prompt Label")
    ).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText("Default G2 Template Placeholder")
    ).toBeInTheDocument();
    const templatePromptDescription = screen.getByText(
      "Description for G2 Template.",
      { selector: "p.text-xs" }
    );
    expect(templatePromptDescription).toBeInTheDocument();
  });

  test("handles error when fetching prompt configurations", async () => {
    System.getDocumentBuilderPrompts.mockRejectedValue(
      new Error("API Fetch Failed")
    );
    render(<DocumentBuilder />);

    await waitFor(() => {
      // Use the specific key used in the component for this toast
      expect(showToast).toHaveBeenCalledWith(
        "Failed to load prompt configurations.",
        "error"
      );
    });
  });

  test("allows editing and saving prompts", async () => {
    System.getDocumentBuilderPrompts.mockResolvedValue(mockPromptsData);
    System.updateDocumentBuilderPrompts.mockResolvedValue({ success: true });

    render(<DocumentBuilder />);
    await waitFor(() =>
      expect(System.getDocumentBuilderPrompts).toHaveBeenCalled()
    );

    const g1SystemTextarea = screen.getByPlaceholderText(
      "Default G1 System Placeholder"
    );
    fireEvent.change(g1SystemTextarea, {
      target: { value: "New G1 System Content" },
    });
    expect(g1SystemTextarea).toHaveValue("New G1 System Content");

    const saveButton = await screen.findByRole("button", {
      name: /Save Prompt Settings/i,
    });
    expect(saveButton).toBeInTheDocument();

    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(System.updateDocumentBuilderPrompts).toHaveBeenCalledTimes(1);
      expect(System.updateDocumentBuilderPrompts).toHaveBeenCalledWith({
        g1_system_prompt: "New G1 System Content",
        g1_user_prompt: "Custom G1 User Value",
        g2_template_prompt: "",
      });
    });

    expect(showToast).toHaveBeenCalledWith(
      "Document builder prompts saved successfully.",
      "success"
    );
  });

  test("handles error when saving prompts", async () => {
    System.getDocumentBuilderPrompts.mockResolvedValue(mockPromptsData);
    System.updateDocumentBuilderPrompts.mockResolvedValue({
      success: false,
      error: "DB Save Failed",
    });

    render(<DocumentBuilder />);
    await waitFor(() =>
      expect(System.getDocumentBuilderPrompts).toHaveBeenCalled()
    );

    const g1SystemTextarea = screen.getByPlaceholderText(
      "Default G1 System Placeholder"
    );
    fireEvent.change(g1SystemTextarea, {
      target: { value: "Attempt to save" },
    });

    const saveButton = await screen.findByRole("button", {
      name: /Save Prompt Settings/i,
    });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(System.updateDocumentBuilderPrompts).toHaveBeenCalledTimes(1);
    });
    expect(showToast).toHaveBeenCalledWith(
      "Failed to save document builder prompts.",
      "error"
    );
  });
});
