export default {
  // =========================
  // PRIVACY & DATA-HANDLING
  // =========================
  privacy: {
    title: "Datenschutz & Datenverarbeitung",
    description:
      "Dies ist deine Konfiguration, wie verbundene Drittanbieter und unsere Plattform mit deinen Daten umgehen.",
    llm: "LLM-Auswahl",
    embedding: "Embedding-Einstellung",
    vector: "Vektor-Datenbank",
    anonymous: "Anonyme Telemetrie aktiviert",
    "desc-event": "Alle Ereignisse speichern keine IP-Adresse und enthalten",
    "desc-id": "keine identifizierenden",
    "desc-cont":
      "Inhalte, Einstellungen, Chats oder andere nicht nutzungsbezogene Informationen. Die Liste der gesammelten Ereignis-Tags findest du auf",
    "desc-git": "Github hier",
    "desc-end":
      "Solltest du die Telemetrie deaktivieren, bitten wir lediglich um Feedback, damit wir die Plattform weiter verbessern können.",
  },
  // =========================
  // LLM SELECTION PRIVACY
  // =========================
  "llm-selection-privacy": {
    openai: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für OpenAI sichtbar",
      ],
    },
    azure: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Texte und Embedding-Texte sind für OpenAI oder Microsoft nicht sichtbar",
      ],
    },
    anthropic: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für Anthropic sichtbar",
      ],
    },
    gemini: {
      description: [
        "Deine Chats werden anonymisiert und für Trainingszwecke genutzt",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für Google sichtbar",
      ],
    },
    lmstudio: {
      description: [
        "Dein Modell und deine Chats sind nur auf dem Server zugänglich, auf dem LMStudio läuft",
      ],
    },
    localai: {
      description: [
        "Dein Modell und deine Chats sind nur auf dem Server zugänglich, auf dem LocalAI läuft",
      ],
    },
    ollama: {
      description: [
        "Dein Modell und deine Chats sind nur auf dem Rechner zugänglich, auf dem Ollama läuft",
      ],
    },
    native: {
      description: [
        "Dein Modell und deine Chats sind nur in dieser Instanz zugänglich",
      ],
    },
    togetherai: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für TogetherAI sichtbar",
      ],
    },
    mistral: {
      description: [
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für Mistral sichtbar",
      ],
    },
    huggingface: {
      description: [
        "Deine Prompts und Dokumenttexte werden an deinen HuggingFace-Endpunkt gesendet",
      ],
    },
    perplexity: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für Perplexity AI sichtbar",
      ],
    },
    openrouter: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für OpenRouter sichtbar",
      ],
    },
    groq: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für Groq sichtbar",
      ],
    },
    koboldcpp: {
      description: [
        "Dein Modell und deine Chats sind nur auf dem Server zugänglich, auf dem KoboldCPP läuft",
      ],
    },
    textgenwebui: {
      description: [
        "Dein Modell und deine Chats sind nur auf dem Server zugänglich, auf dem Oobabooga Text Generation Web UI läuft",
      ],
    },
    "generic-openai": {
      description: [
        "Daten werden gemäß den Nutzungsbedingungen deines generischen Endpunkt-Anbieters geteilt.",
      ],
    },
    cohere: {
      description: [
        "Daten werden gemäß den Nutzungsbedingungen von cohere.com und den Datenschutzgesetzen deines Standorts geteilt.",
      ],
    },
    litellm: {
      description: [
        "Dein Modell und deine Chats sind nur auf dem Server zugänglich, auf dem LiteLLM läuft",
      ],
    },
  },

  // =========================
  // VECTOR DATABASE PRIVACY
  // =========================
  "vector-db-privacy": {
    chroma: {
      description: [
        "Deine Vektoren und Dokumenttexte werden auf deiner Chroma-Instanz gespeichert",
        "Der Zugriff auf deine Instanz wird von dir verwaltet",
      ],
    },
    pinecone: {
      description: [
        "Deine Vektoren und Dokumenttexte werden auf den Servern von Pinecone gespeichert",
        "Der Zugriff auf deine Daten wird von Pinecone verwaltet",
      ],
    },
    qdrant: {
      description: [
        "Deine Vektoren und Dokumenttexte werden in deiner Qdrant-Instanz (Cloud oder self–hosted) gespeichert",
      ],
    },
    weaviate: {
      description: [
        "Deine Vektoren und Dokumenttexte werden in deiner Weaviate-Instanz (Cloud oder self–hosted) gespeichert",
      ],
    },
    milvus: {
      description: [
        "Deine Vektoren und Dokumenttexte werden in deiner Milvus-Instanz (Cloud oder self–hosted) gespeichert",
      ],
    },
    zilliz: {
      description: [
        "Deine Vektoren und Dokumenttexte werden in deinem Zilliz-Cloud-Cluster gespeichert.",
      ],
    },
    astra: {
      description: [
        "Deine Vektoren und Dokumenttexte werden in deiner cloudbasierten AstraDB-Datenbank gespeichert.",
      ],
    },
    lancedb: {
      description: [
        "Deine Vektoren und Dokumenttexte werden privat in dieser Instanz der Plattform gespeichert",
      ],
    },
  },

  // =========================
  // EMBEDDING ENGINE PRIVACY
  // =========================
  "embedding-engine-privacy": {
    native: {
      description: [
        "Dein Dokumenttext wird privat in dieser Instanz der Plattform eingebettet",
      ],
    },
    openai: {
      description: [
        "Dein Dokumenttext wird an OpenAI-Server gesendet",
        "Deine Dokumente werden nicht für Trainingszwecke genutzt",
      ],
    },
    azure: {
      description: [
        "Dein Dokumenttext wird an deinen Microsoft Azure-Dienst gesendet",
        "Deine Dokumente werden nicht für Trainingszwecke genutzt",
      ],
    },
    localai: {
      description: [
        "Dein Dokumenttext wird privat auf dem Server eingebettet, auf dem LocalAI läuft",
      ],
    },
    ollama: {
      description: [
        "Dein Dokumenttext wird privat auf dem Server eingebettet, auf dem Ollama läuft",
      ],
    },
    lmstudio: {
      description: [
        "Dein Dokumenttext wird privat auf dem Server eingebettet, auf dem LMStudio läuft",
      ],
    },
    cohere: {
      description: [
        "Daten werden gemäß den Nutzungsbedingungen von cohere.com und den Datenschutzgesetzen deines Standorts geteilt.",
      ],
    },
    voyageai: {
      description: [
        "Daten, die an die Server von Voyage AI gesendet werden, werden gemäß den Nutzungsbedingungen von voyageai.com geteilt.",
      ],
    },
  },
};
