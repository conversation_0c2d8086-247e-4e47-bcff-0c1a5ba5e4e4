export default {
  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Registrera ärendenummer",
    "project-id": "Ärendenummer",
    "resource-id": "Resurs-ID",
    "activity-id": "Aktivitets-ID",
    register: "Registrera ärende",
    "invoice-text": "Foynet antal slagningar",
    registering: "registrerar ...",
    "not-active": "Detta ärende är inte aktivt för registrering",
    account: {
      title: "<PERSON>gga in på Rexor",
      username: "<PERSON>v<PERSON><PERSON>rnam<PERSON>",
      password: "<PERSON>ösenord",
      "no-token":
        "Inloggning misslyckades (Ingen token mottagen i handleLoginSuccess)",
      logout: "Logga ut",
      "no-user": "Vänligen logga in först",
      connected: "Ansluten till Rexor",
      "not-connected": "Inte ansluten",
      "change-account": "Byt konto",
      "session-expired": "Sessionen har gått ut. Logga in igen.",
    },
    "hide-article-transaction": "Dölj formulär för artikel transaktion",
    "show-article-transaction": "Visa formulär för artikel transaktion",
    "article-transaction-title": "Lägg till artikel transaktion",
    "registration-date": "Registreringsdatum",
    description: "Beskrivning",
    "description-internal": "Intern beskrivning",
    "hours-worked": "Arbetade timmar",
    "invoiced-hours": "Fakturerade timmar",
    invoiceable: "Fakturerbar",
    "sending-article-transaction": "Skickar artikel transaktion...",
    "save-article-transaction": "Spara artikel transaktion",
    "project-not-register": "Projektet måste registreras först.",
    "article-transaction-error":
      "Misslyckades att registrera artikel transaktion",
    "not-exist": "Detta ärende kunde inte hittas",
    "missing-economy-id-admin":
      "Vänligen lägg till ett ekonomi-ID till din användarprofil innan du registrerar ett projekt.",
    "missing-economy-id-user":
      "Vänligen be systemadministratören att lägga till ett ekonomi-ID till din användarprofil för att kunna registrera ett projekt.",
    "api-settings": {
      title: "Rexor API-konfiguration",
      description:
        "Konfigurera anpassade Rexor API-endpoints och autentiseringsuppgifter. Lämna tomt för att använda standardvärden.",
      "loading-message": "Laddar Rexor API-inställningar...",
      "api-base-url": "API Bas-URL",
      "api-base-url-placeholder": "https://api.rexor.se/v231/Api",
      "auth-url": "Autentiserings-URL",
      "auth-url-placeholder": "https://auth.rexor.se/v231/Token",
      "client-id-dev": "Utvecklings-klient-ID",
      "client-id-dev-placeholder": "testfoyen",
      "client-id-prod": "Produktions-klient-ID",
      "client-id-prod-placeholder": "foyen",
      "api-host": "API-värd",
      "api-host-placeholder": "api.rexor.se",
      "save-button": "Spara inställningar",
      "reset-button": "Återställ till standard",
      "success-message": "Rexor API-inställningar sparades framgångsrikt",
      "error-message": "Misslyckades att spara Rexor API-inställningar",
    },
  },
};
