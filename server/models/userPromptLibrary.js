const prisma = require("../utils/prisma");

const UserPromptLibrary = {
  writable: ["name", "prompt_text", "description"],

  validations: {
    name: (value) => {
      if (!value || typeof value !== "string") {
        throw new Error("Name is required and must be a string");
      }
      const trimmed = value.trim();
      if (trimmed.length === 0) {
        throw new Error("Name cannot be empty");
      }
      if (trimmed.length > 100) {
        throw new Error("Name cannot exceed 100 characters");
      }
      return trimmed;
    },

    prompt_text: (value) => {
      if (!value || typeof value !== "string") {
        throw new Error("Prompt text is required and must be a string");
      }
      const trimmed = value.trim();
      if (trimmed.length === 0) {
        throw new Error("Prompt text cannot be empty");
      }
      if (trimmed.length > 10000) {
        throw new Error("Prompt text cannot exceed 10,000 characters");
      }
      return trimmed;
    },

    description: (value) => {
      if (value === null || value === undefined) return null;
      if (typeof value !== "string") {
        throw new Error("Description must be a string");
      }
      const trimmed = value.trim();
      if (trimmed.length === 0) return null;
      if (trimmed.length > 500) {
        throw new Error("Description cannot exceed 500 characters");
      }
      return trimmed;
    },
  },

  /**
   * Get all prompt libraries for a user
   * @param {number} userId - The user ID
   * @returns {Promise<Array>} Array of prompt libraries
   */
  getByUserId: async function (userId) {
    try {
      const prompts = await prisma.user_prompt_libraries.findMany({
        where: { user_id: userId },
        orderBy: { created_at: "desc" },
      });
      return prompts;
    } catch (error) {
      console.error("Error fetching user prompt libraries:", error);
      throw error;
    }
  },

  /**
   * Get a specific prompt library by ID and user ID
   * @param {number} id - The prompt library ID
   * @param {number} userId - The user ID
   * @returns {Promise<Object|null>} The prompt library or null if not found
   */
  getByIdAndUserId: async function (id, userId) {
    try {
      const prompt = await prisma.user_prompt_libraries.findFirst({
        where: {
          id: parseInt(id),
          user_id: userId,
        },
      });
      return prompt;
    } catch (error) {
      console.error("Error fetching prompt library by ID:", error);
      throw error;
    }
  },

  /**
   * Create a new prompt library
   * @param {number} userId - The user ID
   * @param {Object} data - The prompt library data
   * @returns {Promise<Object>} The created prompt library
   */
  create: async function (userId, data) {
    try {
      // Validate input data
      const validatedData = {
        name: this.validations.name(data.name),
        prompt_text: this.validations.prompt_text(data.prompt_text),
        description: this.validations.description(data.description),
      };

      const prompt = await prisma.user_prompt_libraries.create({
        data: {
          user_id: userId,
          ...validatedData,
        },
      });

      return prompt;
    } catch (error) {
      if (error.code === "P2002") {
        throw new Error("A prompt with this name already exists");
      }
      console.error("Error creating prompt library:", error);
      throw error;
    }
  },

  /**
   * Update an existing prompt library
   * @param {number} id - The prompt library ID
   * @param {number} userId - The user ID
   * @param {Object} data - The updated data
   * @returns {Promise<Object|null>} The updated prompt library or null if not found
   */
  update: async function (id, userId, data) {
    try {
      // First check if the prompt exists and belongs to the user
      const existingPrompt = await this.getByIdAndUserId(id, userId);
      if (!existingPrompt) {
        return null;
      }

      // Validate input data
      const validatedData = {};
      if (data.name !== undefined) {
        validatedData.name = this.validations.name(data.name);
      }
      if (data.prompt_text !== undefined) {
        validatedData.prompt_text = this.validations.prompt_text(
          data.prompt_text
        );
      }
      if (data.description !== undefined) {
        validatedData.description = this.validations.description(
          data.description
        );
      }

      const updatedPrompt = await prisma.user_prompt_libraries.update({
        where: { id: parseInt(id) },
        data: validatedData,
      });

      return updatedPrompt;
    } catch (error) {
      if (error.code === "P2002") {
        throw new Error("A prompt with this name already exists");
      }
      console.error("Error updating prompt library:", error);
      throw error;
    }
  },

  /**
   * Delete a prompt library
   * @param {number} id - The prompt library ID
   * @param {number} userId - The user ID
   * @returns {Promise<boolean>} True if deleted, false if not found
   */
  delete: async function (id, userId) {
    try {
      // First check if the prompt exists and belongs to the user
      const existingPrompt = await this.getByIdAndUserId(id, userId);
      if (!existingPrompt) {
        return false;
      }

      await prisma.user_prompt_libraries.delete({
        where: { id: parseInt(id) },
      });

      return true;
    } catch (error) {
      console.error("Error deleting prompt library:", error);
      throw error;
    }
  },

  /**
   * Check if a prompt name exists for a user
   * @param {number} userId - The user ID
   * @param {string} name - The prompt name
   * @param {number} excludeId - Optional ID to exclude from the check (for updates)
   * @returns {Promise<boolean>} True if name exists, false otherwise
   */
  nameExists: async function (userId, name, excludeId = null) {
    try {
      const where = {
        user_id: userId,
        name: name.trim(),
      };

      if (excludeId) {
        where.id = { not: parseInt(excludeId) };
      }

      const existing = await prisma.user_prompt_libraries.findFirst({ where });
      return !!existing;
    } catch (error) {
      console.error("Error checking prompt name existence:", error);
      throw error;
    }
  },
};

module.exports = { UserPromptLibrary };
