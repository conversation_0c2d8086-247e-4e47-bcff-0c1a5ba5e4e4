# State Management

## Rule Type: Always

For Zustand state management:

- Split large stores into domain-specific stores
- Use hook pattern in components instead of getState()
- Prefer function-based state updates
- Extract complex logic to utility functions
- Use Zustand's selector pattern for better performance

## Rule Type: Always

Use centralized stores for domain-specific data:

- Legal tasks should use the legalTasksStore for all operations
- System.js methods should use the centralized stores internally
- Components should access data through the appropriate store

## Rule Type: Always

When implementing new features that require state management:

- Create a dedicated store if the feature has complex state
- Use existing stores if the feature relates to an existing domain
- Ensure all API calls update the centralized store
- Provide helper methods in the store for common operations
