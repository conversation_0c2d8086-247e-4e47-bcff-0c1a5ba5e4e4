export default {
  // =========================
  // PRIVACY & DATA-HANDLING
  // =========================
  privacy: {
    title: "Prywatność i zarządzanie danymi",
    description:
      "To jest Twoja konfiguracja dotycząca sposobu, w jaki podłączone zewnętrzne usługi i nasza platforma zarządzają Twoimi danymi.",
    llm: "Wybór LLM",
    embedding: "Preferencje osadzania",
    vector: "Baza danych wektorowych",
    anonymous: "Włączona anonimowa telemetria",
    "desc-event": "Wszystkie zdarzenia nie rejestrują adresu IP i zawierają",
    "desc-id": "bez danych identyfikacyjnych",
    "desc-cont":
      "treści, ustawień, czatów lub innych informacji nieopartych na użyciu. Aby zobaczyć listę zbieranych tagów zdarzeń, możesz zajrzeć na",
    "desc-git": "Github tutaj",
    "desc-end":
      "Jesteśmy zobowiązani do przejrzystości i kontroli w zakresie Twoich danych osobowych. Jeśli zdecydujesz się wyłączyć telemetrię, prosimy jedynie o przesłanie nam opinii i sugestii, abyśmy mogli dalej ulepszać platformę.",
  },
  // =========================
  // LLM SELECTION PRIVACY
  // =========================
  "llm-selection-privacy": {
    openai: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla OpenAI",
      ],
    },
    azure: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twój tekst i tekst osadzania nie są widoczne dla OpenAI ani Microsoft",
      ],
    },
    anthropic: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla Anthropic",
      ],
    },
    gemini: {
      description: [
        "Twoje czaty są anonimizowane i wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla Google",
      ],
    },
    lmstudio: {
      description: [
        "Twój model i czaty są dostępne tylko na serwerze, na którym działa LMStudio",
      ],
    },
    localai: {
      description: [
        "Twój model i czaty są dostępne tylko na serwerze, na którym działa LocalAI",
      ],
    },
    ollama: {
      description: [
        "Twój model i czaty są dostępne tylko na maszynie, na której działają modele Ollama",
      ],
    },
    native: {
      description: ["Twój model i czaty są dostępne tylko w tej instancji"],
    },
    togetherai: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla TogetherAI",
      ],
    },
    mistral: {
      description: [
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla Mistral",
      ],
    },
    huggingface: {
      description: [
        "Twoje zapytania i tekst dokumentów używane przy odpowiedziach są wysyłane do zarządzanego punktu końcowego HuggingFace",
      ],
    },
    perplexity: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla Perplexity AI",
      ],
    },
    openrouter: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla OpenRouter",
      ],
    },
    groq: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla Groq",
      ],
    },
    koboldcpp: {
      description: [
        "Twój model i czaty są dostępne tylko na serwerze, na którym działa KoboldCPP",
      ],
    },
    textgenwebui: {
      description: [
        "Twój model i czaty są dostępne tylko na serwerze, na którym działa interfejs Text Generation Web UI Oobabooga",
      ],
    },
    "generic-openai": {
      description: [
        "Dane są udostępniane zgodnie z warunkami usługi obowiązującymi u Twojego dostawcy ogólnego punktu końcowego.",
      ],
    },
    cohere: {
      description: [
        "Dane są udostępniane zgodnie z warunkami usługi cohere.com oraz lokalnymi przepisami o ochronie prywatności.",
      ],
    },
    litellm: {
      description: [
        "Twój model i czaty są dostępne tylko na serwerze, na którym działa LiteLLM",
      ],
    },
  },

  // =========================
  // VECTOR DATABASE PRIVACY
  // =========================
  "vector-db-privacy": {
    chroma: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane na Twojej instancji Chroma",
        "Dostęp do Twojej instancji jest zarządzany przez Ciebie",
      ],
    },
    pinecone: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane na serwerach Pinecone",
        "Dostęp do Twoich danych jest zarządzany przez Pinecone",
      ],
    },
    qdrant: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane na Twojej instancji Qdrant (w chmurze lub samodzielnie hostowanej)",
      ],
    },
    weaviate: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane na Twojej instancji Weaviate (w chmurze lub samodzielnie hostowanej)",
      ],
    },
    milvus: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane na Twojej instancji Milvus (w chmurze lub samodzielnie hostowanej)",
      ],
    },
    zilliz: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane w Twoim klastrze chmurowym Zilliz.",
      ],
    },
    astra: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane w Twojej bazie danych AstraDB w chmurze.",
      ],
    },
    lancedb: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane prywatnie w tej instancji platformy",
      ],
    },
  },

  // =========================
  // EMBEDDING ENGINE PRIVACY
  // =========================
  "embedding-engine-privacy": {
    native: {
      description: [
        "Tekst Twoich dokumentów jest osadzany prywatnie w tej instancji platformy",
      ],
    },
    openai: {
      description: [
        "Tekst Twoich dokumentów jest wysyłany do serwerów OpenAI",
        "Twoje dokumenty nie są używane do treningu",
      ],
    },
    azure: {
      description: [
        "Tekst Twoich dokumentów jest wysyłany do Twojej usługi Microsoft Azure",
        "Twoje dokumenty nie są używane do treningu",
      ],
    },
    localai: {
      description: [
        "Tekst Twoich dokumentów jest osadzany prywatnie na serwerze działającym LocalAI",
      ],
    },
    ollama: {
      description: [
        "Tekst Twoich dokumentów jest osadzany prywatnie na serwerze działającym Ollama",
      ],
    },
    lmstudio: {
      description: [
        "Tekst Twoich dokumentów jest osadzany prywatnie na serwerze działającym LMStudio",
      ],
    },
    cohere: {
      description: [
        "Dane są udostępniane zgodnie z warunkami usługi cohere.com oraz lokalnymi przepisami o ochronie prywatności.",
      ],
    },
    voyageai: {
      description: [
        "Dane wysłane do serwerów Voyage AI są udostępniane zgodnie z warunkami usługi voyageai.com.",
      ],
    },
  },
};
