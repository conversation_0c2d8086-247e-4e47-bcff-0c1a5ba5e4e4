export default {
  // =========================
  // EMBEDDER (EMBEDDING PROVIDER NAMES)
  // =========================
  embeder: {
    allm: "Użyj wbudowanego dostawcy osadzania. Zero setup!",
    openai: "Standardowa opcja dla większości niekomercyjnych zastosowań.",
    azure: "Enterprise opcja OpenAI hostowana na usługach Azure.",
    localai: "Uruchom modele osadzania lokalnie na swojej maszynie.",
    ollama: "Uruchom modele osadzania lokalnie na swojej maszynie.",
    lmstudio:
      "Odkryj, pobierz i uruchom tysiące zaawansowanych LLMs w kilku kliknięciach.",
    cohere: "Uruchom potężne modele osadzania z Cohere.",
    voyageai: "Uruchom potężne modele osadzania z Voyage AI.",
    "generic-openai": "Użyj ogólnego modelu osadzania OpenAI.",
    "default.embedder": "Domyślny dostawca osadzania",
    jina: "Modele osadzania tekstu Jina AI dla wielojęzycznych i wysokiej wydajności osadzeń.",
    litellm: "Uruchom potężne modele osadzania z LiteLLM.",
  },
  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Common strings
    "provider-logo": "{{provider}} logo",

    // LMStudio Embedding Options
    lmstudio: {
      "model-label": "Model osadzania LM Studio",
      "max-chunk-length": "Maksymalna długość fragmentu",
      "max-chunk-length-help":
        "Maksymalna długość fragmentów tekstu przy osadzaniu.",
      "hide-endpoint": "Ukryj ręczne wprowadzanie punktu końcowego",
      "show-endpoint": "Pokaż ręczne wprowadzanie punktu końcowego",
      "base-url": "Podstawowy adres URL LM Studio",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help": "Wprowadź adres URL, pod którym działa LM Studio.",
      "auto-detect": "Wykryj automatycznie",
      "loading-models": "--ładowanie dostępnych modeli--",
      "enter-url-first": "Najpierw wprowadź adres URL LM Studio",
      "model-help":
        "Wybierz model LM Studio do osadzania. Modele zostaną załadowane po wprowadzeniu poprawnego adresu URL LM Studio.",
      "loaded-models": "Twoje załadowane modele",
    },
    // Ollama Embedding Options
    ollama: {
      "model-label": "Model osadzania Ollama",
      "max-chunk-length": "Maksymalna długość fragmentu",
      "max-chunk-length-help":
        "Maksymalna długość fragmentów tekstu przy osadzaniu.",
      "hide-endpoint": "Ukryj ręczne wprowadzanie punktu końcowego",
      "show-endpoint": "Pokaż ręczne wprowadzanie punktu końcowego",
      "base-url": "Podstawowy adres URL Ollama",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help": "Wprowadź adres URL, pod którym działa Ollama.",
      "auto-detect": "Wykryj automatycznie",
      "loading-models": "--ładowanie dostępnych modeli--",
      "enter-url-first": "Najpierw wprowadź adres URL Ollama",
      "model-help":
        "Wybierz model Ollama do osadzania. Modele zostaną załadowane po wprowadzeniu poprawnego adresu URL Ollama.",
      "loaded-models": "Twoje załadowane modele",
    },
    // LiteLLM Embedding Options
    litellm: {
      "model-label": "Wybór modelu osadzania",
      "max-chunk-length": "Maksymalna długość fragmentu",
      "max-chunk-length-help":
        "Maksymalna długość fragmentów tekstu przy osadzaniu.",
      "api-key": "Klucz API",
      optional: "opcjonalne",
      "api-key-placeholder": "sk-mysecretkey",
      "loading-models": "-- ładowanie dostępnych modeli --",
      "waiting-url": "-- oczekiwanie na adres URL --",
      "loaded-models": "Twoje załadowane modele",
      "model-tooltip": "Zobacz obsługiwane modele osadzania na",
      "model-tooltip-link": "Dokumentacja LiteLLM",
      "model-tooltip-more":
        "aby uzyskać więcej informacji o dostępnych modelach.",
    },
    // Cohere Embedding Options
    cohere: {
      "api-key": "Klucz API Cohere",
      "api-key-placeholder": "Wprowadź swój klucz API Cohere",
      "model-label": "Wybór modelu",
      "available-models": "Dostępne modele osadzania",
    },
    // Jina Embedding Options
    jina: {
      "api-key": "Klucz API Jina",
      "api-key-format": "Klucz API Jina musi zaczynać się od 'jina_'",
      "api-key-placeholder": "Wprowadź swój klucz API Jina",
      "api-key-error": "Klucz API musi zaczynać się od 'jina_'",
      "model-label": "Wybór modelu",
      "available-models": "Dostępne modele osadzania",
      "embedding-type": "Typ osadzania",
      "available-types": "Dostępne typy osadzania",
      dimensions: "Wymiary",
      "available-dimensions": "Dostępne wymiary",
      task: "Zadanie",
      "available-tasks": "Dostępne zadania",
      "late-chunking": "Późne dzielenie na fragmenty",
      "late-chunking-help":
        "Włącz późne dzielenie na fragmenty przy przetwarzaniu dokumentów",
    },
    // LocalAI Embedding Options
    localai: {
      "model-label": "Nazwa modelu osadzania",
      "hide-endpoint": "Ukryj zaawansowane ustawienia",
      "show-endpoint": "Pokaż zaawansowane ustawienia",
      "base-url": "Podstawowy adres URL LocalAI",
      "base-url-placeholder": "http://localhost:8080/v1",
      "base-url-help": "Wprowadź adres URL, pod którym działa LocalAI.",
      "auto-detect": "Wykryj automatycznie",
      "loading-models": "-- ładowanie dostępnych modeli --",
      "waiting-url": "-- oczekiwanie na adres URL --",
      "loaded-models": "Twoje załadowane modele",
    },
    // Generic OpenAI-Compatible Embedding Options
    generic: {
      "base-url": "Podstawowy adres URL",
      "base-url-placeholder": "https://api.openai.com/v1",
      "base-url-help":
        "Wprowadź bazowy adres URL dla punktu końcowego API kompatybilnego z OpenAI.",
      "model-label": "Model osadzania",
      "model-placeholder": "Wprowadź nazwę modelu (np. text-embedding-ada-002)",
      "model-help": "Określ identyfikator modelu do generowania osadzeń.",
      "api-key": "Klucz API",
      "api-key-placeholder": "sk-mysecretkey",
      "api-key-help": "Wprowadź swój klucz API do uwierzytelniania.",
    },
    // OpenAI Embedding Options
    openai: {
      "api-key": "Klucz API OpenAI",
      "api-key-placeholder": "Wprowadź swój klucz API OpenAI",
      "model-label": "Wybór modelu",
      "available-models": "Dostępne modele osadzania",
    },
    // VoyageAI Embedding Options
    voyageai: {
      "api-key": "Klucz API VoyageAI",
      "api-key-placeholder": "Wprowadź swój klucz API VoyageAI",
      "model-label": "Wybór modelu",
      "available-models": "Dostępne modele osadzania",
    },
    // Azure OpenAI Embedding Options
    azureai: {
      "service-endpoint": "Punkt końcowy usługi Azure OpenAI",
      "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
      "service-endpoint-help":
        "Wprowadź adres URL punktu końcowego usługi Azure OpenAI",
      "api-key": "Klucz API Azure OpenAI",
      "api-key-placeholder": "Wprowadź swój klucz API Azure OpenAI",
      "api-key-help":
        "Wprowadź swój klucz API Azure OpenAI do uwierzytelniania",
      "deployment-name": "Nazwa wdrożenia modelu osadzania",
      "deployment-name-placeholder":
        "Wprowadź nazwę wdrożenia modelu osadzania Azure OpenAI",
      "deployment-name-help":
        "Nazwa wdrożenia twojego modelu osadzania Azure OpenAI",
    },
    // Native Embedding Options
    native: {
      description:
        "Używanie natywnego dostawcy osadzania do przetwarzania tekstu",
    },
  },
  // =========================
  // JINA EMBEDDING
  // =========================
  jina: {
    "api-key": "Klucz API Jina",
    "api-key-placeholder": "Wprowadź swój klucz API Jina",
    "api-key-format": "Klucz API Jina musi zaczynać się od 'jina_'",
    "model-preference": "Preferencje modelu",
  },

  // =========================
  // OLLAMA EMBEDDING
  // =========================
  ollama: {
    "max-embedding-chunk-length": "Maksymalna długość fragmentu osadzania",
  },

  // =========================
  // VOYAGEAI EMBEDDING
  // =========================
  voyageai: {
    "api-key": "Klucz API VoyageAI",
    "api-key-placeholder": "Wprowadź swój klucz API VoyageAI",
    "model-preference": "Preferencje modelu",
  },
};
