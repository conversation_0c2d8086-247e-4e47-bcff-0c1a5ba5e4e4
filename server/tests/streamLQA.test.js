const { streamChatWithWorkspaceLQA } = require("../utils/chats/streamLQA");
const { SystemSettings } = require("../models/systemSettings");
const { WorkspaceChats } = require("../models/workspaceChats");
const { DocumentManager } = require("../utils/DocumentManager");
// const vectorDbProviders = require("../utils/vectorDbProviders"); // Not directly used in test like this
const helpers = require("../utils/helpers"); // Corrected import
const { TokenManager } = require("../utils/helpers/tiktoken");

// Mock dependencies
jest.mock("../models/systemSettings");
jest.mock("../models/workspaceChats");
jest.mock("../utils/DocumentManager");

// Mock the helpers module that exports getLLMProvider and getVectorDbClass
jest.mock("../utils/helpers", () => ({
  ...jest.requireActual("../utils/helpers"), // Import and retain other exports
  getLLMProvider: jest.fn(),
  getVectorDbClass: jest.fn(),
}));

jest.mock("../utils/helpers/tiktoken");

// Mock the specific vector DB provider that will be loaded by the actual getVectorDbClass
jest.mock(
  "../utils/vectorDbProviders/lance",
  () => ({
    LanceDb: {
      hasNamespace: jest.fn().mockResolvedValue(true),
      namespaceCount: jest.fn().mockResolvedValue(10),
      performSimilaritySearch: jest
        .fn()
        .mockResolvedValue({ contextTexts: [], sources: [] }),
      getAdjacentVectors: jest
        .fn()
        .mockResolvedValue({ previous: [], next: [], totalTokens: 0 }),
    },
  }),
  { virtual: true }
);

// Mock the entire 'express' module for request/response objects if needed
// jest.mock('express', () => ({
//   request: {},
//   response: {
//     writeHead: jest.fn().mockReturnThis(),
//     write: jest.fn().mockReturnThis(),
//     end: jest.fn().mockReturnThis(),
//   },
// }));

describe("streamChatWithWorkspaceLQA - LLM Selection Logic", () => {
  let mockRequest;
  let mockResponse;
  let mockWorkspace;
  let mockUser;
  let mockThread;
  let mockLLMInstance;

  beforeEach(() => {
    jest.clearAllMocks();
    process.env.VECTOR_DB = "lancedb";

    // Explicitly make model methods jest.fn() before setting their mock behavior
    // This can help if the automatic jest.mock isn't fully fleshing out the object's methods as mocks
    SystemSettings.currentSettings = jest.fn();
    SystemSettings.getDynamicContextSettings = jest.fn();
    SystemSettings.getDeepSearchSettings = jest.fn();
    SystemSettings.getPdrSettings = jest.fn();
    SystemSettings.isPromptOutputLogging = jest.fn();

    WorkspaceChats.new = jest.fn();
    WorkspaceChats.get = jest.fn();
    WorkspaceChats.update = jest.fn();
    // Add any other WorkspaceChats methods if they are called by the SUT and need mocking.

    mockLLMInstance = {
      constructor: { name: "TestLLM" },
      streamingEnabled: jest.fn().mockReturnValue(true),
      promptWindowLimit: jest.fn().mockReturnValue(4096),
      customPromptWindowLimit: jest.fn().mockReturnValue(4096),
      compressMessages: jest.fn().mockResolvedValue([]),
      getChatCompletion: jest
        .fn()
        .mockResolvedValue({ textResponse: "Test completion", metrics: {} }),
      streamGetChatCompletion: jest.fn().mockResolvedValue({ metrics: {} }),
      handleStream: jest.fn().mockResolvedValue("Test stream completion"),
      defaultTemp: 0.7,
    };

    helpers.getLLMProvider.mockReturnValue(mockLLMInstance);
    helpers.getVectorDbClass.mockReturnValue({
      hasNamespace: jest.fn().mockResolvedValue(true),
      namespaceCount: jest.fn().mockResolvedValue(10),
      performSimilaritySearch: jest
        .fn()
        .mockResolvedValue({ contextTexts: [], sources: [] }),
      getAdjacentVectors: jest
        .fn()
        .mockResolvedValue({ previous: [], next: [], totalTokens: 0 }),
    });

    // Now that they are jest.fn(), we can set their resolved values for general cases if needed here,
    // but it's better to set them specifically in each test as we started doing.
    WorkspaceChats.new.mockResolvedValue({ chat: { id: "new-chat-id" } });
    WorkspaceChats.get.mockResolvedValue({
      id: "existing-chat-id",
      response: JSON.stringify({ text: "" }),
    });
    WorkspaceChats.update.mockResolvedValue({});

    DocumentManager.prototype.pinnedDocs = jest.fn().mockResolvedValue([]);
    DocumentManager.prototype.pdrDocs = jest.fn().mockResolvedValue([]);

    TokenManager.mockImplementation(() => ({
      countFromString: jest.fn().mockReturnValue(10),
    }));

    mockRequest = {
      on: jest.fn(),
      off: jest.fn(),
      removeListener: jest.fn(),
    };
    mockResponse = {
      writeHead: jest.fn().mockReturnThis(),
      write: jest.fn(),
      end: jest.fn(),
      on: jest.fn(),
      off: jest.fn(),
      removeListener: jest.fn(),
    };
    mockWorkspace = {
      id: "test-workspace-id",
      slug: "test-workspace",
      chatProvider: "openai",
      chatModel: "gpt-3.5-turbo",
      topN: 4,
      similarityThreshold: 0.75,
    };
    mockUser = {
      id: "test-user-id",
      custom_ai_userselected: false,
    };
    mockThread = null;
  });

  test("should use LLM provider and model from settings_suffix when CUAI is not active", async () => {
    mockUser.custom_ai_userselected = false;
    const settingsSuffix = "_TM";
    const suffixedProvider = "openai";
    const suffixedModel = "gpt-4-tm-test";

    SystemSettings.currentSettings.mockResolvedValue({
      [`LLMProvider${settingsSuffix}`]: suffixedProvider,
      [`OpenAiModelPref${settingsSuffix}`]: suffixedModel,
    });
    SystemSettings.getDynamicContextSettings.mockResolvedValue(70);
    SystemSettings.getDeepSearchSettings.mockResolvedValue({
      contextPercentage: 15,
    });
    SystemSettings.getPdrSettings.mockResolvedValue({
      adjacentVector: 1,
      keepPdrVectors: true,
    });
    SystemSettings.isPromptOutputLogging.mockResolvedValue(false);

    await streamChatWithWorkspaceLQA(
      mockRequest,
      mockResponse,
      mockWorkspace,
      "Test message",
      "chat",
      mockUser,
      mockThread,
      [],
      null,
      false,
      false,
      settingsSuffix
    );

    expect(helpers.getLLMProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        provider: suffixedProvider,
        model: suffixedModel,
        settings_suffix: settingsSuffix,
      })
    );
  });

  test("should fallback to workspace defaults if settings_suffix provider is 'none'", async () => {
    mockUser.custom_ai_userselected = false;
    const settingsSuffix = "_TM";

    SystemSettings.currentSettings.mockResolvedValue({
      [`LLMProvider${settingsSuffix}`]: "none",
    });
    SystemSettings.getDynamicContextSettings.mockResolvedValue(70);
    SystemSettings.getDeepSearchSettings.mockResolvedValue({
      contextPercentage: 15,
    });
    SystemSettings.getPdrSettings.mockResolvedValue({
      adjacentVector: 1,
      keepPdrVectors: true,
    });
    SystemSettings.isPromptOutputLogging.mockResolvedValue(false);

    const workspaceProvider = "openai";
    const workspaceModel = "gpt-3.5-turbo";
    mockWorkspace.chatProvider = workspaceProvider;
    mockWorkspace.chatModel = workspaceModel;

    await streamChatWithWorkspaceLQA(
      mockRequest,
      mockResponse,
      mockWorkspace,
      "Test message",
      "chat",
      mockUser,
      mockThread,
      [],
      null,
      false,
      false,
      settingsSuffix
    );

    expect(helpers.getLLMProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        provider: workspaceProvider,
        model: workspaceModel,
        settings_suffix: settingsSuffix,
      })
    );
  });

  test("settings_suffix should override active CUAI settings", async () => {
    const settingsSuffix = "_TM";
    const suffixProvider = "openai";
    const suffixModel = "gpt-4-turbo-for-template";

    const cuaiSuffix = "_CUAI"; // Default CUAI suffix if user.custom_ai_selected_engine is null
    const cuaiProvider = "anthropic";
    const cuaiModel = "claude-2-for-cuai";

    mockUser.custom_ai_userselected = true;
    mockUser.custom_ai_selected_engine = null; // Will default to _CUAI

    SystemSettings.currentSettings.mockResolvedValue({
      // Settings for _TM (should be chosen)
      [`LLMProvider${settingsSuffix}`]: suffixProvider,
      [`OpenAiModelPref${settingsSuffix}`]: suffixModel,

      // Settings for CUAI (should be overridden by _TM)
      [`LLMProvider${cuaiSuffix}`]: cuaiProvider,
      [`AnthropicModelPref${cuaiSuffix}`]: cuaiModel,
    });
    SystemSettings.getDynamicContextSettings.mockResolvedValue(70); // General mock
    SystemSettings.getDeepSearchSettings.mockResolvedValue({
      contextPercentage: 15,
    });
    SystemSettings.getPdrSettings.mockResolvedValue({
      adjacentVector: 1,
      keepPdrVectors: true,
    });
    SystemSettings.isPromptOutputLogging.mockResolvedValue(false);

    await streamChatWithWorkspaceLQA(
      mockRequest,
      mockResponse,
      mockWorkspace, // Workspace defaults won't be used here
      "Test message for override",
      "chat",
      mockUser,
      mockThread,
      [],
      null,
      false,
      false,
      settingsSuffix // Critical: _TM suffix is passed
    );

    // Expect that the LLMProvider was called with the _TM settings, not CUAI
    expect(helpers.getLLMProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        provider: suffixProvider, // From _TM
        model: suffixModel, // From _TM
        settings_suffix: settingsSuffix, // Should be _TM
      })
    );
  });

  // Test scenarios will go here
});
