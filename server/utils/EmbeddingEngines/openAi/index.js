const { toChunks } = require("../../helpers");
const { TokenAwareBatcher } = require("../helpers/tokenAwareBatching");

class OpenAiEmbedder {
  constructor() {
    if (!process.env.OPEN_AI_KEY) throw new Error("No OpenAI API key was set.");
    const { OpenAI: OpenAIApi } = require("openai");
    this.openai = new OpenAIApi({
      apiKey: process.env.OPEN_AI_KEY,
    });
    this.model = process.env.EMBEDDING_MODEL_PREF || "text-embedding-ada-002";

    // Limit of how many strings we can process in a single pass to stay with resource or network limits
    this.maxConcurrentChunks = 500;

    // https://platform.openai.com/docs/guides/embeddings/embedding-models
    this.embeddingMaxChunkLength = 8_191;

    // Initialize token-aware batcher for handling 300k token limit
    this.tokenBatcher = new TokenAwareBatcher();
  }

  async embedTextInput(textInput) {
    const result = await this.embedChunks(
      Array.isArray(textInput) ? textInput : [textInput]
    );
    return result?.[0] || [];
  }

  async embedChunks(textChunks = []) {
    // Use token-aware batching instead of fixed chunk size batching
    // This respects OpenAI's 300k token per request limit
    const tokenAwareBatches = this.tokenBatcher.createTokenAwareBatches(
      textChunks,
      this.maxConcurrentChunks
    );

    const embeddingRequests = [];

    for (const batch of tokenAwareBatches) {
      embeddingRequests.push(this._processBatchWithRetry(batch));
    }

    const { data = [], error = null } = await Promise.all(
      embeddingRequests
    ).then((results) => {
      // If any errors were returned from OpenAI abort the entire sequence because the embeddings
      // will be incomplete.
      const errors = results
        .filter((res) => !!res.error)
        .map((res) => res.error)
        .flat();
      if (errors.length > 0) {
        let uniqueErrors = new Set();
        errors.map((error) =>
          uniqueErrors.add(`[${error.type}]: ${error.message}`)
        );

        return {
          data: [],
          error: Array.from(uniqueErrors).join(", "),
        };
      }
      return {
        data: results.map((res) => res?.data || []).flat(),
        error: null,
      };
    });

    if (error) throw new Error(`OpenAI Failed to embed: ${error}`);
    return data.length > 0 &&
      data.every((embd) => Object.hasOwn(embd, "embedding"))
      ? data.map((embd) => embd.embedding)
      : null;
  }

  /**
   * Process a single batch with automatic retry for token limit errors
   * @private
   */
  async _processBatchWithRetry(batch, retryAttempt = 0) {
    try {
      const result = await this.openai.embeddings.create({
        model: this.model,
        input: batch,
      });

      // Log token discrepancy for monitoring
      if (result.usage && result.usage.total_tokens) {
        const localTokenCount = batch.reduce(
          (sum, text) =>
            sum + this.tokenBatcher.tokenManager.countFromString(text),
          0
        );
        this.tokenBatcher.logTokenDiscrepancy(
          localTokenCount,
          result.usage.total_tokens,
          batch.length
        );
      }

      return { data: result?.data, error: null };
    } catch (e) {
      e.type =
        e?.response?.data?.error?.code ||
        e?.response?.status ||
        "failed_to_embed";
      e.message = e?.response?.data?.error?.message || e.message;

      // Check if this is a token limit error and we haven't exceeded max retries
      if (
        this.tokenBatcher.isTokenLimitError(e) &&
        retryAttempt < this.tokenBatcher.maxRetries
      ) {
        console.warn(
          `[OpenAiEmbedder] Token limit error detected, attempting retry ${retryAttempt + 1}/${this.tokenBatcher.maxRetries}`
        );

        // Create smaller batches for retry
        const retryBatches = this.tokenBatcher.createRetryBatches(
          batch,
          retryAttempt + 1
        );

        try {
          // Process all retry batches
          const retryResults = await Promise.all(
            retryBatches.map((retryBatch) =>
              this._processBatchWithRetry(retryBatch, retryAttempt + 1)
            )
          );

          // Combine results from all retry batches
          const combinedData = retryResults
            .filter((result) => !result.error)
            .map((result) => result.data || [])
            .flat();

          // Check if any retry batch failed
          const retryErrors = retryResults
            .filter((result) => !!result.error)
            .map((result) => result.error);

          if (retryErrors.length > 0) {
            return { data: [], error: retryErrors[0] };
          }

          return { data: combinedData, error: null };
        } catch (retryError) {
          console.error(
            `[OpenAiEmbedder] Retry attempt ${retryAttempt + 1} failed:`,
            retryError
          );
          return { data: [], error: retryError };
        }
      }

      // Return error if not a token limit error or max retries exceeded
      return { data: [], error: e };
    }
  }
}

module.exports = {
  OpenAiEmbedder,
};
