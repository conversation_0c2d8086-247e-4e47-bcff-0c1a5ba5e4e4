import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { <PERSON><PERSON><PERSON><PERSON>outer } from "react-router-dom";
import NewThreadButton from "../index";
import { useTranslation } from "react-i18next";
import useNavigationWithInvoiceCheck from "@/hooks/useNavigationWithInvoiceCheck";
import Workspace from "@/models/workspace";
import PropTypes from "prop-types";

// Mock dependencies
jest.mock("react-i18next", () => ({
  useTranslation: jest.fn(),
}));

jest.mock("@/hooks/useNavigationWithInvoiceCheck", () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock("@/models/workspace", () => ({
  threads: {
    new: jest.fn(),
  },
}));

jest.mock("@/utils/toast", () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => jest.fn(),
}));

// Extract the NewThreadButton component for testing
const TestNewThreadButton = ({ workspace }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = React.useState(false);

  const {
    checkAndNavigate,
    showModal,
    destinationType,
    handleClearAndContinue,
    handleKeepAndContinue,
    handleCloseModal,
  } = useNavigationWithInvoiceCheck();

  const createNewThread = async () => {
    setLoading(true);
    const { thread, error } = await Workspace.threads.new(workspace.slug);
    if (error) {
      console.error("Failed to create thread:", error);
      setLoading(false);
      return;
    }
    console.log("Thread created:", thread);
    setLoading(false);
  };

  const onClick = () => {
    checkAndNavigate(createNewThread, "thread");
  };

  return (
    <>
      <button onClick={onClick} disabled={loading}>
        {loading ? "Creating..." : "New Thread"}
      </button>
      {showModal && (
        <div data-testid="invoice-modal">
          <p>Invoice Reference Modal</p>
          <button onClick={handleClearAndContinue}>Clear and Continue</button>
          <button onClick={handleKeepAndContinue}>Keep and Continue</button>
          <button onClick={handleCloseModal}>Close</button>
        </div>
      )}
    </>
  );
};

TestNewThreadButton.propTypes = {
  workspace: PropTypes.shape({
    slug: PropTypes.string.isRequired,
  }).isRequired,
};

describe("NewThreadButton with Invoice Reference Check", () => {
  const mockWorkspace = { slug: "test-workspace" };
  const mockT = jest.fn((key) => key);
  const mockCheckAndNavigate = jest.fn();
  const mockHandleClearAndContinue = jest.fn();
  const mockHandleKeepAndContinue = jest.fn();
  const mockHandleCloseModal = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    useTranslation.mockReturnValue({ t: mockT });
    useNavigationWithInvoiceCheck.mockReturnValue({
      checkAndNavigate: mockCheckAndNavigate,
      showModal: false,
      destinationType: "thread",
      handleClearAndContinue: mockHandleClearAndContinue,
      handleKeepAndContinue: mockHandleKeepAndContinue,
      handleCloseModal: mockHandleCloseModal,
    });
    Workspace.threads.new.mockResolvedValue({
      thread: { slug: "new-thread" },
      error: null,
    });
  });

  it("should call checkAndNavigate when button is clicked", () => {
    render(
      <BrowserRouter>
        <TestNewThreadButton workspace={mockWorkspace} />
      </BrowserRouter>
    );

    const button = screen.getByText("New Thread");
    fireEvent.click(button);

    expect(mockCheckAndNavigate).toHaveBeenCalledWith(
      expect.any(Function),
      "thread"
    );
  });

  it("should show invoice modal when showModal is true", () => {
    useNavigationWithInvoiceCheck.mockReturnValue({
      checkAndNavigate: mockCheckAndNavigate,
      showModal: true,
      destinationType: "thread",
      handleClearAndContinue: mockHandleClearAndContinue,
      handleKeepAndContinue: mockHandleKeepAndContinue,
      handleCloseModal: mockHandleCloseModal,
    });

    render(
      <BrowserRouter>
        <TestNewThreadButton workspace={mockWorkspace} />
      </BrowserRouter>
    );

    expect(screen.getByTestId("invoice-modal")).toBeInTheDocument();
    expect(screen.getByText("Invoice Reference Modal")).toBeInTheDocument();
  });

  it("should call appropriate handlers when modal buttons are clicked", () => {
    useNavigationWithInvoiceCheck.mockReturnValue({
      checkAndNavigate: mockCheckAndNavigate,
      showModal: true,
      destinationType: "thread",
      handleClearAndContinue: mockHandleClearAndContinue,
      handleKeepAndContinue: mockHandleKeepAndContinue,
      handleCloseModal: mockHandleCloseModal,
    });

    render(
      <BrowserRouter>
        <TestNewThreadButton workspace={mockWorkspace} />
      </BrowserRouter>
    );

    fireEvent.click(screen.getByText("Clear and Continue"));
    expect(mockHandleClearAndContinue).toHaveBeenCalled();

    fireEvent.click(screen.getByText("Keep and Continue"));
    expect(mockHandleKeepAndContinue).toHaveBeenCalled();

    fireEvent.click(screen.getByText("Close"));
    expect(mockHandleCloseModal).toHaveBeenCalled();
  });

  it("should create thread when navigation function is called", async () => {
    let capturedNavigationFunction;
    mockCheckAndNavigate.mockImplementation((navFn, type) => {
      capturedNavigationFunction = navFn;
    });

    render(
      <BrowserRouter>
        <TestNewThreadButton workspace={mockWorkspace} />
      </BrowserRouter>
    );

    const button = screen.getByText("New Thread");
    fireEvent.click(button);

    // Execute the captured navigation function
    await capturedNavigationFunction();

    expect(Workspace.threads.new).toHaveBeenCalledWith("test-workspace");
  });
});
