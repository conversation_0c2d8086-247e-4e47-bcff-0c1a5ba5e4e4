import { useEffect, useState, useMemo } from "react";
import Sidebar from "@/components/SettingsSidebar";
import * as Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { ArrowLeft, UserPlus } from "@phosphor-icons/react";
import Admin from "@/models/admin";
import paths from "@/utils/paths";
import UserRow from "./UserRow";
import useUser from "@/hooks/useUser";
import NewUserModal from "./NewUserModal";
import { useModal } from "@/hooks/useModal";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { Button } from "@/components/Button";
import showToast from "@/utils/toast";

export default function AdminUsers() {
  const { t } = useTranslation();
  const { isOpen, openModal, closeModal } = useModal();

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="relative rounded-md w-full h-full overflow-y-scroll">
          <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
            <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-3 border-bottom">
              <Link to={paths.home()}>
                <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
              </Link>
              <div className="flex flex-col">
                <div className="items-center flex gap-x-4">
                  <p className="text-lg leading-6 font-bold text-foreground">
                    {t("settings.users")}
                  </p>
                </div>
                <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                  {t("user-setting.description")}
                </p>
              </div>
            </div>
            <div className="w-full justify-end flex absolute right-5">
              <Button onClick={openModal} className="mr-0 -mb-6">
                <UserPlus className="h-4 w-4" weight="bold" />{" "}
                {t("user-setting.add-user")}
              </Button>
            </div>
            <UsersContainer />
          </div>
          <NewUserModal isOpen={isOpen} closeModal={closeModal} />
        </div>
      </div>
    </div>
  );
}

function UsersContainer() {
  const { t } = useTranslation();
  const { user: currUser } = useUser();
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState([]);
  const [selectedDomain, setSelectedDomain] = useState("all");
  const [domains, setDomains] = useState([]);
  const [sortBy, setSortBy] = useState("username");

  useEffect(() => {
    if (localStorage.getItem("showUserCreatedToastAfterReload") === "true") {
      showToast(t("show-toast.user-created"), "success", { clear: true });
      localStorage.removeItem("showUserCreatedToastAfterReload");
    }
  }, [t]);

  useEffect(() => {
    async function fetchUsers() {
      try {
        const allUsers = await Admin.users();
        if (!Array.isArray(allUsers)) {
          console.error(
            "Admin.users() response is not valid. Expected an array of users, received:",
            allUsers
          );
          setUsers([]);
          setDomains(["all"]);
          return;
        }
        setUsers(allUsers);

        const uniqueDomains = allUsers.reduce((acc, user) => {
          const domain = user.username?.includes("@")
            ? user.username.split("@")[1]
            : "others";
          if (!acc.includes(domain)) acc.push(domain);
          return acc;
        }, []);

        setDomains(["all", ...uniqueDomains.sort()]);
      } catch (error) {
        console.error("Error fetching or processing users:", error);
        setUsers([]);
        setDomains(["all"]);
      } finally {
        setLoading(false);
      }
    }
    fetchUsers();
  }, []);

  const filteredUsers = users.filter((user) => {
    if (selectedDomain === "all") return true;
    const userDomain = user.username?.includes("@")
      ? user.username.split("@")[1]
      : "others";
    return userDomain === selectedDomain;
  });

  const sortedUsers = useMemo(() => {
    const usersCopy = [...filteredUsers];
    if (sortBy === "organization") {
      usersCopy.sort((a, b) => {
        const orgA = a.organization?.name || "";
        const orgB = b.organization?.name || "";
        const orgCompare = orgA.localeCompare(orgB);
        if (orgCompare !== 0) return orgCompare;
        // if same organization, sort by username
        return a.username.localeCompare(b.username);
      });
    } else {
      usersCopy.sort((a, b) => a.username.localeCompare(b.username));
    }
    return usersCopy;
  }, [filteredUsers, sortBy]);

  if (loading) {
    return (
      <Skeleton.default
        height="80vh"
        width="100%"
        highlightColor="#fff"
        baseColor="#e2f3fa"
        count={1}
        className="w-full p-4 rounded-b-2xl rounded-tr-2xl rounded-tl-sm mt-6"
        containerClassName="flex w-full"
      />
    );
  }

  return (
    <>
      <div className="flex justify-between mt-4 mb-2">
        <select
          value={selectedDomain}
          onChange={(e) => setSelectedDomain(e.target.value)}
          className="px-4 py-2 rounded-lg border border-border focus:outline-none focus:ring-2 focus:ring-primary bg-background"
        >
          {domains.map((domain) => (
            <option key={domain} value={domain}>
              {domain === "all"
                ? t("user-setting.all-domains")
                : domain === "others"
                  ? t("user-setting.other-users")
                  : domain}
            </option>
          ))}
        </select>
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="px-4 py-2 rounded-lg border border-border focus:outline-none focus:ring-2 focus:ring-primary bg-background"
        >
          <option value="username">{t("user-setting.sort-username")}</option>
          <option value="organization">
            {t("user-setting.sort-organization")}
          </option>
        </select>
      </div>
      <table className="w-full text-sm text-left rounded-lg mt-3">
        <thead className="text-foreground text-opacity-80 text-xs leading-[18px] font-bold uppercase border-white border-b border-opacity-60">
          <tr>
            <th scope="col" className="px-6 py-3 rounded-tl-lg">
              {t("user-setting.username")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("user-setting.role")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("user-setting.economy-id")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("organization.label")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("user-setting.date-added")}
            </th>
            <th scope="col" className="px-6 py-3 rounded-tr-lg">
              {" "}
            </th>
          </tr>
        </thead>
        <tbody>
          {sortedUsers.map((user) => (
            <UserRow key={user.id} currUser={currUser} user={user} />
          ))}
        </tbody>
      </table>
    </>
  );
}

const getRoleHint = (t) => ({
  default: [
    t("new-user.permissions.default.0"),
    t("new-user.permissions.default.1"),
  ],
  manager: [
    t("new-user.permissions.manager.0"),
    t("new-user.permissions.manager.1"),
    t("new-user.permissions.manager.2"),
  ],
  admin: [t("new-user.permissions.admin.0"), t("new-user.permissions.admin.1")],
  superuser: [
    t("new-user.permissions.superuser.0"),
    t("new-user.permissions.superuser.1"),
    t("new-user.permissions.superuser.2"),
  ],
});

export function RoleHintDisplay({ role }) {
  const { t } = useTranslation();

  const ROLE_HINT = getRoleHint(t);

  return (
    <div className="flex flex-col py-1">
      <p className="text-sm font-medium text-foreground">
        {t("new-user.permissions.title")}
      </p>
      <ul className="flex flex-col list-disc">
        {ROLE_HINT[role ?? "default"].map((hints, i) => {
          return (
            <li key={i} className="text-xs text-foreground">
              {hints}
            </li>
          );
        })}
      </ul>
    </div>
  );
}
