export default {
  // =========================
  // GENERIC PROVIDER SELECTION SETTINGS
  // =========================
  generic: {
    "base-url": "Base URL",
    "api-key": "API-nøkkel",
    "api-key-placeholder": "Skriv inn din API-nøkkel",
    "chat-model": "Chat-modell",
    "chat-model-placeholder": "Skriv inn chat-modell",
    "token-window": "Token-kontekstvindu",
    "token-window-placeholder": "Kontekstvindu-grense (f.eks: 4096)",
    "max-tokens": "Maks tokens",
    "max-tokens-placeholder": "Maks tokens per forespørsel (f.eks: 1024)",
    "embedding-deployment": "Embedding-deploynavn",
    "embedding-deployment-placeholder":
      "Azure OpenAI embedding-modell deploynavn",
    "embedding-model": "Embedding-modell",
    "embedding-model-placeholder": "Skriv inn embedding-modell",
    "max-embedding-chunk-length": "Maks embedding-chunk-lengde",
    saving: "Lagrer...",
    "save-changes": "Lagre endringer",
    "workspace-update-error": "Feil: {{error}}",
    "base-url-placeholder": "f.eks: https://proxy.openai.com",
    "password-mask-length": "12",
  },

  // =========================
  // GENERIC MODEL SELECTION SETTINGS
  // =========================
  model: {
    selection: "Chat-modellvalg",
    "embedding-selection": "Embedding-modellvalg",
    "enter-api-key":
      "Skriv inn en gyldig API-nøkkel for å se alle tilgjengelige modeller for kontoen din.",
    "enter-url": "Skriv inn URL først",
    "your-models": "Dine lastede modeller",
    "available-models": "Tilgjengelige modeller",
  },

  // =========================
  // ANTHROPIC
  // =========================
  anthropic: {
    "api-key": "Anthropic API-nøkkel",
    "api-key-placeholder": "Skriv inn din Anthropic API-nøkkel",
    "model-selection": "Chat-modellvalg",
  },

  // =========================
  // AZURE SETTINGS
  // =========================
  azure: {
    "service-endpoint": "Azure-tjenesteendepunkt",
    "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
    "api-key": "API-nøkkel",
    "api-key-placeholder": "Azure OpenAI API-nøkkel",
    "chat-deployment": "Chat-deploynavn",
    "chat-deployment-placeholder": "Azure OpenAI chat-modell deploynavn",
    "token-limit": "Chat-modell token-grense",
  },
  //AZURE-AI FOR EMBEDDING //TODO SORT LLM AND EMBEDDING
  azureai: {
    "service-endpoint": "Azure-tjenesteendepunkt",
    "api-key": "API-nøkkel",
    "api-key-placeholder": "Azure OpenAI API-nøkkel",
    "embedding-deployment-name": "Embedding-deploynavn",
    "embedding-deployment-name-placeholder": "Skriv inn embedding-deploynavn",
  },

  // =========================
  // AWS BEDROCK SETTINGS
  // =========================
  bedrock: {
    "iam-warning":
      "Du bør bruke en ordentlig definert IAM-bruker for slutninger.",
    "read-more": "Les mer om hvordan du bruker AWS Bedrock i denne instansen",
    "access-id": "AWS Bedrock IAM tilgangs-ID",
    "access-id-placeholder": "AWS Bedrock IAM bruker tilgangs-ID",
    "access-key": "AWS Bedrock IAM tilgangsnøkkel",
    "access-key-placeholder": "AWS Bedrock IAM bruker tilgangsnøkkel",
    region: "AWS-region",
    "model-id": "Modell-ID",
    "model-id-placeholder": "Modell-ID fra AWS f.eks: meta.llama3.1-v0.1",
    "context-window": "Modell-kontekstvindu",
    "context-window-placeholder": "Kontekstvindu-grense (f.eks: 4096)",
  },

  // =========================
  // COHERE
  // =========================
  cohere: {
    "api-key": "Cohere API-nøkkel",
    "api-key-placeholder": "Skriv inn din Cohere API-nøkkel",
    "model-preference": "Modellpreferanse",
    "model-selection": "Modellvalg",
  },

  // =========================
  // DEEPSEEK LLM SETTINGS
  // =========================
  deepseek: {
    "api-key": "DeepSeek API-nøkkel",
    "api-key-placeholder": "Skriv inn din DeepSeek API-nøkkel",
    "model-selection": "Chat-modellvalg",
    "loading-models": "-- laster tilgjengelige modeller --",
  },

  // =========================
  // FIREWORKSAI LLM SELECTION
  // =========================
  fireworksai: {
    "api-key": "FireworksAI API-nøkkel",
    "api-key-placeholder": "Skriv inn din FireworksAI API-nøkkel",
    "model-selection": "Chat-modellvalg",
    "loading-models": "-- laster tilgjengelige modeller --",
  },
  // =========================
  // GEMINI LLM OPTIONS
  // =========================
  gemini: {
    "api-key": "Google AI API-nøkkel",
    "api-key-placeholder": "Google Gemini API-nøkkel",
    "model-selection": "Chat-modellvalg",
    "loading-models": "-- laster tilgjengelige modeller --",
    "manual-options": "Manuelle alternativer",
    "safety-setting": "Sikkerhetsinnstilling",
    experimental: "Eksperimentell",
    stable: "Stabil",
    "safety-options": {
      none: "Ingen (standard)",
      "block-few": "Blokker kun høyrisiko",
      "block-some": "Blokker middels og høyrisiko",
      "block-most": "Blokker det meste av innholdet",
    },
  },

  // =========================
  // GROQ LLM SELECTION
  // =========================
  groq: {
    "api-key": "Groq API-nøkkel",
    "api-key-placeholder": "Skriv inn din Groq API-nøkkel",
    "model-selection": "Chat-modellvalg",
    "loading-models": "-- laster tilgjengelige modeller --",
    "enter-api-key":
      "Skriv inn en gyldig API-nøkkel for å se alle tilgjengelige modeller for kontoen din.",
    "available-models": "Tilgjengelige modeller",
    "model-description":
      "Velg GroqAI-modellen du vil bruke for samtalene dine.",
  },

  // =========================
  // HUGGINGFACE LLM SETTINGS
  // =========================
  huggingface: {
    "inference-endpoint": "HuggingFace Inference-endepunkt",
    "endpoint-placeholder": "https://example.endpoints.huggingface.cloud",
    "access-token": "HuggingFace tilgangstoken",
    "token-placeholder": "Skriv inn din HuggingFace tilgangstoken",
    "token-limit": "Modell-tokengrense",
    "token-limit-placeholder": "4096",
  },

  // =========================
  // KOBOLDCPP SETTINGS
  // =========================
  koboldcpp: {
    "show-advanced": "Vis manuell endepunktinndata",
    "hide-advanced": "Skjul manuell endepunktinndata",
    "base-url": "KoboldCPP base-URL",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "base-url-desc": "Skriv inn URL-en der KoboldCPP kjører.",
    "auto-detect": "Auto-deteksjon",
    "token-context-window": "Token-kontekstvindu",
    "token-window-placeholder": "4096",
    "token-window-desc": "Maksimalt antall tokens for kontekst og svar.",
    model: "KoboldCPP-modell",
    "loading-models": "--laster tilgjengelige modeller--",
    "enter-url": "Skriv inn KoboldCPP URL først",
    "model-desc":
      "Velg KoboldCPP-modellen du vil bruke. Modeller vil lastes etter at en gyldig KoboldCPP URL er oppgitt.",
    "model-choose": "Velg KoboldCPP-modellen du vil bruke for samtalene dine.",
  },

  // =========================
  // LITELLM
  // =========================
  litellm: {
    "model-tooltip":
      "Sørg for å velge en gyldig embedding-modell. Chat-modeller er ikke embedding-modeller. Se",
    "model-tooltip-link": "denne siden",
    "model-tooltip-more": "for mer informasjon.",
    "base-url": "Base URL",
    "base-url-placeholder": "f.eks: https://proxy.openai.com",
    "max-embedding-chunk-length": "Maks embedding-chunk-lengde",
    "token-window": "Token-kontekstvindu",
    "token-window-placeholder": "Kontekstvindu-grense (f.eks: 4096)",
    "api-key": "API-nøkkel",
    optional: "valgfritt",
    "api-key-placeholder": "sk-minhemmeligenøkkel",
    "model-selection": "Chat-modellvalg",
    "loading-models": "-- laster tilgjengelige modeller --",
    "waiting-url": "-- venter på URL --",
    "loaded-models": "Dine lastede modeller",
    "manage-embedding": "Administrer embedding",
    "embedding-required":
      "Litellm krever at en embedding-tjeneste er satt opp for å brukes.",
  },

  // =========================
  // LMSTUDIO LLM SELECTION
  // =========================
  lmstudio: {
    "max-tokens": "Maks tokens",
    "max-tokens-desc": "Maksimalt antall tokens for kontekst og svar.",
    "show-advanced": "Vis manuell endepunktinndata",
    "hide-advanced": "Skjul manuell endepunktinndata",
    "base-url": "LM Studio base-URL",
    "base-url-placeholder": "http://localhost:1234/v1",
    "base-url-desc": "Skriv inn URL-en der LM Studio kjører.",
    "auto-detect": "Auto-deteksjon",
    model: "LM Studio-modell",
    "model-loading": "--laster tilgjengelige modeller--",
    "model-url-first": "Skriv inn LM Studio URL først",
    "model-desc":
      "Velg LM Studio-modellen du vil bruke. Modeller vil lastes etter at en gyldig LM Studio URL er oppgitt.",
    "model-choose": "Velg LM Studio-modellen du vil bruke for samtalene dine.",
    "model-loaded": "Dine lastede modeller",
    "embedding-required":
      "LMStudio som din LLM krever at du setter opp en embedding-tjeneste for å bruke.",
    "manage-embedding": "Administrer embedding",
    "max-embedding-chunk-length": "Maks embedding-chunk-lengde",
  },

  // =========================
  // LOCALAI LLM SELECTION
  // =========================
  localai: {
    "token-window": "Token-kontekstvindu",
    "token-window-placeholder": "4096",
    "api-key": "Local AI API-nøkkel",
    "api-key-optional": "valgfritt",
    "api-key-placeholder": "sk-minhemmeligenøkkel",
    "show-advanced": "Vis avanserte innstillinger",
    "hide-advanced": "Skjul avanserte innstillinger",
    "base-url": "Local AI base-URL",
    "base-url-placeholder": "http://localhost:8080/v1",
    "base-url-help": "Skriv inn URL-en der LocalAI kjører.",
    "auto-detect": "Auto-deteksjon",
    "max-embedding-chunk-length": "Maks embedding-chunk-lengde",
    "embedding-required": "Embedding er nødvendig for LocalAI.",
    "manage-embedding": "Administrer embedding",
    "model-selection": "Chat-modellvalg",
    "loading-models": "-- laster tilgjengelige modeller --",
    "waiting-url": "-- venter på URL --",
    "loaded-models": "Dine lastede modeller",
  },

  // =========================
  // MISTRAL LLM OPTIONS
  // =========================
  mistral: {
    "api-key": "Mistral API-nøkkel",
    "api-key-placeholder": "Mistral API-nøkkel",
    "model-selection": "Chat-modellvalg",
    "loading-models": "-- laster tilgjengelige modeller --",
    "waiting-key": "-- venter på API-nøkkel --",
    "available-models": "Tilgjengelige Mistral-modeller",
  },

  // =========================
  // NATIVE LLM SETTINGS
  // =========================

  native: {
    "experimental-warning":
      "Bruk av en lokalt hostet LLM er eksperimentell og fungerer kanskje ikke som forventet.",
    "model-desc": "Velg en modell fra dine lokalt hostede modeller.",
    "token-desc": "Maksimalt antall tokens for kontekst og svar.",
  },

  // =========================
  // OLLAMA LLM SELECTION
  // =========================

  ollamallmselection: {
    "max-tokens": "Maks tokens",
    "max-tokens-desc": "Maksimalt antall tokens for kontekst og svar.",
    "show-advanced": "Vis manuell endepunktinndata",
    "hide-advanced": "Skjul manuell endepunktinndata",
    "base-url": "Ollama base-URL",
    "base-url-placeholder": "http://127.0.0.1:11434",
    "base-url-desc": "Skriv inn URL-en der Ollama kjører.",
    "auto-detect": "Auto-deteksjon",
    "keep-alive": "Ollama hold i live",
    "no-cache": "Ingen cache",
    "five-minutes": "5 minutter",
    "one-hour": "1 time",
    forever: "For alltid",
    "keep-alive-desc": "Hold modellen lastet i minnet.",
    "learn-more": "Lær mer",
    "performance-mode": "Ytelsesmodus",
    "base-default": "Base (Standard)",
    maximum: "Maksimum",
    "performance-mode-desc": "Velg ytelsesmodus.",
    note: "Merk:",
    "maximum-warning": "Maksimum-modus bruker mer ressurser.",
    base: "Base",
    "base-desc": "Base-modus er standardinnstillingen.",
    "maximum-desc":
      "Maksimum-modus gir høyere ytelse ved å bruke fullt kontekstvindu opp til maks tokens.",
    model: "Ollama-modell",
    "loading-models": "--laster tilgjengelige modeller--",
    "enter-url": "Skriv inn Ollama URL først",
    "model-desc":
      "Velg Ollama-modellen du vil bruke. Modeller vil lastes etter at en gyldig Ollama URL er oppgitt.",
    "model-choose": "Velg Ollama-modellen du vil bruke for samtalene dine.",
  },

  // =========================
  // OPENAI LLM SELECTION
  // =========================
  openai: {
    "api-key": "OpenAI API-nøkkel",
    "api-key-placeholder": "Skriv inn din OpenAI API-nøkkel",
    "model-preference": "Modellpreferanse",
    "model-selection": "Chat-modellvalg",
    "loading-models": "-- laster tilgjengelige modeller --",
    "model-divider": "──────────",
  },

  // =========================
  // OPENROUTER LLM SELECTION
  // =========================
  openrouter: {
    "api-key": "OpenRouter API-nøkkel",
    "api-key-placeholder": "Skriv inn din OpenRouter API-nøkkel",
    "model-selection": "Chat-modellvalg",
    "loading-models": "-- laster tilgjengelige modeller --",
  },

  // =========================
  // PERPLEXITY LLM SELECTION
  // =========================
  perplexity: {
    "api-key": "Perplexity API-nøkkel",
    "api-key-placeholder": "Skriv inn din Perplexity API-nøkkel",
    "model-selection": "Chat-modellvalg",
    "loading-models": "-- laster tilgjengelige modeller --",
    "available-models": "Tilgjengelige Perplexity-modeller",
  },

  // =========================
  // TEXTGENWEBUI COMPONENT
  // =========================
  textgenwebui: {
    "base-url": "Base URL",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "token-window": "Token-kontekstvindu",
    "token-window-placeholder": "Kontekstvindu-grense (f.eks: 4096)",
    "api-key": "API-nøkkel (Valgfritt)",
    "api-key-placeholder": "TextGen Web UI API-nøkkel",
    "max-tokens": "Maks tokens",
    "max-tokens-placeholder": "Maks tokens per forespørsel (f.eks: 1024)",
  },

  // =========================
  // TOGETHERAI
  // =========================
  togetherai: {
    "api-key": "TogetherAI API-nøkkel",
    "api-key-placeholder": "Skriv inn din TogetherAI API-nøkkel",
    "model-selection": "Chat-modellvalg",
    "loading-models": "-- laster tilgjengelige modeller --",
  },

  // =========================
  // XAI
  // =========================
  xai: {
    "api-key": "xAI API-nøkkel",
    "api-key-placeholder": "xAI API-nøkkel",
    "model-selection": "Chat-modellvalg",
    "loading-models": "-- laster tilgjengelige modeller --",
    "enter-api-key":
      "Skriv inn en gyldig API-nøkkel for å se tilgjengelige modeller for kontoen din.",
    "available-models": "Tilgjengelige modeller",
    "model-description": "Velg xAI-modellen du vil bruke for samtalene dine.",
  },
  // LLM Provider specific translations
  "llm-provider.textgenwebui": "Koble til en Text Generation WebUI-instans.",
  "llm-provider.litellm": "Koble til en LLM via LiteLLM.",
  "llm-provider.openai-generic":
    "Koble til en OpenAI-kompatibel API-slutpunkt.",
  "llm-provider.system-default": "Bruk den innebygde Native-modellen.",
  // =========================
  // LOADING STATES
  // =========================
  loading: {
    models: "-- laster tilgjengelige modeller --",
    "waiting-url": "-- venter på URL --",
    "waiting-api-key": "-- venter på API-nøkkel --",
    "waiting-models": "-- venter på modeller --",
  },
};
