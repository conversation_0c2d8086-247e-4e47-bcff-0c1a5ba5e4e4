const { UserStyleProfile } = require("../../../models/userStyleProfile");
const prisma = require("../../../utils/prisma");

// Mock Prisma
jest.mock("../../../utils/prisma", () => ({
  userStyleProfile: {
    create: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    update: jest.fn(),
    updateMany: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
  $transaction: jest.fn(),
}));

describe("UserStyleProfile Model", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("create", () => {
    test("creates a new style profile successfully", async () => {
      const mockProfile = {
        id: 1,
        user_id: 1,
        name: "Legal Brief Style",
        instructions: "Use formal legal language with clear structure.",
        is_active: false,
        created_at: new Date(),
        updated_at: new Date(),
      };

      prisma.userStyleProfile.create.mockResolvedValue(mockProfile);

      const result = await UserStyleProfile.create({
        userId: 1,
        name: "Legal Brief Style",
        instructions: "Use formal legal language with clear structure.",
      });

      expect(result).toEqual({ profile: mockProfile, error: null });
      expect(prisma.userStyleProfile.create).toHaveBeenCalledWith({
        data: {
          user_id: 1,
          name: "Legal Brief Style",
          instructions: "Use formal legal language with clear structure.",
          is_active: false,
        },
      });
    });

    test("validates required fields", async () => {
      const result = await UserStyleProfile.create({
        userId: null,
        name: "Test",
        instructions: "Test instructions",
      });

      expect(result).toEqual({
        profile: null,
        error: "User ID, name, and instructions are required",
      });
      expect(prisma.userStyleProfile.create).not.toHaveBeenCalled();
    });

    test("validates instructions length", async () => {
      const longInstructions = "A".repeat(10001);

      const result = await UserStyleProfile.create({
        userId: 1,
        name: "Test",
        instructions: longInstructions,
      });

      expect(result).toEqual({
        profile: null,
        error: "Style instructions cannot exceed 10,000 characters",
      });
      expect(prisma.userStyleProfile.create).not.toHaveBeenCalled();
    });

    test("handles database errors", async () => {
      prisma.userStyleProfile.create.mockRejectedValue(
        new Error("Database connection failed")
      );

      const result = await UserStyleProfile.create({
        userId: 1,
        name: "Test",
        instructions: "Test instructions",
      });

      expect(result).toEqual({
        profile: null,
        error: "Database connection failed",
      });
    });

    test("trims name and instructions", async () => {
      const mockProfile = {
        id: 1,
        user_id: 1,
        name: "Test Style",
        instructions: "Test instructions",
        is_active: false,
        created_at: new Date(),
        updated_at: new Date(),
      };

      prisma.userStyleProfile.create.mockResolvedValue(mockProfile);

      await UserStyleProfile.create({
        userId: 1,
        name: "  Test Style  ",
        instructions: "  Test instructions  ",
      });

      expect(prisma.userStyleProfile.create).toHaveBeenCalledWith({
        data: {
          user_id: 1,
          name: "Test Style",
          instructions: "Test instructions",
          is_active: false,
        },
      });
    });
  });

  describe("getByUserId", () => {
    test("returns user style profiles", async () => {
      const mockProfiles = [
        {
          id: 1,
          user_id: 1,
          name: "Style 1",
          instructions: "Instructions 1",
          is_active: true,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 2,
          user_id: 1,
          name: "Style 2",
          instructions: "Instructions 2",
          is_active: false,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      prisma.userStyleProfile.findMany.mockResolvedValue(mockProfiles);

      const result = await UserStyleProfile.getByUserId(1);

      expect(result).toEqual(mockProfiles);
      expect(prisma.userStyleProfile.findMany).toHaveBeenCalledWith({
        where: { user_id: 1 },
        orderBy: { createdAt: "desc" },
      });
    });

    test("handles invalid user ID", async () => {
      const result = await UserStyleProfile.getByUserId(null);

      expect(result).toEqual([]);
    });

    test("handles database errors", async () => {
      prisma.userStyleProfile.findMany.mockRejectedValue(
        new Error("Database error")
      );

      const result = await UserStyleProfile.getByUserId(1);

      expect(result).toEqual([]);
    });
  });

  describe("getActiveProfile", () => {
    test("returns active profile for user", async () => {
      const mockProfile = {
        id: 1,
        user_id: 1,
        name: "Active Style",
        instructions: "Active instructions",
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      };

      prisma.userStyleProfile.findFirst.mockResolvedValue(mockProfile);

      const result = await UserStyleProfile.getActiveProfile(1);

      expect(result).toEqual(mockProfile);
      expect(prisma.userStyleProfile.findFirst).toHaveBeenCalledWith({
        where: {
          user_id: 1,
          is_active: true,
        },
      });
    });

    test("returns null when no active profile exists", async () => {
      prisma.userStyleProfile.findFirst.mockResolvedValue(null);

      const result = await UserStyleProfile.getActiveProfile(1);

      expect(result).toEqual(null);
    });

    test("handles invalid user ID", async () => {
      const result = await UserStyleProfile.getActiveProfile(null);

      expect(result).toEqual(null);
    });
  });

  describe("activate", () => {
    test("sets profile as active and deactivates others", async () => {
      const mockProfile = {
        id: 1,
        user_id: 1,
        name: "Style 1",
        instructions: "Instructions 1",
        is_active: false,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock the get method to return the profile
      UserStyleProfile.get = jest.fn().mockResolvedValue(mockProfile);

      // Mock the transaction
      prisma.$transaction.mockImplementation(async (callback) => {
        await callback(prisma);
      });

      const result = await UserStyleProfile.activate(1, 1);

      expect(result).toEqual({ success: true, error: null });
    });

    test("validates profile ownership", async () => {
      // Mock the get method to return null (profile not found)
      UserStyleProfile.get = jest.fn().mockResolvedValue(null);

      const result = await UserStyleProfile.activate(1, 1);

      expect(result).toEqual({
        success: false,
        error: "Style profile not found or access denied",
      });
    });

    test("handles database errors", async () => {
      // Mock the get method to return a profile
      const mockProfile = { id: 1, user_id: 1 };
      UserStyleProfile.get = jest.fn().mockResolvedValue(mockProfile);

      prisma.$transaction.mockRejectedValue(new Error("Database error"));

      const result = await UserStyleProfile.activate(1, 1);

      expect(result).toEqual({
        success: false,
        error: "Database error",
      });
    });
  });

  describe("update", () => {
    test("updates profile successfully", async () => {
      const mockProfile = {
        id: 1,
        user_id: 1,
        name: "Updated Style",
        instructions: "Updated instructions",
        is_active: false,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock the get method to return the existing profile
      UserStyleProfile.get = jest.fn().mockResolvedValue({
        id: 1,
        user_id: 1,
        name: "Original Style",
        instructions: "Original instructions",
      });

      prisma.userStyleProfile.update.mockResolvedValue(mockProfile);

      const result = await UserStyleProfile.update(1, 1, {
        name: "Updated Style",
        instructions: "Updated instructions",
      });

      expect(result).toEqual({ profile: mockProfile, error: null });
      expect(prisma.userStyleProfile.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          name: "Updated Style",
          instructions: "Updated instructions",
        },
      });
    });

    test("validates profile ownership before update", async () => {
      // Mock the get method to return null (profile not found)
      UserStyleProfile.get = jest.fn().mockResolvedValue(null);

      const result = await UserStyleProfile.update(1, 1, {
        name: "Updated Style",
      });

      expect(result).toEqual({
        profile: null,
        error: "Style profile not found or access denied",
      });
    });

    test("validates instructions length on update", async () => {
      // Mock the get method to return a profile
      UserStyleProfile.get = jest.fn().mockResolvedValue({
        id: 1,
        user_id: 1,
      });

      const longInstructions = "a".repeat(10001);

      const result = await UserStyleProfile.update(1, 1, {
        instructions: longInstructions,
      });

      expect(result).toEqual({
        profile: null,
        error: "Style instructions cannot exceed 10,000 characters",
      });
    });
  });

  describe("delete", () => {
    test("deletes profile successfully", async () => {
      // Mock the get method to return the profile
      UserStyleProfile.get = jest.fn().mockResolvedValue({
        id: 1,
        user_id: 1,
      });

      prisma.userStyleProfile.delete.mockResolvedValue({});

      const result = await UserStyleProfile.delete(1, 1);

      expect(result).toEqual({ success: true, error: null });
      expect(prisma.userStyleProfile.delete).toHaveBeenCalledWith({
        where: { id: 1 },
      });
    });

    test("validates profile ownership before deletion", async () => {
      // Mock the get method to return null (profile not found)
      UserStyleProfile.get = jest.fn().mockResolvedValue(null);

      const result = await UserStyleProfile.delete(1, 1);

      expect(result).toEqual({
        success: false,
        error: "Style profile not found or access denied",
      });
    });

    test("handles database errors during deletion", async () => {
      // Mock the get method to return a profile
      UserStyleProfile.get = jest.fn().mockResolvedValue({
        id: 1,
        user_id: 1,
      });

      prisma.userStyleProfile.delete.mockRejectedValue(
        new Error("Database error")
      );

      const result = await UserStyleProfile.delete(1, 1);

      expect(result).toEqual({
        success: false,
        error: "Database error",
      });
    });
  });

  describe("count", () => {
    test("returns count of user profiles", async () => {
      prisma.userStyleProfile.count.mockResolvedValue(3);

      const result = await UserStyleProfile.count(1);

      expect(result).toBe(3);
      expect(prisma.userStyleProfile.count).toHaveBeenCalledWith({
        where: { user_id: 1 },
      });
    });

    test("handles invalid user ID", async () => {
      const result = await UserStyleProfile.count(null);

      expect(result).toBe(0);
      expect(prisma.userStyleProfile.count).not.toHaveBeenCalled();
    });

    test("handles database errors", async () => {
      prisma.userStyleProfile.count.mockRejectedValue(
        new Error("Database error")
      );

      const result = await UserStyleProfile.count(1);

      expect(result).toBe(0);
    });
  });
});
