export default {
  "answer-upgrade": {
    title: "Upgrade Answer",
    subtitle: "Select sub-category",
    steps: "Executing the legal tasks",
    planning: "Planning...",
    "cancel-process":
      "Are you sure you want to cancel this legal task process?",
    "wait-process": " Please wait while we prepare the process.",
    "process-title-one": " Building Action Plan",
    "process-description-one":
      "Defining the document's structure, objectives, and key tasks to ensure a clear and organized process.",
    "process-title-two": "Building Action Step",
    "process-description-two":
      "Executing each task from the plan, generating content and sections based on context and purpose.",
    "process-title-three": "Building the Final Output",
    "process-description-three":
      "Combining all content, refining for accuracy, and delivering a polished, ready-to-use document.",
    "category-step": {
      title: "Select Category",
      description: "Choose a category that best matches your needs",
      categories: {
        formality: {
          label: "Formality",
          choices: {
            more_formal: "Make this more formal",
            less_formal: "Make this less formal",
            more_professional: "Make this more professional",
            more_casual: "Make this more casual",
            more_polished: "Make this more polished",
            more_relaxed: "Make this more relaxed",
            academic_tone: "Use a more academic tone",
            conversational_tone: "Use a more conversational tone",
          },
        },
        complexity: {
          label: "Language Complexity",
          choices: {
            simplify: "Simplify the language",
            more_descriptive: "Add more descriptive language",
            complex_vocab: "Use more complex vocabulary",
            simple_vocab: "Use simpler vocabulary",
            technical: "Increase the use of technical language",
            layman: "Use more layman's terms",
            add_jargon: "Incorporate jargon specific to the field",
            avoid_jargon: "Avoid jargon and use general terms",
            add_rhetorical: "Add more rhetorical questions",
            less_rhetorical: "Use fewer rhetorical questions",
          },
        },
        structure: {
          label: "Sentence Structure",
          choices: {
            shorter: "Shorten the sentences",
            longer: "Lengthen the sentences",
            vary: "Vary the sentence structure",
            standardize: "Standardize the sentence structure",
            more_complex: "Use more complex sentences",
            simpler: "Use simpler sentences",
            active_voice: "Increase the use of active voice",
            passive_voice: "Increase the use of passive voice",
          },
        },
        figurative: {
          label: "Figurative Language",
          choices: {
            more_figurative: "Use more figurative language",
            less_figurative: "Reduce the use of figurative language",
            metaphors: "Add more metaphors and similes",
            literal: "Use more literal language",
            more_idioms: "Incorporate more idioms",
            less_idioms: "Minimize the use of idioms",
            more_symbolism: "Enhance the use of symbolism",
            less_symbolism: "Reduce the use of symbolism",
          },
        },
        conciseness: {
          label: "Conciseness",
          choices: {
            more_concise: "Make this more concise",
            more_wordy: "Make this more wordy",
            remove_redundant: "Eliminate redundant phrases",
            add_details: "Add more detailed explanations",
            reduce_filler: "Reduce filler words",
            add_elaboration: "Include more elaboration",
          },
        },
        imagery: {
          label: "Imagery and Sensory Details",
          choices: {
            enhance_imagery: "Enhance the imagery",
            simplify_imagery: "Simplify the imagery",
            vivid_descriptions: "Use more vivid descriptions",
            straightforward_descriptions:
              "Use more straightforward descriptions",
            more_visual: "Incorporate more visual details",
            less_visual: "Focus less on visual details",
          },
        },
        paragraph: {
          label: "Paragraph and Text Structure",
          choices: {
            shorter_paragraphs: "Make the paragraphs shorter",
            longer_paragraphs: "Make the paragraphs longer",
            break_sections: "Break text into smaller sections",
            combine_sections: "Combine sections for a smoother flow",
            more_lists: "Use more bullet points and lists",
            more_continuous: "Use more continuous text",
            vary_paragraphs: "Increase paragraph variety",
            consistent_length: "Maintain consistent paragraph length",
          },
        },
        content_length_legal_memo: {
          label: "Content and length, legal memo",
          choices: {
            extend_memo: "Make the memo more comprehensive",
            summarize_memo: "Make the memo more concise",
            expand_analysis: "Expand the legal analysis",
            deepen_case_law: "Add more case law references",
            add_statutory_references: "Add more statutory references",
            add_conclusion: "Strengthen the conclusion",
            add_recommendations: "Add practical recommendations",
            add_risk_assessment: "Include risk assessment",
            add_executive_summary: "Add an executive summary",
          },
        },
        content_length_legal_document: {
          label: "Content and length, legal document",
          choices: {
            extend_document: "Make the document more comprehensive",
            shorten_document: "Make the document more concise",
            add_clauses: "Add more protective clauses",
            simplify_clauses: "Simplify complex clauses",
            add_definitions: "Add more defined terms",
            expand_scope: "Expand the scope section",
            strengthen_warranties: "Strengthen warranties and representations",
            enhance_remedies: "Enhance remedies section",
            add_boilerplate: "Add standard boilerplate provisions",
            add_schedules: "Add or expand schedules/exhibits",
          },
        },
        other: {
          label: "Other Aspects",
          choices: {
            replace_context:
              "Replace references to CONTEXT to the actual name of the source",
            add_numbering: "Add numbering of paragraphs",
            remove_numbering: "Remove numbering of paragraphs",
            extend_statutories: "Extend the text on statutories",
            reduce_statutories: "Reduce the text on statutories",
            extend_jurisprudence: "Extend the text on jurisprudence",
            reduce_jurisprudence: "Reduce the text on jurisprudence",
          },
        },
      },
    },
    "prompt-step": {
      title: "Select Prompt",
      description: "Choose how you want to enhance the answer",
    },
    actions: {
      next: "Next",
      back: "Back",
      upgrade: "Upgrade Answer",
      cancel: "Cancel",
    },
    "text-upgrade-prompt":
      "Upgrade the original prompt to fit for professional legal prompting. The text to upgrade is: {{prompt}}",
  },
};
