const TRANSLATIONS = {
  "text-edit": {
    toolbar: {
      bold: "Text fett formatieren",
      italic: "Text kursiv formatieren",
      underline: "Text unterstreichen",
      strikethrough: "Text durchstreichen",
      code: "Als Code formatieren",
      "heading-1": "Als große Überschrift formatieren",
      "heading-2": "Als mittlere Überschrift formatieren",
      "heading-3": "Als kleine Überschrift formatieren",
      "bullet-list": "Aufzählungsliste erstellen",
      "numbered-list": "Nummerierte Liste erstellen",
      blockquote: "Als Blockzitat formatieren",
      "add-link": "<PERSON> hinzufügen",
      "remove-link": "Link entfernen",
      "insert-table": "Tabelle einfügen",
      "text-color": "Textfarbe ändern",
      highlight: "Text hervorheben",
      undo: "Letzte Aktion rückgängig machen",
      redo: "Letzte Aktion wiederholen",
    },
    modals: {
      link: {
        title: "<PERSON> hinzufügen",
        "url-label": "URL",
        "url-placeholder": "https://example.com",
        cancel: "Abbrechen",
        add: "<PERSON> hinzufügen",
      },
      "text-color": {
        title: "Textfarbe festlegen",
        "color-label": "Farbe wählen",
        "custom-color": "Benutzerdefinierte Farbe",
        "color-placeholder": "#ff0000, rot, rgb(255,0,0)",
        "color-help":
          "Geben Sie eine Farbe in Hex (#ff0000), RGB (rgb(255,0,0)) oder Namen (rot) ein",
        cancel: "Abbrechen",
        apply: "Farbe anwenden",
      },
      highlight: {
        title: "Hervorhebungsfarbe festlegen",
        "color-label": "Hervorhebungsfarbe wählen",
        "custom-color": "Benutzerdefinierte Hervorhebungsfarbe",
        "color-placeholder": "#ffff00, gelb, rgb(255,255,0)",
        "color-help":
          "Geben Sie eine Farbe in Hex (#ffff00), RGB (rgb(255,255,0)) oder Namen (gelb) ein",
        cancel: "Abbrechen",
        apply: "Hervorhebung anwenden",
      },
    },
    errors: {
      "invalid-url":
        "Ungültige URL. Bitte geben Sie eine gültige Webadresse ein.",
      "invalid-color":
        "Ungültige Farbe. Bitte geben Sie eine gültige CSS-Farbe ein.",
      "dangerous-url": "Diese URL ist aus Sicherheitsgründen nicht erlaubt.",
      "dangerous-color": "Diese Farbe enthält potenziell schädliche Inhalte.",
    },
    colors: {
      select: "Auswählen",
      highlight: "hervorhebung",
      "no-highlight": "Keine Hervorhebung",
      black: "Schwarz",
      "dark-gray": "Dunkelgrau",
      gray: "Grau",
      "light-gray": "Hellgrau",
      red: "Rot",
      orange: "Orange",
      amber: "Bernstein",
      yellow: "Gelb",
      lime: "Limette",
      green: "Grün",
      emerald: "Smaragd",
      teal: "Blaugrün",
      cyan: "Cyan",
      sky: "Himmel",
      blue: "Blau",
      indigo: "Indigo",
      violet: "Violett",
      purple: "Lila",
      fuchsia: "Fuchsia",
      pink: "Rosa",
      rose: "Rose",
    },
  },
};

export default TRANSLATIONS;
