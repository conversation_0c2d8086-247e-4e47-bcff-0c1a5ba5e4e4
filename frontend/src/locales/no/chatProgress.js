export default {
  // =========================
  // PROGRESS
  // =========================
  chatProgress: {
    title: "Behandler",
    processing: "Behandler...",
    step: "Steg",
    timeLeft: "Tid igjen",
    details: "Detaljer",
    abort: "Avbryt",
    modalTitle: "Fremdriftsdetaljer",
    "close-msg": "Er du sikker på at du vil avbryte prosessen?",
    noThreadSelected: "Ingen tråd valgt",
    noActiveProgress: "Ingen aktiv fremgang",
    of: "av",
    started: "Startet",
    error: {
      title: "Det oppstod en feil under behandling av forespørselen din.",
      description:
        "Vennligst prøv igjen eller kontakt support hvis problemet vedvarer.",
      retry: "Prøv igjen",
      dismiss: "Lukk",
      showDetails: "Vis tekniske detaljer",
      hideDetails: "Skjul tekniske detaljer",
    },
    cancelled: "Prosessen ble avbrutt.",
    types: {
      main: {
        step1: {
          label: "Genererer seksjonsliste",
          desc: "Bruker hoveddokumentet for å lage en innledende struktur.",
        },
        step2: {
          label: "Behandler dokumenter",
          desc: "Genererer beskrivelser og sjekker relevans.",
        },
        step3: {
          label: "Kobler dokumenter til seksjoner",
          desc: "Tildeler relevante dokumenter til hver seksjon.",
        },
        step4: {
          label: "Identifiserer juridiske problemer",
          desc: "Trekker ut sentrale juridiske problemer for hver seksjon.",
        },
        step5: {
          label: "Genererer juridiske memoer",
          desc: "Lager juridiske memoranda for de identifiserte problemene.",
        },
        step6: {
          label: "Utarbeider seksjoner",
          desc: "Komponerer innholdet for hver enkelt seksjon.",
        },
        step7: {
          label: "Kombinerer & fullfører dokument",
          desc: "Setter sammen seksjoner til det endelige dokumentet.",
        },
      },
      noMain: {
        step1: {
          label: "Behandler dokumenter",
          desc: "Genererer beskrivelser for alle opplastede filer.",
        },
        step2: {
          label: "Genererer seksjonsliste",
          desc: "Lager en strukturert liste over seksjoner fra dokumentsammendrag.",
        },
        step3: {
          label: "Fullfører dokumentkobling",
          desc: "Bekrefter dokumentenes relevans for hver planlagte seksjon.",
        },
        step4: {
          label: "Identifiserer juridiske problemer",
          desc: "Trekker ut sentrale juridiske problemer for hver seksjon.",
        },
        step5: {
          label: "Genererer juridiske memoer",
          desc: "Lager juridiske memoranda for de identifiserte problemene.",
        },
        step6: {
          label: "Utarbeider seksjoner",
          desc: "Komponerer innholdet for hver enkelt seksjon.",
        },
        step7: {
          label: "Kombinerer & fullfører dokument",
          desc: "Setter sammen alle seksjoner til det endelige juridiske dokumentet.",
        },
      },
      referenceFiles: {
        step1: {
          label: "Behandler referansefiler",
          desc: "Behandler referansefiler",
        },
        step2: {
          label: "Behandler gjennomgangsfiler",
          desc: "Behandler gjennomgangsfiler",
        },
        step3: {
          label: "Genererer seksjonsliste",
          desc: "Genererer seksjonsliste",
        },
        step4: {
          label: "Utarbeider seksjoner",
          desc: "Utarbeider seksjoner",
        },
        step5: {
          label: "Genererer rapport",
          desc: "Genererer rapport",
        },
      },
      documentDrafting: {
        step1: {
          label: "Forbereder dokumenter",
          desc: "Samler og forbereder alle relevante dokumenter for utarbeidelsesprosessen.",
        },
        step2: {
          label: "Analyserer innhold",
          desc: "Analyserer dokumentinnhold og identifiserer viktige juridiske problemer og klausuler.",
        },
        step3: {
          label: "Genererer utkast",
          desc: "Komponerer det endelige dokumentutkastet basert på analysen.",
        },
      },
    },
  },

  // =========================
  // PROGRESS TYPES
  // =========================
  ddProgress: {
    title: "Dokumentutkast",
  },
  cdbProgress: {
    title: "Kompleks Dokumentgenerator",
    general: {
      placeholderSubTask: "Behandler element {{index}}...",
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Bekreft avbrudd",
    confirm_abort_description: "Er du sikker på at du vil avbryte prosessen?",
    keep_running: "Fortsett kjøring",
    abort_run: "Avbryt prosess",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Fremdrift for svargenerering",
    description:
      "Viser sanntidsfremdrift for oppgaver for å fullføre prompt, avhengig av kobling til andre arbeidsområder og filstørrelse. Modalen lukkes automatisk når alle trinn er fullført.",
    step_fetching_memos: "Henter juridiske data om aktuelle emner",
    step_processing_chunks: "Behandler opplastede dokumenter",
    step_combining_responses: "Fullfører svar",
    sub_step_chunk_label: "Behandler dokumentgruppe {{index}}",
    sub_step_memo_label: "Hentet juridiske data fra {{workspaceSlug}}",
    placeholder_sub_task: "Deloppgave i kø",
    desc_fetching_memos:
      "Henter relevante juridiske opplysninger fra tilknyttede arbeidsområder",
    desc_processing_chunks:
      "Analyserer og trekker ut informasjon fra dokumentgrupper",
    desc_combining_responses: "Syntetiserer informasjon til et omfattende svar",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Juridisk oppgave kjører i bakgrunnen.",
    dd: "Dokumentutkast fortsetter i bakgrunnen.",
    reopen: "Åpne statusvindu",
  },
};
