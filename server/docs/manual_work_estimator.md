# Manual Work Estimator Documentation

## Overview

The Manual Work Estimator provides administrators with insights into the complexity and time requirements of AI-generated legal responses by estimating equivalent manual work hours.

## Architecture

### Backend Implementation

- **Endpoint**: `POST /system/manual-work-estimate`
- **Location**: `server/endpoints/system.js`
- **Function**: `EstimateManualWork(question, answer)`

### LLM Provider Handling

The system automatically detects and formats messages for different LLM providers with boosted context window:

```javascript
// Calculate boosted context window (adds 5% to current setting, capped at 100%)
const currentContextPercentageRaw =
  await SystemSettings.getDynamicContextSettings("");
const currentContextPercentage = Number(currentContextPercentageRaw) || 0;
const boostedContextPercentage = Math.min(currentContextPercentage + 5, 100);
const baseContextWindow = LLMConnector.promptWindowLimit();
const boostedContextWindow = Math.floor(
  baseContextWindow * (boostedContextPercentage / 100)
);

// Provider-specific message formatting with boosted context
if (resolvedProvider === "gemini") {
  messages = [{ role: "user", content: `${systemPrompt}\n\n${content}` }];
} else if (resolvedProvider === "anthropic") {
  messages = [
    { role: "system", content: systemPrompt },
    { role: "user", content: content },
  ];
} else {
  messages = await LLMConnector.compressMessages({
    systemPrompt,
    userPrompt: content,
    maxAllowedTokens: boostedContextWindow,
  });
}
```

### Response Processing

The system handles various LLM response formats:

```javascript
const textResponse =
  typeof estimate === "string" ? estimate : estimate.textResponse || estimate;
```

## Frontend Implementation

### Component Structure

```
ManualWorkEstimator/
├── index.jsx          # Main component
└── README.md          # Component documentation
```

### State Management

```javascript
const [isLoading, setIsLoading] = useState(false);
const [isOpen, setIsOpen] = useState(false);
const [result, setResult] = useState(null);
const [showPrompt, setShowPrompt] = useState(false);
```

### Integration Pattern

```javascript
// In Actions component
{
  role === "assistant" && isLegalQA && user?.role === "admin" && (
    <ManualWorkEstimator question={question} answer={response} />
  );
}
```

## Customization Guide

### Modifying the Estimation Prompt

The estimation prompt can be customized to focus on different aspects of manual work estimation:

#### Current Default Prompt

```javascript
const systemPrompt =
  "Estimate the manual working hours required for a human to produce the provided system response based on the user question.";
```

#### Enhanced Prompt Examples

**Detailed Legal Analysis Prompt**:

```javascript
const systemPrompt = `
Analyze the provided legal question and AI-generated response to estimate the manual working hours a qualified legal professional would require to produce equivalent work.

Consider the following factors:
1. Research time (case law, statutes, regulations)
2. Analysis and reasoning time
3. Writing and formatting time
4. Review and quality assurance time
5. Complexity of legal issues involved

Provide your estimate in hours and include a brief breakdown of time allocation across these categories.
`;
```

**Cost-Focused Prompt**:

```javascript
const systemPrompt = `
Estimate the billable hours a legal professional would charge for producing the provided response to the given question.

Consider:
- Junior associate time for research
- Senior associate time for analysis
- Partner time for review
- Administrative time for formatting

Provide both total hours and a breakdown by professional level.
`;
```

**Complexity-Based Prompt**:

```javascript
const systemPrompt = `
Evaluate the complexity of the legal question and response to estimate manual work time.

Rate complexity factors:
- Legal research depth required (1-5 scale)
- Number of legal areas involved
- Citation and precedent analysis needed
- Writing complexity and structure

Provide time estimate with complexity justification.
`;
```

### Implementing Custom Prompts

1. **Direct Code Modification**:

   ```javascript
   // In server/endpoints/system.js, EstimateManualWork function
   const systemPrompt = "Your custom prompt here...";
   ```

2. **Environment Variable Approach**:

   ```javascript
   const systemPrompt =
     process.env.MANUAL_WORK_ESTIMATE_PROMPT || "Default estimation prompt...";
   ```

3. **Database Configuration**:
   ```javascript
   const promptSetting = await SystemSettings.get({
     label: "manual_work_estimate_prompt",
   });
   const systemPrompt = promptSetting?.value || "Default prompt...";
   ```

### Response Format Customization

The system can be modified to expect specific response formats:

```javascript
// Example: Structured JSON response
const systemPrompt = `
Estimate manual work hours and respond in JSON format:
{
  "totalHours": number,
  "breakdown": {
    "research": number,
    "analysis": number,
    "writing": number,
    "review": number
  },
  "complexity": "low|medium|high",
  "justification": "explanation"
}
`;
```

## Error Handling

### Common Issues and Solutions

1. **LLM Provider Errors**:

   - Automatic fallback to default provider
   - Graceful error messages to user
   - Logging for debugging

2. **Response Format Issues**:

   - Type checking for response objects
   - Text extraction from various formats
   - Fallback to error message

3. **Access Control**:
   - Role verification before API calls
   - Module type checking
   - Message type validation

## Monitoring and Analytics

### Logging Events

The system logs estimation requests for monitoring:

```javascript
await EventLogs.logEvent("manual_work_estimate_requested", {
  userId: user.id,
  questionLength: question.length,
  answerLength: answer.length,
  provider: resolvedProvider,
  model: LLMConnector.model,
});
```

### Performance Metrics

Consider tracking:

- Average estimation time
- LLM provider success rates
- User adoption metrics
- Error frequency

## Security Considerations

### Access Control Matrix

| User Role | Feature Access | Prompt Display | Legal QA | Document Drafting | Other Modules |
| --------- | -------------- | -------------- | -------- | ----------------- | ------------- |
| Admin     | ✅             | ✅             | ✅       | ❌                | ❌            |
| Manager   | ✅             | ✅             | ✅       | ❌                | ❌            |
| User      | ✅             | ❌             | ✅       | ❌                | ❌            |

### Data Protection

- No storage of estimation results
- Temporary processing only
- Input sanitization
- Error message sanitization

## API Reference

### Request Format

```javascript
POST /system/manual-work-estimate
Content-Type: application/json

{
  "question": "User's legal question",
  "answer": "AI-generated response"
}
```

### Response Format

```javascript
{
  "success": true,
  "result": {
    "textResponse": "Estimation result text",
    "prompt": {
      "systemPrompt": "System prompt used",
      "userContent": "Formatted user content",
      "messages": [...],
      "provider": "openai",
      "model": "gpt-4"
    }
  }
}
```

### Error Response

```javascript
{
  "success": false,
  "error": "Error message"
}
```

## Testing

### Unit Tests

```javascript
// Example test structure
describe("Manual Work Estimator", () => {
  test("should require admin role", () => {
    // Test access control
  });

  test("should handle LLM errors gracefully", () => {
    // Test error handling
  });

  test("should format prompts correctly", () => {
    // Test prompt formatting
  });
});
```

### Integration Tests

- End-to-end workflow testing
- LLM provider compatibility
- Error scenario handling
- Access control verification

## Future Enhancements

### Planned Features

1. **Historical Tracking**: Store estimation results for analytics
2. **Custom Prompts**: Admin interface for prompt management
3. **Batch Processing**: Estimate multiple responses at once
4. **Cost Calculation**: Integration with hourly rate settings
5. **Export Functionality**: CSV/PDF reports of estimations

### Integration Opportunities

- Time tracking systems
- Billing software
- Analytics dashboards
- Performance monitoring tools

## Troubleshooting

### Common Problems

1. **"[object Object]" displayed instead of text**:

   - Check LLM response format handling
   - Ensure proper text extraction

2. **Icon import errors**:

   - Use `react-icons/bs` instead of `lucide-react`
   - Check import statements

3. **Access control not working**:

   - Verify user role checking
   - Check workspace type detection

4. **Modal not displaying**:
   - Check state management
   - Verify modal component integration

### Debug Steps

1. Check browser console for errors
2. Verify API endpoint responses
3. Test with different LLM providers
4. Validate user permissions
5. Check translation key availability
