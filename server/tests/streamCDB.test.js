// Tests for streamChatWithWorkspaceCDB phases
const EventEmitter = require("events");
// Mock heavy dependencies up front
jest.mock("../utils/helpers/tiktoken", () => {
  class FakeTokenManager {
    countFromString(str = "") {
      return str.split(/\s+/).filter(Boolean).length;
    }
  }
  return { TokenManager: FakeTokenManager };
});
// Mock getLLMProvider more flexibly for relevance checks
const mockGetChatCompletion = jest.fn();
jest.doMock("../utils/helpers", () => ({
  getLLMProvider: () => ({
    compressMessages: jest.fn(async (messages) => messages),
    getChatCompletion: mockGetChatCompletion, // Use the hoisted mock
    defaultTemp: 0.7,
    model: "mock-model",
    metrics: { lastCompletionTokens: 0 },
    promptWindowLimit: jest.fn(() => 128000),
  }),
}));

// Enhanced mock for SystemSettings
const mockSystemSettingsGet = jest.fn();

jest.doMock("../models/systemSettings", () => ({
  SystemSettings: {
    get: mockSystemSettingsGet, // Use the hoisted mock for .get
    // Keep other mocks if they were specifically needed by other parts of SystemSettings
    // For instance, if the flow calls other SystemSettings static methods:
    getValueOrFallback: jest.fn(async (clause, fallback) => {
      const result = await mockSystemSettingsGet(clause);
      return result?.value ?? fallback;
    }),
    // Add any other SystemSettings methods that might be called by the flows if not just .get
  },
}));
jest.doMock("../models/workspace", () => ({
  Workspace: {
    where: jest.fn().mockResolvedValue([]),
    get: jest.fn().mockResolvedValue(null),
  },
}));
jest.doMock("../models/workspaceChats", () => ({
  WorkspaceChats: {
    new: jest.fn().mockResolvedValue({ chat: { id: 1 } }),
  },
}));
jest.doMock("../utils/files", () => ({
  purgeDocumentBuilder: jest.fn().mockReturnValue(0),
}));

const mockGenerateLegalMemo = jest.fn().mockResolvedValue({
  memo: "mock memo",
  tokenCount: 5,
  sources: [],
  tokenUsage: {},
});

jest.doMock("../utils/helpers/legalMemo", () => ({
  generateLegalMemo: mockGenerateLegalMemo,
}));
// Mock fs interactions to avoid real file IO
jest.doMock("fs", () => {
  const actual = jest.requireActual("fs");
  return {
    ...actual,
    existsSync: jest.fn().mockReturnValue(true),
    readdirSync: jest.fn().mockReturnValue([]),
    readFileSync: jest.fn().mockReturnValue(
      JSON.stringify({
        pageContent: "dummy content for readFileSync mock", // Make it distinct
        token_count_estimate: 10,
        metadata: { title: "dummyTitleFromReadFileSync" },
      })
    ),
    mkdirSync: jest.fn(),
    writeFileSync: jest.fn(),
  };
});
// After mocks setup, import the function under test
const { streamChatWithWorkspaceCDB } = require("../utils/chats/streamCDB");
// Increase default Jest timeout in case asynchronous steps still take time
jest.setTimeout(10000);

// Original default prompts for comparison
const originalDefaultPrompts = require("../utils/chats/prompts/legalDrafting");
describe("streamChatWithWorkspaceCDB", () => {
  beforeEach(() => {
    mockGetChatCompletion.mockReset();
    mockSystemSettingsGet.mockReset();
    mockGenerateLegalMemo.mockClear(); // Clear this specific mock as well

    // Default behavior for SystemSettings.get: no custom prompts found
    mockSystemSettingsGet.mockImplementation(async ({ label }) => {
      // console.log(`[Test SystemSettings.get Mock] Default call with label: ${label}`);
      return Promise.resolve(null);
    });

    // Default behavior for LLM (throw if not specifically mocked by mockResolvedValueOnce for a step)
    mockGetChatCompletion.mockImplementation(async (messages) => {
      // console.log("[Test LLM Mock] Fallback getChatCompletion called with:", messages);
      // This can be a generic response or an error depending on test needs.
      // For prompt testing, sometimes we just need to inspect 'messages'.
      return {
        textResponse: "Fallback LLM response",
        metrics: { lastCompletionTokens: 5 },
      };
    });
  });

  test("emits progress events for steps 1-7 and finalizes response for main flow", async () => {
    const request = new EventEmitter();
    const chunks = [];
    const response = {
      write: (data) => {
        const strData = data.toString();
        const cleanedStr = strData.startsWith("data: ")
          ? strData.substring(6).trim()
          : strData.trim();
        if (cleanedStr) {
          try {
            if (
              (cleanedStr.startsWith("{") && cleanedStr.endsWith("}")) ||
              (cleanedStr.startsWith("[") && cleanedStr.endsWith("]"))
            ) {
              const parsedChunk = JSON.parse(cleanedStr);
              chunks.push(parsedChunk);
            }
          } catch (e) {
            console.error(
              "[TEST] Failed to parse JSON chunk:",
              cleanedStr,
              "Error:",
              e
            );
          }
        }
      },
      on: jest.fn(),
      removeListener: jest.fn(),
    };
    const workspace = { id: 1, slug: "test-slug", type: "document-drafting" };
    const mockChatId = "mock-chat-id-main-flow-123";
    const mockCdbOptions = [
      "Legal Task Main Flow",
      "Custom Instructions Main",
      "mainDoc.pdf",
    ];
    const mockLegalTaskConfig = { flowType: "main" };
    const MOCK_FILES = ["doc1.json", "mainDoc.pdf.json", "anotherDoc.json"];
    const fs = require("fs");
    fs.readdirSync.mockReturnValue(MOCK_FILES);
    fs.readFileSync.mockImplementation((filePath) => {
      // For final document .md files, return the expected markdown content
      if (filePath.includes("final-document-") && filePath.endsWith(".md")) {
        return "## Main Doc Section 1\n\nDrafted content for Section 1.";
      }
      // For JSON files, return the standard JSON format
      return JSON.stringify({
        metadata: { title: filePath.split("/").pop().replace(".json", "") },
        pageContent: "Content for " + filePath,
      });
    });
    // Specific mocks for LLM calls within the flow
    // 1. Generate Section List (from mainDoc.pdf content) – occurs before doc processing
    mockGetChatCompletion
      .mockResolvedValueOnce({
        textResponse: JSON.stringify([
          { index_number: 1, title: "Main Doc Section 1" },
        ]),
        metrics: { lastCompletionTokens: 20 },
      })
      // 2-4. Document Descriptions (called for each of MOCK_FILES)
      .mockResolvedValueOnce({
        textResponse: "Description for doc1",
        metrics: { lastCompletionTokens: 10 },
      })
      .mockResolvedValueOnce({
        textResponse: "Description for mainDoc.pdf",
        metrics: { lastCompletionTokens: 10 },
      })
      .mockResolvedValueOnce({
        textResponse: "Description for anotherDoc",
        metrics: { lastCompletionTokens: 10 },
      })
      // 5-7. Document Relevance (mainDoc relevance is NOT an LLM call)
      .mockResolvedValueOnce({
        textResponse: "true",
        metrics: { lastCompletionTokens: 5 },
      })
      // Relevance for mainDoc.pdf is skipped by code, so its previous mock is removed.
      .mockResolvedValueOnce({
        textResponse: "false",
        metrics: { lastCompletionTokens: 5 },
      })
      // 8. Map other documents (doc1) to sections
      .mockResolvedValueOnce({
        textResponse: JSON.stringify([1]),
        metrics: { lastCompletionTokens: 5 },
      })
      // 9. Identify Legal Issues for section 1 - THIS WILL BECOME THE DRAFT CONTENT DUE TO SHIFT
      .mockResolvedValueOnce({
        textResponse: "Drafted content for Section 1.",
        metrics: { lastCompletionTokens: 15 },
      })
      // 10. Generate Legal Memo (is a separate mock)
      // 11. Draft Section 1 - THIS WILL CONSUME THE LEGAL ISSUES MOCK
      .mockResolvedValueOnce({
        textResponse: JSON.stringify([{ Issue: "Legal Issue A for Sec 1" }]),
        metrics: { lastCompletionTokens: 40 },
      });
    // Mock for Combine Sections is removed as it's not used by code for the final string.

    await streamChatWithWorkspaceCDB(
      request,
      response,
      workspace,
      "Main Flow Legal Task",
      "chat",
      { id: 1, role: "admin" },
      null,
      [],
      mockChatId,
      false,
      false,
      "",
      null,
      "default",
      false,
      null,
      false,
      mockCdbOptions,
      mockLegalTaskConfig
    );

    const cdbProgressEvents = chunks.filter((c) => c.type === "cdbProgress");
    // console.log(
    //   "[DEBUG] streamCDB.test.js - all cdbProgressEvents for main flow:",
    //   JSON.stringify(cdbProgressEvents, null, 2)
    // );
    expect(cdbProgressEvents.length).toBeGreaterThanOrEqual(7);

    const uniqueProgressSteps = [
      ...new Set(cdbProgressEvents.map((e) => e.step)),
    ].sort((a, b) => a - b);

    for (let i = 1; i <= 7; i++) {
      expect(uniqueProgressSteps).toContain(i);
    }
    cdbProgressEvents.forEach((event) => expect(event.flowType).toBe("main"));

    // Find the chunk containing the actual document content
    // It might be parsed as type: "text" or type: "textResponseChunk" by the mock `response.write`
    // and will have close: false initially.
    const documentContentChunk = chunks.find(
      (c) =>
        (c.type === "text" || c.type === "textResponseChunk") &&
        typeof c.textResponse === "string" &&
        c.close === false
    );
    expect(documentContentChunk).toBeDefined();
    expect(documentContentChunk.textResponse).toBe(
      "## Main Doc Section 1\n\nDrafted content for Section 1."
    );

    // Find the separate finalization chunk
    const finalizationChunk = chunks.find(
      (c) => c.type === "finalizeResponseStream" && c.close === true
    );
    expect(finalizationChunk).toBeDefined();
    expect(finalizationChunk.error).toBe(false);

    // Regression: Check if writeFileSync was called with paths for the main flow
    const writeFileSyncCalls = fs.writeFileSync.mock.calls;
    expect(writeFileSyncCalls.length).toBeGreaterThan(0);

    writeFileSyncCalls.forEach((call) => {
      const filePath = call[0];
      // All temporary files should include the chatId in their path or name and be in document-builder directory
      // This regex handles both Windows and Unix path separators
      expect(filePath).toMatch(
        new RegExp(
          `.*[/\\\\]storage[/\\\\]document-builder[/\\\\].*${mockChatId}.*`
        )
      );
    });

    fs.readdirSync.mockClear();
    fs.readFileSync.mockClear();
    fs.writeFileSync.mockClear();
    fs.mkdirSync.mockClear();
  });

  test("emits progress events for steps 1-7 and finalizes response for noMain flow", async () => {
    const request = new EventEmitter();
    const chunks = [];
    const response = {
      write: (data) => {
        const strData = data.toString();
        const cleanedStr = strData.startsWith("data: ")
          ? strData.substring(6).trim()
          : strData.trim();
        if (cleanedStr) {
          try {
            if (
              (cleanedStr.startsWith("{") && cleanedStr.endsWith("}")) ||
              (cleanedStr.startsWith("[") && cleanedStr.endsWith("]"))
            ) {
              const parsedChunk = JSON.parse(cleanedStr);
              chunks.push(parsedChunk);
            }
          } catch (e) {
            /* console.error for debug */
          }
        }
      },
      on: jest.fn(),
      removeListener: jest.fn(),
    };
    const workspaceNoMain = {
      id: 2,
      slug: "no-main-slug",
      type: "document-drafting",
    };
    const mockChatIdNoMain = "mock-chat-id-no-main-456";
    const mockCdbOptionsNoMain = [
      "Legal Task NoMain",
      "Custom NoMain Instructions",
      null,
    ];
    const mockLegalTaskConfigNoMain = { flowType: "noMain" };

    const MOCK_FILES_NO_MAIN = ["report.json", "data.json"];
    require("fs").readdirSync.mockReturnValue(MOCK_FILES_NO_MAIN);
    require("fs").readFileSync.mockImplementation((filePath) => {
      // For final document .md files, return the expected markdown content
      if (filePath.includes("final-document-") && filePath.endsWith(".md")) {
        return "## NoMain Section 1\n\nDrafted content for NoMain Section 1.";
      }
      // For JSON files, return the standard JSON format
      return JSON.stringify({
        metadata: { title: filePath.split("/").pop().replace(".json", "") },
        pageContent: "Content for " + filePath,
      });
    });

    mockGetChatCompletion.mockReset();
    // 1. Descriptions (2 files)
    mockGetChatCompletion
      .mockResolvedValueOnce({
        textResponse: "Description for report",
        metrics: { lastCompletionTokens: 10 },
      })
      .mockResolvedValueOnce({
        textResponse: "Description for data",
        metrics: { lastCompletionTokens: 10 },
      });
    // 2. Generate Section List (from summaries)
    mockGetChatCompletion.mockResolvedValueOnce({
      textResponse: JSON.stringify([
        {
          index_number: 1,
          title: "NoMain Section 1",
          relevant_documents: ["report"],
        },
      ]),
      metrics: { lastCompletionTokens: 20 },
    });
    // 3. Identify Legal Issues (Step 4 in noMainDoc.js)
    mockGetChatCompletion.mockResolvedValueOnce({
      textResponse: JSON.stringify([{ Issue: "Legal Issue B for Sec 1" }]),
      metrics: { lastCompletionTokens: 15 },
    });
    // 4. Generate Legal Memo (Step 5 in noMainDoc.js)
    const { generateLegalMemo } = require("../utils/helpers/legalMemo");
    generateLegalMemo.mockResolvedValueOnce({
      memo: "Memo for Issue B",
      tokenCount: 30,
      sources: [],
    });
    // 5. Draft Section (Step 6 in noMainDoc.js)
    mockGetChatCompletion.mockResolvedValueOnce({
      textResponse: "Drafted content for NoMain Section 1.",
      metrics: { lastCompletionTokens: 40 },
    });
    // Mock for Combine Sections is removed.

    await streamChatWithWorkspaceCDB(
      request,
      response,
      workspaceNoMain,
      "NoMain Flow Legal Task",
      "chat",
      { id: 1, role: "admin" },
      null,
      [],
      mockChatIdNoMain,
      false,
      false,
      "",
      null,
      "default",
      false,
      null,
      false,
      mockCdbOptionsNoMain,
      mockLegalTaskConfigNoMain
    );
    const cdbProgressEvents = chunks.filter((c) => c.type === "cdbProgress");
    const uniqueProgressSteps = [
      ...new Set(
        cdbProgressEvents.map((c) => c.step).filter((s) => s !== undefined)
      ),
    ].sort((a, b) => a - b);

    for (let i = 1; i <= 7; i++) {
      expect(uniqueProgressSteps).toContain(i);
    }
    cdbProgressEvents.forEach((event) => expect(event.flowType).toBe("noMain"));

    // Find the chunk containing the actual document content
    const documentContentChunkNoMain = chunks.find(
      (c) =>
        (c.type === "text" || c.type === "textResponseChunk") &&
        typeof c.textResponse === "string" &&
        c.close === false
    );
    expect(documentContentChunkNoMain).toBeDefined();
    expect(documentContentChunkNoMain.textResponse).toBe(
      "## NoMain Section 1\n\nDrafted content for NoMain Section 1."
    );

    // Find the separate finalization chunk
    const finalizationChunkNoMain = chunks.find(
      (c) => c.type === "finalizeResponseStream" && c.close === true
    );
    expect(finalizationChunkNoMain).toBeDefined();
    expect(finalizationChunkNoMain.error).toBe(false);
  });

  describe("Prompt Resolution with promptManager", () => {
    const workspace = {
      id: 990,
      slug: "prompt-res-ws",
      type: "document-drafting",
    };
    const user = { id: 10, role: "admin" };
    const baseFlowOptions = {
      request: new EventEmitter(),
      response: {
        write: jest.fn(),
        on: jest.fn(),
        removeListener: jest.fn(),
        end: jest.fn(),
      }, // Added end mock
      workspace,
      user,
      thread: null,
      attachments: [],
      isCanvasChat: false,
      preventChatCreation: false,
      settings_suffix: "",
      invoice_ref: null,
      vectorSearchMode: "default",
      hasUploadedFile: false,
      displayMessage: null,
      useDeepSearch: false,
    };

    beforeEach(() => {
      // Reset response write mock for each test in this sub-suite
      baseFlowOptions.response.write.mockClear();
      const fs = require("fs");
      fs.readdirSync.mockClear().mockReturnValue(["doc1.json"]);
      fs.readFileSync.mockClear().mockImplementation((filePath) => {
        const fileName = filePath.split(/[/\\]/).pop().replace(".json", "");
        return JSON.stringify({
          metadata: { title: fileName },
          pageContent: `Content for ${fileName}`,
          token_count_estimate: 15,
        });
      });
      fs.writeFileSync.mockClear();
    });

    test("should use default prompts when no custom SystemSettings exist", async () => {
      const testChatId = "default-prompts-test-chat-id";
      const legalTask = "Test Task - Default Prompts";
      const cdbOptions = [legalTask, "", null]; // No main doc, no custom instructions
      const legalTaskConfig = { flowType: "noMain" };

      // Mock LLM for document description (first LLM call in noMainDoc flow)
      mockGetChatCompletion.mockImplementationOnce(async (messages) => {
        // console.log("[Default Test] LLM for Doc Desc called with:", messages);
        expect(messages.systemPrompt).toEqual(
          originalDefaultPrompts.DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT
        );

        // USER_PROMPT is a template, so check if the core part matches
        const userPromptTemplate =
          originalDefaultPrompts.DEFAULT_DOCUMENT_SUMMARY.USER_PROMPT;
        const expectedUserPromptStart = userPromptTemplate.substring(
          0,
          userPromptTemplate.indexOf("{{task}}")
        );
        expect(messages.userPrompt).toContain(expectedUserPromptStart);
        expect(messages.userPrompt).toContain(legalTask); // Task should be in the prompt
        expect(messages.userPrompt).toContain("Content for doc1"); // Content from mocked doc
        return {
          textResponse: "Summary for doc1 (default test)",
          metrics: { lastCompletionTokens: 10 },
        };
      });
      // Mock subsequent LLM calls for the flow to run without erroring on unexpected calls
      mockGetChatCompletion.mockResolvedValueOnce({
        // Section List from Summaries
        textResponse: JSON.stringify([
          {
            index_number: 1,
            title: "Default Section 1",
            relevant_documents: ["doc1"],
          },
        ]),
      });
      mockGetChatCompletion.mockResolvedValueOnce({
        // Identify Legal Issues
        textResponse: JSON.stringify([{ Issue: "Default Legal Issue" }]),
      });
      mockGenerateLegalMemo.mockResolvedValueOnce({
        memo: "Default Memo Content",
      }); // Mock direct helper call
      mockGetChatCompletion.mockResolvedValueOnce({
        textResponse: "Default Drafted Section Content",
      }); // Draft Section

      await streamChatWithWorkspaceCDB(
        baseFlowOptions.request,
        baseFlowOptions.response,
        workspace,
        legalTask,
        "chat",
        user,
        baseFlowOptions.thread,
        baseFlowOptions.attachments,
        testChatId,
        baseFlowOptions.isCanvasChat,
        baseFlowOptions.preventChatCreation,
        baseFlowOptions.settings_suffix,
        baseFlowOptions.invoice_ref,
        baseFlowOptions.vectorSearchMode,
        baseFlowOptions.hasUploadedFile,
        baseFlowOptions.displayMessage,
        baseFlowOptions.useDeepSearch,
        cdbOptions,
        legalTaskConfig
      );

      expect(mockGetChatCompletion).toHaveBeenCalledTimes(4); // Desc, Sections, Issues, Draft
      expect(mockGenerateLegalMemo).toHaveBeenCalledTimes(1);
    });

    test("should use custom system prompt for document summary when set in SystemSettings", async () => {
      const testChatId = "custom-summary-sys-prompt-test-chat-id";
      const legalTask = "Test Task - Custom Summary System Prompt";
      const cdbOptions = [legalTask, "", null];
      const legalTaskConfig = { flowType: "noMain" };
      const customPromptText =
        "This is my ultra custom system prompt for document summaries!";

      mockSystemSettingsGet.mockImplementation(async ({ label }) => {
        if (label === "cdb_document_summary_system_prompt") {
          // console.log(`[Test SystemSettings.get Mock] Matched custom label: ${label}`);
          return Promise.resolve({ value: customPromptText });
        }
        // console.log(`[Test SystemSettings.get Mock] Defaulting for label: ${label}`);
        return Promise.resolve(null);
      });

      // Mock LLM for document description
      mockGetChatCompletion.mockImplementationOnce(async (messages) => {
        // console.log("[Custom SysPrompt Test] LLM for Doc Desc called with:", messages);
        expect(messages.systemPrompt).toEqual(customPromptText);
        // User prompt should still be the default template
        const userPromptTemplate =
          originalDefaultPrompts.DEFAULT_DOCUMENT_SUMMARY.USER_PROMPT;
        const expectedUserPromptStart = userPromptTemplate.substring(
          0,
          userPromptTemplate.indexOf("{{task}}")
        );
        expect(messages.userPrompt).toContain(expectedUserPromptStart);
        expect(messages.userPrompt).toContain(legalTask);
        expect(messages.userPrompt).toContain("Content for doc1");
        return {
          textResponse: "Summary for doc1 (custom sysprompt test)",
          metrics: { lastCompletionTokens: 10 },
        };
      });
      // Mock subsequent calls
      mockGetChatCompletion.mockResolvedValueOnce({
        textResponse: JSON.stringify([
          {
            index_number: 1,
            title: "CustomSys Section 1",
            relevant_documents: ["doc1"],
          },
        ]),
      });
      mockGetChatCompletion.mockResolvedValueOnce({
        textResponse: JSON.stringify([{ Issue: "CustomSys Legal Issue" }]),
      });
      mockGenerateLegalMemo.mockResolvedValueOnce({
        memo: "CustomSys Memo Content",
      });
      mockGetChatCompletion.mockResolvedValueOnce({
        textResponse: "CustomSys Drafted Section Content",
      });

      await streamChatWithWorkspaceCDB(
        baseFlowOptions.request,
        baseFlowOptions.response,
        workspace,
        legalTask,
        "chat",
        user,
        baseFlowOptions.thread,
        baseFlowOptions.attachments,
        testChatId,
        baseFlowOptions.isCanvasChat,
        baseFlowOptions.preventChatCreation,
        baseFlowOptions.settings_suffix,
        baseFlowOptions.invoice_ref,
        baseFlowOptions.vectorSearchMode,
        baseFlowOptions.hasUploadedFile,
        baseFlowOptions.displayMessage,
        baseFlowOptions.useDeepSearch,
        cdbOptions,
        legalTaskConfig
      );
      expect(mockGetChatCompletion).toHaveBeenCalledTimes(4);
      expect(mockGenerateLegalMemo).toHaveBeenCalledTimes(1);
    });
  });
});
