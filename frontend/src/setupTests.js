// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import "@testing-library/jest-dom";

// Mock constants.js to handle import.meta.env
jest.mock("@/utils/constants", () => ({
  API_BASE: "/api",
  AUTH_USER: "istlegal_user",
  AUTH_TOKEN: "istlegal_authToken",
  AUTH_TIMESTAMP: "istlegal_authTimestamp",
  COMPLETE_QUESTIONNAIRE: "istlegal_completed_questionnaire",
  APPEARANCE_SETTINGS: "istlegal_appearance_settings",
  USER_BACKGROUND_COLOR: "bg-historical-msg-user",
  AI_BACKGROUND_COLOR: "bg-historical-msg-system",
  MODULE_LEGAL_QA: "legal-qa",
  MODULE_DOCUMENT_DRAFTING: "document-drafting",
  OLLAMA_COMMON_URLS: [
    "http://127.0.0.1:11434",
    "http://host.docker.internal:11434",
    "http://**********:11434",
  ],
  LMSTUDIO_COMMON_URLS: [
    "http://localhost:1234/v1",
    "http://127.0.0.1:1234/v1",
    "http://host.docker.internal:1234/v1",
    "http://**********:1234/v1",
  ],
  KOBOLDCPP_COMMON_URLS: [
    "http://127.0.0.1:5000/v1",
    "http://localhost:5000/v1",
    "http://host.docker.internal:5000/v1",
    "http://**********:5000/v1",
  ],
  LOCALAI_COMMON_URLS: [
    "http://127.0.0.1:8080/v1",
    "http://localhost:8080/v1",
    "http://host.docker.internal:8080/v1",
    "http://**********:8080/v1",
  ],
  fullApiUrl: jest.fn().mockReturnValue("/api"),
  POPUP_BROWSER_EXTENSION_EVENT: "NEW_BROWSER_EXTENSION_CONNECTION",
  isQura: jest.fn().mockReturnValue(false),
  isCitationEnabled: jest.fn().mockReturnValue(false),
  validateReferenceNumberWithStore: jest.fn().mockReturnValue(true),
}));

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock localStorage
const localStorageMock = (function () {
  let store = {};
  return {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn((key) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, "localStorage", {
  value: localStorageMock,
});

// Suppress console errors during tests
console.error = jest.fn();
console.warn = jest.fn();

// Create portal root for Modals
const portalRoot = document.createElement("div");
portalRoot.id = "theme-wrapper";
document.body.appendChild(portalRoot);

// Mock immer middleware for zustand
jest.mock("zustand/middleware/immer", () => ({
  immer: (config) => (set, get, api) => {
    return config(
      (fn) => {
        if (typeof fn === "function") {
          return set((state) => {
            // Create a deep copy of the state
            const newState = JSON.parse(JSON.stringify(state));
            fn(newState);
            return newState;
          });
        } else {
          return set(fn);
        }
      },
      get,
      api
    );
  },
}));
