# Chat Progress System

The **Chat Progress System** gives users real-time visibility into multi-step background processes that occur within a specific chat thread. It is designed to be generic and can handle various types of long-running tasks, such as document drafting or complex data analysis.

## How It Works

1.  **Backend Events**: A backend process (e.g., a streaming chat endpoint) emits progress events via Server-Sent Events (SSE). These events are tied to a specific `threadSlug`. Each event payload can include:

    - `step`: The current step number.
    - `status`: The state of the step (e.g., `in_progress`, `complete`, `error`).
    - `message`: A descriptive message for the current step or sub-task.
    - `subStep`: The current sub-task number within a step.
    - `flowType`: An identifier for the specific process (e.g., `"document-drafting-main"`, `"analysis"`), which allows the UI to display custom step labels.

2.  **State Management (`progressStore`)**: A centralized Zustand store, `progressStore.js`, listens for these events. It maintains a map of all active processes, keyed by their `threadSlug`. This allows the system to track multiple background tasks concurrently and independently.

3.  **React Hook (`useThreadProgress`)**: The `useThreadProgress(threadSlug)` hook provides a clean and simple API for React components to access and control the progress state of a single thread. It abstracts away the direct interaction with the Zustand store.

4.  **UI Components**:
    - `ChatProgress`: A small, inline component that shows the current step, a progress bar, and buttons to view details or abort the process.
    - `ProgressModal`: A modal dialog that displays a detailed, step-by-step view of the entire process using the `ProgressList` component. It shows completed steps, the current step, and pending steps.

## `flowType` and Customization

The system is highly customizable through the `flowType` property. When a process is started, it can be assigned a `flowType`. The UI components use this type to look up corresponding translation keys for step labels and descriptions. This allows for different "named" flows to be displayed to the user with meaningful text, even though the underlying logic is generic.

Example translation structure:

```javascript
// In frontend/src/locales/en/common.js
{
  "chatProgress": {
    "types": {
      "document-drafting-main": {
        "step1": { "label": "Initializing Main Draft..." },
        "step2": { "label": "Generating Section List..." }
      },
      "analysis": {
        "step1": { "label": "Starting Analysis..." },
        "step2": { "label": "Processing Data..." }
      }
    }
  }
}
```

## Translations

UI strings for the modal, including titles and step names for both flows, should be managed in the locale files (e.g., `frontend/src/locales/en/common.js`). Ensure keys are descriptive and cover all steps for both `main` and `noMain` flows.

Example structure (illustrative):

```javascript
// In frontend/src/locales/en/common.js
{
  "doc_drafting_progress_modal": {
    "title": "Document Drafting Progress",
    "description": "Displays the real-time progress of document drafting tasks...",
    "steps": {
      "main_flow": {
        "step_1_name": "Initializing Main Draft...",
        "step_4_name": "Generating Sections from Main Document..."
        // ... other main flow steps
      },
      "no_main_flow": {
        "step_1_name": "Initializing Draft...",
        "step_3_name": "Generating Sections from Summaries..."
        // ... other no-main flow steps
      },
      "common_step_x_name": "Generating Legal Memos..." // For steps common to both
    }
  }
}
```

It is crucial to add equivalent keys in all supported locale files.

## Developer Tips

- Inspect progress events in your browser's developer tools (Network tab, usually under Fetch/XHR or WS/EventStream depending on the browser) to debug the data being sent from the backend.
- Use `useProgressStore.getState().forceCleanup(threadSlug)` to clear progress state for a specific thread during development or testing.
- Use `useProgressStore.getState().cleanupStaleProcesses()` to remove all stale progress states across all threads.

---
