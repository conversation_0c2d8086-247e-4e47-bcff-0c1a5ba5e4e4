const TRANSLATIONS = {
  // =========================
  // COMMON STRINGS & PLACEHOLDERS
  // =========================
  common: {
    examples: "Examples",
    "workspaces-name": "Workspaces Name",
    ok: "OK",
    error: "error",
    confirm: "Confirm",
    confirmstart: "Confirm and start",
    savesuccess: "Successfully saved settings",
    saveerror: "Failed to save settings",
    success: "success",
    user: "User",
    selection: "Model Selection",
    saving: "Saving...",
    save: "Save changes",
    previous: "Previous Page",
    next: "Next Page",
    cancel: "Cancel",
    "search-placeholder": "Search...",
    "no-results": "No results found",
    "more-actions": "More actions",
    "delete-message": "Delete message",
    copy: "Copy",
    edit: "Edit",
    regenerate: "Regenerate",
    "export-word": "Export to Word",
    "stop-generating": "Stop generating",
    "attach-file": "Attach a file to this chat",
    home: "Home",
    settings: "Settings",
    support: "Support",
    "clear-reference": "Clear reference",
    "send-message": "Send message",
    "ask-legal": "Ask for legal information",
    "stop-response": "Stop generating response",
    "contact-support": "Contact Support",
    "copy-connection": "Copy connection string",
    "auto-connect": "Automatically connect to extension",
    back: "Back",
    "back-to-workspaces": "Back to workspaces",
    off: "Off",
    on: "On",
    continue: "Continue",
    rename: "Rename",
    delete: "Delete",
    "default-skill":
      "This skill is enabled by default and cannot be turned off.",
    timeframes: "Time Periods",
    other: "Other Options",
    placeholder: {
      username: "My username",
      password: "Your password",
      email: "Enter your email",
      "support-email": "<EMAIL>",
      website: "https://www.example.com",
      "site-name": "IST Legal",
      "search-llm": "Search for a specific LLM provider",
      "search-providers": "Search available providers",
      "message-heading": "Message heading",
      "message-content": "Message",
      "token-limit": "4096",
      "max-tokens": "Maximum tokens per request (eg: 1024)",
      "api-key": "API Key",
      "base-url": "Base URL",
      endpoint: "API endpoint",
    },
    tooltip: {
      copy: "Copy to clipboard",
      delete: "Delete this item",
      edit: "Edit this item",
      save: "Save changes",
      cancel: "Cancel changes",
      search: "Search items",
      add: "Add new item",
      remove: "Remove item",
      upload: "Upload file",
      download: "Download file",
      refresh: "Refresh data",
      settings: "Open settings",
      more: "More options",
    },
    "default.message": "Enter your message here",
    preview: "Preview",
    prompt: "Prompt",
    loading: "Loading...",
    download: "Download in raw format",
    open_in_new_tab: "Open in new tab with formatting",
    close: "Close",
    done: "Done",
    note: "Note",
    clearing: "Clearing...",
  },

  // =========================
  // CONFIRMATION MESSAGES
  // =========================
  deleteWorkspaceConfirmation:
    "Are you sure you want to delete {{name}}?\nAfter you do this it will be unavailable in this instance.\n\nThis action is irreversible.",
  deleteConfirmation:
    "Are you sure you want to delete ${user.username}?\nAfter you do this they will be logged out and unable to use this instance.\n\nThis action is irreversible.",
  suspendConfirmation:
    "Are you sure you want to suspend {{username}}?\nAfter you do this they will be logged out and unable to log back into this instance until unsuspended by an admin.",
  flushVectorCachesWorkspaceConfirmation:
    "Are you sure you want to flush vector caches for this workspace?",
  apiKeys: {
    "deactivate-title": "Deactivate API Key",
    "deactivate-message":
      "Are you sure you want to deactivate this API key?\nAfter you do this it will not longer be useable.\n\nThis action is irreversible.",
  },

  // =========================
  // BUTTON LABELS
  // =========================
  button: {
    delete: "Delete",
    edit: "Edit",
    suspend: "Suspend",
    unsuspend: "Unsuspend",
    save: "Save",
    accept: "Accept",
    decline: "Decline",
    ok: "OK",
    "flush-vector-caches": "Flush Vector Caches",
    cancel: "Cancel",
    saving: "Saving",
    save_llm: "Save LLM Selection",
    save_template: "Save Template",
    "reset-to-default": "Reset to Default",
    create: "Create",
    enable: "Enable",
    disable: "Disable",
    reset: "Reset",
    revoke: "Revoke",
  },

  // =========================
  // CONFIRM MESSAGE
  // =========================
  "confirm-message": {
    "delete-doc":
      "Are you sure you want to delete these files and folders?\nThis will remove the files from the system and remove them from any existing workspaces automatically.\nThis action is not reversible.",
  },

  // =========================
  // STATUSES
  // =========================
  statuses: { enabled: "enabled", disabled: "disabled" },

  // =========================
  // ERRORS
  // =========================
  errors: {
    "fetch-models": "Failed to fetch custom models",
    "fetch-models-error": "Error fetching models",
    "upgrade-error": "Error during upgrade",
    "failed-process-file": "Failed to process file: {{text}}",
    "failed-process-attachment": "Failed to process attachment",
    "failed-extract-content": "Failed to extract content from {{fileName}}",
    "failed-process-content": "Failed to process file content",
    common: { error: "Error" },
    workspace: {
      "already-exists": "A workspace with this name already exists",
    },
    auth: {
      "invalid-credentials": "Invalid login credentials.",
      "account-suspended": "Account suspended by admin.",
      "invalid-password": "Invalid password provided",
    },
    env: {
      "anthropic-key-format":
        "Invalid Anthropic API key format. The key must start with 'sk-ant-'",
      "openai-key-format": "OpenAI API key must start with 'sk-'",
      "jina-key-format": "Jina API key must start with 'jina_'",
    },
  },

  // =========================
  // CHARTS
  // =========================
  charts: {
    downloading: "Downloading image...",
    download: "Download graph image",
  },

  // =========================
  // OPTIONS
  // =========================
  options: { yes: "Yes", no: "No" },

  // =========================
  // USER MANAGEMENT
  // =========================
  user: {
    "delete-title": "Delete User",
    "suspend-title": "Suspend User",
    "unsuspend-title": "Unsuspend User",
    suspended: "User suspended successfully",
    unsuspended: "User unsuspended successfully",
  },

  // =========================
  // METRICS VISIBILITY
  // =========================
  "metrics.visibility.hover": "Metrics are visible.",
  "metrics.visibility.available": "Metrics are available.",

  // =========================
  // INVOICE REFERENCE NAVIGATION
  // =========================
  "invoice-reference-navigation": {
    title: "Active Invoice Reference",
    message:
      "You have an active invoice reference. Do you want to clear it before navigating to {{destinationType}}?",
    "current-reference": "Current reference:",
    explanation:
      "You can choose to keep your reference active or clear it. Clearing will remove the reference from all future interactions.",
    "clear-and-continue": "Clear reference and continue",
    "keep-and-continue": "Keep reference and continue",
    success: "Invoice reference cleared",
    "destination-types": {
      thread: "thread",
      workspace: "workspace",
      module: "module",
      location: "location",
    },
  },

  // Months
  "month.1": "Jan",
  "month.2": "Feb",
  "month.3": "Mar",
  "month.4": "Apr",
  "month.5": "May",
  "month.6": "Jun",
  "month.7": "Jul",
  "month.8": "Aug",
  "month.9": "Sep",
  "month.10": "Oct",
  "month.11": "Nov",
  "month.12": "Dec",

  // =========================
  // VERSION DISPLAY
  // =========================
  version: {
    "tooltip-title": "Version {{version}}",
    title: "Version {{version}}",
  },

  // =========================
  // STYLE UPLOAD
  // =========================
  "style-upload": {
    "manage-files": "Manage Style Reference Files",
    "manage-files-description":
      "In here you can upload files you have drafted to generate a personalized styling. When this is active, output in the platform will align more closely with your personal professional writing style.",
    "file-already-exists": "File already exists",
    "files-uploaded": "Files uploaded successfully",
    "remove-file": "Remove file",
    "add-more": "Add More Files",
    "clear-all": "Clear All",
    "no-files": "Please upload at least one file",
  },
};

export default TRANSLATIONS;
