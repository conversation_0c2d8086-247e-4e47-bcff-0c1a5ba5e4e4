const request = require("supertest");
const express = require("express");

jest.mock("../../../utils/files", () => ({
  purgeDocumentBuilder: jest.fn(),
}));

jest.mock("../../../models/eventLogs", () => ({
  EventLogs: { logEvent: jest.fn() },
}));

jest.mock("../../../utils/middleware/validatedRequest.js", () => ({
  validatedRequest: (req, res, next) => next(),
}));

jest.mock("../../../utils/middleware/multiUserProtected", () => ({
  flexUserRoleValid: () => (req, res, next) => next(),
  isMultiUserSetup: () => (req, res, next) => next(),
  ROLES: {
    admin: "admin",
    manager: "manager",
    superuser: "superuser",
    default: "default",
    all: "<all>",
  },
}));

const { purgeDocumentBuilder } = require("../../../utils/files");
const { EventLogs } = require("../../../models/eventLogs");
const { systemEndpoints } = require("../../../endpoints/system");

const app = express();
app.use(express.json());
systemEndpoints(app);

beforeEach(() => {
  jest.clearAllMocks();
});

describe("POST /system/purge-document-builder", () => {
  test("purges files and logs event", async () => {
    purgeDocumentBuilder.mockReturnValue(5);

    const res = await request(app).post("/system/purge-document-builder");

    expect(res.status).toBe(200);
    expect(res.body).toEqual({ success: true, removedFiles: 5 });
    expect(purgeDocumentBuilder).toHaveBeenCalledTimes(1);
    expect(EventLogs.logEvent).toHaveBeenCalledWith(
      "purged_document_builder",
      { removedFiles: 5 },
      undefined
    );
  });

  test("handles errors and returns 500", async () => {
    purgeDocumentBuilder.mockImplementation(() => {
      throw new Error("fail");
    });

    const res = await request(app).post("/system/purge-document-builder");

    expect(res.status).toBe(500);
    expect(res.body).toEqual({ success: false, error: "fail" });
  });
});
