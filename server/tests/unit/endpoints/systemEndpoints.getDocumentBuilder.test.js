const request = require("supertest");
const express = require("express");

jest.mock("../../../models/systemSettings", () => ({
  SystemSettings: {
    get: jest.fn(),
  },
}));

jest.mock("../../../utils/middleware/validatedRequest.js", () => ({
  validatedRequest: (req, res, next) => next(),
}));

jest.mock("../../../utils/middleware/multiUserProtected", () => ({
  flexUserRoleValid: () => (req, res, next) => next(),
  isMultiUserSetup: () => (req, res, next) => next(),
  ROLES: {
    admin: "admin",
    manager: "manager",
    superuser: "superuser",
    default: "default",
    all: "<all>",
  },
}));

const { SystemSettings } = require("../../../models/systemSettings");
const LEGAL_DRAFTING_PROMPTS = require("../../../utils/chats/prompts/legalDrafting");
const { systemEndpoints } = require("../../../endpoints/system");

const app = express();
app.use(express.json());
systemEndpoints(app);

beforeEach(() => {
  jest.clearAllMocks();
});

describe("GET /system/get-document-builder", () => {
  test("returns defaults when no custom settings and no flowType", async () => {
    SystemSettings.get.mockResolvedValue(null);

    const res = await request(app).get("/system/get-document-builder");

    expect(res.status).toBe(200);
    expect(res.body.summarySystemPrompt).toBe(
      LEGAL_DRAFTING_PROMPTS.DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT
    );
    expect(res.body.topicsSectionsSystemPrompt).toBe(
      LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_LIST_FROM_SUMMARIES.SYSTEM_PROMPT
    );
    // noMainDoc flow should omit select main document prompts
    const hasSelectMain = res.body.promptFlowStructure.some((p) =>
      p.key.includes("selectMainDocument")
    );
    expect(hasSelectMain).toBe(false);
  });

  test("returns custom prompts for mainDoc flow", async () => {
    SystemSettings.get.mockResolvedValue({ value: "custom" });

    const res = await request(app)
      .get("/system/get-document-builder")
      .query({ flowType: "mainDoc" });

    expect(res.status).toBe(200);
    expect(res.body.summarySystemPrompt).toBe("custom");
    expect(
      res.body.promptFlowStructure.some(
        (p) => p.key === "selectMainDocumentSystemPrompt"
      )
    ).toBe(true);
  });

  test("sanitizes empty values and uses defaults", async () => {
    SystemSettings.get.mockImplementation(({ label }) => {
      if (label === "summary_system_prompt")
        return Promise.resolve({ value: "   " });
      return Promise.resolve({ value: "custom" });
    });

    const res = await request(app)
      .get("/system/get-document-builder")
      .query({ flowType: "noMainDoc" });

    expect(res.status).toBe(200);
    expect(res.body.summarySystemPrompt).toBe(
      LEGAL_DRAFTING_PROMPTS.DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT
    );
    expect(res.body.relevanceSystemPrompt).toBe("custom");
  });
});
