import React from "react";
import { useTranslation } from "react-i18next";
import { Plus, Minus } from "@phosphor-icons/react";
import { Button } from "@/components/Button";
import Slider from "@/components/ui/Slider";

export default function SettingSlider({
  title,
  description,
  value,
  min,
  max,
  onChange,
  step = 1,
}) {
  const { t } = useTranslation();

  const handleIncrement = () => {
    const newValue = Math.min(value + step, max);
    onChange({ target: { value: newValue } });
  };

  const handleDecrement = () => {
    const newValue = Math.max(value - step, min);
    onChange({ target: { value: newValue } });
  };

  return (
    <div className="flex justify-between items-center w-full">
      <div className="flex flex-col gap-y-1 w-3/4">
        <p className="text-sm leading-6 font-medium text-foreground">
          {t(title)}
        </p>
        <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
          {t(description)}
        </p>
      </div>
      <div className="flex items-center gap-x-2">
        <Button
          type="button"
          onClick={handleDecrement}
          disabled={value <= min}
          variant="outline"
          size="icon"
          aria-label={t("scroll-threshold-settings.decrease", "Decrease")}
        >
          <Minus size={16} />
        </Button>
        <Slider
          value={[value]}
          min={min}
          max={max}
          step={step}
          onValueChange={(newVal) => onChange({ target: { value: newVal[0] } })}
          className="w-40"
        />
        <Button
          type="button"
          onClick={handleIncrement}
          disabled={value >= max}
          variant="outline"
          size="icon"
          aria-label={t("scroll-threshold-settings.increase", "Increase")}
        >
          <Plus size={16} />
        </Button>
        <p className="text-sm font-medium text-foreground w-12 text-right">
          {value}
        </p>
      </div>
    </div>
  );
}
