---
description: Guidelines for creating implementation strategy files
globs:
alwaysApply: true
---

# Implementation Strategy Files

When implementing major features or architectural changes, start by creating an implementation strategy file in the `AgentTemp/` directory. This file should:

1. **Outline the implementation strategy** - Provide a high-level overview of the approach
2. **Structure each implementation step** - Break down the work into manageable tasks
3. **Include progress tracking** - Add a way to track completion of each step, including testing status
4. **Document design decisions** - Explain key architectural choices
5. **Provide examples** - Include code examples where helpful
6. **Plan for testing** - Include comprehensive testing strategy with unit tests and integration tests
7. **Include documentation updates** - Plan for updating project documentation and cursor rules

The `AgentTemp/` directory is included in `.gitignore` and is meant for planning and temporary files that shouldn't be committed to the repository.

**Continuous Progress Tracking**: When implementing tasks based on the strategy file, continuously update the progress tracking section to reflect the current status of each step. Add checkbox marks for finished sub-steps. This helps maintain visibility into the implementation progress and identifies any blockers or issues.

**Documentation Requirements**: The implementation strategy must include specific steps for:

- Adding comprehensive tests for all new functionality (unit tests, integration tests, and manual verification)
- Updating relevant documentation in `frontend/docs`, `server/docs`, or `collector/docs` as applicable
- Updating cursor project rules if the implementation introduces new patterns or guidelines

**Testing Requirements**: The implementation strategy must include a detailed testing plan that covers:

- Unit tests for individual components and functions
- Integration tests for component interactions
- Manual verification steps for complex functionality
- Test coverage goals and metrics
- A clear process for marking implementation as "Tested" only after all tests have been successfully run

## Example Implementation Strategy File Structure

```markdown
# Feature Name Implementation Plan

## Overview
[High-level description of the feature/change]

## Implementation Strategy
[Overall approach and methodology]

## Steps to Implement
1. [Step 1]
2. [Step 2]
3. [Implementation of core functionality]
4. [Add unit tests for new functionality]
5. [Update documentation in frontend/docs, server/docs, or collector/docs]
6. [Update cursor project rules if needed]
...

## Progress Tracking
| Step | Implementation | Tests | Documentation | Status | Notes |
|------|---------------|-------|---------------|--------|-------|
| Step 1 | Not Started | Not Started | Not Started | Not Started | |
| Step 2 | In Progress | Not Started | Not Started | In Progress | |
| Step 3 | Completed | Completed | Completed | Completed | Successfully implemented and tested on [date] |
| Step 4 | Not Started | Not Started | Not Started | Not Started | |
| Step 5 | Not Started | Not Started | Not Started | Not Started | |
| Step 6 | Not Started | Not Started | Not Started | Not Started | |
...

## Design Decisions
[Explanation of key architectural decisions]

## Testing Strategy
[Detailed approach to testing the new functionality]

### Unit Tests
[Specific unit tests to be implemented]

### Integration Tests
[Specific integration tests to be implemented]

### Manual Verification
[Steps for manual verification of complex functionality]

### Test Coverage Goals
[Target test coverage metrics]

## Documentation Updates
[Specific documentation files that will need to be created or updated]

## Examples
[Code examples or mockups]
```
