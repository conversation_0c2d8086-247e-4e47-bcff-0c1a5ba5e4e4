| Rule File | Description | Type | Globs |
| project-overview.mdc | Project overview and high-level architecture | Always | - |
| core-files.mdc | Core files to consider when making changes | Always | - |
| documentation-updates.mdc | Mandatory documentation updates for commits | Always | - |
| internationalization-with-i18n.mdc | Guidelines for internationalization (i18n) and translation handling | Always | - |
| coding-standards.mdc | Coding standards and best practices for the project | Always | - |
| form-guidelines.mdc | Guidelines and best practices for implementing forms | Always | - |
| implementation-strategy.mdc | Guidelines for creating implementation strategy files | Always | - |
| frontend-development.mdc | Guidelines for frontend development | Auto Attached | frontend/**/*.js, frontend/**/*.jsx |
| frontend-architecture.mdc | Guidelines for frontend architecture and implementation | Auto Attached | frontend/**/*.js, frontend/**/*.jsx |
| backend-development.mdc | Guidelines for backend development | Auto Attached | server/**/*.js, collector/**/*.js |
| server-architecture.mdc | Guidelines for server architecture and implementation | Auto Attached | server/**/*.js |
| document-processing.mdc | Guidelines for document processing and RAG implementation | Auto Attached | server/**/*.js, collector/**/*.js |
| collector-service.mdc | Guidelines for the Collector service | Auto Attached | collector/**/*.js |
| extension-system.mdc | Guidelines for the Collector extension system | Auto Attached | collector/extensions/**/*.js |
| llm-integration.mdc | Guidelines for LLM integration and usage | Auto Attached | server/**/*.js, frontend/**/*.js, frontend/**/*.jsx |
| llm-component.mdc | Guidelines for the LLM component architecture | Auto Attached | frontend/src/components/LLMSelection/**/*.js, frontend/src/components/LLMSelection/**/*.jsx, server/utils/AiProviders/**/*.js |
| frontend-state-management.mdc | Guidelines for state management and event handling | Auto Attached | frontend/**/*.js, frontend/**/*.jsx |
| markdown-formatting.mdc | Guidelines for markdown formatting and rendering | Auto Attached | frontend/**/*.js, frontend/**/*.jsx, server/**/*.js |
| embedding-models.mdc | Guidelines for embedding models and vector search | Auto Attached | server/**/*.js, server/**/*.jsx, frontend/**/*.js, frontend/**/*.jsx |
| vector-search.mdc | Guidelines for vector search and context handling | Auto Attached | server/utils/vectorDbProviders/**/*.js, server/utils/helpers/chat/**/*.js |
| context-window-management.mdc | Guidelines for vector fetching and context window management | Auto Attached | server/utils/chats/**/*.js, server/utils/helpers/chat/**/*.js |
| context-backfilling.mdc | Guidelines for context backfilling in chat | Auto Attached | server/utils/helpers/chat/**/*.js, server/utils/chats/**/*.js |
| backend-api.mdc | Guidelines for backend API endpoints | Auto Attached | server/**/*.js |
| database-schema.mdc | Guidelines for database schema and models | Auto Attached | server/prisma/**/*.prisma, server/prisma/**/*.js, server/models/**/*.js |
| agent-system.mdc | Guidelines for the agent system and plugins | Auto Attached | server/utils/agents/**/*.js |
| document-sync.mdc | Guidelines for document synchronization | Auto Attached | server/jobs/**/*.js, server/utils/collectorApi/**/*.js |
| swagger-docs.mdc | Guidelines for Swagger/OpenAPI documentation | Auto Attached | server/swagger/**/*.js, server/endpoints/**/*.js |
| hooks-and-utils.mdc | Guidelines for hooks and utilities | Auto Attached | frontend/src/hooks/**/*.js, frontend/src/utils/**/*.js |
| chat-system.mdc | Guidelines for the chat system architecture | Auto Attached | frontend/src/components/WorkspaceChat/**/*.js, frontend/src/components/WorkspaceChat/**/*.jsx, frontend/src/utils/chat/**/*.js |
| system-settings.mdc | Guidelines for implementing system settings | Auto Attached | server/models/systemSettings.js, frontend/src/pages/Admin/System/**/*.jsx, frontend/src/stores/settingsStore.js |
| ui-components.mdc   | Guidelines for standard UI components and usage (Switch, Modal, Checkbox, etc.) | Auto Attached | frontend/src/components/ui/**/*.jsx |
| legal-memo-helper.mdc | Guidelines for using the Legal Memo helper utility | Auto Attached | server/**/*.js |
| linting-and-code-quality.mdc | Guidelines for linting, code quality, and fixing common ESLint issues | Always | **/*.{js,jsx} |
| iterative-context-window-management.mdc | Guidelines for iterative context window management and token optimization | Auto Attached | server/utils/chats/helpers/contextWindowManager.js, server/utils/chats/helpers/tokenTracker.js, server/utils/chats/helpers/documentProcessing.js |
| document-builder.mdc | Guidelines for the Document Builder page and prompts configuration | Auto Attached | frontend/src/pages/GeneralSettings/DocumentBuilder/**/*.jsx, server/endpoints/system.js |
| lawyer-individualizer.mdc | Guidelines for the Lawyer Individualizer feature (style alignment) | Auto Attached | frontend/src/components/UserMenu/StyleAlignmentModal/**/*.jsx, server/models/userStyleProfile.js, frontend/src/stores/userStore.js |
| locale-scripts.mdc | Documentation for locale verification and fixing scripts | Manual | scripts/*.mjs |
| mermaid-diagram-guidelines.mdc | Guidelines for creating Mermaid architecture diagrams | Manual | - |
| lancedb-integration.mdc | Guidelines for LanceDB integration and vector handling | Auto Attached | server/**/*.js |
| star-marking.mdc | Guidelines for star-marking feature and similarity score boost | Auto Attached | frontend/src/components/Modals/ManageWorkspace/**/*.jsx, server/endpoints/workspaceDocuments.js, server/utils/helpers/starredDocuments.js |
| testing-guidelines.mdc | Guidelines for writing frontend and backend tests | Manual | - |
| security-guidelines.mdc | Security best practices and considerations | Manual | - |

## Usage

These rules are automatically applied based on their type and glob patterns. You can also manually reference a rule using `@rulename` in your prompts to Cursor AI.

## Migration

These rules replace the deprecated `.cursorrules` file in the root directory. The content from that file has been migrated to these more specific and scoped rules.
These rules are now deprecated, readme.mdc is the new source of truth.
