import { create } from "zustand";

const createThreadState = () => ({
  isActive: false,
  currentStep: 1,
  totalSteps: 7,
  startTime: null,
  flowType: null,
  currentSubStep: null,
  totalSubSteps: null,
  stepStatus: "pending", // "pending", "starting", "in_progress", "complete", "error"
  stepMessage: null,
  stepDetails: [],
  // Each thread state gets its own AbortController instance to ensure proper isolation.
  // This prevents shared references between threads and allows independent cancellation.
  abortController: new AbortController(),
  error: null,
});

const useProgressStore = create((set, get) => ({
  // Map of thread states: Map<threadSlug, ThreadState>
  threads: new Map(),

  startProcess: (threadSlug, totalSteps = 7, flowType = null) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      // If thread already exists, abort its current process
      if (state.threads.has(threadSlug)) {
        const existingState = state.threads.get(threadSlug);
        if (existingState?.abortController) {
          existingState.abortController.abort();
        }
      }

      // Create new thread state with fresh abort controller
      // This clears any previous error state and resets all thread state
      const newThreadState = {
        ...createThreadState(),
        isActive: true,
        totalSteps: Math.max(1, totalSteps),
        startTime: Date.now(),
        flowType,
      };

      // Create new Map and set the thread
      const newThreads = new Map(state.threads);
      newThreads.set(threadSlug, newThreadState);

      return { threads: newThreads };
    });
  },

  updateProgress: (event, threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string" || !event) return;

    set((state) => {
      const newThreads = new Map(state.threads);
      const threadState = newThreads.get(threadSlug);

      if (!threadState || !threadState.isActive) return state;

      const { step, subStep, status, message } = event;

      if (typeof step !== "number") {
        // Ignore malformed event
        return state;
      }

      // Update step details array for tracking step history
      const updatedStepDetails = [...(threadState.stepDetails || [])];

      // Find or create step detail entry
      let stepDetailIndex = updatedStepDetails.findIndex(
        (detail) => detail.step === step
      );
      if (stepDetailIndex === -1) {
        updatedStepDetails.push({
          step,
          status: status || "pending",
          message: message || null,
          subTasks: [],
          startTime: Date.now(),
        });
        stepDetailIndex = updatedStepDetails.length - 1;
      } else {
        // Update existing step
        updatedStepDetails[stepDetailIndex] = {
          ...updatedStepDetails[stepDetailIndex],
          status: status || updatedStepDetails[stepDetailIndex].status,
          message: message || updatedStepDetails[stepDetailIndex].message,
        };
      }

      // Handle sub-tasks
      if (subStep !== undefined && subStep !== null) {
        const stepDetail = updatedStepDetails[stepDetailIndex];
        let subTaskIndex = stepDetail.subTasks.findIndex(
          (subTask) => subTask.subStep === subStep
        );

        if (subTaskIndex === -1) {
          stepDetail.subTasks.push({
            subStep,
            status: status || "pending",
            message: message || null,
            startTime: Date.now(),
          });
        } else {
          stepDetail.subTasks[subTaskIndex] = {
            ...stepDetail.subTasks[subTaskIndex],
            status: status || stepDetail.subTasks[subTaskIndex].status,
            message: message || stepDetail.subTasks[subTaskIndex].message,
          };
        }
      }

      newThreads.set(threadSlug, {
        ...threadState,
        currentStep: step || threadState.currentStep,
        currentSubStep: subStep,
        stepStatus: status || threadState.stepStatus,
        stepMessage: message || threadState.stepMessage,
        stepDetails: updatedStepDetails,
        lastUpdate: Date.now(),
      });

      return { threads: newThreads };
    });
  },

  finishProcess: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      if (!newThreads.has(threadSlug)) return state;

      // Clean up the thread state completely
      newThreads.delete(threadSlug);
      return { threads: newThreads };
    });
  },

  cancelProcess: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      const threadState = newThreads.get(threadSlug);

      if (!threadState) return state;

      if (threadState.abortController) {
        threadState.abortController.abort();
      }

      newThreads.set(threadSlug, {
        ...threadState,
        isActive: false,
        stepStatus: "error",
      });

      return { threads: newThreads };
    });
  },

  getAbortController: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return null;

    const state = get();
    const threadState = state.threads.get(threadSlug);
    return threadState?.abortController || null;
  },

  getThreadState: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return null;

    const state = get();
    return state.threads.get(threadSlug) || null;
  },

  setError: (threadSlug, errorMessage) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      const threadState = newThreads.get(threadSlug);

      if (!threadState) return state;

      newThreads.set(threadSlug, {
        ...threadState,
        isActive: false,
        stepStatus: "error",
        error: errorMessage,
      });

      return { threads: newThreads };
    });
  },

  clearError: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      if (!newThreads.has(threadSlug)) return state;

      // Remove the thread state completely when clearing error
      newThreads.delete(threadSlug);
      return { threads: newThreads };
    });
  },

  // Auto-cleanup method to remove stale progress states
  cleanupStaleProcesses: (maxAgeMinutes = 10) => {
    const now = Date.now();
    const maxAge = maxAgeMinutes * 60 * 1000; // Convert to milliseconds

    set((state) => {
      const newThreads = new Map(state.threads);
      let hasChanges = false;

      for (const [threadSlug, threadState] of newThreads.entries()) {
        const age = now - (threadState.startTime || now);
        if (age > maxAge) {
          newThreads.delete(threadSlug);
          hasChanges = true;
        }
      }

      return hasChanges ? { threads: newThreads } : state;
    });
  },

  // Force cleanup a specific thread (useful for debugging or manual cleanup)
  forceCleanup: (threadSlug) => {
    if (!threadSlug || typeof threadSlug !== "string") return;

    set((state) => {
      const newThreads = new Map(state.threads);
      if (newThreads.has(threadSlug)) {
        newThreads.delete(threadSlug);
        return { threads: newThreads };
      }
      return state;
    });
  },
}));

// Auto-cleanup stale processes every 5 minutes
setInterval(
  () => {
    useProgressStore.getState().cleanupStaleProcesses();
  },
  5 * 60 * 1000
);

export default useProgressStore;
