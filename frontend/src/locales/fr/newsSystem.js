const TRANSLATIONS = {
  "news-system": {
    title: "Actualités et Annonces",
    previous: "Précédent",
    next: "Suivant",
    of: "de",
    close: "Fermer",
    dismissThis: "Ne plus afficher",
    dismissAll: "Tout ignorer",
    management: {
      title: "Gestion des Actualités",
      description:
        "Gérer les annonces système et les actualités. Les actualités système sont déployées avec l'application et ne peuvent pas être modifiées ici.",
      create: "Créer Actualité",
      edit: "Modifier Actualité",
      loading: "Chargement...",
      table: {
        title: "Titre",
        priority: "Priorité",
        targetRoles: "Rôles Cibles",
        created: "Créé",
        expires: "Expire",
        status: "Statut",
        source: "Source",
        actions: "Actions",
        allUsers: "Tous les utilisateurs",
        never: "Jamais",
        active: "Actif",
        inactive: "Inactif",
        systemNews: "Système",
        localNews: "Local",
      },
      source: {
        system: "Actualités Système",
        local: "Actualités Locales",
        systemTooltip:
          "Cette actualité est déployée avec l'application et ne peut pas être modifiée",
        localTooltip:
          "Cette actualité a été créée localement et peut être modifiée",
      },
      form: {
        title: "Titre",
        titlePlaceholder: "Entrez le titre de l'actualité",
        content: "Contenu",
        contentPlaceholder: "Entrez le contenu de l'actualité",
        priority: "Priorité",
        targetRoles: "Rôles Cibles (laisser vide pour tous les utilisateurs)",
        expiresAt: "Expire le (optionnel)",
        priorities: {
          low: "Faible",
          medium: "Moyen",
          high: "Élevé",
          urgent: "Urgent",
        },
        roles: {
          admin: "Administrateur",
          manager: "Gestionnaire",
          default: "Défaut",
        },
      },
      actions: {
        create: "Créer",
        update: "Mettre à jour",
        cancel: "Annuler",
        delete: "Supprimer",
        edit: "Modifier",
        view: "Voir",
        cannotEdit: "Impossible de modifier les actualités système",
        cannotDelete: "Impossible de supprimer les actualités système",
      },
      confirmations: {
        delete: "Êtes-vous sûr de vouloir supprimer cette actualité ?",
      },
      messages: {
        createSuccess: "Actualité créée avec succès",
        updateSuccess: "Actualité mise à jour avec succès",
        deleteSuccess: "Actualité supprimée avec succès",
        createError: "Échec de la création de l'actualité",
        updateError: "Échec de la mise à jour de l'actualité",
        deleteError: "Échec de la suppression de l'actualité",
        fetchError: "Échec de la récupération des actualités",
        systemNewsInfo:
          "Les actualités système sont en lecture seule et déployées avec l'application",
      },
    },
    error: {
      dismiss: "Échec de l'ignorance de l'actualité",
      dismissAll: "Échec de l'ignorance de toutes les actualités",
      fetch: "Échec de la récupération des actualités",
    },
  },

  // News header and list functionality (moved from common.js)
  news: {
    header: {
      viewAll: "Voir toutes les actualités",
      buttonText: "Actualités",
    },
    list: {
      title: "Actualités et Annonces",
      empty: "Aucune actualité à afficher",
      active: "Actif",
      dismissed: "Ignoré",
      dismiss: "Ignorer",
      dismissSuccess: "Actualité ignorée avec succès",
      dismissError: "Échec de l'ignorance de l'actualité",
      dismissedAt: "Rejeté le {{date}}",
      systemNews: "Système",
      viewFull: "Voir l'article complet",
      filter: {
        all: "Tout",
        unread: "Actives",
        read: "Ignorées",
      },
    },
    priority: {
      low: "Faible",
      medium: "Moyen",
      high: "Élevé",
      urgent: "Urgent",
    },
  },
};

export default TRANSLATIONS;
