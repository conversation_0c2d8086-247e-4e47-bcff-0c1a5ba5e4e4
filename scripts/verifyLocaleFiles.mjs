#!/usr/bin/env node

/**
 * Locale Files Verification Script
 *
 * PURPOSE:
 * This script verifies that all locale files are consistent with the English template files.
 * It checks for structural consistency across all supported languages.
 *
 * WHAT IT CHECKS:
 * 1. Missing keys: Keys that exist in English but are missing in other locales
 * 2. Extra keys: Keys that exist in other locales but not in English
 * 3. Key order: Ensures keys appear in the same order as the English template
 * 4. Nested structure: Recursively validates nested objects
 *
 * WHAT IT DOES NOT DO:
 * - Does not automatically add missing keys (requires manual translation)
 * - Does not validate translation quality or accuracy
 * - Does not check for unused keys in the codebase
 *
 * FIXING MISSING KEYS:
 * When missing keys are found, they must be added manually to ensure proper translation
 * to the target language. The fixLocaleStructure.mjs script can help with key reordering
 * but cannot translate missing content.
 *
 * USAGE:
 * Run this script to identify locale inconsistencies:
 *   node scripts/verifyLocaleFiles.mjs
 *
 * Then use fixLocaleStructure.mjs to fix key ordering:
 *   node scripts/fixLocaleStructure.mjs
 *
 * Finally, manually add any missing keys with proper translations.
 */

import fs from "fs";
import path from "path";
import { fileURLToPath, pathToFileURL } from "url";

// Determine script directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to locales directory
const localesDir = path.resolve(__dirname, "../frontend/src/locales");
// Folders to ignore
const ignoreDirs = ["__tests__"];

// Discover all locale code directories
const localeDirs = fs.readdirSync(localesDir).filter((name) => {
  const fullPath = path.join(localesDir, name);
  return fs.statSync(fullPath).isDirectory() && !ignoreDirs.includes(name);
});

// English locale directory
const enDir = path.join(localesDir, "en");
if (!fs.existsSync(enDir)) {
  console.error("English locale directory not found at", enDir);
  process.exit(1);
}

// List all English locale files including common.js
const enFiles = fs.readdirSync(enDir).filter((file) => file.endsWith(".js"));

let hasErrors = false;
const errors = [];

// Compare keys of two translation objects recursively, checking missing, extra, and order mismatches
async function compareKeys(source, target, fileName, lang, basePath = "") {
  const keysA = Object.keys(source);
  const keysB = Object.keys(target);

  // Missing keys in target
  for (const key of keysA) {
    if (!keysB.includes(key)) {
      hasErrors = true;
      errors.push(
        `[${fileName}] Missing key '${basePath + key}' in locale '${lang}' - requires manual translation`
      );
    }
  }

  // Extra keys in target
  for (const key of keysB) {
    if (!keysA.includes(key)) {
      hasErrors = true;
      errors.push(
        `[${fileName}] Extra key '${basePath + key}' in locale '${lang}' - not found in English template`
      );
    }
  }

  // Key order check
  const minLen = Math.min(keysA.length, keysB.length);
  for (let i = 0; i < minLen; i++) {
    if (keysA[i] !== keysB[i]) {
      hasErrors = true;
      errors.push(
        `[${fileName}] Key order mismatch at '${basePath + keysA[i]}' in locale '${lang}': expected index ${i}, found '${keysB[i]}' - run fixLocaleStructure.mjs to fix`
      );
      break;
    }
  }

  // Recursive check for nested objects
  for (const key of keysA) {
    const valA = source[key];
    const valB = target[key];
    if (
      valA &&
      typeof valA === "object" &&
      !Array.isArray(valA) &&
      valB &&
      typeof valB === "object"
    ) {
      await compareKeys(valA, valB, fileName, lang, `${basePath + key}.`);
    }
  }
}

(async () => {
  console.log("🔍 Verifying locale file consistency...");
  console.log(
    `📁 Checking ${localeDirs.length} locales: ${localeDirs.join(", ")}`
  );
  console.log(`📄 Validating ${enFiles.length} files: ${enFiles.join(", ")}`);
  console.log("");

  for (const file of enFiles) {
    const enPath = path.join(enDir, file);
    const enUrl = pathToFileURL(enPath).href;
    const { default: enTranslations } = await import(enUrl);

    for (const lang of localeDirs) {
      if (lang === "en") continue;
      const localeFilePath = path.join(localesDir, lang, file);
      if (!fs.existsSync(localeFilePath)) {
        hasErrors = true;
        errors.push(
          `Missing file '${file}' in locale '${lang}' - copy from English template and translate`
        );
        continue;
      }
      const localeUrl = pathToFileURL(localeFilePath).href;
      const { default: localeTranslations } = await import(localeUrl);

      await compareKeys(enTranslations, localeTranslations, file, lang);
    }
  }

  if (hasErrors) {
    console.error("❌ Errors found during locale verification:");
    console.error("");
    errors.forEach((err) => console.error(" -", err));
    console.error("");
    console.error("🔧 To fix these issues:");
    console.error(
      "   1. Run 'node scripts/fixLocaleStructure.mjs' to fix key ordering"
    );
    console.error("   2. Manually add missing keys with proper translations");
    console.error(
      "   3. Remove extra keys that don't exist in English template"
    );
    console.error("   4. Re-run this script to verify fixes");
    process.exit(1);
  } else {
    console.log(
      "✅ All locale files are present and consistent with English files."
    );
    console.log("🌍 All supported languages have matching key structures.");
    process.exit(0);
  }
})();
