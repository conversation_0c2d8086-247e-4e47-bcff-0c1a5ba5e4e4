const { SystemSettings } = require("../models/systemSettings");
const path = require("path");
const fs = require("fs");

function utilEndpoints(app) {
  if (!app) return;

  app.get("/utils/metrics", async (_, response) => {
    try {
      const metrics = {
        online: true,
        version: getVersion(),
        mode: (await SystemSettings.isMultiUserMode())
          ? "multi-user"
          : "single-user",
        vectorDB: process.env.VECTOR_DB || "lancedb",
        storage: await getDiskStorage(),
      };
      response.status(200).json(metrics);
    } catch (e) {
      console.error(e);
      response.sendStatus(500).end();
    }
  });

  app.get("/utils/version", async (_, response) => {
    try {
      const versionInfo = getVersionInfo();
      response.status(200).json(versionInfo);
    } catch (e) {
      console.error(e);
      response.sendStatus(500).end();
    }
  });
}

function getGitVersion() {
  if (process.env.ANYTHING_LLM_RUNTIME === "docker") return "--";
  try {
    return require("child_process")
      .execSync("git rev-parse HEAD")
      .toString()
      .trim();
  } catch (e) {
    console.error("getGitVersion", e.message);
    return "--";
  }
}

function getGitTag() {
  if (process.env.ANYTHING_LLM_RUNTIME === "docker") return null;
  try {
    return require("child_process")
      .execSync("git describe --tags --exact-match HEAD 2>/dev/null")
      .toString()
      .trim();
  } catch (e) {
    // Not on a tagged commit
    return null;
  }
}

function getLatestGitTag() {
  if (process.env.ANYTHING_LLM_RUNTIME === "docker") return null;
  try {
    return require("child_process")
      .execSync("git describe --tags --abbrev=0 2>/dev/null")
      .toString()
      .trim();
  } catch (e) {
    console.error("getLatestGitTag", e.message);
    return null;
  }
}

function getVersionFromFile() {
  try {
    const versionPath = path.join(__dirname, "../../version.json");
    if (fs.existsSync(versionPath)) {
      const versionData = JSON.parse(fs.readFileSync(versionPath, "utf8"));
      return versionData;
    }
  } catch (e) {
    console.error("getVersionFromFile", e.message);
  }
  return null;
}

function getVersion() {
  // Priority: Git tag > version.json > package.json > git commit
  const gitTag = getGitTag();
  if (gitTag) {
    return gitTag;
  }

  const versionFile = getVersionFromFile();
  if (versionFile?.version) {
    return versionFile.version;
  }

  try {
    const packagePath = path.join(__dirname, "../../package.json");
    const packageData = JSON.parse(fs.readFileSync(packagePath, "utf8"));
    if (packageData.version && packageData.version !== "0.0.0") {
      return packageData.version;
    }
  } catch (e) {
    console.error("Error reading package.json version", e.message);
  }

  // Fallback to git commit
  return getGitVersion();
}

function getVersionInfo() {
  const gitCommit = getGitVersion();
  const gitTag = getGitTag();
  const latestTag = getLatestGitTag();
  const versionFile = getVersionFromFile();

  let packageVersion = null;
  try {
    const packagePath = path.join(__dirname, "../../package.json");
    const packageData = JSON.parse(fs.readFileSync(packagePath, "utf8"));
    packageVersion = packageData.version;
  } catch (e) {
    console.error("Error reading package.json version", e.message);
  }

  return {
    current: getVersion(),
    sources: {
      gitTag,
      latestTag,
      versionFile: versionFile?.version || null,
      packageVersion,
      gitCommit: gitCommit === "--" ? null : gitCommit,
    },
    metadata: {
      description: versionFile?.description || null,
      timestamp: versionFile?.timestamp || null,
      commit: versionFile?.commit || gitCommit,
      isRelease: !!gitTag,
      isDevelopment: !gitTag && gitCommit !== "--",
      isDocker: process.env.ANYTHING_LLM_RUNTIME === "docker",
    },
  };
}

function byteToGigaByte(n) {
  return n / Math.pow(10, 9);
}

async function getDiskStorage() {
  try {
    const checkDiskSpace = require("check-disk-space").default;
    const { free, size } = await checkDiskSpace("/");
    return {
      current: Math.floor(byteToGigaByte(free)),
      capacity: Math.floor(byteToGigaByte(size)),
    };
  } catch {
    return {
      current: null,
      capacity: null,
    };
  }
}

module.exports = {
  utilEndpoints,
  getGitVersion,
  getVersion,
  getVersionInfo,
  getGitTag,
  getLatestGitTag,
};
