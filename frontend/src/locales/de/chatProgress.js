export default {
  // =========================
  // PROGRESS
  // =========================
  chatProgress: {
    title: "Verarbeitung",
    processing: "Verarbeitung...",
    step: "Schritt",
    timeLeft: "Verbleibende Zeit",
    details: "Details",
    abort: "Abbrechen",
    modalTitle: "Fortschrittsdetails",
    "close-msg": "Sind Sie sicher, dass Sie den Vorgang abbrechen möchten?",
    noThreadSelected: "Kein Thread ausgewählt",
    noActiveProgress: "Kein aktiver Fortschritt",
    of: "von",
    started: "Gestartet",
    error: {
      title: "Bei der Bearbeitung Ihrer Anfrage ist ein Fehler aufgetreten.",
      description:
        "Bitte versuchen Sie es erneut oder wenden Sie sich an den Support, wenn das Problem weiterhin besteht.",
      retry: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      dismiss: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      showDetails: "Technische Details anzeigen",
      hideDetails: "Technische Details ausblenden",
    },
    cancelled: "Der Prozess wurde abgebrochen.",
    types: {
      main: {
        step1: {
          label: "Generierung der Abschnittsliste",
          desc: "Verwendung des Hauptdokuments zur Erstellung einer ersten Struktur.",
        },
        step2: {
          label: "Verarbeitung der Dokumente",
          desc: "Beschreibungen generieren und Relevanz prüfen.",
        },
        step3: {
          label: "Zuordnung der Dokumente zu Abschnitten",
          desc: "Relevante Dokumente jedem Abschnitt zuweisen.",
        },
        step4: {
          label: "Identifizierung rechtlicher Probleme",
          desc: "Wichtige rechtliche Probleme für jeden Abschnitt extrahieren.",
        },
        step5: {
          label: "Generierung rechtlicher Memos",
          desc: "Rechtliche Memoranden für die identifizierten Probleme erstellen.",
        },
        step6: {
          label: "Verfassung der Abschnitte",
          desc: "Inhalt für jeden einzelnen Abschnitt erstellen.",
        },
        step7: {
          label: "Kombinierung & Finalisierung des Dokuments",
          desc: "Abschnitte zum finalen Dokument zusammenfügen.",
        },
      },
      noMain: {
        step1: {
          label: "Verarbeitung der Dokumente",
          desc: "Beschreibungen für alle hochgeladenen Dateien generieren.",
        },
        step2: {
          label: "Generierung der Abschnittsliste",
          desc: "Strukturierte Abschnittsliste aus Dokumentzusammenfassungen erstellen.",
        },
        step3: {
          label: "Finalisierung der Dokumentzuordnung",
          desc: "Relevanz der Dokumente für jeden geplanten Abschnitt bestätigen.",
        },
        step4: {
          label: "Identifizierung rechtlicher Probleme",
          desc: "Wichtige rechtliche Probleme für jeden Abschnitt extrahieren.",
        },
        step5: {
          label: "Generierung rechtlicher Memos",
          desc: "Rechtliche Memoranden für die identifizierten Probleme erstellen.",
        },
        step6: {
          label: "Verfassung der Abschnitte",
          desc: "Inhalt für jeden einzelnen Abschnitt erstellen.",
        },
        step7: {
          label: "Kombinierung & Finalisierung des Dokuments",
          desc: "Alle Abschnitte zum finalen rechtlichen Dokument zusammenfügen.",
        },
      },
      referenceFiles: {
        step1: {
          label: "Verarbeitung von Referenzdateien",
          desc: "Verarbeitung von Referenzdateien",
        },
        step2: {
          label: "Verarbeitung von Prüfdateien",
          desc: "Verarbeitung von Prüfdateien",
        },
        step3: {
          label: "Generierung der Abschnittsliste",
          desc: "Abschnittsliste generieren",
        },
        step4: {
          label: "Entwurf der Abschnitte",
          desc: "Abschnitte entwerfen",
        },
        step5: {
          label: "Generierung des Berichts",
          desc: "Bericht generieren",
        },
      },
      documentDrafting: {
        step1: {
          label: "Vorbereitung der Dokumente",
          desc: "Sammlung und Vorbereitung aller relevanten Dokumente für den Entwurfsprozess.",
        },
        step2: {
          label: "Analyse des Inhalts",
          desc: "Analyse des Dokumentinhalts und Identifizierung wichtiger rechtlicher Probleme und Klauseln.",
        },
        step3: {
          label: "Generierung des Entwurfs",
          desc: "Erstellung des finalen Dokumententwurfs basierend auf der Analyse.",
        },
      },
    },
  },

  // =========================
  // PROGRESS TYPES
  // =========================
  ddProgress: {
    title: "Dokumenterstellung",
  },
  cdbProgress: {
    title: "Komplexer Dokumentgenerator",
    general: {
      placeholderSubTask: "Verarbeitung von Element {{index}}...",
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Abbruch bestätigen",
    confirm_abort_description:
      "Sind Sie sicher, dass Sie den Vorgang abbrechen möchten?",
    keep_running: "Weiter ausführen",
    abort_run: "Vorgang abbrechen",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Fortschritt der Antwortgenerierung",
    description:
      "Zeigt den Echtzeitfortschritt der Aufgaben zur Vervollständigung der Eingabe, abhängig von der Verknüpfung mit anderen Arbeitsbereichen und der Dateigröße. Das Modal schließt sich automatisch, wenn alle Schritte abgeschlossen sind.",
    step_fetching_memos: "Abrufen rechtlicher Daten zu aktuellen Themen",
    step_processing_chunks: "Verarbeitung hochgeladener Dokumente",
    step_combining_responses: "Antwort finalisieren",
    sub_step_chunk_label: "Verarbeitung von Dokumentgruppe {{index}}",
    sub_step_memo_label: "Rechtliche Daten von {{workspaceSlug}} abgerufen",
    placeholder_sub_task: "Warteschlangen-Teilaufgabe",
    desc_fetching_memos:
      "Abrufen relevanter rechtlicher Informationen aus verknüpften Arbeitsbereichen",
    desc_processing_chunks:
      "Analyse und Extraktion von Informationen aus Dokumentgruppen",
    desc_combining_responses:
      "Zusammenführung von Informationen zu einer umfassenden Antwort",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Rechtliche Aufgabe läuft im Hintergrund.",
    dd: "Dokumenterstellung läuft im Hintergrund weiter.",
    reopen: "Statusfenster öffnen",
  },
};
