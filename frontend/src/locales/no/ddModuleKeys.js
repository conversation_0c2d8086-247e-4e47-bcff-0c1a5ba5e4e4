export default {
  // =========================
  // DOCUMENT DRAFTING
  // =========================
  "document-drafting": {
    title: "Dokumentutkast",
    description: "<PERSON><PERSON><PERSON>er dine innstillinger for dokumentutkast.",
    configuration: "Konfigurasjon",
    "drafting-model": "Utkast-LLM",
    enabled: "Dokumentutkast er aktivert",
    disabled: "Dokumentutkast er deaktivert",
    "enabled-toast": "Dokumentutkast aktivert",
    "disabled-toast": "Dokumentutkast deaktivert",
    "desc-settings":
      "Administrator kan endre innstillingene for dokumentutkast for alle brukere.",
    "drafting-llm": "Foretrukket LLM for utkast",
    saving: "Lagrer...",
    save: "Lagre endringer",
    "chat-settings": "Chatinnstillinger",
    "drafting-chat-settings": "Chatinnstillinger for dokumentutkast",
    "chat-settings-desc":
      "Kontroller oppførselen til chat-funksjonen for dokumentutkast.",
    "drafting-prompt": "Systemprompt for dokumentutkast",
    "drafting-prompt-desc":
      "Systemprompten som vil bli brukt i dokumentutkast er forskjellig fra systemprompten for juridisk spørsmål og svar. Denne definerer konteksten og instruksjonene for AI-en slik at den kan generere et relevant og nøyaktig svar.",
    linking: "Dokumentkobling",
    "legal-issues-prompt": "Prompt for juridiske problemstillinger",
    "legal-issues-prompt-desc":
      "Skriv inn prompt for juridiske problemstillinger.",
    "memo-prompt": "Memo-prompt",
    "memo-prompt-desc": "Skriv inn prompt for memo.",
    "desc-linkage":
      "Aktiver muligheten til å legge til ytterligere juridisk kontekst ved å kombinere Vector/PDR-søk med memo-henting",
    message: {
      title: "Foreslåtte meldinger for dokumentutkast",
      description:
        "Legg til foreslåtte meldinger som brukere raskt kan velge når de utarbeider dokumenter.",
      heading: "Standard meldingstittel",
      body: "Standard meldingstekst",
      "new-heading": "Meldingstittel",
      message: "Meldingstekst",
      add: "Legg til melding",
      save: "Lagre meldinger",
    },
    "combine-prompt": "Kombinasjonsprompt",
    "combine-prompt-desc":
      "Angi systemprompten for å kombinere flere svar til ett enkelt svar. Denne prompten brukes både for å kombinere svar og DD Linkage-memoer, og for å kombinere de forskjellige svarene fra Infinity Context-behandling.",
    "page-description":
      "Denne siden er for å justere de ulike promptene som brukes i forskjellige funksjoner i dokumentutkastmodulen. I hvert inntastingsfelt vises standardprompten, som vil bli brukt med mindre en tilpasset prompt anvendes på denne siden.",
    "dd-linkage-steps": "Prompter for DD-koblingstrinn",
    "general-combination-prompt": "Generell kombinasjonsprompt",
    "import-memo": {
      title: "Importer fra Legal QA",
      "button-text": "Importer notat",
      "search-placeholder": "Søk i tråder...",
      import: "Importer",
      importing: "Importerer...",
      "no-threads": "Ingen Legal QA-tråder funnet",
      "no-matching-threads": "Ingen tråder samsvarer med søket ditt",
      "thread-not-found": "Valgt tråd ikke funnet",
      "empty-thread": "Den valgte tråden har ingen innhold å importere",
      "import-success": "Trådinnhold importert vellykket",
      "import-error": "Kunne ikke importere trådinnhold",
      "import-error-details": "Feil under import: {{details}}",
      "fetch-error": "Kunne ikke hente tråder. Vennligst prøv igjen senere.",
      "imported-from": "Importert fra Legal QA-tråd",
      "unnamed-thread": "Navnløs tråd",
      "unknown-workspace": "Ukjent arbeidsområde",
      "no-threads-available": "Ingen tråder tilgjengelige å importere",
      "create-conversations-first":
        "Opprett først samtaler i et Legal QA-arbeidsområde, så kan du importere dem her.",
      "no-legal-qa-workspaces":
        "Ingen Legal QA-arbeidsområder med aktive tråder ble funnet. Opprett først samtaler i et Legal QA-arbeidsområde for å importere dem.",
      "empty-workspaces-with-names":
        "Legal QA-arbeidsområder funnet ({{workspaceNames}}), men de inneholder ennå ingen aktive tråder. Opprett først samtaler i disse arbeidsområdene for å importere dem.",
      "import-success-with-name": "Tråden ble importert: {{threadName}}",
    },
  },

  // =========================
  // DD SETTINGS (WORKSPACE LINKING SETTINGS)
  // =========================
  "dd-settings": {
    title: "Innstillinger for arbeidsområdekobling",
    description:
      "Kontroller tokenbegrensninger og oppførsel for koblede arbeidsområder",
    "vector-search": {
      title: "Vektorsøk",
      description:
        "Når denne funksjonen er aktivert, utføres semantiske vektorsøk på tvers av alle koblede arbeidsområder for å finne relevante juridiske dokumenter. Systemet konverterer brukerforespørsler til vektorinnbeddinger og matcher dem mot dokumentvektorer i databasen til hvert koblet arbeidsområde. Denne funksjonen fungerer som en reserveløsning når notatgenerering er aktivert, men ikke gir resultater. Når notatgenerering er deaktivert, blir Vektorsøk den primære metoden for å hente informasjon fra koblede arbeidsområder. Søkedybden kontrolleres av innstillingen for Vektortokengrense.",
    },
    "memo-generation": {
      title: "Notatgenerering",
      description:
        "Denne funksjonen genererer automatisk konsise juridiske notater fra dokumenter funnet i koblede arbeidsområder. Når den er aktivert, analyserer systemet innhentede dokumenter for å lage strukturerte sammendrag av juridiske nøkkelpunkter, presedens og relevant kontekst. Disse notatene fungerer som den primære metoden for å inkorporere kunnskap fra koblede arbeidsområder. Hvis notatgenerering mislykkes eller ikke gir resultater, vil systemet automatisk falle tilbake til Vektorsøk (hvis aktivert) for å sikre at relevant informasjon likevel hentes. Lengden og detaljnivået på disse notatene styres av innstillingen for Notat-tokengrense.",
    },
    "base-generation": {
      title: "Grunnleggende juridisk analyse",
      description:
        "Muliggjør generering av foreløpig juridisk analyse basert på brukerens opprinnelige forespørsel, før informasjon fra koblede arbeidsområder inkorporeres. Når aktiv, skaper systemet et grunnleggende analyserammeverk som hjelper til med å veilede de påfølgende dokumentsøk- og notatgenereringsprosessene. Denne grunnanalysen hjelper til med å sikre at svar forblir fokusert på de sentrale juridiske spørsmålene mens støttende informasjon fra koblede arbeidsområder inkorporeres. Omfanget av denne analysen kontrolleres av innstillingen for Base-tokengrense.",
    },
    "linked-workspace-impact": {
      title: "Tokenpåvirkning for koblede arbeidsområder",
      description:
        "Kontrollerer hvordan systemet håndterer sitt tokenbudsjett på tvers av flere koblede arbeidsområder. Når denne funksjonen er aktivert, justerer systemet dynamisk tilgjengelige tokens for hvert arbeidsområde basert på det totale antallet koblede arbeidsområder, noe som sikrer rettferdig fordeling av dataressurser. Dette forhindrer at ett enkelt arbeidsområde dominerer kontekstvinduet, samtidig som det opprettholder omfattende dekning over alle relevante juridiske områder. Denne innstillingen reserverer tokenkapasitet spesifikt for notatgenerering og/eller vektorsøkresultater fra hvert koblet arbeidsområde, noe som kan redusere det totale antallet tokens tilgjengelig for det primære arbeidsområdet når mange arbeidsområder er koblet.",
    },
    "vector-token-limit": {
      title: "Vektortokengrense",
      description:
        "Angir maksimalt antall tokens som tildeles for vektorsøkresultater fra hvert koblet arbeidsområde. Denne grensen gjelder når Vektorsøk brukes, enten som primærmetode (når notatgenerering er deaktivert) eller som reserveløsning (når notatgenerering mislykkes). Høyere grenser muliggjør mer omfattende dokumenthenting, men reduserer tokens tilgjengelig for andre operasjoner.",
    },
    "memo-token-limit": {
      title: "Notat-tokengrense",
      description:
        "Kontrollerer maksimal lengde på genererte juridiske notater fra hvert koblet arbeidsområde. Som den primære metoden for kunnskapsintegrering, oppsummerer disse notatene juridiske nøkkelpunkter fra dokumentene i det koblede arbeidsområdet. Hvis et notat overstiger denne tokengrensen, vil det bli avvist og systemet vil falle tilbake til Vektorsøk (hvis aktivert). Høyere grenser muliggjør mer detaljert juridisk analyse, men kan redusere antallet koblede arbeidsområder som kan inkorporeres.",
    },
    "base-token-limit": {
      title: "Base-token-grense",
      description:
        "Bestemmer maksimal token-lengde for det innledende juridiske analyserammeverket. Denne grensen påvirker hvor omfattende grunnanalysen kan være før informasjon fra tilkoblede arbeidsområder inkorporeres. En høyere grense muliggjør mer detaljert innledende analyse men etterlater mindre plass for inkorporering av innhold fra tilkoblede arbeidsområder.",
    },
    "toast-success": "Innstillinger oppdatert",
    "toast-fail": "Kunne ikke oppdatere innstillingene",
  },

  // =========================
  // WORKSPACE LINKING
  // =========================
  "workspace-linking": {
    title: "Innstillinger for arbeidsområdekobling",
    description:
      "Kontroller tokenbegrensninger og oppførsel for koblede arbeidsområder",
    "vector-search": {
      title: "Vektorsøk",
      description:
        "Reservemetode for å finne relevante dokumenter når notatgenerering mislykkes eller er deaktivert",
    },
    "memo-generation": {
      title: "Notatgenerering",
      description:
        "Primær metode for å inkorporere kunnskap fra koblede arbeidsområder",
    },
    "base-generation": {
      title: "Grunnleggende juridisk analyse",
      description:
        "Generer innledende juridisk problemanalyse fra brukerforespørsler",
    },
    "linked-workspace-impact": {
      title: "Tokenpåvirkning for koblede arbeidsområder",
      description:
        "Reserver tokens for hvert koblet arbeidsområde proporsjonalt med antallet",
    },
    "vector-token-limit": {
      title: "Token-grense for vector-søk",
      description:
        "Maksimalt antall tokens per koblet arbeidsområde for vector-søk",
    },
    "memo-token-limit": {
      title: "Token-grense for memoer",
      description: "Maksimalt antall tokens for generering av juridiske memoer",
    },
    "base-token-limit": {
      title: "Grunnleggende token-grense",
      description:
        "Maksimalt antall tokens for innhenting av grunnleggende innhold",
    },
    "toast-success": "Innstillinger oppdatert med suksess",
    "toast-fail": "Kunne ikke oppdatere innstillingene",
  },
  // =========================
  // BINARY LLM SELECTION
  // =========================
  binary_llm_selection: {
    "secondary-llm-toggle": "Binært LLM-valg",
    "secondary-llm-toggle-description":
      "Aktiver dette for å gi administratorer muligheten til å velge mellom to LLM-modeller i dokumentutkast-modulen.",
    "secondary-llm-toggle-status": "Status: ",
    "secondary-llm-user-level": "Sekundær LLM Brukernivå",
    "secondary-llm-user-level-description":
      "Aktiver dette for å gi ALLE brukere muligheten til å velge mellom to LLM-modeller i dokumentutkast-arbeidsområdet.",
  },
};
