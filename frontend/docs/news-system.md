# News System - Frontend Documentation

## Overview

The IST Legal platform includes a comprehensive news system that informs users about new features, updates, and important announcements. The system supports both system-wide news (defined in frontend code) and local news (stored in the database), with full internationalization support across 7 languages.

## Architecture

### Frontend Components

```
frontend/src/
├── data/news/                          # System news data
│   ├── index.js                        # Main news registry
│   ├── 2024-01-welcome.js             # Individual news items
│   ├── template.js                     # Template for new items
│   └── README.md                       # Documentation
├── hooks/
│   └── useNews.js                      # Main news hook
├── locales/[lang]/
│   └── newsSystemItems.js              # Translations per language
└── components/
    ├── HeaderWorkspace/                # News display components
    └── NewsModal/                      # News modal components
```

### Key Files

- **`frontend/src/data/news/index.js`**: Central registry of all system news items
- **`frontend/src/hooks/useNews.js`**: React hook for managing news state and operations
- **`frontend/src/locales/[lang]/newsSystemItems.js`**: Translation files for all supported languages

## System News vs Local News

### System News

- **Definition**: News items defined in frontend code and deployed with releases
- **Use Cases**: Feature announcements, platform updates, breaking changes
- **Storage**: Frontend JavaScript files (`frontend/src/data/news/`)
- **Localization**: Translation files in `frontend/src/locales/`
- **Dismissal**: Stored per-user in database

### Local News

- **Definition**: News items created by administrators through the admin interface
- **Use Cases**: Organization-specific announcements, maintenance notices
- **Storage**: Database (`news_messages` table)
- **Localization**: Content created directly in desired language
- **Dismissal**: Stored per-user in database

## useNews Hook

### Basic Usage

```javascript
import { useNews } from "@/hooks/useNews";

function NewsComponent() {
  const {
    unreadNews, // Combined system + local news
    allActiveNews, // All active news items
    systemNewsCount, // Count of system news
    databaseNewsCount, // Count of local news
    isLoading, // Loading state
    error, // Error state
    fetchUnreadNews, // Fetch local news
    fetchAllActiveNews, // Fetch all active news
    dismissNews, // Dismiss a news item
  } = useNews();

  return (
    <div>
      {unreadNews.map((news) => (
        <NewsItem
          key={news.id}
          news={news}
          onDismiss={() => dismissNews(news.id, news.isSystemNews)}
        />
      ))}
    </div>
  );
}
```

### Hook Properties

#### State Properties

- **`unreadNews`**: Array of combined system and local news items, sorted by priority and date
- **`allActiveNews`**: Array of all active news items (used for admin views)
- **`systemNewsCount`**: Number of active system news items
- **`databaseNewsCount`**: Number of unread local news items
- **`isLoading`**: Boolean indicating if news is being fetched
- **`error`**: String containing error message if fetch fails

#### Methods

- **`fetchUnreadNews()`**: Fetches unread local news from the server
- **`fetchAllActiveNews()`**: Fetches all active news (system + local)
- **`dismissNews(newsId, isSystemNews)`**: Dismisses a news item for the current user

### News Item Structure

```javascript
{
  id: "system-welcome-2024",              // Unique identifier
  title: "Welcome to IST Legal",          // Display title (fallback)
  content: "Welcome message...",          // Display content (fallback)
  titleKey: "news-system-items.system-welcome-2024.title",    // Translation key
  contentKey: "news-system-items.system-welcome-2024.content", // Translation key
  priority: "high",                       // Priority level
  isActive: true,                         // Whether news is active
  isSystemNews: true,                     // System vs local news
  targetRoles: null,                      // Target user roles (null = all)
  expiresAt: null,                        // Expiration date (null = never)
  createdAt: "2024-01-15T00:00:00Z",     // Creation timestamp
}
```

## Creating System News Items

### Step 1: Add to News Registry

Add your news item to `frontend/src/data/news/index.js`:

```javascript
export const systemNewsItems = [
  // ... existing items
  {
    id: "system-new-feature-2024",
    title: "New Feature Available",
    content: "We've added an exciting new feature...",
    titleKey: "news-system-items.system-new-feature-2024.title",
    contentKey: "news-system-items.system-new-feature-2024.content",
    priority: "high",
    isActive: true,
    isSystemNews: true,
    targetRoles: null,
    expiresAt: null,
    createdAt: "2024-01-15T00:00:00Z",
  },
];
```

### Step 2: Add Translations

**CRITICAL**: Add translations to ALL supported language files:

#### Supported Languages

- English (`en`)
- Swedish (`sv`)
- French (`fr`)
- German (`de`)
- Norwegian (`no`)
- Polish (`pl`)
- Kinyarwanda (`rw`)

#### Translation File Example

```javascript
// frontend/src/locales/en/newsSystemItems.js
const TRANSLATIONS = {
  "news-system-items": {
    "system-new-feature-2024": {
      title: "New Feature Available",
      content:
        "We've introduced a powerful new feature that allows you to [describe feature]. Access it from **Settings > Feature Name** to get started.",
    },
    // ... other items
  },
};

export default TRANSLATIONS;
```

### Step 3: Test Implementation

1. Verify news appears in UI
2. Test dismissal functionality
3. Check all language translations
4. Validate role-based filtering (if applicable)

## Configuration Options

### Priority Levels

```javascript
// Visual indicators and sorting order
"urgent"; // Red badge, highest priority
"high"; // Orange badge, high priority
"medium"; // Blue badge, standard priority
"low"; // Gray badge, lowest priority
```

### Target Roles

```javascript
null["admin"][("admin", "manager")]["default"]; // Visible to all users // Only admin users // Admin and manager users // Only default users
```

### Expiration

```javascript
null; // Never expires
("2024-12-31T23:59:59Z"); // Expires on specific date
```

## Internationalization

### Translation Structure

Each news item requires translations in all supported languages:

```javascript
// Pattern for translation keys
"news-system-items.{newsId}.title";
"news-system-items.{newsId}.content";
```

### Translation Guidelines

1. **Professional Tone**: Use formal, business-appropriate language
2. **Specific Instructions**: Tell users exactly how to access new features
3. **Consistent Terminology**: Use the same terms as the UI
4. **Preserve Markdown**: Keep formatting like **bold** and [links](url)
5. **Cultural Sensitivity**: Adapt content appropriately for each locale

### Example Translations

**English:**

```javascript
"system-document-builder-2024": {
  title: "New Document Builder Available",
  content: "We've introduced a powerful Document Builder that allows you to create complex legal documents using AI assistance. Access it from **General Settings > Document Builder**."
}
```

**Swedish:**

```javascript
"system-document-builder-2024": {
  title: "Ny Dokumentbyggare Tillgänglig",
  content: "Vi har introducerat en kraftfull Dokumentbyggare som låter dig skapa komplexa juridiska dokument med AI-assistans. Kom åt den från **Allmänna Inställningar > Dokumentbyggare**."
}
```

## State Management

### News Loading Flow

1. **Hook Initialization**: `useNews` hook initializes with empty state
2. **System News Loading**: System news loaded from frontend data files
3. **Dismissed News Check**: Fetch user's dismissed system news from server
4. **Local News Fetch**: Fetch unread local news from server
5. **Combination & Sorting**: Combine and sort all news by priority and date

### Dismissal Flow

1. **User Dismisses**: User clicks dismiss button on news item
2. **API Call**: Send dismissal request to appropriate endpoint
3. **State Update**: Remove dismissed item from local state
4. **Persistence**: Dismissal stored in database for future sessions

### Error Handling

```javascript
const { error, isLoading } = useNews();

if (error) {
  // Handle error state
  console.error("News loading failed:", error);
}

if (isLoading) {
  // Show loading indicator
  return <LoadingSpinner />;
}
```

## Testing

### Test Files

- **`frontend/src/__tests__/newsSystem.test.js`**: Tests for useNews hook and system news
- **`frontend/src/__tests__/newsTranslations.test.js`**: Tests for translation coverage

### Key Test Areas

1. **Hook Functionality**: State management, API calls, error handling
2. **System News Structure**: Data validation, required fields
3. **Translation Coverage**: All languages, consistent structure
4. **Dismissal Logic**: Both system and local news dismissal
5. **Role Filtering**: Target role functionality
6. **Priority Sorting**: Correct news ordering

### Running Tests

```bash
# Run all news system tests
npm test -- newsSystem.test.js

# Run translation tests
npm test -- newsTranslations.test.js

# Run with verbose output
npm test -- newsSystem.test.js --verbose
```

## Performance Considerations

### Optimization Strategies

1. **Memoized Translation Function**: Prevents infinite re-renders
2. **Selective State Updates**: Only update relevant state properties
3. **Efficient Sorting**: Sort combined news arrays efficiently
4. **Lazy Loading**: Load news data only when needed

### Memory Management

- News items are lightweight objects
- Dismissed news IDs stored efficiently
- Translation files loaded on-demand
- No memory leaks in hook cleanup

## Common Issues & Solutions

### Issue: Infinite Re-renders

**Cause**: Translation function dependency in useEffect
**Solution**: Use memoized translation function

```javascript
const memoizedT = useMemo(() => t, []);

useEffect(() => {
  // Use memoizedT instead of t
}, [memoizedT]);
```

### Issue: Missing Translations

**Cause**: Forgot to add translations to all language files
**Solution**: Add to ALL 7 supported languages

### Issue: News Not Appearing

**Cause**: Incorrect `isActive` flag or expired news
**Solution**: Check `isActive: true` and `expiresAt` date

### Issue: Role Filtering Not Working

**Cause**: Incorrect `targetRoles` format
**Solution**: Use array format: `["admin"]` not `"admin"`

## Best Practices

### Content Guidelines

1. **Be Concise**: Keep titles under 60 characters
2. **Be Actionable**: Tell users exactly what to do
3. **Include Benefits**: Explain how features help users
4. **Use Markdown**: Format important text appropriately
5. **Provide Context**: Link to relevant documentation

### Technical Guidelines

1. **Unique IDs**: Use descriptive IDs with year suffix
2. **Consistent Naming**: Follow `system-[feature]-[year]` pattern
3. **Proper Dates**: Use ISO 8601 format
4. **Complete Translations**: Never skip any language
5. **Test Thoroughly**: Verify all functionality works

### Development Workflow

1. **Plan Content**: Write clear, helpful announcements
2. **Add Data**: Create news item in registry
3. **Add Translations**: Complete all language files
4. **Test Locally**: Verify display and functionality
5. **Update Tests**: Add test cases if needed
6. **Document Changes**: Update relevant documentation

## Related Documentation

- [Internationalization](./internationalization.md)
- [State Management](./state-management.md)
- [Testing](./testing.md)
- [UI Components](./ui-components.md)

## API Integration

The frontend news system integrates with several backend endpoints:

- **`GET /api/news/unread`**: Fetch unread local news
- **`GET /api/news/all-active`**: Fetch all active news
- **`POST /api/news/system/{id}/dismiss`**: Dismiss system news
- **`POST /api/news/{id}/dismiss`**: Dismiss local news
- **`GET /api/news/dismissed-system`**: Get dismissed system news IDs

See [Server Documentation](../server/docs/news-system.md) for detailed API documentation.
