# Mock Legal Cases for Document Builder Testing

This directory contains mock legal cases designed to test the three document builder flows in the ISTLegal system:

## Flow Types

### 1. Main Document Flow (`mainDoc.js`)
Used when there's a primary document that serves as the foundation for the response. The system analyzes this main document and builds a response around it.

**Example Use Case**: Drafting a response to a business law court case submission.

### 2. No Main Document Flow (`noMainDoc.js`)
Used when creating legal documents from scratch based on multiple supporting documents and background information.

**Example Use Case**: Drafting a statement of claim based on background descriptions and evidence.

### 3. Compliance Review Flow
Used when reviewing business documents against established corporate guidelines to identify potential violations and compliance gaps.

**Example Use Case**: Auditing customer onboarding processes against corporate data governance policies.

## Directory Structure

```
Mock Legal Cases/
├── README.md
├── Main Document Flow Cases/
│   └── Business Law Court Response/
│       ├── README.md
│       ├── main-document-court-submission.md
│       ├── supporting-evidence-1-licensing-agreement.md
│       ├── supporting-evidence-2-email-correspondence.md
│       └── case-background.md
├── No Main Document Flow Cases/
│   └── Statement of Claim/
│       ├── README.md
│       ├── background-description.md
│       ├── witness-statement-1-project-manager.md
│       ├── witness-statement-2-it-director.md
│       ├── contract-evidence.md
│       └── correspondence.md
└── Compliance Review Flow Cases/
    └── Corporate Data Governance Audit/
        ├── README.md
        ├── corporate-data-governance-policy.md
        ├── financial-data-handling-guidelines.md
        ├── customer-onboarding-process.md
        ├── third-party-vendor-agreement.md
        ├── employee-access-logs.md
        ├── marketing-campaign-data.md
        └── financial-reporting-procedures.md
```

## How to Use

1. **Upload Documents**: Upload all documents from a case folder to a workspace
2. **Select Flow Type**:
   - For Main Document Flow: Select the main document when prompted
   - For No Main Document Flow: Don't select a main document
   - For Compliance Review Flow: Specify reference files (guidelines) and documents to review
3. **Test Document Generation**: Use the document builder to generate legal documents based on the uploaded materials

## Case Descriptions

### Main Document Flow Cases

#### Business Law Court Response
- **Scenario**: Responding to a motion for summary judgment in a contract dispute
- **Main Document**: Court submission from opposing party
- **Supporting Materials**: Evidence, case law, background information
- **Expected Output**: Legal response brief addressing the motion

### No Main Document Flow Cases

#### Statement of Claim
- **Scenario**: Drafting an initial statement of claim for a breach of contract case
- **Supporting Materials**: Background descriptions, witness statements, contract evidence
- **Expected Output**: Comprehensive statement of claim document

### Compliance Review Flow Cases

#### Corporate Data Governance Audit
- **Scenario**: Reviewing business documents for compliance with corporate data governance policies
- **Reference Files**: Corporate data governance policy, financial data handling guidelines
- **Documents to Review**: Customer onboarding process, vendor agreements, access logs, marketing data, financial procedures
- **Expected Output**: Compliance audit report identifying violations and recommendations

## Testing Guidelines

1. **Document Quality**: Ensure all documents are properly formatted and contain realistic legal content
2. **Flow Testing**: Test all three flows with different document combinations
3. **Edge Cases**: Test with missing documents, corrupted files, or unusual document structures
4. **Performance**: Monitor processing time and token usage for large document sets
5. **Violation Detection**: For compliance review flow, verify that all planted violations are detected

## Planted Violations (Compliance Review Flow)

The compliance review case includes intentionally planted violations to test detection capabilities:

### High-Risk Violations
- Unauthorized international data transfers
- Financial data stored without encryption
- Excessive data collection beyond business needs

### Medium-Risk Violations
- Missing consent documentation
- Excessive employee access privileges
- Data retention beyond policy limits

### Low-Risk Violations
- Incomplete audit trails
- Unclear privacy notices
- Missing vendor security assessments

## Adding New Cases

When adding new mock cases:

1. Create a new folder under the appropriate flow type
2. Include a README.md explaining the scenario
3. Provide realistic legal documents in markdown format
4. Ensure documents contain sufficient content for meaningful testing
5. Include edge cases and challenging scenarios
6. For compliance cases, document planted violations clearly

## Legal Disclaimer

These are mock legal cases created for testing purposes only. They do not represent real legal situations and should not be used for actual legal advice or proceedings.
