---
description: Guidelines for document synchronization
globs: server/jobs/**/*.js,server/utils/collectorApi/**/*.js
alwaysApply: false
---

# Document Synchronization

## Overview

The document synchronization system ensures that external documents (GitHub repos, Confluence pages, YouTube transcripts, web links) are kept up-to-date in the RAG system. This is particularly important for maintaining the accuracy and relevance of RAG results over time.

## Architecture

The document synchronization system spans both the server and collector components:

```
server/
├── jobs/
│   ├── sync-watched.documents.js   # Main sync orchestration
│   └── helpers/index.js            # Helper functions
├── utils/
│   └── collectorApi/               # Interface to collector
└── models/
    └── documentSyncQueue.js        # Sync scheduling

collector/
└── extensions/
    └── resync/                     # Resync functionality
        ├── index.js                # Main resync logic
        └── scheduler.js            # Resync scheduling
```

## Sync Process Flow

### Server-Side Process

The `server/jobs/sync-watched.documents.js` file orchestrates the re-sync process:

1. Retrieves stale documents from the `DocumentSyncQueue`
2. Checks if the collector service is online
3. For each stale document, fetches new content
4. Compares with stored content
5. If content has changed, re-embeds in vector database and updates local JSON
6. Updates scheduling for next sync

### Collector-Side Process

The `collector/extensions/resync/` module handles the actual content retrieval:

1. Receives resync request from server
2. Identifies the appropriate source handler based on document type
3. Fetches latest content from the source
4. Processes content using the same pipeline as initial ingestion
5. Returns processed content to server

## Key Components

### Sync Watched Documents

The `server/jobs/sync-watched.documents.js` file is the main entry point for the sync process:

```javascript
// Simplified example
async function syncWatchedDocuments() {
  // Get stale documents
  const staleDocuments = await DocumentSyncQueue.getStaleDocuments();

  // Check collector availability
  if (!await isCollectorOnline()) {
    log('Collector is offline, exiting...');
    return conclude();
  }

  // Process each stale document
  for (const doc of staleDocuments) {
    try {
      // Fetch new content
      const newContent = await fetchUpdatedContent(doc);

      // Compare with existing content
      if (hasContentChanged(doc, newContent)) {
        // Update document
        await updateDocument(doc, newContent);

        // Re-embed in vector database
        await reEmbedDocument(doc);
      }

      // Update sync schedule
      await updateSyncSchedule(doc);
    } catch (error) {
      handleSyncError(doc, error);
    }
  }

  return conclude();
}
```

### Helpers

The `server/jobs/helpers/index.js` file provides helper functions:

- `log(stringContent)`: Logs messages for worker threads or parent process
- `conclude()`: Signals that the job is complete
- `updateSourceDocument(docPath, jsonContent)`: Writes updated JSON to disk after re-sync
- `isCollectorOnline()`: Checks if the collector service is available
- `hasContentChanged(doc, newContent)`: Compares content to detect changes

### Collector API

The `server/utils/collectorApi/` directory contains functions for interacting with the collector service:

- `fetchUpdatedContent(doc)`: Fetches new content from external sources
- `processContent(content)`: Processes and converts content
- `storeContent(content)`: Stores updated content

### Document Sync Queue

The `server/models/documentSyncQueue.js` file manages the sync scheduling:

- Tracks which documents need to be synced and when
- Implements different sync frequencies (hourly, daily, weekly)
- Handles retry logic for failed syncs
- Maintains sync history and status

## Error Handling

- **Continuous Failures**: If a source repeatedly fails to re-sync, it is eventually removed from the watch list
- **Offline Collector**: If the collector is offline, the script logs the issue and exits
- **Invalid Document Types**: Documents of an unknown type or missing crucial metadata are dropped from the queue
- **Source-Specific Errors**: Each source type has specific error handling (API rate limits, authentication issues, etc.)
- **Partial Updates**: If only part of a document can be updated, the system handles partial updates gracefully

## Relationship to RAG

Document synchronization ensures that LLM search is grounded in the latest content. When a document changes upstream, the new content is automatically pulled, re-embedded, and made available for queries. This is critical for:

- Maintaining accuracy of responses
- Ensuring compliance with latest information
- Providing users with up-to-date context
- Supporting time-sensitive applications

## Best Practices

### Implementation

- Follow the established patterns for document synchronization
- Use appropriate error handling for sync operations
- Consider performance implications of frequent syncs
- Document complex sync logic
- Test sync with various document types and sources
- Implement proper security checks for external sources

### Configuration

- Set appropriate sync frequencies based on document update patterns
- Configure retry policies for transient errors
- Implement backoff strategies for rate-limited APIs
- Set up monitoring and alerting for sync failures
- Document sync configuration options
