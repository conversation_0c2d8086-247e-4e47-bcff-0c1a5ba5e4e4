@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* ==============================================
   Font Faces
============================================== */
@font-face {
  font-family: "Roobert";
  src: url("/fonts/Roobert-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: "Roobert";
  src: url("/fonts/Roobert-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "Roobert";
  src: url("/fonts/Roobert-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Roobert";
  src: url("/fonts/Roobert-SemiBold.ttf") format("truetype");
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: "Roobert";
  src: url("/fonts/Roobert-SemiBoldItalic.ttf") format("truetype");
  font-weight: 600;
  font-style: italic;
}

/* ==============================================
   Root / Globals
============================================== */
:root {
  --header-height: 65px;
}

/* ==============================================
   Markdown Prose Styles
============================================== */
.prose {
  max-width: 100%;
  color: inherit;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: inherit;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.prose h1 {
  font-size: 1.75em;
}

.prose h2 {
  font-size: 1.25em;
}

.prose h3 {
  font-size: 1.125em;
  font-style: italic;
}

.prose p {
  margin-top: 1em;
  margin-bottom: 1em;
}

.prose ul,
.prose ol {
  margin-top: 1em;
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.prose ul {
  list-style-type: disc;
}

.prose ol {
  list-style-type: decimal;
}

.prose li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  display: list-item;
  line-height: 1.6;
}

.prose blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1em;
  margin-top: 1em;
  margin-bottom: 1em;
  font-style: italic;
}

.prose code {
  font-family: monospace;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.9em;
}

.prose pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 1em;
  border-radius: 5px;
  overflow-x: auto;
  margin-top: 1em;
  margin-bottom: 1em;
}

.prose pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  font-size: 0.9em;
}

.prose a {
  color: #3b82f6;
  text-decoration: underline;
}

.prose a:hover {
  text-decoration: none;
}

.dark .prose {
  color: inherit;
}

.dark .prose a {
  color: #60a5fa;
}

.dark .prose blockquote {
  border-left-color: #4b5563;
}

.dark .prose code {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark .prose pre {
  background-color: rgba(255, 255, 255, 0.1);
}

* {
  box-sizing: border-box;
  border-color: var(--border);
}

html {
  font-size: 14px;
}

body {
  padding: 0;
  margin: 0;
  font-family:
    "Roobert",
    -apple-system,
    BlinkMacSystemFont,
    Segoe UI,
    Roboto,
    sans-serif;
}

#root,
#dark {
  height: 100vh;
  background-color: var(--background);
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  color: var(--foreground);
}

ul,
ol {
  margin-left: 20px;
}

ul li {
  list-style-type: disc;
}

ol li {
  list-style-type: decimal;
}

li {
  margin-left: 20px;
  margin-top: 10px;
}

pre {
  text-wrap: auto;
}

.text-foreground {
  color: var(--foreground);
}

.list-disc {
  list-style-type: disc;
  margin-left: 20px;
}

.list-decimal {
  list-style-type: decimal;
  margin-left: 20px;
}

/* ==============================================
   Grayscale hover
============================================== */
.grr {
  grid-template-columns: repeat(2, 1fr);
}

.greyC {
  -webkit-filter: grayscale(100%);
  filter: gray;
  transition: 0.4s;
}

.greyC:hover {
  -webkit-filter: none;
  filter: none;
  transition: 0.4s;
}

/* ==============================================
   Progress
============================================== */
.progressContainer {
  line-height: 13px;
}

.progress {
  position: relative;
  list-style: none;
}

.progress__item {
  position: relative;
  min-height: 75px;
  counter-increment: list;
  padding-left: 0.5rem;
}

.progress__item:before {
  content: "";
  position: absolute;
  left: -1.25rem;
  top: 29px;
  bottom: 0;
  width: 1px;
  opacity: 0.8;
  border-left: 3px solid var(--primary-color);
  z-index: 0;
}

.progress__item:after {
  content: counter(list);
  position: absolute;
  top: 0;
  left: -2.1rem;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  background: transparent;
  font-weight: 400;
  font-size: 13px;
  line-height: 2rem;
  text-align: center;
  border: 3px solid var(--primary-hover);
  z-index: 1;
}

/* Hide border for Last Step */
.progress__item:last-child:before {
  border: none;
}

.progress__item--completed:after {
  content: "\2713";
  font-weight: 400;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Active */
.progress__item--active:after {
  background: transparent;
  border: 3px solid var(--primary-color);
  display: flex;
  font-weight: bold;
  color: var(--primary-color);
  align-items: center;
  justify-content: center;
  text-align: center;
}

.progress__item--inactive {
  opacity: 0.5;
}

.progress__item--inactive:after {
  background: transparent;
  border: 3px solid var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.progress__item--active.progress__item:before {
  animation: progressLine 1s infinite;
}

.progress__item--active.progress__item:after {
  animation: circularLoader 1s infinite;
}

.progress__item:last-child:before {
  display: none;
}

@keyframes circularLoader {
  0% {
    transform: rotate(0deg);
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-top: 2px solid #3b82f6;
  }
  100% {
    transform: rotate(360deg);
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-top: 2px solid #3b82f6;
  }
}

@keyframes progressLine {
  0% {
    opacity: 0.3;
    height: 0%;
  }
  50% {
    opacity: 1;
    height: calc(100% - 26px); /* 26px is the height of the circular loader */
  }
  100% {
    opacity: 0.3;
    height: calc(100% - 26px); /* 26px is the height of the circular loader */
  }
}

@keyframes progressLineFull {
  0% {
    opacity: 0.3;
    height: 0%;
  }
  50% {
    opacity: 1;
    height: calc(100% - 26px); /* 26px is the height of the circular loader */
  }
  100% {
    opacity: 0.3;
    height: calc(100% - 26px); /* 26px is the height of the circular loader */
  }
}

/* Apply full-line animation when step has subtask list */
.progress__item--has-subtasks.progress__item:before {
  animation: progressLineFull 1s infinite;
}

.progress__title {
  padding: 0.4rem 0 0.5rem;
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.progress__info {
  font-size: 13px;
}

/* Sub-task list styling */
.progress-subtasks {
  list-style: none;
  margin: 0;
  padding: 0;
  margin-top: 0.5rem;
  margin-left: 0.5rem;
}

.progress-subtask-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  font-size: 0.75rem;
  position: relative;
  left: -1.5rem;
}

.progress-subtask-icon {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-subtask-text {
  font-size: 0.75rem;
  line-height: 1.2;
  color: var(--foreground);
  opacity: 0.9;
}

/* ==============================================
   Chat Animations
============================================== */
.chat__message {
  transform-origin: 0 100%;
  transform: scale(0);
  animation: message 0.15s ease-out 0s forwards;
  animation-delay: 500ms;
}

@keyframes message {
  0% {
    max-height: 100%;
  }
  80% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    max-height: 100%;
    overflow: visible;
    padding-top: 1rem;
  }
}

.doc__source {
  transform-origin: 0 100%;
  transform: scale(0);
  animation: message2 0.15s ease-out 0s forwards;
  animation-delay: 50ms;
}

@keyframes message2 {
  0% {
    max-height: 100%;
  }
  80% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    max-height: 100%;
    overflow: visible;
  }
}

/* ==============================================
   Sidebar fade
============================================== */
@media (prefers-color-scheme: light) {
  .sidebar-items:after {
    content: " ";
    position: absolute;
    left: 0;
    right: 0px;
    height: 4em;
    top: 69vh;
    z-index: 1;
    pointer-events: none;
  }
}

@media (prefers-color-scheme: dark) {
  .sidebar-items:after {
    content: " ";
    position: absolute;
    left: 0;
    right: 0px;
    height: 4em;
    top: 69vh;
    z-index: 1;
    pointer-events: none;
  }
}

@media (prefers-color-scheme: light) {
  .fade-up-border {
    background: linear-gradient(
      to bottom,
      rgba(220, 221, 223, 10%),
      rgb(220, 221, 223) 89%
    );
  }
}

@media (prefers-color-scheme: dark) {
  .fade-up-border {
    background-color: red;
  }
}

.tooltip {
  background-color: var(--foreground) !important;
  color: var(--elevated) !important;
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
  border-radius: 0.375rem !important;
  font-size: 0.875rem !important;
}

#chat-history::-webkit-scrollbar,
#chat-container::-webkit-scrollbar,
.no-scroll::-webkit-scrollbar {
  display: none !important;
}

.z-99 {
  z-index: 99;
}
.z-98 {
  z-index: 98;
}

.file-uploader {
  width: 100% !important;
  height: 100px !important;
}

.grid-loader > circle {
  fill: #008eff;
}

dialog {
  pointer-events: none;
  opacity: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

dialog[open] {
  opacity: 1;
  pointer-events: inherit;
}

dialog::backdrop {
  background: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}

.backdrop {
  background: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}

.animate-slow-pulse {
  transform: scale(1);
  animation: subtlePulse 20s infinite;
  will-change: transform;
}

@keyframes subtlePulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes subtleShift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.login-input-gradient {
  background: linear-gradient(
    180deg,
    rgba(61, 65, 71, 0.3) 0%,
    rgba(44, 47, 53, 0.3) 100%
  ) !important;
  box-shadow: 0px 4px 30px rgba(0, 0, 0, 0.25);
}

.white-fill {
  fill: white;
}

.tip:before {
  content: "";
  display: block;
  width: 0;
  height: 0;
  position: absolute;

  border-bottom: 8px solid transparent;
  border-top: 8px solid rgba(255, 255, 255, 0.5);
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-radius: 0px 0px 0px 5px;
  left: 1%;

  top: 100%;
}

.user-reply > div:first-of-type {
  border: 2px solid white;
}

.reply > *:last-child::after {
  content: "|";
  animation: blink 1.5s steps(1) infinite;
  color: white;
  font-size: 14px;
}

@keyframes blink {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.radio-container:has(input:checked) {
  /* border-blue-500 => #3b82f6 */
  border: 1px solid #3b82f6;
  /* bg-blue-400/10 => #60a5fa at 0.1 alpha => rgba(96,165,250,0.1) */
  background-color: rgba(96, 165, 250, 0.1);
  /* text-blue-800 => #1e40af */
  color: #1e40af;
}

.rti--container {
  background-color: #fff !important;
  color: #fff !important;
  font-size: 0.875rem !important; /* Tailwind .text-sm */
  border-radius: 0.5rem !important; /* ~8px */
  padding: 0.625rem !important; /* ~10px (.p-2.5) */
}
/* For placeholder color/opacity: */
.rti--container::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Toastify text formatting */
.Toastify__toast-body {
  white-space: pre-line;
}

/* ==============================================
   Slide animations
============================================== */
@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 400px;
    opacity: 1;
  }
}
.slide-down {
  animation: slideDown 0.3s ease-out forwards;
}

@keyframes slideUp {
  from {
    max-height: 400px;
    opacity: 1;
  }
  to {
    max-height: 0;
    opacity: 0;
  }
}
.slide-up {
  animation: slideUp 0.3s ease-out forwards;
}

.input-label {
  font-size: 14px;
  font-weight: 700; /* "bold" */
}

/* ==============================================
   Markdown Styles
============================================== */
/* Markdown styles have been moved to the centralized Markdown component */
.font-italic {
  font-style: italic;
}

/* File Rows */
.file-row:nth-child(even),
.file-row:nth-child(odd) {
  background-color: var(--elevated);
  opacity: 1;
}
.file-row.selected:nth-child(even),
.file-row.selected:nth-child(odd) {
  background-color: var(--secondary-hover);
  opacity: 1;
}

/* ==============================================
   Upload Modal: replaced @apply with normal CSS
============================================== */
@media (max-width: 1330px) {
  .upload-modal {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
    overflow: hidden; /* "no-scroll" presumably means no scrolling */
  }
}

.upload-modal {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 1.5rem; /* ~gap-x-6 = 24px */
  justify-content: center;
}
.upload-modal-arrow {
  margin-top: 18%;
}

/* ==============================================
   Scrollbar container: remove non‐standard props
============================================== */

.white-scrollbar {
  overflow-y: scroll;
  margin-right: 8px;
  /* If you want custom scrollbars in Chrome/Safari, use ::-webkit-scrollbar rules
     or remove entirely if not needed. */
}

/* Webkit browsers (Chrome, Safari) */
.white-scrollbar::-webkit-scrollbar {
  width: 3px;
  background-color: #18181b;
}

.white-scrollbar::-webkit-scrollbar-track {
  background-color: #18181b;
  margin-right: 8px;
}

.white-scrollbar::-webkit-scrollbar-thumb {
  background-color: #ffffff;
  border-radius: 4px;
  border: 2px solid #18181b;
}

.white-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #cccccc;
}

/* Recharts rendering styles */
.recharts-text > * {
  fill: #fff;
}

.recharts-legend-wrapper {
  margin-bottom: 10px;
}

.text-tremor-content {
  padding-bottom: 10px;
}

.file-upload {
  -webkit-animation: fadein 0.3s linear forwards;
  animation: fadein 0.3s linear forwards;
}

.file-upload-fadeout {
  -webkit-animation: fadeout 0.3s linear forwards;
  animation: fadeout 0.3s linear forwards;
}

@-webkit-keyframes fadein {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes fadein {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@-webkit-keyframes fadeout {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes fadeout {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.modal-file-name::first-letter {
  text-transform: uppercase;
}

/* RW UI */
.right-block {
  position: relative;
  width: 100%;
  background-image:
    linear-gradient(
      to bottom,
      rgba(57, 130, 163, 0.6),
      rgba(57, 130, 163, 0.3)
    ),
    url("https://media.istockphoto.com/id/1343982895/photo/small-flags-of-rwanda-on-a-blurry-background-of-the-city.jpg?s=612x612&w=0&k=20&c=st7An2wDA9rgiPQSU-RQzI5MK2ZE7eW-aU0npGI4DHc=");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.login-border-left {
  height: 110px;
  width: 25px;
  background-color: white;
}
.visite-website-btn {
  background-color: transparent;
  border: 3px solid white;
  color: white;
  font-weight: bold;
  border-radius: 50px;
  padding: 6px 20px;
}
.visite-website-btn:hover {
  background-color: white;
  color: var(--normal-text);
  border: 3px solid white;
}
.submit-btn-rw {
  background-color: #3982a3;
}
.login-form- .form-group-rw {
  margin: 10px 0;
  text-align: left;
}

/* login */
.tenderFlow-icons {
  display: none;
}
.login-form .submit-btn {
  background-color: var(--foreground);
}

/* Tenderflow */
.tenderFlow-svg {
  display: inline;
}
.tenderFlow-icons {
  border-left: 2px solid #989898;
  padding-left: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 7px;
}
.tenderFlow-icons .tender-icons {
  color: #989898;
}
.tenderFlow-icons .tender-icons:hover {
  color: #2d2b2b;
  cursor: pointer;
}
.login-form .submit-btn {
  background-color: white;
  color: var(--normal-primary);
}
.forgot-password {
  color: white;
}
.form-group input::placeholder {
  color: whitesmoke;
}

.tenderFlow-logo {
  width: 210px;
}

.tenderFlow-login {
  background-color: #c18c06;
  padding: 60px 20px;
  border-radius: 10px;
  box-shadow: rgba(22, 22, 23, 0.2) 0px 7px 29px 0px;
}

/* login form  */
.login-form .form-group {
  margin: 10px 0;
  text-align: left;
}
.form-group input {
  width: 100%;
  border-radius: 6px;
  padding: 5px 10px;
  color: white;
  font-size: 13px;
  border: 2px solid white;
  background-color: transparent;
  outline: none;
}
.login-form .form-group input:focus {
  outline: none;
  border-radius: 8px;
  border: 2px solid white;
}
.form-group input:focus {
  outline: none;
  outline: none;
  border-radius: 8px;
  border: 2px solid white;
}
.login-form .form-group .error {
  color: rgb(204, 114, 114);
  font-size: 11px;
}
.submit-block {
  padding-top: 10px;
}
.login-form .submit-btn:hover {
  opacity: 0.8;
}
.login-form .submit-btn:focus {
  outline: none;
}
.forgot-password {
  color: #7a7a78;
}
.login-form .signup-btn {
  font-size: 14px;
}
.login-form .signup-btn:hover {
  cursor: pointer;
  opacity: 0.8;
}
.login-form .signup-btn:focus {
  outline: none;
}
.crossPlatform-login {
  padding-bottom: 15px;
}
.crossPlatform-login .apple-icn {
  background-color: rgb(240, 239, 239);
  padding: 4px;
  border-radius: 8px;
}
.crossPlatform-login .log-images img:hover {
  cursor: pointer;
}
.mainBlock {
  background-color: #e3eff1;
  border-radius: 0 8px 8px 0;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.public-mode-block {
  padding-top: 19px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid rgba(122, 196, 241, 0.24);
}
.public-mode-btn {
  background-color: transparent;
  border: 2px solid #d7d5c8;
  color: #d7d5c8;
  width: 80%;
}
.public-mode-btn:focus {
  outline: none;
}
.public-mode-btn:hover {
  cursor: pointer;
  border: 2px solid whitesmoke;
  color: whitesmoke;
}
/* END */

.bg-opacity-50 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* END */

.loading-icons {
  border-color: #1a4250;
}

.modal-search-block {
  background-color: white;
}
.dark-theme .modal-search-block {
  background-color: var(--foreground);
}
/* END */

.light-modal-bg {
  background-color: var(--secondary-hover);
  border-radius: 6px;
}
.dark-theme .light-modal-bg {
  background-color: var(--background) !important;
  border-radius: 6px;
}
/* END */

.linked-workspace {
  border-bottom: 1px solid #bbe2f5;
}
.linked-workspace:hover {
  background-color: var(--secondary-color);
}
.linked-workspace .linked-input-o {
  border: 1px solid var(--primary-color);
  border-radius: 3px;
}
.linked-workspace .linked-input {
  background-color: var(--primary-color);
}

.sidebar-sub-list:hover {
  color: white;
  background-color: var(--primary-color);
}

.setting-tab-Inactive {
  color: var(--primary-color);
  border-bottom: 2px solid transparent;
  margin-bottom: 15px;
  font-weight: thin;
}
.setting-tab-Inactive:hover {
  color: var(--foreground);
  border-bottom: 2px solid var(--foreground);
  font-weight: regular;
}
.dark-theme .setting-tab-Inactive:hover {
  color: var(--dark-text);
  border-bottom: 2px solid var(--dark-text);
  font-weight: regular;
}
/* END */

.light-modal-bg .profile-list {
  color: var(--foreground);
}
.dark-theme .light-modal-bg .profile-list {
  color: var(--dark-text);
}
.light-modal-bg .profile-list:hover {
  background-color: var(--primary-color);
  color: white;
}

.reset-palette-btn {
  background-color: var(--secondary-hover);
  color: var(--primary-color) !important;
  font-size: 14px;
}

.upload-border-hover {
  border: 4px solid var(--primary-color);
}

.shadow-bg:hover {
  background-color: var(--secondary-hover);
}
.dark-theme .shadow-bg:hover {
  background-color: var(--background);
}
.primary-bg {
  background-color: var(--primary-color);
  opacity: 1;
}
.primary-bg:hover {
  background-color: var(--primary-hover);
  opacity: 1;
}
.primary-bg:focus {
  outline: none;
}
/* END */

.navbar-link-Active {
  color: var(--foreground);
}

.navbar-link-Inactive {
  color: var(--muted);
}

/* END */

.qura-login-btn {
  color: #0f5132;
  background-color: #d1e7dd;
  border: 1px solid #0f5132;
}

.dark-theme .qura-login-btn {
  color: #7beab6;
  background-color: var(--background);
  border-color: #7beab6;
}
/* END */

.upgrade-chat-btn {
  background-color: var(--secondary-color);
  border: 1px solid #c0c4c8;
}
.dark-theme .upgrade-chat-btn {
  background-color: var(--background);
  border: 1px solid #c0c4c8;
  color: var(--foreground);
}

.primary-o-btn {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}
.dark-theme .primary-o-btn {
  background-color: transparent;
  color: var(--dark-text);
  border: 2px solid var(--dark-text);
}
.primary-o-btn:hover {
  opacity: 0.7;
}
.dark-theme .primary-o-btn:hover {
  opacity: 0.7;
  background-color: var(--background);
}
.primary-o-btn:focus {
  outline: none;
}
/* END */

.secondary-btn {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}
.dark-theme .secondary-btn {
  background-color: transparent;
  color: var(--dark-text);
  border: 2px solid var(--dark-text);
}
.secondary-btn:hover {
  opacity: 0.7;
}
.dark-theme .secondary-btn:hover {
  opacity: 0.7;
  background-color: var(--background);
}
/* END */
.border-bottom {
  border-bottom: 1px solid #eeeeee;
}
.dark-theme .border-bottom {
  border-bottom: 1px solid #3d4f57;
}
.border-top {
  border-top: 1px solid #eeeeee;
}
.dark-theme .border-top {
  border-top: 1px solid #3d4f57;
}
.border-all {
  border: 1px solid #ede8e8;
}
.dark-theme .border-all {
  border: 1px solid #586b73;
}
/* END */

.navbar-blk-image {
  height: 45px;
  min-width: 85px;
  max-width: 120px;
  overflow: hidden;
  margin-left: 12px;
}
.navbar-blk-image- {
  height: 40px;
  min-width: 85px;
  max-width: 120px;
  margin-left: 48px;
}
.main-block-image- {
  min-width: 60%;
  max-width: 65%;
  object-fit: contain;
  margin-left: -40px;
}
.navbar-blk-image- {
  height: 40px;
  min-width: 85px;
  max-width: 120px;
  margin-left: 48px;
}
.main-block-image {
  height: 45px;
  width: 100%;
  object-fit: contain;
}

.dot-options {
  color: var(--foreground);
}

.dark-theme .dot-options {
  color: var(--dark-text);
  font-weight: bold;
}

.dot-options {
  color: var(--foreground);
}

.dark-theme .dot-options {
  color: var(--dark-text);
  font-weight: bold;
}

.workspace-card {
  background-color: var(--secondary-color);
}

.dark-theme .workspace-card {
  background-color: var(--background);
}

.workspace-card:hover {
  background-color: var(--secondary-hover);
}

.dark-theme .workspace-card:hover {
  background-color: var(--background);
}

.workspace-options button:hover {
  background-color: var(--secondary-hover);
  color: var(--foreground);
}

.dark-theme .workspace-options button:hover {
  background-color: var(--secondary-hover);
  color: var(--foreground);
}

.workspace-sublist-btn-Actv {
  background-color: #eee;
  border-radius: 6px;
}
.dark-theme .workspace-sublist-btn-Actv {
  background-color: var(--background);
  border-radius: 6px;
}
.workspace-sublist-btn {
  border-radius: 6px;
  background-color: var(--secondary-hover);
  border-left: 4px solid var(--primary-color);
}
.dark-theme .workspace-sublist-btn {
  border-radius: 6px;
  background-color: var(--primary-hover);
  border-left: 4px solid var(--background);
}
.workspace-sublist-btn-:hover {
  border-radius: 6px;
  background-color: var(--secondary-hover);
  color: var(--primary-color);
  border-left: 4px solid var(--primary-color);
}
.dark-theme .workspace-sublist-btn-:hover {
  background-color: var(--primary-color);
  color: var(--dark-text);
}
/* END */

.chat-main-block {
  border-bottom: 11px solid #eee;
}
.dark-theme .chat-main-block {
  border-bottom: 11px solid var(--background);
}
/* END */

.info-alert {
  background-color: #e2f1fb;
  color: #2287c6;
  padding: 10px;
  border-radius: 6px;
}
.danger-alert {
  background-color: #fbe2e2;
  color: #c62222;
  padding: 10px;
  border-radius: 6px;
}

.uploading-files {
  background-color: transparent;
  color: white;
  overflow: hidden;
}
.dark-theme .uploading-files {
  background-color: transparent;
  overflow: hidden;
}

/* END */

.citaton-tab-Active {
  border-bottom: 2px solid var(--foreground);
}
.dark-theme .citaton-tab-Active {
  border-bottom: 2px solid var(--dark-text);
}
.citaton-tab-Active:hover {
  opacity: 0.8;
}
.citaton-tab-Inactive {
  border-bottom: 2px solid transparent;
}
.citaton-tab-Inactive:hover {
  opacity: 0.8;
  border-bottom: 2px solid var(--foreground);
}
/* END */

.truncate-file {
  padding: 2px 7px;
  border: 2px solid var(--primary-hover);
  color: var(--primary-hover);
  border-radius: 6px;
  overflow: hidden;
}
.truncate-file:hover {
  background-color: var(--secondary-hover);
}
.dark-theme .truncate-file {
  padding: 2px 7px;
  border: 2px solid var(--dark-text);
  color: var(--dark-text);
  border-radius: 6px;
  overflow: hidden;
}
.dark-theme .truncate-file:hover {
  background-color: var(--background);
}
/* END */

.file-list-header {
  background-color: var(--secondary-hover);
}
.dark-theme .file-list-header {
  background-color: var(--background);
}
/* END */

.edit-profile-img {
  background-color: transparent;
  border: 2px dashed var(--foreground);
}
.dark-theme .edit-profile-img {
  background-color: var(--background);
  border: 2px dashed var(--dark-text);
}
/* END */

.deep-xl-input {
  background-color: var(--elevated);
}
.dark-theme .deep-xl-input {
  background-color: var(--background);
}
.dark-theme .deep-xl-input:hover {
  opacity: 0.8;
  background-color: var(--background);
}
/* END */

.setting-title {
  background-color: var(--secondary-hover);
  border-radius: 4px;
  text-align: center;
}
.setting-title-inct:hover {
  background-color: #edf0ef;
  border-radius: 4px;
  text-align: center;
}
.dark-theme .setting-title-inct:hover {
  background-color: var(--primary-color);
  border-radius: 4px;
  text-align: center;
}
.dark-theme .setting-title {
  background-color: var(--secondary-hover);
}
.dark-theme .setting-title:hover {
  background-color: var(--primary-color);
}
/* END */

.upload-image-btn {
  background-color: #eee;
  border: 2px dashed var(--primary-color);
}
.dark-theme .upload-image-btn {
  background-color: var(--background);
  border: 2px dashed var(--dark-text);
}
.upload-image-btn:hover {
  opacity: 0.5;
}

.upload-img-border {
  border: 2px dashed var(--primary-color);
}
.dark-theme .upload-img-border {
  border: 2px dashed var(--dark-text);
}
/* END */

.shadow-bg:hover {
  background-color: var(--secondary-hover);
}
.dark-theme .shadow-bg:hover {
  background-color: var(--background);
}
.primary-bg {
  background-color: var(--primary-color);
  opacity: 1;
}
.primary-bg:hover {
  background-color: var(--primary-hover);
  opacity: 1;
}
.primary-bg:focus {
  outline: none;
}
/* END */

.dark-input-mdl {
  background-color: var(--elevated);
  border: 1px solid var(--border);
}

.dark-input-mdl:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.dark-input-mdl::placeholder {
  color: var(--foreground);
  opacity: 0.5;
}

/* END */

.text-area {
  background-color: #fafafa;
  border: 2px solid var(--primary-color);
}
.dark-theme .text-area {
  background-color: transparent;
  border: 2px solid var(--primary-color);
}
.text-area:focus {
  outline: none;
  border: 2px solid var(--foreground);
}
.dark-theme .text-area:focus {
  outline: none;
  border: 2px solid var(--secondary-hover);
}
/* END */

.table-tr-item {
  border-bottom: 1px solid #dfdddd;
}
.dark-theme .table-tr-item {
  border-bottom: 1px solid var(--primary-color);
}
.table-tr-item:hover {
  border-bottom: 1px solid var(--secondary-hover);
  background-color: var(--secondary-hover);
}
.dark-theme .table-tr-item:hover {
  border-bottom: none;
  background-color: var(--background);
}

.connectior-ptions:hover {
  background-color: var(--secondary-hover);
}
.dark-theme .connectior-ptions:hover {
  background-color: var(--background);
}
.connectior-ptions-Active {
  background-color: var(--secondary-hover);
}
.dark-theme .connectior-ptions-Active {
  background-color: var(--background);
  color: var(--dark-text);
}
/* END */

.modal-list-Active {
  background-color: var(--secondary-hover);
}
.modal-list-Active:hover {
  background-color: var(--secondary-hover);
}
.modal-list-Inactive:hover {
  background-color: var(--secondary-hover);
}
.dark-theme .modal-list-Active {
  background-color: var(--background);
}
.dark-theme .modal-list-Inactive:hover {
  background-color: var(--background);
}
/* END */

.light-promt-modal {
  background-color: var(--secondary-hover);
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
}
.dark-theme .light-promt-modal {
  background-color: var(--background);
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
}
/* END */

.tab-switch-Active {
  background-color: var(--primary-color);
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}
.tab-switch-Inactive {
  background-color: var(--secondary-hover);
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  color: var(--foreground);
}
.dark-theme .tab-switch-Inactive {
  background-color: var(--dark-text);
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  color: var(--foreground);
  opacity: 0.7;
}
.tab-switch-Inactive:hover {
  background-color: var(--primary-hover);
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  color: white;
  opacity: 1;
}
.icon:hover {
  opacity: 0.7;
}
.profile-icon {
  border: 2px solid var(--primary-color);
  padding: 3px;
  border-radius: 50px;
}
.dark-theme .profile-icon {
  border: 1.7px solid var(--dark-text);
  padding: 3px;
  border-radius: 50px;
}
.light-main-bg {
  background-color: white;
}
.modal-background {
  background-color: var(--primary-color);
}

.bg-main-gradient.green {
  background-image: linear-gradient(
    180deg,
    var(--foreground) 0%,
    var(--foreground) 100%
  );
}

.bg-sidebar {
  background-color: var(--backgrd-color);
}

.error-alert-box {
  padding: 8px 10px;
  border-left: 5px solid;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.simple-alert-box-error {
  border-radius: 0 6px 6px 0;
  background-color: #fdeae9;
  color: #df2825;
  border-left: 4px solid #df2825;
}

.box {
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

/* Animation */
.box {
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.error-appear {
  opacity: 1;
  animation:
    shake 0.5s ease,
    bounce-color 2s ease-in-out infinite;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-5px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(5px);
  }
}
/* End animation */

.bg-historical-msg-user {
  background-color: var(--backgrd-color);
}
.bg-historical-msg-system {
  background-color: var(--primary-color);
}
.down-block {
  background-color: var(--primary-color);
}
.input-container {
  border-radius: 10px;
  border: 2px solid var(--primary-color);
}
.dark-theme .input-container {
  border-radius: 10px;
  border: 2px solid var(--dark-text);
}
.new-workspace-btn {
  background-color: var(--primary-color);
  padding: 10px 20px;
  border-radius: 5px;
  border: none;
}
.new-workspace-btn:hover {
  opacity: 0.8;
}
.new-workspace-btn:focus {
  outline: none;
}

.citation-highlight {
  font-weight: 600;
  background-color: #e3af13;
}

.citation-highlight.score-0 {
  background-color: rgba(227, 175, 19, 0.1);
}
.citation-highlight.score-10 {
  background-color: rgba(227, 175, 19, 0.2);
}
.citation-highlight.score-20 {
  background-color: rgba(227, 175, 19, 0.3);
}
.citation-highlight.score-30 {
  background-color: rgba(227, 175, 19, 0.4);
}
.citation-highlight.score-40 {
  background-color: rgba(227, 175, 19, 0.5);
}
.citation-highlight.score-50 {
  background-color: rgba(227, 175, 19, 0.6);
}
.citation-highlight.score-60 {
  background-color: rgba(227, 175, 19, 0.7);
}
.citation-highlight.score-70 {
  background-color: rgba(227, 175, 19, 0.8);
}
.citation-highlight.score-80 {
  background-color: rgba(227, 175, 19, 0.9);
}
.citation-highlight.score-90 {
  background-color: rgba(227, 175, 19, 1);
}

/* Scroll bar */
/* width */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--secondary-hover);
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
  background: var(--primary-hover);
  width: 8px; /* Increased width on hover */
  height: 8px;
}

/* Responsive  */
.mob-avaible {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.mob-avaible img {
  width: 40%;
}
.navbar-blk-btn {
  font-size: 26px;
}
.navbar-blk-btn:hover {
  opacity: 0.7;
}
.dark-theme .navbar-blk-btn:hover {
  color: white;
  font-weight: bold;
}

/* Hide on desktops and larger screens */
@media (min-width: 1025px) {
  .mob-avaible {
    display: none;
  }
}

/* Ensure visibility on tablets and mobile devices */
@media (max-width: 768px) {
  .mob-avaible {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
  .right-block {
    border-radius: 0 0 100% 100%;
    width: 90%;
  }
}

/* Mobile Phones */
@media (min-width: 769px) {
  .mob-avaible {
    display: none;
  }
}
/* END */

@media only screen and (min-width: 200px) and (max-width: 380px) {
  .resp-messages-block {
    width: 101%;
  }
}

@media only screen and (max-width: 767px) {
  body {
    overflow-y: hidden;
  }

  .navbar-block .navbar-blk-image {
    margin-left: 45px;
    min-width: 80px;
  }
  .resp-top-block {
    overflow: hidden;
  }
  .resp-first-block-chat {
    overflow-x: hidden;
  }
  .mob-avaible- {
    display: none;
  }
}

.sidebar-block {
  background-color: var(--background);
}

/* Small Desktops */
@media only screen and (min-width: 1025px) and (max-width: 1366px) {
  .sidebar-block {
    min-width: 250px;
    padding: 15px;
  }
}

/* Large Screens */
@media only screen and (min-width: 1367px) {
  .sidebar-block {
    min-width: 300px;
    padding: 20px;
  }
}

/* Animation & spin */
.animate-remove {
  animation: fadeAndShrink 800ms forwards;
}

.spin {
  border-radius: 50%;
  border-width: 4px;
  border-style: solid;
  border-color: var(--primary-color) var(--secondary-color)
    var(--secondary-color) var(--secondary-color);
  animation: spin 1s linear infinite;
}
.spin.small {
  width: 1rem;
  height: 1rem;
}
.spin.large {
  width: 4rem;
  height: 4rem;
}
.dark-theme .spin {
  border-radius: 50%;
  border-width: 4px;
  border-style: solid;
  border-color: var(--primary-color) var(--secondary-color)
    var(--secondary-color) var(--secondary-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Math/Katex formatting to prevent duplication of content on screen */
.katex-html[aria-hidden="true"] {
  display: none;
}
.katex-mathml {
  font-size: 20px;
}

.has-selection {
  background-color: rgba(255, 255, 0, 0.3);
  transition: background-color 0.3s ease-in-out;
}

.dark .has-selection {
  background-color: rgba(255, 255, 0, 0.15);
}

/* Fix for file rows to ensure proper z-index stacking */
.file-row {
  position: relative;
}

.file-row .col-span-4 {
  z-index: 10;
  position: relative;
}

.file-row .checkbox-wrapper {
  display: flex;
  align-items: center;
  height: 16px;
}

/* Additional styles for checkbox focus and active states to ensure alignment */
.file-row .checkbox-wrapper input[type="checkbox"] {
  margin: 0;
  vertical-align: middle;
}

/* Folder name container */
.folder-name-container {
  position: relative;
  z-index: 5;
  min-height: 22px;
  display: flex;
  align-items: center;
  overflow: hidden;
  flex-shrink: 0;
}

.folder-name-container:hover {
  z-index: 31;
}
/* Contained animation for folder names */
@keyframes folder-text-scroll {
  0%,
  15% {
    transform: translateX(0%);
  }
  85%,
  100% {
    transform: translateX(-100%);
  }
}

/* Add custom keyframes for citation source marquee animation */
@keyframes citation-marquee {
  0%,
  5% {
    transform: translateX(0);
  }
  50%,
  55% {
    transform: translateX(calc(-100% + 70px));
  }
  95%,
  100% {
    transform: translateX(0);
  }
}

/* Current highlight for citations */
.current-highlight {
  background-color: rgba(255, 255, 100, 0.4) !important;
  box-shadow: 0 0 8px rgba(255, 200, 0, 0.5);
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

/* Citation source marquee */
.citation-marquee-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

/* Remove gradient effects */
.citation-marquee-container.overflow-needed::after {
  display: none;
}

.citation-marquee-container.overflow-needed:hover::after {
  display: none;
}

/* In active state, no gradient */
.bg-secondary .citation-marquee-container.overflow-needed::after {
  display: none;
}

.citation-marquee {
  display: inline-block;
  white-space: nowrap;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Apply marquee animation on hover when text overflows */
.citation-marquee-container.overflow-needed:hover .citation-marquee {
  animation: citation-marquee 8s linear infinite;
  text-overflow: clip;
  max-width: none; /* Allow text to extend beyond container during animation */
}

/* Current highlight for citations */

/* PDF Document Modal Styles */
.document-modal {
  max-width: 1500px !important;
  width: 90% !important;
}

.document-modal .rpv-core__viewer {
  width: 100% !important;
}

/* Ensure split view container keeps proper proportions */
.document-modal .flex-1 {
  flex: 1 1 auto;
}

/* Ensure sidebar has proper width and doesn't shrink too much */
.document-modal .w-80 {
  min-width: 220px;
  width: 280px !important;
}

@media (min-width: 768px) {
  .document-modal {
    max-width: 1500px !important;
  }
}

/* Stop pulsating when completed – main task (no subtasks) */
.progress__item--completed.progress__item:not(
    .progress__item--has-subtasks
  ):before {
  animation: none !important;
  opacity: 1;
  height: calc(100% - 26px); /* 26px is the height of the circular loader */
}

/* Stop pulsating when completed – task with subtasks */
.progress__item--completed.progress__item.progress__item--has-subtasks:before {
  animation: none !important;
  opacity: 1;
  height: calc(100% - 26px); /* 26px is the height of the circular loader */
}

/* ==============================================
   Mobile Modal Fixes for iPhone
============================================== */

/* Fix for iPhone viewport height issues */
@supports (-webkit-touch-callout: none) {
  /* iOS Safari specific styles */
  .modal-container {
    height: -webkit-fill-available;
  }
}

/* WebKit overflow scrolling fix */
.-webkit-overflow-scrolling-touch {
  -webkit-overflow-scrolling: touch !important;
}

/* Ensure modal content is properly sized on mobile */
@media (max-width: 768px) {
  .modal-content {
    max-height: calc(100vh - 2rem) !important;
    max-height: calc(
      100dvh - 2rem
    ) !important; /* Use dynamic viewport height when available */
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  /* Ensure the scrollable area works properly */
  .modal-content > div:nth-child(2) {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    overflow-x: visible;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Ensure footer buttons are always visible on mobile */
  .modal-footer {
    position: sticky;
    bottom: 0;
    background: var(--background);
    z-index: 10;
    border-top: 1px solid var(--border);
    margin-top: auto;
    flex-shrink: 0;
  }

  /* Specific fixes for legal template modal on mobile */
  .modal-content form {
    min-height: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  /* Ensure form scrollable area */
  .modal-content form > div:first-child {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Force scrolling on the main content area */
  .modal-content .overflow-y-auto {
    overflow-y: scroll !important;
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain;
    height: 100%;
  }

  /* Fix for dropdown/select elements on mobile */
  [data-radix-select-content] {
    z-index: 9999 !important;
    position: fixed !important;
  }

  /* Ensure proper spacing for form sections */
  .modal-content .space-y-4 > * {
    margin-bottom: 1rem;
  }

  /* Fix upload button positioning on mobile */
  .modal-content .flex-shrink-0 {
    flex-shrink: 0 !important;
  }

  /* Adjust modal padding on very small screens */
  @media (max-width: 480px) {
    .modal-content {
      max-height: calc(100vh - 1rem) !important;
      max-height: calc(100dvh - 1rem) !important;
      padding: 1rem !important;
    }

    /* Reduce padding for form elements on very small screens */
    .modal-content .px-6 {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    /* Stack upload button below label on very small screens */
    .modal-content .flex.items-center.justify-between {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }

  /* Fix for iPhone Safari address bar height changes */
  @supports (-webkit-touch-callout: none) {
    .modal-content {
      max-height: calc(100vh - 2rem) !important;
      max-height: calc(100dvh - 2rem) !important;
    }
  }

  /* Ensure touch scrolling works properly */
  .modal-content * {
    -webkit-overflow-scrolling: touch;
  }
}
