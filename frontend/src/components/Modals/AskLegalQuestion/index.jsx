import React, { useState, useEffect, useRef, useCallback } from "react";
import { useTranslation } from "react-i18next";
import Workspace from "@/models/workspace";
import System from "@/models/system";
import { useParams } from "react-router-dom";
import { AiOutlineLoading } from "react-icons/ai";
import { MdArrowForwardIos } from "react-icons/md";
import { TbArrowNarrowLeft } from "react-icons/tb";
import { IoWarningOutline } from "react-icons/io5";
import { FiSettings } from "react-icons/fi";
import { Button } from "@/components/Button";
import Modal from "@/components/ui/Modal";
import useUser from "@/hooks/useUser";
import LegalTasksSettingsModal from "@/components/Modals/LegalTasksSettingsModal";
import useThreadProgress from "@/hooks/useThreadProgress";
import useProgressStore from "@/stores/progressStore";
// Warning modal component for when no JSON files are found
const NoFilesWarningModal = ({ onClose }) => {
  const { t } = useTranslation();
  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={t("performLegalTask.warning-title")}
      className="w-[400px]"
    >
      <div className="flex flex-col items-center justify-center p-4 text-center">
        <IoWarningOutline className="text-yellow-500 text-5xl mb-4" />
        <h4 className="text-lg font-medium text-foreground mb-2">
          {t("performLegalTask.no-files-title")}
        </h4>
        <p className="text-foreground text-opacity-80 mb-4">
          {t("performLegalTask.no-files-description")}
        </p>
        <Button onClick={onClose}>{t("common.ok")}</Button>
      </div>
    </Modal>
  );
};

// Helper function to extract all file names from the workspace tree
const extractFileNames = (items) => {
  let files = [];
  const recurse = (currentItems) => {
    for (const item of currentItems) {
      // CDB processes .json files from the root or nested, ensure we get them
      if (item.type === "file" && item.name.endsWith(".json")) {
        files.push(item.name);
      } else if (item.type === "folder" && item.items) {
        recurse(item.items);
      }
    }
  };
  if (items) recurse(items);
  return files;
};

export default function PerformLegalTaskModal({
  sendCommand,
  onClose,
  onProcessingChange,
  threadSlug = "",
}) {
  const { t } = useTranslation();
  const { slug } = useParams();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [, setIsLoading] = useState(false);
  const [isSubtaskSelected, setIsSubtaskSelected] = useState(false);
  const [workspace, setWorkspace] = useState(null);
  const [legalTasks, setLegalTasks] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [subCategories, setSubCategories] = useState([]);
  const [loadingSubCategories, setLoadingSubCategories] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [customInstructions, setCustomInstructions] = useState("");
  const [selectedMainDocName, setSelectedMainDocName] = useState("");
  const [selectedReferenceFiles, setSelectedReferenceFiles] = useState([]);
  const [workspaceFiles, setWorkspaceFiles] = useState([]);
  const [generatedFiles, setGeneratedFiles] = useState([]);
  const [showNoFilesWarning, setShowNoFilesWarning] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const abortController = useRef(new AbortController());

  // Permission check for settings access
  const hasAdminAccess = ["admin", "manager", "superuser"].includes(
    user?.role || ""
  );

  const safeThreadSlug = threadSlug || "";
  const progress = useThreadProgress(safeThreadSlug);

  // Function to clean file names by removing UUIDs and file extensions
  const cleanFileName = useCallback((filename) => {
    if (!filename) return "";

    try {
      // Make sure filename is a string
      const filenameStr = String(filename);

      // First, handle JSON files specifically (from server)
      if (filenameStr.endsWith(".json")) {
        // Remove .json extension
        let cleanName = filenameStr.replace(/\.json$/, "");

        // Remove UUID pattern (common in JSON files)
        return cleanName;
      }

      // For other files, remove any file extension
      let cleanName = filenameStr.replace(/\.[^/.]+$/, "");

      // Remove UUID pattern (8-4-4-4-12 format or similar)
      // This regex looks for patterns like -a1b2c3d4 or -a1b2-c3d4-etc
      cleanName = cleanName.replace(
        /-[0-9a-f]{4,}(?:-[0-9a-f]{4,}){0,4}$/i,
        ""
      );

      // Also remove any standalone UUIDs that might be at the end
      cleanName = cleanName.replace(
        /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
        ""
      );

      return cleanName;
    } catch (error) {
      console.error("Error cleaning filename:", error, filename);
      return String(filename || "");
    }
  }, []);

  useEffect(() => {
    async function fetchFilesForSelection() {
      if (workspace?.id) {
        try {
          const localFilesData = await System.localFiles(workspace.id);
          if (localFilesData && localFilesData.items) {
            const extracted = extractFileNames(localFilesData.items);
            // Store names without .json for display, will add back when sending
            setWorkspaceFiles(
              extracted.map((name) => name.replace(".json", ""))
            );
          } else {
            setWorkspaceFiles([]);
          }
        } catch (error) {
          console.error(
            "Error fetching workspace files for main doc selection:",
            error
          );
          setWorkspaceFiles([]);
        }
      }
    }
    fetchFilesForSelection();
  }, [workspace]);

  const handleFetchSubCategories = async (name) => {
    setLoadingSubCategories(true);

    try {
      const response = await System.fetchSubCategories(name);

      if (response.success && response.data) {
        setSubCategories(response.data);
        setSelectedCategory({ name, subCategories: response.data });
      } else {
        console.error(
          "Error fetching sub-categories:",
          response?.error || "No data returned."
        );
        setSubCategories([]);
        setSelectedCategory({ name, subCategories: [] });
      }
    } catch (error) {
      console.error("Error in handleFetchSubCategories:", error);
      setSubCategories([]);
      setSelectedCategory({ name, subCategories: [] });
    } finally {
      setLoadingSubCategories(false);
    }
  };

  const handleCategorySelect = async (category) => {
    await handleFetchSubCategories(category.name);
  };

  const handleTaskSelect = (subCategory) => {
    setSelectedTask(subCategory);
    setSelectedMainDocName("");
    setSelectedReferenceFiles([]);
  };

  const checkWorkspaceFiles = async (workspaceId) => {
    try {
      const localFiles = await System.localFiles(workspaceId);

      // Check if there are any JSON files in the workspace
      let hasFiles = false;

      if (localFiles && localFiles.items) {
        // Recursively check for files in all folders
        const checkFolderForFiles = (folder) => {
          if (folder.type === "file") {
            return true;
          }

          if (folder.items && folder.items.length > 0) {
            return folder.items.some((item) => {
              if (item.type === "file") {
                return true;
              }
              return (
                item.items && item.items.length > 0 && checkFolderForFiles(item)
              );
            });
          }

          return false;
        };

        hasFiles = localFiles.items.some((item) => checkFolderForFiles(item));
      }

      return hasFiles;
    } catch (error) {
      console.error("Error checking workspace files:", error);
      return false;
    }
  };

  const handleConfirmTask = async () => {
    if (!selectedTask) return;

    if (selectedTask.legalTaskType === "mainDoc" && !selectedMainDocName) {
      return;
    }

    if (
      selectedTask.legalTaskType === "referenceFiles" &&
      selectedReferenceFiles.length === 0
    ) {
      return;
    }

    const hasFiles = await checkWorkspaceFiles(workspace.id);
    if (!hasFiles) {
      setShowNoFilesWarning(true);
      return;
    }

    if (progress.isActive) {
      return;
    }

    const cdbOptions = [
      selectedTask?.legalPrompt || null,
      customInstructions || null,
      selectedTask.legalTaskType === "mainDoc"
        ? `${selectedMainDocName}.json`
        : null,
      selectedTask?.legalTaskType || null,
      selectedTask.legalTaskType === "referenceFiles"
        ? selectedReferenceFiles.map((f) => `${f}.json`)
        : null,
    ];

    let determinedFlowType;
    if (selectedTask.legalTaskType === "mainDoc") {
      determinedFlowType = "main";
    } else if (selectedTask.legalTaskType === "noMainDoc") {
      determinedFlowType = "noMain";
    } else if (selectedTask.legalTaskType === "referenceFiles") {
      determinedFlowType = "referenceFiles";
    } else {
      determinedFlowType = selectedTask.requiresMainDocument
        ? "main"
        : "noMain";
    }

    try {
      if (safeThreadSlug) {
        const totalSteps = determinedFlowType === "referenceFiles" ? 5 : 7;

        progress.start(totalSteps, determinedFlowType);
      }

      // Close the modal immediately when process starts
      onClose();

      // Get the AbortController from the progress store (created when progress.start() was called)
      const progressAbortController = useProgressStore
        .getState()
        .getAbortController(safeThreadSlug);

      // Use the progress store's abort controller, or fallback to local one
      const activeAbortController =
        progressAbortController || abortController.current;

      let chatId = null;

      await sendCommand(selectedTask?.name, true, [], [], {
        preventNewChat: true,
        preventChatCreation: false,
        cdb: true,
        cdbOptions: cdbOptions,
        abortController: activeAbortController,
        chatHandler: (chatResult) => {
          if (chatResult.type === "cdbProgress" && safeThreadSlug) {
            progress.update(chatResult);

            if (chatResult.label) {
              setGeneratedFiles((prev) =>
                prev.includes(chatResult.label)
                  ? prev
                  : [...prev, chatResult.label]
              );
            }
          }
          if (
            chatResult.type === "finalizeResponseStream" &&
            chatResult.chatId &&
            safeThreadSlug
          ) {
            chatId = chatResult.chatId;
            progress.finish();
          }
          if (chatResult.type === "stopGeneration") {
            handleAbortTask(false);
          }
          if (
            chatResult.type === "abort" &&
            chatResult.error &&
            safeThreadSlug
          ) {
            progress.setError(chatResult.error);
          }
        },
        displayMessage: selectedTask?.name,
      });

      if (generatedFiles.length > 0) {
        try {
          await System.deleteDocuments(generatedFiles);
          setGeneratedFiles([]);
        } catch (error) {
          console.error("Error cleaning generated files:", error);
        }
      }

      // Close the modal and trigger a refresh of the chat history
      setTimeout(function () {
        window.dispatchEvent(
          new CustomEvent("CDB_PROCESS_COMPLETE", {
            detail: { chatId: chatId },
          })
        );
      }, 1000);
    } catch (error) {
      console.error("Error during API call:", error);

      // Reset progress state to clear UI indicators
      if (safeThreadSlug) {
        progress.cancel();
      }
    }
  };

  const handleProcessComplete = () => {
    setIsSubtaskSelected(true);
    setIsLoading(true);
  };

  const handleOpenSettings = () => {
    // Open the legal tasks settings modal
    setIsSettingsModalOpen(true);
  };

  const fetchGroupedLegalTasks = async () => {
    setLoading(true);

    const response = await System.fetchGroupedLegalTasks();
    if (response.success) {
      setLegalTasks(response.data);
    } else {
      console.error("Error fetching grouped legal tasks:", response.error);
    }

    setLoading(false);
  };

  useEffect(() => {
    fetchGroupedLegalTasks();
  }, []);

  useEffect(() => {
    async function getWorkspace() {
      if (!slug) return;
      setLoading(true);

      try {
        const _workspace = await Workspace.bySlug(slug);
        if (!_workspace) {
          setLoading(false);
          return;
        }

        setWorkspace(_workspace);

        await checkWorkspaceFiles(_workspace.id);
      } catch (error) {
        console.error("Error fetching workspace:", error);
      } finally {
        setLoading(false);
      }
    }

    getWorkspace();
  }, [slug]);

  const handleAbortTask = async (shouldCloseModal = true) => {
    // Cancel the progress first (this will call the progress store's cancel method)
    if (safeThreadSlug) {
      progress.cancel();
    }

    // Also abort the local controller as a fallback
    abortController.current.abort();
    // ensure next run gets a fresh controller
    abortController.current = new AbortController();

    try {
      await System.purgeDocumentBuilder();
    } catch (err) {
      console.error("Failed to purge CDB files on abort", err);
    }

    if (generatedFiles.length > 0) {
      try {
        await System.deleteDocuments(generatedFiles);
        setGeneratedFiles([]);
      } catch (error) {
        console.error("Error deleting generated files:", error);
      }
    }

    if (onProcessingChange) onProcessingChange(false);

    if (shouldCloseModal) {
      onClose();
    }
  };

  const handlePromptGenerated = (generatedPrompt) => {
    setCustomInstructions(generatedPrompt);
  };

  if (showNoFilesWarning) {
    return <NoFilesWarningModal onClose={() => setShowNoFilesWarning(false)} />;
  }

  return (
    <div className="w-full flex flex-col">
      <div className="flex-1 overflow-y-auto">
        <p className="text-foreground text-opacity-60 mb-6 break-words">
          {t("performLegalTask.duration-info")}
        </p>

        {loading ? (
          <div className="flex items-center justify-center p-4">
            <AiOutlineLoading className="animate-spin text-blue-500 text-lg" />
            <span className="ml-2">
              {t("performLegalTask.loading-subcategory")}
            </span>
          </div>
        ) : selectedCategory ? (
          <div className="flex flex-col">
            <div className="flex items-center mb-4">
              <button
                onClick={() => setSelectedCategory(null)}
                className="flex items-center text-foreground hover:text-opacity-80"
              >
                <TbArrowNarrowLeft className="mr-2" />
                {t("performLegalTask.select-category")}
              </button>
            </div>
            {loadingSubCategories ? (
              <div className="flex items-center justify-center p-4">
                <AiOutlineLoading className="animate-spin text-blue-500 text-lg" />
                <span className="ml-2">
                  {t("performLegalTask.loading-subcategory")}
                </span>
              </div>
            ) : subCategories.length === 0 ? (
              <div className="text-center p-4">
                {t("performLegalTask.noSubtskfund")}
              </div>
            ) : (
              <>
                <h3 className="text-lg font-medium text-foreground mb-4">
                  {t("performLegalTask.choose-task")}
                </h3>
                <div className="space-y-4">
                  {subCategories.map((subCategory) => (
                    <div key={subCategory.name} className="relative">
                      <div className="relative">
                        {/* Use a div instead of a button when the task is selected */}
                        {selectedTask?.name === subCategory.name ? (
                          <div className="flex items-center justify-between w-full p-3 text-left rounded-lg transition-colors duration-200 bg-secondary border-2 border-primary">
                            <div>
                              <span className="text-foreground">
                                {subCategory.name}
                              </span>
                              {subCategory.description && (
                                <p className="text-sm text-foreground text-opacity-60 mt-1">
                                  {subCategory.description}
                                </p>
                              )}
                            </div>
                          </div>
                        ) : (
                          <button
                            onClick={() => handleTaskSelect(subCategory)}
                            className="flex items-center justify-between w-full p-3 text-left rounded-lg transition-colors duration-200 border bg-secondary hover:bg-secondary-hover"
                          >
                            <div>
                              <span className="text-foreground">
                                {subCategory.name}
                              </span>
                              {subCategory.description && (
                                <p className="text-sm text-foreground text-opacity-60 mt-1">
                                  {subCategory.description}
                                </p>
                              )}
                            </div>
                            <MdArrowForwardIos className="text-foreground text-opacity-60" />
                          </button>
                        )}
                      </div>

                      {selectedTask?.name === subCategory.name && (
                        <div className="mt-2 p-4 bg-background rounded-lg border border-gray-200">
                          <div className="mb-2">
                            <label className="block text-sm font-medium text-foreground">
                              {t("performLegalTask.custom-instructions-label")}
                            </label>
                          </div>
                          <textarea
                            value={customInstructions}
                            onChange={(e) =>
                              setCustomInstructions(e.target.value)
                            }
                            placeholder={t(
                              "performLegalTask.custom-instructions-placeholder"
                            )}
                            className="w-full p-2 text-sm border rounded-md bg-background text-foreground placeholder-foreground-muted"
                            rows="9"
                          />
                          {selectedTask?.legalTaskType === "mainDoc" &&
                            workspaceFiles.length > 0 && (
                              <div className="mt-4">
                                <label
                                  htmlFor="main-document-select"
                                  className="block text-sm font-medium text-foreground mb-1"
                                >
                                  {t(
                                    "performLegalTask.select-main-document-label"
                                  )}
                                </label>
                                <select
                                  id="main-document-select"
                                  value={selectedMainDocName}
                                  onChange={(e) =>
                                    setSelectedMainDocName(e.target.value)
                                  }
                                  className="w-full p-2 text-sm border rounded-md bg-background text-foreground"
                                  required
                                >
                                  <option value="" disabled>
                                    {t(
                                      "performLegalTask.select-document-placeholder"
                                    )}
                                  </option>
                                  {workspaceFiles.map((fileName) => (
                                    <option key={fileName} value={fileName}>
                                      {cleanFileName(fileName)}
                                    </option>
                                  ))}
                                </select>
                              </div>
                            )}
                          {selectedTask?.legalTaskType === "referenceFiles" &&
                            workspaceFiles.length > 0 && (
                              <div className="mt-4">
                                <label
                                  htmlFor="reference-files-select"
                                  className="block text-sm font-medium text-foreground mb-1"
                                >
                                  {t(
                                    "performLegalTask.selectReferenceFilesLabel"
                                  )}
                                </label>
                                <select
                                  multiple
                                  id="reference-files-select"
                                  value={selectedReferenceFiles}
                                  onChange={(e) =>
                                    setSelectedReferenceFiles(
                                      Array.from(
                                        e.target.selectedOptions,
                                        (opt) => opt.value
                                      )
                                    )
                                  }
                                  className="w-full p-2 text-sm border rounded-md bg-background text-foreground"
                                >
                                  {workspaceFiles.map((fileName) => (
                                    <option key={fileName} value={fileName}>
                                      {cleanFileName(fileName)}
                                    </option>
                                  ))}
                                </select>
                              </div>
                            )}
                          <div className="mt-4 flex justify-end space-x-3">
                            <Button
                              onClick={() => {
                                setSelectedTask(null);
                                setCustomInstructions("");
                                setSelectedMainDocName("");
                                setSelectedReferenceFiles([]);
                              }}
                              variant="secondary"
                            >
                              {t("common.cancel")}
                            </Button>
                            <Button
                              onClick={handleConfirmTask}
                              disabled={
                                (selectedTask?.legalTaskType === "mainDoc" &&
                                  !selectedMainDocName) ||
                                (selectedTask?.legalTaskType ===
                                  "referenceFiles" &&
                                  selectedReferenceFiles.length === 0)
                              }
                            >
                              {t("common.confirmstart")}
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        ) : (
          <>
            <h3 className="text-lg font-medium text-foreground mb-4">
              {t("performLegalTask.select-category")}
            </h3>
            <div className="grid grid-cols-1 gap-4 pb-4 text-foreground">
              {legalTasks.length === 0 ? (
                <div className="text-center p-4">
                  {t("performLegalTask.noTaskfund")}
                </div>
              ) : (
                legalTasks.map((category, index) => (
                  <button
                    key={index}
                    className="text-left p-4 rounded-lg border transition-colors relative bg-secondary hover:bg-secondary-hover"
                    onClick={() => handleCategorySelect(category)}
                  >
                    <h4 className="font-medium text-foreground">
                      {category.name}
                    </h4>
                    <MdArrowForwardIos className="absolute right-4 top-1/2 transform -translate-y-1/2" />
                  </button>
                ))
              )}
            </div>
          </>
        )}
      </div>

      {/* Settings button for admin, manager, and superuser roles */}
      {hasAdminAccess && (
        <div className="mt-6 border-t border-gray-200 pt-4 flex justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={handleOpenSettings}
            className="flex items-center gap-2"
          >
            <FiSettings className="h-4 w-4" />
            {t("performLegalTask.settings-button")}
          </Button>
        </div>
      )}

      {/* Legal Tasks Settings Modal */}
      <LegalTasksSettingsModal
        isOpen={isSettingsModalOpen}
        onClose={() => {
          setIsSettingsModalOpen(false);
          fetchGroupedLegalTasks();

          if (selectedCategory?.name) {
            handleFetchSubCategories(selectedCategory.name);
            setSelectedTask(null);
          }
        }}
      />
    </div>
  );
}
