import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";
import TabButton from "@/components/ui/TabButton";
import {
  Info,
  Warning,
  CheckCircle,
  Clock,
  CircleNotch,
  Newspaper,
} from "@phosphor-icons/react";
import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";
import showToast from "@/utils/toast";
import NewsModal from "@/components/NewsModal";

const priorityIcons = {
  low: Info,
  medium: Info,
  high: Warning,
  urgent: Warning,
};

const priorityColors = {
  low: "text-green-500",
  medium: "text-yellow-500",
  high: "text-orange-500",
  urgent: "text-red-500",
};

export default function NewsListModal({
  isOpen,
  onClose,
  news = [],
  onNewsUpdate,
}) {
  const { t } = useTranslation();
  const [processingIds, setProcessingIds] = useState(new Set());
  const [filter, setFilter] = useState("all"); // all, read, unread
  const [selectedNewsItem, setSelectedNewsItem] = useState(null);
  const [showNewsDetailModal, setShowNewsDetailModal] = useState(false);

  const filteredNews = news.filter((item) => {
    if (filter === "read") return item.isDismissed;
    if (filter === "unread") return !item.isDismissed;
    return true;
  });

  const handleDismissNews = async (newsItem, event) => {
    // Stop event propagation to prevent opening the news detail modal
    event.stopPropagation();

    setProcessingIds((prev) => new Set(prev).add(newsItem.id));

    try {
      const endpoint = newsItem.isSystemNews
        ? `${API_BASE}/news/system/${newsItem.id}/dismiss`
        : `${API_BASE}/news/${newsItem.id}/dismiss`;

      const response = await fetch(endpoint, {
        method: "POST",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error(`Failed to dismiss news: ${response.status}`);
      }

      showToast(t("news.list.dismissSuccess"), "success");

      // Refresh the news list
      if (onNewsUpdate) {
        onNewsUpdate();
      }
    } catch (error) {
      console.error("Failed to dismiss news:", error);
      showToast(t("news.list.dismissError"), "error");
    } finally {
      setProcessingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(newsItem.id);
        return newSet;
      });
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleViewNewsItem = (newsItem) => {
    setSelectedNewsItem(newsItem);
    setShowNewsDetailModal(true);
  };

  const handleCloseNewsDetailModal = () => {
    setShowNewsDetailModal(false);
    setSelectedNewsItem(null);
  };

  const handleNewsDetailUpdate = () => {
    // Refresh the news list when news is updated from the detail modal
    if (onNewsUpdate) {
      onNewsUpdate();
    }
    // Close the detail modal
    handleCloseNewsDetailModal();
  };

  const NewsItem = ({ item }) => {
    const PriorityIcon = priorityIcons[item.priority] || Info;
    const priorityColor = priorityColors[item.priority] || "text-blue-500";
    const isProcessing = processingIds.has(item.id);

    const handleKeyDown = (event) => {
      if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        handleViewNewsItem(item);
      }
    };

    return (
      <button
        className="w-full text-left border rounded-lg p-4 space-y-3 bg-card border-border cursor-pointer transition-all duration-200 hover:bg-accent/50 hover:border-primary hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
        onClick={() => handleViewNewsItem(item)}
        onKeyDown={handleKeyDown}
        aria-label={item.title}
        role="button"
        tabIndex={0}
      >
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <PriorityIcon
              className={`w-5 h-5 ${priorityColor} mt-0.5 flex-shrink-0`}
            />
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-foreground text-sm line-clamp-2">
                  {item.title}
                </h3>
              </div>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                    item.priority === "urgent"
                      ? "bg-red-100 text-red-800"
                      : item.priority === "high"
                        ? "bg-orange-100 text-orange-800"
                        : item.priority === "medium"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-green-100 text-green-800"
                  }`}
                >
                  {t(`news.priority.${item.priority}`)}
                </span>
                {item.isSystemNews && (
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    {t("news.list.systemNews")}
                  </span>
                )}
                <Clock className="w-3 h-3" />
                <span>{formatDate(item.createdAt)}</span>
              </div>
            </div>
          </div>

          {/* Action Button */}
          <div className="flex-shrink-0 ml-3">
            {item.isDismissed ? (
              <span className="text-xs text-muted-foreground px-2 py-1">
                {t("news.list.dismissed")}
              </span>
            ) : (
              <Button
                onClick={(event) => handleDismissNews(item, event)}
                disabled={isProcessing}
                variant="ghost"
                size="sm"
                className="text-xs"
              >
                {isProcessing ? (
                  <CircleNotch className="w-3 h-3 animate-spin" />
                ) : null}
                {t("news.list.dismiss")}
              </Button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="text-sm text-foreground/80 line-clamp-3">
          {item.content}
        </div>

        {/* Dismissal info */}
        {item.isDismissed && item.dismissedAt && (
          <div className="text-xs text-muted-foreground flex items-center gap-1">
            <CheckCircle className="w-3 h-3" />
            {t("news.list.dismissedAt", { date: formatDate(item.dismissedAt) })}
          </div>
        )}
      </button>
    );
  };

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        title={
          <div className="flex items-center gap-2">
            <Newspaper className="w-5 h-5 text-blue-500" />
            <span>{t("news.list.title")}</span>
            {news.length > 0 && (
              <span className="text-sm text-muted-foreground">
                ({news.length})
              </span>
            )}
          </div>
        }
        className="max-w-6xl max-h-[90vh]"
      >
        <div className="space-y-4">
          {/* Filter Tabs */}
          <div className="flex gap-2 border-b border-border">
            <TabButton
              active={filter === "all"}
              onClick={() => setFilter("all")}
            >
              {t("news.list.filter.all")} ({news.length})
            </TabButton>
            <TabButton
              active={filter === "unread"}
              onClick={() => setFilter("unread")}
            >
              {t("news.list.filter.unread")} (
              {news.filter((n) => !n.isDismissed).length})
            </TabButton>
            <TabButton
              active={filter === "read"}
              onClick={() => setFilter("read")}
            >
              {t("news.list.filter.read")} (
              {news.filter((n) => n.isDismissed).length})
            </TabButton>
          </div>

          {/* News List */}
          <div className="max-h-[60vh] overflow-y-auto space-y-3">
            {filteredNews.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Newspaper className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>{t("news.list.empty")}</p>
              </div>
            ) : (
              filteredNews.map((item) => (
                <NewsItem
                  key={`${item.isSystemNews ? "system" : "local"}-${item.id}`}
                  item={item}
                />
              ))
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-end pt-4 border-t border-border">
            <Button onClick={onClose} variant="secondary">
              {t("common.close")}
            </Button>
          </div>
        </div>
      </Modal>

      {/* News Detail Modal */}
      {selectedNewsItem && (
        <NewsModal
          isOpen={showNewsDetailModal}
          onClose={handleCloseNewsDetailModal}
          news={[selectedNewsItem]}
          onNewsUpdate={handleNewsDetailUpdate}
        />
      )}
    </>
  );
}
