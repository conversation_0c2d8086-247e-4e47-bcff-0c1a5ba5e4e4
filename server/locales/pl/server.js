const translations = {
  common: {
    thinking: "Myślę...",
  },
  errors: {
    ai: {
      ollama: {
        "connection-failed":
          "Nie można połączyć się z instancją Ollama lub nie odpowiada. Upewnij się, że serwer API jest uruchomiony i informacje o połączeniu są poprawne.",
        "empty-response": "Odpowiedź tekstowa Ollama była pusta.",
        "stream-failed": "Nie można przesłać czatu. {{message}}",
        "no-endpoint": "Nie ustawiono punktu końcowego API Ollama.",
        "no-token-limit": "Nie ustawiono limitu kontekstu tokenów Ollama.",
        "provider-not-responding":
          "Lokalny dostawca AI nie odpowiada, albo z powodu wyłączenia serwera, albo jego przeciążenia.",
        "provider-unavailable":
          "Lokalny serwer AI jest obecnie niedostępny. Sprawdź instalację i upewnij się, że działa i nie jest przeciążony.",
      },
      azure: {
        "no-endpoint": "Nie ustawiono punktu końcowego API Azure.",
        "no-key": "Nie ustawiono klucza API Azure.",
        "no-model": "Nie ustawiono modelu Azure OpenAI.",
        "no-model-pref":
          "Nie zdefiniowano ENV OPEN_MODEL_PREF. Musi to być nazwa wdrożenia na koncie Azure dla modelu czatu LLM, takiego jak GPT-3.5.",
      },
      openai: {
        "no-key": "Nie ustawiono klucza API OpenAI.",
      },
      mistral: {
        "no-key": "Nie ustawiono klucza API Mistral.",
      },
      litellm: {
        "no-base-path":
          "LiteLLM musi mieć prawidłową ścieżkę bazową do korzystania z API.",
        "no-model": "LiteLLM musi mieć ustawiony prawidłowy model.",
        "no-token-limit": "Nie ustawiono limitu kontekstu tokenów.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI musi mieć prawidłową ścieżkę bazową do korzystania z API.",
        "no-token-limit": "Nie ustawiono limitu kontekstu tokenów.",
      },
      localai: {
        "no-token-limit": "Nie ustawiono limitu kontekstu tokenów LocalAi.",
      },
      lmstudio: {
        "no-token-limit": "Nie ustawiono limitu kontekstu tokenów LMStudio.",
      },
      fireworks: {
        "invalid-model":
          "FireworksAI czat: {{model}} nie jest ważny dla uzupełnienia czatu!",
      },
      context: {
        header: "Źródło(a)",
      },
    },
    system: {
      "ssl-boot-failed":
        "Uruchomienie SSL nie powiodło się: {{message}} - przełączanie na uruchomienie HTTP.",
      "no-app": 'Nie zdefiniowano "app" - awaria!',
      validation: {
        "invalid-transcription-provider":
          "{{input}} nie jest prawidłowym dostawcą modelu transkrypcji.",
        "invalid-safety-setting":
          "Nieprawidłowe ustawienie bezpieczeństwa. Musi być jednym z {{modes}}.",
        "invalid-model-type":
          "Nieprawidłowy typ modelu. Musi być jednym z {{models}}.",
        "invalid-embedding-model":
          "Nieprawidłowy typ modelu osadzania. Musi być jednym z {{supported}}.",
      },
    },
    document: {
      "sync-failed":
        "Nie udało się zsynchronizować dokumentu {{filename}} po {{attempts}} próbach.",
      "invalid-metadata":
        "Dokument {{filename}} ma nieprawidłowe lub brakujące metadane.",
      removed:
        "Dokument {{filename}} został usunięty z zestawu monitorowanych dokumentów.",
    },
    validation: {
      "value-not-number": "Wartość nie jest liczbą.",
      "value-must-be-nonzero": "Wartość musi być różna od zera.",
      "value-cannot-be-negative": "Wartość nie może być mniejsza niż 0.",
      "invalid-serp-provider": "Nieprawidłowy dostawca SERP.",
      "max-rerank-limit-min":
        "Maksymalny limit ponownego rankingu musi wynosić co najmniej 10.",
      "max-rerank-limit-max":
        "Maksymalny limit ponownego rankingu nie może przekraczać 200.",
    },
    embedding: {
      "lmstudio-failed": "Osadzanie LMStudio nie powiodło się: {{errors}}",
      "jina-failed": "Osadzanie Jina nie powiodło się: {{errors}}",
    },
    env: {
      "jina-api-key-error": "Klucz API Jina musi zaczynać się od jina_",
      "empty-value": "Wartość nie może być pusta",
      "non-zero-required": "Wartość musi być większa od zera",
      "must-be-number": "Wartość musi być liczbą",
      "invalid-url": "URL nie jest prawidłowym adresem URL",
      "openai-key-format": "Klucz OpenAI musi zaczynać się od sk-",
      "contextual-prompt-format":
        "Kontekstowe zapytanie użytkownika musi zawierać {file} i {chunk}",
      "anthropic-key-format": "Klucz Anthropic musi zaczynać się od sk-ant-",
      "external-llm-url-format": "URL musi zawierać /v1",
      "url-no-trailing-slash": "URL nie może kończyć się ukośnikiem",
      "invalid-tts-provider": "{{input}} nie jest prawidłowym dostawcą TTS",
      "invalid-whisper-model":
        "{{input}} nie jest prawidłowym wyborem modelu Whisper",
      "invalid-llm-provider": "{{input}} nie jest prawidłowym dostawcą LLM",
    },
    // Workspace sharing errors
    workspace: {
      onlyOwnerShare:
        "Tylko właściciele obszarów roboczych mogą udostępniać obszary robocze",
      onlyOwnerRevoke:
        "Tylko właściciele obszarów roboczych mogą cofnąć udostępnianie obszarów roboczych",
      shareFailed: "Nie udało się udostępnić obszaru roboczego",
      revokeFailed: "Nie udało się cofnąć udostępniania obszaru roboczego",
      getStatusFailed:
        "Nie udało się pobrać statusu udostępniania obszaru roboczego",
    },
    // Thread sharing errors
    thread: {
      onlyOwnerShare: "Tylko właściciele wątków mogą udostępniać wątki",
      onlyOwnerRevoke:
        "Tylko właściciele wątków mogą cofnąć udostępnianie wątków",
      shareFailed: "Nie udało się udostępnić wątku",
      revokeFailed: "Nie udało się cofnąć udostępniania wątku",
      getStatusFailed: "Nie udało się pobrać statusu udostępniania wątku",
    },
    auth: {
      "authentication-required": "Wymagana autoryzacja",
      "insufficient-permissions": "Niewystarczające uprawnienia",
    },
  },
  validation: {
    responseHeader: "Wygenerowana Odpowiedź",
    contextHeader: "Oryginalny Kontekst i Źródła",
    content: {
      "user-question-sources-header": "PYTANIE UŻYTKOWNIKA I ŹRÓDŁA",
      "system-response-header": "ODPOWIEDŹ SYSTEMU",
      "not-provided": "Nie podano",
    },
  },
  manualWorkEstimator: {
    content: {
      "user-question-header": "PYTANIE UŻYTKOWNIKA",
      "system-response-header": "ODPOWIEDŹ SYSTEMU",
    },
  },
  notifications: {
    document: {
      invalid:
        "Dokument {{filename}} nie ma metadanych, jest uszkodzony lub nieprawidłowy i został usunięty.",
      removed:
        "Dokument {{filename}} został usunięty z zestawu monitorowanych dokumentów.",
    },
  },
  docx: {
    title: "Eksport czatu",
    exportedOn: "Wyeksportowano",
    downloadedOn: "Pobrano",
    keywords: "czat, eksport",
    description: "Wyeksportowana wiadomość czatu",
    tokenCount: "Liczba tokenów",
    canvasDocumentTitle: "Dokument Canvas",
    errors: {
      contentRequired: "Treść jest wymagana",
      writingFile: "Błąd podczas zapisywania pliku tymczasowego",
      generating: "Błąd podczas generowania dokumentu",
    },
  },
  deep_search: {
    instruction:
      "Jeśli jakiekolwiek źródła DeepSearch są istotne dla zapytania użytkownika i zostały użyte w odpowiedzi, podaj klikalne linki do tych źródeł na końcu wiadomości w dokładnie tym formacie: 'Źródła: [Tytuł źródła 1](URL1), [Tytuł źródła 2](URL2)'. Klikalny tekst dla każdego linku musi być zwięzłym opisem zawartości źródła (np. tytuł strony lub podsumowanie). Nigdy nie odwołuj się do źródeł DeepSearch za pomocą numerów w tekście (jak [1] lub [Źródło 1]); zawsze odwołuj się do źródła bezpośrednio przez jego nazwę lub zawartość. Nie używaj akademickich formatów cytowania.",
  },
};

module.exports = translations;
