# Star-Marking Documents

## Overview

Star-marking is a feature that allows users to mark important documents in a workspace. When a document is star-marked, it receives a similarity boost during vector searches, making it more likely to appear in search results.

## Implementation Details

### Requirements

For a document to be eligible for star-marking, it must be properly vectorized with docId information in the vector database. Documents that were vectorized before the star-marking feature was implemented may not have this information and will need to be re-vectorized.

Note: Un-marking a star-marked document does not require the document to be properly vectorized, as the vectorization check is only performed when star-marking a document.

### API Endpoints

The following API endpoints are available for star-marking:

1. **Update Star Status**

   - Endpoint: `/v1/workspace/:slug/update-star`
   - Method: POST
   - Parameters:
     - `docPath`: Path to the document
     - `starStatus`: <PERSON><PERSON>an indicating whether to star or unstar the document
     - `isFolder`: <PERSON><PERSON><PERSON> indicating whether the path is a folder
     - `forceUpdate`: Bo<PERSON>an to force update even if the document is not properly vectorized

2. **Re-Vectorize Document**
   - Endpoint: `/v1/workspace/:slug/re-vectorize`
   - Method: POST
   - Parameters:
     - `docId`: ID of the document to re-vectorize

### Frontend Integration

When attempting to star-mark a document, the frontend should:

1. Call the `setStarForDocument` method
2. Check the response for `requiresReVectorization` flag
3. If re-vectorization is required, show a message to the user and provide an option to re-vectorize the document
4. After re-vectorization, retry the star-marking operation

### Vector Search Boost

Star-marked documents receive a similarity boost of 0.1 during vector searches. This makes them more likely to appear in search results, but does not guarantee their inclusion if they are not relevant to the query.

## Troubleshooting

If a document cannot be star-marked, it may be because:

1. The document was vectorized before the star-marking feature was implemented
2. The document's vectors do not contain docId information
3. There was an error during the vectorization process

In these cases, re-vectorizing the document should resolve the issue.

Note: If you encounter issues when un-marking a star-marked document, check the server logs. The system should skip the vectorization check when un-marking documents, so this should not trigger re-vectorization requirements.

## Future Improvements

- Batch re-vectorization of multiple documents
- Automatic re-vectorization of documents when star-marking is attempted
- UI indicators to show which documents are eligible for star-marking
