import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, XCircle } from "@phosphor-icons/react";
import { LuSendH<PERSON>zon<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "react-icons/lu";
import { useTranslation } from "react-i18next";
import showToast from "@/utils/toast";
import { useFeedbackEnabled } from "@/stores/settingsStore";
import { useInvalidateFeedbackCount } from "@/stores/feedbackStore";
import { Button } from "@/components/Button";
import { useModal } from "@/hooks/useModal";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import FormItem from "@/components/ui/FormItem";
import Label from "@/components/ui/Label";
import Input from "@/components/ui/Input";
import System from "@/models/system";
import { TbMessage } from "react-icons/tb";
import Textarea from "@/components/ui/Textarea";

const createFeedbackSchema = (t) =>
  z.object({
    fullName: z
      .string()
      .min(2, t("feedback.validation.fullNameMinLength"))
      .max(100, t("feedback.validation.fullNameMaxLength"))
      .regex(/^[a-zA-Z0-9\s._@-]+$/, t("feedback.validation.fullNameFormat"))
      .transform((val) => val.trim()),
    message: z
      .string()
      .min(12, t("feedback.validation.messageMinLength"))
      .max(1000, t("feedback.validation.messageMaxLength"))
      .refine(
        (val) =>
          val
            .trim()
            .split(/\s+/)
            .filter((word) => word.length > 0).length >= 4,
        t("feedback.validation.messageMinWords")
      )
      .transform((val) => val.trim()),
    file: z
      .instanceof(File)
      .optional()
      .refine(
        (file) =>
          !file ||
          ["image/jpeg", "image/png", "application/pdf"].includes(file.type),
        t("feedback.validation.fileType")
      )
      .refine(
        (file) => !file || file.size <= 5 * 1024 * 1024,
        t("feedback.validation.fileSize")
      ),
  });

const FeedbackButton = () => {
  const { t } = useTranslation();
  const { isOpen, openModal, closeModal } = useModal();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState("");
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false);
  const feedbackEnabled = useFeedbackEnabled();
  const invalidateFeedbackCount = useInvalidateFeedbackCount();

  const feedbackSchema = createFeedbackSchema(t);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm({
    resolver: zodResolver(feedbackSchema),
    defaultValues: {
      fullName: "",
      message: "",
      file: undefined,
    },
  });

  const selectedFile = watch("file");

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    setSubmitError("");

    try {
      const { success, error } = await System.submitFeedback({
        fullName: data.fullName,
        message: data.message,
        file: data.file,
      });

      if (success) {
        setSubmitSuccess(true);
        reset();
        setTimeout(() => {
          invalidateFeedbackCount();
        }, 500);
      } else {
        setSubmitError(error || t("feedback.submitError"));
        showToast(error || t("feedback.submitError"), "error");
      }
    } catch (error) {
      console.error("Submission error:", error);
      setSubmitError(t("feedback.submitError"));
      showToast(t("feedback.submitError"), "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (submitSuccess) {
      const timer = setTimeout(() => {
        setShowSuccessAnimation(true);
      }, 250);
      return () => clearTimeout(timer);
    } else {
      setShowSuccessAnimation(false);
    }
  }, [submitSuccess]);

  const handleFileChange = (e) => {
    const file = e.target.files?.[0];
    setValue("file", file);
  };

  const removeFile = () => {
    setValue("file", undefined);
  };

  if (!feedbackEnabled?.enabled) return null;

  return (
    <div className="relative hidden md:block">
      <Button
        variant="outline"
        onClick={isOpen ? closeModal : openModal}
        className="relative w-full md:w-auto"
      >
        <TbMessage />
        Feedback
      </Button>

      {isOpen && (
        <div className="absolute top-20 right-0 rounded-xl normal-text p-6 w-[28rem] h-[27rem] bg-elevated border shadow-lg z-30">
          {!submitSuccess ? (
            <>
              <h3 className="text-xl">{t("feedback.improvePlatform")}</h3>
              <p className="mt-2 text-muted">
                {t("feedback.suggestionOrQuestion")}
              </p>
              <form
                onSubmit={handleSubmit(onSubmit)}
                className="flex flex-col gap-3 mt-5"
              >
                <FormItem>
                  <Label htmlFor="fullName">{t("workspace.name")}</Label>
                  <Input
                    {...register("fullName")}
                    placeholder={t("feedback.fullNamePlaceholder")}
                    disabled={isSubmitting}
                  />
                  {errors.fullName && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.fullName.message}
                    </p>
                  )}
                </FormItem>

                <FormItem>
                  <Label htmlFor="message">{t("feedback.message")}</Label>
                  <Textarea
                    {...register("message")}
                    placeholder={t("feedback.messagePlaceholder")}
                    disabled={isSubmitting}
                    className="w-full text-base"
                  />
                  {errors.message && (
                    <p className="text-red-500 text-sm mt-2">
                      {errors.message.message}
                    </p>
                  )}
                </FormItem>

                <div className="flex items-center gap-2 max-h-20 overflow-y-auto">
                  <label
                    htmlFor="file"
                    className="flex items-center gap-1 text-sm font-semibold text-foreground opacity-50 hover:cursor-pointer hover:opacity-100"
                  >
                    <Paperclip size={14} />
                    {t("feedback.attachment")}
                  </label>
                  <input
                    type="file"
                    id="file"
                    onChange={handleFileChange}
                    className="hidden"
                    disabled={isSubmitting}
                    accept="image/jpeg,image/png,application/pdf"
                  />
                  {selectedFile && (
                    <div className="flex items-center gap-2 flex-1">
                      <span className="text-xs text-gray-600 truncate flex-1">
                        {selectedFile.name}
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={removeFile}
                        type="button"
                      >
                        <XCircle size={14} />
                      </Button>
                    </div>
                  )}
                </div>

                <div className="flex justify-end gap-4">
                  <Button
                    variant="outline"
                    onClick={() => {
                      reset();
                      closeModal();
                    }}
                    disabled={isSubmitting}
                    type="button"
                  >
                    {t("invites.new.cancel")}
                  </Button>
                  <Button type="submit" isLoading={isSubmitting}>
                    {t("dataConnectors.github.submit")}
                    <LuSendHorizontal />
                  </Button>
                </div>
                {submitError && (
                  <p className="text-red-500 text-xs mt-2">{submitError}</p>
                )}
              </form>
            </>
          ) : (
            <div
              className="flex flex-col items-center justify-center text-center h-full transition-all duration-300"
              style={{
                opacity: showSuccessAnimation ? 1 : 0,
                transform: showSuccessAnimation
                  ? "translateY(0)"
                  : "translateY(10px)",
              }}
            >
              <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mb-4">
                <LuCheck className="size-8 text-white" />
              </div>
              <p className="text-xl font-medium text-foreground">
                {t("feedback.submitSuccess")}
              </p>
              <p className="text-muted mt-2 mb-6">{t("feedback.thankYou")}</p>
              <Button
                onClick={() => {
                  closeModal();
                  setSubmitSuccess(false);
                }}
              >
                {t("common.close")}
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FeedbackButton;
