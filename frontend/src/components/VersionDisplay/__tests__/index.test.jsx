import React from "react";
import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import VersionDisplay from "../index";
import Version from "@/models/version";

// Mock the Version model
jest.mock("@/models/version");

// Mock react-i18next
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key, fallback, options) => {
      if (key === "version.tooltip-title") {
        return `Version ${options?.version || "{{version}}"}`;
      }
      return fallback || key;
    },
  }),
}));

// Mock SimpleTooltip component
jest.mock("@/components/ui/Tooltip", () => {
  return function MockSimpleTooltip({ content, children, placement }) {
    return (
      <div data-testid="tooltip-wrapper" data-placement={placement}>
        {children}
        <div data-testid="tooltip-content" style={{ display: "none" }}>
          {content}
        </div>
      </div>
    );
  };
});

describe("VersionDisplay Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render version display when data is loaded successfully", async () => {
    const mockVersionData = {
      version: "1.1.0",
      description:
        "Added new document builder features and improved chat performance",
    };

    Version.getVersion.mockResolvedValue(mockVersionData);

    render(<VersionDisplay />);

    await waitFor(() => {
      expect(screen.getByText("v1.1.0")).toBeInTheDocument();
    });

    expect(screen.getByTestId("tooltip-wrapper")).toBeInTheDocument();
  });

  it("should not render anything when loading", () => {
    Version.getVersion.mockImplementation(() => new Promise(() => {})); // Never resolves

    render(<VersionDisplay />);

    expect(screen.queryByText(/v\d+\.\d+\.\d+/)).not.toBeInTheDocument();
  });

  it("should not render anything when version data is null", async () => {
    Version.getVersion.mockResolvedValue(null);

    render(<VersionDisplay />);

    await waitFor(() => {
      expect(Version.getVersion).toHaveBeenCalled();
    });

    expect(screen.queryByText(/v\d+\.\d+\.\d+/)).not.toBeInTheDocument();
  });

  it("should not render anything when API call fails", async () => {
    const consoleErrorSpy = jest
      .spyOn(console, "error")
      .mockImplementation(() => {});
    Version.getVersion.mockRejectedValue(new Error("API Error"));

    render(<VersionDisplay />);

    await waitFor(() => {
      expect(Version.getVersion).toHaveBeenCalled();
    });

    expect(screen.queryByText(/v\d+\.\d+\.\d+/)).not.toBeInTheDocument();
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      "Failed to fetch version:",
      expect.any(Error)
    );

    consoleErrorSpy.mockRestore();
  });

  it("should have correct CSS classes for positioning and styling", async () => {
    const mockVersionData = {
      version: "1.1.0",
      description: "Test description",
    };

    Version.getVersion.mockResolvedValue(mockVersionData);

    render(<VersionDisplay />);

    await waitFor(() => {
      expect(screen.getByText("v1.1.0")).toBeInTheDocument();
    });

    // Find the version text element
    const versionElement = screen.getByText("v1.1.0");

    // Check the outer container (grandparent) has fixed positioning
    const outerContainer = versionElement.parentElement?.parentElement;
    expect(outerContainer).toHaveClass("fixed", "bottom-4", "right-4", "z-40");

    // Check the version element has correct styling
    expect(versionElement).toHaveClass(
      "bg-background/80",
      "backdrop-blur-sm",
      "border",
      "border-border",
      "rounded-md",
      "px-2",
      "py-1",
      "text-sm",
      "text-muted-foreground",
      "hover:text-foreground",
      "hover:bg-background/90",
      "transition-all",
      "duration-200",
      "cursor-default",
      "select-none"
    );
  });

  it("should create tooltip content with correct structure", async () => {
    const mockVersionData = {
      version: "1.1.0",
      description: "Test description for tooltip",
    };

    Version.getVersion.mockResolvedValue(mockVersionData);

    render(<VersionDisplay />);

    await waitFor(() => {
      expect(screen.getByText("v1.1.0")).toBeInTheDocument();
    });

    // Check that tooltip content is created (even if hidden)
    const tooltipContent = screen.getByTestId("tooltip-content");
    expect(tooltipContent).toBeInTheDocument();

    // Check tooltip placement
    const tooltipWrapper = screen.getByTestId("tooltip-wrapper");
    expect(tooltipWrapper).toHaveAttribute("data-placement", "top");
  });

  it("should call Version.getVersion on mount", async () => {
    const mockVersionData = {
      version: "1.0.0",
      description: "Test",
    };

    Version.getVersion.mockResolvedValue(mockVersionData);

    render(<VersionDisplay />);

    expect(Version.getVersion).toHaveBeenCalledTimes(1);

    await waitFor(() => {
      expect(screen.getByText("v1.0.0")).toBeInTheDocument();
    });
  });

  it("should handle version data without description", async () => {
    const mockVersionData = {
      version: "2.0.0",
      description: undefined,
    };

    Version.getVersion.mockResolvedValue(mockVersionData);

    render(<VersionDisplay />);

    await waitFor(() => {
      expect(screen.getByText("v2.0.0")).toBeInTheDocument();
    });

    // Component should still render even without description
    expect(screen.getByTestId("tooltip-wrapper")).toBeInTheDocument();
  });

  it("should format version number with v prefix", async () => {
    const mockVersionData = {
      version: "3.2.1",
      description: "Test version",
    };

    Version.getVersion.mockResolvedValue(mockVersionData);

    render(<VersionDisplay />);

    await waitFor(() => {
      expect(screen.getByText("v3.2.1")).toBeInTheDocument();
    });

    // Should not show version without 'v' prefix
    expect(screen.queryByText("3.2.1")).not.toBeInTheDocument();
  });

  it("should use translation for tooltip title", async () => {
    const mockVersionData = {
      version: "1.1.0",
      description: "Test description",
    };

    Version.getVersion.mockResolvedValue(mockVersionData);

    render(<VersionDisplay />);

    await waitFor(() => {
      expect(screen.getByText("v1.1.0")).toBeInTheDocument();
    });

    // The translation should be called with the correct key and version
    // This is tested indirectly through the mock implementation
    expect(screen.getByText("v1.1.0")).toBeInTheDocument();
  });
});
