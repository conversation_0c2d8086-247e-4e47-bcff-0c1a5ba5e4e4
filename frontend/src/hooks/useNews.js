import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";
import useUser from "@/hooks/useUser";
import { getActiveSystemNews, getSystemNewsForRoles } from "@/data/news";

export function useNews() {
  const { t, i18n } = useTranslation();
  const { user } = useUser();
  const [unreadNews, setUnreadNews] = useState([]);
  const [systemNews, setSystemNews] = useState([]);
  const [allUnreadNews, setAllUnreadNews] = useState([]);
  const [allActiveNews, setAllActiveNews] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const hasInitiallyLoaded = useRef(false);

  // Memoize the translation function with language as dependency to update on language switch
  // Use optional chaining for i18n.language to handle test environments where i18n might be undefined
  const memoizedT = useMemo(() => t, [i18n?.language]);

  // Helper function to fetch dismissed system news IDs
  const fetchDismissedSystemNewsIds = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE}/news/dismissed-system`, {
        headers: baseHeaders(),
      });

      if (response.ok) {
        const data = await response.json();
        return data.dismissedIds || [];
      }
      return [];
    } catch (err) {
      console.error("Error fetching dismissed system news IDs:", err);
      return [];
    }
  }, []);

  // Load system news based on user roles
  const loadSystemNews = useCallback(
    async (dismissedSystemIds = null) => {
      if (!user?.role) return;

      try {
        const userRoles = user?.role ? [user.role] : [];

        // Get system news for user's roles WITHOUT translations first
        const activeSystemNews = getSystemNewsForRoles(userRoles);

        // Use provided dismissedSystemIds or fetch them if not provided
        let dismissedIds = dismissedSystemIds;
        if (dismissedIds === null) {
          dismissedIds = await fetchDismissedSystemNewsIds();
        }

        // Filter out dismissed system news using direct ID comparison (no hashing)
        const unreadSystemNews = activeSystemNews.filter(
          (news) => !dismissedIds.includes(news.id)
        );

        setSystemNews(unreadSystemNews);
      } catch (err) {
        console.error("Error loading system news:", err);
        // Don't set error for system news loading failure
      }
    },
    [user?.role, fetchDismissedSystemNewsIds]
  );

  const fetchUnreadNews = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE}/news/unread`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch news");
      }

      const data = await response.json();

      if (data.success) {
        setUnreadNews(data.news || []);
      } else {
        setError(data.message || "Failed to fetch news");
      }
    } catch (err) {
      console.error("Error fetching unread news:", err);
      setError("Failed to fetch news");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchAllActiveNews = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE}/news/all-active`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch all active news");
      }

      const data = await response.json();

      if (data.success) {
        // Apply translations to system news items from server
        const translatedServerNews = data.news.map((item) => {
          if (item.isSystemNews && (item.titleKey || item.contentKey)) {
            const translatedItem = { ...item };

            // Apply translations if keys exist
            if (item.titleKey) {
              translatedItem.title = memoizedT(
                item.titleKey,
                item.title || item.titleKey
              );
            }
            if (item.contentKey) {
              translatedItem.content = memoizedT(
                item.contentKey,
                item.content || item.contentKey
              );
            }

            return translatedItem;
          }
          return item;
        });

        // Also get system news from frontend data as fallback
        const userRoles = user?.role ? [user.role] : [];
        const frontendSystemNews = getSystemNewsForRoles(userRoles);

        // Fetch dismissed system news IDs once and reuse for both operations
        const dismissedSystemIds = await fetchDismissedSystemNewsIds();

        // Filter and translate frontend system news
        const activeFrontendSystemNews = frontendSystemNews
          .filter((news) => !dismissedSystemIds.includes(news.id))
          .map((item) => {
            const translatedItem = { ...item };

            // Apply translations if keys exist
            if (item.titleKey) {
              translatedItem.title = memoizedT(
                item.titleKey,
                item.title || item.titleKey
              );
            }
            if (item.contentKey) {
              translatedItem.content = memoizedT(
                item.contentKey,
                item.content || item.contentKey
              );
            }

            return {
              ...translatedItem,
              isSystemNews: true,
              isDismissed: false,
            };
          });

        // Combine server news with frontend system news (avoid duplicates)
        const serverSystemNewsIds = translatedServerNews
          .filter((item) => item.isSystemNews)
          .map((item) => item.id);

        const uniqueFrontendSystemNews = activeFrontendSystemNews.filter(
          (item) => !serverSystemNewsIds.includes(item.id)
        );

        const allNews = [...translatedServerNews, ...uniqueFrontendSystemNews];

        // Sort by priority and date
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        allNews.sort((a, b) => {
          const priorityDiff =
            (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
          if (priorityDiff !== 0) return priorityDiff;

          return new Date(b.createdAt) - new Date(a.createdAt);
        });

        setAllActiveNews(allNews);
      } else {
        setError(data.message || "Failed to fetch all active news");
      }
    } catch (err) {
      console.error("Error fetching all active news:", err);
      setError("Failed to fetch all active news");
    } finally {
      setIsLoading(false);
    }
  }, [user?.role, memoizedT, fetchDismissedSystemNewsIds]);

  // Combine system news and database news with translations applied
  useEffect(() => {
    const combined = [
      // Apply translations to system news when combining
      ...systemNews.map((item) => {
        const translatedItem = { ...item };

        // Apply translations if keys exist
        if (item.titleKey) {
          translatedItem.title = memoizedT(
            item.titleKey,
            item.title || item.titleKey
          );
        }
        if (item.contentKey) {
          translatedItem.content = memoizedT(
            item.contentKey,
            item.content || item.contentKey
          );
        }

        return { ...translatedItem, isSystemNews: true };
      }),
      ...unreadNews.map((item) => ({ ...item, isSystemNews: false })),
    ];

    // Sort by priority (urgent > high > medium > low) then by creation date
    const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
    combined.sort((a, b) => {
      const priorityDiff =
        (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
      if (priorityDiff !== 0) return priorityDiff;

      return new Date(b.createdAt) - new Date(a.createdAt);
    });

    setAllUnreadNews(combined);
  }, [systemNews, unreadNews, memoizedT]); // Include memoizedT to ensure translations update on language change

  const markAsViewed = useCallback(async (newsId, isSystemNews = false) => {
    try {
      if (isSystemNews) {
        // For system news, mark as dismissed (system news doesn't have separate viewed state)
        const response = await fetch(
          `${API_BASE}/news/system/${newsId}/dismiss`,
          {
            method: "POST",
            headers: baseHeaders(),
          }
        );

        if (!response.ok) {
          throw new Error(`Failed to dismiss system news: ${response.status}`);
        }

        // Only remove from system news if API call was successful
        setSystemNews((prev) => prev.filter((news) => news.id !== newsId));
      } else {
        // For database news, mark as viewed but don't remove from local state
        // This allows the news to reappear on next login if not dismissed
        const response = await fetch(`${API_BASE}/news/${newsId}/view`, {
          method: "POST",
          headers: baseHeaders(),
        });

        if (!response.ok) {
          throw new Error(`Failed to mark news as viewed: ${response.status}`);
        }

        // Don't remove from local state - let it reappear on next login
        // Only dismissNews should remove it permanently
      }
    } catch (err) {
      console.error("Error marking news as viewed:", err);
      throw err; // Re-throw so calling code can handle the error
    }
  }, []);

  const dismissNews = useCallback(async (newsId, isSystemNews = false) => {
    try {
      if (isSystemNews) {
        const response = await fetch(
          `${API_BASE}/news/system/${newsId}/dismiss`,
          {
            method: "POST",
            headers: baseHeaders(),
          }
        );

        if (!response.ok) {
          throw new Error(`Failed to dismiss system news: ${response.status}`);
        }

        // Only remove from system news if API call was successful
        setSystemNews((prev) => prev.filter((news) => news.id !== newsId));
      } else {
        const response = await fetch(`${API_BASE}/news/${newsId}/dismiss`, {
          method: "POST",
          headers: baseHeaders(),
        });

        if (!response.ok) {
          throw new Error(`Failed to dismiss news: ${response.status}`);
        }

        // Only remove from unread news if API call was successful
        setUnreadNews((prev) => prev.filter((news) => news.id !== newsId));
      }
    } catch (err) {
      console.error("Error dismissing news:", err);
      throw err;
    }
  }, []);

  // Initial load effect - only runs once when user is available
  useEffect(() => {
    if (user && !hasInitiallyLoaded.current) {
      hasInitiallyLoaded.current = true;
      fetchUnreadNews();
      loadSystemNews();
    }
  }, [user]); // Remove functions from dependency array

  return {
    unreadNews: allUnreadNews, // Return combined news
    allActiveNews, // Return all active news (including dismissed)
    isLoading,
    error,
    fetchUnreadNews: () => {
      fetchUnreadNews();
      loadSystemNews();
    },
    fetchAllActiveNews,
    markAsViewed,
    dismissNews,
    hasUnreadNews: allUnreadNews.length > 0,
    hasActiveNews: allActiveNews.length > 0,
    systemNewsCount: systemNews.length,
    databaseNewsCount: unreadNews.length,
  };
}
