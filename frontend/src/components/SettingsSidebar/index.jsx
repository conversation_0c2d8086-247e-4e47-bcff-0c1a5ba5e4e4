import React, { useEffect, useRef, useState } from "react";
import Footer from "../Footer";
import paths from "@/utils/paths";
import useUser from "@/hooks/useUser";
import {
  useFeedbackCount,
  useFetchFeedbackCount,
} from "@/stores/feedbackStore";
import { Link } from "react-router-dom";
import { isMobile } from "react-device-detect";
import { useTranslation } from "react-i18next";
import showToast from "@/utils/toast";
import { USER_BACKGROUND_COLOR } from "@/utils/constants";
import {
  House,
  List,
  Robot,
  Flask,
  UserCircleGear,
  PencilSimpleLine,
  Toolbox,
  Files,
} from "@phosphor-icons/react";
import System from "@/models/system";
import Option from "./MenuOption";
import { TbPlugConnectedX } from "react-icons/tb";
import { GrShieldSecurity } from "react-icons/gr";

export default function SettingsSidebar() {
  const { t } = useTranslation();
  const { user } = useUser();
  const sidebarRef = useRef(null);
  const [showSidebar, setShowSidebar] = useState(false);
  const [showBgOverlay, setShowBgOverlay] = useState(false);

  useEffect(() => {
    function handleBg() {
      if (showSidebar) {
        setTimeout(() => {
          setShowBgOverlay(true);
        }, 300);
      } else {
        setShowBgOverlay(false);
      }
    }
    handleBg();
  }, [showSidebar]);

  if (isMobile) {
    return (
      <>
        <div className="fixed top-0 left-0 z-10 flex items-center gap-28 px-4 py-2 text-foreground h-16">
          <button
            onClick={() => setShowSidebar(true)}
            className="rounded-md p-2 flex items-center justify-center text-foreground"
          >
            <List className="h-6 w-6" />
          </button>
          <div className="flex items-center justify-center">
            <span className="text-foreground font-semibold text-lg">
              {t("settings.title")}
            </span>
          </div>
        </div>
        <div
          style={{
            transform: showSidebar ? `translateX(0vw)` : `translateX(-100vw)`,
          }}
          className={`z-99 fixed top-0 left-0 transition-all duration-500 w-[100vw] h-[100vh]`}
        >
          <div
            className={`${showBgOverlay ? "transition-all opacity-1" : "transition-none opacity-0"} duration-500 fixed top-0 left-0 ${USER_BACKGROUND_COLOR} bg-opacity-75 w-screen h-screen`}
            onClick={() => setShowSidebar(false)}
          />
          <div
            ref={sidebarRef}
            className="h-[100vh] fixed top-0 left-0 rounded-r-[26px] bg-background w-[80%] p-[18px]"
          >
            <div className="w-full h-full flex flex-col overflow-x-hidden items-between">
              <div className="flex w-full items-center justify-between gap-x-4">
                <div className="flex shrink-1 w-fit items-center justify-start">
                  <button
                    onClick={() => setShowSidebar(false)}
                    className="rounded-md p-2 flex items-center justify-center text-foreground"
                  >
                    <List className="h-6 w-6" />
                  </button>
                </div>
                <div className="flex gap-x-2 items-center text-foreground shrink-0">
                  <a
                    href={paths.home()}
                    className="transition-all duration-300 p-2 rounded-full text-foreground bg-secondary-color hover:bg-secondary-hover"
                  >
                    <House className="h-6 w-6" />
                  </a>
                </div>
              </div>

              {/* Primary Body */}
              <div className="h-full flex flex-col w-full justify-between pt-4 overflow-y-scroll no-scroll ">
                <div className="h-auto md:sidebar-items">
                  <div className=" flex flex-col gap-y-4 pb-8 overflow-y-scroll no-scroll">
                    <SidebarOptions user={user} t={t} />
                    <div className="h-[1.5px] bg-[#3D4147] mx-3 mt-[14px]" />
                    <SupportEmail />
                    <Link
                      hidden={user?.role !== undefined && user.role !== "admin"}
                      to={paths.settings.privacy()}
                      className="text-darker hover:text-white text-xs leading-[18px] mx-3"
                    >
                      {t("settings.privacy-data")}
                    </Link>
                  </div>
                </div>
                <Footer user={user} t={t} />
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <div>
        <div
          className="relative mr-2 rounded-md min-w-[250px] p-[2px] h-full sidebar-block"
          ref={sidebarRef}
        >
          <div className="w-full h-full flex flex-col overflow-x-hidden items-between min-w-[235px]">
            <div className="text-foreground text-sm py-2 font-bold uppercase mb-2 setting-title">
              {t("settings.title")}
            </div>
            <div className="relative h-full flex flex-col w-full justify-between pt-[10px] overflow-y-scroll no-scroll">
              <div className="h-auto sidebar-items">
                <div className="flex flex-col gap-y-2 h-full pb-8 overflow-y-scroll no-scroll">
                  <SidebarOptions user={user} t={t} />
                  <div className="fixed bottom-8 flex flex-col">
                    <SupportEmail />
                    <Link
                      hidden={user?.role !== undefined && user.role !== "admin"}
                      to={paths.settings.privacy()}
                      className="text-foreground text-xs leading-[18px] mx-3"
                    >
                      {t("settings.privacy-data")}
                    </Link>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-2">
              <Footer user={user} t={t} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

function SupportEmail() {
  const { t } = useTranslation();
  const [supportEmail, setSupportEmail] = useState(paths.mailToISTLegal());

  useEffect(() => {
    const fetchSupportEmail = async () => {
      const supportEmail = await System.fetchSupportEmail();
      setSupportEmail(
        supportEmail?.email
          ? `mailto:${supportEmail.email}`
          : paths.mailToISTLegal()
      );
    };
    fetchSupportEmail();
  }, []);

  return (
    <Link
      to={supportEmail}
      className="text-foreground text-xs leading-[18px] mx-3 mt-1"
    >
      {t("common.contact-support")}
    </Link>
  );
}

const SidebarOptions = ({ user = null, t }) => {
  const feedbackCount = useFeedbackCount();
  const fetchFeedbackCount = useFetchFeedbackCount();

  // Fetch feedback count when component mounts
  useEffect(() => {
    fetchFeedbackCount();

    // Set up periodic refresh every 2 minutes
    const interval = setInterval(
      () => {
        fetchFeedbackCount();
      },
      2 * 60 * 1000
    );

    return () => clearInterval(interval);
  }, [fetchFeedbackCount]);

  return (
    <>
      <Option
        btnText={t("settings.customization")}
        icon={
          <PencilSimpleLine className="h-5 w-5 flex-shrink-0 text-foreground" />
        }
        href={paths.settings.appearance()}
        user={user}
        flex={true}
        roles={["admin"]}
      />
      <Option
        btnText={t("settings.admin")}
        icon={
          <UserCircleGear className="h-5 w-5 flex-shrink-0 text-foreground" />
        }
        user={user}
        roles={["admin", "manager", "superuser"]}
        childOptions={[
          {
            btnText: t("settings.system"),
            href: paths.settings.system(),
            roles: ["admin", "manager"],
          },
          {
            btnText: t("settings.users"),
            href: paths.settings.users(),
            roles: ["admin", "manager"],
          },
          {
            btnText: t("settings.invites"),
            href: paths.settings.invites(),
            roles: ["admin", "manager"],
          },
          {
            btnText: t("settings.workspaces"),
            href: paths.settings.workspaces(),
            roles: ["admin", "manager"],
          },
          {
            btnText: t("settings.mcp-servers", "MCP Servers"),
            href: paths.settings.mcpManagement(),
            roles: ["admin"],
          },
          {
            btnText: t("feedback-settings.header-title"),
            href: paths.settings.feedbackList(),
            roles: ["admin", "manager"],
            badge: feedbackCount,
          },
          {
            btnText: t("news-system.title"),
            href: paths.settings.news(),
            roles: ["admin", "manager"],
          },
          {
            btnText: t("settings.workspace-chats"),
            href: paths.settings.chats(),
            flex: true,
            roles: ["admin", "manager"],
          },
          {
            btnText: t("settings.default-settings"),
            href: paths.settings.defaultSettings(),
            flex: true,
            roles: ["admin", "manager"],
          },
          {
            btnText: t("settings.document-builder"),
            href: paths.settings.documentBuilder(),
            flex: true,
            roles: ["admin", "manager", "superuser"],
            isSuperuserDefault: true,
          },
          {
            btnText: t("settings.chat"),
            href: paths.settings.chatSettings(),
            flex: true,
            roles: ["admin", "manager"],
          },
          {
            btnText: t("settings.chat-ui-settings"),
            href: paths.settings.chatUISettings(),
            flex: true,
            roles: ["admin", "manager"],
          },
        ]}
      />
      <Option
        btnText={t("settings.ai-providers")}
        icon={
          <TbPlugConnectedX className="h-5 w-5 flex-shrink-0 text-foreground" />
        }
        user={user}
        childOptions={[
          {
            btnText: t("settings.llm"),
            href: paths.settings.llmPreference(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("settings.prompt-upgrade-llm"),
            href: paths.settings.upgradePromptLlmPreference(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("validate-answer.setting"),
            href: paths.settings.validateAnswerLlmPreference(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("template-llm-preference.settings"),
            href: paths.settings.templateLlmPreference(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("cdb-llm-preference.settings"),
            href: paths.settings.cdbLlmPreference(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("settings.custom-user-ai"),
            href: paths.settings.customUserAIpreference(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("settings.vector-database"),
            href: paths.settings.vectorDatabase(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("system.rerank.title"),
            href: paths.settings.reRankSettings(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("deep_search.title"),
            href: paths.settings.deepSearch(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("settings.embedder"),
            href: paths.settings.embedder.modelPreference(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("settings.text-splitting"),
            href: paths.settings.embedder.chunkingPreference(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("settings.voice-speech"),
            href: paths.settings.audioPreference(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("settings.transcription"),
            href: paths.settings.transcriptionPreference(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("settings.pdr-settings"),
            href: paths.settings.pdrSettings(),
            flex: true,
            roles: ["admin"],
          },
        ]}
      />
      <Option
        btnText={t("settings.agent-skills")}
        icon={<Robot className="h-5 w-5 flex-shrink-0 text-foreground" />}
        href={paths.settings.agentSkills()}
        user={user}
        flex={true}
        roles={["admin"]}
      />
      <Option
        btnText={t("settings.tools")}
        icon={<Toolbox className="h-5 w-5 flex-shrink-0 text-foreground" />}
        user={user}
        childOptions={[
          {
            btnText: t("settings.embed-chats"),
            href: paths.settings.embedChats(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("settings.embeds"),
            href: paths.settings.embedSetup(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("settings.event-logs"),
            href: paths.settings.logs(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("settings.api-keys"),
            href: paths.settings.apiKeys(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("settings.browser-extension"),
            href: paths.settings.browserExtension(),
            flex: true,
            roles: ["admin"],
          },
        ]}
      />
      <Option
        btnText={t("settings.security")}
        icon={<GrShieldSecurity className="h-5 w-5 flex-shrink-0" />}
        user={user}
        roles={["admin"]}
        childOptions={[
          {
            btnText: t("settings.security"),
            href: paths.settings.security(),
            flex: true,
            roles: ["admin"],
          },
        ]}
      />
      <Option
        btnText={t("document-drafting.title")}
        icon={<Files className="h-5 w-5 flex-shrink-0" />}
        user={user}
        roles={["admin"]}
        childOptions={[
          {
            btnText: t("document-drafting.configuration"),
            href: paths.settings.documentDrafting(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("document-drafting.drafting-model"),
            href: paths.settings.draftingModel(),
            flex: true,
            roles: ["admin"],
          },
          {
            btnText: t("document-drafting.chat-settings"),
            href: paths.settings.chatSettings(),
            flex: true,
            roles: ["admin"],
          },
        ]}
      />
      <HoldToReveal key="exp_features">
        <Option
          btnText={t("experimental.title")}
          icon={<Flask className="h-5 w-5 flex-shrink-0" />}
          href={paths.settings.experimental()}
          user={user}
          flex={true}
          roles={["admin"]}
        />
      </HoldToReveal>
    </>
  );
};

function HoldToReveal({ children, holdForMs = 3_000 }) {
  let timeout = null;
  const [showing, setShowing] = useState(
    window.localStorage.getItem("experimental_feature_preview_unlocked")
  );
  const { t } = useTranslation();

  useEffect(() => {
    const onPress = (e) => {
      if (!["Control", "Meta"].includes(e.key) || timeout !== null) return;
      timeout = setTimeout(() => {
        setShowing(true);
        // Setting toastId prevents hook spam from holding control too many times or the event not detaching
        showToast(t("show-toast.experimental-features-unlocked"));
        window.localStorage.setItem(
          "experimental_feature_preview_unlocked",
          "enabled"
        );
        window.removeEventListener("keypress", onPress);
        window.removeEventListener("keyup", onRelease);
        clearTimeout(timeout);
      }, holdForMs);
    };
    const onRelease = (e) => {
      if (!["Control", "Meta"].includes(e.key)) return;
      if (showing) {
        window.removeEventListener("keypress", onPress);
        window.removeEventListener("keyup", onRelease);
        clearTimeout(timeout);
        return;
      }
      clearTimeout(timeout);
    };

    if (!showing) {
      window.addEventListener("keydown", onPress);
      window.addEventListener("keyup", onRelease);
    }
    return () => {
      window.removeEventListener("keydown", onPress);
      window.removeEventListener("keyup", onRelease);
    };
  }, [t]);

  if (!showing) return null;
  return children;
}
