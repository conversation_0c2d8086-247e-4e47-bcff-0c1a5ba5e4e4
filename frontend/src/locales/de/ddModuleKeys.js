export default {
  // =========================
  // DOCUMENT DRAFTING
  // =========================
  "document-drafting": {
    title: "Dokumenten-Entwurf",
    description: "Steuere die Einstellungen für den Dokumenten-Entwurf.",
    configuration: "Konfiguration",
    "drafting-model": "Entwurfs-LLM",
    enabled: "Dokumenten-Entwurf ist aktiviert",
    disabled: "Dokumenten-Entwurf ist deaktiviert",
    "enabled-toast": "Dokumenten-Entwurf aktiviert",
    "disabled-toast": "Dokumenten-Entwurf deaktiviert",
    "desc-settings":
      "Der Administrator kann die Dokumenten-Entwurfseinstellungen für alle Benutzer ändern.",
    "drafting-llm": "Voreinstellung für Entwurfs-LLM",
    saving: "Wird gespeichert...",
    save: "Änderungen speichern",
    "chat-settings": "Chat-Einstellungen",
    "drafting-chat-settings": "Chat-Einstellungen für den Dokumenten-Entwurf",
    "chat-settings-desc":
      "Steuere das Verhalten der Chat-Funktion im Dokumenten-Entwurf.",
    "drafting-prompt": "System-Prompt für Dokumenten-Entwurf",
    "drafting-prompt-desc":
      "Der System-Prompt, der im Dokumenten-Entwurf genutzt wird, unterscheidet sich vom System-Prompt für die rechtliche Q&A. Er definiert den Kontext und die Anweisungen für die KI zur Generierung einer Antwort. Gib einen sorgfältig formulierten Prompt ein, damit die Antwort relevant und präzise ist.",
    linking: "Dokumenten-Verknüpfung",
    "legal-issues-prompt": "Prompt für rechtliche Fragen",
    "legal-issues-prompt-desc": "Gib den Prompt für rechtliche Fragen ein.",
    "memo-prompt": "Memo-Prompt",
    "memo-prompt-desc": "Gib den Prompt für ein Memo ein.",
    "desc-linkage":
      "Aktiviere die zusätzliche rechtliche Kontextsuche mittels Vektor/PDR, basierend auf Memo-Abrufen.",
    message: {
      title: "Vorgeschlagene Nachrichten für den Dokumenten-Entwurf",
      description:
        "Füge Nachrichten hinzu, die Benutzer beim Verfassen von Dokumenten schnell auswählen können.",
      heading: "Standard-Nachrichtenüberschrift",
      body: "Standard-Nachrichtentext",
      "new-heading": "Nachrichtenüberschrift",
      message: "Nachrichtentext",
      add: "Nachricht hinzufügen",
      save: "Nachrichten speichern",
    },
    "combine-prompt": "Kombinationsaufforderung",
    "combine-prompt-desc":
      "Geben Sie die Systemaufforderung für die Kombination mehrerer Antworten in eine einzelne Antwort an. Diese Aufforderung wird sowohl für die Kombination von Antworten und DD Linkage-Memos als auch für die Kombination der verschiedenen Antworten aus der Infinity Context-Verarbeitung verwendet.",
    "page-description":
      "Auf dieser Seite können Sie die verschiedenen Prompts anpassen, die in verschiedenen Funktionen des Dokumenten-Entwurfsmoduls verwendet werden. In jedem Eingabefeld wird der Standard-Prompt angezeigt, der verwendet wird, wenn auf dieser Seite kein benutzerdefinierter Prompt festgelegt wird.",
    "dd-linkage-steps": "Prompts für DD-Verknüpfungsschritte",
    "general-combination-prompt": "Allgemeiner Kombinationsprompt",
    "import-memo": {
      title: "Aus Legal QA importieren",
      "button-text": "Memo importieren",
      "search-placeholder": "Threads durchsuchen...",
      import: "Importieren",
      importing: "Importieren...",
      "no-threads": "Keine Legal QA-Threads gefunden",
      "no-matching-threads": "Keine Threads entsprechen Ihrer Suche",
      "thread-not-found": "Ausgewählter Thread nicht gefunden",
      "empty-thread":
        "Der ausgewählte Thread hat keine Inhalte zum Importieren",
      "import-success": "Thread-Inhalte erfolgreich importiert",
      "import-error": "Fehler beim Importieren der Thread-Inhalte",
      "import-error-details": "Fehler beim Import: {{details}}",
      "fetch-error":
        "Fehler beim Abrufen der Threads. Bitte versuchen Sie es später erneut.",
      "imported-from": "Importiert aus Legal QA-Thread",
      "unnamed-thread": "Unbenannter Thread",
      "unknown-workspace": "Unbekannter Arbeitsbereich",
      "no-threads-available": "Keine Threads zum Importieren verfügbar",
      "create-conversations-first":
        "Erstellen Sie zuerst Konversationen in einem Legal QA-Arbeitsbereich, dann können Sie sie hier importieren.",
      "no-legal-qa-workspaces":
        "Es wurden keine Legal QA-Arbeitsbereiche mit aktiven Threads gefunden. Erstellen Sie zuerst Konversationen in einem Legal QA-Arbeitsbereich, um sie zu importieren.",
      "empty-workspaces-with-names":
        "Legal QA-Arbeitsbereiche gefunden ({{workspaceNames}}), aber sie enthalten noch keine aktiven Threads. Erstellen Sie zuerst Konversationen in diesen Arbeitsbereichen, um sie zu importieren.",
      "import-success-with-name":
        "Thread erfolgreich importiert: {{threadName}}",
    },
  },

  // =========================
  // DD SETTINGS (WORKSPACE LINKING SETTINGS)
  // =========================
  "dd-settings": {
    title: "Einstellungen für Workspace-Verknüpfung",
    description: "Token-Limits und Verhalten für verknüpfte Workspaces steuern",
    "vector-search": {
      title: "Vektorsuche",
      description:
        "Wenn diese Funktion aktiviert ist, werden semantische Vektorsuchen über alle verknüpften Workspaces durchgeführt, um relevante juristische Dokumente zu finden. Das System konvertiert Benutzeranfragen in Vektoreinbettungen und gleicht sie mit Dokumentvektoren in der Datenbank jedes verknüpften Workspace ab. Diese Funktion dient als Fallback-Lösung, wenn die Memo-Generierung aktiviert ist, aber keine Ergebnisse liefert. Wenn die Memo-Generierung deaktiviert ist, wird die Vektorsuche zur primären Methode für den Abruf von Informationen aus verknüpften Workspaces. Die Suchtiefe wird durch die Einstellung für das Vektor-Token-Limit gesteuert.",
    },
    "memo-generation": {
      title: "Memo-Generierung",
      description:
        "Diese Funktion generiert automatisch präzise juristische Memos aus Dokumenten, die in verknüpften Workspaces gefunden werden. Wenn aktiviert, analysiert das System abgerufene Dokumente, um strukturierte Zusammenfassungen wichtiger juristischer Punkte, Präzedenzfälle und relevanter Kontexte zu erstellen. Diese Memos dienen als primäre Methode zur Einbindung von Wissen aus verknüpften Workspaces. Wenn die Memo-Generierung fehlschlägt oder keine Ergebnisse liefert, greift das System automatisch auf die Vektorsuche zurück (falls aktiviert), um sicherzustellen, dass relevante Informationen dennoch abgerufen werden. Die Länge und der Detailgrad dieser Memos werden durch die Einstellung für das Memo-Token-Limit gesteuert.",
    },
    "base-generation": {
      title: "Grundlegende Rechtsanalyse",
      description:
        "Ermöglicht die Generierung einer vorläufigen Rechtsanalyse basierend auf der ursprünglichen Benutzeranfrage, bevor Informationen aus verknüpften Workspaces einbezogen werden. Wenn aktiviert, erstellt das System einen grundlegenden Analyserahmen, der dabei hilft, die nachfolgenden Dokumentensuche- und Memo-Generierungsprozesse zu leiten. Diese Grundanalyse hilft sicherzustellen, dass Antworten auf die zentralen Rechtsfragen fokussiert bleiben, während unterstützende Informationen aus verknüpften Workspaces einbezogen werden. Der Umfang dieser Analyse wird durch die Einstellung für das Basis-Token-Limit gesteuert.",
    },
    "linked-workspace-impact": {
      title: "Token-Auswirkung verknüpfter Workspaces",
      description:
        "Steuert, wie das System sein Token-Budget über mehrere verknüpfte Workspaces hinweg verwaltet. Wenn diese Funktion aktiviert ist, passt das System die verfügbaren Tokens für jeden Workspace dynamisch basierend auf der Gesamtzahl der verknüpften Workspaces an, was eine faire Verteilung der Datenressourcen gewährleistet. Dies verhindert, dass ein einzelner Workspace das Kontextfenster dominiert, während eine umfassende Abdeckung aller relevanten Rechtsbereiche aufrechterhalten wird. Diese Einstellung reserviert Token-Kapazität speziell für Memo-Generierungs- und/oder Vektorsuche-Ergebnisse aus jedem verknüpften Workspace, was die Gesamtzahl der für den primären Workspace verfügbaren Tokens reduzieren kann, wenn viele Workspaces verknüpft sind.",
    },
    "vector-token-limit": {
      title: "Vektor-Token-Limit",
      description:
        "Gibt die maximale Anzahl von Tokens an, die für Vektorsuchergebnisse aus jedem verknüpften Workspace zugewiesen werden. Dieses Limit gilt, wenn die Vektorsuche verwendet wird, entweder als primäre Methode (wenn die Memo-Generierung deaktiviert ist) oder als Fallback-Lösung (wenn die Memo-Generierung fehlschlägt). Höhere Limits ermöglichen einen umfassenderen Dokumentenabruf, reduzieren jedoch die für andere Operationen verfügbaren Tokens.",
    },
    "memo-token-limit": {
      title: "Memo-Token-Limit",
      description:
        "Kontrolliert die maximale Länge der generierten juristischen Memos aus jedem verknüpften Workspace. Als primäre Methode zur Wissensintegration fassen diese Memos die wichtigsten juristischen Punkte aus den Dokumenten des verknüpften Workspace zusammen. Wenn ein Memo dieses Token-Limit überschreitet, wird es abgelehnt und das System greift auf die Vektorsuche zurück (falls aktiviert). Höhere Limits ermöglichen eine detailliertere juristische Analyse, können jedoch die Anzahl der verknüpften Workspaces, die einbezogen werden können, reduzieren.",
    },
    "base-token-limit": {
      title: "Basis-Token-Grenze",
      description:
        "Bestimmt die maximale Token-Länge für den initialen juristischen Analyserahmen. Diese Grenze beeinflusst, wie umfassend die Grundanalyse sein kann, bevor Informationen aus verknüpften Arbeitsbereichen einbezogen werden. Eine höhere Grenze ermöglicht eine detailliertere initiale Analyse, lässt aber weniger Raum für die Einbeziehung von Inhalten aus verknüpften Arbeitsbereichen.",
    },
    "toast-success": "Einstellungen erfolgreich aktualisiert",
    "toast-fail": "Einstellungen konnten nicht aktualisiert werden",
  },

  // =========================
  // WORKSPACE LINKING
  // =========================
  "workspace-linking": {
    title: "Einstellungen für Workspace-Verknüpfung",
    description: "Token-Limits und Verhalten für verknüpfte Workspaces steuern",
    "vector-search": {
      title: "Vektorsuche",
      description:
        "Fallback-Methode zum Finden relevanter Dokumente, wenn die Memo-Generierung fehlschlägt oder deaktiviert ist",
    },
    "memo-generation": {
      title: "Memo-Generierung",
      description:
        "Primäre Methode zur Einbindung von Wissen aus verknüpften Workspaces",
    },
    "base-generation": {
      title: "Grundlegende Rechtsanalyse",
      description:
        "Generierung einer initialen Rechtsanalyse aus Benutzeranfragen",
    },
    "linked-workspace-impact": {
      title: "Token-Auswirkung verknüpfter Workspaces",
      description:
        "Tokens für jeden verknüpften Workspace proportional zu ihrer Anzahl reservieren",
    },
    "vector-token-limit": {
      title: "Token-Limit für Vektorsuche",
      description:
        "Maximale Tokens pro verknüpftem Workspace für die Vektorsuche",
    },
    "memo-token-limit": {
      title: "Token-Limit für Memo-Erstellung",
      description: "Maximale Tokens für die Erstellung rechtlicher Memos",
    },
    "base-token-limit": {
      title: "Grundlegendes Token-Limit",
      description: "Maximale Tokens für den Abruf von Basisinhalten",
    },
    "toast-success": "Einstellungen erfolgreich aktualisiert",
    "toast-fail": "Einstellungen konnten nicht aktualisiert werden",
  },
  // =========================
  // BINARY LLM SELECTION
  // =========================
  binary_llm_selection: {
    "secondary-llm-toggle": "Binäre LLM-Auswahl",
    "secondary-llm-toggle-description":
      "Aktiviere diese Option, um Administratoren die Möglichkeit zu geben, zwischen zwei LLM-Modellen im Dokumenten-Entwurfsmodul zu wählen.",
    "secondary-llm-toggle-status": "Status: ",
    "secondary-llm-user-level": "Sekundäre LLM-Benutzerstufe",
    "secondary-llm-user-level-description":
      "Aktiviere diese Option, um ALLEN Benutzern die Möglichkeit zu geben, im Dokumenten-Entwurfsarbeitsbereich zwischen zwei LLM-Modellen zu wählen.",
  },
};
