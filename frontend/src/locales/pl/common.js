// /Users/<USER>/GitHub/I_produktion/ISTLegal/frontend/src/locales/pl/common.js

const TRANSLATIONS = {
  // =========================
  // COMMON STRINGS & PLACEHOLDERS
  // =========================
  common: {
    examples: "Przykłady",
    "workspaces-name": "Nazwa obszaru roboczego",
    ok: "OK",
    error: "błąd",
    confirm: "Potwierdź",
    confirmstart: "Potwierdź i rozpocznij",
    savesuccess: "Pomyślnie zapisano ustawienia",
    saveerror: "Nie udało się zapisać ustawień",
    success: "sukces",
    user: "Użytkownik",
    selection: "Wybór modelu",
    saving: "Zapisywanie...",
    save: "Zap<PERSON>z zmiany",
    previous: "Poprzednia strona",
    next: "Następna strona",
    cancel: "Anuluj",
    "search-placeholder": "Szukaj...",
    "no-results": "Nie znaleziono wyników",
    "more-actions": "Więcej działań",
    "delete-message": "Usuń wiadomość",
    copy: "Kopiuj",
    edit: "Edytuj",
    regenerate: "Wygeneruj ponownie",
    "export-word": "Eksportuj do Worda",
    "text-editing": "Edycja tekstu",
    "stop-generating": "Zatrzymaj generowanie",
    "attach-file": "Dołącz plik do tego czatu",
    home: "Strona główna",
    settings: "Ustawienia",
    support: "Wsparcie",
    "clear-reference": "Wyczyść odniesienie",
    "send-message": "Wyślij wiadomość",
    "ask-legal": "Zapytaj o informacje prawne",
    "stop-response": "Zatrzymaj generowanie odpowiedzi",
    "contact-support": "Skontaktuj się ze wsparciem",
    "copy-connection": "Kopiuj ciąg połączenia",
    "auto-connect": "Automatycznie połącz z rozszerzeniem",
    back: "Wstecz",
    "back-to-workspaces": "Powrót do obszarów roboczych",
    off: "Wyłączone",
    on: "Włączone",
    continue: "Kontynuuj",
    rename: "Zmień nazwę",
    delete: "Usuń",
    "default-skill":
      "Ta umiejętność jest domyślnie włączona i nie można jej wyłączyć.",
    timeframes: "Okresy czasu",
    other: "Inne opcje",
    placeholder: {
      username: "Moja nazwa użytkownika",
      password: "Twoje hasło",
      email: "Wprowadź swój e-mail",
      "support-email": "<EMAIL>",
      website: "https://www.przyklad.pl",
      "site-name": "IST Legal",
      "search-llm": "Szukaj konkretnego dostawcy LLM",
      "search-providers": "Szukaj dostępnych dostawców",
      "message-heading": "Nagłówek wiadomości",
      "message-content": "Wiadomość",
      "token-limit": "4096",
      "max-tokens": "Maksymalna liczba tokenów na żądanie (np.: 1024)",
      "api-key": "Klucz API",
      "base-url": "Adres URL podstawowy",
      endpoint: "Punkt końcowy API",
    },
    tooltip: {
      copy: "Kopiuj do schowka",
      delete: "Usuń ten element",
      edit: "Edytuj ten element",
      save: "Zapisz zmiany",
      cancel: "Anuluj zmiany",
      search: "Szukaj elementów",
      add: "Dodaj nowy element",
      remove: "Usuń element",
      upload: "Prześlij plik",
      download: "Pobierz plik",
      refresh: "Odśwież dane",
      settings: "Otwórz ustawienia",
      more: "Więcej opcji",
    },
    "default.message": "Wprowadź swoją wiadomość tutaj",
    preview: "Podgląd",
    prompt: "Prompt",
    loading: "Ładowanie...",
    download: "Pobierz w formacie surowym",
    open_in_new_tab: "Otwórz w nowej karcie z formatowaniem",
    close: "Zamknij",
    done: "Gotowe",
    note: "Uwaga",
    clearing: "Czyszczenie...",
  },

  // =========================
  // CONFIRMATION MESSAGES
  // =========================
  deleteWorkspaceConfirmation:
    "Czy na pewno chcesz usunąć {{name}}?\nPo wykonaniu tej operacji będzie ona niedostępna w tej instancji.\n\nTa operacja jest nieodwracalna.",
  deleteConfirmation:
    "Czy na pewno chcesz usunąć ${user.username}?\nPo wykonaniu tej operacji użytkownik zostanie wylogowany i nie będzie mógł korzystać z tej instancji.\n\nTa operacja jest nieodwracalna.",
  suspendConfirmation:
    "Czy na pewno chcesz zawiesić {{username}}?\nPo wykonaniu tej operacji użytkownik zostanie wylogowany i nie będzie mógł się ponownie zalogować do tej instancji, dopóki administrator nie zdejmie zawieszenia.",
  flushVectorCachesWorkspaceConfirmation:
    "Czy na pewno chcesz wyczyścić pamięć podręczną wektorów dla tej przestrzeni roboczej?",
  apiKeys: {
    "deactivate-title": "Dezaktywuj klucz API",
    "deactivate-message":
      "Czy na pewno chcesz dezaktywować ten klucz API?\nPo wykonaniu tej operacji nie będzie już można go używać.\n\nTa operacja jest nieodwracalna.",
  },

  // =========================
  // BUTTON LABELS
  // =========================
  button: {
    delete: "Usuń",
    edit: "Edytuj",
    suspend: "Zawiesić",
    unsuspend: "Wznowić",
    save: "Zapisz",
    accept: "Zaakceptuj",
    decline: "Odrzuć",
    ok: "OK",
    "flush-vector-caches": "Wyczyść pamięć podręczną wektorów",
    cancel: "Anuluj",
    saving: "Zapisywanie",
    save_llm: "Zapisz wybór LLM",
    save_template: "Zapisz szablon",
    "reset-to-default": "Przywróć domyślne",
    create: "Utwórz",
    enable: "Włącz",
    disable: "Wyłącz",
    reset: "Przywróć",
    revoke: "Odwołaj",
  },

  // =========================
  // CONFIRM MESSAGE
  // =========================
  "confirm-message": {
    "delete-doc":
      "Czy na pewno chcesz usunąć te pliki i foldery?\nSpowoduje to usunięcie plików z systemu oraz automatyczne usunięcie ich z dowolnych istniejących przestrzeni roboczych.\nTa operacja jest nieodwracalna.",
  },

  // =========================
  // STATUSES
  // =========================
  statuses: { enabled: "włączony", disabled: "wyłączony" },

  // =========================
  // ERRORS
  // =========================
  errors: {
    "fetch-models": "Nie udało się pobrać niestandardowych modeli",
    "fetch-models-error": "Błąd podczas pobierania modeli",
    "upgrade-error": "Błąd podczas ulepszania",
    "failed-process-file": "Nie udało się przetworzyć pliku: {{text}}",
    "failed-process-attachment": "Nie udało się przetworzyć załącznika",
    "failed-extract-content":
      "Nie udało się wyodrębnić zawartości z {{fileName}}",
    "failed-process-content": "Nie udało się przetworzyć zawartości pliku",
    common: { error: "Błąd" },
    workspace: {
      "already-exists": "Obszar roboczy o tej nazwie już istnieje",
    },
    auth: {
      "invalid-credentials": "Nieprawidłowe dane logowania.",
      "account-suspended": "Konto zawieszone przez administratora.",
      "invalid-password": "Podano nieprawidłowe hasło",
    },
    env: {
      "anthropic-key-format":
        "Nieprawidłowy format klucza API Anthropic. Klucz musi zaczynać się od 'sk-ant-'",
      "openai-key-format": "Klucz API OpenAI musi zaczynać się od 'sk-'",
      "jina-key-format": "Klucz API Jina musi zaczynać się od 'jina_'",
    },
  },

  // =========================
  // CHARTS
  // =========================
  charts: {
    downloading: "Pobieranie obrazu...",
    download: "Pobierz obraz wykresu",
  },

  // =========================
  // OPTIONS
  // =========================
  options: { yes: "Tak", no: "Nie" },

  // =========================
  // USER MANAGEMENT
  // =========================
  user: {
    "delete-title": "Usuń użytkownika",
    "suspend-title": "Zawiesić użytkownika",
    "unsuspend-title": "Wznowić użytkownika",
    suspended: "Użytkownik zawieszony pomyślnie",
    unsuspended: "Użytkownik wznowiony pomyślnie",
  },

  // =========================
  // METRICS VISIBILITY
  // =========================
  "metrics.visibility.hover": "Metryki są widoczne.",
  "metrics.visibility.available": "Metryki są dostępne.",

  // =========================
  // INVOICE REFERENCE NAVIGATION
  // =========================
  "invoice-reference-navigation": {
    title: "Aktywne odniesienie faktury",
    message:
      "Masz aktywne odniesienie faktury ({{reference}}) i zamierzasz przejść do innego {{destinationType}}. Co chcesz zrobić?",
    "current-reference": "Bieżące odniesienie:",
    explanation:
      "Możesz wyczyścić odniesienie i kontynuować lub zachować odniesienie i kontynuować.",
    "clear-and-continue": "Wyczyść odniesienie i kontynuuj",
    "keep-and-continue": "Zachowaj odniesienie i kontynuuj",
    success: "Odniesienie faktury zostało pomyślnie wyczyszczone",
    "destination-types": {
      thread: "wątek",
      workspace: "obszar roboczy",
      module: "moduł",
      location: "lokalizacja",
    },
  },

  // Months
  "month.1": "sty",
  "month.2": "lut",
  "month.3": "mar",
  "month.4": "kwi",
  "month.5": "maj",
  "month.6": "cze",
  "month.7": "lip",
  "month.8": "sie",
  "month.9": "wrz",
  "month.10": "paź",
  "month.11": "lis",
  "month.12": "gru",

  // =========================
  // VERSION DISPLAY
  // =========================
  version: {
    "tooltip-title": "Wersja {{version}}",
    title: "Wersja {{version}}",
  },

  // =========================
  // STYLE UPLOAD
  // =========================
  "style-upload": {
    "manage-files": "Zarządzaj plikami referencyjnymi stylu",
    "manage-files-description":
      "Tutaj możesz przesłać pliki, które napisałeś, aby wygenerować spersonalizowany styl. Gdy ta funkcja jest aktywna, wyniki na platformie będą bardziej zgodne z Twoim osobistym profesjonalnym stylem pisania.",
    "file-already-exists": "Plik już istnieje",
    "files-uploaded": "Pliki przesłane pomyślnie",
    "remove-file": "Usuń plik",
    "add-more": "Dodaj więcej plików",
    "clear-all": "Wyczyść wszystko",
    "no-files": "Proszę przesłać co najmniej jeden plik",
  },

  // =========================
  // TEXT EDITING
  // =========================
  "text-editing": {
    "button-tooltip": "Edytuj wiadomość z formatowaniem tekstu sformatowanego",
    "button-label": "Edycja tekstu",
    "modal-title": "Edytor tekstu",
    "modal-description":
      "Edytuj zawartość swojej wiadomości z formatowaniem tekstu sformatowanego",
    "editor-placeholder": "Zacznij edytować swoją wiadomość...",
    "save-success": "Wiadomość zaktualizowana pomyślnie",
    "save-error": "Nie udało się zaktualizować wiadomości",
    "error-empty-content": "Nie można zapisać pustej zawartości",
    saving: "Zapisywanie...",
  },
};

export default TRANSLATIONS;
