const prisma = require("../utils/prisma");
const { EventLogs } = require("./eventLogs");

const ThreadShare = {
  create: async function (threadId, userId) {
    // Idempotent: if a share already exists, return it
    const existing = await prisma.threadShare.findFirst({
      where: { threadId, userId },
    });
    if (existing) {
      return { share: existing, error: null };
    }
    try {
      const share = await prisma.threadShare.create({
        data: {
          threadId,
          userId,
        },
      });
      await EventLogs.logEvent("thread_shared", { threadId, userId });
      return { share, error: null };
    } catch (error) {
      console.error("Failed to create thread share:", error);
      return { share: null, error: "Failed to create thread share" };
    }
  },

  getByThread: async function (threadId) {
    try {
      const shares = await prisma.threadShare.findMany({
        where: { threadId },
        include: { user: true },
      });
      return { shares, error: null };
    } catch (error) {
      console.error("Failed to get thread shares:", error);
      return { shares: [], error: "Failed to get thread shares" };
    }
  },

  getThreadIdsSharedWithUser: async function (userId) {
    try {
      const shares = await prisma.threadShare.findMany({
        where: { userId },
        select: { threadId: true },
      });
      return shares.map((share) => share.threadId);
    } catch (error) {
      return [];
    }
  },

  delete: async function (threadId, userId) {
    try {
      await prisma.threadShare.deleteMany({
        where: {
          threadId,
          userId,
        },
      });
      await EventLogs.logEvent("thread_share_revoked", { threadId, userId });
      return { success: true, error: null };
    } catch (error) {
      console.error("Failed to delete thread share:", error);
      return { success: false, error: "Failed to delete thread share" };
    }
  },

  hasAccess: async function (threadId, userId) {
    try {
      // Check direct share
      const directShare = await prisma.threadShare.findFirst({
        where: {
          threadId,
          userId,
        },
      });
      if (directShare) return true;

      // Check organization-wide share based on the thread owner
      const threadRow = await prisma.workspace_threads.findUnique({
        where: { id: threadId },
        select: { sharedWithOrg: true, user_id: true },
      });
      if (threadRow?.sharedWithOrg) {
        const targetUser = await prisma.users.findUnique({
          where: { id: userId },
          select: { organizationId: true },
        });
        if (targetUser?.organizationId) {
          const owner = await prisma.users.findUnique({
            where: { id: threadRow.user_id },
            select: { organizationId: true },
          });
          if (owner?.organizationId === targetUser.organizationId) {
            return true;
          }
        }
      }

      return false;
    } catch (error) {
      console.error("Failed to check thread share access:", error);
      return false;
    }
  },
};

module.exports = { ThreadShare };
