---
description: Guidelines for Swagger/OpenAPI documentation
globs: server/swagger/**/*.js,server/endpoints/**/*.js
alwaysApply: false
---

# Swagger/OpenAPI Documentation

## Overview

The system uses Swagger/OpenAPI for API documentation. The documentation is automatically generated from code comments and configuration.

## Key Components

### Swagger Initialization

The `server/swagger/init.js` file initializes the Swagger documentation:

1. Sets up the Swagger UI
2. Configures the OpenAPI specification
3. Generates the OpenAPI JSON file

### OpenAPI Specification

The `server/swagger/openapi.json` file contains the OpenAPI specification:

- API endpoints
- Request and response schemas
- Authentication requirements
- Parameter descriptions

## Documenting Endpoints

When creating or modifying endpoints, follow these guidelines:

1. Add JSDoc comments to describe the endpoint
2. Include parameter descriptions
3. Document request and response schemas
4. Specify authentication requirements
5. Provide examples where appropriate

## Automatic Generation

The Swagger documentation is automatically regenerated:

- On server restart (configured in `nodemon.json`)
- When endpoints are modified

## Best Practices

- Keep documentation up-to-date with code changes
- Use clear and concise descriptions
- Provide examples for complex endpoints
- Document all parameters and response fields
- Include error responses and codes
- Test the documentation with the Swagger UI
