# Legal Templates: LLM Interactions and Prompt Upgrading

## Overview

Legal Templates provide a powerful way to streamline the creation of standardized legal documents. They allow users to define a structure, key clauses, and instructional content that can then be used to generate specific documents based on user input for a particular matter.

A critical aspect of the Legal Templates feature is how it interacts with Large Language Models (LLMs) for both refining the initial task (prompt upgrading) and generating the final document content.

## LLM Usage with Legal Templates

When a user initiates a document generation process using a Legal Template, a two-stage LLM process is typically involved:

1.  **Initial Prompt Upgrading**:

    - The user's initial input or the core task defined by the template (e.g., "Draft a Non-Disclosure Agreement for software development with company X") is first processed by an LLM.
    - The goal of this step is to "upgrade" or refine this initial input into a more detailed, structured, and effective prompt that will guide the subsequent document generation.
    - **LLM Selection for Template Prompt Upgrade**:
      - **Workspace/Template-Specific LLM**: If the Legal Template is associated with a specific workspace that has its own LLM provider and model configured (e.g., a particular version of GPT, Claude, or a local LLM), these workspace-specific LLM settings are prioritized for the prompt upgrade step. This is achieved by calling the `POST /api/system/generate-legal-task-prompt` endpoint with the `useWorkspaceLLM: true` flag and the relevant `workspaceId`.
      - **System Default**: If the template isn't explicitly tied to a workspace's LLM for the upgrade, or if that mechanism isn't invoked, the system falls back to a default prompt upgrade LLM. This default selection prioritizes `process.env.LLM_PROVIDER_PU` and then `process.env.LLM_PROVIDER`.
    - This ensures that if a template is designed to work best with a particular LLM, that LLM is used to refine the initial instructions for it.

2.  **Document Content Generation**:
    - The upgraded prompt (output from Stage 1) is then used as the primary instruction for the main document drafting process.
    - This involves various steps within the legal drafting flows (see `legal_drafting_flows.md`), such as generating a section list, drafting individual sections, and combining them.
    - **LLM for Content Generation**: The LLM used for these content generation steps is typically the one configured for the active workspace or, in the context of a template flow, the same LLM that was selected for the prompt upgrade (if a workspace/template-specific LLM was used). This ensures consistency, meaning the same "intelligence" that refined the prompt also generates the document content based on that refined prompt.

## Benefits of this Approach

- **Consistency**: Using the template's/workspace's designated LLM for both prompt upgrading and content generation ensures that the nuances and capabilities of that specific LLM are leveraged throughout the process.
- **Quality**: Refining the initial prompt with a potentially powerful LLM leads to better-structured inputs for the content generation phase, improving the quality of the final document.
- **Flexibility**: While promoting consistency, the system still allows for a general system-default prompt upgrade if a template isn't strictly tied to a specific LLM for its initial refinement.

## Interaction with Drafting Flows

The upgraded prompt, once generated, feeds into the `streamChatWithWorkspaceCDB` function and its associated flows (`mainDoc.js` or `noMainDoc.js`). The `LLMConnector` instance used within these flows for actual section drafting and content assembly will be the one corresponding to the LLM chosen for the template (if specified and used for upgrade) or the general workspace LLM.

This ensures that the template's intent, as refined by its preferred LLM, is carried through the entire document creation lifecycle.
