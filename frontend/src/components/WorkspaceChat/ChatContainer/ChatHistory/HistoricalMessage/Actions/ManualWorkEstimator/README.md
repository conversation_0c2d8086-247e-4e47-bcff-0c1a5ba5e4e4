# ManualWorkEstimator Component

## Overview

The ManualWorkEstimator component provides manager and admin users with the ability to estimate manual working hours required to produce AI-generated responses in the Legal QA module. The feature uses a configurable system prompt that can be customized by administrators.

## Features

- **Manager/Admin access**: Available to manager and admin level accounts for work estimation insights
- **Role-based prompt display**: Shows exact prompt used for estimation (admin only)
- **Legal QA integration**: Only appears for assistant messages in legal QA workspaces
- **LLM-powered estimation**: Uses system LLM to analyze response complexity
- **Configurable prompts**: Administrators can customize the estimation prompt via system settings
- **Language-aware responses**: Automatically responds in the same language as the original question
- **Modal interface**: Clean, accessible UI for displaying results
- **Internationalization**: Full i18n support across 7 languages

## Usage

### Basic Integration

```jsx
import ManualWorkEstimator from "./ManualWorkEstimator";

// In Actions component
{
  role === "assistant" &&
    isLegalQA &&
    (user?.role === "admin" || user?.role === "manager") && (
      <ManualWorkEstimator question={question} answer={response} user={user} />
    );
}
```

### Props

| Prop       | Type   | Required | Description                             |
| ---------- | ------ | -------- | --------------------------------------- |
| `question` | string | Yes      | The user's original question            |
| `answer`   | string | Yes      | The AI-generated response               |
| `user`     | object | Yes      | User object containing role information |

## Access Control

- **Button visibility**: Manager and admin level accounts
- **Prompt viewing**: Admin only (can view the exact prompt used for estimation)
- **Module restriction**: Only available in Legal QA workspaces
- **Message type**: Only appears for assistant messages

## State Management

The component manages the following state:

- `isLoading`: Loading state during LLM analysis
- `isOpen`: Modal visibility state
- `result`: Estimation result from API
- `showPrompt`: Toggle for prompt details display (admin only)

## API Integration

Uses the `System.estimateManualWork()` method to call the backend endpoint:

```javascript
const response = await System.estimateManualWork({ question, answer });
```

The backend uses a configurable system prompt that can be customized in General Settings > Default Settings.

## Configurable Prompt System

### Default Prompt

The system includes a comprehensive default prompt that:

- Considers legal analysis complexity
- Accounts for research requirements
- Evaluates document review time
- Assesses drafting effort
- Provides realistic time estimates with reasoning
- Includes sub-task time breakdowns
- Ends with total time estimation
- Responds in the same language as the user's question
- Outputs JSON formatted total time estimation

### Customization

Administrators can customize the prompt via:

1. Navigate to General Settings > Default Settings
2. Scroll to "Manual Work Estimator Prompt" section
3. Modify the prompt or use the default
4. Save changes to apply system-wide

## UI Components Used

- `Button` - For the trigger button and prompt toggle
- `Modal` - For displaying estimation results
- `Markdown` - For rendering formatted estimation text
- Icons from `react-icons/bs`

## Internationalization

All text uses translation keys across 7 supported languages (English, Swedish, French, German, Norwegian, Polish, Kinyarwanda):

- `manual-work-estimator.button-text` - Button text ("Time estimate")
- `manual-work-estimator.modal-title` - Modal title
- `manual-work-estimator.loading-text` - Loading message
- `manual-work-estimator.error-message` - Error message
- `manual-work-estimator.close-button` - Close button
- `manual-work-estimator.prompt-section.title` - Prompt section title
- `manual-work-estimator.prompt-section.description` - Prompt description

## Error Handling

- Toast notifications for API errors using localized error messages
- Graceful handling of LLM failures
- Fallback text for missing estimation results
- Network error recovery

## Accessibility

- Semantic HTML structure
- Proper ARIA labels
- Keyboard navigation support
- Screen reader compatibility
- High contrast support
- Focus management

## Security

- Role-based access control (manager/admin for button, admin for prompt viewing)
- Input sanitization handled by backend
- No storage of sensitive data
- Secure API communication

## Testing

The component includes comprehensive tests for:

- Manager/admin access control
- Admin-only prompt display
- Legal QA module restriction
- API error handling
- Modal interaction
- Loading states
- Internationalization
- Edge cases and error scenarios

## Backend Integration

### System Settings

- Prompt stored in `system_settings` table with label `manual_work_estimator_prompt`
- Falls back to default prompt if custom prompt is not set
- Follows same pattern as other system prompts

### LLM Integration

- Uses system LLM provider for estimation
- Supports multiple LLM providers (OpenAI, Anthropic, Gemini, etc.)
- Handles provider-specific message formatting
- Language-aware response generation

## Dependencies

- React hooks (useState)
- react-i18next for translations
- react-icons/bs for icons
- @phosphor-icons/react for loading spinner
- Custom UI components (Modal, Button, Markdown)
- System model for API calls
- Toast utility for notifications

## Browser Support

Compatible with all modern browsers that support:

- ES6+ features
- React 18+
- CSS Grid and Flexbox
- Modern JavaScript APIs

## Performance Considerations

- Lazy loading of estimation results
- Efficient state management
- Minimal re-renders
- Optimized API calls
- Proper cleanup on unmount
