const prisma = require("../utils/prisma");
const { EventLogs } = require("./eventLogs");
const { UserToken } = require("./userToken");
const { Organization } = require("./organization");

const User = {
  usernameRegex: new RegExp(/^[a-z0-9_.@-]+$/),
  writable: [
    // Used for generic updates so we can validate keys in request body
    "username",
    "password",
    "pfpFilename",
    "role",
    "suspended",
    "custom_ai_userselected",
    "custom_ai_option",
    "custom_ai_selected_engine",
    "custom_system_prompt",
    "economy_system_id",
    "organizationId",
  ],
  validations: {
    username: (newValue = "") => {
      try {
        if (String(newValue).length > 100)
          throw new Error("Username cannot be longer than 100 characters");
        if (String(newValue).length < 2)
          throw new Error("Username must be at least 2 characters");
        return String(newValue);
      } catch (e) {
        throw new Error(e.message);
      }
    },
    role: (role = "default") => {
      const VALID_ROLES = ["default", "admin", "manager", "superuser"];
      if (!VALID_ROLES.includes(role)) {
        throw new Error(
          `Invalid role. Allowed roles are: ${VALID_ROLES.join(", ")}`
        );
      }
      return String(role);
    },
    custom_ai_userselected: (value) => {
      return Boolean(value);
    },
    custom_ai_option: (value = 1) => {
      const option = Number(value);
      if (isNaN(option) || option < 1 || option > 3) {
        return 1; // Default to option 1 if invalid
      }
      return option;
    },
    custom_ai_selected_engine: (value = "_CUAI") => {
      // Validate that the value is a string and has a reasonable length
      if (typeof value !== "string") {
        return "_CUAI"; // Default value if not a string
      }

      // Trim the value to prevent excessively long strings
      const trimmed = String(value).trim().substring(0, 50);

      // If empty after trimming, return default
      return trimmed || "_CUAI";
    },
    custom_system_prompt: (value) => {
      if (value === null || value === undefined) return null;
      const str = String(value).trim();
      if (str.length === 0) return null;
      if (str.length > 10000) {
        throw new Error("Custom system prompt cannot exceed 10,000 characters");
      }
      return str;
    },
    organizationId: (value) => {
      const id = parseInt(value);
      if (isNaN(id) || id <= 0) {
        // Allow null or handle invalid ID case - here we allow null
        return null;
      }
      return id;
    },
  },

  // validations for the above writable fields.
  castColumnValue: function (key, value) {
    switch (key) {
      case "suspended":
      case "custom_ai_userselected":
        return Number(Boolean(value));
      case "custom_ai_option":
        return Number(value) || 1;
      case "organizationId":
        const parsedId = parseInt(value);
        return isNaN(parsedId) ? null : parsedId;
      default:
        return String(value);
    }
  },

  filterFields: function (user = {}) {
    const { password, ...rest } = user;
    return { ...rest };
  },

  create: async function ({
    username,
    password,
    role = "default",
    economy_system_id,
    organizationId = null,
    newOrganizationName = null,
  }) {
    const passwordCheck = this.checkPasswordComplexity(password);
    if (!passwordCheck.checkedOK) {
      return { user: null, error: passwordCheck.error };
    }

    try {
      // Do not allow new users to bypass validation
      if (!this.usernameRegex.test(username))
        throw new Error(
          "Username can only include lowercase letters, numbers, underscores (_), dots (.), @ symbols, and hyphens (-)."
        );

      let finalOrganizationId = organizationId;

      // If a new organization name is provided, create it first
      if (newOrganizationName) {
        const existingOrg = await Organization.get({
          name: newOrganizationName,
        });
        if (existingOrg) {
          console.warn(
            `Organization "${newOrganizationName}" already exists. Using existing ID.`
          );
          finalOrganizationId = existingOrg.id;
        } else {
          const { organization: newOrg, error: orgError } =
            await Organization.create(newOrganizationName);
          if (orgError || !newOrg) {
            return {
              user: null,
              error: orgError || "Failed to create new organization.",
            };
          }
          finalOrganizationId = newOrg.id;
        }
      }

      const bcrypt = require("bcryptjs");
      const hashedPassword = bcrypt.hashSync(password, 10);
      const user = await prisma.users.create({
        data: {
          username: this.validations.username(username),
          password: hashedPassword,
          role: this.validations.role(role),
          economy_system_id: economy_system_id || null,
          organizationId: finalOrganizationId,
        },
        include: {
          organization: true,
        },
      });

      if (role === "default") {
        const { Workspace } = require("./workspace");
        await Workspace.addStandardUser(user.id);
      }

      return { user: this.filterFields(user), error: null };
    } catch (error) {
      console.error("FAILED TO CREATE USER.", error.message);
      if (error.code === "P2002" && error.meta?.target?.includes("name")) {
        return {
          user: null,
          error: `Organization with name "${newOrganizationName}" already exists.`,
        };
      }
      return { user: null, error: error.message };
    }
  },

  // Log the changes to a user object, but omit sensitive fields
  // that are not meant to be logged.
  loggedChanges: function (updates, prev = {}) {
    const changes = {};
    const sensitiveFields = ["password"];

    Object.keys(updates).forEach((key) => {
      if (!sensitiveFields.includes(key) && updates[key] !== prev[key]) {
        changes[key] = `${prev[key]} => ${updates[key]}`;
      }
    });

    return changes;
  },

  update: async function (userId, updates = {}) {
    try {
      if (!userId) throw new Error("No user id provided for update");
      const currentUser = await prisma.users.findUnique({
        where: { id: parseInt(userId) },
      });
      if (!currentUser) return { success: false, error: "User not found" };

      let finalUpdates = { ...updates };
      let newOrganizationName = finalUpdates.newOrganizationName;
      delete finalUpdates.newOrganizationName;

      let finalOrganizationId = finalUpdates.organizationId;

      if (newOrganizationName) {
        const existingOrg = await Organization.get({
          name: newOrganizationName,
        });
        if (existingOrg) {
          console.warn(
            `Organization "${newOrganizationName}" already exists. Using existing ID.`
          );
          finalOrganizationId = existingOrg.id;
        } else {
          const { organization: newOrg, error: orgError } =
            await Organization.create(newOrganizationName);
          if (orgError || !newOrg) {
            return {
              success: false,
              error:
                orgError || "Failed to create new organization during update.",
            };
          }
          finalOrganizationId = newOrg.id;
        }
        finalUpdates.organizationId = finalOrganizationId;
      } else if (
        finalUpdates.organizationId === "none" ||
        finalUpdates.organizationId === null ||
        finalUpdates.organizationId === undefined
      ) {
        finalUpdates.organizationId = null;
      } else if (finalUpdates.hasOwnProperty("organizationId")) {
        const parsedId = parseInt(finalUpdates.organizationId);
        finalUpdates.organizationId = isNaN(parsedId) ? null : parsedId;
      }

      Object.entries(finalUpdates).forEach(([key, value]) => {
        if (this.writable.includes(key)) {
          if (Object.hasOwn(this.validations, key)) {
            finalUpdates[key] = this.validations[key](
              this.castColumnValue(key, value)
            );
          } else {
            finalUpdates[key] = this.castColumnValue(key, value);
          }
          return;
        }
        delete finalUpdates[key];
      });

      if (finalUpdates.hasOwnProperty("organizationId")) {
        finalUpdates.organizationId =
          finalUpdates.organizationId === null
            ? null
            : parseInt(finalUpdates.organizationId);
        if (isNaN(finalUpdates.organizationId))
          finalUpdates.organizationId = null;
      }

      if (Object.keys(finalUpdates).length === 0 && !newOrganizationName)
        return { success: false, error: "No valid updates applied." };

      if (Object.hasOwn(finalUpdates, "password")) {
        const passwordCheck = this.checkPasswordComplexity(
          finalUpdates.password
        );
        if (!passwordCheck.checkedOK) {
          return { success: false, error: passwordCheck.error };
        }
        const bcrypt = require("bcryptjs");
        finalUpdates.password = bcrypt.hashSync(finalUpdates.password, 10);
      }

      if (
        Object.hasOwn(finalUpdates, "username") &&
        currentUser.username !== finalUpdates.username &&
        !this.usernameRegex.test(finalUpdates.username)
      )
        return {
          success: false,
          error:
            "Username can only include lowercase letters, numbers, underscores (_), dots (.), @ symbols, and hyphens (-).",
        };

      const user = await prisma.users.update({
        where: { id: parseInt(userId) },
        data: finalUpdates,
        include: {
          organization: true,
        },
      });

      await EventLogs.logEvent(
        "user_updated",
        {
          username: user.username,
          changes: this.loggedChanges(finalUpdates, currentUser),
        },
        userId
      );
      return { success: true, error: null, user: this.filterFields(user) };
    } catch (error) {
      console.error(error.message);
      if (
        error.code === "P2002" &&
        error.meta?.target?.includes("name") &&
        updates.newOrganizationName
      ) {
        return {
          success: false,
          error: `Organization with name "${updates.newOrganizationName}" already exists.`,
        };
      }
      return { success: false, error: error.message };
    }
  },

  // Explicit direct update of user object.
  // Only use this method when directly setting a key value
  // that takes no user input for the keys being modified.
  _update: async function (id = null, data = {}) {
    if (!id) throw new Error("No user id provided for update");

    try {
      const user = await prisma.users.update({
        where: { id },
        data,
      });
      return { user, message: null };
    } catch (error) {
      console.error(error.message);
      return { user: null, message: error.message };
    }
  },

  get: async function (clause = {}) {
    try {
      // If clause is empty or doesn't have any valid properties, return null
      if (!clause || Object.keys(clause).length === 0) {
        return null;
      }

      // Simple ID validation - convert string IDs to numbers
      if (clause.id !== undefined) {
        if (clause.id === null || clause.id === undefined) {
          return null;
        }

        if (typeof clause.id === "string") {
          const parsedId = parseInt(clause.id);
          if (isNaN(parsedId)) {
            return null;
          }
          clause.id = parsedId;
        }
      }

      const user = await prisma.users.findFirst({ where: clause });
      return user ? this.filterFields({ ...user }) : null;
    } catch (error) {
      console.error("Error in User.get:", error.message);
      return null;
    }
  },

  // Returns user object with all fields
  _get: async function (clause = {}) {
    try {
      // Simple ID validation - convert string IDs to numbers
      if (clause.id !== undefined && typeof clause.id === "string") {
        const parsedId = parseInt(clause.id);
        if (isNaN(parsedId)) {
          return null;
        }
        clause.id = parsedId;
      }

      const user = await prisma.users.findFirst({
        where: clause,
        include: { organization: true },
      });
      return user ? { ...user } : null;
    } catch (error) {
      console.error("Error in User._get:", error.message);
      return null;
    }
  },

  count: async function (clause = {}) {
    try {
      const count = await prisma.users.count({ where: clause });
      return count;
    } catch (error) {
      console.error(error.message);
      return 0;
    }
  },

  delete: async function (clause = {}) {
    try {
      const usersToDelete = await prisma.users.findMany({ where: clause });

      if (usersToDelete.length === 0) {
        return true;
      }

      const { Workspace } = require("./workspace");

      for (const user of usersToDelete) {
        const workspaces = await Workspace.where({ user_id: user.id });

        for (const workspace of workspaces) {
          await Workspace.delete({ slug: workspace.slug });
        }

        await UserToken.deleteAllUserTokens(user.id);
        await prisma.users.delete({ where: { id: user.id } });
      }

      return true;
    } catch (error) {
      console.error("Error deleting user and associated data:", error);
      return false;
    }
  },

  where: async function (clause = {}, limit = null, offset = null) {
    try {
      const { users, total } = await prisma.$transaction(async (tx) => {
        const users = await tx.users.findMany({
          where: clause,
          ...(limit !== null ? { take: limit } : {}),
          ...(offset !== null ? { skip: offset } : {}),
          orderBy: { createdAt: "desc" },
          include: { organization: true },
        });
        const total = await tx.users.count({ where: clause });
        return { users, total };
      });

      return { users: users.map((user) => this.filterFields(user)), total };
    } catch (error) {
      console.error(error.message);
      return { users: [], total: 0 };
    }
  },

  checkPasswordComplexity: function (passwordInput = "") {
    const passwordComplexity = require("joi-password-complexity");
    // Can be set via ENV variable on boot. No frontend config at this time.
    // Docs: https://www.npmjs.com/package/joi-password-complexity
    const complexityOptions = {
      min: process.env.PASSWORDMINCHAR || 8,
      max: process.env.PASSWORDMAXCHAR || 250,
      lowerCase: process.env.PASSWORDLOWERCASE || 0,
      upperCase: process.env.PASSWORDUPPERCASE || 0,
      numeric: process.env.PASSWORDNUMERIC || 0,
      symbol: process.env.PASSWORDSYMBOL || 0,
      // reqCount should be equal to how many conditions you are testing for (1-4)
      requirementCount: process.env.PASSWORDREQUIREMENTS || 0,
    };

    const complexityCheck = passwordComplexity(
      complexityOptions,
      "password"
    ).validate(passwordInput);
    if (Object.hasOwn(complexityCheck, "error")) {
      let myError = "";
      let prepend = "";
      for (let i = 0; i < complexityCheck.error.details.length; i++) {
        myError += prepend + complexityCheck.error.details[i].message;
        prepend = ", ";
      }
      return { checkedOK: false, error: myError };
    }

    return { checkedOK: true, error: "No error." };
  },

  getWithOrg: async function (clause = {}) {
    try {
      // Simple ID validation - convert string IDs to numbers
      if (clause.id !== undefined && typeof clause.id === "string") {
        const parsedId = parseInt(clause.id);
        if (isNaN(parsedId)) {
          return null;
        }
        clause.id = parsedId;
      }

      const user = await prisma.users.findFirst({
        where: clause,
        include: { organization: true },
      });
      return user ? this.filterFields({ ...user }) : null;
    } catch (error) {
      console.error("Error in User.getWithOrg:", error.message);
      return null;
    }
  },

  // Get user with style preferences for chat system
  getWithStylePreferences: async function (userId) {
    try {
      if (!userId) {
        return null;
      }

      const user = await prisma.users.findFirst({
        where: { id: parseInt(userId) },
        include: {
          style_profiles: {
            where: { is_active: true },
            take: 1,
          },
        },
      });

      if (!user) {
        return null;
      }

      // Add style alignment properties to user object
      const userWithStyle = this.filterFields({ ...user });
      userWithStyle.styleAlignmentEnabled = user.style_profiles.length > 0;
      userWithStyle.activeStyleProfile = user.style_profiles[0] || null;

      return userWithStyle;
    } catch (error) {
      console.error("Error fetching user with style preferences:", error);
      return null;
    }
  },

  // Check if user has style alignment enabled
  hasStyleAlignment: async function (userId) {
    try {
      if (!userId) {
        return false;
      }

      const activeProfile = await prisma.userStyleProfile.findFirst({
        where: {
          user_id: parseInt(userId),
          is_active: true,
        },
      });

      return !!activeProfile;
    } catch (error) {
      console.error("Error checking style alignment:", error);
      return false;
    }
  },

  // Get active style profile for a user
  getActiveStyleProfile: async function (userId) {
    try {
      if (!userId) {
        return null;
      }

      const profile = await prisma.userStyleProfile.findFirst({
        where: {
          user_id: parseInt(userId),
          is_active: true,
        },
      });

      return profile;
    } catch (error) {
      console.error("Error fetching active style profile:", error);
      return null;
    }
  },
};

module.exports = { User };
