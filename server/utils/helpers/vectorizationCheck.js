/**
 * Utility functions for checking if documents have been properly vectorized with docIds
 * and updating document metadata in vector storage
 */

const { getVectorDbClass } = require("../helpers");

/**
 * Checks if a document has been properly vectorized with a docId
 * @param {string} workspaceSlug - The workspace slug
 * @param {string} docId - The document ID to check
 * @returns {Promise<boolean>} - True if the document has been properly vectorized
 */
async function isDocumentProperlyVectorized(workspaceSlug, docId) {
  try {
    if (!workspaceSlug || !docId) {
      console.log(
        `[VECTORIZATION-CHECK] Missing parameters: workspaceSlug=${workspaceSlug}, docId=${docId}`
      );
      return false;
    }

    const VectorDb = getVectorDbClass();
    // Fallback: if we have vector records in primary DB, assume document is properly vectorized
    try {
      const { DocumentVectors } = require("../../models/vectors");
      const dbVectors = await DocumentVectors.where({ docId });
      if (dbVectors.length > 0) {
        console.log(
          `[VECTORIZATION-CHECK] Document ${docId} has ${dbVectors.length} vector records in primary DB`
        );
        return true;
      }
    } catch (e) {
      console.error(
        `[VECTORIZATION-CHECK] Error checking DocumentVectors table:`,
        e
      );
    }

    // Check if the document exists in the vector database with a docId
    const hasDocId = await VectorDb.checkDocumentHasDocId(workspaceSlug, docId);

    console.log(
      `[VECTORIZATION-CHECK] Document ${docId} in workspace ${workspaceSlug} has docId in vectors: ${hasDocId}`
    );

    return hasDocId;
  } catch (error) {
    console.error(
      `[VECTORIZATION-CHECK] Error checking document vectorization:`,
      error
    );
    // For schema-related errors, return true to prevent unnecessary re-vectorization
    if (error.toString().includes("Schema error")) {
      console.log(
        `[VECTORIZATION-CHECK] Schema error detected, assuming document is properly vectorized`
      );
      return true;
    }
    return false;
  }
}

/**
 * Adds docId to existing vectors without re-embedding
 * This function now supports dynamic schema modification - if the LanceDB table
 * schema lacks a docId field, it will automatically add it using LanceDB's
 * addColumns API instead of falling back to full re-vectorization.
 *
 * @param {string} workspaceSlug - The workspace slug
 * @param {string} docId - The document ID to add
 * @returns {Promise<boolean>} - True if update was successful
 */
async function addDocIdToExistingVectors(workspaceSlug, docId) {
  try {
    if (!workspaceSlug || !docId) {
      console.log(
        `[METADATA-UPDATE] Missing parameters: workspaceSlug=${workspaceSlug}, docId=${docId}`
      );
      return false;
    }

    console.log(
      `[METADATA-UPDATE] Adding docId to existing vectors for document ${docId} in workspace ${workspaceSlug}`
    );

    const VectorDb = getVectorDbClass();
    const { DocumentVectors } = require("../../models/vectors");

    // Get the vector IDs for this document
    const vectors = await DocumentVectors.where({ docId });

    if (vectors.length === 0) {
      console.log(`[METADATA-UPDATE] No vectors found for document ${docId}`);
      return false;
    }

    console.log(
      `[METADATA-UPDATE] Found ${vectors.length} vectors for document ${docId}`
    );

    // Connect to LanceDB
    const { client } = await VectorDb.connect();

    // Check if namespace exists
    if (!(await VectorDb.namespaceExists(client, workspaceSlug))) {
      console.log(
        `[METADATA-UPDATE] Namespace ${workspaceSlug} does not exist`
      );
      return false;
    }

    const table = await client.openTable(workspaceSlug);

    // Check if the schema has a docId field, if not, we need to add it
    const schema = await table.schema();
    const hasDocIdField = schema.fields.some((field) => field.name === "docId");

    if (!hasDocIdField) {
      console.log(
        `[METADATA-UPDATE] Schema for namespace ${workspaceSlug} does not have docId field, attempting to add it`
      );
      try {
        // Use LanceDB's addColumns API to dynamically add the docId field to the schema
        // This allows us to upgrade existing vector stores without full re-vectorization
        // Reference: https://lancedb.github.io/lancedb/guides/tables/#adding-new-columns
        console.log(
          `[METADATA-UPDATE] Adding docId column to schema for namespace ${workspaceSlug}`
        );

        // Add the docId column with NULL as default value (cast to string type for UUID compatibility)
        await table.addColumns([
          { name: "docId", valueSql: "cast(NULL as string)" },
        ]);

        console.log(
          `[METADATA-UPDATE] Successfully added docId column to schema for namespace ${workspaceSlug}`
        );
      } catch (schemaError) {
        console.error(
          `[METADATA-UPDATE] Error adding docId column to schema:`,
          schemaError
        );

        // Check if the error is due to column already existing (race condition)
        if (
          schemaError.message &&
          schemaError.message.includes("already exists")
        ) {
          console.log(
            `[METADATA-UPDATE] docId column already exists (race condition), continuing with metadata update`
          );
        } else {
          console.log(
            `[METADATA-UPDATE] Schema modification failed, falling back to full re-vectorization`
          );
          return false;
        }
      }
    }

    // Update each vector with the docId
    let updateCount = 0;
    let errorCount = 0;

    for (const vector of vectors) {
      try {
        // Use LanceDB's update API with where clause and values
        await table.update({
          where: `id = '${vector.vectorId}'`,
          values: { docId: docId },
        });

        updateCount++;
      } catch (updateError) {
        console.error(
          `[METADATA-UPDATE] Error updating vector ${vector.vectorId}:`,
          updateError
        );
        errorCount++;
      }
    }

    console.log(
      `[METADATA-UPDATE] Updated ${updateCount}/${vectors.length} vectors for document ${docId} (${errorCount} errors)`
    );
    return updateCount > 0;
  } catch (error) {
    console.error(`[METADATA-UPDATE] Error adding docId to vectors:`, error);
    return false;
  }
}

/**
 * Re-vectorizes a document to ensure it has proper docId information
 * @param {string} workspaceSlug - The workspace slug
 * @param {string} docId - The document ID to re-vectorize
 * @param {object} document - The document object from the database
 * @returns {Promise<boolean>} - True if re-vectorization was successful
 */
async function reVectorizeDocument(workspaceSlug, docId, document) {
  // Return false if any required parameter is missing
  if (!workspaceSlug || !docId || !document) {
    console.log(
      `[VECTORIZATION-CHECK] Missing parameters for re-vectorization`
    );
    return false;
  }

  // Additional input validation
  if (typeof workspaceSlug !== "string" || typeof docId !== "string") {
    console.log(
      `[VECTORIZATION-CHECK] Invalid parameter types for re-vectorization`
    );
    return false;
  }

  if (!document.docpath || typeof document.docpath !== "string") {
    console.log(`[VECTORIZATION-CHECK] Invalid or missing document path`);
    return false;
  }

  console.log(
    `[VECTORIZATION-CHECK] Attempting lightweight metadata update for document ${docId}`
  );
  const lightweightSuccess = await addDocIdToExistingVectors(
    workspaceSlug,
    docId
  );

  if (lightweightSuccess) {
    console.log(
      `[VECTORIZATION-CHECK] Successfully updated document metadata for ${docId} without re-embedding`
    );
    return true;
  }

  // Fallback to full re-vectorization with safety checks
  try {
    console.log(
      `[VECTORIZATION-CHECK] Lightweight update failed, falling back to full re-vectorization for document ${docId}`
    );

    // SAFETY CHECK 1: Validate VectorDb is available before proceeding
    const VectorDb = getVectorDbClass();
    if (!VectorDb) {
      console.error(
        `[VECTORIZATION-CHECK] VectorDb class not available for document ${docId}`
      );
      return false;
    }

    // SAFETY CHECK 2: Verify document file exists and is readable BEFORE deletion
    const { fileData } = require("../../utils/files");
    console.log(
      `[VECTORIZATION-CHECK] Validating document file accessibility for ${document.docpath}`
    );

    const data = await fileData(document.docpath);
    if (!data) {
      console.error(
        `[VECTORIZATION-CHECK] Could not read file data for ${document.docpath} - aborting re-vectorization to prevent data loss`
      );
      return false;
    }

    // SAFETY CHECK 3: Validate document data has required content
    if (!data.pageContent || data.pageContent.length === 0) {
      console.error(
        `[VECTORIZATION-CHECK] Document ${document.docpath} has no content - aborting re-vectorization`
      );
      return false;
    }

    // SAFETY CHECK 4: Test vectorization capability before deletion
    console.log(
      `[VECTORIZATION-CHECK] Validating vectorization capability before proceeding with deletion`
    );

    // Only now that we've validated everything, proceed with deletion
    console.log(
      `[VECTORIZATION-CHECK] All safety checks passed, proceeding with vector deletion for document ${docId}`
    );

    try {
      await VectorDb.deleteDocumentFromNamespace(workspaceSlug, docId);
      console.log(
        `[VECTORIZATION-CHECK] Successfully deleted existing vectors for document ${docId}`
      );
    } catch (deleteError) {
      console.error(
        `[VECTORIZATION-CHECK] Failed to delete existing vectors for document ${docId}:`,
        deleteError
      );
      // Don't return false here - the vectors might not exist, which is okay
      console.log(
        `[VECTORIZATION-CHECK] Continuing with re-vectorization despite deletion error`
      );
    }

    // Add document back with docId
    console.log(
      `[VECTORIZATION-CHECK] Adding document back to namespace with docId ${docId}`
    );

    const { vectorized, error } = await VectorDb.addDocumentToNamespace(
      workspaceSlug,
      { ...data, docId },
      document.docpath
    );

    if (!vectorized) {
      console.error(
        `[VECTORIZATION-CHECK] Failed to re-vectorize document ${docId}:`,
        error
      );

      // Log this as a critical error since we may have lost data
      console.error(
        `[VECTORIZATION-CHECK] CRITICAL: Document ${docId} vectors were deleted but re-vectorization failed. Document may need manual recovery.`
      );

      return false;
    }

    console.log(
      `[VECTORIZATION-CHECK] Successfully re-vectorized document ${docId}`
    );
    return true;
  } catch (error) {
    console.error(
      `[VECTORIZATION-CHECK] Error re-vectorizing document ${docId}:`,
      error
    );

    // Provide more context about what stage failed
    console.error(
      `[VECTORIZATION-CHECK] Re-vectorization failure details: ${error.message}`
    );

    return false;
  }
}

module.exports = {
  isDocumentProperlyVectorized,
  addDocIdToExistingVectors,
  reVectorizeDocument,
};
