// This file is for allowing Codex to load dependencies for running npm tests after importing repo.
// It is not for using outside of custom Codex applications
//    For Codex:
//    "node setup-test-env.cjs" or
//    "npm run setup:test-env"

const { execSync } = require("child_process");
const { resolve } = require("path");
const root = __dirname;
const directories = [".", "server", "frontend"];
for (const dir of directories) {
  const cwd = resolve(root, dir);
  console.log(`Installing dependencies in ${dir}...`);
  try {
    execSync("npm install --no-audit --fund=false", { stdio: "inherit", cwd });
  } catch (err) {
    console.error(`Failed to install dependencies in ${dir}`);
    console.error(err.message);
  }
}
