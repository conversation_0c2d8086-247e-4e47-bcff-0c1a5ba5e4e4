const TRANSLATIONS = {
  "scroll-threshold-settings": {
    title: "Einstellungen für das Scroll-Verhalten",
    description:
      "Konfigurieren Sie die Empfindlichkeit des automatischen Scrollens in der Chat-Ansicht. Niedrigere Werte sind empfindlicher.",
    saved: "Einstellungen für das Scroll-Verhalten wurden aktualisiert.",
    saving: "Einstellungen werden gespeichert...",
    "save-changes": "Änderungen speichern",
    "fetch-error": "Fehler beim Laden der Scroll-Einstellungen.",
    error: "Fehler beim Speichern der Scroll-Einstellungen.",
    increase: "Erh<PERSON><PERSON>",
    decrease: "Verringern",
    "bottom-threshold": {
      title: "<PERSON><PERSON><PERSON>well<PERSON> (Pixel)",
      description:
        "Der Abstand vom unteren Rand des Chats, um als 'unten' zu gelten.",
    },
    "streaming-disable-threshold": {
      title: "Streaming-Deaktivierungsschwelle (Pixel)",
      description:
        "Wie weit der Benutzer während des Streamings von Nachrichten nach oben scrollen muss, um das automatische Scrollen zu deaktivieren.",
    },
    "auto-scroll-threshold": {
      title: "Auto-Scroll-Schwelle (Pixel)",
      description:
        "Der Abstand vom unteren Rand, bei dem das automatische Scrollen wieder aktiviert wird.",
    },
  },
};
export default TRANSLATIONS;
