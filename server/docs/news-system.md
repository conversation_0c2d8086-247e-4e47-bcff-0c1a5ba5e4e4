# News System - Server Documentation

## Overview

The server-side news system manages local news items (administrator-created announcements) and provides APIs for news management, user dismissals, and integration with the frontend system news. It supports role-based filtering, expiration dates, and comprehensive user interaction tracking.

## Architecture

### Database Schema

```sql
-- News messages table
CREATE TABLE news_messages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  priority TEXT NOT NULL DEFAULT 'medium',
  target_roles TEXT,                    -- JSON array of target roles
  expires_at DATETIME,                  -- Expiration date (nullable)
  created_by INTEGER NOT NULL,          -- User ID who created the news
  is_active BOOLEAN DEFAULT true,       -- Whether news is active
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- User news dismissals table
CREATE TABLE user_news_dismissals (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  news_id TEXT NOT NULL,               -- Can be local ID or system news ID
  dismissed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, news_id)
);
```

### Key Files

```
server/
├── models/
│   └── newsMessage.js                  # NewsMessage model
├── endpoints/
│   └── api/system/news.js             # News API endpoints
└── docs/
    └── news-system.md                 # This documentation
```

## NewsMessage Model

### Location

`server/models/newsMessage.js`

### Methods

#### `create(data)`

Creates a new news message.

```javascript
const result = await NewsMessage.create({
  title: "Important Update",
  content: "System maintenance scheduled...",
  priority: "high",
  targetRoles: ["admin", "manager"],
  expiresAt: new Date("2024-12-31T23:59:59Z"),
  createdBy: userId,
});

// Returns: { newsMessage: object, message: string|null }
```

**Parameters:**

- `title` (string, required): News title
- `content` (string, required): News content
- `priority` (string, optional): Priority level ("low", "medium", "high", "urgent")
- `targetRoles` (array, optional): Array of target user roles
- `expiresAt` (Date, optional): Expiration date
- `createdBy` (number, required): ID of user creating the news

#### `getActive()`

Retrieves all active news messages.

```javascript
const activeNews = await NewsMessage.getActive();
// Returns: Array of active news messages
```

#### `getUnreadForUser(userId)`

Gets unread news for a specific user, filtered by role and dismissals.

```javascript
const unreadNews = await NewsMessage.getUnreadForUser(1);
// Returns: Array of unread news messages for the user
```

**Features:**

- Filters by user role and target roles
- Excludes dismissed news items
- Excludes expired news items
- Only includes news created after user registration
- Sorts by priority and creation date

#### `update(id, updates)`

Updates an existing news message.

```javascript
const result = await NewsMessage.update(1, {
  title: "Updated Title",
  priority: "urgent",
  expiresAt: new Date("2025-01-01T00:00:00Z"),
});

// Returns: { newsMessage: object, message: string|null }
```

#### `delete(id)`

Deletes a news message.

```javascript
const result = await NewsMessage.delete(1);
// Returns: { success: boolean, message: string|null }
```

#### `dismiss(userId, newsId, isSystemNews)`

Records a news dismissal for a user.

```javascript
// Dismiss local news
const result = await NewsMessage.dismiss(1, 123, false);

// Dismiss system news
const result = await NewsMessage.dismiss(1, "system-welcome-2024", true);

// Returns: { dismissal: object, message: string|null }
```

**Parameters:**

- `userId` (number): ID of the user dismissing the news
- `newsId` (number|string): ID of the news item (number for local, string for system)
- `isSystemNews` (boolean): Whether this is system news or local news

#### `getDismissedNewsIds(userId)`

Gets all dismissed news IDs for a user, separated by type.

```javascript
const dismissed = await NewsMessage.getDismissedNewsIds(1);
// Returns: { local: [1, 5], system: ["system-welcome-2024"] }
```

## API Endpoints

### Base Path

All news endpoints are under `/api/news/`

### Endpoints

#### `GET /api/news/unread`

Get unread news for the current user.

**Authentication:** Required
**Response:**

```json
{
  "success": true,
  "news": [
    {
      "id": 1,
      "title": "Important Update",
      "content": "System maintenance...",
      "priority": "high",
      "target_roles": "[\"admin\"]",
      "expires_at": "2024-12-31T23:59:59Z",
      "created_by": 1,
      "is_active": true,
      "created_at": "2024-01-15T10:00:00Z"
    }
  ]
}
```

#### `GET /api/news/all-active`

Get all active news (admin only).

**Authentication:** Required (Admin)
**Response:**

```json
{
  "success": true,
  "news": [
    // Array of all active news messages
  ]
}
```

#### `POST /api/news`

Create a new news message (admin only).

**Authentication:** Required (Admin)
**Request Body:**

```json
{
  "title": "New Announcement",
  "content": "Important information...",
  "priority": "medium",
  "targetRoles": ["admin", "manager"],
  "expiresAt": "2024-12-31T23:59:59Z"
}
```

**Response:**

```json
{
  "success": true,
  "newsMessage": {
    "id": 1,
    "title": "New Announcement"
    // ... other fields
  }
}
```

#### `PUT /api/news/:id`

Update an existing news message (admin only).

**Authentication:** Required (Admin)
**Parameters:** `id` - News message ID
**Request Body:** Same as POST (all fields optional)

#### `DELETE /api/news/:id`

Delete a news message (admin only).

**Authentication:** Required (Admin)
**Parameters:** `id` - News message ID

#### `POST /api/news/:id/dismiss`

Dismiss a local news item for the current user.

**Authentication:** Required
**Parameters:** `id` - Local news message ID

**Response:**

```json
{
  "success": true,
  "message": "News dismissed successfully"
}
```

#### `POST /api/news/system/:id/dismiss`

Dismiss a system news item for the current user.

**Authentication:** Required
**Parameters:** `id` - System news ID (e.g., "system-welcome-2024")

#### `GET /api/news/dismissed-system`

Get dismissed system news IDs for the current user.

**Authentication:** Required
**Response:**

```json
{
  "success": true,
  "dismissedIds": ["system-welcome-2024", "system-update-2024"]
}
```

## Role-Based Filtering

### Target Roles

News messages can target specific user roles:

```javascript
// Target all users
targetRoles: null;

// Target specific roles
targetRoles: ["admin"];
targetRoles: ["admin", "manager"];
targetRoles: ["default"];
```

### Filtering Logic

1. **No Target Roles**: News visible to all users
2. **With Target Roles**: News only visible to users with matching roles
3. **Invalid JSON**: Treated as no target roles (visible to all)

### Implementation

```javascript
// In getUnreadForUser method
const filteredNews = allNews.filter((news) => {
  if (!news.target_roles) return true;

  try {
    const targetRoles = JSON.parse(news.target_roles);
    return targetRoles.includes(user.role);
  } catch (error) {
    return true; // Fallback: show to all users
  }
});
```

## Priority System

### Priority Levels

```javascript
"urgent"; // Highest priority, red badge
"high"; // High priority, orange badge
"medium"; // Standard priority, blue badge
"low"; // Lowest priority, gray badge
```

### Sorting Order

News items are sorted by:

1. **Priority** (urgent → high → medium → low)
2. **Creation Date** (newest first)

### Database Ordering

```sql
ORDER BY
  CASE priority
    WHEN 'urgent' THEN 4
    WHEN 'high' THEN 3
    WHEN 'medium' THEN 2
    WHEN 'low' THEN 1
    ELSE 0
  END DESC,
  created_at DESC
```

## Expiration Handling

### Expiration Logic

```javascript
// In database queries
WHERE (expires_at IS NULL OR expires_at > NOW())
```

### Setting Expiration

```javascript
// Never expires
expiresAt: null;

// Expires on specific date
expiresAt: new Date("2024-12-31T23:59:59Z");
```

### Automatic Cleanup

Expired news items are automatically filtered out of queries but remain in the database for audit purposes.

## Dismissal System

### Dismissal Types

1. **Local News Dismissal**: Stores numeric ID with "local-" prefix
2. **System News Dismissal**: Stores system news ID directly

### Storage Format

```javascript
// Local news dismissal
news_id: "local-123"; // For local news with ID 123

// System news dismissal
news_id: "system-welcome-2024"; // For system news with ID "system-welcome-2024"
```

### Dismissal Persistence

- Dismissals are permanent per user
- Stored in `user_news_dismissals` table
- Unique constraint prevents duplicate dismissals
- Uses upsert pattern for idempotent operations

## Error Handling

### Model Error Handling

```javascript
try {
  const result = await NewsMessage.create(data);
  return { newsMessage: result, message: null };
} catch (error) {
  console.error("NewsMessage creation failed:", error);
  return { newsMessage: null, message: error.message };
}
```

### API Error Responses

```javascript
// Success response
{ success: true, data: result }

// Error response
{ success: false, error: "Error message" }

// Validation error
{ success: false, error: "Invalid input data" }

// Authentication error
{ success: false, error: "Unauthorized" }
```

### Common Error Scenarios

1. **Invalid User ID**: Returns empty array or null
2. **Database Connection**: Returns error message
3. **Invalid JSON in target_roles**: Treats as no target roles
4. **Missing Required Fields**: Returns validation error
5. **Unauthorized Access**: Returns 401/403 status

## Testing

### Test Files

- **`server/tests/unit/models/newsMessage.test.js`**: Unit tests for NewsMessage model
- **`server/tests/integration/newsEndpoints.test.js`**: Integration tests for API endpoints

### Test Coverage

1. **Model Methods**: All CRUD operations and business logic
2. **Role Filtering**: Target role functionality
3. **Dismissal Logic**: Both local and system news dismissal
4. **Expiration Handling**: Date-based filtering
5. **Error Scenarios**: Database errors, invalid input
6. **API Endpoints**: All HTTP methods and status codes

### Running Tests

```bash
# Run model tests
npm test -- tests/unit/models/newsMessage.test.js

# Run endpoint tests
npm test -- tests/integration/newsEndpoints.test.js

# Run all news tests
npm test -- --testNamePattern="news"
```

## Performance Considerations

### Database Optimization

1. **Indexes**: Add indexes on frequently queried columns
2. **Pagination**: Implement pagination for large news lists
3. **Caching**: Cache active news for better performance
4. **Cleanup**: Periodic cleanup of old dismissals

### Recommended Indexes

```sql
-- Index for active news queries
CREATE INDEX idx_news_active_expires ON news_messages(is_active, expires_at);

-- Index for user dismissals
CREATE INDEX idx_dismissals_user ON user_news_dismissals(user_id);

-- Index for creation date sorting
CREATE INDEX idx_news_created ON news_messages(created_at DESC);
```

### Query Optimization

```javascript
// Efficient query for unread news
const unreadNews = await prisma.news_messages.findMany({
  where: {
    is_active: true,
    createdAt: { gt: user.createdAt },
    OR: [{ expires_at: null }, { expires_at: { gt: new Date() } }],
    NOT: {
      id: { in: dismissedLocalIds },
    },
  },
  orderBy: [{ priority: "desc" }, { createdAt: "desc" }],
});
```

## Security Considerations

### Authentication

- All endpoints require valid JWT token
- Admin endpoints require admin role verification
- User-specific data filtered by user ID

### Authorization

```javascript
// Admin-only endpoints
if (user.role !== "admin") {
  return response.status(403).json({
    success: false,
    error: "Admin access required",
  });
}

// User can only dismiss their own news
const dismissal = await NewsMessage.dismiss(user.id, newsId, isSystemNews);
```

### Input Validation

```javascript
// Validate required fields
if (!title || !content) {
  return response.status(400).json({
    success: false,
    error: "Title and content are required",
  });
}

// Validate priority
const validPriorities = ["low", "medium", "high", "urgent"];
if (priority && !validPriorities.includes(priority)) {
  return response.status(400).json({
    success: false,
    error: "Invalid priority level",
  });
}
```

### Data Sanitization

- HTML content is escaped to prevent XSS
- SQL injection prevented by Prisma ORM
- User input validated and sanitized

## Integration with Frontend

### System News Integration

The server provides APIs for frontend system news dismissal:

```javascript
// Frontend calls server to dismiss system news
POST / api / news / system / system - welcome - 2024 / dismiss;

// Server stores dismissal with system news ID
news_id: "system-welcome-2024";
```

### Combined News Flow

1. **Frontend**: Loads system news from local data
2. **Frontend**: Fetches dismissed system news IDs from server
3. **Frontend**: Filters out dismissed system news
4. **Frontend**: Fetches unread local news from server
5. **Frontend**: Combines and sorts all news

### API Response Format

Server responses match frontend expectations:

```javascript
// Server response format
{
  success: true,
  news: [
    {
      id: 1,
      title: "Local News",
      content: "Content...",
      priority: "high",
      isSystemNews: false,  // Added by frontend
      // ... other fields
    }
  ]
}
```

## Monitoring and Logging

### Key Metrics

1. **News Creation Rate**: Track admin news creation
2. **Dismissal Rate**: Monitor user engagement
3. **Error Rate**: Track API failures
4. **Performance**: Monitor query execution times

### Logging

```javascript
// Log news operations
console.log(`News created: ${newsMessage.id} by user ${userId}`);
console.log(`News dismissed: ${newsId} by user ${userId}`);
console.error(`News operation failed: ${error.message}`);
```

### Health Checks

```javascript
// Health check endpoint
GET /api/news/health

// Response
{
  status: "healthy",
  activeNews: 5,
  totalDismissals: 150
}
```

## Migration and Deployment

### Database Migrations

```sql
-- Migration for news_messages table
CREATE TABLE news_messages (
  -- ... table definition
);

-- Migration for user_news_dismissals table
CREATE TABLE user_news_dismissals (
  -- ... table definition
);
```

### Deployment Considerations

1. **Backward Compatibility**: Ensure API changes are backward compatible
2. **Data Migration**: Migrate existing news data if needed
3. **Index Creation**: Add database indexes for performance
4. **Environment Variables**: Configure any required environment variables

## Related Documentation

- [Frontend News System](../frontend/docs/news-system.md)
- [Database Schema](./database-schema.md)
- [API Endpoints](./ENDPOINTS.md)
- [User Roles](./UserRoles.md)
- [Testing](./testing.md)

## Troubleshooting

### Common Issues

1. **News Not Appearing**: Check `is_active` flag and expiration date
2. **Role Filtering Not Working**: Verify `target_roles` JSON format
3. **Dismissal Not Persisting**: Check user authentication and database connection
4. **Performance Issues**: Review database indexes and query optimization

### Debug Queries

```sql
-- Check active news
SELECT * FROM news_messages WHERE is_active = true;

-- Check user dismissals
SELECT * FROM user_news_dismissals WHERE user_id = ?;

-- Check expired news
SELECT * FROM news_messages WHERE expires_at < datetime('now');
```

### Logging Debug Information

```javascript
// Enable debug logging
console.log("User role:", user.role);
console.log("Target roles:", news.target_roles);
console.log("Dismissed IDs:", dismissedIds);
console.log("Filtered news count:", filteredNews.length);
```
