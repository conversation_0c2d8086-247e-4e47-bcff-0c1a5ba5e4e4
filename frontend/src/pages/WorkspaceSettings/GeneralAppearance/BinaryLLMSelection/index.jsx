import React, { useEffect, useState } from "react";
import Open<PERSON>i<PERSON>ogo from "@/media/llmprovider/openai.png";
import AzureOpenAiLogo from "@/media/llmprovider/azure.png";
import Anthrop<PERSON><PERSON>ogo from "@/media/llmprovider/anthropic.png";
import <PERSON><PERSON><PERSON> from "@/media/llmprovider/gemini.png";
import Ollama<PERSON>ogo from "@/media/llmprovider/ollama.png";
import LMStudioLogo from "@/media/llmprovider/lmstudio.png";
import LocalAiLogo from "@/media/llmprovider/localai.png";
import TogetherAILogo from "@/media/llmprovider/togetherai.png";
import MistralLogo from "@/media/llmprovider/mistral.jpeg";
import HuggingFaceLogo from "@/media/llmprovider/huggingface.png";
import PerplexityLogo from "@/media/llmprovider/perplexity.png";
import OpenRouterLogo from "@/media/llmprovider/openrouter.jpeg";
import Gro<PERSON><PERSON><PERSON> from "@/media/llmprovider/groq.png";
import Kobold<PERSON><PERSON>ogo from "@/media/llmprovider/koboldcpp.png";
import TextGenWebUILogo from "@/media/llmprovider/text-generation-webui.png";
import CohereLogo from "@/media/llmprovider/cohere.png";
import LiteLLMLogo from "@/media/llmprovider/litellm.png";
import AWSBedrockLogo from "@/media/llmprovider/bedrock.png";
import useUser from "@/hooks/useUser";
import System from "@/models/system";
import { useTranslation } from "react-i18next";

// Move LLM_MAP to a separate config file for better organization
export const LLM_MAP = {
  openai: {
    value: "OpenAi",
    logo: OpenAiLogo,
  },
  azure: {
    value: "AzureOpenAi",
    logo: AzureOpenAiLogo,
  },
  anthropic: {
    value: "Anthropic",
    logo: AnthropicLogo,
  },
  gemini: {
    value: "GeminiLLM",
    logo: GeminiLogo,
  },
  huggingface: {
    value: "HuggingFace",
    logo: HuggingFaceLogo,
  },
  ollama: {
    value: "OllamaLLM",
    logo: OllamaLogo,
  },
  lmstudio: {
    value: "LMStudio",
    logo: LMStudioLogo,
  },
  localai: {
    value: "LocalAi",
    logo: LocalAiLogo,
  },
  togetherai: {
    value: "TogetherAi",
    logo: TogetherAILogo,
  },
  mistral: {
    value: "Mistral",
    logo: MistralLogo,
  },
  perplexity: {
    value: "Perplexity",
    logo: PerplexityLogo,
  },
  openrouter: {
    value: "OpenRouter",
    logo: OpenRouterLogo,
  },
  groq: {
    value: "Groq",
    logo: GroqLogo,
  },
  koboldcpp: {
    value: "KoboldCpp",
    logo: KoboldCPPLogo,
  },
  textgenwebui: {
    value: "TextGenWebUi",
    logo: TextGenWebUILogo,
  },
  cohere: {
    value: "Cohere",
    logo: CohereLogo,
  },
  litellm: {
    value: "LiteLLM",
    logo: LiteLLMLogo,
  },
  awsbedrock: {
    value: "AwsBedrock",
    logo: AWSBedrockLogo,
  },
};

const LLMOption = ({ isSelected, onClick, logo, model }) => (
  <div
    className={`flex flex-col p-4 rounded-lg cursor-pointer transition-all duration-200 min-w-[200px] ${
      isSelected
        ? "bg-accent border-2 border-primary"
        : "bg-card border-2 border-border hover:border-primary hover:bg-accent"
    }`}
    onClick={onClick}
  >
    <div className="flex items-center justify-start gap-3 h-full">
      <img src={logo} className="w-16 h-16 flex-shrink-0" alt={model} />
      <h3 className="font-medium text-lg text-foreground break-words overflow-hidden">
        {model}
      </h3>
    </div>
  </div>
);

export default function BinaryLLMSelection() {
  const { t } = useTranslation();
  const [selected, setSelected] = useState(() => {
    const savedOption = localStorage.getItem("selectedLLMOption");
    return savedOption ? parseInt(savedOption) : 0;
  });
  const [settings, setSettings] = useState(null);
  const [selectedLLM, setSelectedLLM] = useState(null);
  const [selectedSecondaryLLM, setSelectedSecondaryLLM] = useState(null);
  const [selectedModel, setSelectedModel] = useState(null);
  const [selectedSecondaryModel, setSelectedSecondaryModel] = useState(null);
  const { user } = useUser();

  useEffect(() => {
    const fetchKeys = async () => {
      try {
        const _settings = await System.keys();
        setSettings(_settings);
        setSelectedLLM(_settings?.LLMProvider_DD);
        setSelectedSecondaryLLM(_settings?.LLMProvider_DD_2);
        setSelectedModel(
          _settings[`${LLM_MAP[_settings?.LLMProvider_DD]?.value}ModelPref_DD`]
        );
        setSelectedSecondaryModel(
          _settings[
            `${LLM_MAP[_settings?.LLMProvider_DD_2]?.value}ModelPref_DD_2`
          ]
        );
        setSelected(parseInt(localStorage.getItem("selectedLLMOption")));
      } catch (error) {
        console.error("Error fetching keys:", error);
      }
    };
    fetchKeys();
  }, []);

  const handleSelect = (option) => {
    setSelected(option);
    localStorage.setItem("selectedLLMOption", option.toString());
  };

  if (
    settings?.BinaryLLM_DD === "off" ||
    (user.role !== "admin" && settings?.BinaryLLMUserLevel_DD !== "on")
  ) {
    return null;
  }
  return (
    <div className="flex flex-col gap-y-2">
      <label className="text-sm font-semibold">
        {t("binaryLLM.model-selection")}
      </label>
      <div className="flex gap-4">
        <LLMOption
          isSelected={selected === 0}
          onClick={() => handleSelect(0)}
          logo={LLM_MAP[selectedLLM]?.logo}
          model={selectedModel}
        />
        <LLMOption
          isSelected={selected === 1}
          onClick={() => handleSelect(1)}
          logo={LLM_MAP[selectedSecondaryLLM]?.logo}
          model={selectedSecondaryModel}
        />
      </div>
    </div>
  );
}
