import React, { useState } from "react";
import { MagnifyingGlass, X } from "@phosphor-icons/react";
import Admin from "@/models/admin";
import showToast from "@/utils/toast";
import { useTranslation } from "react-i18next";
import Checkbox from "@/components/ui/Checkbox";
import { Button } from "@/components/Button";
import Modal from "@/components/ui/Modal";

export default function AddMemberModal({
  closeModal,
  workspace,
  users = [],
  isOpen,
}) {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUsers, setSelectedUsers] = useState(workspace?.userIds || []);

  // Unwrap the users prop, handling nested shape { users: [...], total }
  const allUsers = Array.isArray(users)
    ? users
    : users && Array.isArray(users.users)
      ? users.users
      : [];

  const filteredUsers = allUsers
    .filter((user) =>
      String(user.username).toLowerCase().includes(searchTerm.toLowerCase())
    )
    .filter((user) => user.role !== "admin")
    .filter((user) => user.role !== "manager")
    .filter((user) => user.role !== "superuser");

  const filteredUserIds = filteredUsers.map((user) => user.id);

  const filteredSelectedUsers = selectedUsers.filter((id) =>
    filteredUserIds.includes(id)
  );

  const handleUpdate = async (e) => {
    e.preventDefault();
    const { success, error } = await Admin.updateUsersInWorkspace(
      workspace.id,
      selectedUsers
    );
    if (success) {
      showToast(t("show-toast.users-updated"), "success");
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
    showToast(error, "error");
  };

  const handleUserSelect = (userId) => {
    setSelectedUsers((prevSelectedUsers) => {
      if (prevSelectedUsers.includes(userId)) {
        return prevSelectedUsers.filter((id) => id !== userId);
      } else {
        return [...prevSelectedUsers, userId];
      }
    });
  };

  const handleSelectAll = () => {
    if (
      filteredSelectedUsers.length === filteredUsers.length &&
      filteredUsers.length > 0
    ) {
      setSelectedUsers((prevSelected) =>
        prevSelected.filter((id) => !filteredUserIds.includes(id))
      );
    } else {
      setSelectedUsers((prevSelected) => {
        const newSelection = [...prevSelected];
        filteredUsers.forEach((user) => {
          if (!newSelection.includes(user.id)) {
            newSelection.push(user.id);
          }
        });
        return newSelection;
      });
    }
  };

  const handleUnselect = () => {
    setSelectedUsers([]);
  };

  const isUserSelected = (userId) => {
    return selectedUsers.includes(userId);
  };

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  const isAllSelected =
    filteredUsers.length > 0 &&
    filteredSelectedUsers.length === filteredUsers.length;

  const isSomeSelected =
    filteredUsers.length > 0 &&
    filteredSelectedUsers.length > 0 &&
    filteredSelectedUsers.length < filteredUsers.length;

  return (
    <Modal
      isOpen={isOpen}
      onClose={closeModal}
      title={t("workspaces-settings.users.users")}
      footer={
        <>
          {filteredUsers.length > 0 && (
            <>
              <div className="flex-grow">
                <Checkbox
                  variant={"primary"}
                  checked={isAllSelected}
                  indeterminate={isSomeSelected}
                  onChange={handleSelectAll}
                  label={t("workspaces-settings.users.select")}
                  id={"select-all"}
                />
              </div>
              {selectedUsers.length > 0 && (
                <Button variant="ghost" type="button" onClick={handleUnselect}>
                  {t("workspaces-settings.users.unselect")}
                </Button>
              )}
            </>
          )}
          <Button type="button" variant="secondary" onClick={closeModal}>
            {t("common.cancel")}
          </Button>
          <Button type="submit" form="updateUsers">
            {t("workspaces-settings.users.save")}
          </Button>
        </>
      }
    >
      <div className="relative">
        <div className="flex items-center gap-x-4">
          <div className="relative">
            <input
              onChange={handleSearch}
              className="bg-background border border-border text-foreground text-sm rounded-lg focus:ring-primary focus:border-primary block w-[400px] h-[34px] pl-10"
              placeholder={t("workspaces-settings.users.search")}
            />
            <MagnifyingGlass
              size={16}
              weight="bold"
              className="text-muted-foreground absolute left-3 top-1/2 transform -translate-y-1/2"
            />
          </div>
        </div>
        <form onSubmit={handleUpdate} id="updateUsers">
          <div className="max-h-[385px] overflow-y-auto no-scroll pt-2">
            {filteredUsers.length > 0 ? (
              <div className="flex flex-col gap-y-[8px] py-2 px-1">
                {filteredUsers.map((user) => (
                  <Checkbox
                    variant={"primary"}
                    checked={isUserSelected(user.id)}
                    onChange={() => handleUserSelect(user.id)}
                    label={user.username}
                    id={user.id}
                    key={user.id}
                  />
                ))}
              </div>
            ) : (
              <p className="text-foreground text-opacity-60 text-sm font-medium ">
                {t("workspaces-settings.users.no-user")}
              </p>
            )}
          </div>
        </form>
      </div>
    </Modal>
  );
}
