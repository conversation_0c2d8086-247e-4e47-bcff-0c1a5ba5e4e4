---
description: Guidelines for legal templates and document management
globs: "{frontend,server}/**/*.{js,jsx}"
alwaysApply: false
---
# Legal Templates and Document Management

## Document Types
- Include only specified agreement types across all locales
- Sort document categories and document types alphabetically in lists
- Apply translation keys properly for document types
- Enable document categories and documents based on the current system language

## Template Settings
- Settings should have tabs for Document Types, Input Fields, and Special Clauses
- Format business descriptions as separate sentences in prompt construction
- Change 'Business type' field to include '(If relevant)' in the description
- Show empty by default with empty being a selectable choice

## Custom Templates
- Allow users to add new categories/types for custom templates
- Store uploaded document content for custom templates
- Align custom legal template creation with document builder structure
- Make custom templates the first tab in the template selection interface

## Template Content
- Bracket template content with <CONTENT_EXAMPLES> tags
- Bracket template formatting with <FORMATTING_EXAMPLES> tags
- Clarify in placeholders that template content is for content examples/instructions
- Add '(optional)' to the names of template content and template formatting fields

## Document Creation
- Auto-process constructed prompts through prompt upgrading before submission. This upgrade step can utilize the legal template's associated workspace LLM configuration if specified, ensuring the prompt is refined by an intelligence aligned with the LLM that will perform the main document generation.
- Add document template prompts to the system prompt rather than user prompts
- Support DOCX file upload/edit with LLM commands
- Display uploaded content directly in chat box without auto-saving

## Document Formatting
- Use consistent DOCX export formatting across the platform
- Ensure proper markdown transformation for documents
- Format legal document headings with markdown numbering
- Ensure numbered paragraphs are aligned with numbered headings

## Additional Fields
- Add 'Template detailed description (optional)' field to custom legal templates
- Include detailed description in prompt construction with localized instruction
- Add 'Template formatting (optional)' field for uploading DOCX files
- Add 'Output example' field to legal task creation/editing
