const request = require("supertest");
const express = require("express");
const { streamChatWithWorkspace } = require("../../utils/chats");
const { chatPrompt } = require("../../utils/helpers/chat");
const { UserStyleProfile } = require("../../models/userStyleProfile");

// Mock dependencies
jest.mock("../../utils/chats", () => ({
  streamChatWithWorkspace: jest.fn(),
}));

jest.mock("../../utils/helpers/chat", () => ({
  chatPrompt: jest.fn(),
}));

jest.mock("../../models/userStyleProfile", () => ({
  UserStyleProfile: {
    getActiveProfile: jest.fn(),
  },
}));

describe("Chat Integration with Style Alignment", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("chatPrompt function integration", () => {
    test("includes style instructions when enabled", async () => {
      const mockStyleProfile = {
        id: 1,
        instructions: "Use formal legal language with clear structure.",
        is_active: true,
      };

      UserStyleProfile.getActiveProfile.mockResolvedValue(mockStyleProfile);

      const styleAlignment = {
        enabled: true,
        userId: 1,
      };

      const result = await chatPrompt({
        workspace: { id: 1, name: "Test Workspace" },
        message: "Test message",
        contextTexts: [],
        user: { id: 1 },
        styleAlignment,
      });

      expect(chatPrompt).toHaveBeenCalledWith({
        workspace: { id: 1, name: "Test Workspace" },
        message: "Test message",
        contextTexts: [],
        user: { id: 1 },
        styleAlignment,
      });
    });

    test("excludes style instructions when disabled", async () => {
      const styleAlignment = {
        enabled: false,
        userId: 1,
      };

      const result = await chatPrompt({
        workspace: { id: 1, name: "Test Workspace" },
        message: "Test message",
        contextTexts: [],
        user: { id: 1 },
        styleAlignment,
      });

      expect(chatPrompt).toHaveBeenCalledWith({
        workspace: { id: 1, name: "Test Workspace" },
        message: "Test message",
        contextTexts: [],
        user: { id: 1 },
        styleAlignment,
      });

      // Should not call getActiveProfile when disabled
      expect(UserStyleProfile.getActiveProfile).not.toHaveBeenCalled();
    });

    test("handles null style alignment", async () => {
      const result = await chatPrompt({
        workspace: { id: 1, name: "Test Workspace" },
        message: "Test message",
        contextTexts: [],
        user: { id: 1 },
        styleAlignment: null,
      });

      expect(chatPrompt).toHaveBeenCalledWith({
        workspace: { id: 1, name: "Test Workspace" },
        message: "Test message",
        contextTexts: [],
        user: { id: 1 },
        styleAlignment: null,
      });

      expect(UserStyleProfile.getActiveProfile).not.toHaveBeenCalled();
    });

    test("handles empty style instructions", async () => {
      const mockStyleProfile = {
        id: 1,
        instructions: "",
        is_active: true,
      };

      UserStyleProfile.getActiveProfile.mockResolvedValue(mockStyleProfile);

      const styleAlignment = {
        enabled: true,
        userId: 1,
      };

      const result = await chatPrompt({
        workspace: { id: 1, name: "Test Workspace" },
        message: "Test message",
        contextTexts: [],
        user: { id: 1 },
        styleAlignment,
      });

      expect(chatPrompt).toHaveBeenCalledWith({
        workspace: { id: 1, name: "Test Workspace" },
        message: "Test message",
        contextTexts: [],
        user: { id: 1 },
        styleAlignment,
      });
    });

    test("handles whitespace-only style instructions", async () => {
      const mockStyleProfile = {
        id: 1,
        instructions: "   \n\t   ",
        is_active: true,
      };

      UserStyleProfile.getActiveProfile.mockResolvedValue(mockStyleProfile);

      const styleAlignment = {
        enabled: true,
        userId: 1,
      };

      const result = await chatPrompt({
        workspace: { id: 1, name: "Test Workspace" },
        message: "Test message",
        contextTexts: [],
        user: { id: 1 },
        styleAlignment,
      });

      expect(chatPrompt).toHaveBeenCalledWith({
        workspace: { id: 1, name: "Test Workspace" },
        message: "Test message",
        contextTexts: [],
        user: { id: 1 },
        styleAlignment,
      });
    });
  });

  describe("streamChatWithWorkspace integration", () => {
    test("passes style alignment data to chat stream", async () => {
      const mockRequest = {};
      const mockResponse = {};
      const mockWorkspace = { id: 1, name: "Test Workspace" };
      const mockUser = { id: 1 };
      const mockThread = { id: 1 };
      const styleAlignment = {
        enabled: true,
        userId: 1,
      };

      await streamChatWithWorkspace(
        mockRequest,
        mockResponse,
        mockWorkspace,
        "Test message",
        "chat",
        mockUser,
        mockThread,
        [],
        null,
        false,
        false,
        "",
        null,
        "similarity",
        false,
        null,
        null,
        false,
        [],
        {},
        styleAlignment
      );

      expect(streamChatWithWorkspace).toHaveBeenCalledWith(
        mockRequest,
        mockResponse,
        mockWorkspace,
        "Test message",
        "chat",
        mockUser,
        mockThread,
        [],
        null,
        false,
        false,
        "",
        null,
        "similarity",
        false,
        null,
        null,
        false,
        [],
        {},
        styleAlignment
      );
    });

    test("handles disabled style alignment", async () => {
      const mockRequest = {};
      const mockResponse = {};
      const mockWorkspace = { id: 1, name: "Test Workspace" };
      const mockUser = { id: 1 };
      const mockThread = { id: 1 };
      const styleAlignment = {
        enabled: false,
        userId: 1,
      };

      await streamChatWithWorkspace(
        mockRequest,
        mockResponse,
        mockWorkspace,
        "Test message",
        "chat",
        mockUser,
        mockThread,
        [],
        null,
        false,
        false,
        "",
        null,
        "similarity",
        false,
        null,
        null,
        false,
        [],
        {},
        styleAlignment
      );

      expect(streamChatWithWorkspace).toHaveBeenCalledWith(
        mockRequest,
        mockResponse,
        mockWorkspace,
        "Test message",
        "chat",
        mockUser,
        mockThread,
        [],
        null,
        false,
        false,
        "",
        null,
        "similarity",
        false,
        null,
        null,
        false,
        [],
        {},
        styleAlignment
      );
    });

    test("handles missing style alignment data", async () => {
      const mockRequest = {};
      const mockResponse = {};
      const mockWorkspace = { id: 1, name: "Test Workspace" };
      const mockUser = { id: 1 };
      const mockThread = { id: 1 };

      await streamChatWithWorkspace(
        mockRequest,
        mockResponse,
        mockWorkspace,
        "Test message",
        "chat",
        mockUser,
        mockThread,
        [],
        null,
        false,
        false,
        "",
        null,
        "similarity",
        false,
        null,
        null,
        false,
        [],
        {},
        null
      );

      expect(streamChatWithWorkspace).toHaveBeenCalledWith(
        mockRequest,
        mockResponse,
        mockWorkspace,
        "Test message",
        "chat",
        mockUser,
        mockThread,
        [],
        null,
        false,
        false,
        "",
        null,
        "similarity",
        false,
        null,
        null,
        false,
        [],
        {},
        null
      );
    });
  });
});
