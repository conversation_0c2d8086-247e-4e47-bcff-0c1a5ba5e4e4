export default {
  // =========================
  // PRIVACY & DATA-HANDLING
  // =========================
  privacy: {
    title: "Personvern & databehandling",
    description:
      "Dette er din konfigurasjon for hvordan tilkoblede tredjepartsleverandører og vår plattform håndterer dataene dine.",
    llm: "LLM-valg",
    embedding: "Embedding-preferanse",
    vector: "Vektordatabasen",
    anonymous: "Anonym telemetri aktivert",
    "desc-event": "Alle hendelser registrerer ikke IP-adresse og inneholder",
    "desc-id": "ingen identifiserende",
    "desc-cont":
      "innhold, innstillinger, chatter eller annen ikke-bruksbasert informasjon. For å se listen over hendelses-tagger som samles inn, kan du se på",
    "desc-git": "Gith<PERSON> her",
    "desc-end":
      "Vi er dedikert til å bygge den beste løsningen for å integrere AI og dokumenter privat og sikkert. Hvis du bestemmer deg for å slå av telemetri, ber vi deg vurdere å sende oss tilbakemeldinger slik at vi kan fortsette å forbedre plattformen for deg.",
  },
  // =========================
  // LLM SELECTION PRIVACY
  // =========================
  "llm-selection-privacy": {
    openai: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for OpenAI",
      ],
    },
    azure: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Din tekst og embedding-tekst er ikke synlig for OpenAI eller Microsoft",
      ],
    },
    anthropic: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for Anthropic",
      ],
    },
    gemini: {
      description: [
        "Dine chatter er anonymisert og brukes i trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for Google",
      ],
    },
    lmstudio: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på serveren som kjører LMStudio",
      ],
    },
    localai: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på serveren som kjører LocalAI",
      ],
    },
    ollama: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på maskinen som kjører Ollama-modeller",
      ],
    },
    native: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på denne instansen",
      ],
    },
    togetherai: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for TogetherAI",
      ],
    },
    mistral: {
      description: [
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for Mistral",
      ],
    },
    huggingface: {
      description: [
        "Dine prompt og dokumenttekst brukt i svar er sendt til din HuggingFace-administrerte endepunkt",
      ],
    },
    perplexity: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for Perplexity AI",
      ],
    },
    openrouter: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for OpenRouter",
      ],
    },
    groq: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for Groq",
      ],
    },
    koboldcpp: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på serveren som kjører KoboldCPP",
      ],
    },
    textgenwebui: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på serveren som kjører Oobabooga Text Generation Web UI",
      ],
    },
    "generic-openai": {
      description: [
        "Data deles i henhold til vilkårene for den generiske endepunktleverandøren.",
      ],
    },
    cohere: {
      description: [
        "Data deles i henhold til vilkårene for cohere.com og ditt lokale personvern.",
      ],
    },
    litellm: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på serveren som kjører LiteLLM",
      ],
    },
  },

  // =========================
  // VECTOR DATABASE PRIVACY
  // =========================
  "vector-db-privacy": {
    chroma: {
      description: [
        "Dine vektorer og dokumenttekst lagres på din Chroma-instans",
        "Tilgang til din instans administreres av deg",
      ],
    },
    pinecone: {
      description: [
        "Dine vektorer og dokumenttekst lagres på Pinecones servere",
        "Tilgang til dine data administreres av Pinecone",
      ],
    },
    qdrant: {
      description: [
        "Dine vektorer og dokumenttekst lagres på din Qdrant-instans (skybasert eller selvhostet)",
      ],
    },
    weaviate: {
      description: [
        "Dine vektorer og dokumenttekst lagres på din Weaviate-instans (skybasert eller selvhostet)",
      ],
    },
    milvus: {
      description: [
        "Dine vektorer og dokumenttekst lagres på din Milvus-instans (skybasert eller selvhostet)",
      ],
    },
    zilliz: {
      description: [
        "Dine vektorer og dokumenttekst lagres på din Zilliz sky-klynge.",
      ],
    },
    astra: {
      description: [
        "Dine vektorer og dokumenttekst lagres på din skybaserte AstraDB-database.",
      ],
    },
    lancedb: {
      description: [
        "Dine vektorer og dokumenttekst lagres privat på denne instansen av plattformen",
      ],
    },
  },

  // =========================
  // EMBEDDING ENGINE PRIVACY
  // =========================
  "embedding-engine-privacy": {
    native: {
      description: [
        "Din dokumenttekst embedes privat på denne instansen av plattformen",
      ],
    },
    openai: {
      description: [
        "Din dokumenttekst sendes til OpenAI-servere",
        "Dokumentene dine brukes ikke til trening",
      ],
    },
    azure: {
      description: [
        "Din dokumenttekst sendes til din Microsoft Azure-tjeneste",
        "Dokumentene dine brukes ikke til trening",
      ],
    },
    localai: {
      description: [
        "Din dokumenttekst embedes privat på serveren som kjører LocalAI",
      ],
    },
    ollama: {
      description: [
        "Din dokumenttekst embedes privat på serveren som kjører Ollama",
      ],
    },
    lmstudio: {
      description: [
        "Din dokumenttekst embedes privat på serveren som kjører LMStudio",
      ],
    },
    cohere: {
      description: [
        "Data deles i henhold til vilkårene for cohere.com og ditt lokale personvern.",
      ],
    },
    voyageai: {
      description: [
        "Data sendt til Voyage AI sine servere deles i henhold til vilkårene for voyageai.com.",
      ],
    },
  },
};
