export default {
  // =========================
  // EMBEDDER (EMBEDDING PROVIDER NAMES)
  // =========================
  embeder: {
    allm: "Verwenden Sie den integrierten Einbettungsanbieter. Keine Einstellungen erforderlich!",
    openai:
      "Die Standardoption für die meisten nicht-kommerziellen Anwendungen.",
    azure:
      "Die Enterprise-Option von OpenAI, die auf Azure-Diensten gehostet wird.",
    localai:
      "Führen Sie Einbettungsmodelle lokal auf Ihrem eigenen Computer aus.",
    ollama:
      "Führen Sie Einbettungsmodelle lokal auf Ihrem eigenen Computer aus.",
    lmstudio:
      "Entdecken, herunterladen und in wenigen Klicks tausende leistungsstarke LLMs ausführen.",
    cohere: "Führen Sie leistungsstarke Einbettungsmodelle von Cohere aus.",
    voyageai:
      "Führen Sie leistungsstarke Einbettungsmodelle von Voyage AI aus.",
    "generic-openai": "Verwenden Sie ein generisches OpenAI-Einbettungsmodell.",
    "default.embedder": "Standard-Einbettungsanbieter",
    jina: "Jina AI's text-embedding models für mehrsprachige und hochleistungsfähige Einbettungen.",
    litellm: "Führen Sie leistungsstarke Einbettungsmodelle von LiteLLM aus.",
  },
  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Common strings
    "provider-logo": "{{provider}}-Logo",

    // LMStudio Embedding Options
    lmstudio: {
      "model-label": "LM Studio Einbettungsmodell",
      "max-chunk-length": "Maximale Chunk-Länge",
      "max-chunk-length-help":
        "Maximale Länge der Text-Chunks für die Einbettung.",
      "hide-endpoint": "Manuelle Endpunkt-Eingabe ausblenden",
      "show-endpoint": "Manuelle Endpunkt-Eingabe anzeigen",
      "base-url": "LM Studio Basis-URL",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help":
        "Geben Sie die URL ein, unter der LM Studio ausgeführt wird.",
      "auto-detect": "Automatisch erkennen",
      "loading-models": "--lade verfügbare Modelle--",
      "enter-url-first": "Geben Sie zuerst die LM Studio URL ein",
      "model-help":
        "Wählen Sie das LM Studio Modell für Einbettungen. Die Modelle werden geladen, nachdem eine gültige LM Studio URL eingegeben wurde.",
      "loaded-models": "Ihre geladenen Modelle",
    },
    // Ollama Embedding Options
    ollama: {
      "model-label": "Ollama Einbettungsmodell",
      "max-chunk-length": "Maximale Chunk-Länge",
      "max-chunk-length-help":
        "Maximale Länge der Text-Chunks für die Einbettung.",
      "hide-endpoint": "Manuelle Endpunkt-Eingabe ausblenden",
      "show-endpoint": "Manuelle Endpunkt-Eingabe anzeigen",
      "base-url": "Ollama Basis-URL",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help":
        "Geben Sie die URL ein, unter der Ollama ausgeführt wird.",
      "auto-detect": "Automatisch erkennen",
      "loading-models": "--lade verfügbare Modelle--",
      "enter-url-first": "Geben Sie zuerst die Ollama URL ein",
      "model-help":
        "Wählen Sie das Ollama Modell für Einbettungen. Die Modelle werden geladen, nachdem eine gültige Ollama URL eingegeben wurde.",
      "loaded-models": "Ihre geladenen Modelle",
    },
    // LiteLLM Embedding Options
    litellm: {
      "model-label": "Auswahl des Einbettungsmodells",
      "max-chunk-length": "Maximale Chunk-Länge",
      "max-chunk-length-help":
        "Maximale Länge der Text-Chunks für die Einbettung.",
      "api-key": "API-Schlüssel",
      optional: "optional",
      "api-key-placeholder": "sk-mysecretkey",
      "loading-models": "-- lade verfügbare Modelle --",
      "waiting-url": "-- warte auf URL --",
      "loaded-models": "Ihre geladenen Modelle",
      "model-tooltip": "Unterstützte Einbettungsmodelle anzeigen unter",
      "model-tooltip-link": "LiteLLM-Dokumentation",
      "model-tooltip-more":
        "für weitere Informationen zu den verfügbaren Modellen.",
    },
    // Cohere Embedding Options
    cohere: {
      "api-key": "Cohere API-Schlüssel",
      "api-key-placeholder": "Geben Sie Ihren Cohere API-Schlüssel ein",
      "model-label": "Modellauswahl",
      "available-models": "Verfügbare Einbettungsmodelle",
    },
    // Jina Embedding Options
    jina: {
      "api-key": "Jina API-Schlüssel",
      "api-key-format": "Jina API-Schlüssel muss mit 'jina_' beginnen",
      "api-key-placeholder": "Geben Sie Ihren Jina API-Schlüssel ein",
      "api-key-error": "API-Schlüssel muss mit 'jina_' beginnen",
      "model-label": "Modellauswahl",
      "available-models": "Verfügbare Einbettungsmodelle",
      "embedding-type": "Einbettungstyp",
      "available-types": "Verfügbare Einbettungstypen",
      dimensions: "Dimensionen",
      "available-dimensions": "Verfügbare Dimensionen",
      task: "Aufgabe",
      "available-tasks": "Verfügbare Aufgaben",
      "late-chunking": "Spätes Chunking",
      "late-chunking-help":
        "Aktivieren Sie das späte Chunking für die Dokumentenverarbeitung",
    },
    // LocalAI Embedding Options
    localai: {
      "model-label": "Name des Einbettungsmodells",
      "hide-endpoint": "Erweiterte Einstellungen ausblenden",
      "show-endpoint": "Erweiterte Einstellungen anzeigen",
      "base-url": "LocalAI Basis-URL",
      "base-url-placeholder": "http://localhost:8080/v1",
      "base-url-help":
        "Geben Sie die URL ein, unter der LocalAI ausgeführt wird.",
      "auto-detect": "Automatisch erkennen",
      "loading-models": "-- lade verfügbare Modelle --",
      "waiting-url": "-- warte auf URL --",
      "loaded-models": "Ihre geladenen Modelle",
    },
    // Generic OpenAI-Compatible Embedding Options
    generic: {
      "base-url": "Basis-URL",
      "base-url-placeholder": "https://api.openai.com/v1",
      "base-url-help":
        "Geben Sie die Basis-URL für Ihren OpenAI-kompatiblen API-Endpunkt ein.",
      "model-label": "Einbettungsmodell",
      "model-placeholder":
        "Geben Sie den Modellnamen ein (z.B. text-embedding-ada-002)",
      "model-help":
        "Geben Sie den Modellbezeichner zur Generierung von Einbettungen an.",
      "api-key": "API-Schlüssel",
      "api-key-placeholder": "sk-mysecretkey",
      "api-key-help":
        "Geben Sie Ihren API-Schlüssel zur Authentifizierung ein.",
    },
    // OpenAI Embedding Options
    openai: {
      "api-key": "OpenAI API-Schlüssel",
      "api-key-placeholder": "Geben Sie Ihren OpenAI API-Schlüssel ein",
      "model-label": "Modellauswahl",
      "available-models": "Verfügbare Einbettungsmodelle",
    },
    // VoyageAI Embedding Options
    voyageai: {
      "api-key": "VoyageAI API-Schlüssel",
      "api-key-placeholder": "Geben Sie Ihren VoyageAI API-Schlüssel ein",
      "model-label": "Modellauswahl",
      "available-models": "Verfügbare Einbettungsmodelle",
    },
    // Azure OpenAI Embedding Options
    azureai: {
      "service-endpoint": "Azure OpenAI Service-Endpunkt",
      "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
      "service-endpoint-help":
        "Geben Sie die URL Ihres Azure OpenAI Service-Endpunkts ein",
      "api-key": "Azure OpenAI API-Schlüssel",
      "api-key-placeholder": "Geben Sie Ihren Azure OpenAI API-Schlüssel ein",
      "api-key-help":
        "Geben Sie Ihren Azure OpenAI API-Schlüssel zur Authentifizierung ein",
      "deployment-name": "Bereitstellungsname des Einbettungsmodells",
      "deployment-name-placeholder":
        "Geben Sie den Bereitstellungsnamen Ihres Azure OpenAI Einbettungsmodells ein",
      "deployment-name-help":
        "Der Bereitstellungsname für Ihr Azure OpenAI Einbettungsmodell",
    },
    // Native Embedding Options
    native: {
      description:
        "Verwendung eines nativen Einbettungsanbieters für die Textverarbeitung",
    },
  },
  // =========================
  // JINA
  // =========================
  jina: {
    "api-key": "Jina API-Schlüssel",
    "api-key-placeholder": "Gib deinen Jina API-Schlüssel ein",
    "api-key-format": "Jina API-Schlüssel muss mit 'jina_' beginnen",
    "model-preference": "Modell-Voreinstellung",
  },

  // =========================
  // OLLAMA
  // =========================
  ollama: {
    "max-embedding-chunk-length": "Maximale Embedding-Chuck-Länge",
  },

  // =========================
  // VOYAGEAI EMBEDDING
  // =========================
  voyageai: {
    "api-key": "VoyageAI API-Schlüssel",
    "api-key-placeholder": "Gib deinen VoyageAI API-Schlüssel ein",
    "model-preference": "Modell-Voreinstellung",
  },
};
