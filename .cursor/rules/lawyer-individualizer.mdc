---
description:
globs:
alwaysApply: false
---
# Lawyer Individualizer Feature Guidelines

## Overview

The Lawyer Individualizer feature allows users to upload document samples for style analysis and generate personalized style instructions that are automatically applied to AI responses. This rule provides guidelines for working with and extending this feature.

## Core Components

### Frontend Components

- **StyleAlignmentModal**: Main UI component (`frontend/src/components/UserMenu/StyleAlignmentModal/index.jsx`)
- **userStore**: Zustand state management (`frontend/src/stores/userStore.js`)
- **HeaderWorkspace**: Integration point in user dropdown menu

### Backend Components

- **UserStyleProfile Model**: Database operations (`server/models/userStyleProfile.js`)
- **System Endpoint**: Style generation API (`server/endpoints/system.js`)
- **Chat Integration**: Style application in chat responses

### Database Schema

- **Table**: `user_style_profiles`
- **Relations**: Linked to users with CASCADE DELETE
- **Constraints**: 10,000 character limit on style instructions

## Development Guidelines

### Adding New Style Profile Features

When extending style profile functionality:

1. **Frontend Changes**:
   - Update `userStore.js` for new state management needs
   - Modify `StyleAlignmentModal` for UI changes
   - Add translation keys to all locale files
   - Update tests in `__tests__/StyleAlignmentModal.test.jsx`

2. **Backend Changes**:
   - Extend `UserStyleProfile` model for new database operations
   - Update API endpoints in `system.js` if needed
   - Add validation for new fields
   - Update tests in `server/tests/unit/models/userStyleProfile.test.js`

3. **Database Changes**:
   - Create migrations for schema changes
   - Update model validation rules
   - Ensure proper indexing for performance

### Customizing Style Generation

#### Prompt Customization

The style generation prompt is located in `server/endpoints/system.js` in the `/system/generate-style-profile` endpoint:

```javascript
const styleAnalysisPrompt = `You are a legal writing style analyzer...`;
```

**Options for customization**:

1. **Direct modification**: Edit the prompt directly in the code
2. **Environment variable**: Add `STYLE_ANALYSIS_PROMPT` environment variable support
3. **System settings**: Integrate with SystemSettings for admin configuration

#### LLM Configuration

Style generation uses the system's default LLM provider:
- **Temperature**: 0.3 (for consistent analysis)
- **Max Tokens**: 2000
- **Provider**: Uses `process.env.LLM_PROVIDER`

### Chat Integration Guidelines

When modifying chat functionality that might affect style alignment:

1. **Frontend**: Ensure `styleAlignment` data is included in chat requests
2. **Backend**: Preserve style instruction application in prompt construction
3. **Testing**: Verify style alignment works in integration tests

### State Management Best Practices

#### Zustand Store Usage

- Use selector hooks for performance: `useStyleProfiles()`, `useActiveStyleProfile()`
- Keep actions focused and atomic
- Persist relevant state using the `persist` middleware
- Validate state changes to prevent corruption

#### Example Selector Hook

```javascript
export const useActiveStyleProfile = () =>
  useUserStore((state) =>
    state.styleProfiles.find(p => p.id === state.activeStyleProfileId)
  );
```

### API Integration Patterns

#### Error Handling

Always provide user-friendly error messages:

```javascript
try {
  const result = await System.generateStyleProfile(documentContent);
  if (result.success) {
    // Handle success
  } else {
    showToast(result.error || t("user-menu.style-generation-failed"), "error");
  }
} catch (error) {
  showToast(t("user-menu.style-generation-failed"), "error");
}
```

#### Loading States

Provide visual feedback for async operations:

```javascript
const [isGenerating, setIsGenerating] = useState(false);

const handleGeneration = async () => {
  setIsGenerating(true);
  try {
    // API call
  } finally {
    setIsGenerating(false);
  }
};
```

## Testing Requirements

### Frontend Testing

- **Component Tests**: Test all user interactions and state changes
- **Integration Tests**: Test API integration and error handling
- **Accessibility Tests**: Ensure proper ARIA attributes and keyboard navigation

### Backend Testing

- **Unit Tests**: Test model methods and API endpoints
- **Integration Tests**: Test end-to-end chat functionality with style alignment
- **Database Tests**: Test CRUD operations and constraints

### Test Data

Use consistent test data across test suites:

```javascript
const mockStyleProfile = {
  id: "1",
  name: "Legal Brief Style",
  instructions: "Use formal legal language with clear structure.",
  isActive: true,
  createdAt: "2024-01-01T00:00:00Z",
};
```

## Security Considerations

### User Ownership Validation

Always validate user ownership for style profile operations:

```javascript
// In backend model methods
const profile = await prisma.userStyleProfile.findFirst({
  where: { id: profileId, user_id: userId }
});

if (!profile) {
  throw new Error("Profile not found or access denied");
}
```

### Input Validation

- Validate file types (DOCX only)
- Sanitize style instructions
- Enforce character limits
- Validate profile names

### Data Privacy

- Document content is processed temporarily and not stored
- Style instructions are user-specific and private
- LLM processing follows existing privacy policies

## Performance Guidelines

### Frontend Optimization

- Use React.memo for expensive components
- Implement proper loading states
- Debounce API calls
- Use efficient Zustand selectors

### Backend Optimization

- Index database queries on `user_id` and `is_active`
- Limit style instruction length to prevent bloat
- Cache LLM responses if appropriate
- Monitor API response times

### Database Performance

```sql
-- Ensure proper indexing
CREATE INDEX idx_user_style_profiles_user_id ON user_style_profiles(user_id);
CREATE INDEX idx_user_style_profiles_active ON user_style_profiles(user_id, is_active);
```

## Internationalization

### Translation Key Management

All user-facing text must use translation keys:

```javascript
// Add to modalRelatedKeys.js for all languages
"user-menu": {
  "style-alignment": "Personal Style Alignment",
  "style-generate": "Style Setting Generator",
  // ... other keys
}
```

### Supported Languages

Maintain translations for all system languages:
- English (en), French (fr), Swedish (sv), German (de)
- Norwegian (no), Polish (pl), Kinyarwanda (rw)

## Common Patterns

### Modal Component Structure

```jsx
export default function StyleAlignmentModal({ isOpen, onClose }) {
  // Local state for UI
  const [localState, setLocalState] = useState(initialValue);

  // Zustand store hooks
  const storeData = useStoreSelector();
  const storeAction = useStoreAction();

  // Effects and handlers
  useEffect(() => {
    // Component effects
  }, [dependencies]);

  const handleAction = useCallback(() => {
    // Action handlers
  }, [dependencies]);

  // Render
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      {/* Modal content */}
    </Modal>
  );
}
```

### API Integration Pattern

```javascript
const apiCall = async (data) => {
  try {
    const response = await fetch(endpoint, {
      method: "POST",
      headers: baseHeaders(),
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (result.success) {
      return result;
    } else {
      throw new Error(result.error || "Operation failed");
    }
  } catch (error) {
    console.error("API call failed:", error);
    throw error;
  }
};
```

## Troubleshooting

### Common Issues

1. **Style Not Applied**: Check if style alignment is enabled and active profile exists
2. **Generation Fails**: Verify LLM provider configuration and document content
3. **Upload Issues**: Ensure collector service is running and file is DOCX format

### Debug Tools

```javascript
// Enable debug logging
localStorage.setItem('debug', 'style-alignment');

// Check store state
console.log(useUserStore.getState());

// Monitor API calls
// Check Network tab in browser dev tools
```

## Future Development

### Planned Enhancements

- Multiple document analysis
- Style comparison tools
- Team style profiles
- Advanced analytics
- Export/import functionality

### Extension Points

- Custom prompt templates
- Additional file format support
- Style validation metrics
- Integration with document templates

## Documentation

### Required Documentation Updates

When making changes to the Lawyer Individualizer feature:

1. Update `server/docs/lawyer_individualizer.md` for backend changes
2. Update `frontend/docs/lawyer_individualizer.md` for frontend changes
3. Update this cursor rule for new patterns or guidelines
4. Update API documentation if endpoints change

### Code Comments

- Document complex style generation logic
- Explain non-obvious state management decisions
- Comment on performance optimizations
- Document security considerations

## Conclusion

The Lawyer Individualizer feature provides a robust foundation for personalized writing style management. Follow these guidelines to maintain code quality, ensure proper testing, and provide a consistent user experience when extending or modifying the feature.
