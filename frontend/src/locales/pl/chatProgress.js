export default {
  // =========================
  // PROGRESS
  // =========================
  chatProgress: {
    title: "Przetwarzanie",
    processing: "Przetwarzanie...",
    step: "Krok",
    timeLeft: "Pozostały czas",
    details: "Szczegóły",
    abort: "Przerwij",
    modalTitle: "Szczegóły postępu",
    "close-msg": "<PERSON>zy na pewno chcesz anulować proces?",
    noThreadSelected: "Nie wybrano wątku",
    noActiveProgress: "Brak aktywnego postępu",
    of: "z",
    started: "Roz<PERSON>częto",
    error: {
      title: "W<PERSON><PERSON><PERSON><PERSON>ł błąd podczas przetwarzania Twojego żądania.",
      description:
        "Spróbuj ponownie lub skontaktuj się z pomocą techniczną, jeśli problem będzie się powtarzał.",
      retry: "Spróbuj ponownie",
      dismiss: "Zamknij",
      showDetails: "Pokaż szczegóły techniczne",
      hideDetails: "Ukryj szczegóły techniczne",
    },
    cancelled: "Proces został anulowany.",
    types: {
      main: {
        step1: {
          label: "Generowanie listy sekcji",
          desc: "Używanie głównego dokumentu do utworzenia wstępnej struktury.",
        },
        step2: {
          label: "Przetwarzanie dokumentów",
          desc: "Generowanie opisów i sprawdzanie trafności.",
        },
        step3: {
          label: "Mapowanie dokumentów do sekcji",
          desc: "Przypisywanie odpowiednich dokumentów do każdej sekcji.",
        },
        step4: {
          label: "Identyfikowanie problemów prawnych",
          desc: "Wyodrębnianie kluczowych problemów prawnych dla każdej sekcji.",
        },
        step5: {
          label: "Generowanie not prawnych",
          desc: "Tworzenie memorandów prawnych dla zidentyfikowanych problemów.",
        },
        step6: {
          label: "Tworzenie sekcji",
          desc: "Komponowanie treści dla każdej pojedynczej sekcji.",
        },
        step7: {
          label: "Łączenie i finalizowanie dokumentu",
          desc: "Składanie sekcji w końcowy dokument.",
        },
      },
      noMain: {
        step1: {
          label: "Przetwarzanie dokumentów",
          desc: "Generowanie opisów dla wszystkich przesłanych plików.",
        },
        step2: {
          label: "Generowanie listy sekcji",
          desc: "Tworzenie uporządkowanej listy sekcji z podsumowań dokumentów.",
        },
        step3: {
          label: "Finalizowanie mapowania dokumentów",
          desc: "Potwierdzanie trafności dokumentów dla każdej planowanej sekcji.",
        },
        step4: {
          label: "Identyfikowanie problemów prawnych",
          desc: "Wyodrębnianie kluczowych problemów prawnych dla każdej sekcji.",
        },
        step5: {
          label: "Generowanie not prawnych",
          desc: "Tworzenie memorandów prawnych dla zidentyfikowanych problemów.",
        },
        step6: {
          label: "Tworzenie sekcji",
          desc: "Komponowanie treści dla każdej pojedynczej sekcji.",
        },
        step7: {
          label: "Łączenie i finalizowanie dokumentu",
          desc: "Składanie wszystkich sekcji w końcowy dokument prawny.",
        },
      },
      referenceFiles: {
        step1: {
          label: "Przetwarzanie plików referencyjnych",
          desc: "Przetwarzanie plików referencyjnych",
        },
        step2: {
          label: "Przetwarzanie plików przeglądowych",
          desc: "Przetwarzanie plików przeglądowych",
        },
        step3: {
          label: "Generowanie listy sekcji",
          desc: "Generowanie listy sekcji",
        },
        step4: {
          label: "Pisanie sekcji",
          desc: "Pisanie sekcji",
        },
        step5: {
          label: "Generowanie raportu",
          desc: "Generowanie raportu",
        },
      },
      documentDrafting: {
        step1: {
          label: "Przygotowywanie dokumentów",
          desc: "Zbieranie i przygotowywanie wszystkich odpowiednich dokumentów dla procesu tworzenia.",
        },
        step2: {
          label: "Analizowanie treści",
          desc: "Analizowanie treści dokumentów i identyfikowanie kluczowych problemów prawnych i klauzul.",
        },
        step3: {
          label: "Generowanie szkicu",
          desc: "Komponowanie końcowego szkicu dokumentu na podstawie analizy.",
        },
      },
    },
  },

  // =========================
  // PROGRESS TYPES
  // =========================
  ddProgress: {
    title: "Tworzenie Dokumentu",
  },
  cdbProgress: {
    title: "Złożony Generator Dokumentów",
    general: {
      placeholderSubTask: "Przetwarzanie elementu {{index}}...",
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Potwierdź przerwanie",
    confirm_abort_description: "Czy na pewno chcesz anulować proces?",
    keep_running: "Kontynuuj działanie",
    abort_run: "Przerwij proces",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Postęp generowania odpowiedzi",
    description:
      "Wyświetla postęp zadań w czasie rzeczywistym w celu ukończenia promptu, w zależności od powiązania z innymi obszarami roboczymi i rozmiaru plików. Modal zamknie się automatycznie po ukończeniu wszystkich kroków.",
    step_fetching_memos: "Pobieranie danych prawnych na aktualne tematy",
    step_processing_chunks: "Przetwarzanie przesłanych dokumentów",
    step_combining_responses: "Finalizacja odpowiedzi",
    sub_step_chunk_label: "Przetwarzanie grupy dokumentów {{index}}",
    sub_step_memo_label: "Pobrano dane prawne z {{workspaceSlug}}",
    placeholder_sub_task: "Zadanie w kolejce",
    desc_fetching_memos:
      "Pobieranie odpowiednich informacji prawnych z powiązanych obszarów roboczych",
    desc_processing_chunks:
      "Analizowanie i wyodrębnianie informacji z grup dokumentów",
    desc_combining_responses: "Synteza informacji w kompleksową odpowiedź",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Zadanie prawne działa w tle.",
    dd: "Tworzenie dokumentu kontynuowane w tle.",
    reopen: "Otwórz okno statusu",
  },
};
