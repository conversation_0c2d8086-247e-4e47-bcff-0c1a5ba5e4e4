const { TokenManager } = require("../../helpers/tiktoken");
const { TokenTracker } = require("./tokenTracker");

/**
 * Context Window Manager for handling LLM token limits and iterative processing
 *
 * This utility provides:
 * - Token-aware content chunking
 * - Budget allocation for different content types
 * - Iterative processing strategies
 * - Real-time token tracking with TokenTracker integration
 */
class ContextWindowManager {
  constructor(LLMConnector, options = {}) {
    this.LLMConnector = LLMConnector;
    this.tokenManager = new TokenManager(LLMConnector.model);
    this.options = {
      defaultOverlapTokens: 100,
      maxIterations: 10,
      reservedOutputTokens: 4000,
      enableTokenTracking: true,
      ...options,
    };

    // Initialize TokenTracker if enabled
    if (this.options.enableTokenTracking) {
      this.tokenTracker = new TokenTracker(LLMConnector, {
        enableDetailedTracking: true,
        trackContentTypes: true,
        logTokenUsage: options.logTokenUsage || false,
      });
    }

    // Legacy metrics for backward compatibility
    this.metrics = {
      totalTokensProcessed: 0,
      chunksCreated: 0,
      iterationsCompleted: 0,
    };
  }

  /**
   * Get the effective context window size for content processing
   * @param {number} reservedTokens - Tokens to reserve for output
   * @returns {number} Available tokens for input content
   */
  getAvailableContextWindow(reservedTokens = null) {
    const contextWindowLimit = this.LLMConnector.promptWindowLimit();
    const reserved = reservedTokens || this.options.reservedOutputTokens;
    return Math.floor(contextWindowLimit * 0.7) - reserved;
  }

  /**
   * Calculate token budget allocation for different content types
   * @param {Object} params
   * @param {string} params.systemPrompt - System prompt text
   * @param {string} params.userPromptTemplate - User prompt template
   * @param {number} params.reservedTokens - Tokens reserved for output
   * @returns {Object} Token budget breakdown
   */
  calculateTokenBudget({
    systemPrompt,
    userPromptTemplate,
    reservedTokens = null,
  }) {
    const availableTokens = this.getAvailableContextWindow(reservedTokens);

    const systemTokens = this.tokenManager.countFromString(systemPrompt || "");
    const userPromptOverhead = this.tokenManager.countFromString(
      userPromptTemplate || ""
    );

    const remainingTokens = availableTokens - systemTokens - userPromptOverhead;

    return {
      total: availableTokens,
      system: systemTokens,
      userPromptOverhead,
      availableForContent: Math.max(0, remainingTokens),
      reserved: reservedTokens || this.options.reservedOutputTokens,
    };
  }

  /**
   * Chunk content into token-aware pieces with optional overlap
   * @param {string} content - Content to chunk
   * @param {Object} options - Chunking options
   * @param {number} options.maxTokensPerChunk - Maximum tokens per chunk
   * @param {number} options.overlapTokens - Tokens to overlap between chunks
   * @param {string} options.strategy - Chunking strategy ('token' or 'semantic')
   * @returns {Array} Array of content chunks with metadata
   */
  chunkContent(content, options = {}) {
    const {
      maxTokensPerChunk = 8000,
      overlapTokens = this.options.defaultOverlapTokens,
      strategy = "token",
    } = options;

    if (!content || typeof content !== "string") {
      return [];
    }

    const totalTokens = this.tokenManager.countFromString(content);

    // If content fits in a single chunk, return as-is
    if (totalTokens <= maxTokensPerChunk) {
      this.metrics.chunksCreated += 1;
      return [
        {
          content,
          tokens: totalTokens,
          chunkIndex: 0,
          totalChunks: 1,
          startToken: 0,
          endToken: totalTokens,
        },
      ];
    }

    // Use token-based chunking for now (semantic chunking can be added later)
    return this._tokenBasedChunking(content, maxTokensPerChunk, overlapTokens);
  }

  /**
   * Token-based chunking implementation
   * @private
   */
  _tokenBasedChunking(content, maxTokensPerChunk, overlapTokens) {
    const tokens = this.tokenManager.tokensFromString(content);
    const chunks = [];
    let chunkIndex = 0;

    const chunkSize = Math.max(100, maxTokensPerChunk);
    const overlap = Math.min(overlapTokens, Math.floor(chunkSize * 0.2));

    for (let i = 0; i < tokens.length; i += chunkSize - overlap) {
      const endIdx = Math.min(i + chunkSize, tokens.length);
      const chunkTokens = tokens.slice(i, endIdx);
      const chunkContent = this.tokenManager.bytesFromTokens(chunkTokens);

      chunks.push({
        content: chunkContent,
        tokens: chunkTokens.length,
        chunkIndex,
        totalChunks: Math.ceil(tokens.length / (chunkSize - overlap)),
        startToken: i,
        endToken: endIdx,
      });

      chunkIndex++;
      this.metrics.chunksCreated += 1;
    }

    return chunks;
  }

  /**
   * Process content iteratively when it exceeds context window limits
   * @param {Object} params
   * @param {Function} params.processor - Function to process each iteration
   * @param {Array} params.documents - Documents to process
   * @param {Array} params.memos - Memos to process
   * @param {Object} params.budget - Token budget from calculateTokenBudget
   * @param {Object} params.context - Additional context data
   * @returns {Object} Processing result with iterations and final content
   */
  async processIteratively({
    processor,
    documents = [],
    memos = [],
    budget,
    context = {},
  }) {
    const processedDocuments = new Set();
    const processedMemos = new Set();
    let iterations = [];
    let currentResult = "";
    let iteration = 0;

    // Track iterative processing stage
    const stageTracker = this.tokenTracker
      ? this.tokenTracker.startStage("iterativeProcessing")
      : null;

    // Track budget allocation
    if (this.tokenTracker) {
      this.tokenTracker.trackBudget(budget);
    }

    while (
      (processedDocuments.size < documents.length ||
        processedMemos.size < memos.length ||
        iteration === 0) &&
      iteration < this.options.maxIterations
    ) {
      iteration++;

      const iterationData = this._prepareIterationData({
        documents,
        memos,
        processedDocuments,
        processedMemos,
        budget,
        currentResult,
        iteration,
      });

      if (iteration > 1 && !iterationData.hasNewContent) {
        console.log(
          `[ContextWindowManager] No new content for iteration ${iteration}, stopping`
        );
        break;
      }

      try {
        // Track content tokens for this iteration
        if (this.tokenTracker) {
          iterationData.documents.forEach((doc) => {
            this.tokenTracker.trackContentTokens(
              doc.content,
              "documents",
              doc.name
            );
          });
          iterationData.memos.forEach((memo) => {
            this.tokenTracker.trackContentTokens(
              memo.content,
              "memos",
              memo.issue
            );
          });
          if (currentResult) {
            this.tokenTracker.trackContentTokens(
              currentResult,
              "previousResults",
              `iteration-${iteration - 1}`
            );
          }
        }

        const result = await processor({
          ...iterationData,
          iteration,
          context,
          previousResult: currentResult,
          tokenTracker: this.tokenTracker, // Pass token tracker to processor
        });

        currentResult = result.content || result;

        // Track iteration metrics
        if (this.tokenTracker) {
          this.tokenTracker.trackIteration(
            iteration,
            iterationData.documents.length,
            iterationData.memos.length,
            iterationData.tokenCount
          );

          // Track LLM response if available
          if (result.llmResponse) {
            this.tokenTracker.trackLLMResponse(
              result.llmResponse,
              "iterativeProcessing",
              `iteration-${iteration}`
            );
          }
        }

        iterations.push({
          iteration,
          documentsProcessed: iterationData.documents.length,
          memosProcessed: iterationData.memos.length,
          tokensUsed: iterationData.tokenCount,
          result: result,
        });

        this.metrics.iterationsCompleted++;
        if (stageTracker) {
          stageTracker.addTokens(
            iterationData.tokenCount,
            `iteration-${iteration}`
          );
        }
      } catch (error) {
        console.error(
          `[ContextWindowManager] Error in iteration ${iteration}:`,
          error
        );

        // Track error
        if (this.tokenTracker) {
          this.tokenTracker.addError(error.message, "iterativeProcessing");
        }

        iterations.push({
          iteration,
          error: error.message,
          documentsProcessed: 0,
          memosProcessed: 0,
          tokensUsed: 0,
        });
        break;
      }
    }

    // Finish stage tracking
    if (stageTracker) {
      stageTracker.finish();
    }

    const result = {
      finalContent: currentResult,
      iterations,
      totalIterations: iteration,
      metrics: { ...this.metrics },
    };

    // Add comprehensive token tracking metrics
    if (this.tokenTracker) {
      result.tokenMetrics = this.tokenTracker.getMetrics();
      result.tokenReport = this.tokenTracker.generateReport();
    }

    return result;
  }

  /**
   * Prepare data for a single iteration
   * @private
   */
  _prepareIterationData({
    documents,
    memos,
    processedDocuments,
    processedMemos,
    budget,
    currentResult,
    iteration,
  }) {
    let availableTokens = budget.availableForContent;
    let currentTokenCount = 0;
    let hasNewContent = false;

    // Account for current result tokens in refinement iterations
    if (iteration > 1 && currentResult) {
      currentTokenCount += this.tokenManager.countFromString(currentResult);
    }

    const iterationDocuments = [];
    const iterationMemos = [];

    // Add documents within token budget
    for (const doc of documents) {
      if (processedDocuments.has(doc.name)) {
        continue;
      }

      let docContent = doc.content;
      let docTokens =
        doc.tokens || this.tokenManager.countFromString(docContent);

      // For refinement iterations, use snippets to fit more content
      if (iteration > 1) {
        const maxSnippetTokens = Math.min(docTokens, 500);
        if (docTokens > maxSnippetTokens) {
          const tokens = this.tokenManager.tokensFromString(docContent);
          docContent = this.tokenManager.bytesFromTokens(
            tokens.slice(0, maxSnippetTokens)
          );
          if (tokens.length > maxSnippetTokens) docContent += "...";
          docTokens = this.tokenManager.countFromString(docContent);
        }
      }

      if (currentTokenCount + docTokens <= availableTokens) {
        iterationDocuments.push({
          ...doc,
          content: docContent,
          tokens: docTokens,
        });
        currentTokenCount += docTokens;
        processedDocuments.add(doc.name);
        hasNewContent = true;
      }
    }

    // Add memos within remaining token budget
    for (const memo of memos) {
      if (processedMemos.has(memo.issue)) {
        continue;
      }

      const memoTokens =
        memo.tokens || this.tokenManager.countFromString(memo.content);

      if (currentTokenCount + memoTokens <= availableTokens) {
        iterationMemos.push(memo);
        currentTokenCount += memoTokens;
        processedMemos.add(memo.issue);
        hasNewContent = true;
      }
    }

    return {
      documents: iterationDocuments,
      memos: iterationMemos,
      tokenCount: currentTokenCount,
      hasNewContent,
    };
  }

  /**
   * Get processing metrics (legacy format for backward compatibility)
   * @returns {Object} Current metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Get comprehensive token metrics from TokenTracker
   * @returns {Object} Detailed token metrics
   */
  getTokenMetrics() {
    return this.tokenTracker ? this.tokenTracker.getMetrics() : null;
  }

  /**
   * Get token tracker instance
   * @returns {TokenTracker|null} TokenTracker instance or null if disabled
   */
  getTokenTracker() {
    return this.tokenTracker || null;
  }

  /**
   * Generate comprehensive token usage report
   * @returns {Object} Detailed token usage report
   */
  generateTokenReport() {
    return this.tokenTracker ? this.tokenTracker.generateReport() : null;
  }

  /**
   * Reset metrics (both legacy and TokenTracker)
   */
  resetMetrics() {
    this.metrics = {
      totalTokensProcessed: 0,
      chunksCreated: 0,
      iterationsCompleted: 0,
    };

    if (this.tokenTracker) {
      this.tokenTracker.resetMetrics();
    }
  }
}

module.exports = {
  ContextWindowManager,
};
