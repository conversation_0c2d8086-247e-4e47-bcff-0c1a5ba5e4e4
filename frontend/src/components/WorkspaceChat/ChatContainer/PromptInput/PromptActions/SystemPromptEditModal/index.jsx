import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslation } from "react-i18next";
import { TbBooks } from "react-icons/tb";
import Modal from "@/components/ui/Modal";
import Label from "@/components/ui/Label";
import Textarea from "@/components/ui/Textarea";
import FormItem from "@/components/ui/FormItem";
import { Button } from "@/components/Button";
import showToast from "@/utils/toast";
import User from "@/models/user";
import useUser from "@/hooks/useUser";
import PromptLibraryModal from "./PromptLibraryModal";

const formSchema = z.object({
  customPrompt: z
    .string()
    .max(10000, "Custom system prompt cannot exceed 10,000 characters")
    .optional(),
});

export default function SystemPromptEditModal({ isOpen, onClose, workspace }) {
  const { t } = useTranslation();
  const { user, setUser } = useUser();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentPrompt, setCurrentPrompt] = useState("");
  const [defaultPrompt, setDefaultPrompt] = useState("");
  const [workspacePrompt, setWorkspacePrompt] = useState("");
  const [userCustomPrompt, setUserCustomPrompt] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [showLibraryModal, setShowLibraryModal] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      customPrompt: "",
    },
  });

  // Watch the current form value to enable/disable save button
  const currentFormValue = watch("customPrompt");

  // Check if the current form value differs from the saved custom prompt
  const hasChanges = currentFormValue !== (userCustomPrompt || "");

  // Fetch current and default prompts when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchPrompts();
    }
  }, [isOpen, workspace]);

  const fetchPrompts = async () => {
    setIsLoading(true);
    try {
      const response = await User.getCustomSystemPrompt();
      if (response.success) {
        setDefaultPrompt(response.defaultPrompt || "");
        setUserCustomPrompt(response.customPrompt || "");

        // Set workspace prompt from the workspace prop
        const wsPrompt = workspace?.openAiPrompt || "";
        setWorkspacePrompt(wsPrompt);

        // The current prompt display should show what would be used as the base prompt
        // (i.e., what would be used if the user didn't have a custom prompt)
        const basePrompt =
          wsPrompt && wsPrompt !== response.defaultPrompt
            ? wsPrompt
            : response.defaultPrompt;
        setCurrentPrompt(basePrompt);

        setValue("customPrompt", response.customPrompt || "");
      } else {
        showToast(t("system-prompt-edit.error-loading"), "error");
      }
    } catch (error) {
      console.error("Error fetching prompts:", error);
      showToast(t("system-prompt-edit.error-loading"), "error");
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      const response = await User.setCustomSystemPrompt(data.customPrompt);
      if (response.success) {
        showToast(t("system-prompt-edit.success-message"), "success");

        // Update user context
        setUser({
          ...user,
          custom_system_prompt: data.customPrompt || null,
        });

        // Update local state
        setUserCustomPrompt(data.customPrompt || "");

        reset();
        onClose();
      } else {
        showToast(
          response.error || t("system-prompt-edit.error-message"),
          "error"
        );
      }
    } catch (error) {
      console.error("Error saving custom prompt:", error);
      showToast(t("system-prompt-edit.error-message"), "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClear = async () => {
    setIsSubmitting(true);
    try {
      const response = await User.clearCustomSystemPrompt();
      if (response.success) {
        showToast(t("system-prompt-edit.clear-success"), "success");

        // Update user context
        setUser({
          ...user,
          custom_system_prompt: null,
        });

        setValue("customPrompt", "");
        setUserCustomPrompt("");
      } else {
        showToast(
          response.error || t("system-prompt-edit.error-message"),
          "error"
        );
      }
    } catch (error) {
      console.error("Error clearing custom prompt:", error);
      showToast(t("system-prompt-edit.error-message"), "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    reset();
    onClose();
  };

  const handleLibraryOpen = () => {
    setShowLibraryModal(true);
  };

  const handleLibraryClose = () => {
    setShowLibraryModal(false);
  };

  const handleApplyPrompt = (promptText) => {
    setValue("customPrompt", promptText);
  };

  if (!isOpen) return null;

  return (
    <Modal
      title={t("system-prompt-edit.modal-title")}
      isOpen={isOpen}
      onClose={handleCancel}
      className="w-[800px] max-w-[90vw]"
    >
      <div className="max-h-[70vh] overflow-y-auto px-4">
        <div className="space-y-6">
          {/* Modal description */}
          <p className="text-sm text-muted-foreground">
            {t("system-prompt-edit.modal-description")}
          </p>

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <>
              {/* Current Base Prompt Display */}
              <div>
                <Label htmlFor="current-prompt">
                  {userCustomPrompt
                    ? t(
                        "system-prompt-edit.base-prompt-label",
                        "Base System Prompt (without your customization)"
                      )
                    : t("system-prompt-edit.current-prompt-label")}
                </Label>
                <div className="mt-2 p-3 bg-secondary rounded border min-h-[100px] max-h-[200px] overflow-y-auto">
                  <p className="text-sm whitespace-pre-wrap">{currentPrompt}</p>
                </div>
                <div className="text-xs text-muted-foreground mt-1 space-y-1">
                  {userCustomPrompt && (
                    <p className="text-blue-600 font-medium">
                      {t(
                        "system-prompt-edit.your-custom-overrides",
                        "Your custom prompt below overrides this base prompt"
                      )}
                    </p>
                  )}
                  {!userCustomPrompt &&
                    workspacePrompt &&
                    workspacePrompt !== defaultPrompt && (
                      <p className="text-green-600 font-medium">
                        {t(
                          "system-prompt-edit.using-workspace-prompt",
                          "Using workspace prompt"
                        )}
                      </p>
                    )}
                  {!userCustomPrompt &&
                    (!workspacePrompt || workspacePrompt === defaultPrompt) && (
                      <p className="text-gray-600">
                        {t(
                          "system-prompt-edit.using-default-prompt",
                          "Using system default prompt"
                        )}
                      </p>
                    )}
                </div>
              </div>

              {/* Custom Prompt Form */}
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <FormItem>
                  <Label htmlFor="customPrompt">
                    {t("system-prompt-edit.custom-prompt-label")}
                  </Label>
                  <Textarea
                    id="customPrompt"
                    {...register("customPrompt")}
                    placeholder={t(
                      "system-prompt-edit.custom-prompt-placeholder"
                    )}
                    rows={8}
                    className="resize-none"
                  />
                  {errors.customPrompt && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.customPrompt.message}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground mt-1">
                    {t("system-prompt-edit.character-limit")}
                  </p>
                </FormItem>

                {/* Action Buttons */}
                <div className="flex items-center justify-between pt-4">
                  <div className="flex gap-2">
                    <Button
                      type="submit"
                      disabled={isSubmitting || !hasChanges}
                      size="sm"
                    >
                      {isSubmitting
                        ? t("common.saving")
                        : t("system-prompt-edit.save-button")}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancel}
                      disabled={isSubmitting}
                      size="sm"
                    >
                      {t("system-prompt-edit.cancel-button")}
                    </Button>
                  </div>

                  <div className="flex gap-2">
                    {userCustomPrompt && (
                      <Button
                        type="button"
                        variant="destructive"
                        onClick={handleClear}
                        disabled={isSubmitting}
                        size="sm"
                      >
                        {isSubmitting
                          ? t("common.clearing")
                          : t("system-prompt-edit.clear-button")}
                      </Button>
                    )}

                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleLibraryOpen}
                      disabled={isSubmitting}
                      size="sm"
                    >
                      <TbBooks className="mr-2 h-4 w-4" />
                      {t("system-prompt-edit.library-button")}
                    </Button>
                  </div>
                </div>
              </form>
            </>
          )}
        </div>
      </div>

      {/* Prompt Library Modal */}
      <PromptLibraryModal
        isOpen={showLibraryModal}
        onClose={handleLibraryClose}
        currentPrompt={currentFormValue || ""}
        onApplyPrompt={handleApplyPrompt}
      />
    </Modal>
  );
}
