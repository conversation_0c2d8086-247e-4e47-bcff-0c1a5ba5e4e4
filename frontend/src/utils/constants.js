export const API_BASE = import.meta.env.VITE_API_BASE || "/api";

export const AUTH_USER = "istlegal_user";
export const AUTH_TOKEN = "istlegal_authToken";
export const AUTH_TIMESTAMP = "istlegal_authTimestamp";
export const COMPLETE_QUESTIONNAIRE = "istlegal_completed_questionnaire";
export const APPEARANCE_SETTINGS = "istlegal_appearance_settings";

export const USER_BACKGROUND_COLOR = "bg-historical-msg-user";
export const AI_BACKGROUND_COLOR = "bg-historical-msg-system";

export const MODULE_LEGAL_QA = "legal-qa";
export const MODULE_DOCUMENT_DRAFTING = "document-drafting";

export const OLLAMA_COMMON_URLS = [
  "http://127.0.0.1:11434",
  "http://host.docker.internal:11434",
  "http://**********:11434",
];

export const LMSTUDIO_COMMON_URLS = [
  "http://localhost:1234/v1",
  "http://127.0.0.1:1234/v1",
  "http://host.docker.internal:1234/v1",
  "http://**********:1234/v1",
];

export const KOBOLDCPP_COMMON_URLS = [
  "http://127.0.0.1:5000/v1",
  "http://localhost:5000/v1",
  "http://host.docker.internal:5000/v1",
  "http://**********:5000/v1",
];

export const LOCALAI_COMMON_URLS = [
  "http://127.0.0.1:8080/v1",
  "http://localhost:8080/v1",
  "http://host.docker.internal:8080/v1",
  "http://**********:8080/v1",
];

export function fullApiUrl() {
  if (API_BASE !== "/api") return API_BASE;
  return `${window.location.origin}/api`;
}

export const POPUP_BROWSER_EXTENSION_EVENT = "NEW_BROWSER_EXTENSION_CONNECTION";

export function isQura() {
  return window.localStorage.getItem("qura") === "true";
}

export function isCitationEnabled() {
  return window.localStorage.getItem("citationEnabled") === "true";
}
export const getActiveReference = () => {
  // This function will be updated to use the store in components
  // For now, keep backward compatibility
  return localStorage.getItem("activeReference");
};

export const validateReferenceNumber = (isInvoiceLoggingEnabled) => {
  if (!isInvoiceLoggingEnabled) return true;

  const reference = getActiveReference();
  return !!reference;
};

// New function that works with the Rexor store
export const validateReferenceNumberWithStore = (
  isInvoiceLoggingEnabled,
  activeReference
) => {
  if (!isInvoiceLoggingEnabled) return true;
  return !!activeReference;
};
