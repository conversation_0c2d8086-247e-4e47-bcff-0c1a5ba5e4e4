# System News Items

This directory contains hardcoded news items that are deployed with the application. These news items are version-controlled and will appear in all deployments.

## Adding New System News

1. Create a new `.js` file in this directory (e.g., `2024-01-system-update.js`)
2. Follow the template structure shown in `template.js`
3. Import and add the news item to `index.js`
4. The news will be automatically loaded when the application starts

## File Naming Convention

Use the format: `YYYY-MM-description.js`

- `YYYY-MM`: Year and month of the news item
- `description`: Brief description in kebab-case

Examples:

- `2024-01-welcome.js`
- `2024-02-new-features.js`
- `2024-03-maintenance-notice.js`

## News Item Structure

Each news item should export an object with the following properties:

```javascript
export default {
  id: "unique-system-id", // Must be unique across all system news
  title: "News Title",
  content: "News content...",
  priority: "medium", // low, medium, high, urgent
  targetRoles: ["admin", "manager"], // Optional: null for all users
  expiresAt: "2024-12-31T23:59:59Z", // Optional: null for no expiration
  isActive: true,
  isSystemNews: true, // Always true for system news
  createdAt: "2024-01-15T10:00:00Z",
  version: "1.0.0", // Application version when added
};
```

## Important Notes

- System news items are read-only in the admin interface
- They cannot be edited or deleted through the UI
- To modify system news, update the source file and redeploy
- System news items are merged with database news items
- Use unique IDs to avoid conflicts (prefix with "system-" recommended)
