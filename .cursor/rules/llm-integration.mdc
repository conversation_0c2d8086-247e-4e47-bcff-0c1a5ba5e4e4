---
description: Guidelines for LLM integration and usage
globs: server/**/*.js,frontend/**/*.js,frontend/**/*.jsx
alwaysApply: false
---
# LLM Integration and Usage

## Supported LLM Providers
- OpenAI (GPT models)
- Anthropic (Claude models)
- Azure OpenAI
- LocalAI
- And many others (Cohere, Gemini, Mistral, etc.)

## Provider Integration
- LLM providers are integrated in `server/utils/AiProviders`
- Each provider has its own implementation with consistent interfaces
- Configuration is managed through environment variables and system settings.
- For specific tasks like prompt upgrading (via `System.generateLegalTaskPrompt` or its underlying endpoint),
- LLM selection follows a hierarchy:
-   1. Explicitly passed LLM instance (e.g., resolved from workspace settings for a template-specific upgrade).
-   2. System Prompt Upgrade LLM: `process.env.LLM_PROVIDER_PU` (if set and no explicit LLM is passed).
-   3. Default System LLM: `process.env.LLM_PROVIDER` (as a fallback if the above are not applicable).

## Embedding Models
- Multiple embedding models are supported for vector search
- Embedding engines are implemented in `server/utils/EmbeddingEngines`
- Models include OpenAI, Cohere, Gemini, and others

## Prompt Construction
- Prompts are constructed in `server/utils/helpers/chat`
- Context from vector search is included in prompts
- System prompts and user messages are combined appropriately
- Token limits are managed to stay within model constraints

## Streaming Responses
- LLM responses are streamed to the client using Server-Sent Events (SSE)
- Streaming is implemented in the chat endpoints
- Frontend components handle streaming updates

## Document Drafting
- Special handling for document drafting workflows
- Templates and formatting for legal documents
- Support for document generation and editing

## Agent Plugins
- Advanced capabilities through agent plugins
- SQL queries, web scraping, memory, and other tools
- Implemented in `server/utils/agents`

## Adding New LLM Models

### General
The system uses model maps to define properties for various LLM providers. It's important to note that there are two separate model maps that should be kept in sync:
-   **Backend**: `server/utils/AiProviders/modelMap.js`
-   **Frontend**: `frontend/src/utils/AiProviders/modelMap.js`

When adding or updating models for any provider, ensure consistency between these two files.

### Gemini Models
Adding a new Gemini model is a multi-step process involving backend and frontend changes. This ensures the model is available for all use cases, including the dynamic list on the main Gemini settings page and other manually curated lists.

#### Step 1: Update Backend Configuration

1.  **Define Model Properties in `modelMap.js`**:
    -   In `server/utils/AiProviders/modelMap.js`, add the new model with its `context` and `maxOutput` tokens.
    ```javascript
    // server/utils/AiProviders/modelMap.js
    "gemini-2.5-pro-preview-06-05": { context: 1_048_576, maxOutput: 65_536 },
    ```
2.  **Add to `defaultModals.js` for Dynamic List Grouping**:
    -   In `server/utils/AiProviders/gemini/defaultModals.js`, add the model ID to either the `stableModels` or `experimentalModels` array.
    -   This sets a flag that the main Gemini settings page (`GeminiLLMOptions`) uses to group the dynamically fetched models into "Stable" or "Experimental" `<optgroup>` sections.
    ```javascript
    // server/utils/AiProviders/gemini/defaultModals.js
    const experimentalModels = ["gemini-2.5-pro-preview-06-05"];
    ```

#### Step 2: Update Frontend Configuration

1.  **Define Model Properties in `modelMap.js`**:
    -   Mirror the backend change by adding the model to `frontend/src/utils/AiProviders/modelMap.js`.

2.  **Add to Hardcoded Manual `<optgroup>` on Settings Page**:
    -   The main Gemini settings page (`frontend/src/components/LLMSelection/GeminiLLMOptions/index.jsx`) contains a **hardcoded, manual `<optgroup>`** for pinning specific models to the top of the list.
    -   To add a model to this top group, you must edit the JSX directly.
    ```jsx
    // frontend/src/components/LLMSelection/GeminiLLMOptions/index.jsx
    <optgroup label={t("gemini.manual-options")}>
      <option value="gemini-2.5-flash-preview-05-20">...</option>
      {/* Add new manual model here */}
      <option value="new-gemini-model-to-pin">new-gemini-model-to-pin</option>
    </optgroup>
    ```

3.  **Add to Other Manual Lists (if applicable)**:
    -   For the model to appear in other manually curated UI dropdowns (e.g., workspace-level settings), add its ID to the `PROVIDER_DEFAULT_MODELS` constant in `frontend/src/hooks/useGetProvidersModels.js`.
    ```javascript
    // frontend/src/hooks/useGetProvidersModels.js
    const PROVIDER_DEFAULT_MODELS = {
      gemini: ["gemini-pro", "gemini-2.5-pro-preview-06-05"],
      // ...
    };
    ```

By following all of these steps, you ensure a new Gemini model is fully integrated across the entire application.

## Best Practices
- Use appropriate models for different tasks
- Manage token usage efficiently
- Implement proper error handling for API calls
- Consider rate limiting and fallback options
