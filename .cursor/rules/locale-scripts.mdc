---
description:
globs:
alwaysApply: false
---
# Locale Scripts Documentation

This rule provides guidance on the locale management scripts in the frontend.

## Scripts

### fixLocaleStructure.mjs

Location: `scripts/fixLocaleStructure.mjs`

Purpose:
- Reorders translation keys in each locale file to match the English template.
- Uses AST-based reordering with recast and Babel parser to preserve comments.
- Run<PERSON> Prettier on all locale files after transformation.

Usage:
```
node frontend/scripts/fixLocaleStructure.mjs
```

### verifyLocaleFiles.mjs

Location: `scripts/verifyLocaleFiles.mjs`

Purpose:
- Validates that all locale files under `frontend/src/locales/{lang}` match the English files in keys and ordering.
- Reports missing, extra, and out-of-order keys.

Usage:
```
node frontend/scripts/verifyLocaleFiles.mjs
```

These scripts help maintain consistency and quality of translation files across all supported languages.
