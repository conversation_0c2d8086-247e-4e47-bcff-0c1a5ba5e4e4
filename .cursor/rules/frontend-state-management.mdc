---
description: Guidelines for state management and event handling
globs: frontend/**/*.js,frontend/**/*.jsx
alwaysApply: false
---
# Frontend State Management Guidelines (React + Zustand)

## General Principles
- Use React's built-in state (`useState`, `useReducer`) for local, component-specific state.
- Use Zustand for global or cross-component state, especially when state is shared across unrelated components or needs to persist.
- Avoid mixing multiple state paradigms (e.g., window events, global variables, localStorage) for the same data. Zustand should be the single source of truth for shared state.
- Prefer multiple small, domain-specific stores over a single large, monolithic store.
- Use selectors to subscribe to only the state your component needs, minimizing unnecessary re-renders.
- Use Zustand middleware (e.g., `persist`, `immer`, `devtools`) for persistence, immutability, and debugging.
- Move complex business logic out of store actions into utility functions when possible.
- Avoid prop drilling by accessing store state directly in components.

## Store Structure
- **Good:** Split stores by domain (chat, workspace, settings, etc.)
- **Bad:** One massive store handling unrelated concerns

```js
// Good: Domain-specific stores
export const useChatStore = create((set) => ({
  chatHistory: [],
  addMessage: (msg) => set((state) => ({ chatHistory: [...state.chatHistory, msg] }))
}))

export const useSettingsStore = create(persist((set) => ({
  theme: 'light',
  setTheme: (theme) => set({ theme })
}), { name: 'settings' }))

// Bad: Monolithic store
export const useAppStore = create((set) => ({
  chatHistory: [],
  theme: 'light',
  user: null,
  // ... dozens of unrelated fields and actions
}))
```

## Store Usage: Selectors and Custom Hooks
- **Best Practice:** Use selectors to subscribe to only the state your component needs. This minimizes unnecessary re-renders.
- **Inline Selector Usage:**
  - Use inline selectors in your components for most cases:
  ```js
  // Good: Inline selector
  const theme = useSettingsStore((state) => state.theme)
  const addMessage = useChatStore((state) => state.addMessage)
  ```
- **Exporting Custom Selector Hooks:**
  - It is acceptable to export custom selector hooks from your store file if:
    - The selector is simple and stable (e.g., a primitive value or a boolean)
    - The hook does not return the entire store or a large object
    - The selector does not cause unnecessary re-renders
  - Example:
  ```js
  // In userStore.js
  export const useSelectedModule = () =>
    useUserStore((state) => state.selectedModule);
  export const useIsDocumentDrafting = () =>
    useUserStore((state) => state.selectedModule === "document-drafting");
  // Usage in component
  const selectedModule = useSelectedModule();
  ```
- **Bad Practice:**
  - Exporting a hook that returns the entire store or a large object, or using the store without a selector in components:
  ```js
  // Bad: Triggers re-render on any state change
  const store = useAppStore();
  // Bad: Exporting a hook that returns the whole store
  export const useWholeStore = () => useAppStore();
  ```
- **Tradeoffs:**
  - Exporting custom selector hooks can improve code readability and encapsulation, but only if the selector is narrow and stable.
  - Prefer inline selectors for ad-hoc or composite selections, or when you need to select multiple fields at once.

## State Updates
- **Good:** Use function-based updates to ensure latest state
- **Bad:** Directly set state with objects, risking stale updates

```js
// Good
set((state) => ({ count: state.count + 1 }))

// Bad
set({ count: state.count + 1 }) // May use stale value
```

## Persistence
- Use Zustand's `persist` middleware for state that should survive reloads (e.g., user settings, last selected module).
- Do not manually use localStorage or sessionStorage for state managed by Zustand.

## Anti-Patterns
- Do **not** use window events, global variables, or localStorage for state that should be managed by Zustand.
- Avoid using `useStore.getState()` in React components for reading state (use selectors or custom selector hooks instead).
- Do not put unrelated state in the same store.
- Avoid complex, multi-responsibility actions in stores—extract to utilities.
- **Do not export hooks that return the entire store object.**

## Example: Migrating to Zustand

**Before (hybrid, anti-pattern):**
```js
// Multiple sources of truth
const module = window.localStorage.getItem('module')
let _lastModuleSlug = ...

function setModule(module) {
  window.localStorage.setItem('module', module)
  _lastModuleSlug = module
}
```

**After (Zustand, best practice):**
```js
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

const useModuleStore = create(persist((set, get) => ({
  currentModule: 'legal-qa',
  setModule: (module) => set({ currentModule: module })
}), { name: 'module-settings' }))

// Usage in component (inline selector)
const currentModule = useModuleStore((state) => state.currentModule)
// Or, if exported as a custom hook (if selector is simple)
// export const useCurrentModule = () => useModuleStore((state) => state.currentModule)
// const currentModule = useCurrentModule();
```

## Summary
- Use React state for local, isolated state
- Use small, domain-specific Zustand stores for shared/global state
- Use selectors or custom selector hooks to minimize re-renders
- Use middleware for persistence and immutability
- Avoid legacy patterns (window events, global vars, direct localStorage)
- Extract complex logic from store actions
- Document store structure and usage for LLMs and developers
