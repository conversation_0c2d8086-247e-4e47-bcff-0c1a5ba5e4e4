import React, { memo, useRef, useMemo } from "react";
import { v4 } from "uuid";
import Actions from "./Actions";
import UserIcon from "../../../../UserIcon";
import { Warning } from "@phosphor-icons/react";
import { userFromStorage } from "@/utils/request";
import Markdown from "@/components/ui/Markdown";
import AssistantIcon from "../../../../AssistantIcon";
import { useWatchDeleteMessage } from "./Actions/DeleteMessage";
import { useTranslation } from "react-i18next";
import { useTextSize } from "@/stores/userStore";

// Helper function to properly handle table markdown
function processTableMarkdown(text) {
  if (!text) return text;

  // If the text doesn't contain potential table elements, return as is
  if (!text.includes("|")) return text;

  const lines = text.split("\n");
  let inTable = false;
  let tableStart = -1;
  let tableEnd = -1;

  // Find all complete tables in the message
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Detect table headers and separators
    if (
      !inTable &&
      line.startsWith("|") &&
      i < lines.length - 1 &&
      (lines[i + 1].includes("| --- |") ||
        lines[i + 1].includes("|---|") ||
        lines[i + 1].includes("| -- |"))
    ) {
      inTable = true;
      tableStart = i;
    }
    // Detect end of table
    else if (inTable && (!line.startsWith("|") || i === lines.length - 1)) {
      inTable = false;
      tableEnd = i;

      // The table is complete, so don't modify it
      if (tableStart !== -1 && tableEnd !== -1) {
        // Continue from after this table
        i = tableEnd;
        tableStart = -1;
        tableEnd = -1;
      }
    }
  }

  // If we have an incomplete table at the end, add placeholders to complete it
  if (inTable && tableStart !== -1) {
    // Get the header row to determine column count
    const headerLine = lines[tableStart].trim();
    const columnCount = headerLine.split("|").length - 2; // -2 for the start/end pipes

    // Check if we have the separator row
    if (
      tableStart + 1 < lines.length &&
      (lines[tableStart + 1].includes("| --- |") ||
        lines[tableStart + 1].includes("|---|") ||
        lines[tableStart + 1].includes("| -- |"))
    ) {
      // We have a header and separator but no data rows
      if (
        tableStart + 2 >= lines.length ||
        !lines[tableStart + 2].trim().startsWith("|")
      ) {
        // Add an empty data row to complete the table
        lines.push("| " + " | ".repeat(columnCount) + " |");
      }
    } else {
      // We only have a header row, add a separator and data row
      const separatorParts = [];
      for (let i = 0; i < columnCount; i++) {
        separatorParts.push("---");
      }
      const separatorLine = "| " + separatorParts.join(" | ") + " |";
      lines.splice(tableStart + 1, 0, separatorLine);

      // Add an empty data row
      lines.push("| " + " | ".repeat(columnCount) + " |");
    }
  }

  return lines.join("\n");
}

const HistoricalMessage = ({
  uuid = v4(),
  chat,
  message,
  role,
  workspace,
  sources = [],
  attachments = [],
  error = false,
  feedbackScore = null,
  chatId = null,
  isLastMessage = false,
  regenerateMessage,
  forkThread,
  metrics = {},
  quraEnabled,
  index,
  question,
  answer,
  sendCommand,
  threadSlug,
  updateHistory,
  history,
  isLegalTaskEnabled,
  questMessage = null,
  slug = null,
}) => {
  const { isDeleted, completeDelete, onEndAnimation } = useWatchDeleteMessage({
    chatId,
    role,
  });
  const messageRef = useRef(null);
  const { t } = useTranslation();
  const textSize = useTextSize();

  // Filter out file content from message but keep the file list
  const displayMessage = useMemo(() => {
    if (!message) return "";
    const uploadedFileTag = t("chatboxdnd.uploaded-file-tag");
    return message
      .replace(
        new RegExp(
          `<(${uploadedFileTag}|USER UPLOADED FILE)>[\\s\\S]*?</(${uploadedFileTag}|USER UPLOADED FILE)>`,
          "g"
        ),
        ""
      )
      .trim();
  }, [message, t]);

  // Process display message to ensure complete tables
  const processedDisplayMessage = useMemo(() => {
    return processTableMarkdown(displayMessage);
  }, [displayMessage]);

  if (error) {
    return (
      <div
        key={uuid}
        className={`flex justify-center items-start w-full bg-background ${
          role === "user"
            ? "text-foreground font-bold"
            : `text-foreground ${textSize}`
        }`}
      >
        <div className={`py-3 px-4 flex gap-x-5 w-full max-w-[100%] flex-col`}>
          <div className="flex gap-x-3">
            <AssistantImage role={role} workspace={workspace} />
            {error ? (
              <div className="p-2 rounded-lg bg-red-50 text-red-500">
                <span className={`inline-block ${textSize}`}>
                  <Warning className="h-4 w-4 mb-1 inline-block" /> Could not
                  respond to message.
                </span>
                <p
                  className={`font-mono mt-2 border-l-2 border-red-300 pl-2 bg-red-200 p-2 rounded-sm ${textSize}`}
                >
                  {typeof error === "string"
                    ? error
                    : message ||
                      "An error occurred while streaming the response."}
                </p>
              </div>
            ) : (
              <div className={`flex flex-col gap-y-1 ${textSize}`}>
                <Markdown content={processedDisplayMessage} />
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (completeDelete) return null;
  return (
    <div
      key={uuid}
      onAnimationEnd={onEndAnimation}
      className={`${
        isDeleted ? "animate-remove" : ""
      } flex justify-center items-end w-[100%] group resp-messages-block ${
        role === "user"
          ? "text-foreground font-bold"
          : `text-foreground ${isLastMessage ? "" : "pb-8 border-b"}`
      }`}
    >
      <div className="w-full flex gap-x-5 md:max-w-full flex-col">
        <div ref={messageRef}>
          <div className="flex flex-col gap-y-1 w-full">
            {role === "user" ? (
              <p className={`py-8 ${textSize} text-foreground`}>
                {displayMessage}
              </p>
            ) : (
              <div className={`flex flex-col gap-y-1 ${textSize}`}>
                <Markdown content={processedDisplayMessage} />
              </div>
            )}
          </div>
        </div>
        {role === "assistant" && (
          <Actions
            chat={chat}
            message={message}
            questMessage={questMessage}
            feedbackScore={feedbackScore}
            chatId={chatId}
            slug={slug}
            role={role}
            isLastMessage={isLastMessage}
            regenerateMessage={regenerateMessage}
            index={index}
            question={question}
            answer={answer}
            sendCommand={sendCommand}
            isLegalTaskEnabled={isLegalTaskEnabled}
            isCanvasEnabled={true}
            metrics={metrics}
            workspace={workspace}
            threadSlug={threadSlug}
            updateHistory={updateHistory}
            history={history}
            sources={sources}
          />
        )}
      </div>
    </div>
  );
};

function ProfileImage({ role, workspace }) {
  if (role === "assistant" && workspace.pfpUrl) {
    return (
      <div className="relative w-[35px] h-[35px] rounded-full flex-shrink-0 overflow-hidden">
        <img
          src={workspace.pfpUrl}
          alt="Workspace profile picture"
          className="absolute top-0 left-0 w-full h-full object-cover rounded-full bg-white"
        />
      </div>
    );
  }

  return (
    <UserIcon
      user={{
        uid: role === "user" ? userFromStorage()?.username : workspace.slug,
      }}
      role={role}
    />
  );
}

//Assistant Icons
function AssistantImage({ role, workspace }) {
  if (role === "assistant" && workspace.pfpUrl) {
    return (
      <div className="relative w-[35px] h-[35px] rounded-full flex-shrink-0 overflow-hidden">
        <img
          src={workspace.pfpUrl}
          alt="Workspace profile picture"
          className="absolute top-0 left-0 w-full h-full object-cover rounded-full bg-white"
        />
      </div>
    );
  }

  return (
    <AssistantIcon
      user={{
        uid: role === "user" ? userFromStorage()?.username : workspace.slug,
      }}
      role={role}
    />
  );
}

export default memo(HistoricalMessage, (prevProps, nextProps) => {
  return (
    prevProps.message === nextProps.message &&
    prevProps.isLastMessage === nextProps.isLastMessage &&
    prevProps.chatId === nextProps.chatId
  );
});

function ChatAttachments({ attachments = [] }) {
  if (!attachments.length) return null;
  return (
    <div className="flex flex-col gap-1 mt-2 mb-2 border-t border-border pt-2">
      <p className="text-xs text-muted-foreground">Attached files:</p>
      <div className="flex flex-wrap gap-2">
        {attachments.map((item) => (
          <div
            key={item.name}
            className="bg-secondary rounded-md px-2 py-1 text-xs flex items-center gap-1"
          >
            <span className="text-muted-foreground">
              {item.display_name || item.name}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}

const normalizeText = (text) =>
  text
    .replace(/“|”/g, '"')
    .replace(/‘|’/g, "'")
    .replace(/\u00A0/g, " ")
    .replace(/(?:\r\n|\r|\n)/g, " ")
    .replace(/[-•]\s?/g, "")
    .replace(/\d+\.\s?/g, "")
    .trim();
