---
description: Guidelines for using the Button component
globs: "frontend/**/*.{js,jsx,ts,tsx}"
alwaysApply: false
---

# Button Component Usage Guidelines

## Avoiding Nested Button Elements

React will throw a warning when a `<Button>` element is nested inside another `<Button>` element, as this is invalid HTML. This can happen when using the Button component from `@/components/Button` inside another button element.

### Common Issues

1. **Nested Buttons**: Never place a `<Button>` component inside another `<Button>` element or another `<Button>` component.

2. **Conditional Rendering**: When conditionally rendering buttons, ensure that the button is completely replaced with a non-button element (like a `<div>`) when it should be inactive, rather than nesting a button inside another element.

### Example of Correct Usage

```jsx
// GOOD: Conditionally render either a Button or a div
{isActive ? (
  <Button onClick={handleClick} className="...">
    Click me
  </Button>
) : (
  <div className="...">
    Inactive state
  </div>
)}

// GOOD: Using the Button component standalone
<Button onClick={handleClick} variant="primary">
  Click me
</Button>
```

### Example of Incorrect Usage

```jsx
// BAD: Button nested inside another Button
<Button onClick={handleOuterClick}>
  Outer button
  <Button onClick={handleInnerClick}>
    Inner button
  </Button>
</Button>

// BAD: Button component inside a Button element
<Button>
  <Button>This will cause a warning</Button>
</Button>
```

## Using the asChild Prop

The Button component supports an `asChild` prop that allows it to render as a different element using Radix UI's Slot component. This is useful when you need button styling but want to render a different element:

```jsx
<Button asChild>
  <Link to="/some-path">This renders as a link with button styling</Link>
</Button>
```

## Best Practices

1. Always use the Button component for clickable actions rather than styling regular HTML buttons.

2. Use the appropriate variant and size props to maintain consistent styling.

3. For disabled states, use the `disabled` prop rather than conditional rendering when possible.

4. When a button needs to be conditionally rendered as a non-interactive element, use a `<div>` with appropriate styling rather than nesting buttons.

5. Use the `isLoading` prop for loading states rather than creating custom loading indicators.
