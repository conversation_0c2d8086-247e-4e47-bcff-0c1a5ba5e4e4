---
description: Core files to consider when making changes
globs: 
alwaysApply: true
---

# Core Files to Consider

When making changes to the codebase, consider these core files that drive the main functionality:

## Frontend

1. `/frontend/index.html`
   - Main HTML entry for the React app

2. `/frontend/src/main.jsx` & `/frontend/src/App.jsx`
   - Root React components; sets up Router, context providers, etc.

3. `/frontend/src/AuthContext.jsx` and `/frontend/src/LogoContext.jsx`
   - Global contexts for user authentication & logo images

4. `/frontend/src/hooks/`
   - `useUser.js`, `useLoginMode.js`, `useQuery.js`, etc.
   - Handle user session logic, query parameters, login modes, etc.

5. `/frontend/src/utils/`
   - `constants.js`, `request.js`, `session.js` – Core for building fetch requests with auth tokens
   - `directories.js` – Common file-related formatting utilities

6. `/frontend/src/models/`
   - `system.js`, `dataConnector.js`, `experimental/` – Data models & calls to server endpoints

7. `/frontend/src/components/`
   - `Sidebar/` – Workspace listing and thread container
   - `WorkspaceChat/` – Chat rendering logic
   - `Modals/ManageWorkspace/` – Managing workspace docs, connectors, etc.
   - `EmbeddingSelection/`, `LLMSelection/`, `VectorDBSelection/`, `TextToSpeech/`, `TranscriptionSelection/`

8. `/frontend/src/pages/`
   - `Admin/` – Admin console for invites, user management, system usage logs
   - `GeneralSettings/` – System-wide settings, LLM config, embedding preferences
   - `WorkspaceSettings/` – Per-workspace advanced configuration
   - `OnboardingFlow/` – Steps for new user or workspace setup

## Server / Backend

1. `server/index.js`
   - Main entry point. Loads environment vars, sets up Express & routes

2. `server/prisma/schema.prisma`
   - Defines the Prisma schema for the database models

3. `server/prisma/migrations/*/migration.sql`
   - Each folder contains an incremental migration for the database schema

4. `server/prisma/seed.js`
   - Seeds default data when the database is first set up

5. `server/utils/`
   - `/AiProviders/*` - LLM provider integrations
   - `/EmbeddingEngines/*` - Embedding logic for providers
   - `/vectorDbProviders/*` - Vector database integrations
   - `/chats/*` - Orchestrates chat flow, slash commands, streaming
   - `/DocumentManager/index.js` - Manages pinned docs for RAG
   - `/helpers/chat/` - Handles prompt construction, token limits
   - `/files/index.js` - Core logic for file operations
   - `/boot/index.js` - Boot logic for server creation
   - `/middleware/` - Route-level middleware
   - `/database/index.js` - Custom DB checks

6. `server/models/`
   - `documents.js`, `user.js`, `workspace.js`, `workspaceChats.js`, `invite.js`, `eventLogs.js`, etc.
   - High-level data-access methods and domain logic

7. `server/endpoints/`
   - `workspaces.js`, `chat.js`, `system.js`, `document.js`, `admin.js`
   - The main endpoints for managing RAG workflows
   - `/api/*` subfolders for standard REST API

8. `server/jobs/sync-watched.documents.js`
   - Background job that auto-syncs watched documents

9. `server/swagger/`
   - `init.js` – Generates openapi.json from code
   - `openapi.json` – Primary OpenAPI definition

## Collector

- `processSingleFile/`, `processLink/`, `processRawText/` – Convert or parse files/links/text into JSON documents for RAG

## Notes on Exclusions

- Locale files (e.g., frontend/src/locales/**, server/locales/**) are excluded to avoid bloat
- CSS theme variants or color variants in public/*.css are not critical for code logic changes
