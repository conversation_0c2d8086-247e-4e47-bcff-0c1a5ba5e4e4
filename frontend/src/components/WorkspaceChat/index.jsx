import React, { useEffect, useState, useCallback } from "react";
import Workspace from "@/models/workspace";
import LoadingChat from "./LoadingChat";
import ChatContainer from "./ChatContainer";
import paths from "@/utils/paths";
import Modal from "@/components/ui/Modal";
import {
  TTSProvider,
  useWatchForAutoPlayAssistantTTSResponse,
} from "../contexts/TTSProvider";
import { useParams, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import System from "@/models/system";
import showToast from "@/utils/toast";

export default function WorkspaceChat({ loading, workspace }) {
  const { t } = useTranslation();
  useWatchForAutoPlayAssistantTTSResponse();
  const { threadSlug = null } = useParams();
  const location = useLocation();
  const [history, setHistory] = useState([]);
  const [loadingHistory, setLoadingHistory] = useState(true);

  // Check for import success message
  useEffect(() => {
    const showImportSuccess = sessionStorage.getItem("showImportSuccess");
    const importedThreadName = sessionStorage.getItem("importedThreadName");

    if (showImportSuccess === "true") {
      showToast(
        t("document-drafting.import-memo.import-success-with-name", {
          threadName: importedThreadName,
        }),
        "success"
      );
      sessionStorage.removeItem("showImportSuccess");
      sessionStorage.removeItem("importedThreadName");
    }
  }, [t]);

  const getHistory = useCallback(async () => {
    if (loading) return;
    if (!workspace?.slug) {
      setLoadingHistory(false);
      return false;
    }

    setLoadingHistory(true);

    const chatHistory = threadSlug
      ? await Workspace.threads.chatHistory(workspace.slug, threadSlug)
      : await Workspace.chatHistory(workspace.slug);

    await System.keys();
    setHistory(chatHistory);
    setLoadingHistory(false);
  }, [workspace, loading, threadSlug]);

  useEffect(() => {
    // Only fetch history once the parent component indicates it's no longer loading.
    if (!loading) {
      getHistory();
    }
  }, [loading, getHistory, location.pathname]);

  if (loadingHistory) return <LoadingChat />;
  if (!loading && !loadingHistory && !workspace) {
    return (
      <>
        {loading === false && !workspace && (
          <Modal isOpen={true} title={t("workspace.deleted.title")}>
            <div className="flex flex-col gap-y-4 w-full text-center">
              <p className="text-sm mt-4 text-white">
                {t("workspace.deleted.description")}
              </p>

              <div className="flex w-full justify-center items-center mt-4">
                <a
                  href={paths.home()}
                  className="primary-bg text-white px-4 py-2 rounded-md text-sm items-center flex gap-x-2 transition-all duration-300"
                >
                  {t("workspace.deleted.homepage")}
                </a>
              </div>
            </div>
          </Modal>
        )}
        <LoadingChat />
      </>
    );
  }

  setEventDelegatorForCodeSnippets();
  return (
    <TTSProvider>
      <ChatContainer workspace={workspace} knownHistory={history} />
    </TTSProvider>
  );
}

// Enables us to safely markdown and sanitize all responses without risk of injection
// but still be able to attach a handler to copy code snippets on all elements
// that are code snippets.
function copyCodeSnippet(uuid) {
  const target = document.querySelector(`[data-code="${uuid}"]`);
  if (!target) return false;
  const markdown =
    target.parentElement?.parentElement?.querySelector(
      "pre:first-of-type"
    )?.innerText;
  if (!markdown) return false;

  window.navigator.clipboard.writeText(markdown);
  target.classList.add("text-green-500");
  const originalText = target.innerHTML;
  target.innerText = "Copied!";
  target.setAttribute("disabled", true);

  setTimeout(() => {
    target.classList.remove("text-green-500");
    target.innerHTML = originalText;
    target.removeAttribute("disabled");
  }, 2500);
}

// Listens and hunts for all data-code-snippet clicks.
function setEventDelegatorForCodeSnippets() {
  document?.addEventListener("click", function (e) {
    const target = e.target.closest("[data-code-snippet]");
    const uuidCode = target?.dataset?.code;
    if (!uuidCode) return false;
    copyCodeSnippet(uuidCode);
  });
}
