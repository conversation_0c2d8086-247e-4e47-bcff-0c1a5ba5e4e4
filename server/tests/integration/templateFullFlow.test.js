const { getLLMProvider } = require("../../utils/helpers");
const {
  generateLegalTaskPrompt: actualGenerateLegalTaskPrompt,
} = require("../../endpoints/generateLegalTaskPrompt");
const {
  generateSectionListFromSummaries,
} = require("../../utils/chats/helpers/documentProcessing");
const { Workspace } = require("../../models/workspace"); // For mocking workspace retrieval

// Mock dependencies
// Mock for the system's base LLM provider (used by generateLegalTaskPrompt)
jest.mock("../../utils/helpers", () => ({
  getLLMProvider: jest.fn(),
}));

// Mock for the workspace/task-specific LLMConnector (used by documentProcessing helpers)
const mockWorkspaceLLMConnector = {
  compressMessages: jest.fn(async (messages) => messages),
  getChatCompletion: jest.fn(),
  model: "workspace-mock-model",
  promptWindowLimit: jest.fn().mockReturnValue(8000),
  isValidChatCompletionModel: jest.fn().mockReturnValue(true),
  metrics: { lastCompletionTokens: 0 },
  constructor: { name: "WorkspaceLLM" }, // Added for error messages
};

// Mock TokenManager used by documentProcessing helpers (if not already globally mocked in jest.setup.js)
// For this test, let's assume it might be needed here if not global.
jest.mock("../../utils/helpers/tiktoken", () => ({
  TokenManager: jest.fn().mockImplementation(() => ({
    countFromString: jest.fn((str) => str.length),
  })),
}));

// Mock fs used by documentProcessing if it tries to cache (though not directly relevant to this test's core logic)
jest.mock("fs", () => ({
  existsSync: jest.fn().mockReturnValue(false), // Assume no cache hit
  mkdirSync: jest.fn(),
  writeFileSync: jest.fn(),
  readFileSync: jest.fn(),
}));

// **** ADD THIS MOCK ****
jest.mock("../../models/workspace", () => ({
  Workspace: {
    get: jest.fn(), // Mock Workspace.get specifically
  },
}));
// *************************

describe("Template Generation Full Flow with Workspace LLM for Upgrade Integration Test", () => {
  let originalEnv;
  const generateLegalTaskPromptInternal = actualGenerateLegalTaskPrompt;

  beforeEach(() => {
    originalEnv = { ...process.env };
    // No system default LLM_PROVIDER needed here if workspace LLM is always used for upgrade in this flow

    // Setup getLLMProvider to return the mockWorkspaceLLMConnector when called with workspace settings
    getLLMProvider.mockImplementation(({ provider, modelName }) => {
      if (
        provider === "workspace-llm-provider" &&
        modelName === "workspace-llm-model"
      ) {
        return mockWorkspaceLLMConnector;
      }
      return null; // Or some default mock if necessary for other calls
    });

    Workspace.get.mockClear();
    mockWorkspaceLLMConnector.getChatCompletion.mockClear();
    mockWorkspaceLLMConnector.compressMessages.mockClear();
    mockWorkspaceLLMConnector.isValidChatCompletionModel.mockReturnValue(true); // Default to valid
    getLLMProvider.mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
    process.env = originalEnv;
  });

  test("should use the same workspace LLM for both prompt upgrade and section generation", async () => {
    const workspaceId = "ws-integ-test";
    const mockActiveWorkspace = {
      id: workspaceId,
      llmProvider: "workspace-llm-provider",
      llmModelName: "workspace-llm-model",
      llmSettings: JSON.stringify({ wsSetting: "val" }),
      chatSettings: JSON.stringify({ wsChatTemp: 0.6 }),
    };
    Workspace.get.mockResolvedValue(mockActiveWorkspace);

    // ---- Stage 1: Prompt Upgrade (using workspace LLM) ----
    const initialUserContentForUpgrade = "Draft a basic NDA for software.";
    const upgradedPromptText =
      "UPGRADED_PROMPT_BY_WORKSPACE_LLM: Create a detailed Non-Disclosure Agreement for software, ensuring all clauses for IP protection are included.";

    // Simulate the endpoint resolving the workspace LLM and passing it
    const llmForUpgrade = getLLMProvider({
      provider: mockActiveWorkspace.llmProvider,
      modelName: mockActiveWorkspace.llmModelName,
      settings: {
        ...(mockActiveWorkspace.llmSettings
          ? JSON.parse(mockActiveWorkspace.llmSettings)
          : {}),
        ...(mockActiveWorkspace.chatSettings
          ? JSON.parse(mockActiveWorkspace.chatSettings)
          : {}),
      },
    });
    expect(llmForUpgrade).toBe(mockWorkspaceLLMConnector); // Ensure setup is correct

    mockWorkspaceLLMConnector.getChatCompletion.mockResolvedValueOnce({
      textResponse: upgradedPromptText,
    });

    const actualUpgradedPrompt = await generateLegalTaskPromptInternal(
      initialUserContentForUpgrade,
      llmForUpgrade // Passing the workspace LLM instance
    );

    expect(getLLMProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        provider: "workspace-llm-provider",
        modelName: "workspace-llm-model",
      })
    );
    expect(mockWorkspaceLLMConnector.getChatCompletion).toHaveBeenCalledTimes(
      1
    );
    expect(mockWorkspaceLLMConnector.getChatCompletion).toHaveBeenNthCalledWith(
      1,
      [
        {
          role: "system",
          content:
            "You are a legal expert who creates effective prompts for legal professionals. Your task is to generate clear, specific, and well-structured prompts that work optimally with the Case Document Builder system. IMPORTANT: Detect the language of the user task description and generate the prompt in the same detected language.",
        },
        {
          role: "user",
          content: initialUserContentForUpgrade,
        },
      ],
      {
        temperature: 0.2,
        max_tokens: Math.floor(
          mockWorkspaceLLMConnector.promptWindowLimit() * 0.6
        ),
      }
    );
    expect(actualUpgradedPrompt).toBe(upgradedPromptText);

    // ---- Stage 2: Template/Document Generation (using the same workspace LLM and upgraded prompt) ----
    const docSummariesForGeneration = [
      {
        "Doc Name": "ip_guidelines.pdf",
        Description: "Guidelines for IP protection.",
      },
    ];
    const generatedSectionListText = [
      { title: "Section generated with UPGRADED prompt by WORKSPACE LLM" },
    ];

    mockWorkspaceLLMConnector.getChatCompletion.mockResolvedValueOnce({
      textResponse: JSON.stringify(generatedSectionListText),
    });

    const customInstructionsForStage2 =
      "Custom instructions for section generation stage";
    const finalSectionList = await generateSectionListFromSummaries(
      docSummariesForGeneration,
      actualUpgradedPrompt, // Using the output from Stage 1
      mockWorkspaceLLMConnector, // Using the SAME workspace LLMConnector instance
      customInstructionsForStage2, // Pass custom instructions
      { temperature: 0.65 } // Potentially different temp for this specific call
    );

    expect(mockWorkspaceLLMConnector.getChatCompletion).toHaveBeenCalledTimes(
      2
    ); // Called once for upgrade, once for sections

    // Reconstruct expectedUserPromptForSectionList based on how generateSectionListFromSummaries formats it
    const expectedFormattedSummariesStage2 = docSummariesForGeneration
      .map((s) => `${s["Doc Name"]}: ${s["Description"]}`)
      .join("\n\n");

    const baseUserPromptForStage2 = `Legal Task: ${actualUpgradedPrompt}\n\nDocument Summaries:\n${expectedFormattedSummariesStage2}\n\nBased on these document summaries, identify appropriate sections for a comprehensive legal document that addresses the task. Return only a JSON array with the structure specified.`;

    // Mirror the prepending logic of generateSectionListFromSummaries
    const finalUserPromptWithInstructions = `${customInstructionsForStage2}\n\n${baseUserPromptForStage2}`;

    expect(mockWorkspaceLLMConnector.compressMessages).toHaveBeenCalledWith(
      expect.objectContaining({
        userPrompt: finalUserPromptWithInstructions,
      })
    );
    expect(mockWorkspaceLLMConnector.getChatCompletion).toHaveBeenNthCalledWith(
      2,
      expect.anything(),
      { temperature: 0.65 }
    );
    expect(finalSectionList).toEqual(generatedSectionListText);
  });
});
