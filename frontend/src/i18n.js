import i18next from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import {
  defaultNS,
  resources,
  loadLanguageAsync,
  initializeLanguageAsync,
} from "./locales/resources";

export const DEFAULT_LANGUAGE = "en";
export const SUPPORTED_LANGUAGES = ["en", "fr", "de", "sv", "rw", "no", "pl"]; // Reflects project structure

const languageDetector = new LanguageDetector(null, {
  order: ["navigator"],
  caches: [],
});

let i18nInstance;

const applyLanguageResources = async (language, bundle) => {
  if (language === DEFAULT_LANGUAGE) return true;

  try {
    const mergedBundle = bundle;
    if (mergedBundle && Object.keys(mergedBundle).length > 0) {
      i18next.addResourceBundle(language, defaultNS, mergedBundle);
      return i18next.hasResourceBundle(language, defaultNS);
    }
    return false;
  } catch (error) {
    console.error(
      `[i18n] Error applying language resources for ${language}:`,
      error
    );
    return false;
  }
};

const initialize = async () => {
  await i18next
    .use(initReactI18next)
    .use(languageDetector)
    .init({
      supportedLngs: SUPPORTED_LANGUAGES,
      fallbackLng: "en",
      debug: false,
      defaultNS,
      resources,
      lowerCaseLng: true,
      interpolation: {
        escapeValue: false,
      },
    });

  const targetLanguage = await initializeLanguageAsync();

  i18next.on("languageChanged", async (lng) => {
    if (lng !== DEFAULT_LANGUAGE) {
      await applyLanguageResources(lng, await loadLanguageAsync(lng));
    }
    localStorage.setItem("language", lng);
  });

  if (targetLanguage !== DEFAULT_LANGUAGE) {
    const success = await applyLanguageResources(
      targetLanguage,
      await loadLanguageAsync(targetLanguage)
    );

    if (success) {
      await i18next.changeLanguage(targetLanguage);
    } else {
      localStorage.setItem("language", DEFAULT_LANGUAGE);
      await i18next.changeLanguage(DEFAULT_LANGUAGE);
    }
  }

  i18nInstance = i18next;
  return i18nInstance;
};

let initializationPromise = null;

export const ensureI18nInitialized = () => {
  if (!initializationPromise) {
    initializationPromise = initialize();
  }
  return initializationPromise;
};

export const changeLanguage = async (targetLang) => {
  await ensureI18nInitialized();

  if (!targetLang || !SUPPORTED_LANGUAGES.includes(targetLang)) {
    return false;
  }

  const hasBundleAlready = i18next.hasResourceBundle(targetLang, defaultNS);
  if (i18next.language === targetLang && hasBundleAlready) {
    return true;
  }

  try {
    const bundle = await loadLanguageAsync(targetLang);
    if (bundle && Object.keys(bundle).length > 0) {
      const appliedSuccessfully = await applyLanguageResources(
        targetLang,
        bundle
      );
      if (appliedSuccessfully) {
        await i18next.changeLanguage(targetLang);
        localStorage.setItem("language", targetLang);
        return true;
      }
    }

    if (i18next.language !== DEFAULT_LANGUAGE) {
      await i18next.changeLanguage(DEFAULT_LANGUAGE);
      localStorage.setItem("language", DEFAULT_LANGUAGE);
    }
    return false;
  } catch (error) {
    console.error(`[i18n] Error changing language to ${targetLang}:`, error);
    try {
      await i18next.changeLanguage(targetLang);
      localStorage.setItem("language", targetLang);
    } catch (changeLangError) {
      console.error(`[i18n] Error in direct language change:`, changeLangError);
      if (i18next.language !== DEFAULT_LANGUAGE) {
        try {
          await i18next.changeLanguage(DEFAULT_LANGUAGE);
          localStorage.setItem("language", DEFAULT_LANGUAGE);
        } catch (revertError) {
          console.error(
            `[i18n] Error reverting to default language:`,
            revertError
          );
        }
      }
    }
    return false;
  }
};

export default i18next;
