export default {
  // =========================
  // DOCUMENT DRAFTING
  // =========================
  "document-drafting": {
    title: "Document Drafting",
    description: "Control your document drafting settings.",
    configuration: "Configuration",
    "drafting-model": "Drafting LLM",
    enabled: "Document Drafting is enabled",
    disabled: "Document Drafting is disabled",
    "enabled-toast": "Document Drafting enabled",
    "disabled-toast": "Document Drafting disabled",
    "desc-settings":
      "The Admin can change the document drafting settings for all users.",
    "drafting-llm": "Drafting LLM Preference",
    saving: "Saving...",
    save: "Save changes",
    "chat-settings": "Chat Settings",
    "drafting-chat-settings": "Document Drafting Chat Settings",
    "chat-settings-desc":
      "Control the behavior of the chat feature for document drafting.",
    "drafting-prompt": "Document Drafting system Prompt",
    "drafting-prompt-desc":
      "The system prompt that will be used in document drafting is different from the legal Q&A systemprompt. Document drafting system prompt defines the context and instructions for the AI to generate a response. You should to provide a carefully crafted prompt so the AI can generate a relevant and accurate response",
    linking: "Document Linking",
    "legal-issues-prompt": "Legal Issues Prompt",
    "legal-issues-prompt-desc": "Enter the prompt for legal issues.",
    "memo-prompt": "Memo Prompt",
    "memo-prompt-desc": "Enter the prompt for memo.",
    "desc-linkage":
      "Enable adding further legal context by doing Vector/PDR searches on top of memo fetching",
    message: {
      title: "Suggested Document Drafting Messages",
      description:
        "Add suggested messages that users can quickly select when drafting documents.",
      heading: "Default Message Heading",
      body: "Default Message Body",
      "new-heading": "Message Heading",
      message: "Message Content",
      add: "Add Message",
      save: "Save Messages",
    },
    "combine-prompt": "Combine Prompt",
    "combine-prompt-desc":
      "Provide the system prompt for combining multiple responses into a single response. This prompt is used for both combining response and DD Linkage memos, and for combining the different responses from Infinity Context processing.",
    "page-description":
      "This page is for adjusting the different prompts used in different functionalities in the document drafting module. In each input field, the default prompt is shown, which will be used unless a custom prompt is applied on this page.",
    "dd-linkage-steps": "Prompts applied for DD Linkage steps",
    "general-combination-prompt": "General Combination Prompt",
    "import-memo": {
      title: "Import from Legal QA",
      "button-text": "Import Memo",
      "search-placeholder": "Search threads...",
      import: "Import",
      importing: "Importing...",
      "no-threads": "No Legal QA threads found",
      "no-matching-threads": "No threads match your search",
      "thread-not-found": "Selected thread not found",
      "empty-thread": "The selected thread has no content to import",
      "import-success": "Thread content imported successfully",
      "import-error": "Failed to import thread content",
      "import-error-details": "Error during import: {{details}}",
      "fetch-error": "Failed to fetch threads. Please try again later.",
      "imported-from": "Imported from Legal QA thread",
      "unnamed-thread": "Unnamed Thread",
      "unknown-workspace": "Unknown Workspace",
      "no-threads-available": "No threads available to import",
      "create-conversations-first":
        "Create conversations in a Legal QA workspace first, then you can import them here.",
      "no-legal-qa-workspaces":
        "No Legal QA workspaces with active threads were found. Create conversations in a Legal QA workspace first to import them.",
      "empty-workspaces-with-names":
        "Found Legal QA workspaces ({{workspaceNames}}) but they don't contain any active threads yet. Create conversations in these workspaces first to import them.",
      "import-success-with-name":
        "Successfully imported thread: {{threadName}}",
    },
  },

  // =========================
  // DD SETTINGS (WORKSPACE LINKING SETTINGS)
  // =========================
  "dd-settings": {
    title: "Workspace Linking Settings",
    description:
      "Control token limits and behavior for workspace linking features",
    "vector-search": {
      title: "Vector Search",
      description:
        "When enabled, this feature performs semantic vector searches across all linked workspaces to find relevant legal documents. The system will convert user queries into vector embeddings and match them against document vectors in each linked workspace's database. This feature serves as a fallback when Memo Generation is enabled but fails to produce results. When Memo Generation is disabled, Vector Search becomes the primary method for retrieving information from linked workspaces. The search depth is controlled by the Vector Token Limit setting.",
    },
    "memo-generation": {
      title: "Memo Generation",
      description:
        "This feature automatically generates concise legal issue memos from documents found in linked workspaces. When enabled, the system analyzes retrieved documents to create structured summaries of key legal points, precedents, and relevant context. These memos serve as the primary method for incorporating knowledge from linked workspaces. If memo generation fails or returns no results, the system will automatically fall back to Vector Search (if enabled) to ensure relevant information is still retrieved. The length and detail of these memos are controlled by the Memo Token Limit setting.",
    },
    "base-generation": {
      title: "Base Legal Analysis",
      description:
        "Enables preliminary legal analysis generation based on the user's initial query, before incorporating information from linked workspaces. When active, the system creates a foundational analysis framework that helps guide the subsequent document search and memo generation processes. This base analysis helps ensure that responses remain focused on the core legal issues while incorporating supporting information from linked workspaces. The scope of this analysis is controlled by the Base Token Limit setting.",
    },
    "linked-workspace-impact": {
      title: "Linked Workspace Token Impact",
      description:
        "Controls how the system manages its token budget across multiple linked workspaces. When enabled, the system will dynamically adjust the available tokens for each workspace based on the total number of linked workspaces, ensuring fair distribution of computational resources. This prevents any single workspace from dominating the context window while maintaining comprehensive coverage across all relevant legal areas. This setting reserves token capacity specifically for Memo Generation and/or Vector Search results from each linked workspace, which can reduce the total tokens available for the primary workspace when many workspaces are linked.",
    },
    "vector-token-limit": {
      title: "Vector Token Limit",
      description:
        "Specifies the maximum number of tokens allocated for vector search results from each linked workspace. This limit applies when Vector Search is used, either as the primary method (when Memo Generation is disabled) or as a fallback (when Memo Generation fails). Higher limits allow more comprehensive document retrieval but reduce the tokens available for other operations.",
    },
    "memo-token-limit": {
      title: "Memo Token Limit",
      description:
        "Controls the maximum length of generated legal issue memos from each linked workspace. As the primary method for knowledge incorporation, these memos summarize key legal points from the linked workspace's documents. If a memo exceeds this token limit, it will be rejected and the system will fall back to Vector Search (if enabled). Higher limits enable more detailed legal analysis but may reduce the number of linked workspaces that can be incorporated.",
    },
    "base-token-limit": {
      title: "Base Token Limit",
      description:
        "Determines the maximum token length for the initial legal analysis framework. This limit affects how comprehensive the base analysis can be before incorporating information from linked workspaces. A higher limit allows for more detailed initial analysis but leaves less room for incorporating content from linked workspaces.",
    },
    "toast-success": "Settings updated successfully",
    "toast-fail": "Failed to update settings",
  },

  // =========================
  // WORKSPACE LINKING
  // =========================
  "workspace-linking": {
    title: "Workspace Linking Settings",
    description:
      "Control token limits and behavior for workspace linking features",
    "vector-search": {
      title: "Vector Search",
      description:
        "Fallback method for finding relevant documents when memo generation fails or is disabled",
    },
    "memo-generation": {
      title: "Memo Generation",
      description:
        "Primary method for incorporating knowledge from linked workspaces",
    },
    "base-generation": {
      title: "Base Legal Analysis",
      description: "Generate initial legal issue analysis from user prompts",
    },
    "linked-workspace-impact": {
      title: "Linked Workspace Token Impact",
      description:
        "Reserve tokens for each linked workspace in proportion to their number",
    },
    "vector-token-limit": {
      title: "Vector Token Limit",
      description: "Maximum tokens per linked workspace for vector search",
    },
    "memo-token-limit": {
      title: "Memo Token Limit",
      description: "Maximum tokens for legal issue memo generation",
    },
    "base-token-limit": {
      title: "Base Token Limit",
      description: "Maximum tokens for base content fetching",
    },
    "toast-success": "Settings updated successfully",
    "toast-fail": "Failed to update settings",
  },
  // =========================
  // BINARY LLM SELECTION
  // =========================
  binary_llm_selection: {
    "secondary-llm-toggle": "Binary LLM selection",
    "secondary-llm-toggle-description":
      "Enable this to give admins the ability to choose between two LLM models in the document drafting module.",
    "secondary-llm-toggle-status": "Status: ",
    "secondary-llm-user-level": "Secondary LLM User Level",
    "secondary-llm-user-level-description":
      "Enable this to give ALL users the ability to choose between two LLM models in the document drafting workspace.",
  },
};
