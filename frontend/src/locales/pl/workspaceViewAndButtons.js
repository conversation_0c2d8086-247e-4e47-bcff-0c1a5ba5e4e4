export default {
  // =========================
  // WORKSPACE CHATS
  // =========================
  "workspace-chats": {
    welcome: "Witaj w swojej nowej przestrzeni roboczej.",
    "desc-start": "Aby rozpo<PERSON>, możesz albo",
    "desc-mid": "przesłać dokument",
    "desc-or": "lub",
    start: "Aby rozpocząć",
    "desc-end": "wysłać wiadomość.",
    "attached-file": "Załączony plik",
    "attached-files": "Załączone pliki",
    "token-count": "Liczba tokenów",
    "total-tokens": "Całkowita liczba tokenów",
    "context-window": "Dostępne okno kontekstu",
    "remaining-tokens": "Pozostało",
    "view-files": "<PERSON>ob<PERSON><PERSON> zał<PERSON>czone pliki",
    prompt: {
      send: "Wyślij",
      "send-message": "<PERSON>yś<PERSON><PERSON> wiadomo<PERSON>",
      placeholder: "Zapytaj o informacje prawne",
      "change-size": "Zmień rozmiar tekstu",
      reset: "Zresetuj czat",
      clear: "Wyczyść historię czatu i rozpocznij nową rozmowę",
      command: "Polecenie",
      description: "Opis",
      save: "zapisz",
      small: "Mały",
      normal: "Normalny",
      large: "Duży",
      larger: "Większy",
      attach: "Dołącz plik do tego czatu",
      upgrade: "Ulepsz swoje zapytanie",
      upgrading: "Ulepszanie zapytania...",
      "original-prompt": "Oryginalne zapytanie:",
      "upgraded-prompt": "Ulepszone zapytanie:",
      "edit-prompt": "Możesz edytować nowe zapytanie przed wysłaniem",
      "shortcut-tip":
        "Wskazówka: Naciśnij Enter, aby zaakceptować zmiany. Użyj Shift+Enter dla nowych linii.",
      "speak-prompt": "Wypowiedz swoje zapytanie",
      "view-agents": "Pokaż wszystkich dostępnych agentów do czatowania",
      "deep-search": "Wyszukiwanie w sieci",
      "deep-search-tooltip":
        "Wyszukaj w sieci informacje, aby ulepszyć odpowiedzi",
      "ability-tag": "Umiejętność",
      "workspace-chats.prompt.view-agents": "Pokaż agentów",
      "workspace-chats.prompt.ability-tag": "Umiejętność",
      "workspace-chats.prompt.speak-prompt": "Wypowiedz swoje zapytanie",
      "total-tokens": "Całkowita liczba tokenów",
    },
  },
  // =========================
  // NEW WORKSPACE
  // =========================
  "new-workspace": {
    title: "Nowa przestrzeń robocza",
    placeholder: "Moja przestrzeń robocza",
    "legal-areas": "Obszary prawa",
    create: {
      title: "Utwórz nową przestrzeń roboczą",
      description:
        "Po utworzeniu tej przestrzeni tylko administratorzy będą mogli ją zobaczyć. Użytkowników możesz dodać po jej utworzeniu.",
      error: "Błąd: ",
      cancel: "Anuluj",
      "create-workspace": "Utwórz przestrzeń roboczą",
    },
  },
  // =========================
  // WORKSPACE NAMES (LEGAL AREAS)
  // =========================
  "workspace-names": {
    "Administrative Law": "Prawo administracyjne",
    "Business Law": "Prawo gospodarcze",
    "Civil Law": "Prawo cywilne",
    "Criminal Law": "Prawo karne",
    "Diplomatic Law": "Prawo dyplomatyczne",
    "Fundamental Law": "Prawo fundamentalne",
    "Human Rights Law": "Prawo praw człowieka",
    "Judicial Laws": "Prawo sądowe",
    "Security Laws": "Prawo bezpieczeństwa",
    "Taxation Laws": "Prawo podatkowe",
  },
  // =========================
  // PRESETS
  // =========================
  presets: {
    "edit-title": "Edytuj Standardowy Prompt",
    description: "Opis promptu",
    "description-placeholder": "Tworzy podsumowanie dołączonych plików.",
    deleting: "Usuwanie...",
    "delete-preset": "Usuń Standardowy Prompt",
    cancel: "Anuluj",
    save: "Zapisz",
    "add-title": "Dodaj Standardowy Prompt",
    "command-label": "Nazwa promptu, jedno słowo",
    "command-placeholder": "Podsumowanie",
    "command-desc":
      "Nazwa jest również skrótem chatbox, zaczynającym się od /, aby użyć tego promptu bez naciśnięcia przycisków.",
    "prompt-label": "Prompt",
    "prompt-placeholder": "Tworzy podsumowanie dołączonych plików.",
    "prompt-desc":
      "Prompt, który zostanie wysłany po użyciu tego predefiniowanego zestawu.",
    "tooltip-add": "Dodaj nowy Standardowy Prompt",
    "tooltip-hover": "Zobacz swoje własne Standardowe Prompty.",
    "confirm-delete": "Potwierdź usunięcie standardowego szablonu monitu.",
  },
  // =========================
  // QURA BUTTONS
  // =========================
  qura: {
    "copy-to-cora": "Sprawdź źródło Qura",
    "qura-status": "Przycisk Qura jest ",
    "copy-option": "Kopiuj opcję",
    "option-quest": "Pytanie",
    "option-resp": "Odpowiedź",
    "role-description":
      "Dodaj przycisk Qura, aby pobudzić odpowiedzi na Qura.law",
  },
  // =========================
  // CHAT BOX DRAG & DROP COMPONENT
  // =========================
  chatboxdnd: {
    title: "Dodaj plik do tego zapytania",
    description:
      "Upuść tutaj swój plik, aby dodać go do tego zapytania. Nie jest on przechowywany w przestrzeni roboczej jako trwałe źródło.",
    "file-prefix": "Plik:",
    "attachment-tooltip":
      "Ten plik zostanie dołączony do Twojej wiadomości. Nie zostanie zapisany w przestrzeni roboczej jako trwałe źródło.",
    "uploaded-file-tag": "PRZESŁANY PLIK UŻYTKOWNIKA",
  },
  // =========================
  // CONTEXT WINDOW DISPLAY
  // =========================
  context_window: {
    "context-window": "Okno kontekstu",
    "max-output-tokens": "Maksymalna liczba tokenów wyjściowych",
    "output-limit": "Limit wyjścia",
    tokens: "tokenów",
    "fallback-value": "Użyto wartości domyślnej",
  },
  // =========================
  // PROMPT VALIDATION
  // =========================
  "prompt-validate": {
    edit: "Edytuj",
    response: "Odpowiedź",
    prompt: "Zapytanie",
    regenerate: "Generuj ponownie odpowiedź",
    good: "Dobra odpowiedź",
    bad: "Zła odpowiedź",
    copy: "Kopiuj",
    more: "Więcej akcji",
    fork: "Rozgałęź",
    delete: "Usuń",
    cancel: "Anuluj",
    save: "Zapisz i wyślij",
    "export-word": "Eksportuj do Worda",
    exporting: "Eksportowanie...",
  },
  // =========================
  // VALIDATE ANSWER
  // =========================
  "validate-answer": {
    setting: "Walidacyjny LLM",
    title: "Preferencje walidacyjnego LLM",
    description:
      "Są to dane uwierzytelniające i ustawienia dla preferowanego dostawcy czatu walidacyjnego LLM i osadzania. Ważne, aby te klucze były aktualne i poprawne, w przeciwnym razie system nie będzie działał poprawnie.",
    "toast-success": "Ustawienia walidacyjnego LLM zaktualizowane",
    "toast-fail": "Nie udało się zaktualizować ustawień walidacyjnego LLM",
    saving: "Zapisywanie...",
    "save-changes": "Zapisz zmiany",
  },
  // =========================
  // VALIDATION
  // =========================
  validation: {
    responseHeader: "Oto wygenerowana odpowiedź",
    contextHeader: "Oryginalny kontekst i źródła",
  },
  // =========================
  // CITATIONS
  // =========================
  citations: {
    show: "Pokaż cytowania",
    hide: "Ukryj cytowania",
    chunk: "Fragmenty cytowań",
    pdr: "Dokument źródłowy",
    "pdr-h": "Podświetlenie dokumentu",
    referenced: "Cytowane",
    times: "razy.",
    citation: "Cytat",
    match: "dopasowanie",
    download:
      "Ta przeglądarka nie obsługuje plików PDF. Proszę pobierz plik PDF, aby go wyświetlić:",
    "download-btn": "Pobierz PDF",
    view: "Zobacz cytowania",
    sources: "Źródła cytowań",
    "pdf-collapse-tip":
      "Wskazówka: Możesz zwinąć tę kartę PDF za pomocą przycisku w lewym górnym rogu",
    "open-in-browser": "Otwórz w przeglądarce",
    "loading-pdf": "-- ładowanie PDF --",
    "error-loading": "Błąd podczas ładowania PDF",
    "no-valid-path": "Nie znaleziono prawidłowej ścieżki PDF",
    "view-details": "Zobacz szczegóły",
    "web-search": "Wyszukiwanie w sieci",
    "web-search-summary": "Podsumowanie wyszukiwania w sieci",
    "web-search-results": "Wyniki wyszukiwania w sieci",
    "no-web-search-results": "Nie znaleziono wyników wyszukiwania w sieci",
    "previous-highlight": "Poprzednie podświetlenie",
    "next-highlight": "Następne podświetlenie",
    "try-alternative-view": "Wypróbuj alternatywny widok",
  },
  // =========================
  // MANUAL WORK ESTIMATOR
  // =========================
  "manual-work-estimator": {
    title: "Szacowanie pracy manualnej",
    button: "Ocena czasu",
    "show-prompt": "Pokaż prompt",
    "hide-prompt": "Ukryj prompt",
    "prompt-title": "Prompt użyty do szacowania",
    "system-prompt": "Prompt systemowy",
    "user-content": "Treść użytkownika",
    "provider-info": "Informacje o dostawcy",
    model: "Model",
    provider: "Dostawca",
  },
};
