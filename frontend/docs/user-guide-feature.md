# User Guide Feature Documentation

## Overview

The User Guide feature provides comprehensive in-app documentation to help users understand and effectively use all features of the IST Legal platform. It appears as a button in the workspace header and opens a modal with detailed sections covering platform functionality.

## Architecture

### Component Structure

```
frontend/src/components/HeaderWorkspace/UseGuide/
├── index.jsx                 # Main UseGuide component (button)
└── UseGuideModal/
    └── index.jsx            # Modal component with all documentation content
```

### Integration Points

1. **Header Integration**: The UseGuide component is integrated into `HeaderWorkspace/index.jsx`
2. **Translation System**: Fully internationalized using react-i18next
3. **UI Components**: Uses project-standard Button and Modal components
4. **Icon System**: Uses BookOpen icon from @phosphor-icons/react

## Implementation Details

### Main Component (`UseGuide/index.jsx`)

```jsx
import { useState } from "react";
import { Button } from "@/components/Button";
import { BookOpen } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";
import UseGuideModal from "./UseGuideModal";

const UseGuide = () => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);

  return (
    <>
      <Button variant="outline" onClick={() => setOpen(true)}>
        <BookOpen />
        {t("use-guide.button")}
      </Button>
      <UseGuideModal isOpen={open} onClose={() => setOpen(false)} />
    </>
  );
};
```

**Key Features:**

- Uses outline variant Button for consistent header styling
- BookOpen phosphor icon for visual recognition
- Simple state management for modal visibility
- Proper translation integration

### Modal Component (`UseGuideModal/index.jsx`)

The modal component contains:

- **Expandable sections** for organized content navigation
- **Comprehensive documentation** covering all platform features
- **Responsive design** that works across device sizes
- **Accessibility features** including proper focus management

**Section Structure:**

1. Platform Overview
2. Platform Modules (Legal Q&A, Document Drafting)
3. Template Generation
4. Complex Document Builder
5. Workspace Management
6. Message Tools
7. Prompt Input Features
8. User Account & Settings

## Internationalization

### Translation Structure

The user guide is fully internationalized with translations in 7 languages:

```
frontend/src/locales/
├── en/useGuide.js    # English (primary)
├── sv/useGuide.js    # Swedish
├── fr/useGuide.js    # French
├── de/useGuide.js    # German
├── no/useGuide.js    # Norwegian
├── pl/useGuide.js    # Polish
└── rw/useGuide.js    # Kinyarwanda
```

### Translation Key Structure

```javascript
export default {
  "use-guide": {
    button: "Use Guide",
    title: "How to Use IST Legal",

    // Common labels for UI elements
    labels: {
      requirements: "Requirements",
      availability: "Availability",
      note: "Note",
      shortcut: "Shortcut",
    },

    // Main sections
    sections: {
      overview: "Platform Overview",
      modules: "Platform Modules",
      templateGeneration: "Template Generation",
      documentBuilder: "Complex Document Builder",
      workspace: "Workspace Management",
      messageTools: "Message Tools",
      promptInput: "Prompt Input Features",
      userAccount: "User Account & Settings",
    },

    // Detailed content for each section
    overview: {
      description: "Platform description...",
    },

    messageTools: {
      toolName: {
        title: "Tool Title",
        description: "Tool description",
        icon: "Icon description",
        usage: "How to use instructions",
        note: "Important notes",
        availability: "Availability restrictions",
      },
    },

    promptInput: {
      featureName: {
        title: "Feature Title",
        description: "Feature description",
        icon: "Icon description",
        usage: "How to use instructions",
        shortcut: "Keyboard shortcut",
        requirements: "Prerequisites",
        availability: "Availability restrictions",
      },
    },
  },
};
```

### Translation Integration

All translation files are properly imported in `frontend/src/locales/resources.js`:

```javascript
// Import statements for all languages
import EnglishUseGuide from "./en/useGuide.js";
import SvUseGuide from "./sv/useGuide.js";
// ... other imports

const localeFiles = {
  en: {
    // ... other files
    useGuide: EnglishUseGuide,
  },
  sv: {
    // ... other files
    useGuide: SvUseGuide,
  },
  // ... other languages
};
```

## Content Management

### Adding New Features to the User Guide

When adding new features to the platform, the user guide must be updated:

1. **Identify the appropriate section** (message tools, prompt input, etc.)
2. **Add content to English first** (`en/useGuide.js`)
3. **Translate to all other languages** (sv, fr, de, no, pl, rw)
4. **Update the modal component** if new sections are needed
5. **Test in all languages** to ensure proper display

### Content Structure Guidelines

**For Message Tools:**

```javascript
newTool: {
  title: "Tool Name",
  description: "What the tool does",
  icon: "Icon description",
  usage: "Step-by-step usage instructions",
  note: "Important notes (optional)",
  availability: "Availability restrictions (optional)",
},
```

**For Prompt Input Features:**

```javascript
newFeature: {
  title: "Feature Name",
  description: "What the feature does",
  icon: "Icon description",
  usage: "Step-by-step usage instructions",
  shortcut: "Keyboard shortcut (optional)",
  requirements: "Prerequisites (optional)",
  availability: "Availability restrictions (optional)",
},
```

## Styling and UI

### CSS Classes and Styling

The user guide uses Tailwind CSS classes for consistent styling:

- **Modal**: Standard Modal component with proper spacing
- **Sections**: Expandable sections with hover effects
- **Content**: Proper typography hierarchy with responsive design
- **Labels**: Color-coded labels for different types of information:
  - Requirements: Orange text (`text-orange-600`)
  - Availability: Blue text (`text-blue-600`)
  - Note: Blue text (`text-blue-600`)
  - Shortcut: Green text (`text-green-600`)

### Responsive Design

The user guide is designed to work across all device sizes:

- **Desktop**: Full modal with all sections visible
- **Tablet**: Responsive layout with proper spacing
- **Mobile**: Optimized for smaller screens with touch-friendly interactions

## Accessibility

### Accessibility Features

- **Keyboard Navigation**: Full keyboard support for modal and sections
- **Focus Management**: Proper focus trapping within modal
- **Screen Reader Support**: Semantic HTML and ARIA attributes
- **Color Contrast**: Sufficient contrast for all text elements
- **Expandable Sections**: Clear indication of expanded/collapsed state

### ARIA Implementation

- Modal has proper `role="dialog"` and `aria-labelledby`
- Expandable sections use `aria-expanded` attribute
- All interactive elements have appropriate labels
- Focus is managed properly when modal opens/closes

## Performance Considerations

### Optimization Strategies

- **Lazy Loading**: Modal content is only rendered when opened
- **Memoization**: Translation keys are memoized to prevent unnecessary re-renders
- **Efficient State**: Minimal state management for section expansion
- **Icon Optimization**: Uses optimized phosphor icons

### Bundle Size Impact

- Translation files add approximately 15-20KB to the bundle
- Modal component is code-split and loaded on demand
- Icons are tree-shaken to include only used icons

## Testing

### Manual Testing Checklist

- [ ] Button appears in header across all pages
- [ ] Modal opens and closes properly
- [ ] All sections expand and collapse correctly
- [ ] Content displays properly in all 7 languages
- [ ] Responsive design works on all device sizes
- [ ] Keyboard navigation functions correctly
- [ ] Screen reader compatibility verified

### Automated Testing

```javascript
// Example test structure
describe("UseGuide Component", () => {
  test("renders button with correct text", () => {
    // Test button rendering and translation
  });

  test("opens modal when clicked", () => {
    // Test modal state management
  });

  test("displays all sections correctly", () => {
    // Test section rendering and expansion
  });

  test("handles translations properly", () => {
    // Test i18n integration
  });
});
```

## Maintenance

### Regular Updates Required

1. **Feature Updates**: When new features are added to the platform
2. **Translation Updates**: When UI text changes or new languages are added
3. **Content Reviews**: Periodic review to ensure accuracy and completeness
4. **Accessibility Audits**: Regular accessibility testing and improvements

### Common Issues and Solutions

**Issue**: Translation keys not displaying

- **Solution**: Check that new locale files are imported in `resources.js`

**Issue**: Modal not opening

- **Solution**: Verify Button component integration and state management

**Issue**: Content not responsive

- **Solution**: Review Tailwind classes and test across device sizes

**Issue**: Missing translations

- **Solution**: Ensure all translation keys exist in all language files

## Future Enhancements

### Potential Improvements

1. **Search Functionality**: Add search within the user guide
2. **Interactive Tours**: Guided tours for new users
3. **Video Integration**: Embed instructional videos
4. **Contextual Help**: Show relevant help based on current page
5. **Feedback System**: Allow users to rate help content
6. **Progressive Disclosure**: Show basic/advanced content levels

### Technical Considerations

- **Performance**: Monitor bundle size as content grows
- **Accessibility**: Ensure new features maintain accessibility standards
- **Internationalization**: Plan for additional languages
- **Content Management**: Consider CMS integration for easier updates

## Dependencies

### Required Packages

- `react`: ^18.2.0
- `react-i18next`: ^14.1.3
- `@phosphor-icons/react`: ^2.1.7
- `@radix-ui/react-slot`: ^1.1.2 (for Button component)

### Internal Dependencies

- `@/components/Button`: Project Button component
- `@/components/ui/Modal`: Project Modal component
- `@/utils/classes`: Utility for conditional classes
- Translation system and locale files

## Conclusion

The User Guide feature provides a comprehensive, accessible, and internationalized help system for the IST Legal platform. It follows established patterns in the codebase and provides a solid foundation for ongoing documentation needs. Regular maintenance and updates ensure it remains current and valuable for users across all supported languages and devices.
