export default {
  // ----------------------------
  // Data Connectors
  // ----------------------------
  dataConnectors: {
    github: {
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON> GitHub",
      description: "Importez un dépôt GitHub public ou privé en un seul clic.",
      url: "URL du dépôt GitHub",
      "collect-url": "URL du dépôt à collecter",
      "access-token": "Jeton d'accès GitHub",
      optional: "facultatif",
      "rate-limiting": "Jeton d'accès pour éviter la limitation de débit.",
      "desc-picker":
        "Une fois terminé, tous les fichiers seront disponibles pour être intégrés dans les espaces de travail via le sélecteur de documents.",
      branch: "Branche",
      "branch-desc": "Branche à partir de laquelle collecter les fichiers.",
      "branch-loading": "-- chargement des branches disponibles --",
      "desc-start": "Sans renseigner le",
      "desc-token": "jeton d'accès GitHub",
      "desc-connector":
        "ce connecteur ne pourra collecter que les fichiers de niveau supérieur en raison des limites de l'API publique GitHub.",
      "desc-level": "niveau supérieur",
      "desc-end": "",
      "personal-token":
        "Obtenez un jeton d'accès personnel gratuit avec un compte GitHub ici.",
      without: "Sans",
      "personal-token-access": "jeton d'accès personnel",
      "desc-api":
        ", l'API GitHub peut limiter le nombre de fichiers collectés. Vous pouvez",
      "temp-token": "créer un jeton temporaire",
      "avoid-issue": "pour éviter ce problème.",
      submit: "Soumettre",
      "collecting-files": "Collecte des fichiers...",
    },
    "youtube-transcript": {
      name: "Transcription YouTube",
      description:
        "Importez la transcription d'une vidéo YouTube complète à partir d'un lien.",
      url: "URL de la vidéo YouTube",
      "url-video": "URL de la vidéo à transcrire",
      collect: "Collecter la transcription",
      collecting: "Collecte de la transcription...",
      "desc-end":
        "Une fois terminé, la transcription sera disponible pour être intégrée via le sélecteur de documents.",
    },
    "website-depth": {
      name: "Extraction de Liens en Masse",
      description:
        "Scrutez un site web et ses sous-liens jusqu'à une certaine profondeur.",
      url: "URL du site web",
      "url-scrape": "URL du site à scruter",
      depth: "Profondeur",
      "child-links":
        "Nombre de sous-liens à suivre à partir de l'URL d'origine.",
      "max-links": "Liens maximum",
      "links-scrape": "Nombre maximum de liens à extraire",
      scraping: "Scrutation du site web...",
      submit: "Soumettre",
      "desc-scrap":
        "Une fois terminé, toutes les pages récupérées seront disponibles pour être intégrées dans les espaces de travail.",
    },
    confluence: {
      name: "Confluence",
      description: "Importez une page Confluence entière en un seul clic.",
      url: "URL de la page Confluence",
      "url-page": "URL d'une page dans l'espace Confluence",
      username: "Nom d'utilisateur Confluence",
      "own-username": "Votre nom d'utilisateur Confluence",
      token: "Jeton d'accès Confluence",
      "desc-start":
        "Vous devez fournir un jeton d'accès pour l'authentification. Vous pouvez générer un jeton",
      here: "ici",
      access: "Jeton d'accès pour l'authentification.",
      collecting: "Collecte des pages...",
      submit: "Soumettre",
      "desc-end":
        "Une fois terminé, toutes les pages seront disponibles pour l'intégration dans les espaces de travail.",
    },
  },
  // ----------------------------
  // Confluence Connector
  // ----------------------------
  confluence: {
    "space-key": "Clé d'espace Confluence",
    "space-key-desc":
      "C'est la clé de votre espace Confluence qui sera utilisée. Commence généralement par ~",
    "space-key-placeholder": "ex: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "ex: https://example.atlassian.net, http://localhost:8211, etc...",
    "token-tooltip": "Vous pouvez créer un jeton API",
    "token-tooltip-here": "ici",
  },
};
