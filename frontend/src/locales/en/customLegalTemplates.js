export default {
  // =========================
  // CUSTOM LEGAL TEMPLATES MODAL
  // =========================
  "custom-legal-templates": {
    "organization-label": "Organization",
    "user-label": "User",
    "modal-title": "Custom Legal Templates",
    "modal-description": "Manage your custom legal templates.",
    "add-new": "Add New Template",
    "no-templates":
      "No custom templates found. Create one by clicking the button above.",
    "fetch-org-error": "Failed to fetch organizations",
    "fetch-user-error": "Failed to fetch users",
    category: "Category",
    "document-type": "Document Type",
    actions: "Actions",
    "created-success": "Custom template created successfully",
    "creation-error": "Failed to create custom template",
    "deleted-success": "Custom template deleted successfully",
    "delete-error": "Failed to delete custom template",
    "updated-success": "Custom template updated successfully",
    "update-error": "Failed to update custom template",
    "create-title": "Create New Custom Template",
    "create-description":
      "Create a new custom template for generating legal documents.",
    "edit-title": "Edit Custom Template",
    "edit-description":
      "Edit an existing custom template for generating legal documents.",
    "new-category": "Create New Category",
    creating: "Creating...",
    updating: "Updating...",
    create: "Create Template",
    update: "Update Template",
    "existing-category": "Use Existing Category",
    "category-name": "Category Name",
    "category-placeholder": "Enter category name",
    "select-category": "Select Category",
    "document-type-name": "Document Type Name",
    "document-type-placeholder": "Enter document type name",
    "template-description": "Template detailed description (optional)",
    "template-description-placeholder":
      "Provide detailed description of the document type and the purpose of the template",
    "template-content": "Template Content (optional)",
    "template-content-placeholder":
      "Add specific content examples and instructions to apply when generating the document. This can include both instructions and actual template content. You can upload an existing document with the upload DOCX button.",
    "template-formatting": "Template Formatting (optional)",
    "template-formatting-placeholder":
      "Add specific formatting examples and instructions for document structure. This will be used as a formatting guide for the AI. Upload a DOCX file with the specific formatting and document structure you want to apply to the generated document.",
    "category-required": "Category is required",
    "document-type-required": "Document type is required",
    "template-content-required": "Template content is required",
    "confirm-delete":
      "Are you sure you want to delete this template? This action cannot be undone.",
    "enable-custom-input-fields": "Enable custom input fields",
    "custom-input-placeholder": "Custom input field {{number}}",
    "add-custom-input": "Add additional input field",
    "delete-custom-input": "Delete",
  },
};
