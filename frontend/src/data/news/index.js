/**
 * System News Items Index
 *
 * This file imports and exports all system news items.
 * When adding new system news, import the file here and add it to the array.
 */

// Import all system news items
import welcome2024 from "./2024-01-welcome.js";
import newFeatures2025 from "./2025-05-new-features.js";

// Export array of all system news items
export const systemNewsItems = [
  welcome2024,
  newFeatures2025,
  // Add new system news items here
];

// Helper function to resolve translations for system news
export const resolveSystemNewsTranslations = (newsItem, t) => {
  const resolved = { ...newsItem };

  // Resolve title
  if (newsItem.titleKey) {
    resolved.title = t(newsItem.titleKey, newsItem.title || newsItem.titleKey);
  }

  // Resolve content
  if (newsItem.contentKey) {
    resolved.content = t(
      newsItem.contentKey,
      newsItem.content || newsItem.contentKey
    );
  }

  return resolved;
};

// Helper function to get active system news with translations
export const getActiveSystemNews = (t = null) => {
  const now = new Date();

  const activeNews = systemNewsItems.filter((item) => {
    // Check if item is active
    if (!item.isActive) return false;

    // Check if item has expired
    if (item.expiresAt && new Date(item.expiresAt) < now) return false;

    return true;
  });

  // If translation function is provided, resolve translations
  if (t) {
    return activeNews.map((item) => resolveSystemNewsTranslations(item, t));
  }

  return activeNews;
};

// Helper function to get system news for specific user roles with translations
export const getSystemNewsForRoles = (userRoles = [], t = null) => {
  const activeNews = getActiveSystemNews(t);

  return activeNews.filter((item) => {
    // If no target roles specified, show to all users
    if (!item.targetRoles || item.targetRoles.length === 0) return true;

    // Check if user has any of the target roles
    return userRoles.some((role) => item.targetRoles.includes(role));
  });
};

// Helper function to get system news by ID with translations
export const getSystemNewsById = (id, t = null) => {
  const newsItem = systemNewsItems.find((item) => item.id === id);
  if (!newsItem) return null;

  if (t) {
    return resolveSystemNewsTranslations(newsItem, t);
  }

  return newsItem;
};

// Helper function to get system news by priority with translations
export const getSystemNewsByPriority = (priority, t = null) => {
  return getActiveSystemNews(t).filter((item) => item.priority === priority);
};

export default systemNewsItems;
