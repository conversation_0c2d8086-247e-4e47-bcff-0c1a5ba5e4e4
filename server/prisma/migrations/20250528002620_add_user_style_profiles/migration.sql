/*
  Lawyer Individualizer Feature Migration

  This migration creates the UserStyleProfile table to support the Lawyer Individualizer feature.
  The feature allows users to upload document samples for style analysis and generate personalized
  style instructions that are automatically applied to AI responses.

  Table: UserStyleProfile
  - Stores user-specific writing style profiles
  - Links to users table with CASCADE DELETE for data integrity
  - Includes indexes for performance optimization
  - Supports multiple profiles per user with one active profile
*/

-- CreateTable
CREATE TABLE IF NOT EXISTS "UserStyleProfile" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "user_id" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "instructions" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "UserStyleProfile_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateIndex
CREATE INDEX IF NOT EXISTS "UserStyleProfile_user_id_idx" ON "UserStyleProfile"("user_id");

-- CreateIndex
CREATE INDEX IF NOT EXISTS "UserStyleProfile_user_id_is_active_idx" ON "UserStyleProfile"("user_id", "is_active");
