// Mock baseHeaders first, before importing Version
jest.mock("@/utils/request", () => ({
  baseHeaders: () => ({ Authorization: "Bearer mock-token" }),
}));

import Version from "../version";
import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";

// Mock fetch globally
global.fetch = jest.fn();

describe("Version Model", () => {
  beforeEach(() => {
    fetch.mockClear();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe("getVersion", () => {
    it("should return version data when API call is successful", async () => {
      const mockVersionData = {
        success: true,
        version: "1.1.0",
        description:
          "Added new document builder features and improved chat performance",
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockVersionData,
      });

      const result = await Version.getVersion();

      expect(fetch).toHaveBeenCalledWith(`${API_BASE}/version`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer mock-token",
        },
      });

      expect(result).toEqual({
        version: "1.1.0",
        description:
          "Added new document builder features and improved chat performance",
      });
    });

    it("should return null when API response is not ok", async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
      });

      const consoleSpy = jest
        .spyOn(console, "error")
        .mockImplementation(() => {});

      const result = await Version.getVersion();

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to fetch version information"
      );

      consoleSpy.mockRestore();
    });

    it("should return null when API response success is false", async () => {
      const mockErrorResponse = {
        success: false,
        error: "Version file not found",
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockErrorResponse,
      });

      const result = await Version.getVersion();

      expect(result).toBeNull();
    });

    it("should return null and log error when fetch throws an exception", async () => {
      const mockError = new Error("Network error");
      fetch.mockRejectedValueOnce(mockError);

      const consoleSpy = jest
        .spyOn(console, "error")
        .mockImplementation(() => {});

      const result = await Version.getVersion();

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith(
        "Error fetching version:",
        mockError
      );

      consoleSpy.mockRestore();
    });

    it("should return null when JSON parsing fails", async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => {
          throw new Error("Invalid JSON");
        },
      });

      const consoleSpy = jest
        .spyOn(console, "error")
        .mockImplementation(() => {});

      const result = await Version.getVersion();

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith(
        "Error fetching version:",
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });

    it("should handle missing version or description in response", async () => {
      const mockIncompleteData = {
        success: true,
        version: "1.0.0",
        // description is missing
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockIncompleteData,
      });

      const result = await Version.getVersion();

      expect(result).toEqual({
        version: "1.0.0",
        description: undefined,
      });
    });

    it("should use correct API endpoint", async () => {
      const mockVersionData = {
        success: true,
        version: "1.0.0",
        description: "Test version",
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockVersionData,
      });

      await Version.getVersion();

      expect(fetch).toHaveBeenCalledWith(
        `${API_BASE}/version`,
        expect.objectContaining({
          method: "GET",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
            Authorization: "Bearer mock-token",
          }),
        })
      );
    });
  });
});
