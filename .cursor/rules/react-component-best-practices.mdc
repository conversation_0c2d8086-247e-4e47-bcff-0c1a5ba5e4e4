---
description: Best practices for React component development
globs: "{frontend/src/**/*.{js,jsx}}"
alwaysApply: true
---

# React Component Best Practices

## Context Definitions

### Separate Context Definitions from Components

React's Fast Refresh feature only works correctly when a file only exports components. To ensure optimal development experience with hot reloading:

1. **Create separate files for context definitions**:

```javascript
// BAD: Defining and exporting context in the same file as components
// file: MyComponent.jsx
import { createContext } from 'react';

export const MyContext = createContext();

export function MyComponent() {
  // Component code...
}
```

```javascript
// GOOD: Separate context definition into its own file
// file: context.js
import { createContext } from 'react';

export const MyContext = createContext();

// file: MyComponent.jsx
import { MyContext } from './context';

export function MyComponent() {
  // Component code...
}
```

2. **Benefits of this approach**:
   - Enables Fast Refresh to work correctly
   - Improves code organization and separation of concerns
   - Makes it easier to track context usage across the application
   - Reduces the risk of circular dependencies

3. **Naming conventions**:
   - Context files should be named descriptively (e.g., `userContext.js`, `themeContext.js`)
   - For component-specific contexts, use `context.js` in the component's directory

## Component Structure

### Keep Components Focused

1. **Single Responsibility Principle**:
   - Each component should have a single responsibility
   - Extract complex logic into custom hooks or utility functions

2. **Component Size**:
   - If a component file exceeds 300 lines, consider splitting it
   - Extract reusable parts into separate components

3. **Props Handling**:
   - Use prop destructuring for clarity
   - Provide default values for optional props
   - Use PropTypes or TypeScript for type checking

## Performance Optimization

### Memoization

1. **Use React.memo for pure functional components**:
   - Wrap components with React.memo when they render the same result given the same props

2. **Use useMemo for expensive calculations**:
   - Memoize values that are expensive to compute

3. **Use useCallback for event handlers passed to child components**:
   - Memoize callback functions to prevent unnecessary re-renders

## State Management

### Use Appropriate State Management

1. **Local Component State**:
   - Use useState for component-specific state
   - Use useReducer for complex state logic

2. **Shared State**:
   - Use context for state shared between related components
   - Use Zustand for application-wide state management

3. **State Updates**:
   - Use functional updates for state that depends on previous state
   - Batch related state updates together
