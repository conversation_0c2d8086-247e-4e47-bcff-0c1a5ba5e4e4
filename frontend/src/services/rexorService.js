import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";
import { t } from "i18next";

export const loginToRexor = async (username, password) => {
  try {
    const body = new URLSearchParams({
      username,
      password,
    }).toString();

    const response = await fetch(`${API_BASE}/system/rexor/auth`, {
      method: "POST",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData?.error || "Failed to login to <PERSON><PERSON>");
    }

    const data = await response.json();
    if (!data.access_token) {
      throw new Error("No access token in response");
    }

    return {
      success: true,
      access_token: data.access_token,
    };
  } catch (error) {
    console.error("Error logging into Rexor:", error);
    throw error;
  }
};

export const registerProject = async (projectData, accessToken) => {
  try {
    const response = await fetch(`${API_BASE}/system/rexor/projects`, {
      method: "POST",
      headers: {
        ...baseHeaders(),
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        access_token: accessToken,
        projectData: {
          ApiTable: "TProjectAll p",
          ApiSelect: `
            p.UID AS 'ProjectUID', p.ID AS 'ProjectID', p.Description AS 'ProjectDescription',
            r.*,
            pa.*,
            a.*,
            p.Status AS 'ProjectStatus'
          `
            .trim()
            .replace(/\s+/g, " "),
          ApiJoin: `
            CROSS JOIN (
                SELECT TOP 1 r.[UID] AS 'ResourceUID', r.[ID] AS 'ResourceID', r.[Name] AS 'ResourceName'
                FROM TResource r
                WHERE r.[ID] = '${projectData.ResourceID}'
            ) r
            CROSS APPLY (
                SELECT TOP 1 pa.[UID] AS 'ProjectActivityUID', pa.[ID] AS 'ProjectActivityID', pa.[Description] AS 'ProjectActivityDescription'
                FROM TProjectActivity pa
                INNER JOIN TProjectActivityMember pam ON pam.[ProjectUID] = p.[UID]
                WHERE pa.[ID] = dbo.GetValueFromPropertyBag('Api', 'Foynet', 'ProjectActivityID')
            ) pa
            CROSS APPLY (
                SELECT TOP 1 a.[UID] AS 'ArticleUID', a.[ID] AS 'ArticleID', a.[Description] AS 'ArticleDescription'
                FROM TArticle a
                WHERE a.[ID] = dbo.GetValueFromPropertyBag('Api', 'Foynet', 'ArticleID')
            ) a
          `
            .trim()
            .replace(/\s+/g, " "),
          ApiWhere: `ID = '${projectData.ProjectID}'`,
        },
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      console.error("Error response:", error);
      throw new Error(error.error || "Failed to register project");
    }

    return await response.json();
  } catch (error) {
    console.error("Error registering project:", error.message);
    throw error;
  }
};

export const getInvoiceStatus = async (transactionUID, accessToken) => {
  try {
    const response = await fetch(
      `${API_BASE}/system/rexor/transaction-status/${transactionUID}`,
      {
        method: "GET",
        headers: {
          ...baseHeaders(),
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to get invoice status");
    }

    return await response.json();
  } catch (error) {
    console.error("Error getting invoice status:", error.message);
    throw error;
  }
};

export const writeArticleTransaction = async (transactionData, accessToken) => {
  const invoiceText = transactionData.InvoiceText || t("rexor.invoice-text");
  try {
    const response = await fetch(
      `${API_BASE}/system/rexor/article-expense-transaction`,
      {
        method: "POST",
        headers: {
          ...baseHeaders(),
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          access_token: accessToken,
          transactionData: {
            ...transactionData,
            RegistrationDate: new Date().toISOString().split("T")[0],
            InvoiceText: invoiceText,
            Number: Number(transactionData.Number || 1),
            InvoicedNumber: Number(transactionData.InvoicedNumber || 1),
            Invoiceable: Number(transactionData.Invoiceable || 1),
            Origin: Number(transactionData.Origin || 0),
            InvoiceStatus: "ReadyForInvoiceBasis",
            Status: 2,
          },
        }),
      }
    );

    const responseData = await response.json();
    console.log("rexor response for article transaction", responseData);

    if (!response.ok) {
      throw new Error(
        responseData.error || "Failed to write article transaction"
      );
    }
    transactionData.UID = responseData.UID;

    try {
      const projectData = JSON.parse(
        localStorage.getItem("rexor_registered_project") || "{}"
      );
      projectData.UID = responseData.UID;
      localStorage.setItem(
        "rexor_registered_project",
        JSON.stringify(projectData)
      );
    } catch (e) {
      console.error("Error updating localStorage with transaction UID:", e);
    }

    return responseData;
  } catch (error) {
    console.error("Error writing time transaction:", error.message);
    throw error;
  }
};

export const updateArticleTransaction = async (projectDetails, accessToken) => {
  try {
    if (projectDetails.UID) {
      const invoiceStatus = await getInvoiceStatus(
        projectDetails.UID,
        accessToken
      );

      const currentTransaction = Array.isArray(invoiceStatus)
        ? invoiceStatus.length > 0
          ? invoiceStatus[0]
          : null
        : invoiceStatus;

      if (currentTransaction) {
        if (currentTransaction.InvoiceStatus === "ReadyForInvoiceBasis") {
          const updatedTransactionData = {
            ...currentTransaction,
            Number: Number(currentTransaction.Number) + 1,
            InvoicedNumber: Number(currentTransaction.InvoicedNumber) + 1,
          };

          const response = await writeArticleTransaction(
            updatedTransactionData,
            accessToken
          );
          return response;
        }
      }
    }
  } catch (error) {
    console.error("Error managing Rexor transaction:", error);
    throw error;
  }
};
