import { CloudArrowUp } from "@phosphor-icons/react";
import { useEffect, useState, useCallback } from "react";
import showToast from "../../../../../utils/toast";
import System from "../../../../../models/system";
import { useDropzone } from "react-dropzone";
import { v4 as uuidv4 } from "uuid";
import FileUploadProgress from "./FileUploadProgress";
import Workspace from "../../../../../models/workspace";
import debounce from "lodash.debounce";
import { useTranslation } from "react-i18next";
import { useDocumentDraftingEnabled } from "@/stores/settingsStore";
import Input from "@/components/ui/Input";
import { Button } from "@/components/Button";

export default function UploadFile({
  workspace,
  fetchKeys,
  loading,
  setLoading,
  setLoadingMessage,
  setIsUpload,
  documentDraftingSelected,
  onFileChange,
}) {
  const { isDocumentDrafting } = useDocumentDraftingEnabled();
  const [ready, setReady] = useState(false);
  const [files, setFiles] = useState([]);
  const [fetchingUrl, setFetchingUrl] = useState(false);
  const [failedUploads, setFailedUploads] = useState([]);
  const [tokenStats, setTokenStats] = useState({
    tokenCount: 0,
    promptLimit: 0,
  });

  const { t } = useTranslation();

  const handleSendLink = async (e) => {
    e.preventDefault();
    setLoading(true);
    setLoadingMessage(t("modale.document.scraping-link", "Scraping link..."));
    setFetchingUrl(true);
    const formEl = e.target;
    const form = new FormData(formEl);
    const { response, data } = await Workspace.uploadLink(
      workspace.slug,
      form.get("link")
    );
    if (!response.ok) {
      showToast(
        t("show-toast.link-upload-error", { error: data.error }),
        "error"
      );
    } else {
      fetchKeys(true, documentDraftingSelected);
      showToast(t("show-toast.link-upload-success"), "success");
      formEl.reset();
    }
    setLoading(false);
    setFetchingUrl(false);
    setIsUpload(true);
  };

  // Don't spam fetchKeys, wait 1s between calls at least.
  const handleUploadSuccess = debounce(
    () => fetchKeys(true, documentDraftingSelected),
    1000
  );

  const fetchTokenStats = useCallback(async () => {
    if (!documentDraftingSelected || !workspace?.slug) return null;
    try {
      const data = await Workspace.getTokenCount(workspace.slug);
      if (data) {
        setTokenStats({
          tokenCount: data.tokenCount,
          promptLimit: data.promptLimit,
        });
        onFileChange?.(data);
        return data;
      }
      return null;
    } catch (error) {
      console.error("Error fetching token stats:", error);
      return null;
    }
  }, [workspace?.slug, documentDraftingSelected, onFileChange]);

  useEffect(() => {
    fetchTokenStats();
  }, [workspace.slug, documentDraftingSelected, onFileChange, fetchTokenStats]);

  useEffect(() => {
    if (documentDraftingSelected) {
      fetchTokenStats();
    }
  }, [files, documentDraftingSelected, fetchTokenStats]);

  // Add a forced refresh of token stats when component is visible
  useEffect(() => {
    if (documentDraftingSelected) {
      // Set up an interval to refresh token stats regularly when component is mounted
      const intervalId = setInterval(() => {
        fetchTokenStats();
      }, 2000); // Refresh every 2 seconds

      // Cleanup interval on unmount
      return () => clearInterval(intervalId);
    }
  }, [documentDraftingSelected, fetchTokenStats]);

  const handleUploadError = useCallback((file) => {
    setFailedUploads((prev) => [
      ...prev,
      { fileName: file.originalname, error: file.error },
    ]);
  }, []);

  const onDrop = useCallback(
    (acceptedFiles, rejections) => {
      setFailedUploads([]);
      const existingFiles = files.map((f) => `${f.file.name}-${f.file.size}`);

      const newAccepted = acceptedFiles
        .filter((file) => !existingFiles.includes(`${file.name}-${file.size}`))
        .map((file) => ({
          uid: uuidv4(),
          file,
        }));
      const newRejected = rejections.map((file) => ({
        uid: uuidv4(),
        file: file.file,
        rejected: true,
        reason: file.errors[0].code,
      }));
      setFiles((prevFiles) => [...prevFiles, ...newAccepted, ...newRejected]);
    },
    [files]
  );

  const handleFileRemoval = useCallback(
    (uid) => {
      setFiles((prevFiles) => {
        const newFiles = prevFiles.filter((file) => file.uid !== uid);
        // Schedule token count update for next tick after state update
        setTimeout(() => {
          if (documentDraftingSelected) {
            fetchTokenStats();
          }
        }, 0);
        return newFiles;
      });
    },
    [documentDraftingSelected, fetchTokenStats]
  );

  useEffect(() => {
    async function checkProcessorOnline() {
      const online = await System.checkDocumentProcessorOnline();
      setReady(online);
    }
    checkProcessorOnline();
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    disabled: !ready,
  });

  return (
    <div>
      <div
        className={`w-full max-w-[560px] border-2 border-dashed border-border bg-elevated rounded-lg p-6 transition-colors ${
          ready
            ? "cursor-pointer hover:border-primary hover:bg-secondary/50"
            : "cursor-not-allowed opacity-60"
        }`}
        {...getRootProps()}
      >
        <input
          {...getInputProps()}
          accept={
            isDocumentDrafting ? "application/pdf,image/*" : "application/pdf"
          }
        />
        {ready === false ? (
          <div className="flex flex-col items-center justify-center h-full min-h-[120px]">
            <CloudArrowUp className="w-8 h-8 text-muted mb-3" />
            <div className="text-foreground text-sm font-semibold mb-2">
              {t("modale.document.doc-processor")}
            </div>
            <div className="text-muted text-xs font-medium text-center max-w-xs">
              {t("modale.document.processor-offline")}
            </div>
          </div>
        ) : files.length === 0 ? (
          <div className="flex flex-col items-center justify-center min-h-[120px]">
            <CloudArrowUp className="w-8 h-8 text-primary mb-3" />
            <div className="text-foreground text-sm font-semibold mb-2">
              {t("modale.document.drag-drop")}
            </div>
            <div className="text-muted text-xs font-medium">
              {t("modale.document.supported-files")}
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-3 max-h-[180px] overflow-y-auto">
            {files.map(function (file) {
              return (
                <FileUploadProgress
                  key={file.uid}
                  file={file.file}
                  uuid={file.uid}
                  setFiles={handleFileRemoval}
                  slug={workspace.slug}
                  workspace={workspace}
                  rejected={file?.rejected}
                  reason={file?.reason}
                  onUploadSuccess={handleUploadSuccess}
                  onUploadError={handleUploadError}
                  loading={loading}
                  setLoading={setLoading}
                  setLoadingMessage={setLoadingMessage}
                  setIsUpload={setIsUpload}
                  isModalUpload={true}
                  fetchTokenStats={
                    documentDraftingSelected ? fetchTokenStats : undefined
                  }
                  onFileChange={onFileChange}
                />
              );
            })}
          </div>
        )}
        {failedUploads.length > 0 && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <h3 className="text-red-600 font-semibold text-sm mb-2">
              {t("modale.document.failed-uploads")}
            </h3>
            <div className="max-h-[100px] overflow-y-auto space-y-1">
              {failedUploads.map((failedFile, index) => (
                <div key={index} className="text-xs text-red-500">
                  {index + 1}. {failedFile.fileName}: {failedFile.error}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      {!documentDraftingSelected && (
        <div className="mt-4">
          <div className="text-center text-muted text-xs font-medium mb-3">
            {t("modale.document.submit-link")}
          </div>
          <form onSubmit={handleSendLink} className="flex gap-2">
            <Input
              disabled={fetchingUrl}
              name="link"
              type="url"
              placeholder="https://example.com"
              autoComplete="off"
              className="flex-1"
            />
            <Button
              disabled={fetchingUrl}
              type="submit"
              size="sm"
              isLoading={fetchingUrl}
            >
              {fetchingUrl
                ? t("modale.document.justify-betweening")
                : t("modale.document.fetch")}
            </Button>
          </form>
          <div className="mt-3 text-left text-muted text-xs">
            {t("modale.document.file-desc")}
          </div>
        </div>
      )}
      {documentDraftingSelected && (
        <div className="mt-4">
          <div className="bg-elevated border border-border rounded-lg p-4">
            <div className="flex justify-between text-sm text-muted mb-2">
              <span>
                {t("workspace-chats.total-tokens")}:{" "}
                {tokenStats.tokenCount.toLocaleString()}
              </span>
              <span>
                {t("workspace-chats.context-window")}:{" "}
                {tokenStats.promptLimit.toLocaleString()}
              </span>
            </div>
            <div className="w-full bg-border rounded-full h-2">
              <div
                className="h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${Math.min((tokenStats.tokenCount / tokenStats.promptLimit) * 100, 100)}%`,
                  backgroundColor:
                    tokenStats.tokenCount > tokenStats.promptLimit
                      ? "#ef4444"
                      : "#2563eb",
                }}
              />
            </div>
            {tokenStats.tokenCount > tokenStats.promptLimit && (
              <div className="text-red-500 text-xs mt-3 p-2 bg-red-50 border border-red-200 rounded">
                {t(
                  "modale.document.exceeds-prompt-limit",
                  "Note: The uploaded content exceeds what can fit with one prompt. The system will process any requests by multiple prompts which will increase the time for generating the answer, and precision may be affected."
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
