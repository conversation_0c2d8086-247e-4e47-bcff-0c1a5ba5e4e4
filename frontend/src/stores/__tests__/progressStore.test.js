import { act, renderHook } from "@testing-library/react";
import useProgressStore from "../progressStore";

// Mock thread IDs for tests
const MOCK_THREAD_ID = "thread-123";
const MOCK_THREAD_ID_2 = "thread-456";

describe("progressStore", () => {
  beforeEach(() => {
    // Reset the store state completely before each test
    act(() => {
      useProgressStore.setState({ threads: new Map() });
    });

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe("basic state management", () => {
    it("should initialize with empty threads Map", () => {
      const { result } = renderHook(() => useProgressStore());
      expect(result.current.threads).toEqual(new Map());
    });

    it("should start a process with default parameters", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState).toBeDefined();
      expect(threadState.isActive).toBe(true);
      expect(threadState.currentStep).toBe(1);
      expect(threadState.totalSteps).toBe(7);
      expect(threadState.startTime).toBeGreaterThan(0);
      expect(threadState.flowType).toBeNull();
      expect(threadState.stepStatus).toBe("pending");
      expect(threadState.stepDetails).toEqual([]);
      expect(threadState.abortController).toBeInstanceOf(AbortController);
    });

    it("should start a process with custom parameters", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID, 5, "cdb");
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState.totalSteps).toBe(5);
      expect(threadState.flowType).toBe("cdb");
    });

    it("should abort existing process when starting a new one for the same thread", () => {
      const { result } = renderHook(() => useProgressStore());

      // Start first process
      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      const firstController =
        result.current.threads.get(MOCK_THREAD_ID).abortController;
      const abortSpy = jest.spyOn(firstController, "abort");

      // Start second process for same thread
      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      expect(abortSpy).toHaveBeenCalled();

      // Verify new controller is different
      const secondController =
        result.current.threads.get(MOCK_THREAD_ID).abortController;
      expect(secondController).not.toBe(firstController);
    });

    it("should ignore invalid thread slugs in startProcess", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(null);
        result.current.startProcess("");
        result.current.startProcess(123);
      });

      expect(result.current.threads.size).toBe(0);
    });
  });

  describe("progress updates", () => {
    beforeEach(() => {
      const { result } = renderHook(() => useProgressStore());
      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });
    });

    it("should update progress with step information", () => {
      const { result } = renderHook(() => useProgressStore());

      const updateEvent = {
        step: 2,
        status: "in_progress",
        message: "Processing step 2",
      };

      act(() => {
        result.current.updateProgress(updateEvent, MOCK_THREAD_ID);
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState.currentStep).toBe(2);
      expect(threadState.stepStatus).toBe("in_progress");
      expect(threadState.stepMessage).toBe("Processing step 2");
      expect(threadState.lastUpdate).toBeGreaterThan(0);
    });

    it("should track step details", () => {
      const { result } = renderHook(() => useProgressStore());

      const updateEvent = {
        step: 1,
        status: "in_progress",
        message: "Starting step 1",
      };

      act(() => {
        result.current.updateProgress(updateEvent, MOCK_THREAD_ID);
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState.stepDetails).toHaveLength(1);
      expect(threadState.stepDetails[0]).toMatchObject({
        step: 1,
        status: "in_progress",
        message: "Starting step 1",
        subTasks: [],
      });
    });

    it("should handle sub-tasks", () => {
      const { result } = renderHook(() => useProgressStore());

      const updateEvent = {
        step: 1,
        subStep: 1,
        status: "in_progress",
        message: "Sub-task 1",
      };

      act(() => {
        result.current.updateProgress(updateEvent, MOCK_THREAD_ID);
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState.stepDetails[0].subTasks).toHaveLength(1);
      expect(threadState.stepDetails[0].subTasks[0]).toMatchObject({
        subStep: 1,
        status: "in_progress",
        message: "Sub-task 1",
      });
    });

    it("should ignore malformed update events", () => {
      const { result } = renderHook(() => useProgressStore());

      const initialState = result.current.threads.get(MOCK_THREAD_ID);

      act(() => {
        result.current.updateProgress({ status: "test" }, MOCK_THREAD_ID); // Missing step
        result.current.updateProgress(null, MOCK_THREAD_ID); // Null event
      });

      const finalState = result.current.threads.get(MOCK_THREAD_ID);
      expect(finalState.currentStep).toBe(initialState.currentStep);
    });

    it("should ignore updates for inactive threads", () => {
      const { result } = renderHook(() => useProgressStore());

      // Cancel the process first
      act(() => {
        result.current.cancelProcess(MOCK_THREAD_ID);
      });

      const updateEvent = { step: 2, status: "in_progress" };

      act(() => {
        result.current.updateProgress(updateEvent, MOCK_THREAD_ID);
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState.currentStep).toBe(1); // Should not update
    });
  });

  describe("process lifecycle", () => {
    it("should finish and remove a process", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);

      act(() => {
        result.current.finishProcess(MOCK_THREAD_ID);
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(false);
    });

    it("should cancel a process", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      const controller =
        result.current.threads.get(MOCK_THREAD_ID).abortController;
      const abortSpy = jest.spyOn(controller, "abort");

      act(() => {
        result.current.cancelProcess(MOCK_THREAD_ID);
      });

      expect(abortSpy).toHaveBeenCalled();

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState.isActive).toBe(false);
      expect(threadState.stepStatus).toBe("error");
    });

    it("should set error state", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      act(() => {
        result.current.setError(MOCK_THREAD_ID, "Test error message");
      });

      const threadState = result.current.threads.get(MOCK_THREAD_ID);
      expect(threadState.isActive).toBe(false);
      expect(threadState.stepStatus).toBe("error");
      expect(threadState.error).toBe("Test error message");
    });

    it("should clear error and remove thread", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
        result.current.setError(MOCK_THREAD_ID, "Test error");
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);

      act(() => {
        result.current.clearError(MOCK_THREAD_ID);
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(false);
    });
  });

  describe("utility methods", () => {
    it("should get abort controller for a thread", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      const controller = result.current.getAbortController(MOCK_THREAD_ID);
      expect(controller).toBeInstanceOf(AbortController);
    });

    it("should return null for non-existent thread controller", () => {
      const { result } = renderHook(() => useProgressStore());

      const controller = result.current.getAbortController("non-existent");
      expect(controller).toBeNull();
    });

    it("should get thread state", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      const threadState = result.current.getThreadState(MOCK_THREAD_ID);
      expect(threadState).toBeDefined();
      expect(threadState.isActive).toBe(true);
    });

    it("should return null for non-existent thread state", () => {
      const { result } = renderHook(() => useProgressStore());

      const threadState = result.current.getThreadState("non-existent");
      expect(threadState).toBeNull();
    });

    it("should force cleanup a thread", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);

      act(() => {
        result.current.forceCleanup(MOCK_THREAD_ID);
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(false);
    });
  });

  describe("cleanup functionality", () => {
    it("should cleanup stale processes", () => {
      const { result } = renderHook(() => useProgressStore());

      // Create a process first
      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      // Then manually set an old start time
      act(() => {
        const threads = new Map(result.current.threads);
        const threadState = threads.get(MOCK_THREAD_ID);
        if (threadState) {
          threadState.startTime = Date.now() - 11 * 60 * 1000;
          threads.set(MOCK_THREAD_ID, threadState);
          useProgressStore.setState({ threads });
        }
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);

      act(() => {
        result.current.cleanupStaleProcesses(10); // 10 minutes max age
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(false);
    });

    it("should not cleanup recent processes", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);

      act(() => {
        result.current.cleanupStaleProcesses(10); // 10 minutes max age
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(true);
    });
  });

  describe("multiple threads", () => {
    it("should handle multiple independent threads", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
        result.current.startProcess(MOCK_THREAD_ID_2, 5, "different");
      });

      expect(result.current.threads.size).toBe(2);

      const thread1 = result.current.threads.get(MOCK_THREAD_ID);
      const thread2 = result.current.threads.get(MOCK_THREAD_ID_2);

      expect(thread1.totalSteps).toBe(7);
      expect(thread1.flowType).toBeNull();
      expect(thread2.totalSteps).toBe(5);
      expect(thread2.flowType).toBe("different");
    });

    it("should update threads independently", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
        result.current.startProcess(MOCK_THREAD_ID_2);
      });

      act(() => {
        result.current.updateProgress(
          { step: 2, status: "in_progress" },
          MOCK_THREAD_ID
        );
        result.current.updateProgress(
          { step: 3, status: "complete" },
          MOCK_THREAD_ID_2
        );
      });

      const thread1 = result.current.threads.get(MOCK_THREAD_ID);
      const thread2 = result.current.threads.get(MOCK_THREAD_ID_2);

      expect(thread1.currentStep).toBe(2);
      expect(thread1.stepStatus).toBe("in_progress");
      expect(thread2.currentStep).toBe(3);
      expect(thread2.stepStatus).toBe("complete");
    });

    it("should finish threads independently", () => {
      const { result } = renderHook(() => useProgressStore());

      act(() => {
        result.current.startProcess(MOCK_THREAD_ID);
        result.current.startProcess(MOCK_THREAD_ID_2);
      });

      act(() => {
        result.current.finishProcess(MOCK_THREAD_ID);
      });

      expect(result.current.threads.has(MOCK_THREAD_ID)).toBe(false);
      expect(result.current.threads.has(MOCK_THREAD_ID_2)).toBe(true);
    });
  });

  describe("error handling", () => {
    it("should handle invalid thread slugs gracefully", () => {
      const { result } = renderHook(() => useProgressStore());

      // These should not throw errors
      act(() => {
        result.current.updateProgress({ step: 1 }, null);
        result.current.finishProcess("");
        result.current.cancelProcess(undefined);
        result.current.setError(123, "error");
        result.current.clearError();
      });

      expect(result.current.threads.size).toBe(0);
    });

    it("should handle operations on non-existent threads", () => {
      const { result } = renderHook(() => useProgressStore());

      // These should not throw errors
      act(() => {
        result.current.updateProgress({ step: 1 }, "non-existent");
        result.current.finishProcess("non-existent");
        result.current.cancelProcess("non-existent");
        result.current.setError("non-existent", "error");
        result.current.clearError("non-existent");
      });

      expect(result.current.threads.size).toBe(0);
    });
  });
});
