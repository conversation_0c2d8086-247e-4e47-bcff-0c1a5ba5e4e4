const translations = {
  common: {
    thinking: "Thinking...",
  },
  errors: {
    auth: {
      "authentication-required": "Authentication required",
      "insufficient-permissions": "Insufficient permissions",
    },
    ai: {
      ollama: {
        "connection-failed":
          "Your Ollama instance could not be reached or is not responding. Please make sure it is running the API server and your connection information is correct.",
        "empty-response": "Ollama text response was empty.",
        "stream-failed": "Could not stream chat. {{message}}",
        "no-endpoint": "No Ollama API endpoint was set.",
        "no-token-limit": "No Ollama token context limit was set.",
        "provider-not-responding":
          "The Local AI engine is not responding, either due to server being offline or overloaded.",
        "provider-unavailable":
          "The Local AI server is currently unavailable. Please check installation and make sure it's running and not overloaded.",
      },
      azure: {
        "no-endpoint": "No Azure API endpoint was set.",
        "no-key": "No Azure API key was set.",
        "no-model": "No Azure OpenAI model was set.",
        "no-model-pref":
          "No OPEN_MODEL_PREF ENV defined. This must be the name of a deployment on your Azure account for an LLM chat model like GPT-3.5.",
      },
      openai: {
        "no-key": "No OpenAI API key was set.",
      },
      mistral: {
        "no-key": "No Mistral API key was set.",
      },
      litellm: {
        "no-base-path":
          "LiteLLM must have a valid base path to use for the api.",
        "no-model": "LiteLLM must have a valid model set.",
        "no-token-limit": "No token context limit was set.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI must have a valid base path to use for the api.",
        "no-token-limit": "No token context limit was set.",
      },
      localai: {
        "no-token-limit": "No LocalAi token context limit was set.",
      },
      lmstudio: {
        "no-token-limit": "No LMStudio token context limit was set.",
      },
      fireworks: {
        "invalid-model":
          "FireworksAI chat: {{model}} is not valid for chat completion!",
      },
      context: {
        header: "Source(s)",
      },
    },
    system: {
      "ssl-boot-failed":
        "SSL Boot Failed: {{message}} - falling back to HTTP boot.",
      "no-app": 'No "app" defined - crashing!',
      validation: {
        "invalid-transcription-provider":
          "{{input}} is not a valid transcription model provider.",
        "invalid-safety-setting":
          "Invalid Safety setting. Must be one of {{modes}}.",
        "invalid-model-type": "Invalid Model type. Must be one of {{models}}.",
        "invalid-embedding-model":
          "Invalid Embedding model type. Must be one of {{supported}}.",
      },
    },
    document: {
      "sync-failed":
        "Failed to sync document {{filename}} after {{attempts}} attempts.",
      "invalid-metadata":
        "Document {{filename}} has no metadata, is broken, or invalid.",
      removed:
        "Document {{filename}} has been removed from the watched document set.",
    },
    validation: {
      "value-not-number": "Value is not a number.",
      "value-must-be-nonzero": "Value must be non-zero.",
      "value-cannot-be-negative": "Value cannot be less than 0.",
      "invalid-serp-provider": "Invalid SERP provider.",
      "max-rerank-limit-min": "Maximum rerank limit must be at least 10.",
      "max-rerank-limit-max": "Maximum rerank limit cannot exceed 200.",
    },
    embedding: {
      "lmstudio-failed": "LMStudio Failed to embed: {{errors}}",
      "jina-failed": "Jina Failed to embed: {{errors}}",
    },
    env: {
      "empty-value": "Value cannot be empty",
      "jina-api-key-error": "Jina API key must start with jina_",
      "non-zero-required": "Value must be greater than zero",
      "must-be-number": "Value must be a number",
      "invalid-url": "URL is not a valid URL",
      "openai-key-format": "OpenAI Key must start with sk-",
      "contextual-prompt-format":
        "Contextual User Prompt must contain {file} & {chunk}",
      "anthropic-key-format": "Anthropic Key must start with sk-ant-",
      "external-llm-url-format": "URL must include /v1",
      "url-no-trailing-slash": "URL cannot end with a slash",
      "invalid-tts-provider": "{{input}} is not a valid TTS provider",
      "invalid-whisper-model":
        "{{input}} is not a valid Whisper model selection",
      "invalid-llm-provider": "{{input}} is not a valid LLM provider",
    },
    workspace: {
      onlyOwnerShare: "Only workspace owners can share workspaces",
      onlyOwnerRevoke: "Only workspace owners can revoke workspace shares",
      shareFailed: "Failed to share workspace",
      revokeFailed: "Failed to revoke workspace share",
      getStatusFailed: "Failed to get share status",
    },
    thread: {
      onlyOwnerShare: "Only thread owners can share threads",
      onlyOwnerRevoke: "Only thread owners can revoke thread shares",
      shareFailed: "Failed to share thread",
      revokeFailed: "Failed to revoke thread share",
      getStatusFailed: "Failed to get thread share status",
    },
  },
  notifications: {
    document: {
      invalid:
        "Document {{filename}} has no metadata, is broken, or invalid and has been removed.",
      removed:
        "Document {{filename}} has been removed from the watched document set.",
    },
  },
  validation: {
    responseHeader: "Generated Response",
    contextHeader: "Original Context and Sources",
    content: {
      "user-question-sources-header": "USER QUESTION AND SOURCES",
      "system-response-header": "SYSTEM RESPONSE",
      "not-provided": "Not provided",
    },
  },
  manualWorkEstimator: {
    content: {
      "user-question-header": "USER QUESTION",
      "system-response-header": "SYSTEM RESPONSE",
    },
  },
  docx: {
    title: "Chat Export",
    exportedOn: "Exported on",
    downloadedOn: "Downloaded on",
    keywords: "chat, export",
    description: "Exported chat message",
    tokenCount: "Token count",
    canvasDocumentTitle: "Canvas Document",
    errors: {
      contentRequired: "Content is required",
      writingFile: "Error writing temporary file",
      generating: "Error generating document",
    },
  },
  deep_search: {
    instruction:
      "If any DeepSearch sources are relevant to the user query and used in the response, provide clickable links to those sources at the end of the message in this exact format: 'Sources: [Source Title 1](URL1), [Source Title 2](URL2)'. The clickable text for each link must be a concise description of the source content (e.g., the page title or a summary). Never reference DeepSearch sources using numbers in the text (like [1] or [Source 1]); always refer to the source by its name or content directly. Do not use academic citation formats.",
  },
};

module.exports = translations;
