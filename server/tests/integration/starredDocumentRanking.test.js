const {
  applySimilarityBumpToStarredDocuments,
} = require("../../utils/helpers/starredDocuments");
const { Document } = require("../../models/documents");

// Mock Document model
jest.mock("../../models/documents", () => ({
  Document: {
    where: jest.fn(),
    get: jest.fn(),
  },
}));

describe("Starred Document Ranking Test", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should rank starred documents higher in similarity search results", async () => {
    // Mock starred documents in the database
    Document.where.mockResolvedValue([{ docId: "doc1", starred: true }]);

    // Simulate vector search results with scores
    const searchResults = [
      { docId: "doc2", score: 0.9, text: "Document 2" },
      { docId: "doc1", score: 0.8, text: "Document 1" }, // This one is starred
      { docId: "doc3", score: 0.7, text: "Document 3" },
    ];

    // Apply similarity bump to starred documents
    const result = await applySimilarityBumpToStarredDocuments(
      searchResults,
      "123" // workspaceId as string
    );

    // Sort the results by score (descending) to simulate what would happen in the vector search
    const sortedResults = [...result].sort((a, b) => b.score - a.score);

    // Verify Document.where was called with correct parameters
    expect(Document.where).toHaveBeenCalledWith({
      workspaceId: 123, // The function converts string to number
      starred: true,
    });

    // Verify the starred document got a similarity bump
    expect(result.find((doc) => doc.docId === "doc1").score).toBeCloseTo(
      0.9,
      5
    ); // 0.8 + 0.1 = 0.9

    // Verify the order of documents after sorting
    // doc1 and doc2 now have the same score (0.9), but doc2 comes first due to stable sort
    expect(sortedResults[0].docId).toBe("doc2");
    expect(sortedResults[1].docId).toBe("doc1");
    expect(sortedResults[2].docId).toBe("doc3");
  });

  it("should boost a starred document to the top of search results", async () => {
    // Mock starred documents in the database
    Document.where.mockResolvedValue([{ docId: "doc1", starred: true }]);

    // Simulate vector search results with scores
    const searchResults = [
      { docId: "doc2", score: 0.85, text: "Document 2" },
      { docId: "doc1", score: 0.82, text: "Document 1" }, // This one is starred
      { docId: "doc3", score: 0.83, text: "Document 3" },
    ];

    // Apply similarity bump to starred documents
    const result = await applySimilarityBumpToStarredDocuments(
      searchResults,
      "123" // workspaceId as string
    );

    // Sort the results by score (descending) to simulate what would happen in the vector search
    const sortedResults = [...result].sort((a, b) => b.score - a.score);

    // Verify the starred document got a similarity bump
    expect(result.find((doc) => doc.docId === "doc1").score).toBeCloseTo(
      0.92,
      5
    ); // 0.82 + 0.1 = 0.92

    // Verify the order of documents after sorting
    expect(sortedResults[0].docId).toBe("doc1"); // Now highest at 0.92
    expect(sortedResults[1].docId).toBe("doc2"); // Now second at 0.85
    expect(sortedResults[2].docId).toBe("doc3"); // Now lowest at 0.83
  });
});
