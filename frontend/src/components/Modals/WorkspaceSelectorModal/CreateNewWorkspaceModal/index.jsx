import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useState } from "react";
import Label from "@/components/ui/Label";
import Input from "@/components/ui/Input";
import FormItem from "@/components/ui/FormItem";
import Workspace from "@/models/workspace";
import paths from "@/utils/paths";
import { useSetSelectedModule } from "@/stores/userStore";
import { LuArrowRight } from "react-icons/lu";

const CreateNewWorkspaceModal = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const setSelectedModule = useSetSelectedModule();
  const [navigating, setNavigating] = useState(false);

  const formSchema = z.object({
    workspaceName: z
      .string()
      .min(1, t("workspaceSelector.workspaceNameRequired"))
      .min(2, t("workspaceSelector.workspaceNameMustBeMoreThanOneCharacter")),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      workspaceName: "",
    },
  });

  const onSubmit = async (data) => {
    setNavigating(true);
    try {
      setSelectedModule("document-drafting");
      const { workspace } = await Workspace.new({
        name: data.workspaceName,
        type: "document-drafting",
      });
      window.location.href = paths.workspace.chat(workspace.slug);
    } catch (error) {
      console.error("Failed to create workspace:", error);
      setNavigating(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t("new-workspace.title")}
      footer={
        <Button
          type="submit"
          form="create-workspace-form"
          isLoading={navigating}
        >
          {t("workspaceSelector.next")}
          <LuArrowRight />
        </Button>
      }
      className="max-w-lg p-8"
    >
      <form id="create-workspace-form" onSubmit={handleSubmit(onSubmit)}>
        <FormItem>
          <Label htmlFor="workspaceName" className="text-base">
            {t("workspaceSelector.workspaceNameLabel")}
          </Label>
          <Input
            id="workspaceName"
            type="text"
            placeholder={t("workspaceSelector.workspaceNamePlaceholder")}
            {...register("workspaceName")}
            className="mt-1"
          />
          {errors.workspaceName && (
            <p className="pt-1 text-sm text-red-600">
              {errors.workspaceName.message}
            </p>
          )}
        </FormItem>
      </form>
    </Modal>
  );
};

export default CreateNewWorkspaceModal;
