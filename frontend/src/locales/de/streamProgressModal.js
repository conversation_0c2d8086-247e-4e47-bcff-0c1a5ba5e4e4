export default {
  // =========================
  // CDB PROGRESS
  // =========================
  cdbProgress: {
    "close-msg": "Möchten Sie den Vorgang wirklich abbrechen?",
    general: {
      placeholderSubTask: "Element {{index}} wird verarbeitet...",
    },
    main: {
      step1: {
        label: "Liste der Abschnitte generieren",
        desc: "Verwendung des Hauptdokuments zur Erstellung einer initialen Struktur.",
      },
      step2: {
        label: "Dokumente verarbeiten",
        desc: "Generierung von Beschreibungen und Überprüfung der Relevanz.",
      },
      step3: {
        label: "Dokumente den Abschnitten zuordnen",
        desc: "Zuweisung relevanter Dokumente zu jedem Abschnitt.",
      },
      step4: {
        label: "Rechtliche Fragen identifizieren",
        desc: "Extraktion wichtiger rechtlicher Fragen für jeden Abschnitt.",
      },
      step5: {
        label: "Rechtsgutachten generieren",
        desc: "Erstellung von Rechtsgutachten für die identifizierten Fragen.",
      },
      step6: {
        label: "Abschnitte entwerfen",
        desc: "Verfassen des Inhalts für jeden einzelnen Abschnitt.",
      },
      step7: {
        label: "Dokument zusammenführen & finalisieren",
        desc: "Zusammenstellung der Abschnitte zum endgültigen Dokument.",
      },
    },
    noMain: {
      step1: {
        label: "Dokumente verarbeiten",
        desc: "Generierung von Beschreibungen für alle hochgeladenen Dateien.",
      },
      step2: {
        label: "Liste der Abschnitte generieren",
        desc: "Erstellung einer strukturierten Liste von Abschnitten aus Dokumentzusammenfassungen.",
      },
      step3: {
        label: "Dokumentzuordnung finalisieren",
        desc: "Bestätigung der Dokumentrelevanz für jeden geplanten Abschnitt.",
      },
      step4: {
        label: "Rechtliche Fragen identifizieren",
        desc: "Extraktion wichtiger rechtlicher Fragen für jeden Abschnitt.",
      },
      step5: {
        label: "Rechtsgutachten generieren",
        desc: "Erstellung von Rechtsgutachten für die identifizierten Fragen.",
      },
      step6: {
        label: "Abschnitte entwerfen",
        desc: "Verfassen des Inhalts für jeden einzelnen Abschnitt.",
      },
      step7: {
        label: "Dokument zusammenführen & finalisieren",
        desc: "Zusammenstellung aller Abschnitte zum endgültigen Rechtsdokument.",
      },
    },
    referenceFiles: {
      step1: {
        label: "Verarbeitung von Referenzdateien",
        desc: "Verarbeitung von Referenzdateien",
      },
      step2: {
        label: "Verarbeitung von Prüfdateien",
        desc: "Verarbeitung von Prüfdateien",
      },
      step3: {
        label: "Abschnittsliste generieren",
        desc: "Abschnittsliste generieren",
      },
      step4: {
        label: "Abschnitte entwerfen",
        desc: "Abschnitte entwerfen",
      },
      step5: {
        label: "Bericht generieren",
        desc: "Bericht generieren",
      },
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Abbruch bestätigen",
    confirm_abort_description:
      "Sind Sie sicher, dass Sie den Prozess abbrechen möchten?",
    keep_running: "Weiter ausführen",
    abort_run: "Prozess abbrechen",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Fortschritt der Antwortgenerierung",
    description:
      "Zeigt den realen Fortschritt der Aufgaben zum Abschluss der Erstellung von Dokumenten an, abhängig von der Verknüpfung mit anderen Arbeitsbereichen und der Größe der Dateien. Der Modal schließt automatisch, wenn alle Schritte abgeschlossen sind.",
    step_fetching_memos: "Abrufen von juristischen Daten zu aktuellen Themen",
    step_processing_chunks: "Verarbeitung hochgeladener Dokumente",
    step_combining_responses: "Antwort finalisieren",
    sub_step_chunk_label: "Verarbeitung der Dokumentengruppe {{index}}",
    sub_step_memo_label: "Juristische Daten abgerufen von {{workspaceSlug}}",
    placeholder_sub_task: "Wartender Schritt",
    desc_fetching_memos:
      "Abrufen relevanter juristischer Informationen aus verknüpften Arbeitsbereichen",
    desc_processing_chunks:
      "Analyse und Extraktion von Informationen aus Dokumentengruppen",
    desc_combining_responses:
      "Zusammenführung von Informationen zu einer umfassenden Antwort",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Rechtliche Aufgabe läuft im Hintergrund.",
    dd: "Dokumentenerstellung läuft im Hintergrund weiter.",
    reopen: "Statusfenster öffnen",
  },
};
