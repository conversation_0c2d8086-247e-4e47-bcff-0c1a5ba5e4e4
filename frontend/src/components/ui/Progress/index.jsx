import React, { useState, useEffect, useRef } from "react";
import { cn } from "@/utils/classes";

export default function Progress({ className, value, ...props }) {
  const [displayValue, setDisplayValue] = useState(0);
  const hasAnimatedInitially = useRef(false);

  // doing this to add an animation effect
  useEffect(() => {
    if (!hasAnimatedInitially.current) {
      const timer = setTimeout(() => {
        setDisplayValue(2);
        hasAnimatedInitially.current = true;
      }, 1500);

      return () => clearTimeout(timer);
    } else {
      setDisplayValue(value || 0);
    }
  }, [value]);

  return (
    <div
      className={cn(
        "bg-border relative h-2 w-full overflow-hidden rounded-full",
        className
      )}
      {...props}
    >
      <div
        className="bg-foreground h-full w-full flex-1 transition-transform duration-500"
        style={{ transform: `translateX(-${100 - displayValue}%)` }}
      />
    </div>
  );
}
