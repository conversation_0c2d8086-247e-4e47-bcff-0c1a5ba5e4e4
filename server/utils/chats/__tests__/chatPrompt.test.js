const { chatPrompt } = require("../index");

describe("chatPrompt function", () => {
  const defaultPrompt =
    "Given the following conversation, relevant context, and a follow up question, reply with an answer to the current question the user is asking. Return only your response to the question given the above information following the users instructions as needed.";

  describe("prompt hierarchy", () => {
    test("should return user custom prompt when available (highest priority)", () => {
      const workspace = { openAiPrompt: "Workspace prompt" };
      const user = { custom_system_prompt: "User custom prompt" };

      const result = chatPrompt(workspace, user);
      expect(result).toBe("User custom prompt");
    });

    test("should return workspace prompt when no user custom prompt", () => {
      const workspace = { openAiPrompt: "Workspace prompt" };
      const user = { custom_system_prompt: null };

      const result = chatPrompt(workspace, user);
      expect(result).toBe("Workspace prompt");
    });

    test("should return workspace prompt when user has no custom prompt", () => {
      const workspace = { openAiPrompt: "Workspace prompt" };
      const user = {};

      const result = chatPrompt(workspace, user);
      expect(result).toBe("Workspace prompt");
    });

    test("should return default prompt when no workspace or user prompt", () => {
      const workspace = {};
      const user = {};

      const result = chatPrompt(workspace, user);
      expect(result).toBe(defaultPrompt);
    });

    test("should return default prompt when workspace is null", () => {
      const workspace = null;
      const user = null;

      const result = chatPrompt(workspace, user);
      expect(result).toBe(defaultPrompt);
    });

    test("should return user custom prompt even when workspace prompt exists", () => {
      const workspace = { openAiPrompt: "Workspace prompt" };
      const user = { custom_system_prompt: "User custom prompt" };

      const result = chatPrompt(workspace, user);
      expect(result).toBe("User custom prompt");
    });

    test("should handle empty user custom prompt", () => {
      const workspace = { openAiPrompt: "Workspace prompt" };
      const user = { custom_system_prompt: "" };

      const result = chatPrompt(workspace, user);
      expect(result).toBe("Workspace prompt");
    });

    test("should handle whitespace-only user custom prompt", () => {
      const workspace = { openAiPrompt: "Workspace prompt" };
      const user = { custom_system_prompt: "   " };

      const result = chatPrompt(workspace, user);
      expect(result).toBe("   ");
    });
  });

  describe("style alignment integration", () => {
    test("should append style instructions to user custom prompt", () => {
      const workspace = { openAiPrompt: "Workspace prompt" };
      const user = { custom_system_prompt: "User custom prompt" };
      const styleAlignment = {
        enabled: true,
        instructions: "Write in a formal tone",
      };

      const result = chatPrompt(workspace, user, styleAlignment);
      expect(result).toContain("User custom prompt");
      expect(result).toContain("IMPORTANT STYLE INSTRUCTIONS");
      expect(result).toContain("Write in a formal tone");
    });

    test("should append style instructions to workspace prompt when no user prompt", () => {
      const workspace = { openAiPrompt: "Workspace prompt" };
      const user = {};
      const styleAlignment = {
        enabled: true,
        instructions: "Write in a formal tone",
      };

      const result = chatPrompt(workspace, user, styleAlignment);
      expect(result).toContain("Workspace prompt");
      expect(result).toContain("IMPORTANT STYLE INSTRUCTIONS");
      expect(result).toContain("Write in a formal tone");
    });

    test("should not append style instructions when disabled", () => {
      const workspace = { openAiPrompt: "Workspace prompt" };
      const user = { custom_system_prompt: "User custom prompt" };
      const styleAlignment = {
        enabled: false,
        instructions: "Write in a formal tone",
      };

      const result = chatPrompt(workspace, user, styleAlignment);
      expect(result).toBe("User custom prompt");
      expect(result).not.toContain("IMPORTANT STYLE INSTRUCTIONS");
    });

    test("should not append style instructions when instructions are empty", () => {
      const workspace = { openAiPrompt: "Workspace prompt" };
      const user = { custom_system_prompt: "User custom prompt" };
      const styleAlignment = {
        enabled: true,
        instructions: "",
      };

      const result = chatPrompt(workspace, user, styleAlignment);
      expect(result).toBe("User custom prompt");
      expect(result).not.toContain("IMPORTANT STYLE INSTRUCTIONS");
    });
  });
});
