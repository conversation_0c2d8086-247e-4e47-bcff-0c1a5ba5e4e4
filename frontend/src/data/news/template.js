/**
 * Template for System News Items
 *
 * Copy this file and rename it following the naming convention:
 * YYYY-MM-description.js (e.g., 2024-01-welcome.js)
 *
 * Then update the content below and add it to index.js
 *
 * For internationalized news, use titleKey and contentKey instead of title and content.
 * Add the corresponding translations to all language files in:
 * frontend/src/locales/[lang]/newsSystemItems.js
 *
 * CATEGORY GUIDELINES:
 * Choose one of the following categories for your news item:
 *
 * 1. "welcome" - Use for:
 *    - Welcome messages for new users
 *    - Platform introduction content
 *    - Getting started guides
 *    - Onboarding information
 *
 * 2. "system-updates" - Use for:
 *    - New feature announcements
 *    - System maintenance notifications
 *    - Performance improvements
 *    - Bug fixes and patches
 *    - API changes or updates
 *
 * 3. "general" - Use for:
 *    - General announcements
 *    - Policy changes
 *    - Company news
 *    - Educational content
 *    - Tips and best practices
 *
 * AUTHOR GUIDELINES:
 * Use one of the following authors (they will be automatically translated):
 * - "IST Legal Team" - For official announcements from the IST Legal team
 * - "System" - For automated system-generated messages
 * - For other authors, use the actual name (it will be displayed as-is)
 *
 * INTERNATIONALIZATION:
 * - Always use titleKey and contentKey for system news
 * - Add translations to ALL language files in frontend/src/locales/[lang]/newsSystemItems.js
 * - Follow the existing pattern: "system-[identifier]" for the news ID
 * - Use the same ID in the translation keys: "news-system-items.[news-id].title" and "news-system-items.[news-id].content"
 */

export default {
  // Unique identifier for this news item (prefix with "system-" recommended)
  id: "system-template-example",

  // For internationalized news, use titleKey and contentKey
  titleKey: "news-system-items.system-template-example.title",
  contentKey: "news-system-items.system-template-example.content",

  // For non-internationalized news, use title and content directly
  // title: "System News Template",
  // content: `This is a template for system news items...`,

  // Priority level: "low", "medium", "high", "urgent"
  priority: "medium",

  // Target roles (null for all users, or array of roles)
  // Available roles: "admin", "manager", "default"
  targetRoles: null, // Show to all users
  // targetRoles: ["admin", "manager"], // Show only to admins and managers

  // Expiration date (null for no expiration)
  expiresAt: null,
  // expiresAt: "2024-12-31T23:59:59Z", // Expires at end of 2024

  // Whether this news item is active
  isActive: true,

  // Always true for system news (used to identify system vs database news)
  isSystemNews: true,

  // Creation date (when this news was added to the system)
  createdAt: "2024-01-15T10:00:00Z",

  // Application version when this news was added (for tracking)
  version: "1.0.0",

  // Optional: Additional metadata
  metadata: {
    // Author: Use "IST Legal Team" or "System" for automatic translation
    author: "System",

    // Category: Choose from "welcome", "system-updates", or "general"
    category: "general",

    // Tags: Optional array of tags for categorization
    tags: ["template", "example"],
  },
};
