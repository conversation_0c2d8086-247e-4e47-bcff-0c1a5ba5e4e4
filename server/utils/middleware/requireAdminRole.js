const { tSync } = require("../i18n");

function requireAdminRole(request, response, next) {
  const user = response.locals.user;

  if (!user) {
    return response.status(401).json({
      success: false,
      message: tSync("errors.auth.authentication-required"),
    });
  }

  if (!["admin", "manager"].includes(user.role)) {
    return response.status(403).json({
      success: false,
      message: tSync("errors.auth.insufficient-permissions"),
    });
  }

  next();
}

module.exports = {
  requireAdminRole,
};
