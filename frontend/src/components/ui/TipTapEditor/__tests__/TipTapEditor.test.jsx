import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, jest, beforeEach } from "@jest/globals";
import TipTapEditor from "../index";

// Mock dependencies
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key, fallback) => {
      const translations = {
        "text-edit.toolbar.bold": "Bold",
        "text-edit.toolbar.italic": "Italic",
        "text-edit.toolbar.underline": "Underline",
        "text-edit.toolbar.strikethrough": "Strikethrough",
        "text-edit.toolbar.heading-1": "Heading 1",
        "text-edit.toolbar.heading-2": "Heading 2",
        "text-edit.toolbar.heading-3": "Heading 3",
        "text-edit.toolbar.bullet-list": "Bullet List",
        "text-edit.toolbar.numbered-list": "Numbered List",
        "text-edit.toolbar.blockquote": "Blockquote",
        "text-edit.toolbar.text-color": "Text Color",
        "text-edit.toolbar.highlight": "Highlight",
        "text-edit.toolbar.undo": "Undo",
        "text-edit.toolbar.redo": "Redo",
      };
      return translations[key] || fallback || key;
    },
  }),
}));

jest.mock("@/utils/validation", () => ({
  sanitizeUrl: jest.fn((url) => url),
  sanitizeCssColor: jest.fn((color) => color),
  isColorInSafePalette: jest.fn(() => true),
}));

jest.mock("@/components/Button", () => ({
  Button: ({ children, onClick, disabled, className, ...props }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      className={className}
      {...props}
    >
      {children}
    </button>
  ),
}));

jest.mock("@/components/ui/Modal", () => {
  return function MockModal({ isOpen, children, title }) {
    return isOpen ? (
      <div data-testid="modal" role="dialog">
        <h2>{title}</h2>
        {children}
      </div>
    ) : null;
  };
});

jest.mock("@/components/ui/Input", () => {
  return function MockInput(props) {
    return <input {...props} />;
  };
});

jest.mock("@/components/ui/Label", () => {
  return function MockLabel({ children, ...props }) {
    return <label {...props}>{children}</label>;
  };
});

jest.mock("@/components/ui/Tooltip", () => {
  return function MockTooltip({ children, content }) {
    return <div title={content}>{children}</div>;
  };
});

// Mock react-icons
jest.mock("react-icons/md", () => ({
  MdFormatBold: () => <span>Bold</span>,
  MdFormatItalic: () => <span>Italic</span>,
  MdFormatUnderlined: () => <span>Underline</span>,
  MdFormatStrikethrough: () => <span>Strike</span>,
  MdFormatListBulleted: () => <span>BulletList</span>,
  MdFormatListNumbered: () => <span>NumberedList</span>,
  MdFormatQuote: () => <span>Quote</span>,
  MdPalette: () => <span>Palette</span>,
  MdHighlight: () => <span>Highlight</span>,
  MdUndo: () => <span>Undo</span>,
  MdRedo: () => <span>Redo</span>,
}));

jest.mock("react-icons/lu", () => ({
  LuHeading1: () => <span>H1</span>,
  LuHeading2: () => <span>H2</span>,
  LuHeading3: () => <span>H3</span>,
}));

describe("TipTapEditor", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the editor with toolbar", () => {
    render(<TipTapEditor content="" onChange={() => {}} />);

    // Check for toolbar buttons
    expect(screen.getByText("Bold")).toBeInTheDocument();
    expect(screen.getByText("Italic")).toBeInTheDocument();
    expect(screen.getByText("H1")).toBeInTheDocument();
    expect(screen.getByText("H2")).toBeInTheDocument();
    expect(screen.getByText("H3")).toBeInTheDocument();
  });

  it("handles empty lines in markdown content", async () => {
    const markdownWithEmptyLines = `First paragraph

Second paragraph after empty line


Third paragraph after multiple empty lines`;

    const onChange = jest.fn();
    render(
      <TipTapEditor content={markdownWithEmptyLines} onChange={onChange} />
    );

    // Wait for the editor to initialize and process the content
    await waitFor(() => {
      const editorContent = document.querySelector(".ProseMirror");
      expect(editorContent).toBeInTheDocument();
    });

    // Check that the editor content contains paragraphs with proper classes
    const paragraphs = document.querySelectorAll(".ProseMirror p");
    expect(paragraphs.length).toBeGreaterThan(0);

    // Check that paragraphs have the whitespace-pre-line class
    const paragraphsWithClass = document.querySelectorAll(
      ".ProseMirror p.whitespace-pre-line"
    );
    expect(paragraphsWithClass.length).toBeGreaterThan(0);
  });

  it("preserves empty lines when converting back to markdown", async () => {
    const markdownWithEmptyLines = `First line

Second line`;

    const onChange = jest.fn();
    const ref = React.createRef();

    render(
      <TipTapEditor
        ref={ref}
        content={markdownWithEmptyLines}
        onChange={onChange}
      />
    );

    await waitFor(() => {
      expect(ref.current).toBeTruthy();
    });

    // Get the markdown content back
    const markdown = ref.current.getMarkdown();

    // Should preserve the empty line structure
    expect(markdown).toContain("First line");
    expect(markdown).toContain("Second line");
    // Should have some form of empty line preservation
    expect(markdown.split("\n").length).toBeGreaterThan(2);
  });

  it("applies correct CSS classes for empty line handling", () => {
    render(<TipTapEditor content="Test content" onChange={() => {}} />);

    // Check that the editor has the correct CSS classes applied
    const editorContainer = document.querySelector(".tiptap-editor");
    expect(editorContainer).toBeInTheDocument();

    // Check that the ProseMirror editor is present
    const proseMirrorEditor = document.querySelector(".ProseMirror");
    expect(proseMirrorEditor).toBeInTheDocument();
  });

  it("handles empty paragraphs correctly", async () => {
    const onChange = jest.fn();
    render(<TipTapEditor content="" onChange={onChange} />);

    await waitFor(() => {
      const editorContent = document.querySelector(".ProseMirror");
      expect(editorContent).toBeInTheDocument();
    });

    // Check that the editor is properly initialized
    const proseMirrorEditor = document.querySelector(".ProseMirror");
    expect(proseMirrorEditor).toBeInTheDocument();

    // Check that the editor container has the tiptap-editor class
    const editorContainer = document.querySelector(".tiptap-editor");
    expect(editorContainer).toBeInTheDocument();
  });

  it("creates proper paragraph structure for empty lines", async () => {
    const markdownWithEmptyLines = `First paragraph

Second paragraph`;

    const onChange = jest.fn();
    render(
      <TipTapEditor content={markdownWithEmptyLines} onChange={onChange} />
    );

    await waitFor(() => {
      const editorContent = document.querySelector(".ProseMirror");
      expect(editorContent).toBeInTheDocument();
    });

    // Check that paragraphs are created with the whitespace-pre-line class
    const paragraphsWithClass = document.querySelectorAll(
      ".ProseMirror p.whitespace-pre-line"
    );
    expect(paragraphsWithClass.length).toBeGreaterThan(0);

    // Check that empty paragraphs are preserved as actual paragraph elements
    const allParagraphs = document.querySelectorAll(".ProseMirror p");
    expect(allParagraphs.length).toBeGreaterThanOrEqual(2); // At least the two content paragraphs
  });

  it("preserves empty lines as empty paragraphs with trailing breaks", async () => {
    const markdownWithEmptyLines = `Line 1

Line 3`;

    const onChange = jest.fn();
    render(
      <TipTapEditor content={markdownWithEmptyLines} onChange={onChange} />
    );

    await waitFor(() => {
      const editorContent = document.querySelector(".ProseMirror");
      expect(editorContent).toBeInTheDocument();
    });

    // Check that we have paragraphs with the whitespace-pre-line class
    const paragraphsWithClass = document.querySelectorAll(
      ".ProseMirror p.whitespace-pre-line"
    );
    expect(paragraphsWithClass.length).toBeGreaterThan(0);

    // Check that we have multiple paragraphs (indicating structure is preserved)
    const allParagraphs = document.querySelectorAll(".ProseMirror p");
    expect(allParagraphs.length).toBeGreaterThanOrEqual(2);

    // Verify that the content includes both lines
    const editorContent = document.querySelector(".ProseMirror");
    expect(editorContent.textContent).toContain("Line 1");
    expect(editorContent.textContent).toContain("Line 3");
  });

  it("renders markdown formatting correctly", async () => {
    const markdownWithFormatting = `# Heading 1

## Heading 2

### Heading 3

**Bold text** and *italic text*

~~Strikethrough text~~

- Bullet list item
- Another item

1. Numbered list
2. Second item`;

    const onChange = jest.fn();
    render(
      <TipTapEditor content={markdownWithFormatting} onChange={onChange} />
    );

    await waitFor(() => {
      const editorContent = document.querySelector(".ProseMirror");
      expect(editorContent).toBeInTheDocument();
    });

    // Check for headings
    const h1 = document.querySelector(".ProseMirror h1");
    const h2 = document.querySelector(".ProseMirror h2");
    const h3 = document.querySelector(".ProseMirror h3");
    expect(h1).toBeInTheDocument();
    expect(h2).toBeInTheDocument();
    expect(h3).toBeInTheDocument();

    // Check for formatting
    const strongElements = document.querySelectorAll(".ProseMirror strong");
    const emElements = document.querySelectorAll(".ProseMirror em");
    expect(strongElements.length).toBeGreaterThan(0);
    expect(emElements.length).toBeGreaterThan(0);

    // Check for lists
    const ulElements = document.querySelectorAll(".ProseMirror ul");
    const olElements = document.querySelectorAll(".ProseMirror ol");
    expect(ulElements.length).toBeGreaterThan(0);
    expect(olElements.length).toBeGreaterThan(0);
  });
});
