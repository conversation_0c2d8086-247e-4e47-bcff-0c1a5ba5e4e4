const TRANSLATIONS = {
  "scroll-threshold-settings": {
    title: "Paramètres de défilement",
    description:
      "Configurez la sensibilité du défilement automatique dans la vue de chat. Les valeurs plus basses sont plus sensibles.",
    saved: "Les paramètres de défilement ont été mis à jour.",
    saving: "Enregistrement des paramètres...",
    "save-changes": "Enregistrer les modifications",
    "fetch-error": "Impossible de charger les paramètres de défilement.",
    error: "Impossible d'enregistrer les paramètres de défilement.",
    increase: "Augmenter",
    decrease: "Diminuer",
    "bottom-threshold": {
      title: "Seuil inférieur (pixels)",
      description:
        "La distance par rapport au bas du chat pour être considéré 'en bas'.",
    },
    "streaming-disable-threshold": {
      title: "Seuil de désactivation du streaming (pixels)",
      description:
        "Distance que l'utilisateur doit faire défiler vers le haut pendant la diffusion de messages pour désactiver le défilement automatique.",
    },
    "auto-scroll-threshold": {
      title: "Seuil de défilement automatique (pixels)",
      description:
        "La distance par rapport au bas où le défilement automatique se réactivera.",
    },
  },
};
export default TRANSLATIONS;
