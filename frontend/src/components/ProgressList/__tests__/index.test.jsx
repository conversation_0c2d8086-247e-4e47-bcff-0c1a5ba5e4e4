import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, jest, beforeEach } from "@jest/globals";
import ProgressList from "../index";

// Mock the useThreadProgress hook
jest.mock("@/hooks/useThreadProgress", () => {
  return jest.fn(() => ({
    isActive: false,
    currentStep: 1,
    totalSteps: 7,
    flowType: null,
    stepDetails: [],
  }));
});

// Mock translation
const mockT = jest.fn((key, options) => {
  const translations = {
    "chatProgress.step": "Step",
    "chatProgress.noThreadSelected": "No thread selected",
    "chatProgress.noActiveProgress": "No active progress",
    "chatProgress.processing": "Processing",
    "chatProgress.types.cdb.step1.label": "Step 1: Document Analysis",
    "chatProgress.types.cdb.step1.desc": "Analyzing uploaded documents",
    "chatProgress.types.cdb.step2.label": "Step 2: Content Processing",
    "chatProgress.types.cdb.step2.desc": "Processing document content",
    "cdbProgress.general.placeholderSubTask": "Sub-task {{index}}",
  };

  if (options && typeof options === "object" && !Array.isArray(options)) {
    let result = translations[key] || key;
    Object.keys(options).forEach((optionKey) => {
      result = result.replace(`{{${optionKey}}}`, options[optionKey]);
    });
    return result;
  }

  return translations[key] || key;
});

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}));

const useThreadProgress = require("@/hooks/useThreadProgress");

describe("ProgressList", () => {
  const MOCK_THREAD_ID = "thread-123";

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Reset the mock implementation
    useThreadProgress.mockReturnValue({
      isActive: false,
      currentStep: 1,
      totalSteps: 7,
      flowType: null,
      stepDetails: [],
    });
  });

  describe("empty states", () => {
    it("should show no thread selected message when no threadSlug provided", () => {
      render(<ProgressList />);
      expect(screen.getByText("No thread selected")).toBeInTheDocument();
    });

    it("should show no thread selected message when threadSlug is null", () => {
      render(<ProgressList threadSlug={null} />);
      expect(screen.getByText("No thread selected")).toBeInTheDocument();
    });

    it("should show no active progress message when process is not active", () => {
      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 1,
        totalSteps: 7,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);
      expect(screen.getByText("No active progress")).toBeInTheDocument();
    });
  });

  describe("step rendering", () => {
    it("should render basic steps with default labels", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 3,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expect(screen.getByText("Step 1")).toBeInTheDocument();
      expect(screen.getByText("Step 2")).toBeInTheDocument();
      expect(screen.getByText("Step 3")).toBeInTheDocument();
    });

    it("should render flow-specific step labels when flowType is provided", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 2,
        flowType: "cdb",
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expect(screen.getByText("Step 1: Document Analysis")).toBeInTheDocument();
      expect(
        screen.getByText("Step 2: Content Processing")
      ).toBeInTheDocument();
    });

    it("should use backend message when available in stepDetails", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 3,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "complete",
            message: "Custom step 1 message",
            subTasks: [],
          },
          {
            step: 2,
            status: "in_progress",
            message: "Custom step 2 message",
            subTasks: [],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expect(screen.getByText("Custom step 1 message")).toBeInTheDocument();
      expect(screen.getByText("Custom step 2 message")).toBeInTheDocument();
      expect(screen.getByText("Step 3")).toBeInTheDocument(); // No detail, so falls back to default
    });
  });

  describe("step status and icons", () => {
    it("should show correct icons for different step statuses", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 3,
        totalSteps: 5,
        flowType: null,
        stepDetails: [
          { step: 1, status: "complete", subTasks: [] },
          { step: 2, status: "complete", subTasks: [] },
          { step: 3, status: "in_progress", subTasks: [] },
          { step: 4, status: "pending", subTasks: [] },
          { step: 5, status: "pending", subTasks: [] },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Complete steps should have checkmark icons (we can't easily test for specific icons)
      // But we can verify the steps are rendered with proper status
      const steps = screen.getAllByText(/Step \d/);
      expect(steps).toHaveLength(5);
    });

    it("should derive correct status for steps without explicit stepDetails", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 3,
        totalSteps: 5,
        flowType: null,
        stepDetails: [], // No explicit step details
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should render all steps with derived statuses
      expect(screen.getByText("Step 1")).toBeInTheDocument();
      expect(screen.getByText("Step 2")).toBeInTheDocument();
      expect(screen.getByText("Step 3")).toBeInTheDocument();
      expect(screen.getByText("Step 4")).toBeInTheDocument();
      expect(screen.getByText("Step 5")).toBeInTheDocument();
    });

    it("should handle when process is inactive but currentStep > 1", () => {
      useThreadProgress.mockReturnValue({
        isActive: false,
        currentStep: 3,
        totalSteps: 5,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should still render steps
      expect(screen.getByText("Step 1")).toBeInTheDocument();
      expect(screen.getByText("Step 2")).toBeInTheDocument();
      expect(screen.getByText("Step 3")).toBeInTheDocument();
    });
  });

  describe("sub-tasks", () => {
    it("should render sub-tasks when they exist", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 2,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "in_progress",
            message: "Step with sub-tasks",
            subTasks: [
              {
                subStep: 1,
                status: "complete",
                message: "Sub-task 1 complete",
              },
              {
                subStep: 2,
                status: "in_progress",
                message: "Sub-task 2 processing",
              },
            ],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expect(screen.getByText("Step with sub-tasks")).toBeInTheDocument();

      // Click to expand sub-tasks
      fireEvent.click(screen.getByText("Step with sub-tasks"));

      expect(screen.getByText("Sub-task 1 complete")).toBeInTheDocument();
      expect(screen.getByText("Sub-task 2 processing")).toBeInTheDocument();
      expect(screen.getByText("Processing...")).toBeInTheDocument();
    });

    it("should show placeholder message for sub-tasks without custom message", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "in_progress",
            message: "Step with sub-tasks",
            subTasks: [
              { subStep: 1, status: "complete" }, // No message
              { subStep: 2, status: "in_progress" }, // No message
            ],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Click to expand sub-tasks
      fireEvent.click(screen.getByText("Step with sub-tasks"));

      expect(screen.getByText("Sub-task 1")).toBeInTheDocument();
      expect(screen.getByText("Sub-task 2")).toBeInTheDocument();
    });

    it("should toggle sub-task visibility when clicking on expandable step", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "in_progress",
            message: "Expandable step",
            subTasks: [
              { subStep: 1, status: "complete", message: "Hidden sub-task" },
            ],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Sub-task should not be visible initially
      expect(screen.queryByText("Hidden sub-task")).not.toBeInTheDocument();

      // Click to expand
      fireEvent.click(screen.getByText("Expandable step"));
      expect(screen.getByText("Hidden sub-task")).toBeInTheDocument();

      // Click to collapse
      fireEvent.click(screen.getByText("Expandable step"));
      expect(screen.queryByText("Hidden sub-task")).not.toBeInTheDocument();
    });

    it("should not make step clickable if it has no sub-tasks", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "complete",
            message: "Simple step",
            subTasks: [],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      const stepElement = screen.getByText("Simple step");
      // Should not have hover effects or be clickable (hard to test without DOM inspection)
      expect(stepElement).toBeInTheDocument();
    });
  });

  describe("step expansion state", () => {
    it("should maintain independent expansion state for multiple steps", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 2,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "complete",
            message: "First expandable step",
            subTasks: [{ subStep: 1, message: "First sub-task" }],
          },
          {
            step: 2,
            status: "in_progress",
            message: "Second expandable step",
            subTasks: [{ subStep: 1, message: "Second sub-task" }],
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Expand first step
      fireEvent.click(screen.getByText("First expandable step"));
      expect(screen.getByText("First sub-task")).toBeInTheDocument();
      expect(screen.queryByText("Second sub-task")).not.toBeInTheDocument();

      // Expand second step (first should remain expanded)
      fireEvent.click(screen.getByText("Second expandable step"));
      expect(screen.getByText("First sub-task")).toBeInTheDocument();
      expect(screen.getByText("Second sub-task")).toBeInTheDocument();

      // Collapse first step (second should remain expanded)
      fireEvent.click(screen.getByText("First expandable step"));
      expect(screen.queryByText("First sub-task")).not.toBeInTheDocument();
      expect(screen.getByText("Second sub-task")).toBeInTheDocument();
    });
  });

  describe("edge cases", () => {
    it("should handle empty stepDetails gracefully", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 3,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should still render steps with default labels
      expect(screen.getByText("Step 1")).toBeInTheDocument();
      expect(screen.getByText("Step 2")).toBeInTheDocument();
      expect(screen.getByText("Step 3")).toBeInTheDocument();
    });

    it("should handle totalSteps of 0", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 0,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should not render any steps
      expect(screen.queryByText(/Step \d/)).not.toBeInTheDocument();
    });

    it("should handle missing subTasks array", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 1,
        totalSteps: 1,
        flowType: null,
        stepDetails: [
          {
            step: 1,
            status: "complete",
            message: "Step without subTasks array",
            // subTasks property missing
          },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      expect(
        screen.getByText("Step without subTasks array")
      ).toBeInTheDocument();
      // Should not crash and step should not be expandable
    });

    it("should handle invalid step numbers in stepDetails", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 2,
        totalSteps: 3,
        flowType: null,
        stepDetails: [
          {
            step: 999,
            status: "complete",
            message: "Invalid step",
            subTasks: [],
          },
          { step: 1, status: "complete", message: "Valid step", subTasks: [] },
        ],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Should render all totalSteps regardless of invalid stepDetails
      expect(screen.getByText("Valid step")).toBeInTheDocument(); // Step 1 has detail
      expect(screen.getByText("Step 2")).toBeInTheDocument(); // Step 2 has no detail, uses default
      expect(screen.getByText("Step 3")).toBeInTheDocument(); // Step 3 has no detail, uses default
      expect(screen.queryByText("Invalid step")).not.toBeInTheDocument(); // Step 999 is outside range
    });
  });

  describe("scrolling", () => {
    it("should have scrollable container with proper max height", () => {
      useThreadProgress.mockReturnValue({
        isActive: true,
        currentStep: 5,
        totalSteps: 10,
        flowType: null,
        stepDetails: [],
      });

      render(<ProgressList threadSlug={MOCK_THREAD_ID} />);

      // Test that container exists (we can't easily test CSS in jsdom)
      const container = screen.getByText("Step 1").closest("div");
      expect(container).toBeInTheDocument();
    });
  });
});
