#!/usr/bin/env node

import fs from "fs";
import path from "path";
import { execSync } from "child_process";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, "..");

// Colors for console output
const colors = {
  reset: "\x1b[0m",
  bright: "\x1b[1m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  magenta: "\x1b[35m",
  cyan: "\x1b[36m",
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function getCurrentVersion() {
  try {
    // Try to get version from git tag first
    const gitTag = execSync(
      "git describe --tags --exact-match HEAD 2>/dev/null",
      { encoding: "utf8" }
    ).trim();
    return gitTag.replace(/^v/, "");
  } catch (e) {
    // Fallback to version.json
    const versionPath = path.join(rootDir, "version.json");
    if (fs.existsSync(versionPath)) {
      const versionData = JSON.parse(fs.readFileSync(versionPath, "utf8"));
      return versionData.version;
    }

    // Fallback to package.json
    const packagePath = path.join(rootDir, "package.json");
    const packageData = JSON.parse(fs.readFileSync(packagePath, "utf8"));
    return packageData.version;
  }
}

function getLatestGitTag() {
  try {
    return execSync("git describe --tags --abbrev=0 2>/dev/null", {
      encoding: "utf8",
    }).trim();
  } catch (e) {
    return null;
  }
}

function updateVersionJson(version, description = null) {
  const versionPath = path.join(rootDir, "version.json");
  const timestamp = new Date().toISOString();

  let currentCommit;
  try {
    currentCommit = execSync("git rev-parse HEAD", { encoding: "utf8" }).trim();
  } catch (e) {
    currentCommit = "unknown";
  }

  const versionData = {
    version,
    description: description || `Release v${version}`,
    timestamp,
    commit: currentCommit,
  };

  fs.writeFileSync(versionPath, JSON.stringify(versionData, null, 2) + "\n");
  log(`✅ Updated version.json to ${version}`, colors.green);
}

function updatePackageVersions(version) {
  const packagePaths = [
    path.join(rootDir, "package.json"),
    path.join(rootDir, "server", "package.json"),
    path.join(rootDir, "collector", "package.json"),
  ];

  packagePaths.forEach((packagePath) => {
    if (fs.existsSync(packagePath)) {
      const packageData = JSON.parse(fs.readFileSync(packagePath, "utf8"));
      packageData.version = version;
      fs.writeFileSync(
        packagePath,
        JSON.stringify(packageData, null, 2) + "\n"
      );

      const relativePath = path.relative(rootDir, packagePath);
      log(`✅ Updated ${relativePath} to version ${version}`, colors.green);
    }
  });
}

function validateVersion(version) {
  const semverRegex =
    /^(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?(?:\+([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?$/;

  if (!semverRegex.test(version)) {
    throw new Error(
      `Invalid version format: ${version}. Expected semantic version (e.g., 1.2.3)`
    );
  }

  return true;
}

function bumpVersion(type) {
  const currentVersion = getCurrentVersion();
  const [major, minor, patch] = currentVersion.split(".").map(Number);

  let newVersion;
  switch (type) {
    case "major":
      newVersion = `${major + 1}.0.0`;
      break;
    case "minor":
      newVersion = `${major}.${minor + 1}.0`;
      break;
    case "patch":
      newVersion = `${major}.${minor}.${patch + 1}`;
      break;
    default:
      throw new Error(
        `Invalid bump type: ${type}. Use 'major', 'minor', or 'patch'`
      );
  }

  return newVersion;
}

function createGitTag(version, message = null) {
  const tagName = `v${version}`;
  const tagMessage = message || `Release ${tagName}`;

  try {
    execSync(`git tag -a ${tagName} -m "${tagMessage}"`, { stdio: "inherit" });
    log(`✅ Created git tag: ${tagName}`, colors.green);

    // Ask if user wants to push the tag
    log(
      `\n${colors.yellow}To push the tag to remote repository, run:${colors.reset}`
    );
    log(`${colors.cyan}git push origin ${tagName}${colors.reset}`);
  } catch (e) {
    log(`❌ Failed to create git tag: ${e.message}`, colors.red);
    throw e;
  }
}

function showCurrentVersionInfo() {
  const currentVersion = getCurrentVersion();
  const latestTag = getLatestGitTag();

  let gitCommit;
  try {
    gitCommit = execSync("git rev-parse HEAD", { encoding: "utf8" })
      .trim()
      .substring(0, 8);
  } catch (e) {
    gitCommit = "unknown";
  }

  log(
    `\n${colors.bright}Current Version Information:${colors.reset}`,
    colors.cyan
  );
  log(`Current Version: ${colors.green}${currentVersion}${colors.reset}`);
  log(`Latest Git Tag: ${colors.yellow}${latestTag || "none"}${colors.reset}`);
  log(`Git Commit: ${colors.blue}${gitCommit}${colors.reset}`);

  // Check version.json
  const versionPath = path.join(rootDir, "version.json");
  if (fs.existsSync(versionPath)) {
    const versionData = JSON.parse(fs.readFileSync(versionPath, "utf8"));
    log(`Version File: ${colors.magenta}${versionData.version}${colors.reset}`);
    if (versionData.description) {
      log(`Description: ${versionData.description}`);
    }
    if (versionData.timestamp) {
      log(`Last Updated: ${versionData.timestamp}`);
    }
  }
}

function syncVersions() {
  const currentVersion = getCurrentVersion();
  log(
    `\n${colors.bright}Syncing all version files to: ${currentVersion}${colors.reset}`,
    colors.cyan
  );

  updateVersionJson(currentVersion);
  updatePackageVersions(currentVersion);

  log(`✅ All versions synced to ${currentVersion}`, colors.green);
}

function showHelp() {
  log(
    `\n${colors.bright}IST Legal Version Management${colors.reset}`,
    colors.cyan
  );
  log(`\nUsage: node scripts/version-management.js <command> [options]\n`);

  log(`${colors.bright}Commands:${colors.reset}`);
  log(
    `  ${colors.green}info${colors.reset}                     Show current version information`
  );
  log(
    `  ${colors.green}sync${colors.reset}                     Sync all package.json files to current version`
  );
  log(
    `  ${colors.green}set <version>${colors.reset}            Set specific version (e.g., 1.2.3)`
  );
  log(
    `  ${colors.green}bump <type>${colors.reset}              Bump version by type (major|minor|patch)`
  );
  log(
    `  ${colors.green}release <version>${colors.reset}        Create a full release with git tag`
  );
  log(
    `  ${colors.green}tag <version>${colors.reset}            Create git tag for specific version`
  );

  log(`\n${colors.bright}Examples:${colors.reset}`);
  log(`  ${colors.cyan}node scripts/version-management.js info${colors.reset}`);
  log(
    `  ${colors.cyan}node scripts/version-management.js bump patch${colors.reset}`
  );
  log(
    `  ${colors.cyan}node scripts/version-management.js set 1.2.3${colors.reset}`
  );
  log(
    `  ${colors.cyan}node scripts/version-management.js release 1.2.3${colors.reset}`
  );
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  try {
    switch (command) {
      case "info":
        showCurrentVersionInfo();
        break;

      case "sync":
        syncVersions();
        break;

      case "set":
        if (!args[1]) {
          throw new Error("Version number required. Usage: set <version>");
        }
        validateVersion(args[1]);
        updateVersionJson(args[1], args[2]);
        updatePackageVersions(args[1]);
        log(`✅ Version set to ${args[1]}`, colors.green);
        break;

      case "bump": {
        if (!args[1]) {
          throw new Error(
            "Bump type required. Usage: bump <major|minor|patch>"
          );
        }
        const newVersion = bumpVersion(args[1]);
        updateVersionJson(
          newVersion,
          `Bump ${args[1]} version to ${newVersion}`
        );
        updatePackageVersions(newVersion);
        log(`✅ Version bumped to ${newVersion}`, colors.green);
        break;
      }

      case "tag":
        if (!args[1]) {
          throw new Error("Version number required. Usage: tag <version>");
        }
        validateVersion(args[1]);
        createGitTag(args[1], args[2]);
        break;

      case "release":
        if (!args[1]) {
          throw new Error("Version number required. Usage: release <version>");
        }
        validateVersion(args[1]);

        log(
          `\n${colors.bright}Creating release ${args[1]}...${colors.reset}`,
          colors.cyan
        );

        // Update all version files
        updateVersionJson(args[1], args[2] || `Release v${args[1]}`);
        updatePackageVersions(args[1]);

        // Create git tag
        createGitTag(args[1], args[2]);

        log(
          `\n${colors.green}✅ Release ${args[1]} created successfully!${colors.reset}`
        );
        log(`\n${colors.yellow}Next steps:${colors.reset}`);
        log(`1. Review the changes and commit them:`);
        log(
          `   ${colors.cyan}git add . && git commit -m "chore: release v${args[1]}"${colors.reset}`
        );
        log(`2. Push the tag to trigger GitHub Actions:`);
        log(`   ${colors.cyan}git push origin v${args[1]}${colors.reset}`);
        log(`3. Push the commits:`);
        log(`   ${colors.cyan}git push${colors.reset}`);
        break;

      case "help":
      case "--help":
      case "-h":
        showHelp();
        break;

      default:
        log(`❌ Unknown command: ${command}`, colors.red);
        showHelp();
        process.exit(1);
    }
  } catch (error) {
    log(`❌ Error: ${error.message}`, colors.red);
    process.exit(1);
  }
}

main().catch(console.error);
