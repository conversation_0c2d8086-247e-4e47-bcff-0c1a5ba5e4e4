export default {
  // Bsed on this order of pages in system settings:
  // Customization (Appearance)
  //Admin: System settings
  //Admin: Users
  //Admin: Invites
  //Admin: Workspaces
  //Admin: MCP Servers
  //Admin: Feedback List
  //Admin: Workspace chats
  //Admin: Default system
  //Admin: Document Builder
  //Admin: DD Prompt settings
  //Admin: Chat UI settings
  //AI-Providers: LLM
  //AI-Providers: Prompt Upgrade LLM
  //AI-Providers: Validation LLM
  //AI-Providers: CDB LLM
  //AI-Providers: Custom User AI
  //AI-Providers:  Vector Database
  //AI-Providers: Re-ranking settings
  //AI-Providers: Deep search
  //AI-Providers: Embedder
  //AI-Providers: Text Splitter & Chunking
  //AI-Providers: Voice & Speech
  //AI-Providers: transcription
  //AI-Providers: PDR System
  //Agent skills
  //Tools: Chat Embed History
  //Tools: Chat Embed
  //Tools: Event logs
  //Tools: Developer API
  //Tools: Browser Extension
  //Security
  //Document Drafting: Configuration
  //Document Drafting: Drafting LLM
  //Document Drafting: Chat Settings

  // =========================
  // LLM PREFERENCE PAGE
  // =========================
  llm: {
    title: "Préférence LLM",
    description:
      "Ces identifiants et paramètres concernent votre fournisseur LLM préféré pour le chat et l'intégration. Il est important que ces clés soient à jour et correctes, sinon le système ne fonctionnera pas correctement.",
    provider: "Fournisseur LLM",
    "secondary-provider": "Fournisseur LLM secondaire",
    "none-selected": "Aucun sélectionné",
    "select-llm":
      "Les agents ne fonctionneront pas tant qu'une sélection valide n'est pas faite.",
    "search-llm": "Rechercher tous les fournisseurs LLM",
    "context-window-warning":
      "Attention : Impossible de récupérer la fenêtre de contexte pour le modèle sélectionné.",
    "context-window-waiting":
      " -- en attente des informations de fenêtre de contexte -- ",
    "validation-prompt": {
      disable: {
        label: "Désactiver le prompt de validation",
        description:
          "Lorsque cette option est activée, le bouton de validation n'apparaîtra pas dans l'interface utilisateur.",
      },
    },
    "prompt-upgrade": {
      title: "Fournisseur LLM pour l'amélioration des prompts",
      description:
        "Le fournisseur LLM et le modèle spécifiques qui seront utilisés pour améliorer les prompts des utilisateurs. Par défaut, il utilise le fournisseur LLM et les paramètres du système.",
      search:
        "Rechercher les fournisseurs LLM disponibles pour cette fonctionnalité",
      template: "Modèle d'amélioration des prompts",
      "template-description":
        "Ce modèle sera utilisé lors de l'amélioration des prompts. Utilisez {{prompt}} pour faire référence au texte qui doit être amélioré.",
      "template-placeholder":
        "Entrez le modèle qui sera utilisé pour améliorer les prompts...",
      "template-hint":
        "Exemple : Veuillez améliorer le texte suivant tout en conservant son sens : {{prompt}}",
    },
    "logo-alt": "{{name}} logo",
    "context-window": "Fenêtre de contexte",
    "default-context-window": "(taille par défaut pour ce fournisseur)",
    tokens: "tokens",
    "save-error": "Échec de l'enregistrement des paramètres LLM",
  },
  // =========================
  // LLM PROVIDER DESCRIPTIONS
  // =========================
  "llm-provider": {
    openai:
      "L'option standard pour la plupart des utilisations non commerciales.",
    azure: "L'option entreprise d'OpenAI hébergée sur les services Azure.",
    anthropic: "Un assistant IA amical hébergé par Anthropic.",
    gemini: "Le modèle IA le plus grand et le plus capable de Google",
    huggingface:
      "Accès à 150,000+ modèles LLM open-source et à la communauté mondiale d'IA",
    ollama: "Exécutez des modèles LLM localement sur votre propre machine.",
    lmstudio:
      "Découvrez, téléchargez et exécutez des milliers de modèles LLM de pointe en quelques clics.",
    localai: "Exécutez des modèles LLM localement sur votre propre machine.",
    togetherai: "Exécutez des modèles open-source depuis Together AI.",
    mistral: "Exécutez des modèles open-source depuis Mistral AI.",
    perplexityai:
      "Exécutez des modèles puissants et connectés à Internet hébergés par Perplexity AI.",
    openrouter: "Une interface unifiée pour les LLMs.",
    groq: "Le plus rapide pour l'inférence des LLMs disponibles pour les applications IA en temps réel.",
    koboldcpp: "Exécutez des modèles LLM localement en utilisant koboldcpp.",
    oobabooga:
      "Exécutez des modèles LLM localement en utilisant Oobabooga's Text Generation Web UI.",
    cohere: "Exécutez les modèles puissants de Cohere.",
    lite: "Exécutez LiteLLM's OpenAI compatible proxy pour diverses LLMs.",
    "generic-openai":
      "Connectez-vous à tout service OpenAI compatible via une configuration personnalisée",
    native:
      "Utilisez un modèle Llama personnalisé téléchargé pour discuter sur cette instance.",
    xai: "Exécutez les puissants LLMs de xAI comme Grok-2 et plus.",
    "aws-bedrock":
      "Exécutez des modèles de base puissants en privé avec AWS Bedrock.",
    deepseek: "Exécutez les puissants LLMs de DeepSeek.",
    fireworksai:
      "Le moteur d'inférence le plus rapide et le plus efficace pour construire des systèmes AI production-ready, composés.",
    bedrock:
      "Exécutez des modèles de base puissants en privé avec AWS Bedrock.",
  },
  "custom-user-ai": {
    title: "IA utilisateur personnalisée",
    settings: "IA utilisateur personnalisée",
    description: "Configurer le fournisseur d'IA personnalisé",
    "custom-model-reference": "Nom et description du modèle personnalisé",
    "custom-model-reference-description":
      "Ajoutez une référence personnalisée pour ce modèle. Elle sera visible lors de l'utilisation du sélecteur de moteur d'IA utilisateur personnalisé dans le panneau de prompt.",
    "custom-model-reference-name": "Nom du modèle personnalisé",
    "custom-model-reference-description-label":
      "Description du modèle (Optionnel)",
    "custom-model-reference-description-placeholder":
      "Entrez une description optionnelle pour ce modèle",
    "custom-model-reference-name-placeholder":
      "Entrez un nom personnalisé pour ce modèle",
    "model-ref-placeholder":
      "Entrez un nom ou une description personnalisée pour cette configuration de modèle",
    "enter-custom-model-reference": "Entrez un nom personnalisé pour ce modèle",
    "standard-engine": "Moteur IA standard",
    "standard-engine-description":
      "Notre moteur par défaut utile pour la plupart des tâches",
    "dynamic-context-window-percentage":
      "Pourcentage de fenêtre de contexte dynamique",
    "dynamic-context-window-percentage-desc":
      "Contrôle la quantité de la fenêtre de contexte du LLM qui peut être utilisée pour des sources supplémentaires (10-100%)",
    "no-alternative-title": "Aucun modèle alternatif sélectionné",
    "no-alternative-desc":
      "Lorsque cette option est sélectionnée, les utilisateurs n'ont pas la possibilité de choisir un modèle alternatif.",
    "select-option": "Sélectionner un profil IA personnalisé",
    tab: {
      "custom-1": "Moteur personnalisé 1",
      "custom-2": "Moteur personnalisé 2",
      "custom-3": "Moteur personnalisé 3",
      "custom-4": "Moteur personnalisé 4",
      "custom-5": "Moteur personnalisé 5",
      "custom-6": "Moteur personnalisé 6",
    },
    engine: {
      "custom-1": "Moteur personnalisé 1",
      "custom-2": "Moteur personnalisé 2",
      "custom-3": "Moteur personnalisé 3",
      "custom-4": "Moteur personnalisé 4",
      "custom-5": "Moteur personnalisé 5",
      "custom-6": "Moteur personnalisé 6",
      "custom-1-title": "Moteur personnalisé 1",
      "custom-2-title": "Moteur personnalisé 2",
      "custom-3-title": "Moteur personnalisé 3",
      "custom-4-title": "Moteur personnalisé 4",
      "custom-5-title": "Moteur personnalisé 5",
      "custom-6-title": "Moteur personnalisé 6",
      "custom-1-description":
        "Configurer les paramètres du Moteur personnalisé 1",
      "custom-2-description":
        "Configurer les paramètres du Moteur personnalisé 2",
      "custom-3-description":
        "Configurer les paramètres du Moteur personnalisé 3",
      "custom-4-description":
        "Configurer les paramètres du Moteur personnalisé 4",
      "custom-5-description":
        "Configurer les paramètres du Moteur personnalisé 5",
      "custom-6-description":
        "Configurer les paramètres du Moteur personnalisé 6",
    },
    "option-number": "Option {{number}}",
    "llm-provider-selection": "Sélection du fournisseur LLM",
    "llm-provider-selection-desc":
      "Choisissez le fournisseur LLM pour cette configuration IA personnalisée",
    "custom-option": "Option personnalisée",
    saving: "Enregistrement...",
    "save-changes": "Enregistrer les modifications",
    "model-ref-saved":
      "Paramètres du modèle personnalisé enregistrés avec succès",
    "model-ref-save-failed":
      "Échec de l'enregistrement des paramètres du modèle personnalisé : {{error}}",
    "llm-settings-save-failed":
      "Échec de l'enregistrement des paramètres LLM : {{error}}",
    "settings-fetch-failed": "Échec de la récupération des paramètres",
    "llm-saved": "Paramètres LLM enregistrés avec succès",
    "select-provider-first":
      "Veuillez sélectionner un fournisseur LLM pour configurer les paramètres du modèle. Une fois configurée, cette option sera sélectionnable comme moteur d'IA personnalisé dans l'interface utilisateur.",
  },
  // =========================
  // CDB LLM PREFERENCE
  // =========================
  "cdb-llm-preference": {
    title: "Préférence LLM CDB",
    settings: "LLM CDB",
    description: "Configurer le fournisseur LLM pour CDB",
  },
  // =========================
  // TEMPLATE LLM PREFERENCE
  // =========================
  "template-llm-preference": {
    title: "Préférence LLM de modèle",
    settings: "LLM de modèle",
    description:
      "Sélectionnez le fournisseur LLM utilisé pour générer des modèles de documents. Par défaut, le fournisseur système est utilisé.",
    "toast-success": "Paramètres du LLM de modèle mis à jour",
    "toast-fail": "Échec de la mise à jour du LLM de modèle",
    saving: "Sauvegarde...",
    "save-changes": "Enregistrer les modifications",
  },
  // ----------------------------
  // Audio Preferences
  // ----------------------------
  audio: {
    title: "Préférences de Synthèse Vocale",
    provider: "Fournisseur",
    "system-native": "Système natif",
    "desc-speech":
      "Ici, vous pouvez spécifier le type de fournisseurs de synthèse vocale et de reconnaissance vocale à utiliser dans votre expérience. Par défaut, nous utilisons le support intégré du navigateur, mais vous pouvez en utiliser d'autres.",
    "title-text": "Préférences de Synthèse Vocale",
    "desc-text":
      "Ici, vous pouvez spécifier le type de fournisseurs de synthèse vocale à utiliser. Par défaut, nous utilisons le support intégré du navigateur, mais vous pouvez en utiliser d'autres.",
    "desc-config": "Aucune configuration n'est nécessaire pour ce fournisseur.",
    "placeholder-stt": "Rechercher des fournisseurs de reconnaissance vocale",
    "placeholder-tts": "Rechercher des fournisseurs de synthèse vocale",
    "native-stt":
      "Utilise le service STT intégré de votre navigateur s'il est pris en charge.",
    "native-tts":
      "Utilise le service TTS intégré de votre navigateur s'il est pris en charge.",
    "piper-tts":
      "Exécutez les modèles TTS localement dans votre navigateur en privé.",
    "openai-description":
      "Utiliser les voix et la technologie de synthèse vocale d'OpenAI.",
    openai: {
      "api-key": "Clé API",
      "api-key-placeholder": "Clé API OpenAI",
      "voice-model": "Modèle vocal",
    },
    elevenlabs: "Utiliser la technologie de synthèse vocale d'ElevenLabs.",
  },

  // ----------------------------
  // Transcription Preferences
  // ----------------------------
  transcription: {
    title: "Préférence du modèle de transcription",
    description:
      "Ces identifiants et paramètres concernent votre fournisseur de modèle de transcription préféré. Il est important que ces clés soient à jour et correctes pour que la transcription fonctionne.",
    provider: "Fournisseur de transcription",
    "warn-start":
      "L'utilisation du modèle Whisper local sur des machines avec une RAM ou un CPU limité peut bloquer la plateforme lors du traitement de fichiers multimédias.",
    "warn-recommend":
      "Nous recommandons au moins 2 Go de RAM et des fichiers de moins de 10 Mo.",
    "warn-end":
      "Le modèle intégré se téléchargera automatiquement à la première utilisation.",
    "search-audio": "Rechercher des fournisseurs de transcription audio",
    "api-key": "Clé API",
    "api-key-placeholder": "Clé API OpenAI",
    "whisper-model": "Modèle Whisper",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Intégré par défaut",
    "default-built-in-desc":
      "Exécutez un modèle whisper intégré sur cette instance en privé.",
    "openai-name": "OpenAI",
    "openai-desc":
      "Utilisez le modèle OpenAI Whisper-large avec votre clé API.",
    "model-turbo": "openai/whisper-large-v3-turbo", // Nouveau nom de modèle
    "model-size-turbo": "(~810mb)", // Nouvelle taille de modèle
  },

  // ----------------------------
  // Embedding Preferences
  // ----------------------------
  embedding: {
    title: "Préférence d'incorporation",
    "desc-start":
      "Lorsqu'un LLM ne prend pas en charge nativement un moteur d'incorporation, vous devrez peut-être spécifier des identifiants supplémentaires pour incorporer le texte.",
    "desc-end":
      "L'incorporation est le processus de conversion du texte en vecteurs. Ces identifiants sont nécessaires pour transformer vos fichiers et prompts dans un format utilisable par la plateforme.",
    provider: {
      title: "Fournisseur d'incorporation",
      description:
        "Aucune configuration n'est nécessaire lors de l'utilisation du moteur d'incorporation natif de la plateforme.",
      "search-embed": "Rechercher tous les fournisseurs d'incorporation",
      search: "Rechercher tous les fournisseurs d'incorporation",
      select: "Sélectionner un fournisseur d'incorporation",
    },
    workspace: {
      title: "Préférence d'intégration de l'espace de travail",
      description:
        "Le fournisseur d'intégration et le modèle spécifiques qui seront utilisés pour cet espace de travail. Par défaut, il utilise la préférence d'intégration du système.",
      "multi-model":
        "Le support multi-modèles n'est pas encore pris en charge pour ce fournisseur.",
      "workspace-use": "Cet espace de travail utilisera",
      "model-set": "le modèle défini pour le système.",
      embedding: "Modèle d'intégration de l'espace de travail",
      model:
        "Le modèle d'intégration spécifique qui sera utilisé pour cet espace de travail. S'il est vide, la préférence d'intégration du système sera utilisée.",
      wait: "-- en attente des modèles --",
      setup: "Configurer",
      use: "Pour utiliser",
      "need-setup":
        "en tant qu'intégrateur pour cet espace de travail, vous devez le configurer d'abord.",
      cancel: "Annuler",
      save: "Enregistrer",
      settings: "Paramètres",
      search: "Rechercher tous les fournisseurs d'intégration",
      "need-llm":
        "en tant que LLM pour cet espace de travail, vous devez le configurer d'abord.",
      "save-error":
        "Échec de l'enregistrement des paramètres {{provider}} : {{error}}",
      "system-default": "Paramètres système",
      "system-default-desc":
        "Utiliser les préférences d'intégration système pour cet espace de travail.",
    },
    warning: {
      "switch-model":
        "Le changement du modèle d'intégration brisera les documents incorporés précédemment qui fonctionnent lors du chat. Ils devront être désincorporés de chaque espace de travail et complètement supprimés et rechargés pour pouvoir être incorporés par le nouveau modèle d'intégration.",
    },
  },

  // ----------------------------
  // Text Splitting & Chunking Preferences
  // ----------------------------
  text: {
    title: "Préférences de découpage et segmentation de texte",
    "desc-start":
      "Parfois, vous devrez peut-être modifier la méthode par défaut de découpage et de segmentation des nouveaux documents avant leur ajout à votre base de données vectorielle.",
    "desc-end":
      "Ne modifiez ceci que si vous comprenez le fonctionnement du découpage de texte et ses implications.",
    "warn-start": "Les modifications ici ne s'appliquent qu'aux",
    "warn-center": "documents nouvellement intégrés",
    "warn-end": ", pas aux documents existants.",
    method: {
      title: "Méthode de découpage de texte",

      "native-explain":
        "Utiliser la taille et le chevauchement de chunks locaux pour le découpage.",

      "jina-explain":
        "Déléguer le chunking/segmentation à la méthode intégrée de Jina.",

      "jina-info": "Chunking Jina actif.",

      jina: {
        api_key: "Clé API Jina",
        api_key_desc:
          "Nécessaire pour utiliser le service de segmentation de Jina. La clé sera stockée dans votre environnement.",
        max_tokens: "Jina : Tokens maximum par chunk",
        max_tokens_desc:
          "Définit les tokens maximum dans chaque chunk pour le segmenteur Jina (maximum 2000 tokens).",
        return_tokens: "Retourner les informations de tokens",
        return_tokens_desc:
          "Inclure le nombre de tokens et les informations du tokenizer dans la réponse.",
        return_chunks: "Retourner les informations de chunks",
        return_chunks_desc:
          "Inclure des informations détaillées sur les chunks générés dans la réponse.",
      },
    },
    size: {
      title: "Taille des chunks de texte",
      description:
        "Il s'agit du nombre maximum de caractères autorisés dans un seul vecteur.",
      recommend: "La longueur maximale du modèle d'intégration est",
    },
    overlap: {
      title: "Chevauchement des chunks de texte",
      description:
        "Il s'agit du chevauchement maximal de caractères qui se produit lors du découpage entre deux chunks de texte adjacents.",
      error:
        "Le chevauchement des chunks ne peut pas être supérieur ou égal à la taille des chunks.",
    },
  },
  // ----------------------------
  // Contextual Embedding Settings
  // ----------------------------
  contextual: {
    checkbox: {
      label: "Contextual Embedding",
      hint: "Activez l'incorporation contextuelle pour améliorer le processus d'intégration avec des paramètres supplémentaires",
    },
    systemPrompt: {
      label: "Invite Système",
      placeholder: "Entrez une valeur...",
      description:
        "Exemple : Veuillez fournir un bref contexte pour situer ce fragment dans le document global afin d'améliorer la recherche du fragment. Répondez uniquement avec le contexte succinct et rien d'autre.",
    },
    userPrompt: {
      label: "Invite Utilisateur",
      placeholder: "Entrez une valeur...",
      description:
        "Exemple : <document>\n{file}\n</document>\nVoici le fragment que nous souhaitons situer dans le document complet\n<chunk>\n{chunk}\n</chunk>",
    },
  },
  // ----------------------------
  // CHAT UI SETTINGS
  // ----------------------------
  "chat-ui-settings": {
    title: "Paramètres de l'interface de chat",
    description: "Configurez les paramètres du chat.",
    auto_submit: {
      title: "Soumission automatique de la saisie vocale",
      description:
        "Soumettre automatiquement la saisie vocale après une période de silence",
    },
    auto_speak: {
      title: "Lecture automatique des réponses",
      description: "Lire automatiquement les réponses de l'IA",
    },
  },
  // =========================
  // PIPER TTS OPTIONS (COMPONENT IN PAGE)
  // =========================
  piperTTS: {
    description:
      "Tous les modèles PiperTTS fonctionnent localement dans votre navigateur. Cela peut être intensif en ressources sur les appareils moins puissants.",
    "voice-model": "Sélection du modèle vocal",
    "loading-models": "-- chargement des modèles disponibles --",
    "stored-indicator":
      "Le \"✔\" indique que ce modèle est déjà stocké localement et n'a pas besoin d'être téléchargé lors de l'exécution.",
    "flush-cache": "Vider le cache vocal",
    "flush-success": "Toutes les voix supprimées du stockage du navigateur",
    demo: {
      stop: "Arrêter la démo",
      loading: "Chargement de la voix",
      play: "Jouer l'échantillon",
      text: "Bonjour, bienvenue chez IST Legal !",
    },
  },

  // ----------------------------
  // Vector Database Settings
  // ----------------------------
  vector: {
    title: "Base de données vectorielle",

    description:
      "Ce sont les identifiants et les paramètres qui déterminent le fonctionnement de votre instance de plateforme. Il est important que ces clés soient à jour et correctes.",

    provider: {
      title: "Fournisseur de base de données vectorielle",
      description: "Aucune configuration n'est nécessaire pour LanceDB.",
      "search-db":
        "Rechercher tous les fournisseurs de bases de données vectorielles",
      search: "Rechercher toutes les bases de données vectorielles",
      select: "Sélectionner le fournisseur de base de données vectorielle",
    },

    warning:
      "Changer de base de données vectorielle nécessitera de ré-intégrer tous les documents dans tous les espaces de travail pertinents. Cela peut prendre du temps.",

    search: {
      title: "Mode de recherche vectorielle",
      mode: {
        "globally-enabled":
          "Ce paramètre est contrôlé globalement dans les paramètres système. Visitez les paramètres système pour modifier le comportement de reclassement.",
        default: "Recherche standard",
        "default-desc":
          "Recherche de similarité vectorielle standard sans reclassement.",
        "accuracy-optimized": "Optimisé pour la précision",
        "accuracy-desc":
          "Reclasse les résultats pour améliorer la précision en utilisant l'attention croisée.",
      },
    },
  },
  // ----------------------------
  // Vector DB Descriptions
  // ----------------------------
  vectordb: {
    lancedb:
      "Base de données vectorielle 100 % locale qui fonctionne sur la même instance que la plateforme.",
    chroma:
      "Base de données vectorielle open-source que vous pouvez héberger vous-même ou sur le cloud.",
    pinecone:
      "Base de données vectorielle 100 % cloud pour des cas d'utilisation en entreprise.",
    zilliz:
      "Base de données vectorielle hébergée sur le cloud, conçue pour les entreprises avec conformité SOC 2.",
    qdrant:
      "Base de données vectorielle open-source, locale ou distribuée sur le cloud.",
    weaviate:
      "Base de données vectorielle multimodale open-source, locale ou hébergée sur le cloud.",
    milvus: "Open-source, hautement évolutif et extrêmement rapide.",
    astra: "Recherche vectorielle pour GenAI dans le monde réel.",
  },
  // ----------------------------
  // DEEP SEARCH SETTINGS
  // ----------------------------
  deep_search: {
    title: "Recherche Approfondie",
    description:
      "Configurez les capacités de recherche web pour les réponses de chat. Lorsqu'activé, le système peut rechercher sur le web des informations pour améliorer les réponses.",
    enable: "Activer la Recherche Approfondie",
    enable_description:
      "Permettre au système de rechercher sur le web des informations lors de la réponse aux requêtes.",
    provider_settings: "Paramètres du Fournisseur",
    provider: "Fournisseur de Recherche",
    model: "Modèle",
    api_key: "Clé API",
    api_key_placeholder: "Entrez votre clé API",
    api_key_placeholder_set:
      "La clé API est définie (entrez une nouvelle clé pour la modifier)",
    api_key_help:
      "Votre clé API est stockée de manière sécurisée et utilisée uniquement pour les requêtes de recherche web.",
    context_percentage: "Pourcentage de Contexte",
    context_percentage_help:
      "Pourcentage de la fenêtre de contexte du LLM à allouer pour les résultats de recherche web (5-20%).",
    fetch_error:
      "Échec de la récupération des paramètres de Recherche Approfondie",
    save_success: "Paramètres de Recherche Approfondie enregistrés avec succès",
    save_error:
      "Échec de l'enregistrement des paramètres de Recherche Approfondie: {{error}}",
    toast_success:
      "Paramètres de Recherche Approfondie enregistrés avec succès",
    toast_error:
      "Échec de l'enregistrement des paramètres de Recherche Approfondie: {{error}}",
    brave_recommended:
      "Brave Search est actuellement le fournisseur recommandé et le plus fiable.",
  },
  // ----------------------------
  // PDR Settings
  // ----------------------------
  "pdr-settings": {
    title: "Paramètres PDR",

    description:
      "Configurez les paramètres de Parent Document Retrieval pour vos espaces de travail.",

    "desc-end":
      "Ces paramètres influent sur le traitement et l'utilisation des documents PDR dans les réponses du chat.",

    "global-override": {
      title: "Remplacement global du PDR dynamique",
      description:
        "Lorsqu'activé, tous les documents de l'espace de travail seront traités comme PDR-activés pour le contexte dans les réponses. Lorsque désactivé, seuls les documents explicitement marqués comme PDR seront utilisés, ce qui peut réduire le contexte disponible et entraîner des réponses de qualité bien inférieure puisque seuls des fragments vectoriels de recherche seront utilisés comme sources dans ces cas.",
    },

    "toast-success": "Paramètres PDR mis à jour",
    "toast-fail": "Échec de la mise à jour des paramètres PDR",
    "adjacent-vector-limit": "Limite de vecteurs adjacents",

    "adjacent-vector-limit-desc":
      "Nombre de vecteurs adjacents utilisés dans l'algorithme PDR.",
    "adjacent-vector-limit-placeholder":
      "Entrez la limite de vecteurs adjacents",
    "keep-pdr-vectors": "Conserver les vecteurs PDR",

    "keep-pdr-vectors-desc":
      "Si activé, les documents PDR complets et leurs fragments vectoriels seront inclus dans le contexte, ce qui peut améliorer la qualité mais consommer plus de tokens.",
  },
  // =========================
  // AGENTS
  // =========================
  agents: {
    title: "Compétences de l'Agent",
    "agent-skills": "Configurer et gérer les capacités de l'agent",
    "custom-skills": "Compétences Personnalisées",
    back: "Retour",
    "select-skill": "Sélectionner une compétence à configurer",
    "preferences-saved": "Préférences de l'agent enregistrées avec succès",

    "preferences-failed":
      "Échec de l'enregistrement des préférences de l'agent",

    "skill-status": {
      on: "Activé",
      off: "Désactivé",
    },
    "skill-config-updated":
      "Configuration de compétence mise à jour avec succès",
  },
  // =========================
  // AGENT MENU
  // =========================
  "agent-menu.default-agent": "Agent par défaut",
  "agent-menu.ability.rag-search": "Recherche RAG",
  "agent-menu.ability.web-scraping": "Web Scraping",
  "agent-menu.ability.web-browsing": "Navigation Web",
  "agent-menu.ability.save-file-to-browser":
    "Enregistrer le fichier dans le navigateur",
  "agent-menu.ability.list-documents": "Lister les documents",
  "agent-menu.ability.summarize-document": "Résumer le document",
  "agent-menu.ability.chart-generation": "Génération de graphiques",
  // =========================
  // BADGES AGENT PAGE
  // =========================
  badges: {
    default: {
      text: "Par défaut",
      tooltip:
        "Cette compétence est activée par défaut et ne peut pas être désactivée.",
    },
  },
  // ----------------------------
  // Embedded Chats History
  // ----------------------------
  "embed-chats": {
    title: "Chats intégrés",
    export: "Exporter",
    description:
      "Voici toutes les conversations et messages enregistrés provenant de toute intégration publiée.",
    table: {
      embed: "Intégration",
      sender: "Expéditeur",
      message: "Message",
      response: "Réponse",
      at: "Envoyé à",
    },
    delete: {
      title: "Supprimer le chat",
      message:
        "Êtes-vous sûr de vouloir supprimer ce chat ? Cette action est irréversible.",
    },
    config: {
      "delete-title": "Intégration supprimer",
      "delete-message":
        "Êtes-vous sûr de vouloir supprimer cette intégration ? Cette action est irréversible.",
      "disable-title": "Intégration désactiver",
      "disable-message":
        "Êtes-vous sûr de vouloir désactiver cette intégration ? Cette action est irréversible.",
      "enable-title": "Intégration activer",
      "enable-message":
        "Êtes-vous sûr de vouloir activer cette intégration ? Cette action est irréversible.",
    },
  },
  // ----------------------------
  // Embeddable Chat Widgets
  // ----------------------------
  embeddable: {
    title: "Widgets de chat intégrables",
    description:
      "Les widgets de chat intégrables sont des interfaces de chat publiques liées à un seul espace de travail. Ils vous permettent de créer des espaces de travail que vous pouvez publier à l'extérieur.",
    create: "Créer une intégration",
    table: {
      workspace: "Espace de travail",
      chats: "Chats envoyés",
      Active: "Domaines actifs",
    },
  },
  // ----------------------------
  // New Embed (Chat Widget)
  // ----------------------------
  "new-embed": {
    title: "Créer une intégration pour l'espace de travail",
    error: "Erreur : ",
    "desc-start":
      "Après création, un lien vous sera fourni que vous pourrez publier sur votre site web avec un simple",
    script: "script",
    tag: "tag.",
    cancel: "Annuler",
    "create-embed": "Créer l'intégration",
    workspace: "Espace de travail",
    "desc-workspace":
      "Ceci est l'espace de travail sur lequel votre fenêtre de chat sera basée. Les paramètres par défaut seront hérités de l'espace de travail, sauf s'ils sont remplacés ici.",
    "allowed-chat": "Méthode de chat autorisée",
    "desc-query":
      "Définissez comment votre chatbot doit fonctionner. Le mode « Interrogation » signifie qu'il répondra uniquement si un document aide à répondre à la requête.",
    "desc-chat":
      "Le mode « Chat » permet de répondre à des questions générales, même si elles ne sont pas liées à l'espace de travail.",
    "desc-response":
      "Chat : Répondre à toutes les questions, quel que soit le contexte",
    "query-response":
      "Interrogation : Répondre uniquement aux messages liés aux documents de l'espace de travail",
    restrict: "Restreindre les demandes par domaine",
    filter:
      "Ce filtre bloquera toute demande provenant d'un domaine non autorisé dans la liste ci-dessous.",
    "use-embed":
      "Laisser ce champ vide signifie que n'importe qui peut utiliser votre intégration sur n'importe quel site.",
    "max-chats": "Chats maximum par jour",
    "limit-chats":
      "Limitez le nombre de chats que cette intégration peut traiter en 24 heures. Zéro signifie illimité.",
    "chats-session": "Chats maximum par session",
    "limit-chats-session":
      "Limitez le nombre de chats qu'un utilisateur peut envoyer dans le cadre de cette intégration en 24 heures. Zéro signifie illimité.",
    "enable-dynamic": "Activer l'utilisation dynamique du modèle",
    "llm-override":
      "Permet de définir un modèle LLM préféré pour remplacer le paramètre par défaut de l'espace de travail.",
    "llm-temp": "Activer la température LLM dynamique",
    "desc-temp":
      "Permet de définir la température du LLM pour remplacer le paramètre par défaut de l'espace de travail.",
    "prompt-override": "Activer le remplacement de l'invite",
    "desc-override":
      "Permet de définir une invite système personnalisée pour remplacer celle par défaut de l'espace de travail.",
  },
  // =========================
  // EMBED CONFIGS
  // =========================
  embedConfigs: {
    "show-code": "Afficher le code",
    enable: "Activer",
    disable: "Désactiver",
    "all-domains": "tous",
    "disable-confirm":
      "Êtes-vous sûr de vouloir désactiver cet embed ?\nUne fois désactivé, l'embed ne répondra plus aux demandes de chat.",
    "delete-confirm":
      "Êtes-vous sûr de vouloir supprimer cet embed ?\nUne fois supprimé, l'embed ne répondra plus aux chats et ne sera plus actif.\n\nCette action est irréversible.",
    "disabled-toast": "L'embed a été désactivé",
    "enabled-toast": "L'embed est actif",
  },
  // ----------------------------
  // Event Logs
  // ----------------------------
  event: {
    title: "Journaux d'événements",
    description:
      "Consultez toutes les actions et événements sur cette instance pour la surveillance.",
    clear: "Effacer les journaux d'événements",
    table: {
      type: "Type d'événement",
      user: "Utilisateur",
      occurred: "Survenu à",
    },
  },
  // ----------------------------
  // API Keys
  // ----------------------------
  api: {
    title: "Clés API",
    description:
      "Les clés API permettent d'accéder et de gérer cette instance de manière programmatique.",
    link: "Lire la documentation de l'API",
    generate: "Générer une nouvelle clé API",
    table: {
      key: "Clé API",
      by: "Créé par",
      created: "Créé",
    },
    new: {
      title: "Créer une nouvelle clé API",
      description:
        "Une fois créée, la clé API peut être utilisée pour accéder et configurer cette instance de manière programmatique.",
      doc: "Lire la documentation de l'API",
      cancel: "Annuler",
      "create-api": "Créer une clé API",
    },
  },
  // ----------------------------
  // Browser Extension API Keys
  // ----------------------------
  "browser-extension-api": {
    title: "Clés API",
    description: "Gérez les clés API pour la connexion à cette instance.",
    "generate-key": "Générer une nouvelle clé API",
    "table-headers": {
      "connection-string": "Chaîne de connexion",
      "created-by": "Créé par",
      "created-at": "Créé le",
      actions: "Actions",
    },
    "no-keys": "Aucune clé API trouvée",
    modal: {
      title: "Nouvelle clé API d'extension de navigateur",
      "multi-user-warning":
        "Attention : Vous êtes en mode multi-utilisateurs. Cette clé API permettra l'accès à tous les espaces de travail associés à votre compte. Veuillez la partager avec précaution.",
      "create-description":
        'Après avoir cliqué sur "Créer une clé API", cette instance tentera de créer une nouvelle clé API pour l\'extension de navigateur.',
      "connection-help":
        "Si vous voyez \"Connecté à IST Legal\" dans l'extension, la connexion a réussi. Sinon, veuillez copier la chaîne de connexion et la coller manuellement dans l'extension.",
      cancel: "Annuler",
      "create-key": "Créer une clé API",
      "copy-key": "Copier la clé API",
      "key-copied": "Clé API copiée !",
    },
    tooltips: {
      "copy-connection": "Copier la chaîne de connexion",
      "auto-connect": "Se connecter automatiquement à l'extension",
    },
    confirm: {
      revoke:
        "Êtes-vous sûr de vouloir révoquer cette clé API d'extension de navigateur ?\nAprès cela, elle ne sera plus utilisable.\n\nCette action est irréversible.",
    },
    toasts: {
      "key-revoked":
        "Clé API d'extension de navigateur révoquée définitivement",
      "revoke-failed": "Échec de la révocation de la clé API",
      copied: "Chaîne de connexion copiée dans le presse-papiers",
      connecting: "Tentative de connexion à l'extension de navigateur...",
    },
    "revoke-title": "Révoquer la clé API d'extension de navigateur",
    "revoke-message":
      "Êtes-vous sûr de vouloir révoquer cette clé API d'extension de navigateur ?\nAprès cela, elle ne sera plus utilisable.\n\nCette action est irréversible.",
  },
  // ----------------------------
  // Security (Additional)
  // ----------------------------
  security: {
    "multi-user-mode-permanent":
      "Le mode multi-utilisateur est activé en permanence pour des raisons de sécurité",
    "password-validation": {
      "restricted-chars":
        "Votre mot de passe contient des caractères restreints. Les symboles autorisés sont _,-,!,@,$,%,^,&,*,(,),;",
    },
    "public-workspace": {
      "access-description":
        "Lorsque activé, tout utilisateur peut accéder aux espaces de travail publics sans se connecter.",
    },
    button: {
      saving: "Enregistrement...",
      "save-changes": "Enregistrer les modifications",
    },
  },
  // ----------------------------
  // Multi-User Mode
  // ----------------------------
  multi: {
    title: "Mode multi-utilisateur",
    description:
      "Configurez votre instance pour prendre en charge votre équipe en activant le mode multi-utilisateur.",
    enable: {
      "is-enable": "Le mode multi-utilisateur est activé",
      enable: "Activer le mode multi-utilisateur",
      description:
        "Par défaut, vous serez le seul administrateur. En tant qu'administrateur, vous devrez créer des comptes pour tous les nouveaux utilisateurs ou administrateurs. Ne perdez pas votre mot de passe, car seul un administrateur peut réinitialiser les mots de passe.",
      username: "Adresse e-mail du compte administrateur",
      password: "Mot de passe du compte administrateur",
      "username-placeholder": "Votre nom d'utilisateur administrateur",
      "password-placeholder": "Votre mot de passe administrateur",
    },
    password: {
      title: "Protection par mot de passe",
      description:
        "Protégez votre instance avec un mot de passe. Si vous l'oubliez, il n'existe pas de méthode de récupération, alors assurez-vous de le sauvegarder.",
    },
    instance: {
      title: "Protéger l'instance par mot de passe",
      description:
        "Par défaut, vous serez le seul administrateur. En tant qu'administrateur, vous devrez créer des comptes pour tous les nouveaux utilisateurs ou administrateurs. Ne perdez pas votre mot de passe, car seul un administrateur peut réinitialiser les mots de passe.",
      password: "Mot de passe de l'instance",
    },
  },
  // =========================
  // EXPERIMENTAL FEATURES
  // =========================
  experimental: {
    title: "Fonctionnalités Expérimentales",
    description: "Fonctionnalités actuellement en phase de test bêta",
    "live-sync": {
      title: "Synchronisation de Documents en Direct",
      description:
        "Activer la synchronisation automatique du contenu depuis des sources externes",
      "manage-title": "Documents surveillés",
      "manage-description":
        "Voici tous les documents actuellement surveillés dans votre instance. Le contenu de ces documents sera synchronisé périodiquement.",
      "document-name": "Nom du document",
      "last-synced": "Dernière synchronisation",
      "next-refresh": "Temps jusqu'à la prochaine actualisation",
      "created-on": "Créé le",
      "auto-sync": "Synchronisation Automatique du Contenu",
      "sync-description":
        'Activez la possibilité de spécifier une source de contenu à "surveiller". Le contenu surveillé sera régulièrement récupéré et mis à jour dans cette instance.',
      "sync-workspace-note":
        "Le contenu surveillé sera automatiquement mis à jour dans tous les espaces de travail où il est référencé.",
      "sync-limitation":
        "Cette fonctionnalité s'applique uniquement au contenu web, comme les sites web, Confluence, YouTube et les fichiers GitHub.",
      documentation: "Documentation et Avertissements",
      "manage-content": "Gérer le Contenu Surveillé",
    },
    tos: {
      title: "Conditions d'utilisation des fonctionnalités expérimentales",
      description:
        "Les fonctionnalités expérimentales de cette plateforme sont des fonctionnalités que nous testons et qui sont optionnelles. Nous vous informerons de manière proactive de toute préoccupation potentielle avant l'approbation d'une fonctionnalité.",
      "possibilities-title":
        "L'utilisation d'une fonctionnalité sur cette page peut entraîner, sans s'y limiter, les possibilités suivantes :",
      possibilities: {
        "data-loss": "Perte de données.",
        "quality-change": "Changement dans la qualité des résultats.",
        "storage-increase": "Augmentation du stockage.",
        "resource-consumption":
          "Augmentation de la consommation de ressources.",
        "cost-increase":
          "Augmentation des coûts ou de l'utilisation des fournisseurs LLM ou d'embedding.",
        "potential-bugs":
          "Bugs ou problèmes potentiels lors de l'utilisation de cette application.",
      },
      "conditions-title":
        "L'utilisation d'une fonctionnalité expérimentale est accompagnée de la liste non exhaustive de conditions suivantes :",
      conditions: {
        "future-updates":
          "La fonctionnalité peut ne pas exister dans les futures mises à jour.",
        stability: "La fonctionnalité utilisée n'est actuellement pas stable.",
        availability:
          "La fonctionnalité peut ne pas être disponible dans les futures versions, configurations ou abonnements de cette instance.",
        privacy:
          "Vos paramètres de confidentialité seront respectés lors de l'utilisation d'une fonctionnalité bêta.",
        changes:
          "Ces conditions peuvent changer dans les futures mises à jour.",
      },
      "read-more": "Si vous souhaitez en savoir plus, vous pouvez consulter",
      contact: "ou contacter",
      reject: "Refuser & Fermer",
      accept: "Je comprends",
    },
    "update-failed": "Échec de la mise à jour du statut de la fonctionnalité",
  },
};
