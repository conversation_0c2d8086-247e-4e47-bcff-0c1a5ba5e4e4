/**
 * Endpoints for managing user custom system prompts
 */
const { reqBody, userFromSession } = require("../utils/http");
const { validatedRequest } = require("../utils/middleware/validatedRequest");
const prisma = require("../utils/prisma");
const { User } = require("../models/user");
const { SystemSettings } = require("../models/systemSettings");
const { DEFAULT_SYSTEM_PROMPT } = require("../utils/chats/streamLQA");

function userCustomSystemPromptEndpoints(router) {
  if (!router) {
    console.error(
      "Router object is undefined in userCustomSystemPromptEndpoints"
    );
    return;
  }

  /**
   * Get the user's custom system prompt
   * Returns the user's custom_system_prompt field and the default system prompt
   */
  router.get(
    "/user/custom-system-prompt",
    [validatedRequest],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);

        if (!user || !user.id) {
          return response.status(401).json({
            success: false,
            error: "Unauthorized",
          });
        }

        // Get the user's custom system prompt
        const userSettings = await User._get({ id: user.id });

        if (!userSettings) {
          return response.status(404).json({
            success: false,
            error: "User not found",
          });
        }

        return response.status(200).json({
          success: true,
          customPrompt: userSettings.custom_system_prompt || "",
          defaultPrompt: DEFAULT_SYSTEM_PROMPT,
        });
      } catch (error) {
        console.error(
          "Error in GET /user/custom-system-prompt:",
          error.message
        );
        return response.status(500).json({
          success: false,
          error: "Internal server error",
        });
      }
    }
  );

  /**
   * Update the user's custom system prompt
   * Allows setting or clearing the custom_system_prompt field
   */
  router.post(
    "/user/custom-system-prompt",
    [validatedRequest],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);
        if (!user || !user.id) {
          return response.status(401).json({
            success: false,
            error: "Unauthorized",
          });
        }

        const { customPrompt } = reqBody(request);

        // Validate the custom prompt using the User model validation
        let validatedPrompt;
        try {
          validatedPrompt = User.validations.custom_system_prompt(customPrompt);
        } catch (validationError) {
          return response.status(400).json({
            success: false,
            error: validationError.message,
          });
        }

        // Update the user in the database
        try {
          await prisma.users.update({
            where: { id: user.id },
            data: { custom_system_prompt: validatedPrompt },
          });

          return response.status(200).json({
            success: true,
            message: validatedPrompt
              ? "Custom system prompt updated successfully"
              : "Custom system prompt cleared successfully",
            customPrompt: validatedPrompt,
          });
        } catch (dbError) {
          console.error(
            "Database error updating user custom system prompt:",
            dbError
          );
          return response.status(500).json({
            success: false,
            error: "Failed to update custom system prompt in database",
          });
        }
      } catch (error) {
        console.error("Error updating user custom system prompt:", error);
        return response.status(500).json({
          success: false,
          error: "Failed to update user custom system prompt",
        });
      }
    }
  );

  /**
   * Delete the user's custom system prompt
   * Clears the custom_system_prompt field
   */
  router.delete(
    "/user/custom-system-prompt",
    [validatedRequest],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);
        if (!user || !user.id) {
          return response.status(401).json({
            success: false,
            error: "Unauthorized",
          });
        }

        // Clear the custom system prompt
        try {
          await prisma.users.update({
            where: { id: user.id },
            data: { custom_system_prompt: null },
          });

          return response.status(200).json({
            success: true,
            message: "Custom system prompt cleared successfully",
          });
        } catch (dbError) {
          console.error(
            "Database error clearing user custom system prompt:",
            dbError
          );
          return response.status(500).json({
            success: false,
            error: "Failed to clear custom system prompt in database",
          });
        }
      } catch (error) {
        console.error("Error clearing user custom system prompt:", error);
        return response.status(500).json({
          success: false,
          error: "Failed to clear user custom system prompt",
        });
      }
    }
  );
}

module.exports = { userCustomSystemPromptEndpoints };
