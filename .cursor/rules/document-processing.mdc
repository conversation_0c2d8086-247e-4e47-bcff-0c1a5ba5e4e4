---
description: Guidelines for document processing and RAG implementation
globs: server/**/*.js,collector/**/*.js
alwaysApply: false
---
# Document Processing and RAG Implementation

## Document Ingestion

### Supported Document Types

- **Document Files**: PDF, DOCX, XLSX, PPTX, TXT, MD, RTF, HTML, XML, JSON, EPUB
- **Email Files**: MBOX, EML
- **Media Files**: Audio (MP3, WAV, M4A) and Video (MP4, AVI) via transcription
- **Code Files**: Various programming languages
- **Archive Files**: ZIP, TAR (contents extracted and processed)
- **Remote Sources**: Web pages, GitHub repositories, YouTube videos, Confluence pages
- **Raw Text**: Direct text input

### Ingestion Process

- The Collector service handles document ingestion from various sources
- Documents are converted to a standardized text format
- Metadata is extracted and stored with the document content
- Large documents are split into manageable chunks

## Document Processing Flow

1. **Upload or Provide Link/Text**: User sends a request with a file, link, or raw text
2. **Validation**: Request is validated for integrity and format
3. **Extraction & Conversion**: Content is extracted and converted to text using specialized converters
4. **Text Processing**: Text is cleaned, normalized, and structured
5. **Chunking**: Large content is split into manageable chunks using semantic or size-based strategies
6. **Metadata Extraction**: Document metadata and processing metadata are collected
7. **Output Generation**: Standardized JSON output is created
8. **Storage**: Completed JSON is stored in the server's storage

## Chunking Strategies

- **Semantic Chunking**: Respects document structure (paragraphs, sections)
- **Size-Based Chunking**: Ensures chunks don't exceed token limits
- **Hybrid Chunking**: Combines semantic and size-based approaches
- **Specialized Chunking**: Table chunking, code chunking, conversation chunking

## Output Format

```json
{
  "metadata": {
    "title": "Document Title",
    "author": "Document Author",
    "created_at": "2023-01-01T00:00:00Z",
    "source_type": "pdf",
    "language": "en",
    "processed_at": "2023-03-01T00:00:00Z",
    "token_count_estimate": 1500
  },
  "chunks": [
    {
      "id": "chunk-1",
      "content": "Chunk content...",
      "token_count_estimate": 250,
      "metadata": {
        "page": 1,
        "section": "Introduction"
      }
    }
    // Additional chunks...
  ]
}
```

## Vector Database Integration

- Multiple vector database providers are supported (Pinecone, Qdrant, Weaviate, LanceDB, etc.)
- Document text is embedded using various embedding models
- Vector search is used to retrieve relevant documents for RAG
- Search modes (similarity, MMR) can be configured
- Results are ranked and filtered based on relevance

## RAG Implementation

- Retrieved documents are used to augment LLM prompts
- Context windows are managed to stay within token limits
- Sources are tracked and can be displayed to users
- Document similarity thresholds can be configured
- Context backfilling maintains conversation coherence

## Watched Document Synchronization

- External documents can be watched for changes
- Background jobs periodically check for updates
- Changed documents are re-processed and re-embedded
- This ensures RAG queries use the most up-to-date information
- Sync frequency can be configured per document

## Legal Document Handling

- Special handling for legal documents
- Detection of legal content based on filename patterns and content analysis
- Support for legal document templates and formatting
- Integration with document drafting workflows
- Preservation of document structure and formatting

## Tri-Flow Document Drafting (`streamChatWithWorkspaceCDB`)

The `streamChatWithWorkspaceCDB` function and its underlying modules implement a sophisticated, multi-step process for drafting legal documents. This system now operates using a tri-flow architecture, managed by `server/utils/chats/flowDispatcher.js`.

### Core Modules:
-   **`server/utils/chats/streamCDB.js`**: Main entry point for these drafting tasks.
-   **`server/utils/chats/flowDispatcher.js`**: Routes to the appropriate flow based on `flowType`.
-   **`server/utils/chats/flows/mainDoc.js`**: Handles drafting when a main document is specified (7 steps).
-   **`server/utils/chats/flows/noMainDoc.js`**: Handles drafting when no main document is specified (7 steps).
-   **`server/utils/chats/flows/referenceFlow.js`**: Handles compliance analysis and comparison tasks (5 steps).

### Flow Types:
1.  **Main Document Flow (`"main"`)**: 7-step pipeline centered around a primary document.
2.  **No Main Document Flow (`"noMain"`)**: 7-step pipeline for multi-source synthesis.
3.  **Reference Files Comparison Flow (`"referenceFiles"`)**: 5-step pipeline for compliance analysis.

### Pipeline Stages:

#### Main Document Flow & No Main Document Flow (7 Steps):
1.  **Create List-of-Sections**: Generate document structure
2.  **Review & Describe Source Documents**: Analyze and summarize all documents
3.  **Map Documents to Sections**: Associate relevant documents with sections
4.  **Identify Legal Issues per Section**: Find legal questions for each section
5.  **Generate Legal Memos**: Create detailed legal analysis (optional)
6.  **Draft Section Content**: Write content for each section
7.  **Finalize & Cleanup**: Assemble final document and clean up

#### Reference Files Comparison Flow (5 Steps):
1.  **Process Reference Files**: Analyze rules and standards
2.  **Process Review Files**: Analyze documents for compliance
3.  **Generate Section List**: Create compliance report structure
4.  **Draft Compliance Sections**: Write compliance analysis sections
5.  **Finalize Compliance Report**: Assemble final report and clean up

Developers working on or extending these drafting capabilities should familiarize themselves with these core modules and the distinct logic paths for each `flowType`. Ensure that any new shared functionality is added to `helpers/documentProcessing.js` and new prompts to `prompts/legalDrafting.js` where appropriate.
