import React, { useState, useEffect } from "react";
import System from "@/models/system";
import SingleUserAuth from "./SingleUserAuth";
import MultiUserAuth from "./MultiUserAuth";
import UserAuthRwanda from "./UserAuthRwanda";
import tenderflowLogo from "@/media/logo/tenderFlow.jpg";
import Admin from "@/models/admin";
import { useTranslation } from "react-i18next";
import { AUTH_TOKEN, AUTH_USER, AUTH_TIMESTAMP } from "@/utils/constants";
import { ICON_COMPONENTS } from "@/components/Footer";
import showToast from "@/utils/toast";
import useLogo from "@/hooks/useLogo";
import AuthTinderFLow from "./AuthTinderFLow";

export default function PasswordModal({ mode = "single" }) {
  const { t } = useTranslation();
  const { logo } = useLogo();
  const [customWebsiteLink, setCustomWebsiteLink] = useState("");
  const [loading, setLoading] = useState(true);
  const [footerIcons, setFooterIcons] = useState([]);
  const [loginUI, setLoginUI] = useState(null);
  const [isFetched, setIsFetched] = useState(false);
  const [customAppName, setCustomAppName] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [websiteLinkData, footerData, appNameData] = await Promise.all([
          System.fetchCustomWebsiteLink(),
          System.fetchCustomFooterIcons(),
          System.fetchCustomAppName(),
        ]);

        setFooterIcons(footerData.footerData || []);
        setCustomWebsiteLink(websiteLinkData.websiteLink || "");
        setCustomAppName(appNameData?.appName || "");
      } catch (error) {
        showToast(t("show-toast.update-failed"), "error");
      }
      setLoading(false);
    };
    fetchData();
  }, [t]);

  useEffect(() => {
    async function fetchLoginUI() {
      try {
        const response = await Admin.defaultLoginUi();
        const settings = response?.settings;
        if (settings && settings.login_ui) {
          setLoginUI(settings.login_ui);
        } else {
          setLoginUI("ist-legal-general"); // Fallback
        }
        setIsFetched(true);
      } catch (error) {
        showToast(t("show-toast.update-failed"), "error", {
          clear: true,
        });
        setLoginUI("ist-legal-general");
        setIsFetched(true);
      }
    }
    fetchLoginUI();
  }, [t]);

  // Helper function to render footer icons
  const renderFooterIcons = (className = "tenderFlow-icons tenderFlow-svg") => {
    if (!footerIcons || footerIcons.length === 0) return null;

    return (
      <div className={className}>
        {footerIcons.map((iconData, index) =>
          iconData && iconData.icon && iconData.url ? (
            <a
              key={index}
              href={iconData.url}
              target="_blank"
              rel="noopener noreferrer"
            >
              {React.createElement(
                ICON_COMPONENTS[iconData.icon] || (() => <span></span>),
                {
                  size: className.includes("hidden") ? 24 : 20,
                  className: className.includes("hidden")
                    ? "tender-icon"
                    : "tender-icons",
                }
              )}
            </a>
          ) : null
        )}
      </div>
    );
  };

  const renderRwandaWebsiteLink = () => {
    if (!customWebsiteLink) return null;

    return (
      <>
        {loading ? (
          <div className="visite-website-btn block bottom-2">
            <p className="text-[10px]">{t("login-ui.loading")}</p>
          </div>
        ) : (
          <div className="pl-0 md:mt-[20px] ml-0 md:ml-[42px] visite-website-btn hover:text-foreground block bottom-2">
            <a href={customWebsiteLink} className="text-[17px] outline-none">
              {t("login-ui.visit-website")}
            </a>
          </div>
        )}
      </>
    );
  };

  const renderTenderFlowWebsiteLink = () => {
    if (!customWebsiteLink) return null;

    return (
      <div className="block footer absolute bottom-2">
        {loading ? (
          <p className="text-[10px]">{t("loading")}</p>
        ) : (
          <a
            href={customWebsiteLink}
            className="text-[12px] outline-none text-foreground underline"
          >
            {t("login-ui.visit-website")}
          </a>
        )}
      </div>
    );
  };

  // Website link for General UI
  const renderGeneralWebsiteLink = () => {
    if (!customWebsiteLink) return null;

    return (
      <div className="text-center">
        {loading ? (
          <p className="text-[10px]">{t("loading")}</p>
        ) : (
          <a
            href={customWebsiteLink}
            className="text-[12px] outline-none text-foreground underline"
          >
            {t("login-ui.visit-website")}
          </a>
        )}
      </div>
    );
  };

  // Render different login UIs based on the loginUI setting
  const renderLoginUI = () => {
    const commonProps = {
      customWebsiteLink,
      loading,
      footerIcons,
      customAppName,
    };

    switch (loginUI) {
      case "tender-flow":
        return (
          <div className="fixed top-0 left-0 right-0 z-50 w-full overflow-x-hidden overflow-y-auto md:inset-0 h-full flex flex-col md:flex-row items-center justify-center">
            <div className="bg-card hidden md:flex md:w-1/2 md:h-full md:items-center md:justify-center">
              <div className="flex flex-col items-center justify-center">
                <img
                  className="tenderFlow-svg w-[60%]"
                  src={tenderflowLogo}
                  alt="Company Logo"
                  draggable="false"
                  onDragStart={(e) => e.preventDefault()}
                />
                <div className="flex flex-row align-center justify-center gap-2">
                  <img
                    className="w-80 tenderFlow-logo"
                    src={logo}
                    alt="Company Logo"
                  />
                  {renderFooterIcons()}
                </div>
              </div>
              {renderTenderFlowWebsiteLink()}
            </div>
            <div className="tender-flow-form-blk flex flex-col items-center justify-center h-full w-full md:w-1/2 z-50 relative">
              <AuthTinderFLow {...commonProps} />
            </div>
          </div>
        );

      case "ist-legal-rwanda":
        return (
          <div className="fixed top-0 left-0 right-0 w-full overflow-x-hidden overflow-y-auto md:inset-0 h-full flex flex-col md:flex-row items-center md:justify-center gap-16 md:gap-1 bg-background">
            <div className="right-block h-fit md:h-full pb-20 md:pb-0 md:flex md:w-[100%] md:items-center md:justify-center">
              <div className="text-white flex flex-col items-center md:items-start w-full md:w-2/3 justify-center">
                <div className="flex flex-row gap-2 items-center pt-3 md:pt-0">
                  <span className="hidden md:flex login-border-left"></span>
                  <h1 className="text-[21px] md:text-4xl lg:text-6xl pt-[10px] pb-0 md:pb-3 px-3 login-main-title w-full md:w-2/3">
                    {t("login.multi-user.welcome")} <b>{customAppName}</b>
                  </h1>
                </div>
                <p className="text-[12px] md:text-xl lg:text-2xl pb-12 md:pt-18 pt-0 md:pt-7 pl-0 md:pl-3 ml-0 md:ml-[35px] text-center md:text-left w-full md:w-2/3 login-second-title">
                  {t("login-ui.rw-login-description")}
                </p>
                {renderRwandaWebsiteLink()}
              </div>
            </div>
            <div className="flex flex-col items-center justify-center h-fit w-full md:w-1/2">
              <UserAuthRwanda {...commonProps} />
            </div>
          </div>
        );

      default:
        return (
          <div className="h-screen bg-[var(--light-bg)]">
            <div className="pt-[6vw] px-8">
              {mode === "single" ? (
                <SingleUserAuth />
              ) : (
                <MultiUserAuth {...commonProps} />
              )}
            </div>

            <div className="md:absolute md:bottom-16 md:mt-0 mt-16 flex flex-col items-center gap-3 w-full">
              {renderFooterIcons("hidden tenderFlow-icons")}
              {renderGeneralWebsiteLink()}
            </div>
          </div>
        );
    }
  };

  return <>{renderLoginUI()}</>;
}

export function usePasswordModal(notry = false) {
  const [auth, setAuth] = useState({
    loading: true,
    requiresAuth: false,
    mode: "single",
  });

  useEffect(() => {
    async function checkAuthReq() {
      if (!window) return;

      if (!System.needsAuthCheck() && notry === false) {
        setAuth({
          loading: false,
          requiresAuth: false,
          mode: "multi",
        });
        return;
      }

      const settings = await System.keys();
      if (settings?.MultiUserMode) {
        const currentToken = window.localStorage.getItem(AUTH_TOKEN);
        if (currentToken) {
          const valid = notry ? false : await System.checkAuth(currentToken);
          if (!valid) {
            setAuth({
              loading: false,
              requiresAuth: true,
              mode: "multi",
            });
            window.localStorage.removeItem(AUTH_USER);
            window.localStorage.removeItem(AUTH_TOKEN);
            window.localStorage.removeItem(AUTH_TIMESTAMP);
            return;
          } else {
            setAuth({
              loading: false,
              requiresAuth: false,
              mode: "multi",
            });
            return;
          }
        } else {
          setAuth({
            loading: false,
            requiresAuth: true,
            mode: "multi",
          });
          return;
        }
      } else {
        const requiresAuth = settings?.RequiresAuth || false;
        if (!requiresAuth) {
          setAuth({
            loading: false,
            requiresAuth: false,
            mode: "single",
          });
          return;
        }

        const currentToken = window.localStorage.getItem(AUTH_TOKEN);
        if (currentToken) {
          const valid = notry ? false : await System.checkAuth(currentToken);
          if (!valid) {
            setAuth({
              loading: false,
              requiresAuth: true,
              mode: "single",
            });
            window.localStorage.removeItem(AUTH_TOKEN);
            window.localStorage.removeItem(AUTH_USER);
            window.localStorage.removeItem(AUTH_TIMESTAMP);
            return;
          } else {
            setAuth({
              loading: false,
              requiresAuth: false,
              mode: "single",
            });
            return;
          }
        } else {
          setAuth({
            loading: false,
            requiresAuth: true,
            mode: "single",
          });
          return;
        }
      }
    }
    checkAuthReq();
  }, [notry]);

  return auth;
}
