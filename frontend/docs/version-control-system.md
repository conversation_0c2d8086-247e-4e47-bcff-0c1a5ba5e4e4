# Version Control System

## Overview

The IST Legal platform includes a built-in version control system that displays the current platform version in the user dropdown menu. This system provides users with visibility into platform updates and helps administrators track deployments. The system features **automatic localization** that displays version descriptions in the user's preferred language.

## Features

- **Version Display**: Shows current version number (e.g., "v1.1.0") in the user dropdown menu
- **Localized Update Descriptions**: Hover tooltip displays description of recent changes in the user's language
- **Automatic Language Detection**: Uses system language settings or browser preferences
- **Multi-Version Support**: Supports both single version and multi-version formats
- **Backend Tracking**: Timestamp logging for deployment tracking
- **Multi-environment Support**: Works in development, production, and Docker environments

## Architecture

### Components

1. **server/data/version.json** (Server data directory)

   - Contains version number, localized descriptions, and timestamp
   - Single source of truth for version information
   - **Special localization system** - uses inline language keys (not locale files)

2. **Backend Endpoint** (`/version`)

   - Serves localized version data to frontend
   - Automatic language detection and description selection
   - Logs access with timestamp for tracking
   - Excludes sensitive timestamp data from frontend response

3. **Language Detection** (`server/utils/helpers/languageDetection.js`)

   - Detects user's preferred language from multiple sources
   - Priority: System settings → Browser Accept-Language → English fallback
   - Selects appropriate localized description

4. **Frontend Model** (`frontend/src/models/version.js`)

   - Handles API communication with authentication headers
   - Provides error handling and fallbacks

5. **VersionDisplay Component** (`frontend/src/components/VersionDisplay/`)
   - Renders version in user dropdown menu
   - Uses SimpleTooltip for hover descriptions
   - Handles loading and error states

## File Structure

```
project-root/
├── server/
│   └── data/
│       └── version.json                         # Version configuration
├── server/endpoints/system.js                     # Backend endpoint
├── frontend/src/
│   ├── models/version.js                          # API model
│   ├── components/VersionDisplay/
│   │   ├── index.jsx                              # Main component
│   │   └── __tests__/index.test.jsx              # Unit tests
│   └── locales/[lang]/common.js                  # Translations
└── server/tests/unit/endpoints/version.test.js   # Backend tests
```

## Localization System

### Special Localization Architecture

**IMPORTANT:** The version system uses a **unique localization approach** that differs from the standard i18n system. Version descriptions are stored directly in `server/data/version.json` with inline language keys, **NOT** in the standard locale files (`frontend/src/locales/[lang]/`).

### Why This Special System?

1. **Centralized Version Management**: All version information in one file
2. **Backend Language Detection**: Server-side localization based on user preferences
3. **Simplified Deployment**: No need to update multiple locale files for version changes
4. **Automatic Fallbacks**: Built-in language detection and fallback logic

### Language Detection Priority

The system detects the user's preferred language in this order:

1. **System Language Setting**: From admin panel (`SystemSettings` database)
2. **Browser Accept-Language Header**: Automatically parsed from user's browser
3. **English Fallback**: Default if no supported language is detected

### Supported Languages

- **English** (`en`): Default language
- **Swedish** (`sv`): Svenska
- **Norwegian** (`no`): Norsk
- **French** (`fr`): Français
- **German** (`de`): Deutsch
- **Polish** (`pl`): Polski
- **Kinyarwanda** (`rw`): Kinyarwanda

## Configuration

### server/data/version.json Format

The version system supports two formats:

#### Single Version Format (Legacy)

```json
{
  "version": "1.1.0",
  "description": "Added new document builder features and improved chat performance",
  "description-sv": "Lade till nya dokumentbyggarfunktioner och förbättrad chattfunktion",
  "description-no": "La til nye dokumentbyggerfunksjoner og forbedret chattfunksjon",
  "description-fr": "Ajout de nouvelles fonctionnalités de création de documents et amélioration du chat",
  "description-de": "Neue Dokumentenerstellungsfunktionen und verbesserte Chat-Funktionen hinzugefügt",
  "description-pl": "Dodano nowe funkcje kreatora dokumentów i poprawiono funkcje czatu",
  "description-rw": "Byongerewe ibintu bishya byo kubaka inyandiko n'guteza imbere ikiganiro",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### Multi-Version Format (Recommended)

```json
{
  "versions": [
    {
      "version": "1.0.0",
      "description": "Initial release with core RAG functionality",
      "description-sv": "Första utgivningen med grundläggande RAG-funktionalitet",
      "description-no": "Første utgivelse med grunnleggende RAG-funksjonalitet",
      "description-fr": "Version initiale avec les fonctionnalités RAG de base",
      "description-de": "Erste Veröffentlichung mit grundlegender RAG-Funktionalität",
      "description-pl": "Wersja początkowa z podstawową funkcjonalnością RAG",
      "description-rw": "Sohoka ya mbere ifite ibikorwa by'ibanze bya RAG",
      "timestamp": "2025-01-01T10:00:00Z"
    },
    {
      "version": "1.1.0",
      "description": "Added new document builder features, news system, and personal style profiles",
      "description-sv": "Lade till nya dokumentbyggarfunktioner, nyhetssystem och personliga stilprofiler",
      "description-no": "La til nye dokumentbyggerfunksjoner, nyhetssystem og personlige stilprofiler",
      "description-fr": "Ajout de nouvelles fonctionnalités de création de documents, système d'actualités et profils de style personnels",
      "description-de": "Neue Dokumentenerstellungsfunktionen, Nachrichtensystem und persönliche Stilprofile hinzugefügt",
      "description-pl": "Dodano nowe funkcje kreatora dokumentów, system wiadomości i osobiste profile stylu",
      "description-rw": "Byongerewe ibintu bishya byo kubaka inyandiko, sisitemu y'amakuru, n'imiterere y'imisusire yihariye",
      "timestamp": "2025-05-31T10:30:00Z"
    }
  ]
}
```

### Required Fields

**For Each Version:**

- `version`: Semantic version number (required)
- `description`: English description of changes (required)
- `description-[lang]`: Localized descriptions for each supported language (required)
- `timestamp`: ISO 8601 timestamp for backend tracking (optional)

### Localization Keys

**CRITICAL:** Use these exact language suffixes in `server/data/version.json`:

- `description`: English (default)
- `description-sv`: Swedish
- `description-no`: Norwegian
- `description-fr`: French
- `description-de`: German
- `description-pl`: Polish
- `description-rw`: Kinyarwanda

### Adding New Version Descriptions

When adding a new version, you **MUST** include descriptions for ALL supported languages:

```json
{
  "version": "1.2.0",
  "description": "Enhanced search functionality and new legal templates",
  "description-sv": "Förbättrad sökfunktionalitet och nya juridiska mallar",
  "description-no": "Forbedret søkefunksjonalitet og nye juridiske maler",
  "description-fr": "Fonctionnalité de recherche améliorée et nouveaux modèles juridiques",
  "description-de": "Verbesserte Suchfunktionen und neue juristische Vorlagen",
  "description-pl": "Ulepszona funkcjonalność wyszukiwania i nowe szablony prawne",
  "description-rw": "Ibikorwa by'ubushakashatsi byatewe imbere n'inyandikokorugero nshya z'amategeko",
  "timestamp": "2025-06-01T10:00:00Z"
}
```

### Version Numbering

Follow semantic versioning (SemVer):

- **Major** (X.0.0): Breaking changes or major new features
- **Minor** (1.X.0): New features, backward compatible
- **Patch** (1.1.X): Bug fixes, backward compatible

## Implementation Details

### Backend Endpoint

**Endpoint:** `GET /version`

**Response:**

```json
{
  "success": true,
  "version": "1.1.0",
  "description": "Added new document builder features and improved chat performance"
}
```

**Features:**

- Reads server/data/version.json from server data directory
- Logs full version data (including timestamp) to console
- Returns only version and description to frontend
- Handles file not found and parsing errors

### Frontend Integration

The VersionDisplay component is integrated into main platform pages:

- Main landing page
- Workspace chat interface
- Settings pages

**Styling:**

- Fixed positioning in bottom-right corner
- Semi-transparent background with backdrop blur
- Hover effects for better UX
- Responsive design

### Frontend Internationalization

**Note:** Only the tooltip title uses standard i18n locale files. The description text comes from the special server/data/version.json localization system.

Translation keys in `frontend/src/locales/[lang]/common.js`:

```javascript
version: {
  "tooltip-title": "Version {{version}}", // Only the tooltip title
}
```

**Tooltip Title Translations:**

- English: "Version {{version}}"
- Swedish: "Version {{version}}"
- French: "Version {{version}}"
- German: "Version {{version}}"
- Norwegian: "Versjon {{version}}"
- Polish: "Wersja {{version}}"
- Kinyarwanda: "Verisiyo {{version}}"

**Description Text:** Comes directly from `server/data/version.json` localization system (see above).

## Usage

### For Developers

#### Updating Version Information

1. **Edit server/data/version.json with ALL language descriptions:**

   ```json
   {
     "versions": [
       {
         "version": "1.2.0",
         "description": "Added new legal templates and improved search functionality",
         "description-sv": "Lade till nya juridiska mallar och förbättrad sökfunktionalitet",
         "description-no": "La til nye juridiske maler og forbedret søkefunksjonalitet",
         "description-fr": "Ajout de nouveaux modèles juridiques et amélioration de la fonctionnalité de recherche",
         "description-de": "Neue juristische Vorlagen und verbesserte Suchfunktionen hinzugefügt",
         "description-pl": "Dodano nowe szablony prawne i poprawiono funkcjonalność wyszukiwania",
         "description-rw": "Byongerewe inyandikokorugero nshya z'amategeko n'ibikorwa by'ubushakashatsi byatewe imbere",
         "timestamp": "2024-02-01T14:30:00Z"
       }
     ]
   }
   ```

2. **Deploy changes** - version updates automatically with localized descriptions

#### Important Localization Rules

- **NEVER add version descriptions to standard locale files** (`frontend/src/locales/[lang]/`)
- **ALWAYS add descriptions directly to server/data/version.json** with language suffixes
- **MUST include ALL supported languages** when adding new versions
- **Use exact language codes**: `sv`, `no`, `fr`, `de`, `pl`, `rw`

#### Adding to New Pages

```jsx
import VersionDisplay from "@/components/VersionDisplay";

export default function MyPage() {
  return (
    <div>
      {/* Your page content */}
      <VersionDisplay />
    </div>
  );
}
```

### For Users

- **View Version**: Look for version number in bottom-right corner
- **See Updates**: Hover over version to see description of recent changes
- **Track Changes**: Version updates indicate new features or improvements

## Testing

### Unit Tests

**Frontend Tests:**

- Component rendering with valid data
- Loading and error states
- CSS classes and positioning
- Tooltip functionality
- Translation integration

**Backend Tests:**

- Successful version data retrieval
- File not found handling
- JSON parsing errors
- Response format validation
- Timestamp exclusion from frontend response

### Running Tests

```bash
# Frontend tests
cd frontend
npm test -- --testPathPattern="version"

# Backend tests
cd server
npm test -- --testPathPattern="version"
```

## Docker Compatibility

The version control system is fully compatible with Docker environments:

1. **Build Context**: server/data/version.json is included in Docker build
2. **Path Resolution**: Relative paths work in containers
3. **Runtime Access**: File accessible at container runtime
4. **No Configuration**: Works without additional Docker setup

## Troubleshooting

### Common Issues

1. **Version not displaying:**

   - Check if server/data/version.json exists in server data directory
   - Verify backend server is running
   - Check browser console for errors

2. **Incorrect version shown:**

   - Ensure server/data/version.json is updated
   - Restart backend server if needed
   - Clear browser cache

3. **Docker deployment issues:**
   - Verify server/data/version.json is not in .dockerignore
   - Check file permissions in container
   - Ensure proper build context

### Debug Information

Backend logs version access:

```
Version info accessed: {
  version: "1.1.0",
  timestamp: "2024-01-15T10:30:00Z",
  description: "Added new document builder features..."
}
```

## Security Considerations

- **Timestamp Privacy**: Timestamps are logged backend-only, not exposed to frontend
- **No Sensitive Data**: Only version and description are public
- **Read-Only Access**: Frontend cannot modify version information
- **Error Handling**: Graceful degradation if version file unavailable

## Future Enhancements

Potential improvements:

- **Version History**: Track multiple previous versions
- **Automatic Updates**: Integration with CI/CD pipelines
- **Release Notes**: Link to detailed changelog
- **Update Notifications**: Alert users to new versions
- **Admin Interface**: GUI for version management

## Related Documentation

- [Frontend Architecture](./frontend-architecture.md)
- [Backend API Documentation](../server/docs/api-documentation.md)
- [Deployment Guide](../deployment-guide.md)
- [Testing Guidelines](./testing-guidelines.md)
