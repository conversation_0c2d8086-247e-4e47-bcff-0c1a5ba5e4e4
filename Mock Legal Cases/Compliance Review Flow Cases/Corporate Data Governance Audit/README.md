# Corporate Data Governance Audit Case

## Scenario Overview

**Case Type**: Compliance Review - Corporate Data Governance Audit
**Flow Type**: Compliance Review Flow
**Jurisdiction**: United States / EU (GDPR Compliance)
**Practice Area**: Corporate Compliance / Data Privacy

## Case Background

TechCorp Global Inc. needs to conduct an internal audit of their data handling practices to ensure compliance with their corporate data governance guidelines and regulatory requirements (GDPR, CCPA). The company has implemented strict internal policies regarding data collection, storage, processing, and sharing.

This case tests the compliance review flow by comparing actual business documents against established corporate guidelines to identify potential violations and compliance gaps.

## Documents Included

### Reference Files (Guidelines)
- **`corporate-data-governance-policy.md`** - Comprehensive corporate policy on data handling
  - Data classification standards
  - Collection and consent requirements
  - Storage and retention policies
  - Third-party sharing restrictions
  - Employee access controls

- **`financial-data-handling-guidelines.md`** - Specific guidelines for financial information
  - Customer financial data protection
  - Payment processing requirements
  - Financial reporting compliance
  - Audit trail requirements

### Documents to Review
- **`customer-onboarding-process.md`** - Customer data collection procedures
  - **PLANTED VIOLATIONS**: Excessive data collection, unclear consent language

- **`third-party-vendor-agreement.md`** - Data sharing agreement with external vendor
  - **PLANTED VIOLATIONS**: Unauthorized data transfers, inadequate security requirements

- **`employee-access-logs.md`** - System access records and procedures
  - **PLANTED VIOLATIONS**: Excessive access privileges, missing audit trails

- **`marketing-campaign-data.md`** - Customer data usage for marketing
  - **PLANTED VIOLATIONS**: Use without consent, retention beyond policy limits

- **`financial-reporting-procedures.md`** - Internal financial data handling
  - **PLANTED VIOLATIONS**: Inadequate encryption, unauthorized access

## Expected Testing Outcomes

When using the Compliance Review Flow with this case:

1. **Violation Detection**: The system should identify all planted compliance violations
2. **Guideline Mapping**: Each violation should be mapped to specific guideline sections
3. **Risk Assessment**: Violations should be categorized by severity and compliance risk
4. **Remediation Suggestions**: The system should suggest corrective actions
5. **Compliance Report**: Generate comprehensive audit report with findings

## Key Compliance Issues to Detect

### Data Collection Violations
- Collection of unnecessary personal data
- Unclear or missing consent mechanisms
- Failure to inform users of data usage

### Data Storage Violations
- Retention beyond policy limits
- Inadequate encryption standards
- Unauthorized data locations

### Data Sharing Violations
- Third-party transfers without proper agreements
- Sharing beyond stated purposes
- Inadequate vendor security requirements

### Access Control Violations
- Excessive employee access privileges
- Missing access audit trails
- Inadequate authentication requirements

## Testing Instructions

1. Upload all reference files (guidelines) to the workspace
2. Upload all documents to be reviewed to the workspace
3. Select the compliance review flow
4. Specify the reference files as guidelines
5. Use a task like: "Review the business documents for compliance with corporate data governance policies and identify any violations"
6. Monitor how the system maps violations to specific guideline sections
7. Verify that all planted violations are detected and properly categorized

## Success Criteria

- All planted violations are detected and reported
- Violations are correctly mapped to specific guideline sections
- Risk levels are appropriately assigned to different violation types
- Remediation recommendations are practical and actionable
- Compliance report is comprehensive and well-structured
- False positives are minimized

## Planted Violations Summary

### High-Risk Violations
1. **Unauthorized International Data Transfer** (third-party vendor agreement)
2. **Financial Data Without Encryption** (financial reporting procedures)
3. **Excessive Data Collection** (customer onboarding)

### Medium-Risk Violations
4. **Missing Consent Documentation** (marketing campaign data)
5. **Excessive Employee Access** (employee access logs)
6. **Data Retention Violations** (marketing campaign data)

### Low-Risk Violations
7. **Incomplete Audit Trails** (employee access logs)
8. **Unclear Privacy Notices** (customer onboarding)
9. **Missing Vendor Security Assessments** (third-party vendor agreement)

## Comparison with Other Flows

Unlike the Main Document Flow (which responds to an existing document) and No Main Document Flow (which creates documents from scratch), this Compliance Review Flow:

- Compares multiple documents against established guidelines
- Identifies specific violations and compliance gaps
- Provides risk assessment and remediation recommendations
- Generates audit reports for compliance purposes
- Tests adherence to internal policies and external regulations

## Regulatory Context

This case addresses compliance with:
- **GDPR** (General Data Protection Regulation)
- **CCPA** (California Consumer Privacy Act)
- **SOX** (Sarbanes-Oxley Act) for financial data
- **Corporate Internal Policies**
- **Industry Best Practices**
