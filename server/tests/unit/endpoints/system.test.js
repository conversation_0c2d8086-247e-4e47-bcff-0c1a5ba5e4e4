const request = require("supertest");
const express = require("express");
const { systemEndpoints } = require("../../../endpoints/system");
const { getLLMProvider } = require("../../../utils/helpers");
const { SystemSettings } = require("../../../models/systemSettings");

// Mock dependencies
jest.mock("../../../utils/helpers", () => ({
  getLLMProvider: jest.fn(),
  getVectorDbClass: jest.fn(),
}));

jest.mock("../../../utils/middleware/validatedRequest", () => ({
  validatedRequest: (req, res, next) => next(),
}));

jest.mock("../../../utils/http", () => ({
  reqBody: (req) => req.body,
  userFromSession: jest.fn(),
  multiUserMode: jest.fn(() => false),
  queryParams: jest.fn(),
  baseHeaders: jest.fn(),
  makeJWT: jest.fn(),
}));

jest.mock("../../../models/systemSettings", () => ({
  SystemSettings: {
    get: jest.fn(),
    getValueOrFallback: jest.fn(),
    publicFields: [],
  },
}));

// Mock other models that might be imported
jest.mock("../../../models/user", () => ({
  User: {
    where: jest.fn(),
  },
}));

jest.mock("../../../models/userToken", () => ({
  UserToken: {},
}));

jest.mock("../../../models/telemetry", () => ({
  Telemetry: {},
}));

jest.mock("../../../models/promptExamples", () => ({
  PromptExamples: {},
}));

jest.mock("../../../models/apiKeys", () => ({
  ApiKey: {},
}));

jest.mock("../../../models/workspaceChats", () => ({
  WorkspaceChats: {},
}));

jest.mock("../../../models/eventLogs", () => ({
  EventLogs: {},
}));

jest.mock("../../../models/slashCommandsPresets", () => ({
  SlashCommandPresets: {},
}));

jest.mock("../../../models/browserExtensionApiKey", () => ({
  BrowserExtensionApiKey: {},
}));

jest.mock("../../../models/workspace", () => ({
  Workspace: {},
}));

jest.mock("../../../models/category", () => ({
  Category: {},
}));

jest.mock("../../../models/Feedback", () => ({
  Feedback: {},
}));

jest.mock("../../../models/documentSyncQueue", () => ({
  DocumentSyncQueue: {},
}));

// Mock utility modules
jest.mock("../../../utils/files", () => ({
  viewLocalFiles: jest.fn(),
  normalizePath: jest.fn(),
  isWithin: jest.fn(),
}));

jest.mock("../../../utils/files/purgeDocument", () => ({
  purgeDocument: jest.fn(),
  purgeFolder: jest.fn(),
  cleanOldDocxSessionFiles: jest.fn(),
}));

jest.mock("../../../utils/helpers/updateENV", () => ({
  updateENV: jest.fn(),
  dumpENV: jest.fn(),
  KEY_MAPPING: {},
}));

jest.mock("../../../utils/files/multer", () => ({
  handleAssetUpload: jest.fn(),
  handlePfpUpload: jest.fn(),
}));

jest.mock("../../../utils/helpers/customModels", () => ({
  getCustomModels: jest.fn(),
  getSupportedModels: jest.fn(),
}));

jest.mock("../../../utils/middleware/multiUserProtected", () => ({
  flexUserRoleValid: jest.fn(() => (req, res, next) => next()),
  ROLES: {
    admin: "admin",
    manager: "manager",
    user: "user",
  },
  isMultiUserSetup: jest.fn(),
}));

jest.mock("../../../utils/files/pfp", () => ({
  fetchPfp: jest.fn(),
  determinePfpFilepath: jest.fn(),
}));

jest.mock("../../../utils/helpers/chat/convertTo", () => ({
  exportChatsAsType: jest.fn(),
}));

jest.mock("../../../utils/collectorApi", () => ({
  CollectorApi: {},
}));

jest.mock("../../../utils/PasswordRecovery", () => ({
  recoverAccount: jest.fn(),
  resetPassword: jest.fn(),
  generateRecoveryCodes: jest.fn(),
}));

jest.mock("../../../utils/EncryptionManager", () => ({
  EncryptionManager: {},
}));

jest.mock("../../../utils/prisma", () => ({}));

jest.mock("../../../utils/i18n", () => ({
  t: jest.fn((key) => key),
}));

jest.mock("../../../utils/chats/streamDD", () => ({
  DEFAULT_COMBINE_PROMPT: "default combine prompt",
  DEFAULT_DOCUMENT_DRAFTING_PROMPT: "default document drafting prompt",
  DEFAULT_LEGAL_ISSUES_PROMPT: "default legal issues prompt",
  DEFAULT_MEMO_PROMPT: "default memo prompt",
}));

jest.mock("../../../utils/chats/prompts/legalDrafting.js", () => ({
  exportedLegalPrompts: [],
}));

jest.mock("../../../utils/chats/helpers/promptManager", () => ({
  PROMPT_MAPPINGS: {},
}));

jest.mock("../../../utils/DocumentManager", () => ({
  DocumentManager: {},
}));

jest.mock("../../../utils/files/logo", () => ({
  getDefaultFilenameLight: jest.fn(),
  getDefaultFilenameDark: jest.fn(),
  determineLogoLightFilepath: jest.fn(),
  determineLogoDarkFilepath: jest.fn(),
  fetchLogo: jest.fn(),
  validFilenameLight: jest.fn(),
  validFilenameDark: jest.fn(),
  renameLogoFile: jest.fn(),
  removeCustomLogoLight: jest.fn(),
  removeCustomLogoDark: jest.fn(),
  LOGO_LIGHT: "logo-light.png",
  LOGO_DARK: "logo-dark.png",
}));

// Mock axios
jest.mock("axios", () => ({
  get: jest.fn(),
  post: jest.fn(),
}));

// Mock uuid
jest.mock("uuid", () => ({
  v4: jest.fn(() => "mock-uuid"),
}));

// Mock dotenv
jest.mock("dotenv", () => ({
  config: jest.fn(),
}));

jest.mock("../../../utils/helpers/tiktoken", () => ({
  TokenManager: jest.fn().mockImplementation(() => ({
    countFromString: jest.fn().mockReturnValue(100), // Mock token count
  })),
}));

describe("POST /system/generate-style-profile", () => {
  let app;
  let mockLLMConnector;

  beforeEach(() => {
    // Set up environment variables
    process.env.LLM_PROVIDER = "openai";
    process.env.LLM_MODEL = "gpt-3.5-turbo";

    app = express();
    app.use(express.json());

    // Mock LLM connector
    mockLLMConnector = {
      getChatCompletion: jest.fn(),
      promptWindowLimit: jest.fn().mockReturnValue(4096), // Mock context window
    };

    getLLMProvider.mockReturnValue(mockLLMConnector);

    // Mock SystemSettings to return null (use default prompt)
    SystemSettings.get.mockResolvedValue(null);

    // Initialize system endpoints
    systemEndpoints(app);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("Success Cases", () => {
    test("generates style profile successfully", async () => {
      const documentContent =
        "This is a sample legal document with formal language and structured paragraphs.";
      const expectedInstructions =
        "Use formal legal language with clear structure and professional tone.";

      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: expectedInstructions,
      });

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent });

      expect(response.status).toBe(200);

      expect(response.body).toEqual({
        success: true,
        styleInstructions: expectedInstructions,
        tokenInfo: {
          documentTokens: 100,
          availableTokens: 1096,
          contextWindow: 4096,
          reservedTokens: 3000,
        },
      });

      expect(getLLMProvider).toHaveBeenCalledWith({
        provider: process.env.LLM_PROVIDER,
        model: process.env.LLM_MODEL,
      });

      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledWith(
        [{ role: "user", content: expect.stringContaining(documentContent) }],
        {
          temperature: 0.3,
          max_tokens: 2000,
        }
      );
    });

    test("includes proper style analysis prompt", async () => {
      const documentContent = "Sample document content";
      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: "Style instructions",
      });

      await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent })
        .expect(200);

      const callArgs = mockLLMConnector.getChatCompletion.mock.calls[0];
      const prompt = callArgs[0][0].content;

      expect(prompt).toContain("legal writing style analyzer");
      expect(prompt).toContain("**Tone and Formality Level**");
      expect(prompt).toContain("**Sentence Structure and Length Patterns**");
      expect(prompt).toContain(
        "**Vocabulary Choices and Legal Terminology Usage**"
      );
      expect(prompt).toContain("**Paragraph Organization and Flow**");
      expect(prompt).toContain("**Use of Citations and References**");
      expect(prompt).toContain("50,000 characters");
      expect(prompt).toContain(documentContent);
    });

    test("trims whitespace from generated instructions", async () => {
      const documentContent = "Sample document";
      const instructionsWithWhitespace = "  \n  Generated instructions  \n  ";

      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: instructionsWithWhitespace,
      });

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent })
        .expect(200);

      expect(response.body.styleInstructions).toBe("Generated instructions");
    });
  });

  describe("Validation Errors", () => {
    test("returns 400 when document content is missing", async () => {
      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({})
        .expect(400);

      expect(response.body).toEqual({
        success: false,
        error: "Document content is required for style analysis",
      });

      expect(mockLLMConnector.getChatCompletion).not.toHaveBeenCalled();
    });

    test("returns 400 when document content is empty string", async () => {
      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: "" })
        .expect(400);

      expect(response.body).toEqual({
        success: false,
        error: "Document content is required for style analysis",
      });
    });

    test("returns 400 when document content is only whitespace", async () => {
      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: "   \n   \t   " })
        .expect(400);

      expect(response.body).toEqual({
        success: false,
        error: "Document content is required for style analysis",
      });
    });
  });

  describe("LLM Provider Errors", () => {
    test("returns 500 when LLM provider is not configured", async () => {
      getLLMProvider.mockReturnValue(null);

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: "Sample content" })
        .expect(500);

      expect(response.body).toEqual({
        success: false,
        error: "No LLM provider configured for style analysis",
      });
    });

    test("returns 500 when LLM generation fails", async () => {
      mockLLMConnector.getChatCompletion.mockRejectedValue(
        new Error("LLM service unavailable")
      );

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: "Sample content" })
        .expect(500);

      expect(response.body).toEqual({
        success: false,
        error: "Internal server error during style generation",
      });
    });

    test("returns 500 when LLM returns empty response", async () => {
      mockLLMConnector.getChatCompletion.mockResolvedValue("");

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: "Sample content" })
        .expect(500);

      expect(response.body).toEqual({
        success: false,
        error: "Failed to generate style instructions",
      });
    });

    test("returns 500 when LLM returns null response", async () => {
      mockLLMConnector.getChatCompletion.mockResolvedValue(null);

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: "Sample content" })
        .expect(500);

      expect(response.body).toEqual({
        success: false,
        error: "Failed to generate style instructions",
      });
    });
  });

  describe("Edge Cases", () => {
    test("handles very long document content", async () => {
      const longContent = "A".repeat(50000); // 50KB of content
      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: "Style instructions",
      });

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: longContent })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalled();
    });

    test("handles special characters in document content", async () => {
      const contentWithSpecialChars =
        "Document with special chars: §, ©, ®, ™, €, £, ¥, ¢";
      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: "Style instructions",
      });

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: contentWithSpecialChars })
        .expect(200);

      expect(response.body.success).toBe(true);

      const callArgs = mockLLMConnector.getChatCompletion.mock.calls[0];
      const prompt = callArgs[0][0].content;
      expect(prompt).toContain(contentWithSpecialChars);
    });

    test("handles unicode characters in document content", async () => {
      const unicodeContent =
        "Document with unicode: 你好, مرحبا, Здравствуйте, こんにちは";
      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: "Style instructions",
      });

      const response = await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: unicodeContent })
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe("LLM Configuration", () => {
    test("uses correct LLM parameters", async () => {
      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: "Instructions",
      });

      await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: "Sample content" })
        .expect(200);

      expect(mockLLMConnector.getChatCompletion).toHaveBeenCalledWith(
        expect.any(Array),
        {
          temperature: 0.3,
          max_tokens: 2000,
        }
      );
    });

    test("uses environment variables for LLM provider configuration", async () => {
      process.env.LLM_PROVIDER = "openai";
      process.env.LLM_MODEL = "gpt-4";

      mockLLMConnector.getChatCompletion.mockResolvedValue({
        textResponse: "Instructions",
      });

      await request(app)
        .post("/system/generate-style-profile")
        .send({ documentContent: "Sample content" })
        .expect(200);

      expect(getLLMProvider).toHaveBeenCalledWith({
        provider: "openai",
        model: "gpt-4",
      });
    });
  });
});
