/**
 * Utility functions for version comparison and handling
 */

/**
 * Compare two semantic version strings
 * @param {string} a - First version string (e.g., "1.2.0")
 * @param {string} b - Second version string (e.g., "1.1.0")
 * @returns {number} - Positive if a > b, negative if a < b, 0 if equal
 */
function compareVersions(a, b) {
  try {
    // Clean and normalize version strings (remove v prefix but keep pre-release info for comparison)
    const cleanA = a.replace(/^v/i, "");
    const cleanB = b.replace(/^v/i, "");

    // Split into base version and pre-release
    const [baseA, preReleaseA] = cleanA.split(/[-+]/);
    const [baseB, preReleaseB] = cleanB.split(/[-+]/);

    // Split base versions into parts
    const partsA = baseA.split(".");
    const partsB = baseB.split(".");

    // Compare each part (major, minor, patch)
    const maxLength = Math.max(partsA.length, partsB.length);

    for (let i = 0; i < maxLength; i++) {
      const partA = parseInt(partsA[i] || "0", 10);
      const partB = parseInt(partsB[i] || "0", 10);

      if (partA > partB) return 1;
      if (partA < partB) return -1;
    }

    // If base versions are equal, compare pre-release status
    // No pre-release (stable) > pre-release
    if (!preReleaseA && preReleaseB) return 1;
    if (preReleaseA && !preReleaseB) return -1;

    // Both have pre-release or both don't - they're equal in terms of version precedence
    return 0;
  } catch (error) {
    console.warn(
      "Error comparing versions, falling back to string comparison:",
      error
    );
    // Fall back to string comparison
    return a.localeCompare(b);
  }
}

/**
 * Normalize version string by removing 'v' prefix and pre-release suffixes
 * @param {string} version - Version string to normalize
 * @returns {string} - Normalized version string
 */
function normalizeVersion(version) {
  if (typeof version !== "string") {
    throw new Error("Version must be a string");
  }

  // Remove 'v' prefix if present (case-insensitive)
  let normalized = version.replace(/^v/i, "");

  // Remove pre-release suffixes (everything after first dash or plus)
  normalized = normalized.split(/[-+]/)[0];

  return normalized;
}

/**
 * Find the highest version from an array of version objects
 * @param {Array} versions - Array of version objects with {version, description, timestamp}
 * @returns {Object|null} - Version object with highest version number, or null if empty
 */
function findHighestVersion(versions) {
  if (!Array.isArray(versions) || versions.length === 0) {
    return null;
  }

  // Filter out versions without version property
  const validVersions = versions.filter((v) => v && v.version);

  if (validVersions.length === 0) {
    return null;
  }

  // Sort versions in descending order and return the highest
  return validVersions.sort((a, b) => compareVersions(b.version, a.version))[0];
}

/**
 * Process version data from version.json - supports both old and new formats
 * @param {Object} versionData - Raw data from version.json
 * @returns {Object|null} - Highest version object or null if no valid version found
 */
function processVersionData(versionData) {
  if (!versionData || typeof versionData !== "object") {
    return null;
  }

  // New format: { versions: [...] }
  if (versionData.versions && Array.isArray(versionData.versions)) {
    return findHighestVersion(versionData.versions);
  }

  // Old format: { version: "...", description: "...", timestamp: "..." }
  if (versionData.version) {
    // Preserve all properties from the original object to include localized descriptions
    return { ...versionData };
  }

  return null;
}

module.exports = {
  compareVersions,
  normalizeVersion,
  findHighestVersion,
  processVersionData,
};
