const translations = {
  common: {
    thinking: "Nachdenken...",
  },
  errors: {
    ai: {
      ollama: {
        "connection-failed":
          "Ihre Ollama-Instanz konnte nicht erreicht werden oder antwortet nicht. Bitte stellen Si<PERSON> sicher, dass der API-Server läuft und Ihre Verbindungsinformationen korrekt sind.",
        "empty-response": "Ollama-Textantwort war leer.",
        "stream-failed": "Chat konnte nicht gestreamt werden. {{message}}",
        "no-endpoint": "Kein Ollama-API-Endpunkt wurde festgelegt.",
        "no-token-limit": "Kein Ollama-Token-Kontextlimit wurde festgelegt.",
        "provider-not-responding":
          "Der lokale KI-Anbieter antwortet nicht, entweder weil der Server offline ist oder überlastet ist.",
        "provider-unavailable":
          "Der lokale KI-Server ist derzeit nicht verfügbar. Bitte überprüfen Sie die Installation und stellen Sie sicher, dass er läuft und nicht überlastet ist.",
      },
      azure: {
        "no-endpoint": "Kein Azure-API-Endpunkt wurde festgelegt.",
        "no-key": "Kein Azure-API-Schlüssel wurde festgelegt.",
        "no-model": "Kein Azure OpenAI-Modell wurde festgelegt.",
        "no-model-pref":
          "Keine OPEN_MODEL_PREF ENV definiert. Dies muss der Name eines Deployments in Ihrem Azure-Konto für ein LLM-Chat-Modell wie GPT-3.5 sein.",
      },
      openai: {
        "no-key": "Kein OpenAI-API-Schlüssel wurde festgelegt.",
      },
      mistral: {
        "no-key": "Kein Mistral-API-Schlüssel wurde festgelegt.",
      },
      litellm: {
        "no-base-path":
          "LiteLLM muss einen gültigen Basispfad für die API haben.",
        "no-model": "LiteLLM muss ein gültiges Modell festgelegt haben.",
        "no-token-limit": "Kein Token-Kontextlimit wurde festgelegt.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI muss einen gültigen Basispfad für die API haben.",
        "no-token-limit": "Kein Token-Kontextlimit wurde festgelegt.",
      },
      localai: {
        "no-token-limit": "Kein LocalAi-Token-Kontextlimit wurde festgelegt.",
      },
      lmstudio: {
        "no-token-limit": "Kein LMStudio-Token-Kontextlimit wurde festgelegt.",
      },
      fireworks: {
        "invalid-model":
          "FireworksAI-Chat: {{model}} ist nicht gültig für Chat-Vervollständigung!",
      },
      context: {
        header: "Quelle(n)",
      },
    },
    system: {
      "ssl-boot-failed":
        "SSL-Start fehlgeschlagen: {{message}} - Fallback auf HTTP-Start.",
      "no-app": 'Keine "app" definiert - Absturz!',
      validation: {
        "invalid-transcription-provider":
          "{{input}} ist kein gültiger Transkriptionsmodell-Anbieter.",
        "invalid-safety-setting":
          "Ungültige Sicherheitseinstellung. Muss einer von {{modes}} sein.",
        "invalid-model-type":
          "Ungültiger Modelltyp. Muss einer von {{models}} sein.",
        "invalid-embedding-model":
          "Ungültiger Embedding-Modelltyp. Muss einer von {{supported}} sein.",
      },
    },
    document: {
      "sync-failed":
        "Synchronisation des Dokuments {{filename}} nach {{attempts}} Versuchen fehlgeschlagen.",
      "invalid-metadata":
        "Dokument {{filename}} hat ungültige oder fehlende Metadaten.",
      removed:
        "Dokument {{filename}} wurde aus dem überwachten Dokumentensatz entfernt.",
    },
    validation: {
      "value-not-number": "Wert ist keine Zahl.",
      "value-must-be-nonzero": "Wert muss ungleich Null sein.",
      "value-cannot-be-negative": "Wert darf nicht kleiner als 0 sein.",
      "invalid-serp-provider": "Ungültiger SERP-Anbieter.",
      "max-rerank-limit-min":
        "Maximales Reranking-Limit muss mindestens 10 sein.",
      "max-rerank-limit-max":
        "Maximales Reranking-Limit darf 200 nicht überschreiten.",
    },
    embedding: {
      "lmstudio-failed": "LMStudio Embedding fehlgeschlagen: {{errors}}",
      "jina-failed": "Jina Embedding fehlgeschlagen: {{errors}}",
    },
    env: {
      "jina-api-key-error": "Jina API-Schlüssel muss mit jina_ beginnen",
      "empty-value": "Wert darf nicht leer sein",
      "non-zero-required": "Wert muss größer als Null sein",
      "must-be-number": "Wert muss eine Zahl sein",
      "invalid-url": "URL ist keine gültige URL",
      "openai-key-format": "OpenAI-Schlüssel muss mit sk- beginnen",
      "contextual-prompt-format":
        "Kontextueller Benutzer-Prompt muss {file} & {chunk} enthalten",
      "anthropic-key-format": "Anthropic-Schlüssel muss mit sk-ant- beginnen",
      "external-llm-url-format": "URL muss /v1 enthalten",
      "url-no-trailing-slash": "URL darf nicht mit einem Schrägstrich enden",
      "invalid-tts-provider": "{{input}} ist kein gültiger TTS-Anbieter",
      "invalid-whisper-model":
        "{{input}} ist keine gültige Whisper-Modellauswahl",
      "invalid-llm-provider": "{{input}} ist kein gültiger LLM-Anbieter",
    },
    // Workspace sharing errors
    workspace: {
      onlyOwnerShare: "Nur Workspace-Besitzer können Workspaces freigeben",
      onlyOwnerRevoke:
        "Nur Workspace-Besitzer können Workspace-Freigaben aufheben",
      shareFailed: "Freigabe des Workspaces fehlgeschlagen",
      revokeFailed: "Aufheben der Workspace-Freigabe fehlgeschlagen",
      getStatusFailed: "Abrufen des Freigabestatus fehlgeschlagen",
    },
    // Thread sharing errors
    thread: {
      onlyOwnerShare: "Nur Thread-Besitzer können Threads freigeben",
      onlyOwnerRevoke: "Nur Thread-Besitzer können Thread-Freigaben aufheben",
      shareFailed: "Freigabe des Threads fehlgeschlagen",
      revokeFailed: "Aufheben der Thread-Freigabe fehlgeschlagen",
      getStatusFailed: "Abrufen des Thread-Freigabestatus fehlgeschlagen",
    },
    auth: {
      "authentication-required": "Authentifizierung erforderlich",
      "insufficient-permissions": "Unzureichende Berechtigungen",
    },
  },
  validation: {
    responseHeader: "Generierte Antwort",
    contextHeader: "Ursprünglicher Kontext und Quellen",
    content: {
      "user-question-sources-header": "BENUTZERFRAGE UND QUELLEN",
      "system-response-header": "SYSTEMANTWORT",
      "not-provided": "Nicht bereitgestellt",
    },
  },
  manualWorkEstimator: {
    content: {
      "user-question-header": "BENUTZERFRAGE",
      "system-response-header": "SYSTEMANTWORT",
    },
  },
  notifications: {
    document: {
      invalid:
        "Dokument {{filename}} hat keine Metadaten, ist beschädigt oder ungültig und wurde entfernt.",
      removed:
        "Dokument {{filename}} wurde aus dem überwachten Dokumentensatz entfernt.",
    },
  },
  docx: {
    title: "Chat-Export",
    exportedOn: "Exportiert am",
    downloadedOn: "Heruntergeladen am",
    keywords: "chat, export",
    description: "Exportierte Chat-Nachricht",
    tokenCount: "Token-Anzahl",
    canvasDocumentTitle: "Canvas-Dokument",
    errors: {
      contentRequired: "Inhalt ist erforderlich",
      writingFile: "Fehler beim Schreiben der temporären Datei",
      generating: "Fehler bei der Dokumentenerstellung",
    },
  },
  deep_search: {
    instruction:
      "Wenn DeepSearch-Quellen für die Benutzeranfrage relevant sind und in der Antwort verwendet werden, geben Sie am Ende der Nachricht klickbare Links zu diesen Quellen in genau diesem Format an: 'Quellen: [Quellentitel 1](URL1), [Quellentitel 2](URL2)'. Der klickbare Text für jeden Link muss eine prägnante Beschreibung des Quelleninhalts sein (z. B. der Seitentitel oder eine Zusammenfassung). Verweisen Sie niemals mit Nummern auf DeepSearch-Quellen im Text (wie [1] oder [Quelle 1]); beziehen Sie sich immer direkt auf die Quelle mit ihrem Namen oder Inhalt. Verwenden Sie keine akademischen Zitierformate.",
  },
};

module.exports = translations;
