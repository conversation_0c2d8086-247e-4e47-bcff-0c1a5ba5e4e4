import React, { useState, createContext, useEffect } from "react";
import { AUTH_TIMESTAMP, AUTH_TOKEN, AUTH_USER } from "@/utils/constants";
import useSystemSettingsStore from "@/stores/settingsStore";
import useWorkspaceStore from "@/stores/workspaceStore";

export const AuthContext = createContext(null);

export function ContextWrapper(props) {
  const localUser = localStorage.getItem(AUTH_USER);
  const localAuthToken = localStorage.getItem(AUTH_TOKEN);

  const fetchAllSettings = useSystemSettingsStore(
    (state) => state.fetchAllSettings
  );
  const fetchModuleWorkspaces = useWorkspaceStore(
    (state) => state.fetchModuleWorkspaces
  );
  const fetchPopulatedWorkspaces = useWorkspaceStore(
    (state) => state.fetchPopulatedWorkspaces
  );

  const [store, setStore] = useState({
    user: localUser ? JSON.parse(localUser) : null,
    authToken: localAuthToken ? localAuthToken : null,
  });

  const [actions] = useState({
    updateUser: (user, authToken = "") => {
      localStorage.setItem(AUTH_USER, JSON.stringify(user));
      localStorage.setItem(AUTH_TOKEN, authToken);
      setStore({ user, authToken });
    },
    unsetUser: () => {
      localStorage.removeItem(AUTH_USER);
      localStorage.removeItem(AUTH_TOKEN);
      localStorage.removeItem(AUTH_TIMESTAMP);
      setStore({ user: null, authToken: null });
    },
  });

  // Effect for initial data fetching when user logs in
  useEffect(() => {
    if (store.user && store.authToken) {
      // Only fetch all settings with force=true for admin users
      if (store.user.role === "admin") {
        fetchAllSettings(true);
      } else {
        fetchAllSettings(false);
      }
      fetchModuleWorkspaces(false);
      fetchPopulatedWorkspaces();
    }
  }, [
    store.user,
    store.authToken,
    fetchAllSettings,
    fetchModuleWorkspaces,
    fetchPopulatedWorkspaces,
  ]);

  return (
    <AuthContext.Provider value={{ store, actions }}>
      {props.children}
    </AuthContext.Provider>
  );
}
