## Prompt Examples

The Prompt Examples feature allows administrators to configure example prompts that are displayed on the main page to help users get started with the system.

### Configuration

Prompt examples can be configured in the General Settings > Appearance section. Each example consists of:

- **Title**: The display title for the example
- **Legal area**: The category/area of law the example relates to
- **Prompt**: The actual prompt text that will be used when the example is clicked
- **Icon**: Visual icon to represent the example
- **Workspace**: The workspace where the example will be executed

### Display Card Truncation

When examples are displayed on the main page, text truncation is applied to maintain consistent card layout:

- **Title truncation**:
  - Mobile: Limited to 1 line
  - Desktop (768px+): Limited to 2 lines
- **Area truncation**: Legal area text is truncated with ellipsis to prevent overflow
- **Responsive design**: Truncation behavior adapts to screen size for optimal display

### Usage

When users click on an example prompt from the main page:

1. A new thread is created in the specified workspace
2. The prompt text is automatically loaded into the chat input
3. The user is redirected to the chat interface with the prompt ready to submit

### Best Practices

- Keep prompt titles concise and descriptive
- Use clear legal area categorization
- Write prompts that provide good examples for users
- Test prompts to ensure they work well with the configured LLM
- Consider the target audience when writing example prompts
- **Keep titles under 80 characters for optimal display across devices**
- **Keep legal area descriptions under 30 characters for optimal display**
