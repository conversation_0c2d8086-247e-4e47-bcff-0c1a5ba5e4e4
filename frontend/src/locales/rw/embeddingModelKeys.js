export default {
  // =========================
  // EMBEDDER (EMBEDDING PROVIDER NAMES)
  // =========================
  embeder: {
    allm: "Urukora ibyitegererezo byawe by'ibitegererezo by'ibitegererezo.",
    openai: "Ibyitegererezo by'ibitegererezo by'ibitegererezo.",
    azure: "Ibyitegererezo by'ibitegererezo by'ibitegererezo.",
    localai: "Urukora ibyitegererezo byawe by'ibitegererezo by'ibitegererezo.",
    ollama: "Urukora ibyitegererezo byawe by'ibitegererezo by'ibitegererezo.",
    lmstudio: "Urukora ibyitegererezo byawe by'ibitegererezo by'ibitegererezo.",
    cohere: "Urukora ibyitegererezo byawe by'ibitegererezo by'ibitegererezo.",
    voyageai: "Urukora ibyitegererezo byawe by'ibitegererezo by'ibitegererezo.",
    "generic-openai":
      "Urukora ibyitegererezo byawe by'ibitegererezo by'ibitegererezo.",
    "default.embedder": "Ibyitegererezo by'ibitegererezo by'ibitegererezo.",
    jina: "Ibyitegererezo by'ibitegererezo by'ibitegererezo.",
    litellm: "Urukora ibyitegererezo byawe by'ibitegererezo by'ibitegererezo.",
  },
  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Common strings
    "provider-logo": "{{provider}} ikirango",

    // LMStudio Embedding Options
    lmstudio: {
      "model-label": "Icyitegererezo cyo kwinjiza cya LM Studio",
      "max-chunk-length": "Uburebure ntarengwa bw'igice",
      "max-chunk-length-help":
        "Uburebure ntarengwa bw'uduce tw'inyandiko bwo kwinjiza.",
      "hide-endpoint": "Hisha uburyo bwo kwinjiza endpoint y'intoki",
      "show-endpoint": "Erekana uburyo bwo kwinjiza endpoint y'intoki",
      "base-url": "URL shingiro ya LM Studio",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help": "Injiza URL aho LM Studio iri gukorera.",
      "auto-detect": "Kumenya byikora",
      "loading-models": "--iri gupakurura ibyitegererezo biboneka--",
      "enter-url-first": "Injiza URL ya LM Studio mbere",
      "model-help":
        "Hitamo icyitegererezo cyo kwinjiza cya LM Studio. Ibyitegererezo bizapakururwa nyuma yo kwinjiza URL ya LM Studio iboneye.",
      "loaded-models": "Ibyitegererezo byawe byapakuruwe",
    },
    // Ollama Embedding Options
    ollama: {
      "model-label": "Icyitegererezo cyo kwinjiza cya Ollama",
      "max-chunk-length": "Uburebure ntarengwa bw'igice",
      "max-chunk-length-help":
        "Uburebure ntarengwa bw'uduce tw'inyandiko bwo kwinjiza.",
      "hide-endpoint": "Hisha uburyo bwo kwinjiza endpoint y'intoki",
      "show-endpoint": "Erekana uburyo bwo kwinjiza endpoint y'intoki",
      "base-url": "URL shingiro ya Ollama",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help": "Injiza URL aho Ollama iri gukorera.",
      "auto-detect": "Kumenya byikora",
      "loading-models": "--iri gupakurura ibyitegererezo biboneka--",
      "enter-url-first": "Injiza URL ya Ollama mbere",
      "model-help":
        "Hitamo icyitegererezo cyo kwinjiza cya Ollama. Ibyitegererezo bizapakururwa nyuma yo kwinjiza URL ya Ollama iboneye.",
      "loaded-models": "Ibyitegererezo byawe byapakuruwe",
    },
    // LiteLLM Embedding Options
    litellm: {
      "model-label": "Hitamo icyitegererezo cyo kwinjiza",
      "max-chunk-length": "Uburebure ntarengwa bw'igice",
      "max-chunk-length-help":
        "Uburebure ntarengwa bw'uduce tw'inyandiko bwo kwinjiza.",
      "api-key": "Urufunguzo rwa API",
      optional: "by'ubushake",
      "api-key-placeholder": "sk-mysecretkey",
      "loading-models": "-- iri gupakurura ibyitegererezo biboneka --",
      "waiting-url": "-- turi gutegereza URL --",
      "loaded-models": "Ibyitegererezo byawe byapakuruwe",
      "model-tooltip": "Reba ibyitegererezo byo kwinjiza byashyigikiwe kuri",
      "model-tooltip-link": "inyandiko za LiteLLM",
      "model-tooltip-more": "kubindi bisobanuro ku byitegererezo biboneka.",
    },
    // Cohere Embedding Options
    cohere: {
      "api-key": "Urufunguzo rwa API rwa Cohere",
      "api-key-placeholder": "Injiza urufunguzo rwa API rwawe rwa Cohere",
      "model-label": "Hitamo icyitegererezo",
      "available-models": "Ibyitegererezo byo kwinjiza biboneka",
    },
    // Jina Embedding Options
    jina: {
      "api-key": "Urufunguzo rwa API rwa Jina",
      "api-key-format":
        "Urufunguzo rwa API rwa Jina rugomba gutangira na 'jina_'",
      "api-key-placeholder": "Injiza urufunguzo rwa API rwawe rwa Jina",
      "api-key-error": "Urufunguzo rwa API rugomba gutangira na 'jina_'",
      "model-label": "Hitamo icyitegererezo",
      "available-models": "Ibyitegererezo byo kwinjiza biboneka",
      "embedding-type": "Ubwoko bwo kwinjiza",
      "available-types": "Ubwoko bwo kwinjiza buboneka",
      dimensions: "Ibyipimo",
      "available-dimensions": "Ibyipimo biboneka",
      task: "Umurimo",
      "available-tasks": "Imirimo iboneka",
      "late-chunking": "Gutandukanya ibice nyuma",
      "late-chunking-help":
        "Hemeza gutandukanya ibice nyuma mu gutunganya inyandiko",
    },
    // LocalAI Embedding Options
    localai: {
      "model-label": "Izina ry'icyitegererezo cyo kwinjiza",
      "hide-endpoint": "Hisha amahitamo y'inyongera",
      "show-endpoint": "Erekana amahitamo y'inyongera",
      "base-url": "URL shingiro ya LocalAI",
      "base-url-placeholder": "http://localhost:8080/v1",
      "base-url-help": "Injiza URL aho LocalAI iri gukorera.",
      "auto-detect": "Kumenya byikora",
      "loading-models": "-- iri gupakurura ibyitegererezo biboneka --",
      "waiting-url": "-- turi gutegereza URL --",
      "loaded-models": "Ibyitegererezo byawe byapakuruwe",
    },
    // Generic OpenAI-Compatible Embedding Options
    generic: {
      "base-url": "URL shingiro",
      "base-url-placeholder": "https://api.openai.com/v1",
      "base-url-help":
        "Injiza URL shingiro y'aho API yawe ihuje na OpenAI iri.",
      "model-label": "Icyitegererezo cyo kwinjiza",
      "model-placeholder":
        "Injiza izina ry'icyitegererezo (urugero: text-embedding-ada-002)",
      "model-help": "Tanga indangamuntu y'icyitegererezo cyo gukora kwinjiza.",
      "api-key": "Urufunguzo rwa API",
      "api-key-placeholder": "sk-mysecretkey",
      "api-key-help": "Injiza urufunguzo rwa API rwawe kugira ngo wemezwe.",
    },
    // OpenAI Embedding Options
    openai: {
      "api-key": "Urufunguzo rwa API rwa OpenAI",
      "api-key-placeholder": "Injiza urufunguzo rwa API rwawe rwa OpenAI",
      "model-label": "Hitamo icyitegererezo",
      "available-models": "Ibyitegererezo byo kwinjiza biboneka",
    },
    // VoyageAI Embedding Options
    voyageai: {
      "api-key": "Urufunguzo rwa API rwa VoyageAI",
      "api-key-placeholder": "Injiza urufunguzo rwa API rwawe rwa VoyageAI",
      "model-label": "Hitamo icyitegererezo",
      "available-models": "Ibyitegererezo byo kwinjiza biboneka",
    },
    // Azure OpenAI Embedding Options
    azureai: {
      "service-endpoint": "Iherezo rya serivisi ya Azure OpenAI",
      "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
      "service-endpoint-help":
        "Injiza URL y'iherezo rya serivisi ya Azure OpenAI",
      "api-key": "Urufunguzo rwa API rwa Azure OpenAI",
      "api-key-placeholder": "Injiza urufunguzo rwa API rwawe rwa Azure OpenAI",
      "api-key-help":
        "Injiza urufunguzo rwa API rwa Azure OpenAI kugira ngo wemezwe",
      "deployment-name":
        "Izina ry'ishyirwa mu bikorwa ry'icyitegererezo cyo kwinjiza",
      "deployment-name-placeholder":
        "Injiza izina ry'ishyirwa mu bikorwa ry'icyitegererezo cyo kwinjiza cya Azure OpenAI",
      "deployment-name-help":
        "Izina ry'ishyirwa mu bikorwa ry'icyitegererezo cyo kwinjiza cya Azure OpenAI",
    },
    // Native Embedding Options
    native: {
      description:
        "Ukoresha umuhawe wa kwinjiza gakondo mu gutunganya inyandiko",
    },
  },
  // =========================
  // JINA EMBEDDING
  // =========================
  jina: {
    "api-key": "Jina API Key",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa Jina",
    "api-key-format": "Jina API key must start with 'jina_'",
    "model-preference": "Ihitamo rya Modeli",
  },

  // =========================
  // OLLAMA EMBEDDING
  // =========================
  ollama: {
    "max-embedding-chunk-length": "Uburebure ntarengwa bw'igice cyo kwinjiza",
  },

  // =========================
  // VOYAGEAI EMBEDDING
  // =========================
  voyageai: {
    "api-key": "VoyageAI API Key",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa VoyageAI",
    "model-preference": "Ihitamo rya Modeli",
  },
};
