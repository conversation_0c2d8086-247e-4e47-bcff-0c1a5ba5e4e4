---
description: Guidelines for embedding models and vector search
globs: server/utils/vectorDbProviders/**/*.js,server/utils/helpers/chat/**/*.js
alwaysApply: false
---

# Embedding Models and Vector Search

## Supported Embedding Models
- OpenAI embedding models (text-embedding-004, text-embedding-3-small, etc.)
- Gemini embedding models:
  - text-multilingual-embedding-002
  - text-embedding-large-exp-03-07 / gemini-embedding-exp-03-07 (supports 8k token chunks)
  - text-embedding-005
  - text-embedding-004
- Cohere embedding models
- And others

## Model Integration
- Embedding engines are implemented in `server/utils/EmbeddingEngines`
- Each provider has its own implementation with consistent interfaces
- Configuration is managed through environment variables and system settings

## Vector Database Integration
- Multiple vector database providers are supported:
  - Pinecone
  - Qdrant
  - Weaviate
  - LanceDB
  - Chroma
  - Milvus
  - Astra
  - Zilliz
- Vector database providers are implemented in `server/utils/vectorDbProviders`

## Document Embedding
- Documents are embedded using the selected embedding model
- Embeddings are stored in the selected vector database
- Document metadata is stored alongside embeddings
- Re-embedding is performed when documents change

## Vector Search
- Vector search is used to retrieve relevant documents for RAG
- Similarity thresholds can be configured
- Search modes (similarity, MMR, etc.) can be selected
- Results are ranked and filtered based on relevance

## Performance Considerations
- Batch embedding for efficiency
- Cache embeddings when appropriate
- Optimize vector search parameters
- Monitor embedding and search performance

## Gemini Embedding Model Compatibility

### Model Name Mappings
The Gemini embedding models have different naming conventions across different SDK versions:

| Frontend/Environment Variable | Google GenAI SDK (@google/genai) | Notes |
|------------------------------|----------------------------------|-------|
| text-embedding-large-exp-03-07 | gemini-embedding-exp-03-07 | 8k token context, automatically mapped for compatibility |
| text-embedding-004 | text-embedding-004 | Standard embedding model |
| text-embedding-005 | text-embedding-005 | Newer embedding model |
| text-multilingual-embedding-002 | text-multilingual-embedding-002 | Multilingual support |

### Implementation Details
- The GeminiEmbedder class in `server/utils/EmbeddingEngines/gemini/index.js` handles these mappings automatically
- When `text-embedding-large-exp-03-07` is specified, it's mapped to `gemini-embedding-exp-03-07` when calling the API
- This mapping ensures backward compatibility with existing data and configurations
- The actual model reference used when calling the embedding API with the new Google GenAI SDK is determined by this mapping

### SDK Migration
- The implementation has been updated to use the new Google GenAI SDK (@google/genai)
- The previous SDK used `GoogleGenAI.getGenerativeModel().embedContent()` method
- The new SDK uses `GoogleGenAI.models.embedContent()` method
- Response structure has changed from `result.embedding.values` to `result.embeddings`

## Best Practices
- Select appropriate embedding models for different languages
- Configure vector databases for optimal performance
- Implement proper error handling for embedding operations
- Consider dimensionality and quantization for efficiency
- Be aware of model name differences between frontend and backend APIs
