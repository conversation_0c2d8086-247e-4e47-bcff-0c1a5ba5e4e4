---
description: Guidelines for the agent system and plugins
globs: server/utils/agents/**/*.js
alwaysApply: false
---

# Agent System

## Overview

The agent system provides advanced capabilities through plugins for tasks such as:

- SQL queries
- Web scraping
- Memory management
- Tool-based interactions

## Server Components

The agent system is implemented in `server/utils/agents/`, with the following structure:

### AIbitat Core

- Core agent framework and orchestration
- Manages agent state and interactions

### Plugins

- `sql-agent/`: SQL query execution and database interaction
- Other specialized plugins for different tasks

### Providers

- Integration with different LLM providers
- Helper functions for provider-specific features

### Utilities

- Common utilities used across the agent system
- Helper functions for agent operations

## Frontend Components

The agent system is integrated into the frontend through:

- `AgentMenu/`: Agent selection and configuration
- `AgentConfig/`: Agent settings and preferences
- `AgentModelSelection/`: Model selection for agents
- `AgentLLMSelection/`: LLM provider selection for agents

## Agent Workflow

1. User selects an agent from the agent menu
2. Agent is configured with appropriate settings
3. User provides input to the agent
4. Agent processes the input using the selected plugin
5. Agent returns results to the user

## Best Practices

- Follow the established patterns for agent implementation
- Use appropriate error handling for agent operations
- Consider performance implications of agent tasks
- Document complex agent logic
- Test agents with various inputs and edge cases
- Implement proper security checks for agent operations
