export default {
  // =========================
  // WORKSPACES SETTINGS MENU
  // =========================

  "workspaces-settings": {
    general: "Paramètres généraux",
    chat: "Paramètres du chat",
    vector: "Base de données vectorielle",
    members: "Membres",
    agent: "Configuration de l'agent",
    "general-settings": {
      "workspace-name": "Nom de l'espace de travail",
      "desc-name":
        "Ceci ne changera que le nom d'affichage de votre espace de travail.",
      "assistant-profile": "Image de profil de l'assistant",
      "assistant-image":
        "Personnalisez l'image de profil de l'assistant pour cet espace de travail.",
      "workspace-image": "Image de l'espace de travail",
      "remove-image": "Supprimer l'image de l'espace de travail",
      delete: "Supprimer l'espace de travail",
      deleting: "Suppression de l'espace de travail...",
      update: "Mettre à jour l'espace de travail",
      updating: "Mise à jour de l'espace de travail...",
    },
    "chat-settings": {
      type: "Type de chat",
      private: "Privé",
      standard: "Standard",
      "private-desc-start": "accordera manuellement l'accès à",
      "private-desc-mid": "seulement",
      "private-desc-end": "des utilisateurs spécifiques.",
      "standard-desc-start": "accordera automatiquement l'accès à",
      "standard-desc-mid": "tous",
      "standard-desc-end": "les nouveaux utilisateurs.",
    },
    users: {
      manage: "Gérer les utilisateurs",
      "workspace-member": "Aucun membre de l'espace de travail",
      username: "Adresse e-mail",
      role: "Rôle",
      default: "Par défaut",
      manager: "Gestionnaire",
      admin: "Administrateur",
      superuser: "Super utilisateur",
      "date-added": "Date d'ajout",
      users: "Utilisateurs",
      search: "Rechercher un utilisateur",
      "no-user": "Aucun utilisateur trouvé",
      select: "Tout sélectionner",
      unselect: "Désélectionner",
      save: "Sauvegarder",
    },
    "linked-workspaces": {
      title: "Espaces de travail liés",
      description:
        "Si les espaces de travail sont liés, les données légales pertinentes pour l'invite seront automatiquement récupérées de chaque domaine juridique lié. Notez que les espaces de travail liés augmenteront le temps de traitement",
      "linked-workspace": "Aucun espace de travail lié",
      manage: "Gérer les espaces de travail",
      name: "Nom",
      slug: "Slug",
      date: "Date d'ajout",
      workspaces: "Espaces de travail",
      search: "Rechercher un espace de travail",
      "no-workspace": "Aucun espace de travail trouvé",
      select: "Tout sélectionner",
      unselect: "Désélectionner",
      save: "Sauvegarder",
    },
    "delete-workspace": "Supprimer l'espace de travail",
    "delete-workspace-message":
      "Vous êtes sur le point de supprimer tout votre espace de travail {{workspace}}. Cela supprimera toutes les incorporations vectorielles de votre base de données vectorielle.\n\nLes fichiers sources originaux resteront intacts. Cette action est irréversible.",
    "vector-database": {
      reset: {
        title: "Réinitialiser la base de données vectorielle",
        message:
          "Vous êtes sur le point de réinitialiser la base de données vectorielle de cet espace de travail. Cela supprimera toutes les incorporations vectorielles actuellement incorporées.\n\nLes fichiers sources originaux resteront intacts. Cette action est irréversible.",
      },
    },
  },

  // =========================
  // GENERAL APPEARANCE & CUSTOMIZATION
  // =========================
  general: {
    vector: {
      title: "Nombre de vecteurs",
      description:
        "Nombre total de vecteurs dans votre base de données vectorielle.",
      vectors: "Nombre de vecteurs",
    },
    names: {
      description:
        "Ceci ne changera que le nom d'affichage de votre espace de travail.",
    },
    message: {
      title: "Messages de chat suggérés",
      description:
        "Personnalisez les messages qui seront suggérés aux utilisateurs de votre espace de travail.",
      add: "Ajouter un nouveau message",
      save: "Sauvegarder les messages",
      heading: "Expliquez-moi",
      body: "les avantages de la plateforme",
      message: "Message",
      "new-heading": "En-tête",
    },
    pfp: {
      title: "Image de profil de l'assistant",
      description:
        "Personnalisez l'image de profil de l'assistant pour cet espace de travail.",
      image: "Image de l'espace de travail",
      remove: "Supprimer l'image de l'espace de travail",
    },
    delete: {
      delete: "Supprimer l'espace de travail",
      deleting: "Suppression de l'espace de travail...",
      "confirm-start": "Vous êtes sur le point de supprimer tout votre",
      "confirm-end":
        "espace de travail. Cela supprimera toutes les incorporations vectorielles de votre base de données vectorielle.\n\nLes fichiers sources originaux resteront intacts. Cette action est irréversible.",
    },
  },

  // =========================
  // CHAT SETTINGS
  // =========================
  chat: {
    llm: {
      title: "Fournisseur LLM de l'espace de travail",
      description:
        "Le fournisseur LLM et modèle spécifique qui sera utilisé pour cet espace de travail. Par défaut, il utilise le fournisseur LLM système et les paramètres.",
      search: "Rechercher tous les fournisseurs LLM",
      "save-error":
        "Échec de la sauvegarde des paramètres {{provider}} : {{error}}",
      setup: "Configuration",
      use: "Pour utiliser",
      "need-setup": "vous devez configurer les identifiants suivants.",
      cancel: "Annuler",
      save: "Sauvegarder",
      settings: "paramètres",
      "multi-model": "Ce fournisseur ne prend pas en charge plusieurs modèles.",
      "workspace-use":
        "Votre espace de travail utilisera le modèle configuré dans",
      "model-set": "paramètres système",
      "system-default": "Par défaut du système",
      "system-default-desc":
        "Utiliser la préférence LLM système pour cet espace de travail.",
      "no-selection": "Aucun LLM sélectionné",
      "select-provider": "Sélectionner un fournisseur LLM",
      "system-standard-name": "Standard du système",
      "system-standard-desc":
        "Utiliser le LLM principal défini dans les paramètres système.",
    },
    "speak-prompt": "Prononcez votre invite",
    "view-agents":
      "Voir tous les agents disponibles que vous pouvez utiliser pour discuter",
    "ability-tag": "Capacité",
    "change-text-size": "Changer la taille du texte",
    "aria-text-size": "Changer la taille du texte",
    model: {
      title: "Modèle de chat de l'espace de travail",
      description:
        "Le modèle de chat spécifique qui sera utilisé pour cet espace de travail. Si vide, utilisera la préférence LLM système.",
      wait: "-- en attente des modèles --",
      general: "Modèles généraux",
      custom: "Modèles personnalisés",
    },
    mode: {
      title: "Mode de chat",
      chat: {
        title: "Chat",
        "desc-start":
          "fournira des réponses avec les connaissances générales du LLM",
        and: "et",
        "desc-end": "le contexte documentaire trouvé.",
      },
      query: {
        title: "Requête",
        "desc-start": "fournira des réponses",
        only: "seulement",
        "desc-end": "si un contexte documentaire est trouvé.",
      },
    },
    history: {
      title: "Historique du chat",
      "desc-start":
        "Le nombre de chats précédents qui seront inclus dans la mémoire à court terme de la réponse.",
      recommend: "Recommandé 20. ",
      "desc-end":
        "Plus de 45 est susceptible de conduire à des échecs de chat continus selon la taille du message.",
    },
    prompt: {
      title: "Invite",
      description:
        "L'invite qui sera utilisée dans cet espace de travail. Définissez le contexte et les instructions pour que l'IA génère une réponse. Vous devriez fournir une invite soigneusement élaborée pour que l'IA puisse générer une réponse pertinente et précise.",
    },
    refusal: {
      title: "Réponse de refus en mode requête",
      "desc-start": "En mode",
      query: "requête",
      "desc-end":
        ", vous voudrez peut-être retourner une réponse de refus personnalisée quand aucun contexte n'est trouvé.",
    },
    temperature: {
      title: "Température LLM",
      "desc-start":
        'Ce paramètre contrôle à quel point vos réponses LLM seront "créatives".',
      "desc-end":
        "Plus le nombre est élevé, plus c'est créatif. Pour certains modèles, cela peut conduire à des réponses incohérentes lorsque réglé trop haut.",
      hint: "La plupart des LLM ont diverses plages acceptables de valeurs valides. Consultez votre fournisseur LLM pour cette information.",
    },
    "dynamic-pdr": {
      title: "PDR dynamique pour l'espace de travail",
      description:
        "Activer ou désactiver le PDR dynamique pour cet espace de travail.",
      "global-enabled":
        "Le PDR dynamique est activé globalement et ne peut pas être désactivé pour des espaces de travail individuels.",
    },
    display_prompt_output_description:
      "Afficher la journalisation de sortie d'invite, Ouvrir et télécharger le fichier",
    display_prompt_output: "Afficher la sortie d'invite",
    loading_prompt_output: "Chargement de la sortie d'invite...",
    prompt_output_not_available:
      "*** La sortie d'invite n'est pas disponible pour ce chat.",
    open_in_new_tab: "Ouvrir dans un nouvel onglet",
  },

  // =========================
  // VECTOR DATABASE (WORKSPACE)
  // =========================
  "vector-workspace": {
    identifier: "Identifiant de base de données vectorielle",
    snippets: {
      title: "Extraits de contexte max",
      description:
        "Ce paramètre contrôle la quantité maximale d'extraits de contexte qui seront envoyés au LLM par chat ou requête.",
      recommend:
        "La valeur recommandée est d'au moins 30. Régler des nombres beaucoup plus élevés augmentera le temps de traitement sans nécessairement améliorer la précision selon la capacité du LLM utilisé.",
    },
    doc: {
      title: "Seuil de similarité des documents",
      description:
        "Le score de similarité minimum requis pour qu'une source soit considérée comme liée au chat. Plus le nombre est élevé, plus la source doit être similaire au chat.",
      zero: "Aucune restriction",
      low: "Faible (score de similarité ≥ .25)",
      medium: "Moyen (score de similarité ≥ .50)",
      high: "Élevé (score de similarité ≥ .75)",
    },
    reset: {
      reset: "Réinitialiser la base de données vectorielle",
      resetting: "Effacement des vecteurs...",
      confirm:
        "Vous êtes sur le point de réinitialiser la base de données vectorielle de cet espace de travail. Cela supprimera toutes les incorporations vectorielles actuellement incorporées.\n\nLes fichiers sources originaux resteront intacts. Cette action est irréversible.",
      error:
        "La base de données vectorielle de l'espace de travail n'a pas pu être réinitialisée !",
      success:
        "La base de données vectorielle de l'espace de travail a été réinitialisée !",
    },
    prompt: { placeholder: "Posez votre question ici..." },
    refusal: {
      placeholder: "Désolé, je ne peux pas répondre à cette question",
    },
  },

  // =========================
  // AGENT CONFIGURATION
  // =========================
  agent: {
    "performance-warning":
      "Les performances des LLM qui ne prennent pas explicitement en charge l'appel d'outils dépendent fortement des capacités et de la précision du modèle. Certaines capacités peuvent être limitées ou non fonctionnelles.",
    provider: {
      title: "Fournisseur LLM d'agent de l'espace de travail",
      description:
        "Le fournisseur LLM et modèle spécifique qui sera utilisé pour l'agent @agent de cet espace de travail.",
      "need-setup":
        "Pour utiliser {{name}} comme LLM d'agent de cet espace de travail, vous devez d'abord le configurer.",
    },
    mode: {
      chat: {
        title: "Modèle de chat d'agent de l'espace de travail",
        description:
          "Le modèle de chat spécifique qui sera utilisé pour l'agent @agent de cet espace de travail.",
      },
      title: "Modèle d'agent de l'espace de travail",
      description:
        "Le modèle LLM spécifique qui sera utilisé pour l'agent @agent de cet espace de travail.",
      wait: "-- en attente des modèles --",
    },
    skill: {
      title: "Compétences d'agent par défaut",
      description:
        "Améliorez les capacités naturelles de l'agent par défaut avec ces compétences pré-construites. Cette configuration s'applique à tous les espaces de travail.",
      rag: {
        title: "RAG et mémoire à long terme",
        description:
          "Permettre à l'agent d'exploiter vos documents locaux pour répondre à une requête ou demander à l'agent de \"se souvenir\" de morceaux de contenu pour la récupération de mémoire à long terme.",
      },
      configure: {
        title: "Configurer les compétences d'agent",
        description:
          "Personnalisez et améliorez les capacités de l'agent par défaut en activant ou désactivant des compétences spécifiques. Ces paramètres seront appliqués à tous les espaces de travail.",
      },
      view: {
        title: "Voir et résumer les documents",
        description:
          "Permettre à l'agent de lister et résumer le contenu des fichiers d'espace de travail actuellement incorporés.",
      },
      scrape: {
        title: "Extraire des sites web",
        description:
          "Permettre à l'agent de visiter et extraire le contenu de sites web.",
      },
      generate: {
        title: "Générer des graphiques",
        description:
          "Activer l'agent par défaut pour générer divers types de graphiques à partir de données fournies ou données en chat.",
      },
      save: {
        title: "Générer et sauvegarder des fichiers dans le navigateur",
        description:
          "Activer l'agent par défaut pour générer et écrire des fichiers qui sauvegardent et peuvent être téléchargés dans votre navigateur.",
      },
      web: {
        title: "Recherche web en direct et navigation",
        "desc-start":
          "Activez votre agent pour rechercher sur le web pour répondre à vos questions en se connectant à un fournisseur de recherche web (SERP).",
        "desc-end":
          "La recherche web pendant les sessions d'agent ne fonctionnera pas jusqu'à ce que cela soit configuré.",
      },
    },
  },
};
