import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import { v4 } from "uuid";
import i18next from "../i18n";
import useUserStore from "@/stores/userStore";
import useProgressStore from "@/stores/progressStore";
import useStreamAbortStore from "@/stores/useStreamAbortStore";

const WorkspaceThread = {
  all: async function (workspaceSlug) {
    const { threads } = await fetch(
      `${API_BASE}/workspace/${workspaceSlug}/threads`,
      { method: "GET", headers: baseHeaders() }
    )
      .then((res) => res.json())
      .catch(() => {
        return { threads: [] };
      });

    return { threads };
  },
  new: async function (workspaceSlug) {
    const { thread, error } = await fetch(
      `${API_BASE}/workspace/${workspaceSlug}/thread/new`,
      { method: "POST", headers: baseHeaders() }
    )
      .then((res) => res.json())
      .catch((e) => {
        return { thread: null, error: e.message };
      });

    return { thread, error };
  },
  update: async function (workspaceSlug, threadSlug, data = {}) {
    const { thread, message } = await fetch(
      `${API_BASE}/workspace/${workspaceSlug}/thread/${threadSlug}/update`,
      { method: "POST", body: JSON.stringify(data), headers: baseHeaders() }
    )
      .then((res) => res.json())
      .catch((e) => {
        return { thread: null, message: e.message };
      });

    return { thread, message };
  },
  delete: async function (workspaceSlug, threadSlug) {
    return await fetch(
      `${API_BASE}/workspace/${workspaceSlug}/thread/${threadSlug}`,
      { method: "DELETE", headers: baseHeaders() }
    )
      .then((res) => res.ok)
      .catch(() => false);
  },
  deleteBulk: async function (workspaceSlug, threadSlugs = []) {
    return await fetch(
      `${API_BASE}/workspace/${workspaceSlug}/thread-bulk-delete`,
      {
        method: "DELETE",
        body: JSON.stringify({ slugs: threadSlugs }),
        headers: baseHeaders(),
      }
    )
      .then((res) => res.ok)
      .catch(() => false);
  },
  chatHistory: async function (workspaceSlug, threadSlug) {
    const history = await fetch(
      `${API_BASE}/workspace/${workspaceSlug}/thread/${threadSlug}/chats`,
      { method: "GET", headers: baseHeaders() }
    )
      .then((res) => res.json())
      .then((res) => res.history || [])
      .catch(() => []);
    return history;
  },
  streamChat: async function (
    { workspaceSlug, threadSlug },
    message,
    handleChat,
    attachments,
    chatId,
    isCanvasChat,
    preventChatCreation,
    cdb,
    llmSelected,
    invoice_ref,
    passedAbortController,
    hasUploadedFile = false,
    docxContent = null,
    displayMessage = null,
    useDeepSearch = false,
    cdbOptions = [],
    settingsSuffix = ""
  ) {
    // Use passed abort controller or get from progress store
    let localAbortController = passedAbortController;

    if (!localAbortController) {
      // Try to get abort controller from progress store for this thread
      const existing = useProgressStore
        .getState()
        .getAbortController(threadSlug);
      localAbortController =
        existing && !existing.signal.aborted ? existing : new AbortController();

      // If we created a fresh controller, push it into the store so UI can cancel
      if (localAbortController !== existing) {
        useProgressStore.getState().threads.set(threadSlug, {
          ...(useProgressStore.getState().getThreadState(threadSlug) ?? {}),
          abortController: localAbortController,
        });
      }
    }

    // Subscribe to the abort request timestamp from the Zustand store
    let lastSeenAbortTimestamp =
      useStreamAbortStore.getState().abortRequestTimestamp;
    const unsubscribeFromStore = useStreamAbortStore.subscribe(
      (state, prevState) => {
        const newTimestamp = state.abortRequestTimestamp;
        console.log("[StreamAbort] Thread subscription triggered:", {
          threadSlug,
          newTimestamp,
          lastSeenAbortTimestamp,
          state: state.abortRequestTimestamp,
        });
        if (newTimestamp !== null && newTimestamp !== lastSeenAbortTimestamp) {
          lastSeenAbortTimestamp = newTimestamp;
          console.log(
            "[StreamAbort] Aborting thread stream due to abort request:",
            threadSlug
          );
          localAbortController.abort();
          handleChat({ id: v4(), type: "stopGeneration" });
        }
      }
    );

    const slugModule = useUserStore.getState().selectedModule;

    const userState = useUserStore.getState();
    const activeProfile = userState.activeStyleProfileId
      ? userState.styleProfiles.find(
          (p) => p.id === userState.activeStyleProfileId
        )
      : null;

    const styleAlignmentData = {
      enabled: userState.styleAlignmentEnabled && !!activeProfile,
      instructions: activeProfile?.instructions || null,
    };

    try {
      await fetchEventSource(
        `${API_BASE}/workspace/${workspaceSlug}/thread/${threadSlug}/stream-chat/${slugModule}?cdb=${cdb}`,
        {
          method: "POST",
          body: JSON.stringify({
            message,
            attachments,
            module: slugModule,
            chatId,
            isCanvasChat,
            preventChatCreation,
            cdb,
            llmSelected,
            invoice_ref,
            hasUploadedFile,
            displayMessage,
            useDeepSearch,
            cdbOptions,
            settings_suffix: settingsSuffix,
            styleAlignment: styleAlignmentData,
          }),
          headers: baseHeaders(),
          signal: localAbortController.signal,
          openWhenHidden: true,
          async onopen(response) {
            if (response.ok) {
              return; // everything's good
            } else if (
              response.status >= 400 &&
              response.status < 500 &&
              response.status !== 429
            ) {
              handleChat({
                id: v4(),
                type: "abort",
                textResponse: null,
                sources: [],
                close: true,
                error: `${i18next.t("errors.streaming.failed", { defaultValue: "An error occurred while streaming response." })} ${i18next.t("errors.streaming.code", { defaultValue: "Code", code: response.status })} ${response.status}`,
              });
              localAbortController.abort();
              throw new Error("Invalid Status code response.");
            } else {
              handleChat({
                id: v4(),
                type: "abort",
                textResponse: null,
                sources: [],
                close: true,
                error: `${i18next.t("errors.streaming.failed", { defaultValue: "An error occurred while streaming response." })} ${i18next.t("errors.streaming.unknown", { defaultValue: "Unknown Error." })}`,
              });
              localAbortController.abort();
              throw new Error("Unknown error");
            }
          },
          async onmessage(msg) {
            try {
              const chatResult = JSON.parse(msg.data);
              handleChat(chatResult);
            } catch (error) {
              console.error("Error parsing message data:", error);
            }
          },
          onerror(err) {
            handleChat({
              id: v4(),
              type: "abort",
              textResponse: null,
              sources: [],
              close: true,
              error: `${i18next.t("errors.streaming.failed", { defaultValue: "An error occurred while streaming response." })} ${err.message}`,
            });
            localAbortController.abort();
            throw new Error();
          },
        }
      );
    } catch (error) {
      if (error.name !== "AbortError") {
        // Only handle non-abort errors here, aborts are handled by the subscription
        handleChat({
          id: v4(),
          type: "abort",
          textResponse: error.message,
          sources: [],
        });
      }
    } finally {
      // IMPORTANT: Unsubscribe from the store when the stream function is done
      unsubscribeFromStore();
    }

    return localAbortController;
  },
  _deleteEditedChats: async function (
    workspaceSlug = "",
    threadSlug = "",
    startingId
  ) {
    return await fetch(
      `${API_BASE}/workspace/${workspaceSlug}/thread/${threadSlug}/delete-edited-chats`,
      {
        method: "DELETE",
        headers: baseHeaders(),
        body: JSON.stringify({ startingId }),
      }
    )
      .then((res) => {
        if (res.ok) return true;
        throw new Error("Failed to delete chats.");
      })
      .catch((e) => {
        console.log(e);
        return false;
      });
  },
  _updateChatResponse: async function (
    workspaceSlug = "",
    threadSlug = "",
    chatId,
    newText
  ) {
    return await fetch(
      `${API_BASE}/workspace/${workspaceSlug}/thread/${threadSlug}/update-chat`,
      {
        method: "POST",
        headers: baseHeaders(),
        body: JSON.stringify({ chatId, newText }),
      }
    )
      .then((res) => {
        if (res.ok) return true;
        throw new Error("Failed to update chat.");
      })
      .catch((e) => {
        console.log(e);
        return false;
      });
  },

  // Sharing methods
  getShareStatus: async function (workspaceSlug, threadSlug) {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${workspaceSlug}/thread/${threadSlug}/share-status`,
        {
          method: "GET",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch thread share status");
      }

      return await response.json();
    } catch (error) {
      console.error("Error fetching thread share status:", error);
      return { success: false, error: error.message };
    }
  },

  share: async function (
    workspaceSlug,
    threadSlug,
    { userIds = [], shareWithOrg = undefined }
  ) {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${workspaceSlug}/thread/${threadSlug}/share`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify({ userIds, shareWithOrg }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to share thread");
      }

      return await response.json();
    } catch (error) {
      console.error("Error sharing thread:", error);
      return { success: false, error: error.message };
    }
  },

  revokeShareUser: async function (workspaceSlug, threadSlug, userId) {
    try {
      const response = await fetch(
        `${API_BASE}/workspace/${workspaceSlug}/thread/${threadSlug}/revoke-share`,
        {
          method: "POST",
          headers: baseHeaders(),
          body: JSON.stringify({ userId }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to revoke thread share");
      }

      return await response.json();
    } catch (error) {
      console.error("Error revoking thread share:", error);
      return { success: false, error: error.message };
    }
  },

  // Helper method to get thread by ID
  get: async function (threadId) {
    try {
      // Fetch the specific thread directly using its ID
      const response = await fetch(`${API_BASE}/threads/${threadId}`, {
        method: "GET",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        // If the thread is not found or another error occurs, throw an error
        const errorData = await response.json().catch(() => ({})); // Attempt to parse error
        console.error(
          `Error fetching thread ${threadId}: ${response.status}`,
          errorData
        );
        return null; // Return null to indicate failure
      }

      const threadData = await response.json();

      // Ensure the response includes necessary fields like workspaceSlug
      if (!threadData || !threadData.workspaceSlug) {
        console.error("Thread data is missing required fields:", threadData);
        return null;
      }

      return threadData; // Return the fetched thread data
    } catch (error) {
      console.error(`Error getting thread ${threadId} by ID:`, error);
      return null; // Return null on any exception
    }
  },
};

export default WorkspaceThread;
