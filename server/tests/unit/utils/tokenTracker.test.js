/**
 * Unit Tests for TokenTracker
 *
 * These tests verify all TokenTracker functionality including:
 * - Metrics initialization and reset
 * - LLM response tracking
 * - Content token tracking by type
 * - Stage-based processing tracking
 * - Budget allocation and utilization tracking
 * - Error and warning handling
 * - Report generation and analysis
 */

const { TokenTracker } = require("../../../utils/chats/helpers/tokenTracker");

// Mock LLM Connector for testing
class MockLLMConnector {
  constructor(model = "gpt-4", contextWindowSize = 128000) {
    this.model = model;
    this.contextWindowSize = contextWindowSize;
    this.metrics = {};
  }

  promptWindowLimit() {
    return this.contextWindowSize;
  }
}

// Mock LLM response for testing
function createMockLLMResponse(inputTokens = 1000, outputTokens = 500) {
  return {
    textResponse: "This is a mock response from the LLM.",
    metrics: {
      prompt_tokens: inputTokens,
      completion_tokens: outputTokens,
      total_tokens: inputTokens + outputTokens,
    },
  };
}

describe("TokenTracker", () => {
  let mockLLMConnector;
  let tokenTracker;

  beforeEach(() => {
    mockLLMConnector = new MockLLMConnector();
    tokenTracker = new TokenTracker(mockLLMConnector, {
      enableDetailedTracking: true,
      trackContentTypes: true,
      logTokenUsage: false, // Disable logging in tests
    });
  });

  describe("Initialization", () => {
    test("should initialize with correct default metrics", () => {
      const metrics = tokenTracker.getMetrics();

      expect(metrics.totalInputTokens).toBe(0);
      expect(metrics.totalOutputTokens).toBe(0);
      expect(metrics.totalTokens).toBe(0);
      expect(metrics.processId).toMatch(/^proc_\d+_[a-z0-9]+$/);
      expect(metrics.startTime).toBeGreaterThan(0);
      expect(metrics.endTime).toBeNull();
    });

    test("should accept custom options", () => {
      const customTracker = new TokenTracker(mockLLMConnector, {
        enableDetailedTracking: false,
        trackContentTypes: false,
        logTokenUsage: true,
      });

      expect(customTracker.options.enableDetailedTracking).toBe(false);
      expect(customTracker.options.trackContentTypes).toBe(false);
      expect(customTracker.options.logTokenUsage).toBe(true);
    });
  });

  describe("LLM Response Tracking", () => {
    test("should track LLM response tokens correctly", () => {
      const mockResponse = createMockLLMResponse(1200, 800);

      tokenTracker.trackLLMResponse(mockResponse, "testStage", "testOperation");

      const metrics = tokenTracker.getMetrics();
      expect(metrics.totalInputTokens).toBe(1200);
      expect(metrics.totalOutputTokens).toBe(800);
      expect(metrics.totalTokens).toBe(2000);
      expect(metrics.stages.testStage.tokens).toBe(2000);
      expect(metrics.stages.testStage.operations).toBe(1);
    });

    test("should handle multiple LLM responses and accumulate correctly", () => {
      const response1 = createMockLLMResponse(500, 300);
      const response2 = createMockLLMResponse(700, 200);

      tokenTracker.trackLLMResponse(response1, "stage1", "op1");
      tokenTracker.trackLLMResponse(response2, "stage1", "op2");

      const metrics = tokenTracker.getMetrics();
      expect(metrics.totalInputTokens).toBe(1200);
      expect(metrics.totalOutputTokens).toBe(500);
      expect(metrics.totalTokens).toBe(1700);
      expect(metrics.stages.stage1.tokens).toBe(1700);
      expect(metrics.stages.stage1.operations).toBe(2);
    });

    test("should handle missing metrics in LLM response", () => {
      const incompleteResponse = {
        textResponse: "Response without metrics",
        metrics: {
          prompt_tokens: 100,
          // Missing completion_tokens and total_tokens
        },
      };

      tokenTracker.trackLLMResponse(incompleteResponse, "testStage");

      const metrics = tokenTracker.getMetrics();
      expect(metrics.totalInputTokens).toBe(100);
      expect(metrics.totalOutputTokens).toBe(0);
      expect(metrics.totalTokens).toBe(100);
    });

    test("should gracefully handle null LLM response", () => {
      tokenTracker.trackLLMResponse(null, "testStage");

      const metrics = tokenTracker.getMetrics();
      expect(metrics.totalTokens).toBe(0);
    });
  });

  describe("Content Token Tracking", () => {
    test("should track content tokens by type", () => {
      const documentContent = "This is a test document with some content.";
      const memoContent = "This is a legal memo content.";

      const docTokens = tokenTracker.trackContentTokens(
        documentContent,
        "documents",
        "test-doc"
      );
      const memoTokens = tokenTracker.trackContentTokens(
        memoContent,
        "memos",
        "test-memo"
      );

      expect(docTokens).toBeGreaterThan(0);
      expect(memoTokens).toBeGreaterThan(0);

      const metrics = tokenTracker.getMetrics();
      expect(metrics.contentBreakdown.documents).toBe(docTokens);
      expect(metrics.contentBreakdown.memos).toBe(memoTokens);
    });

    test("should accumulate tokens for same content type", () => {
      const content1 = "First document content.";
      const content2 = "Second document content with more text.";

      const tokens1 = tokenTracker.trackContentTokens(
        content1,
        "documents",
        "doc1"
      );
      const tokens2 = tokenTracker.trackContentTokens(
        content2,
        "documents",
        "doc2"
      );

      const metrics = tokenTracker.getMetrics();
      expect(metrics.contentBreakdown.documents).toBe(tokens1 + tokens2);
    });

    test("should handle custom content types", () => {
      const customContent = "Custom content type for testing.";

      const tokens = tokenTracker.trackContentTokens(
        customContent,
        "customType",
        "test"
      );

      const metrics = tokenTracker.getMetrics();
      expect(metrics.contentBreakdown.customType).toBe(tokens);
    });

    test("should return 0 for empty content", () => {
      const tokens = tokenTracker.trackContentTokens("", "documents", "empty");
      expect(tokens).toBe(0);
    });

    test("should skip tracking when disabled", () => {
      const disabledTracker = new TokenTracker(mockLLMConnector, {
        trackContentTypes: false,
      });

      const tokens = disabledTracker.trackContentTokens(
        "Some content",
        "documents",
        "test"
      );
      expect(tokens).toBe(0);
    });
  });

  describe("Stage Tracking", () => {
    test("should track processing stages", async () => {
      const stageTracker = tokenTracker.startStage("testStage");

      expect(stageTracker.stageName).toBe("testStage");
      expect(stageTracker.startTime).toBeGreaterThan(0);

      stageTracker.addTokens(1000, "operation1");

      // Add a small delay to ensure duration > 0
      await new Promise((resolve) => setTimeout(resolve, 10));

      stageTracker.addTokens(500, "operation2");
      stageTracker.finish();

      const metrics = tokenTracker.getMetrics();
      expect(metrics.stages.testStage.tokens).toBe(1500);
      expect(metrics.stages.testStage.operations).toBe(2);
      expect(metrics.stages.testStage.duration).toBeGreaterThan(0);
    });

    test("should handle multiple stages independently", () => {
      const stage1 = tokenTracker.startStage("stage1");
      const stage2 = tokenTracker.startStage("stage2");

      stage1.addTokens(1000);
      stage2.addTokens(2000);

      stage1.finish();
      stage2.finish();

      const metrics = tokenTracker.getMetrics();
      expect(metrics.stages.stage1.tokens).toBe(1000);
      expect(metrics.stages.stage2.tokens).toBe(2000);
    });
  });

  describe("Iteration Tracking", () => {
    test("should track iterative processing metrics", () => {
      tokenTracker.trackIteration(1, 3, 2, 1500);
      tokenTracker.trackIteration(2, 2, 1, 1200);
      tokenTracker.trackIteration(3, 1, 3, 1800);

      const metrics = tokenTracker.getMetrics();
      expect(metrics.iterativeMetrics.totalIterations).toBe(3);
      expect(metrics.iterativeMetrics.documentsProcessed).toBe(6);
      expect(metrics.iterativeMetrics.memosProcessed).toBe(6);
      expect(metrics.iterativeMetrics.maxTokensInIteration).toBe(1800);
      expect(metrics.iterativeMetrics.minTokensInIteration).toBe(1200);
    });

    test("should calculate average tokens per iteration", () => {
      // Add some total tokens through LLM responses
      const response1 = createMockLLMResponse(500, 300);
      const response2 = createMockLLMResponse(400, 300);
      tokenTracker.trackLLMResponse(response1);
      tokenTracker.trackLLMResponse(response2);

      tokenTracker.trackIteration(1, 2, 1, 800);
      tokenTracker.trackIteration(2, 1, 2, 900);

      const metrics = tokenTracker.getMetrics();
      // Total tokens: 800 + 700 = 1500, so average = 750
      expect(metrics.iterativeMetrics.averageTokensPerIteration).toBe(
        Math.round(1500 / 2)
      );
    });
  });

  describe("Budget Tracking", () => {
    test("should track budget allocation and utilization", () => {
      const budget = {
        total: 100000,
        availableForContent: 70000,
        system: 5000,
        userPromptOverhead: 3000,
        reserved: 4000,
      };

      tokenTracker.trackBudget(budget, 65000);

      const metrics = tokenTracker.getMetrics();
      expect(metrics.budgetTracking.contextWindowSize).toBe(128000);
      expect(metrics.budgetTracking.availableBudget).toBe(70000);
      expect(metrics.budgetTracking.budgetUtilization).toBe(93); // 65000/70000 * 100
      expect(metrics.budgetTracking.exceedsBudget).toBe(false);
    });

    test("should detect budget overflow", () => {
      const budget = {
        availableForContent: 50000,
      };

      tokenTracker.trackBudget(budget, 55000);

      const metrics = tokenTracker.getMetrics();
      expect(metrics.budgetTracking.exceedsBudget).toBe(true);
      expect(metrics.warnings.length).toBeGreaterThan(0);
      expect(metrics.warnings[0].warning).toContain("Budget exceeded");
    });
  });

  describe("Error and Warning Handling", () => {
    test("should track errors with timestamp and stage", () => {
      const errorMessage = "Test error occurred";
      const stage = "testStage";

      tokenTracker.addError(errorMessage, stage);

      const metrics = tokenTracker.getMetrics();
      expect(metrics.errors.length).toBe(1);
      expect(metrics.errors[0].error).toBe(errorMessage);
      expect(metrics.errors[0].stage).toBe(stage);
      expect(metrics.errors[0].timestamp).toBeGreaterThan(0);
    });

    test("should track warnings with timestamp and stage", () => {
      const warningMessage = "Test warning occurred";
      const stage = "testStage";

      tokenTracker.addWarning(warningMessage, stage);

      const metrics = tokenTracker.getMetrics();
      expect(metrics.warnings.length).toBe(1);
      expect(metrics.warnings[0].warning).toBe(warningMessage);
      expect(metrics.warnings[0].stage).toBe(stage);
      expect(metrics.warnings[0].timestamp).toBeGreaterThan(0);
    });

    test("should handle multiple errors and warnings", () => {
      tokenTracker.addError("Error 1", "stage1");
      tokenTracker.addError("Error 2", "stage2");
      tokenTracker.addWarning("Warning 1", "stage1");

      const metrics = tokenTracker.getMetrics();
      expect(metrics.errors.length).toBe(2);
      expect(metrics.warnings.length).toBe(1);
    });
  });

  describe("Efficiency Calculations", () => {
    test("should calculate processing efficiency", () => {
      // Add some processing time
      const startTime = tokenTracker.metrics.startTime;
      tokenTracker.metrics.startTime = startTime - 10000; // 10 seconds ago

      // Add some token usage
      const response = createMockLLMResponse(800, 400);
      tokenTracker.trackLLMResponse(response, "testStage");

      // Add some iterations
      tokenTracker.trackIteration(1, 2, 1, 600);
      tokenTracker.trackIteration(2, 1, 2, 600);

      const efficiency = tokenTracker.calculateEfficiency();

      expect(efficiency.processingDuration).toBeGreaterThanOrEqual(10000);
      expect(efficiency.tokensPerSecond).toBeGreaterThan(0);
      expect(efficiency.iterativeEfficiency).toBe(600); // 1200 tokens / 2 iterations
      expect(efficiency.errorRate).toBe(0);
    });

    test("should calculate error rate correctly", () => {
      // Add some operations
      const stage = tokenTracker.startStage("testStage");
      stage.addTokens(1000, "op1");
      stage.addTokens(1000, "op2");
      stage.finish();

      // Add an error
      tokenTracker.addError("Test error", "testStage");

      const efficiency = tokenTracker.calculateEfficiency();
      expect(efficiency.errorRate).toBe(0.5); // 1 error / 2 operations
    });
  });

  describe("Report Generation", () => {
    test("should generate comprehensive report", () => {
      // Set up some data
      const response = createMockLLMResponse(1000, 500);
      tokenTracker.trackLLMResponse(
        response,
        "documentDescriptions",
        "test-doc"
      );
      tokenTracker.trackContentTokens(
        "Test document content",
        "documents",
        "test-doc"
      );
      tokenTracker.trackIteration(1, 2, 1, 800);

      const budget = { availableForContent: 50000 };
      tokenTracker.trackBudget(budget, 25000);

      const report = tokenTracker.generateReport();

      // Check report structure
      expect(report.summary).toBeDefined();
      expect(report.contentBreakdown).toBeDefined();
      expect(report.stageMetrics).toBeArray();
      expect(report.iterativeMetrics).toBeDefined();
      expect(report.budgetAnalysis).toBeDefined();
      expect(report.efficiency).toBeDefined();
      expect(report.issues).toBeDefined();

      // Check summary values
      expect(report.summary.totalTokens).toBe(1500);
      expect(report.summary.inputTokens).toBe(1000);
      expect(report.summary.outputTokens).toBe(500);
      expect(report.summary.totalOperations).toBe(1);
    });

    test("should include budget recommendations", () => {
      // Set up high budget utilization
      const budget = { availableForContent: 10000 };
      tokenTracker.trackBudget(budget, 9500);

      // Set up high iteration count
      for (let i = 1; i <= 7; i++) {
        tokenTracker.trackIteration(i, 1, 1, 1000);
      }

      const report = tokenTracker.generateReport();

      expect(report.budgetAnalysis.recommendations.length).toBeGreaterThan(0);
      expect(
        report.budgetAnalysis.recommendations.some(
          (rec) =>
            rec.includes("increasing chunk size") ||
            rec.includes("reducing content")
        )
      ).toBe(true);
      expect(
        report.budgetAnalysis.recommendations.some((rec) =>
          rec.includes("High iteration count")
        )
      ).toBe(true);
    });

    test("should export metrics to JSON", () => {
      const response = createMockLLMResponse(500, 300);
      tokenTracker.trackLLMResponse(response, "testStage");

      const jsonString = tokenTracker.exportMetrics(false);
      const parsedData = JSON.parse(jsonString);

      expect(parsedData.summary.totalTokens).toBe(800);
      expect(parsedData.stageMetrics.length).toBeGreaterThan(0);
    });

    test("should export pretty-formatted JSON", () => {
      const response = createMockLLMResponse(500, 300);
      tokenTracker.trackLLMResponse(response, "testStage");

      const prettyJson = tokenTracker.exportMetrics(true);

      // Pretty-formatted JSON should contain newlines and indentation
      expect(prettyJson).toContain("\n");
      expect(prettyJson).toContain("  ");
    });
  });

  describe("Reset Functionality", () => {
    test("should reset metrics to initial state", () => {
      // Add some data
      const response = createMockLLMResponse(1000, 500);
      tokenTracker.trackLLMResponse(response, "testStage");
      tokenTracker.trackContentTokens("Test content", "documents", "test");
      tokenTracker.addError("Test error", "testStage");
      tokenTracker.addWarning("Test warning", "testStage");

      // Verify data exists
      let metrics = tokenTracker.getMetrics();
      expect(metrics.totalTokens).toBe(1500);
      expect(metrics.errors.length).toBe(1);
      expect(metrics.warnings.length).toBe(1);

      // Reset and verify clean state
      tokenTracker.resetMetrics();
      metrics = tokenTracker.getMetrics();

      expect(metrics.totalTokens).toBe(0);
      expect(metrics.totalInputTokens).toBe(0);
      expect(metrics.totalOutputTokens).toBe(0);
      expect(metrics.errors.length).toBe(0);
      expect(metrics.warnings.length).toBe(0);
      expect(
        Object.values(metrics.contentBreakdown).every((val) => val === 0)
      ).toBe(true);
    });
  });
});

// Helper matchers for Jest
expect.extend({
  toBeArray(received) {
    const pass = Array.isArray(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be an array`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be an array`,
        pass: false,
      };
    }
  },
});

console.log("TokenTracker tests completed successfully!");
