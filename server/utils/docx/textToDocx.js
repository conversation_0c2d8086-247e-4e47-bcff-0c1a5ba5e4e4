// Use require for built-in/CJS modules
const fs = require("fs");
const path = require("path");
const { v4: uuidv4 } = require("uuid");
const i18n = require("../i18n");

/**
 * Pre-processes markdown text to ensure proper paragraph breaks after numbered lists.
 * Adds a blank line after a numbered list item if it's followed by a non-empty,
 * non-list item line.
 * @param {string} text - The markdown content
 * @returns {string} - Pre-processed markdown content
 */
function preprocessMarkdownForListBreaks(text) {
  if (!text) return "";
  const lines = text.split("\n");
  const processedLines = [];
  const numberedListItemRegex = /^\s*\d+\.\s+/; // Matches " 1. ", "2. ", etc.

  for (let i = 0; i < lines.length; i++) {
    const currentLine = lines[i];
    processedLines.push(currentLine);

    const isNumberedItem = numberedListItemRegex.test(currentLine);

    if (isNumberedItem) {
      // Look ahead for the next non-blank line
      let nextContentLineIndex = -1;
      for (let j = i + 1; j < lines.length; j++) {
        if (lines[j].trim() !== "") {
          nextContentLineIndex = j;
          break;
        }
      }

      // If a non-blank line was found later...
      if (nextContentLineIndex !== -1) {
        const nextContentLine = lines[nextContentLineIndex];
        const isNextContentListItem =
          numberedListItemRegex.test(nextContentLine);

        // ...and it's NOT another list item...
        if (!isNextContentListItem) {
          // Check if a blank line separator is needed.
          const nextLineIndex = i + 1;
          let alreadyHasBlankSeparator = false;
          if (
            nextLineIndex < lines.length &&
            lines[nextLineIndex].trim() === ""
          ) {
            // The immediate next line is blank or whitespace, acting as a separator.
            alreadyHasBlankSeparator = true;
          }

          // Add the blank line only if one doesn't already exist immediately after.
          if (!alreadyHasBlankSeparator) {
            processedLines.push("");
          }
        }
      }
    }
  }
  return processedLines.join("\n");
}

/**
 * Converts markdown text to a DOCX file using remark-docx
 * @param {string} text - The markdown content to convert
 * @param {string} outputPath - Path to save the DOCX file
 * @param {Object} options - Additional options (currently unused by remark-docx)
 * @returns {Promise<string>} - Path to the generated DOCX file
 */
async function textToDocx(text, outputPath, options = {}) {
  // Dynamically import ESM modules
  const { unified } = await import("unified");
  const { default: markdown } = await import("remark-parse");
  const { default: docx } = await import("remark-docx");

  // Pre-process HTML tags to markdown-compatible format
  let processedText = text;

  // Convert <mark> tags to bold formatting (since remark-docx doesn't support highlighting)
  processedText = processedText.replace(
    /<mark\s+style="background-color:\s*([^"]+)"[^>]*>(.*?)<\/mark>/gi,
    (match, color, content) => {
      // Use bold formatting as a visual indicator for highlighted text
      return `**${content}**`;
    }
  );

  // Convert <span> tags with colors to italic formatting (since remark-docx doesn't support text colors)
  processedText = processedText.replace(
    /<span\s+style="color:\s*([^"]+)"[^>]*>(.*?)<\/span>/gi,
    (match, color, content) => {
      // Use italic formatting as a visual indicator for colored text
      return `*${content}*`;
    }
  );

  // Pre-process the text to handle list breaks
  processedText = preprocessMarkdownForListBreaks(processedText);

  // Define basic styles for spacing
  const styles = {
    paragraphStyles: [
      {
        id: "Normal",
        name: "Normal",
        paragraph: {
          spacing: {
            after: 160, // Approx 8pt space after paragraphs
          },
        },
      },
    ],
    headingStyles: [
      {
        id: "Heading1",
        name: "Heading 1",
        basedOn: "Normal",
        paragraph: {
          spacing: {
            before: 320, // Approx 16pt space before H1
            after: 160, // Approx 8pt space after H1
          },
        },
      },
      {
        id: "Heading2",
        name: "Heading 2",
        basedOn: "Normal",
        paragraph: {
          spacing: {
            before: 240, // Approx 12pt space before H2
            after: 120, // Approx 6pt space after H2
          },
        },
      },
      {
        id: "Heading3",
        name: "Heading 3",
        basedOn: "Normal",
        paragraph: {
          spacing: {
            before: 200,
            after: 100,
          },
        },
      },
    ],
  };

  const processor = unified().use(markdown).use(docx, {
    output: "buffer",
    styles: styles,
  });

  try {
    const doc = await processor.process(processedText);
    const buffer = await doc.result;

    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    fs.writeFileSync(outputPath, buffer);

    return outputPath;
  } catch (error) {
    console.error("Error generating DOCX from text using remark-docx:", error);
    // Construct error message using potentially async i18n.t
    const errorMsgBase = await i18n.t(
      "docxEdit.errors.textToDocxError",
      "Error generating DOCX from text"
    );
    throw new Error(`${errorMsgBase}: ${error.message}`);
  }
}

/**
 * Creates a unique filename for the generated DOCX
 * @param {string} prefix - Optional prefix for the filename
 * @returns {string} - A unique filename for the DOCX
 */
function createDocxFilename(prefix = "document") {
  const uniqueId = uuidv4().substring(0, 8);
  const timestamp = new Date()
    .toISOString()
    .replace(/[:.]/g, "-")
    .substring(0, 19);
  return `${prefix}-${timestamp}-${uniqueId}.docx`;
}

module.exports = {
  textToDocx,
  createDocxFilename,
  preprocessMarkdownForListBreaks,
};
