const translations = {
  common: {
    thinking: "Réflexion...",
  },
  errors: {
    ai: {
      ollama: {
        "connection-failed":
          "Votre instance Ollama n'a pas pu être atteinte ou ne répond pas. Veuillez vous assurer que le serveur API est en cours d'exécution et que vos informations de connexion sont correctes.",
        "empty-response": "La réponse textuelle d'Ollama était vide.",
        "stream-failed": "Impossible de diffuser le chat. {{message}}",
        "no-endpoint": "Aucun point de terminaison Ollama API n'a été défini.",
        "no-token-limit":
          "Aucune limite de contexte de token Ollama n'a été définie.",
        "provider-not-responding":
          "Le fournisseur d'IA local ne répond pas, soit parce que le serveur est hors ligne, soit parce qu'il est surchargé.",
        "provider-unavailable":
          "Le serveur IA local est actuellement indisponible. Veuillez vérifier l'installation et vous assurer qu'il fonctionne et qu'il n'est pas surchargé.",
      },
      azure: {
        "no-endpoint": "Aucun point de terminaison Azure API n'a été défini.",
        "no-key": "Aucune clé API Azure n'a été définie.",
        "no-model": "Aucun modèle Azure OpenAI n'a été défini.",
        "no-model-pref":
          "Aucun ENV OPEN_MODEL_PREF n'a été défini. Cela doit être le nom d'un déploiement sur votre compte Azure pour un modèle de chat LLM comme GPT-3.5.",
      },
      openai: {
        "no-key": "Aucune clé API OpenAI n'a été définie.",
      },
      mistral: {
        "no-key": "Aucune clé API Mistral n'a été définie.",
      },
      litellm: {
        "no-base-path":
          "LiteLLM doit avoir un chemin de base valide pour utiliser l'API.",
        "no-model": "LiteLLM doit avoir un modèle valide défini.",
        "no-token-limit": "Aucune limite de contexte de token n'a été définie.",
      },
      textgenwebui: {
        "no-base-path":
          "TextGenWebUI doit avoir un chemin de base valide pour utiliser l'API.",
        "no-token-limit": "Aucune limite de contexte de token n'a été définie.",
      },
      localai: {
        "no-token-limit":
          "Aucune limite de contexte de token LocalAi n'a été définie.",
      },
      lmstudio: {
        "no-token-limit":
          "Aucune limite de contexte de token LMStudio n'a été définie.",
      },
      fireworks: {
        "invalid-model":
          "Discussion FireworksAI : {{model}} n'est pas valide pour la complétion de chat !",
      },
      context: {
        header: "Source(s)",
      },
    },
    system: {
      "ssl-boot-failed":
        "Échec du démarrage SSL : {{message}} - retour au démarrage HTTP.",
      "no-app": 'Aucune "app" définie - arrêt !',
      validation: {
        "invalid-transcription-provider":
          "{{input}} n'est pas un fournisseur de modèle de transcription valide.",
        "invalid-safety-setting":
          "Paramètre de sécurité invalide. Doit être l'un de {{modes}}.",
        "invalid-model-type":
          "Type de modèle invalide. Doit être l'un de {{models}}.",
        "invalid-embedding-model":
          "Type de modèle d'embedding invalide. Doit être l'un de {{supported}}.",
      },
    },
    document: {
      "sync-failed":
        "Échec de la synchronisation du document {{filename}} après {{attempts}} tentatives.",
      "invalid-metadata":
        "Le document {{filename}} a des métadonnées invalides ou manquantes.",
      removed:
        "Le document {{filename}} a été retiré de l'ensemble des documents surveillés.",
    },
    validation: {
      "value-not-number": "La valeur n'est pas un nombre.",
      "value-must-be-nonzero": "La valeur doit être non nulle.",
      "value-cannot-be-negative": "La valeur ne peut pas être inférieure à 0.",
      "invalid-serp-provider": "Fournisseur SERP invalide.",
      responseHeader: "Réponse Générée",
      contextHeader: "Contexte et Sources Originaux",
      "max-rerank-limit-min":
        "La limite maximale de reclassement doit être d'au moins 10.",
      "max-rerank-limit-max":
        "La limite maximale de reclassement ne peut pas dépasser 200.",
      content: {
        "user-question-sources-header": "QUESTION UTILISATEUR ET SOURCES",
        "system-response-header": "RÉPONSE SYSTÈME",
        "not-provided": "Non fourni",
      },
    },
    manualWorkEstimator: {
      content: {
        "user-question-header": "QUESTION UTILISATEUR",
        "system-response-header": "RÉPONSE SYSTÈME",
      },
    },
    embedding: {
      "lmstudio-failed": "Échec de l'embedding LMStudio : {{errors}}",
      "jina-failed": "Échec de l'embedding Jina : {{errors}}",
    },
    env: {
      "jina-api-key-error": "La clé API Jina doit commencer par jina_",
      "empty-value": "La valeur ne peut pas être vide",
      "non-zero-required": "La valeur doit être supérieure à zéro",
      "must-be-number": "La valeur doit être un nombre",
      "invalid-url": "L'URL n'est pas une URL valide",
      "openai-key-format": "La clé OpenAI doit commencer par sk-",
      "contextual-prompt-format":
        "L'invite contextuelle de l'utilisateur doit contenir {file} & {chunk}",
      "anthropic-key-format": "La clé Anthropic doit commencer par sk-ant-",
      "external-llm-url-format": "L'URL doit inclure /v1",
      "url-no-trailing-slash":
        "L'URL ne peut pas se terminer par une barre oblique",
      "invalid-tts-provider": "{{input}} n'est pas un fournisseur TTS valide",
      "invalid-whisper-model":
        "{{input}} n'est pas une sélection de modèle Whisper valide",
      "invalid-llm-provider": "{{input}} n'est pas un fournisseur LLM valide",
    },
    // Workspace sharing errors
    workspace: {
      onlyOwnerShare:
        "Seuls les propriétaires d'espace de travail peuvent partager des espaces de travail",
      onlyOwnerRevoke:
        "Seuls les propriétaires d'espace de travail peuvent révoquer le partage d'espaces de travail",
      shareFailed: "Échec du partage de l'espace de travail",
      revokeFailed: "Échec de la révocation du partage de l'espace de travail",
      getStatusFailed: "Échec de la récupération du statut de partage",
    },
    // Thread sharing errors
    thread: {
      onlyOwnerShare:
        "Seuls les propriétaires de fil peuvent partager des fils",
      onlyOwnerRevoke:
        "Seuls les propriétaires de fil peuvent révoquer le partage de fil",
      shareFailed: "Échec du partage du fil",
      revokeFailed: "Échec de la révocation du partage du fil",
      getStatusFailed: "Échec de la récupération du statut de partage du fil",
    },
    auth: {
      "authentication-required": "Authentification requise",
      "insufficient-permissions": "Permissions insuffisantes",
    },
  },
  notifications: {
    document: {
      invalid:
        "Le document {{filename}} n'a pas de métadonnées, est corrompu ou invalide et a été supprimé.",
      removed:
        "Le document {{filename}} a été retiré de l'ensemble des documents surveillés.",
    },
  },
  docx: {
    title: "Export de conversation",
    exportedOn: "Exporté le",
    downloadedOn: "Téléchargé le",
    keywords: "conversation, export",
    description: "Message de conversation exporté",
    tokenCount: "Nombre de tokens",
    canvasDocumentTitle: "Document Canvas",
    errors: {
      contentRequired: "Le contenu est requis",
      writingFile: "Erreur lors de l'écriture du fichier temporaire",
      generating: "Erreur lors de la génération du document",
    },
  },
  deep_search: {
    instruction:
      "Si des sources DeepSearch sont pertinentes pour la requête de l'utilisateur et utilisées dans la réponse, fournissez des liens cliquables vers ces sources à la fin du message dans ce format exact : 'Sources : [Titre de la source 1](URL1), [Titre de la source 2](URL2)'. Le texte cliquable pour chaque lien doit être une description concise du contenu de la source (par exemple, le titre de la page ou un résumé). Ne référencez jamais les sources DeepSearch en utilisant des numéros dans le texte (comme [1] ou [Source 1]) ; référez-vous toujours à la source directement par son nom ou son contenu. N'utilisez pas de formats de citation académiques.",
  },
};

module.exports = translations;
