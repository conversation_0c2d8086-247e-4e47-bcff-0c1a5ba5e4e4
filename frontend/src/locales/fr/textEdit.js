const TRANSLATIONS = {
  "text-edit": {
    toolbar: {
      bold: "Mettre le texte en gras",
      italic: "Mettre le texte en italique",
      underline: "<PERSON><PERSON><PERSON> le texte",
      strikethrough: "Barrer le texte",
      code: "Formater comme code",
      "heading-1": "Formater comme grand titre",
      "heading-2": "Formater comme titre moyen",
      "heading-3": "Formater comme petit titre",
      "bullet-list": "Créer une liste à puces",
      "numbered-list": "Créer une liste numérotée",
      blockquote: "Formater comme bloc de citation",
      "add-link": "Ajouter un lien",
      "remove-link": "Supprimer le lien",
      "insert-table": "Insérer un tableau",
      "text-color": "Changer la couleur du texte",
      highlight: "Surligner le texte",
      undo: "Annuler la dernière action",
      redo: "Refaire la dernière action",
    },
    modals: {
      link: {
        title: "Ajouter un lien",
        "url-label": "URL",
        "url-placeholder": "https://example.com",
        cancel: "Annuler",
        add: "Ajouter le lien",
      },
      "text-color": {
        title: "Définir la couleur du texte",
        "color-label": "Choisir la couleur",
        "custom-color": "Couleur personnalisée",
        "color-placeholder": "#ff0000, rouge, rgb(255,0,0)",
        "color-help":
          "Entrez une couleur en hex (#ff0000), RGB (rgb(255,0,0)), ou nom (rouge)",
        cancel: "Annuler",
        apply: "Appliquer la couleur",
      },
      highlight: {
        title: "Définir la couleur de surlignage",
        "color-label": "Choisir la couleur de surlignage",
        "custom-color": "Couleur de surlignage personnalisée",
        "color-placeholder": "#ffff00, jaune, rgb(255,255,0)",
        "color-help":
          "Entrez une couleur en hex (#ffff00), RGB (rgb(255,255,0)), ou nom (jaune)",
        cancel: "Annuler",
        apply: "Appliquer le surlignage",
      },
    },
    errors: {
      "invalid-url": "URL invalide. Veuillez entrer une adresse web valide.",
      "invalid-color":
        "Couleur invalide. Veuillez entrer une couleur CSS valide.",
      "dangerous-url":
        "Cette URL n'est pas autorisée pour des raisons de sécurité.",
      "dangerous-color":
        "Cette couleur contient du contenu potentiellement dangereux.",
    },
    colors: {
      select: "Sélectionner",
      highlight: "surlignage",
      "no-highlight": "Aucun surlignage",
      black: "Noir",
      "dark-gray": "Gris foncé",
      gray: "Gris",
      "light-gray": "Gris clair",
      red: "Rouge",
      orange: "Orange",
      amber: "Ambre",
      yellow: "Jaune",
      lime: "Citron vert",
      green: "Vert",
      emerald: "Émeraude",
      teal: "Sarcelle",
      cyan: "Cyan",
      sky: "Ciel",
      blue: "Bleu",
      indigo: "Indigo",
      violet: "Violet",
      purple: "Violet",
      fuchsia: "Fuchsia",
      pink: "Rose",
      rose: "Rose",
    },
  },
};

export default TRANSLATIONS;
