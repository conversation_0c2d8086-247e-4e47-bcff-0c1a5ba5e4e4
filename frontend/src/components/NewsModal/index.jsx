import { useState, useEffect } from "react";
import Modal from "@/components/ui/Modal";
import Markdown from "@/components/ui/Markdown";
import { Button } from "@/components/Button";
import { useTranslation } from "react-i18next";
import { Info, Warning, CircleNotch } from "@phosphor-icons/react";
import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";
import showToast from "@/utils/toast";
import { resolveSystemNewsTranslations } from "@/data/news";

const priorityIcons = {
  low: Info,
  medium: Info,
  high: Warning,
  urgent: Warning,
};

const priorityColors = {
  low: "text-green-500",
  medium: "text-yellow-500",
  high: "text-orange-500",
  urgent: "text-red-500",
};

export default function NewsModal({
  isOpen,
  onClose,
  news = [],
  onNewsUpdate,
}) {
  const { t } = useTranslation();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [localNews, setLocalNews] = useState(news); // Local copy to manage dismissals

  // Update local news when props change
  useEffect(() => {
    setLocalNews(news);
    setCurrentIndex(0); // Reset to first item when news changes
  }, [news]);

  // Helper function to translate author names
  const translateAuthor = (author) => {
    if (author === "IST Legal Team") {
      return t("news-system-items.authors.ist-legal-team");
    } else if (author === "System") {
      return t("news-system-items.authors.system");
    }
    return author; // Return original if no translation found
  };

  // Helper function to translate category names
  const translateCategory = (category) => {
    if (category === "welcome") {
      return t("news-system-items.categories.welcome");
    } else if (category === "system-updates") {
      return t("news-system-items.categories.system-updates");
    } else if (category === "general") {
      return t("news-system-items.categories.general");
    }
    return category; // Return original if no translation found
  };

  // Resolve translations for system news using local news
  const resolvedNews = localNews.map((newsItem) => {
    if (newsItem.isSystemNews && (newsItem.titleKey || newsItem.contentKey)) {
      return resolveSystemNewsTranslations(newsItem, t);
    }
    return newsItem;
  });

  const currentNews = resolvedNews[currentIndex];
  const hasMultiple = resolvedNews.length > 1;

  useEffect(() => {
    if (isOpen && currentNews) {
      // For system news, don't automatically mark as viewed
      // For database news, also don't automatically mark as viewed
      // News should only be marked as dismissed when user explicitly dismisses it
    }
  }, [isOpen, currentNews]);

  const dismissNews = async (newsId, isSystemNews = false) => {
    setIsProcessing(true);
    try {
      const response = isSystemNews
        ? await fetch(`${API_BASE}/news/system/${newsId}/dismiss`, {
            method: "POST",
            headers: baseHeaders(),
          })
        : await fetch(`${API_BASE}/news/${newsId}/dismiss`, {
            method: "POST",
            headers: baseHeaders(),
          });

      if (!response.ok) {
        throw new Error(`Failed to dismiss news: ${response.status}`);
      }

      // Remove this news from the local list immediately using functional update
      setLocalNews((currentLocalNews) => {
        const updatedNews = currentLocalNews.filter((n) => n.id !== newsId);

        // Handle all dependent logic within this callback or immediately after
        if (updatedNews.length === 0) {
          // Close modal immediately if no more news
          onClose();
          // Notify parent after a delay to prevent immediate reopening
          setTimeout(() => {
            if (onNewsUpdate) {
              onNewsUpdate();
            }
          }, 300);
        } else {
          // Adjust current index if needed - use functional update to ensure consistency
          setCurrentIndex((currentIdx) => {
            if (currentIdx >= updatedNews.length) {
              return updatedNews.length - 1;
            }
            return currentIdx;
          });
          // Don't notify parent immediately - let user continue with remaining news
          // Parent will be notified when modal is closed
        }

        return updatedNews;
      });
    } catch (error) {
      console.error("Failed to dismiss news:", error);
      showToast(t("news-system.error.dismiss"), "error");
    } finally {
      setIsProcessing(false);
    }
  };

  const goToNext = () => {
    if (currentIndex < resolvedNews.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleClose = () => {
    // Notify parent to refresh news when modal is closed
    if (onNewsUpdate) {
      onNewsUpdate();
    }
    onClose();
  };

  if (!isOpen || !currentNews) return null;

  const PriorityIcon = priorityIcons[currentNews.priority] || Info;
  const priorityColor =
    priorityColors[currentNews.priority] || "text-green-500";

  // Create the modal title with icon and source indicator
  const modalTitle = (
    <div className="flex items-center gap-3">
      <PriorityIcon className={`w-6 h-6 ${priorityColor}`} />
      <div className="flex flex-col">
        <span className="text-xl font-semibold">{currentNews.title}</span>
        {currentNews.isSystemNews && (
          <span className="text-xs text-blue-600 font-medium">
            {t("news-system.management.source.system")}
          </span>
        )}
      </div>
    </div>
  );

  // Create the modal footer with actions
  const modalFooter = (
    <>
      {!currentNews.isDismissed && (
        <Button
          onClick={() => dismissNews(currentNews.id, currentNews.isSystemNews)}
          disabled={isProcessing}
          variant="outline"
        >
          {isProcessing ? (
            <CircleNotch className="w-4 h-4 animate-spin" />
          ) : null}
          {t("news-system.dismissThis")}
        </Button>
      )}
      <Button onClick={handleClose}>{t("news-system.close")}</Button>
    </>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={modalTitle}
      footer={modalFooter}
      className="max-w-2xl"
    >
      {/* Content */}
      <div className="space-y-4">
        <div className="prose max-w-none">
          <Markdown content={currentNews.content} />
        </div>

        {/* System news metadata */}
        {currentNews.isSystemNews && currentNews.metadata && (
          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="text-sm text-blue-800">
              {currentNews.metadata.author && (
                <p>
                  <strong>{t("news-system-items.metadata.author")}:</strong>{" "}
                  {translateAuthor(currentNews.metadata.author)}
                </p>
              )}
              {currentNews.version && (
                <p>
                  <strong>{t("news-system-items.metadata.version")}:</strong>{" "}
                  {currentNews.version}
                </p>
              )}
              {currentNews.metadata.category && (
                <p>
                  <strong>{t("news-system-items.metadata.category")}:</strong>{" "}
                  {translateCategory(currentNews.metadata.category)}
                </p>
              )}
            </div>
          </div>
        )}

        {/* Navigation for multiple news items */}
        {hasMultiple && (
          <div className="flex items-center justify-center gap-4">
            <Button
              onClick={goToPrevious}
              disabled={currentIndex === 0}
              variant="secondary"
              size="sm"
            >
              {t("news-system.previous")}
            </Button>
            <span className="text-sm text-muted">
              {currentIndex + 1} {t("news-system.of")} {resolvedNews.length}
            </span>
            <Button
              onClick={goToNext}
              disabled={currentIndex === resolvedNews.length - 1}
              variant="secondary"
              size="sm"
            >
              {t("news-system.next")}
            </Button>
          </div>
        )}

        {/* Date */}
        <div className="text-xs text-muted text-right">
          {new Date(currentNews.createdAt).toLocaleDateString()}
        </div>
      </div>
    </Modal>
  );
}
