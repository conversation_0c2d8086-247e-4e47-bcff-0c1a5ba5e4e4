import React, {
  useEffect,
  useImperative<PERSON>and<PERSON>,
  forwardRef,
  useState,
  useRef,
} from "react";
import { useTranslation } from "react-i18next";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Link from "@tiptap/extension-link";
import Table from "@tiptap/extension-table";
import TableRow from "@tiptap/extension-table-row";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import Underline from "@tiptap/extension-underline";
import TextStyle from "@tiptap/extension-text-style";
import Color from "@tiptap/extension-color";
import Highlight from "@tiptap/extension-highlight";
import TurndownService from "turndown";
import { marked } from "marked";
import DOMPurify from "dompurify";
import {
  MdFormatBold,
  MdFormatItalic,
  MdFormatUnderlined,
  MdFormatStrikethrough,
  MdCode,
  MdFormatListBulleted,
  MdFormatListNumbered,
  MdFormatQuote,
  MdLink,
  MdTableChart,
  MdUndo,
  MdRedo,
  MdPalette,
  MdHighlight,
} from "react-icons/md";
import { LuHeading1, LuHeading2, LuHeading3 } from "react-icons/lu";
import { Button } from "@/components/Button";
import Modal from "@/components/ui/Modal";
import Input from "@/components/ui/Input";
import Label from "@/components/ui/Label";
import SimpleTooltip from "@/components/ui/Tooltip";
import {
  sanitizeUrl,
  sanitizeCssColor,
  isColorInSafePalette,
} from "@/utils/validation";

// Configure marked for markdown to HTML conversion (done once at module load)
marked.setOptions({
  breaks: true,
  gfm: true,
});

// Create and configure TurndownService instance (done once at module load)
const turndownService = new TurndownService({
  headingStyle: "atx",
  codeBlockStyle: "fenced",
  // Preserve empty lines and whitespace
  blankReplacement: function (content, node) {
    return node.isBlock ? "\n\n" : "";
  },
  keepReplacement: function (content, node) {
    return node.isBlock ? "\n\n" + node.outerHTML + "\n\n" : node.outerHTML;
  },
});

// Configure turndown for better markdown conversion
turndownService.addRule("strikethrough", {
  filter: ["del", "s", "strike"],
  replacement: (content) => `~~${content}~~`,
});

// Preserve empty paragraphs as blank lines
turndownService.addRule("emptyParagraph", {
  filter: function (node) {
    return (
      node.nodeName === "P" &&
      (!node.textContent ||
        node.textContent.trim() === "" ||
        node.innerHTML === '<br class="ProseMirror-trailingBreak">')
    );
  },
  replacement: function () {
    return "\n\n";
  },
});

// Preserve line breaks
turndownService.addRule("lineBreak", {
  filter: "br",
  replacement: function (content, node) {
    // Don't convert ProseMirror trailing breaks to newlines in markdown
    if (
      node.className &&
      node.className.includes("ProseMirror-trailingBreak")
    ) {
      return "";
    }
    return "\n";
  },
});

// Preserve highlighting as HTML in markdown
turndownService.addRule("highlight", {
  filter: ["mark"],
  replacement: (content, node) => {
    const style = node.getAttribute("style") || "";
    const colorMatch = style.match(/background-color:\s*([^;]+)/);
    const rawColor = colorMatch ? colorMatch[1].trim() : "yellow";
    // Sanitize the color before using it
    const color = sanitizeCssColor(rawColor) || "yellow";
    return `<mark style="background-color: ${color}">${content}</mark>`;
  },
});

// Preserve text colors as HTML in markdown
turndownService.addRule("textColor", {
  filter: (node) => {
    return node.nodeName === "SPAN" && node.style && node.style.color;
  },
  replacement: (content, node) => {
    const rawColor = node.style.color;
    // Sanitize the color before using it
    const color = sanitizeCssColor(rawColor) || "#000000";
    return `<span style="color: ${color}">${content}</span>`;
  },
});

// Color palette options (defined once at module load)
const colorPalette = [
  { nameKey: "text-edit.colors.black", value: "#000000" },
  { nameKey: "text-edit.colors.dark-gray", value: "#374151" },
  { nameKey: "text-edit.colors.gray", value: "#6B7280" },
  { nameKey: "text-edit.colors.light-gray", value: "#9CA3AF" },
  { nameKey: "text-edit.colors.red", value: "#EF4444" },
  { nameKey: "text-edit.colors.orange", value: "#F97316" },
  { nameKey: "text-edit.colors.amber", value: "#F59E0B" },
  { nameKey: "text-edit.colors.yellow", value: "#FFDD00" },
  { nameKey: "text-edit.colors.lime", value: "#84CC16" },
  { nameKey: "text-edit.colors.green", value: "#22C55E" },
  { nameKey: "text-edit.colors.emerald", value: "#10B981" },
  { nameKey: "text-edit.colors.teal", value: "#14B8A6" },
  { nameKey: "text-edit.colors.cyan", value: "#06B6D4" },
  { nameKey: "text-edit.colors.sky", value: "#0EA5E9" },
  { nameKey: "text-edit.colors.blue", value: "#3B82F6" },
  { nameKey: "text-edit.colors.indigo", value: "#6366F1" },
  { nameKey: "text-edit.colors.violet", value: "#8B5CF6" },
  { nameKey: "text-edit.colors.purple", value: "#A855F7" },
  { nameKey: "text-edit.colors.fuchsia", value: "#D946EF" },
  { nameKey: "text-edit.colors.pink", value: "#EC4899" },
  { nameKey: "text-edit.colors.rose", value: "#F43F5E" },
];

const highlightPalette = [
  { nameKey: "text-edit.colors.no-highlight", value: null }, // Special value for removing highlight
  { nameKey: "text-edit.colors.yellow", value: "#FFFF99" },
  { nameKey: "text-edit.colors.green", value: "#BBF7D0" },
  { nameKey: "text-edit.colors.blue", value: "#BFDBFE" },
  { nameKey: "text-edit.colors.purple", value: "#DDD6FE" },
  { nameKey: "text-edit.colors.pink", value: "#FBCFE8" },
  { nameKey: "text-edit.colors.orange", value: "#FED7AA" },
  { nameKey: "text-edit.colors.red", value: "#FECACA" },
  { nameKey: "text-edit.colors.gray", value: "#E5E7EB" },
];

// Helper function to convert markdown to HTML (defined once at module load)
const markdownToHtml = (markdown) => {
  if (!markdown) return "";
  try {
    // First, let marked parse the markdown normally
    const html = marked.parse(markdown);

    // Then sanitize the HTML to prevent XSS attacks
    return DOMPurify.sanitize(html, {
      // Allow common HTML elements that TipTap uses
      ALLOWED_TAGS: [
        "p",
        "br",
        "strong",
        "em",
        "u",
        "s",
        "del",
        "strike",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "ul",
        "ol",
        "li",
        "blockquote",
        "a",
        "span",
        "mark",
        "table",
        "thead",
        "tbody",
        "tr",
        "th",
        "td",
        "code",
        "pre",
      ],
      // Allow safe attributes
      ALLOWED_ATTR: ["href", "title", "style", "class", "colspan", "rowspan"],
      // Allow data URIs for images if needed
      ALLOW_DATA_ATTR: false,
      // Keep whitespace
      KEEP_CONTENT: true,
    });
  } catch (error) {
    console.error("Error converting markdown to HTML:", error);
    return DOMPurify.sanitize(markdown); // Sanitize fallback content too
  }
};

const TipTapEditor = forwardRef(function TipTapEditor(
  {
    content = "",
    onChange,
    placeholder = "Start typing...",
    className = "",
    editable = true,
  },
  ref
) {
  const { t } = useTranslation();

  // Modal states
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
  const [isColorModalOpen, setIsColorModalOpen] = useState(false);
  const [isHighlightModalOpen, setIsHighlightModalOpen] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");
  const [textColor, setTextColor] = useState("");
  const [highlightColor, setHighlightColor] = useState("");

  // Error states
  const [linkError, setLinkError] = useState("");
  const [colorError, setColorError] = useState("");
  const [highlightError, setHighlightError] = useState("");

  // Ref to store the last set markdown content to prevent unnecessary updates
  const lastSetMarkdownRef = useRef("");
  // Ref to track if the content change is from internal editor updates
  const isInternalUpdateRef = useRef(false);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        paragraph: {
          HTMLAttributes: {
            class: "whitespace-pre-line",
          },
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: "text-primary underline",
        },
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      Underline,
      TextStyle,
      Color,
      Highlight.configure({
        multicolor: true,
      }),
    ],
    content: markdownToHtml(content),
    editable: editable,
    onUpdate: ({ editor }) => {
      // Mark this as an internal update to prevent circular updates
      isInternalUpdateRef.current = true;
      const html = editor.getHTML();
      const markdown = turndownService.turndown(html);
      lastSetMarkdownRef.current = markdown;
      onChange?.(html, markdown);
      // Reset the flag after a brief delay to allow parent state updates
      setTimeout(() => {
        isInternalUpdateRef.current = false;
      }, 0);
    },
  });

  useImperativeHandle(ref, () => ({
    getContent: () => {
      if (!editor) return "";
      return editor.getHTML();
    },
    getMarkdown: () => {
      if (!editor) return "";
      const html = editor.getHTML();
      return turndownService.turndown(html);
    },
    setContent: (newContent) => {
      if (editor) {
        // Mark as internal update to prevent circular updates
        isInternalUpdateRef.current = true;
        lastSetMarkdownRef.current = newContent;
        const htmlContent = markdownToHtml(newContent);
        editor.commands.setContent(htmlContent);
        // Reset the flag after a brief delay
        setTimeout(() => {
          isInternalUpdateRef.current = false;
        }, 0);
      }
    },
    focus: () => {
      if (editor) {
        editor.commands.focus();
      }
    },
  }));

  // Initialize the ref when editor is first created
  useEffect(() => {
    if (editor && content) {
      lastSetMarkdownRef.current = content;
    }
  }, [editor]); // Only run when editor is created

  useEffect(() => {
    if (editor && content) {
      // Only update if the markdown content is different from what we last set
      // AND the change is not from an internal editor update
      if (
        content !== lastSetMarkdownRef.current &&
        !isInternalUpdateRef.current
      ) {
        lastSetMarkdownRef.current = content;
        const htmlContent = markdownToHtml(content);
        editor.commands.setContent(htmlContent);
      }
    }
  }, [content, editor]);

  if (!editor) {
    return null;
  }

  const addLink = () => {
    setLinkUrl("");
    setLinkError("");
    setIsLinkModalOpen(true);
  };

  const handleLinkSubmit = () => {
    const sanitizedUrl = sanitizeUrl(linkUrl);
    if (sanitizedUrl) {
      editor.chain().focus().setLink({ href: sanitizedUrl }).run();
      setIsLinkModalOpen(false);
      setLinkUrl("");
      setLinkError("");
    } else {
      // Show error message for invalid URL
      setLinkError(t("text-edit.errors.invalid-url"));
    }
  };

  const removeLink = () => {
    editor.chain().focus().unsetLink().run();
  };

  const addTable = () => {
    editor
      .chain()
      .focus()
      .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
      .run();
  };

  const openColorModal = () => {
    setTextColor("");
    setColorError("");
    setIsColorModalOpen(true);
  };

  const handleColorSelect = (color) => {
    // Validate color is from safe palette or is a valid CSS color
    if (isColorInSafePalette(color, "text") || sanitizeCssColor(color)) {
      editor.chain().focus().setColor(color).run();
      setIsColorModalOpen(false);
      setTextColor("");
    } else {
      console.warn("Invalid color provided:", color);
    }
  };

  const handleColorSubmit = () => {
    const sanitizedColor = sanitizeCssColor(textColor);
    if (sanitizedColor) {
      editor.chain().focus().setColor(sanitizedColor).run();
      setIsColorModalOpen(false);
      setTextColor("");
      setColorError("");
    } else {
      setColorError(t("text-edit.errors.invalid-color"));
    }
  };

  const openHighlightModal = () => {
    setHighlightColor("");
    setHighlightError("");
    setIsHighlightModalOpen(true);
  };

  const handleHighlightSelect = (color) => {
    if (color === null) {
      // Remove highlighting
      editor.chain().focus().unsetHighlight().run();
    } else {
      // Validate color is from safe palette or is a valid CSS color
      if (isColorInSafePalette(color, "highlight") || sanitizeCssColor(color)) {
        // Apply highlighting with the selected color
        editor.chain().focus().setHighlight({ color: color }).run();
      } else {
        console.warn("Invalid highlight color provided:", color);
        return; // Don't close modal if color is invalid
      }
    }
    setIsHighlightModalOpen(false);
    setHighlightColor("");
  };

  const handleHighlightSubmit = () => {
    const sanitizedColor = sanitizeCssColor(highlightColor);
    if (sanitizedColor) {
      editor.chain().focus().setHighlight({ color: sanitizedColor }).run();
      setIsHighlightModalOpen(false);
      setHighlightColor("");
      setHighlightError("");
    } else {
      setHighlightError(t("text-edit.errors.invalid-color"));
    }
  };

  return (
    <div className={`border rounded-lg flex flex-col ${className}`}>
      {/* Toolbar */}
      <div className="border-b p-2 flex flex-wrap gap-1 bg-white flex-shrink-0 sticky top-0 z-10">
        {/* Text Formatting */}
        <div className="flex gap-1 border-r pr-2 mr-2">
          <SimpleTooltip content={t("text-edit.toolbar.bold")}>
            <Button
              type="button"
              variant={editor.isActive("bold") ? "default" : "ghost"}
              size="sm"
              onClick={() => editor.chain().focus().toggleBold().run()}
              className="p-1 h-8 w-8"
              aria-label="Bold"
            >
              <MdFormatBold size={16} />
            </Button>
          </SimpleTooltip>
          <SimpleTooltip content={t("text-edit.toolbar.italic")}>
            <Button
              type="button"
              variant={editor.isActive("italic") ? "default" : "ghost"}
              size="sm"
              onClick={() => editor.chain().focus().toggleItalic().run()}
              className="p-1 h-8 w-8"
              aria-label="Italic"
            >
              <MdFormatItalic size={16} />
            </Button>
          </SimpleTooltip>
          <SimpleTooltip content={t("text-edit.toolbar.underline")}>
            <Button
              type="button"
              variant={editor.isActive("underline") ? "default" : "ghost"}
              size="sm"
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              className="p-1 h-8 w-8"
              aria-label="Underline"
            >
              <MdFormatUnderlined size={16} />
            </Button>
          </SimpleTooltip>
          <SimpleTooltip content={t("text-edit.toolbar.strikethrough")}>
            <Button
              type="button"
              variant={editor.isActive("strike") ? "default" : "ghost"}
              size="sm"
              onClick={() => editor.chain().focus().toggleStrike().run()}
              className="p-1 h-8 w-8"
              aria-label="Strikethrough"
            >
              <MdFormatStrikethrough size={16} />
            </Button>
          </SimpleTooltip>
          {/*
          <SimpleTooltip content={t("text-edit.toolbar.code")}>
            <Button
              type="button"
              variant={editor.isActive("code") ? "default" : "ghost"}
              size="sm"
              onClick={() => editor.chain().focus().toggleCode().run()}
              className="p-1 h-8 w-8"
              aria-label="Code"
            >
              <MdCode size={16} />
            </Button>
          </SimpleTooltip>
          */}
        </div>

        {/* Headings */}
        <div className="flex gap-1 border-r pr-2 mr-2">
          <SimpleTooltip content={t("text-edit.toolbar.heading-1")}>
            <Button
              type="button"
              variant={
                editor.isActive("heading", { level: 1 }) ? "default" : "ghost"
              }
              size="sm"
              onClick={() =>
                editor.chain().focus().toggleHeading({ level: 1 }).run()
              }
              className="p-1 h-8 w-8"
              aria-label="Heading 1"
            >
              <LuHeading1 size={16} />
            </Button>
          </SimpleTooltip>
          <SimpleTooltip content={t("text-edit.toolbar.heading-2")}>
            <Button
              type="button"
              variant={
                editor.isActive("heading", { level: 2 }) ? "default" : "ghost"
              }
              size="sm"
              onClick={() =>
                editor.chain().focus().toggleHeading({ level: 2 }).run()
              }
              className="p-1 h-8 w-8"
              aria-label="Heading 2"
            >
              <LuHeading2 size={16} />
            </Button>
          </SimpleTooltip>
          <SimpleTooltip content={t("text-edit.toolbar.heading-3")}>
            <Button
              type="button"
              variant={
                editor.isActive("heading", { level: 3 }) ? "default" : "ghost"
              }
              size="sm"
              onClick={() =>
                editor.chain().focus().toggleHeading({ level: 3 }).run()
              }
              className="p-1 h-8 w-8"
              aria-label="Heading 3"
            >
              <LuHeading3 size={16} />
            </Button>
          </SimpleTooltip>
        </div>

        {/* Lists */}
        <div className="flex gap-1 border-r pr-2 mr-2">
          <SimpleTooltip content={t("text-edit.toolbar.bullet-list")}>
            <Button
              type="button"
              variant={editor.isActive("bulletList") ? "default" : "ghost"}
              size="sm"
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              className="p-1 h-8 w-8"
              aria-label="Bullet List"
            >
              <MdFormatListBulleted size={16} />
            </Button>
          </SimpleTooltip>
          <SimpleTooltip content={t("text-edit.toolbar.numbered-list")}>
            <Button
              type="button"
              variant={editor.isActive("orderedList") ? "default" : "ghost"}
              size="sm"
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              className="p-1 h-8 w-8"
              aria-label="Numbered List"
            >
              <MdFormatListNumbered size={16} />
            </Button>
          </SimpleTooltip>
          <SimpleTooltip content={t("text-edit.toolbar.blockquote")}>
            <Button
              type="button"
              variant={editor.isActive("blockquote") ? "default" : "ghost"}
              size="sm"
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              className="p-1 h-8 w-8"
              aria-label="Blockquote"
            >
              <MdFormatQuote size={16} />
            </Button>
          </SimpleTooltip>
        </div>

        {/* Links and Tables */}
        {/*
        <div className="flex gap-1 border-r pr-2 mr-2">
          <SimpleTooltip
            content={
              editor.isActive("link")
                ? t("text-edit.toolbar.remove-link")
                : t("text-edit.toolbar.add-link")
            }
          >
            <Button
              type="button"
              variant={editor.isActive("link") ? "default" : "ghost"}
              size="sm"
              onClick={editor.isActive("link") ? removeLink : addLink}
              className="p-1 h-8 w-8"
              aria-label={editor.isActive("link") ? "Remove Link" : "Add Link"}
            >
              <MdLink size={16} />
            </Button>
          </SimpleTooltip>
          <SimpleTooltip content={t("text-edit.toolbar.insert-table")}>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={addTable}
              className="p-1 h-8 w-8"
              aria-label="Insert Table"
            >
              <MdTableChart size={16} />
            </Button>
          </SimpleTooltip>
        </div>
        */}

        {/* Colors */}
        <div className="flex gap-1 border-r pr-2 mr-2">
          <div className="relative">
            <SimpleTooltip content={t("text-edit.toolbar.text-color")}>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="p-1 h-8 w-8"
                onClick={openColorModal}
                aria-label="Text Color"
              >
                <MdPalette size={16} />
              </Button>
            </SimpleTooltip>
          </div>
          <div className="relative">
            <SimpleTooltip content={t("text-edit.toolbar.highlight")}>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="p-1 h-8 w-8"
                onClick={openHighlightModal}
                aria-label="Highlight Color"
              >
                <MdHighlight size={16} />
              </Button>
            </SimpleTooltip>
          </div>
        </div>

        {/* Undo/Redo */}
        <div className="flex gap-1">
          <SimpleTooltip content={t("text-edit.toolbar.undo")}>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
              className="p-1 h-8 w-8"
              aria-label="Undo"
            >
              <MdUndo size={16} />
            </Button>
          </SimpleTooltip>
          <SimpleTooltip content={t("text-edit.toolbar.redo")}>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
              className="p-1 h-8 w-8"
              aria-label="Redo"
            >
              <MdRedo size={16} />
            </Button>
          </SimpleTooltip>
        </div>
      </div>

      {/* Editor Content */}
      <div className="p-4 flex-1 overflow-y-auto">
        <style>
          {`
            .tiptap-editor .ProseMirror {
              outline: none !important;
              min-height: 200px;
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
              white-space: pre-wrap;
            }
            .tiptap-editor .ProseMirror p {
              white-space: pre-line;
              margin-bottom: 1em;
            }
            .tiptap-editor .ProseMirror p:empty {
              margin-bottom: 1em;
              min-height: 1.2em;
            }
            .tiptap-editor .ProseMirror p:empty::before {
              content: "";
              display: inline-block;
              width: 0;
            }
            .tiptap-editor .ProseMirror h1 {
              font-size: 2em;
              font-weight: 700;
              margin-top: 1em;
              margin-bottom: 0.5em;
              line-height: 1.2;
            }
            .tiptap-editor .ProseMirror h2 {
              font-size: 1.5em;
              font-weight: 700;
              margin-top: 1em;
              margin-bottom: 0.5em;
              line-height: 1.3;
            }
            .tiptap-editor .ProseMirror h3 {
              font-size: 1.25em;
              font-weight: 700;
              margin-top: 1em;
              margin-bottom: 0.5em;
              line-height: 1.4;
            }
            .tiptap-editor .ProseMirror strong {
              font-weight: 700 !important;
              font-style: normal !important;
              font-synthesis: none !important;
            }
            .tiptap-editor .ProseMirror em {
              font-style: italic !important;
              font-weight: 400 !important;
              font-synthesis: none !important;
            }
            .tiptap-editor .ProseMirror strong em,
            .tiptap-editor .ProseMirror em strong {
              font-weight: 700 !important;
              font-style: italic !important;
              font-synthesis: none !important;
            }
            .tiptap-editor .ProseMirror u {
              text-decoration: underline;
            }
            .tiptap-editor .ProseMirror s,
            .tiptap-editor .ProseMirror del {
              text-decoration: line-through;
            }
            .tiptap-editor .ProseMirror ul,
            .tiptap-editor .ProseMirror ol {
              margin-top: 1em;
              margin-bottom: 1em;
              padding-left: 2em;
            }
            .tiptap-editor .ProseMirror li {
              margin-bottom: 0.25em;
            }
            .tiptap-editor .ProseMirror blockquote {
              border-left: 4px solid #e5e7eb;
              padding-left: 1em;
              margin-top: 1em;
              margin-bottom: 1em;
              font-style: italic;
              color: #6b7280;
            }
            .dark .tiptap-editor .ProseMirror blockquote {
              border-left-color: #4b5563;
              color: #9ca3af;
            }
          `}
        </style>
        <EditorContent
          editor={editor}
          className="focus:outline-none tiptap-editor"
          style={{
            minHeight: "200px",
          }}
        />
      </div>

      {/* Link Modal */}
      <Modal
        isOpen={isLinkModalOpen}
        onClose={() => {
          setIsLinkModalOpen(false);
          setLinkError("");
        }}
        title={t("text-edit.modals.link.title")}
        footer={
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => {
                setIsLinkModalOpen(false);
                setLinkError("");
              }}
            >
              {t("text-edit.modals.link.cancel")}
            </Button>
            <Button onClick={handleLinkSubmit} disabled={!linkUrl.trim()}>
              {t("text-edit.modals.link.add")}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <div>
            <Label htmlFor="link-url">
              {t("text-edit.modals.link.url-label")}
            </Label>
            <Input
              id="link-url"
              type="url"
              value={linkUrl}
              onChange={(e) => {
                setLinkUrl(e.target.value);
                if (linkError) setLinkError(""); // Clear error when user types
              }}
              placeholder={t("text-edit.modals.link.url-placeholder")}
              onKeyDown={(e) => {
                if (e.key === "Enter" && linkUrl.trim()) {
                  handleLinkSubmit();
                }
              }}
              className={linkError ? "border-red-500" : ""}
            />
            {linkError && (
              <p className="text-red-500 text-sm mt-1">{linkError}</p>
            )}
          </div>
        </div>
      </Modal>

      {/* Text Color Modal */}
      <Modal
        isOpen={isColorModalOpen}
        onClose={() => {
          setIsColorModalOpen(false);
          setColorError("");
        }}
        title={t("text-edit.modals.text-color.title")}
        footer={
          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={() => {
                setIsColorModalOpen(false);
                setColorError("");
              }}
            >
              {t("text-edit.modals.text-color.cancel")}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          {/* Color Palette */}
          <div>
            <Label className="text-sm font-medium mb-3 block">
              {t("text-edit.modals.text-color.color-label")}
            </Label>
            <div className="grid grid-cols-7 gap-2">
              {colorPalette.map((color) => (
                <button
                  key={color.value}
                  type="button"
                  className="w-8 h-8 rounded border-2 border-gray-200 hover:border-gray-400 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                  style={{ backgroundColor: color.value }}
                  onClick={() => handleColorSelect(color.value)}
                  title={t(color.nameKey)}
                  aria-label={`${t("text-edit.colors.select")} ${t(color.nameKey)}`}
                />
              ))}
            </div>
          </div>

          {/* Custom Color Input */}
          <div>
            <Label htmlFor="custom-text-color">
              {t("text-edit.modals.text-color.custom-color")}
            </Label>
            <div className="flex gap-2">
              <Input
                id="custom-text-color"
                type="text"
                value={textColor}
                onChange={(e) => {
                  setTextColor(e.target.value);
                  if (colorError) setColorError(""); // Clear error when user types
                }}
                placeholder={t("text-edit.modals.text-color.color-placeholder")}
                className={colorError ? "border-red-500" : ""}
              />
              <Button onClick={handleColorSubmit} disabled={!textColor.trim()}>
                {t("text-edit.modals.text-color.apply")}
              </Button>
            </div>
            {colorError && (
              <p className="text-red-500 text-sm mt-1">{colorError}</p>
            )}
            <p className="text-gray-500 text-xs mt-1">
              {t("text-edit.modals.text-color.color-help")}
            </p>
          </div>
        </div>
      </Modal>

      {/* Highlight Color Modal */}
      <Modal
        isOpen={isHighlightModalOpen}
        onClose={() => {
          setIsHighlightModalOpen(false);
          setHighlightError("");
        }}
        title={t("text-edit.modals.highlight.title")}
        footer={
          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={() => {
                setIsHighlightModalOpen(false);
                setHighlightError("");
              }}
            >
              {t("text-edit.modals.highlight.cancel")}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          {/* Highlight Color Palette */}
          <div>
            <Label className="text-sm font-medium mb-3 block">
              {t("text-edit.modals.highlight.color-label")}
            </Label>
            <div className="grid grid-cols-3 gap-2">
              {highlightPalette.map((color) => (
                <button
                  key={color.value || "no-highlight"}
                  type="button"
                  className={`w-16 h-8 rounded border-2 border-gray-200 hover:border-gray-400 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center text-xs font-medium ${
                    color.value === null
                      ? "text-gray-700 bg-white border-dashed"
                      : "text-gray-700"
                  }`}
                  style={color.value ? { backgroundColor: color.value } : {}}
                  onClick={() => handleHighlightSelect(color.value)}
                  title={t(color.nameKey)}
                  aria-label={`${t("text-edit.colors.select")} ${t(color.nameKey)} ${color.value ? t("text-edit.colors.highlight") : ""}`}
                >
                  {color.value === null ? "✕" : t(color.nameKey)}
                </button>
              ))}
            </div>
          </div>

          {/* Custom Highlight Color Input */}
          <div>
            <Label htmlFor="custom-highlight-color">
              {t("text-edit.modals.highlight.custom-color")}
            </Label>
            <div className="flex gap-2">
              <Input
                id="custom-highlight-color"
                type="text"
                value={highlightColor}
                onChange={(e) => {
                  setHighlightColor(e.target.value);
                  if (highlightError) setHighlightError(""); // Clear error when user types
                }}
                placeholder={t("text-edit.modals.highlight.color-placeholder")}
                className={highlightError ? "border-red-500" : ""}
              />
              <Button
                onClick={handleHighlightSubmit}
                disabled={!highlightColor.trim()}
              >
                {t("text-edit.modals.highlight.apply")}
              </Button>
            </div>
            {highlightError && (
              <p className="text-red-500 text-sm mt-1">{highlightError}</p>
            )}
            <p className="text-gray-500 text-xs mt-1">
              {t("text-edit.modals.highlight.color-help")}
            </p>
          </div>
        </div>
      </Modal>
    </div>
  );
});

export default TipTapEditor;
