const { GeminiLLM } = require("../utils/AiProviders/gemini");
const { MODEL_MAP } = require("../utils/AiProviders/modelMap");

describe("GeminiLLM Model Configuration", () => {
  beforeAll(() => {
    // Provide a dummy API key for tests to allow instantiation
    process.env.GEMINI_API_KEY = "test-key";
  });

  const geminiModels = Object.keys(MODEL_MAP.gemini.models);
  if (geminiModels.length === 0) {
    test("no gemini models to test", () => {
      expect(geminiModels).toBeInstanceOf(Array);
    });
    return;
  }

  test.each(geminiModels)(
    "should correctly resolve prompt window size for model %s",
    (modelName) => {
      const expectedLimit = MODEL_MAP.gemini.models[modelName].context;
      const llm = new GeminiLLM(null, modelName);
      expect(llm.promptWindowLimit()).toBe(expectedLimit);
    }
  );

  test("should use default prompt window size for an unknown model", () => {
    const unknownModelName = "unknown-gemini-model";
    const llm = new GeminiLLM(null, unknownModelName);
    const defaultLimit = MODEL_MAP.gemini.defaults.contextWindow;
    expect(llm.promptWindowLimit()).toBe(defaultLimit);
  });

  test("static promptWindowLimit should resolve known model", () => {
    const modelName = geminiModels[0];
    const expectedLimit = MODEL_MAP.gemini.models[modelName].context;
    expect(GeminiLLM.promptWindowLimit(modelName)).toBe(expectedLimit);
  });

  test("static promptWindowLimit should fallback for unknown model", () => {
    const unknownModelName = "unknown-gemini-model";
    const defaultLimit = MODEL_MAP.gemini.defaults.contextWindow;
    expect(GeminiLLM.promptWindowLimit(unknownModelName)).toBe(defaultLimit);
  });
});
