import React, { useEffect } from "react";
import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import {
  FileCode,
  FileCsv,
  FileDoc,
  FileHtml,
  FileText,
  FileImage,
  FilePdf,
} from "@phosphor-icons/react";
import { PiTrashDuotone } from "react-icons/pi";
import {
  useThreadAttachments,
  useRemoveAttachment,
} from "@/stores/attachmentStore";
import { cleanupAttachmentApi } from "@/services/attachmentService";
import { useIsDocumentDrafting } from "@/stores/userStore";
import useUser from "@/hooks/useUser";
import { useTokenManagement } from "@/hooks/useTokenManagement";
import showToast from "@/utils/toast";

export default function UploadedFilesModal({ isOpen, onClose, workspace }) {
  const { t } = useTranslation();
  const { threadSlug: threadId } = useParams();
  const attachments = useThreadAttachments(threadId);
  const { user } = useUser();
  const removeAttachment = useRemoveAttachment();
  const isDocumentDrafting = useIsDocumentDrafting();
  const { contextLimits, updateTokenLimits } = useTokenManagement(
    workspace,
    user,
    attachments
  );

  useEffect(() => {
    if (isOpen) {
      updateTokenLimits();
    }
  }, [isOpen, updateTokenLimits]);

  const handleRemoveFile = async (uid) => {
    const attachmentToRemove = attachments.find((a) => a.uid === uid);
    let hasErrors = false;

    if (attachmentToRemove?.document?.location) {
      try {
        const result = await cleanupAttachmentApi(
          attachmentToRemove.document.location
        );
        if (!result.success) {
          hasErrors = true;
          console.error("Failed to cleanup attachment:", result.error);
          showToast(t("show-toast.file-remove-error"), "error");
        }
      } catch (error) {
        hasErrors = true;
        console.error("Error cleaning up attachment:", error);
        showToast(t("show-toast.file-remove-error"), "error");
      }
    } else {
      console.warn(
        "No document location found for attachment, skipping cleanup.",
        attachmentToRemove
      );
    }

    removeAttachment(threadId, uid);

    if (!hasErrors) {
      showToast(t("show-toast.file-removed"), "success");
    }

    if (attachments.length === 1) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const totalTokens = attachments.reduce(
    (sum, file) => sum + (file.tokenCount || 0),
    0
  );

  const modalTitle = (
    <div className="flex items-center gap-3">
      <span>{t("workspace-chats.attached-files", "Attached Files")}</span>
      <span className="text-sm text-muted-foreground font-medium">
        ({t("workspace-chats.total-tokens", "Total tokens")}:
        {` ${totalTokens.toLocaleString()}`})
      </span>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={modalTitle}
      className="w-[450px] max-w-[90vw]"
    >
      {isDocumentDrafting && (
        <div className="mb-4">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>
              {t("workspace-chats.context-window", "Available context window")}:{" "}
              {contextLimits.totalWindow.toLocaleString()}
            </span>
            <span>
              {t("workspace-chats.remaining-tokens", "Remaining")}:{" "}
              {Math.max(0, contextLimits.availableTokens).toLocaleString()}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 my-2">
            <div
              className="h-2 rounded-full transition-all duration-300"
              style={{
                width: `${Math.min((totalTokens / contextLimits.totalWindow) * 100, 100)}%`,
                backgroundColor:
                  totalTokens > contextLimits.totalWindow
                    ? "#ef4444"
                    : "#2563eb",
              }}
            />
          </div>
        </div>
      )}
      <div className="max-h-[60vh] overflow-y-auto overflow-x-hidden">
        {attachments.map((file) => {
          const extension = file.file.type.split("/").pop().toLowerCase();
          const iconMap = {
            pdf: { Icon: FilePdf, bgColor: "bg-magenta" },
            msword: { Icon: FileDoc, bgColor: "bg-royalblue" },
            "vnd.openxmlformats-officedocument.wordprocessingml.document": {
              Icon: FileDoc,
              bgColor: "bg-royalblue",
            },
            html: { Icon: FileHtml, bgColor: "bg-purple" },
            csv: { Icon: FileCsv, bgColor: "bg-success" },
            "vnd.ms-excel": { Icon: FileCsv, bgColor: "bg-success" },
            "vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
              Icon: FileCsv,
              bgColor: "bg-success",
            },
            javascript: { Icon: FileCode, bgColor: "bg-warn" },
            json: { Icon: FileCode, bgColor: "bg-warn" },
            "x-sql": { Icon: FileCode, bgColor: "bg-warn" },
            "x-python": { Icon: FileCode, bgColor: "bg-warn" },
            jpeg: { Icon: FileImage, bgColor: "bg-royalblue" },
            jpg: { Icon: FileImage, bgColor: "bg-royalblue" },
            png: { Icon: FileImage, bgColor: "bg-royalblue" },
            gif: { Icon: FileImage, bgColor: "bg-royalblue" },
            webp: { Icon: FileImage, bgColor: "bg-royalblue" },
          };
          const { Icon, bgColor } = iconMap[extension] || {
            Icon: FileText,
            bgColor: "bg-royalblue",
          };
          return (
            <div
              key={file.uid}
              className="flex items-center justify-between p-2 mb-2 rounded-lg border border-border w-full gap-4"
            >
              <div className="flex items-center flex-1 min-w-0">
                <div className={`${bgColor} rounded-lg p-1 mr-2 opacity-90`}>
                  <Icon size={24} className="text-white" />
                </div>
                <div className="flex-1 min-w-0 max-w-full">
                  <p className="text-sm font-medium text-foreground truncate">
                    {file.file.name}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {t("workspace-chats.token-count", "Token count")}:
                    {` ${file.tokenCount?.toLocaleString()}`}
                  </p>
                </div>
              </div>
              <div>
                <Button
                  size="icon"
                  variant="destructive"
                  onClick={() => handleRemoveFile(file.uid)}
                >
                  <PiTrashDuotone />
                </Button>
              </div>
            </div>
          );
        })}
      </div>
    </Modal>
  );
}
