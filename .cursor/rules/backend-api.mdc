---
description: Guidelines for backend API endpoints
globs: server/**/*.js
alwaysApply: false
---

# Backend API Endpoints

## API Structure
- Place internal endpoints outside `/api/` directory
- Use internal endpoints for LLM requests like prompt upgrade component
- Use `/api/` subdirectories for standard REST API endpoints
- Document APIs using Swagger/OpenAPI

## Endpoint Organization
- `workspaces.js`: Endpoints for workspace management
- `chat.js`: Endpoints for chat functionality
- `system.js`: System-level endpoints (health check, env updates, JWT tokens)
- `document.js`: Endpoints for document management
- `admin.js`: Admin endpoints for system management
- `api/workspace`: Standard REST API for workspaces
- `api/auth`: Authentication endpoints
- `api/document`: Document management endpoints
- `api/system`: System settings endpoints
- `api/openai`: OpenAI-compatible endpoints
- `api/workspaceThread`: Workspace thread endpoints

## Request Validation
- Validate request parameters and body
- Use middleware for common validation tasks
- Return appropriate error responses for invalid requests
- Implement proper authentication and authorization checks

## Response Formatting
- Return consistent response formats
- Use appropriate HTTP status codes
- Include relevant metadata in responses
- Handle errors gracefully

## Streaming Responses
- Use Server-Sent Events (SSE) for streaming responses
- Implement proper error handling for streaming
- Close connections appropriately
- Monitor connection status

## Performance
- Optimize database queries
- Use caching where appropriate
- Implement pagination for large result sets
- Monitor endpoint performance

## Security
- Validate API keys and tokens
- Implement rate limiting
- Protect against common vulnerabilities
- Log security-related events
