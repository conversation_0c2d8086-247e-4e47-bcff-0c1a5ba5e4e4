# Document Builder Prompts Management

The Document Builder feature utilizes a set of configurable prompts for its various processing stages. These prompts can be customized through system settings. This document outlines how these prompts are fetched and stored by the server.

## Overview

The server provides API endpoints to manage custom prompts for the Document Builder. If custom prompts are not set, the system falls back to default prompts.

## Fetching Prompts

Custom and default prompts for the Document Builder are retrieved via the following endpoint:

- **Endpoint**: `GET /system/get-document-builder`
- **Method**: `GET`
- **Description**: Fetches the current effective prompts (customized or default) and the default prompt values themselves. This endpoint is primarily for UI/administrator use.
- **Authentication**: Requires admin/manager privileges.
- **Query Parameters (Optional)**:
  - `flowType` (string): Can be `'mainDoc'` or `'noMainDoc'`. If provided, the `topicsSectionsSystemPrompt`, `topicsSectionsUserPrompt`, and their corresponding default values in the response will be specific to this flow. If omitted, defaults to behavior equivalent to `'noMainDoc'` for these prompts.

### Process (for `/system/get-document-builder`):

1.  The endpoint checks for an optional `flowType` query parameter.
2.  It queries the `system_settings` table (via the `SystemSettings.get()` model method) for each component of the Document Builder prompts.
    - For `topicsSectionsSystemPrompt` and `topicsSectionsUserPrompt`, the `system_settings` label used for lookup (e.g., `cdb_section_list_from_main_system_prompt` vs. `cdb_section_list_from_summaries_system_prompt`) is determined by the `flowType` parameter.
3.  For each prompt component, if a custom value is found in `system_settings` and is valid (not empty or "undefined"), that custom value is used.
4.  If a custom value is not found or is invalid, the system falls back to a predefined default prompt.
    - For `topicsSectionsSystemPrompt` and `topicsSectionsUserPrompt`, the specific default (e.g., `DEFAULT_SECTION_LIST_FROM_MAIN` vs. `DEFAULT_SECTION_LIST_FROM_SUMMARIES`) is also determined by the `flowType`.
5.  **Default Prompts Source**: The default prompt texts are defined as constants in `server/utils/chats/prompts/legalDrafting.js`. This file categorizes prompts (e.g., shared, main document flow, no main document flow). Examples:
    - `DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT`
    - `DEFAULT_DOCUMENT_SUMMARY.USER_PROMPT`
    - `DEFAULT_SECTION_LIST_FROM_MAIN.SYSTEM_PROMPT`
    - `DEFAULT_SECTION_LIST_FROM_SUMMARIES.USER_PROMPT`

### Internal Prompt Resolution (within backend flows):

Backend services, particularly the document drafting flows (`mainDoc.js`, `noMainDoc.js`), now utilize a centralized helper for accessing the effective prompts:

- **Helper**: `getResolvedPrompts` from `server/utils/chats/helpers/promptManager.js`.
- **Mechanism**: This function is responsible for:
  - Fetching all relevant system settings for Document Builder prompts.
  - Loading the default prompt structures from `server/utils/chats/prompts/legalDrafting.js`.
  - Returning an object (e.g., `AllPrompts`) where each key (like `CURRENT_DEFAULT_DOCUMENT_SUMMARY`) provides the effective system and user prompts, having already handled the logic of preferring custom settings over defaults.

### Response Body (for `/system/get-document-builder`):

The endpoint returns a JSON object containing:

- The current effective prompt for each component (custom or default). For `topicsSectionsSystemPrompt` and `topicsSectionsUserPrompt`, these are now flow-specific if `flowType` was provided.
- The default value for each prompt component (useful for UI placeholders). For `defaultTopicsSectionsSystemPrompt` and `defaultTopicsSectionsUserPrompt`, these are also now flow-specific if `flowType` was provided.

**Example Response Snippet (when `flowType` is provided, e.g., `'mainDoc'`):**

```json
{
  "summarySystemPrompt": "Custom summary system prompt if set, otherwise default.",
  "summaryUserPrompt": "Custom summary user prompt if set, otherwise default.",
  "defaultSummarySystemPrompt": "The default system prompt for document summary.",
  "defaultSummaryUserPrompt": "The default user prompt for document summary.",
  "relevanceSystemPrompt": "Custom relevance system prompt...",
  "relevanceUserPrompt": "Custom relevance user prompt...",
  "selectMainDocumentSystemPrompt": "Custom select main document system prompt...",
  "selectMainDocumentUserPrompt": "Custom select main document user prompt...",
  "topicsSectionsSystemPrompt": "Effective system prompt for topics/sections (mainDoc flow).",
  "topicsSectionsUserPrompt": "Effective user prompt for topics/sections (mainDoc flow).",
  "sectionSystemPrompt": "Custom section system prompt...",
  "sectionUserPrompt": "Custom section user prompt...",
  "sectionLegalIssuesSystemPrompt": "Custom section legal issues system prompt...",
  "sectionLegalIssuesUserPrompt": "Custom section legal issues user prompt...",
  "myNewStageSystemPrompt": "Custom my new stage system prompt...",
  "myNewStageUserPrompt": "Custom my new stage user prompt...",
  "defaultMyNewStageSystemPrompt": "The default system prompt for my new stage.",
  "defaultMyNewStageUserPrompt": "The default user prompt for my new stage.",
  "defaultTopicsSectionsSystemPrompt": "Default system prompt for topics/sections (mainDoc flow).",
  "defaultTopicsSectionsUserPrompt": "Default user prompt for topics/sections (mainDoc flow)."
}
```

## Storing Custom Prompts

Custom prompts for the Document Builder are saved using the following endpoint:

- **Endpoint**: `POST /system/set-document-builder`
- **Method**: `POST`
- **Description**: Saves or updates the custom prompts for the Document Builder.
- **Authentication**: Requires admin/manager privileges.

### Process:

1.  The endpoint expects a JSON payload in the request body containing the new prompt strings.
2.  It uses the `SystemSettings._updateSettings()` model method to save each prompt component to the `system_settings` table.
3.  Each prompt component is stored as a key-value pair, where the `label` is the prompt identifier (e.g., `summary_system_prompt`) and `value` is the custom prompt string.
4.  The `SystemSettings` model may apply validation before saving.

### Request Body:

A JSON object where keys correspond to the prompts being updated.

**Example Request Body:**

```json
{
  "summarySystemPrompt": "This is my new custom system prompt for document summaries.",
  "summaryUserPrompt": "This is my new custom user prompt for document summaries.",
  "relevanceSystemPrompt": "Custom relevance system prompt...",
  "relevanceUserPrompt": "Custom relevance user prompt...",
  "selectMainDocumentSystemPrompt": "Custom select main document system prompt...",
  "selectMainDocumentUserPrompt": "Custom select main document user prompt...",
  "topicsSectionsSystemPrompt": "Custom topics sections system prompt...",
  "topicsSectionsUserPrompt": "Custom topics sections user prompt...",
  "sectionSystemPrompt": "Custom section system prompt...",
  "sectionUserPrompt": "Custom section user prompt...",
  "sectionLegalIssuesSystemPrompt": "Custom section legal issues system prompt...",
  "sectionLegalIssuesUserPrompt": "Custom section legal issues user prompt...",
  "myNewStageSystemPrompt": "Custom my new stage system prompt...",
  "myNewStageUserPrompt": "Custom my new stage user prompt..."
}
```

## Managed Prompt Keys

The following prompt components are managed by these endpoints. The keys used in the API request/response (camelCase) map to labels in the `system_settings` table (snake_case):

| API Key / Response Field         | `system_settings` Label                                                                                                                                      | Default Source (from `legalDrafting.js`)                                                                                                                             | Resolved by `promptManager.js` as                                                                                                                        |
| -------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `summarySystemPrompt`            | `summary_system_prompt`                                                                                                                                      | `DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT`                                                                                                                             | `CURRENT_DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT`                                                                                                         |
| `summaryUserPrompt`              | `summary_user_prompt`                                                                                                                                        | `DEFAULT_DOCUMENT_SUMMARY.USER_PROMPT`                                                                                                                               | `CURRENT_DEFAULT_DOCUMENT_SUMMARY.USER_PROMPT`                                                                                                           |
| `relevanceSystemPrompt`          | `relevance_system_prompt`                                                                                                                                    | `DEFAULT_DOCUMENT_RELEVANCE.SYSTEM_PROMPT`                                                                                                                           | `CURRENT_DEFAULT_DOCUMENT_RELEVANCE.SYSTEM_PROMPT`                                                                                                       |
| `relevanceUserPrompt`            | `relevance_user_prompt`                                                                                                                                      | `DEFAULT_DOCUMENT_RELEVANCE.USER_PROMPT`                                                                                                                             | `CURRENT_DEFAULT_DOCUMENT_RELEVANCE.USER_PROMPT`                                                                                                         |
| `selectMainDocumentSystemPrompt` | `select_main_document_system_prompt`                                                                                                                         | `DEFAULT_SELECT_MAIN_DOCUMENT.SYSTEM_PROMPT`                                                                                                                         | `CURRENT_DEFAULT_SELECT_MAIN_DOCUMENT.SYSTEM_PROMPT`                                                                                                     |
| `selectMainDocumentUserPrompt`   | `select_main_document_user_prompt`                                                                                                                           | `DEFAULT_SELECT_MAIN_DOCUMENT.USER_PROMPT`                                                                                                                           | `CURRENT_DEFAULT_SELECT_MAIN_DOCUMENT.USER_PROMPT`                                                                                                       |
| `topicsSectionsSystemPrompt`     | `cdb_section_list_from_main_system_prompt` (if flowType='mainDoc') or `cdb_section_list_from_summaries_system_prompt` (if flowType='noMainDoc' or undefined) | `DEFAULT_SECTION_LIST_FROM_MAIN.SYSTEM_PROMPT` (if flowType='mainDoc') or `DEFAULT_SECTION_LIST_FROM_SUMMARIES.SYSTEM_PROMPT` (if flowType='noMainDoc' or undefined) | `CURRENT_DEFAULT_SECTION_LIST_FROM_MAIN.SYSTEM_PROMPT` or `CURRENT_DEFAULT_SECTION_LIST_FROM_SUMMARIES.SYSTEM_PROMPT` (resolved based on SystemSettings) |
| `topicsSectionsUserPrompt`       | `cdb_section_list_from_main_user_prompt` (if flowType='mainDoc') or `cdb_section_list_from_summaries_user_prompt` (if flowType='noMainDoc' or undefined)     | `DEFAULT_SECTION_LIST_FROM_MAIN.USER_PROMPT` (if flowType='mainDoc') or `DEFAULT_SECTION_LIST_FROM_SUMMARIES.USER_PROMPT` (if flowType='noMainDoc' or undefined)     | `CURRENT_DEFAULT_SECTION_LIST_FROM_MAIN.USER_PROMPT` or `CURRENT_DEFAULT_SECTION_LIST_FROM_SUMMARIES.USER_PROMPT` (resolved based on SystemSettings)     |
| `sectionSystemPrompt`            | `section_system_prompt`                                                                                                                                      | `DEFAULT_SECTION_DRAFTING.SYSTEM_PROMPT`                                                                                                                             | `CURRENT_DEFAULT_SECTION_DRAFTING.SYSTEM_PROMPT`                                                                                                         |
| `sectionUserPrompt`              | `section_user_prompt`                                                                                                                                        | `DEFAULT_SECTION_DRAFTING.USER_PROMPT`                                                                                                                               | `CURRENT_DEFAULT_SECTION_DRAFTING.USER_PROMPT`                                                                                                           |
| `sectionLegalIssuesSystemPrompt` | `section_legal_issues_system_prompt`                                                                                                                         | `DEFAULT_SECTION_LEGAL_ISSUES.SYSTEM_PROMPT`                                                                                                                         | `CURRENT_DEFAULT_SECTION_LEGAL_ISSUES.SYSTEM_PROMPT`                                                                                                     |
| `sectionLegalIssuesUserPrompt`   | `section_legal_issues_user_prompt`                                                                                                                           | `DEFAULT_SECTION_LEGAL_ISSUES.USER_PROMPT`                                                                                                                           | `CURRENT_DEFAULT_SECTION_LEGAL_ISSUES.USER_PROMPT`                                                                                                       |
| `myNewStageSystemPrompt`         | `my_new_stage_system_prompt`                                                                                                                                 | `MY_NEW_STAGE_PROMPT.SYSTEM_PROMPT`                                                                                                                                  | `CURRENT_MY_NEW_STAGE_PROMPT.SYSTEM_PROMPT`                                                                                                              |
| `myNewStageUserPrompt`           | `my_new_stage_user_prompt`                                                                                                                                   | `MY_NEW_STAGE_PROMPT.USER_PROMPT`                                                                                                                                    | `CURRENT_MY_NEW_STAGE_PROMPT.USER_PROMPT`                                                                                                                |
| `defaultMyNewStageSystemPrompt`  | `my_new_stage_system_prompt`                                                                                                                                 | `MY_NEW_STAGE_PROMPT.SYSTEM_PROMPT`                                                                                                                                  | `CURRENT_MY_NEW_STAGE_PROMPT.SYSTEM_PROMPT`                                                                                                              |
| `defaultMyNewStageUserPrompt`    | `my_new_stage_user_prompt`                                                                                                                                   | `MY_NEW_STAGE_PROMPT.USER_PROMPT`                                                                                                                                    | `CURRENT_MY_NEW_STAGE_PROMPT.USER_PROMPT`                                                                                                                |

_Note on `topicsSectionsSystemPrompt` and `topicsSectionsUserPrompt` defaults: The actual default applied by the system is determined by the active flow (`mainDoc.js` or `noMainDoc.js`) which uses either `DEFAULT_SECTION_LIST_FROM_MAIN` or `DEFAULT_SECTION_LIST_FROM_SUMMARIES` from `legalDrafting.js` respectively. The API endpoint `/system/get-document-builder` now directly reflects this flow-specific resolution if the `flowType` query parameter is provided. The `promptManager.js` resolves all available custom prompts based on their specific `system_settings` PKeys (e.g., `cdb_section_list_from_main_system_prompt`). The settings page in the UI might need adjustments if admins are to configure these flow-specific topic/section prompts independently through distinct controls; currently, it calls the endpoint without `flowType`, likely affecting the "noMainDoc" or generic settings._

## Adding New Customizable Prompts

To extend the Document Builder with new customizable prompts, follow these backend development steps. This ensures that new prompts are correctly integrated into the system, allowing them to be fetched, displayed, and updated via the Document Builder settings page.

### 1. Define Default Prompts

The first step is to define the default text for your new prompt(s).

- **Location**: `server/utils/chats/prompts/legalDrafting.js`
- **Action**:
  - Define a new constant object for your prompt (e.g., `MY_NEW_STAGE_PROMPT`). If it's flow-specific, consider naming it accordingly (e.g., `MY_NEW_STAGE_MAIN_DOC_PROMPT`). This object should typically contain `SYSTEM_PROMPT` and `USER_PROMPT` keys.
  - Ensure this new constant is exported from the module.
- **Example**:

  ```javascript
  // In server/utils/chats/prompts/legalDrafting.js
  const MY_NEW_STAGE_PROMPT = {
    SYSTEM_PROMPT: "Default system prompt for my new stage.",
    USER_PROMPT:
      "Default user prompt for my new stage related to {{example_variable}}.",
  };

  // Add to module.exports
  module.exports = {
    // ... existing exports
    DEFAULT_DOCUMENT_SUMMARY,
    DEFAULT_DOCUMENT_RELEVANCE,
    // ...
    MY_NEW_STAGE_PROMPT, // Add your new prompt object
  };
  ```

### 2. Update SystemSettings Model

Next, register the database keys for your new prompt(s) in the `SystemSettings` model. This allows them to be stored and retrieved from the database.

- **Location**: `server/models/systemSettings.js`
- **Action**:
  - Add the new database setting key(s) (e.g., `my_new_stage_system_prompt`, `my_new_stage_user_prompt`) to the `protectedFields` array. This array lists settings that are typically managed via specific UI sections like the Document Builder.
- **Example**:
  ```javascript
  // In server/models/systemSettings.js
  const SystemSettings = {
    protectedFields: [
      // ... existing fields like "summary_system_prompt", "relevance_user_prompt", etc.
      "my_new_stage_system_prompt", // Add new snake_case key for system prompt
      "my_new_stage_user_prompt", // Add new snake_case key for user prompt
    ],
    // ... rest of the model
  };
  ```

### 3. Update API Endpoints and Prompt Manager

Modify the API endpoints in `server/endpoints/system.js` and the logic within `server/utils/chats/helpers/promptManager.js` to handle the new prompt(s).

#### a. Update `promptManager.js` (`getResolvedPrompts` function)

This is the new central place for resolving effective prompts.

- **Location**: `server/utils/chats/helpers/promptManager.js`
- **Action**:
  1.  **Fetch New Setting**: Ensure `getResolvedPrompts` fetches the new system setting(s) for your prompt (e.g., `my_new_stage_system_prompt`).
  2.  **Incorporate Default**: Modify the logic to use your new default prompt from `legalDrafting.js` (e.g., `MY_NEW_STAGE_PROMPT.SYSTEM_PROMPT`) if the custom setting is not found or invalid.
  3.  **Expose Resolved Prompt**: Add a new key to the object returned by `getResolvedPrompts` that will provide access to the resolved system and user prompts for your new stage (e.g., `CURRENT_MY_NEW_STAGE_PROMPT: { SYSTEM_PROMPT: effectiveSystemPrompt, USER_PROMPT: effectiveUserPrompt }`).

#### b. `GET /system/get-document-builder` Endpoint (in `server/endpoints/system.js`)

This endpoint fetches all Document Builder prompts for the UI.

- **Action**:
  1.  **Import Default**: At the top of `server/endpoints/system.js`, ensure your new default prompt constant (e.g., `MY_NEW_STAGE_PROMPT`) is imported from `legalDrafting.js`.
      ```javascript
      // const { ..., MY_NEW_STAGE_PROMPT } = require("../utils/chats/prompts/legalDrafting.js");
      const DEFAULT_MY_NEW_STAGE_SYSTEM_PROMPT =
        MY_NEW_STAGE_PROMPT.SYSTEM_PROMPT;
      const DEFAULT_MY_NEW_STAGE_USER_PROMPT = MY_NEW_STAGE_PROMPT.USER_PROMPT;
      ```
  2.  **Fetch Setting**: In the `Promise.all([...])` call, add `SystemSettings.get()` for your new prompt label(s) (e.g., `my_new_stage_system_prompt`).
      ```javascript
      // Example addition to Promise.all
      SystemSettings.get({ label: "my_new_stage_system_prompt" }),
      SystemSettings.get({ label: "my_new_stage_user_prompt" }),
      ```
      Remember to add corresponding variables to the destructuring assignment for the `Promise.all` results (e.g., `myNewStageSystemPromptSetting`, `myNewStageUserPromptSetting`).
  3.  **Add to Response**: In the `result` object that forms the JSON response, add entries for your new prompt(s). Use the `sanitize` helper function to pick between the custom setting and the default. Also include the raw default values.
      ```javascript
      // Example additions to the 'result' object
      myNewStageSystemPrompt: sanitize(
        myNewStageSystemPromptSetting,
        DEFAULT_MY_NEW_STAGE_SYSTEM_PROMPT
      ),
      myNewStageUserPrompt: sanitize(
        myNewStageUserPromptSetting,
        DEFAULT_MY_NEW_STAGE_USER_PROMPT
      ),
      defaultMyNewStageSystemPrompt: DEFAULT_MY_NEW_STAGE_SYSTEM_PROMPT,
      defaultMyNewStageUserPrompt: DEFAULT_MY_NEW_STAGE_USER_PROMPT,
      ```

#### c. `POST /system/set-document-builder` Endpoint (in `server/endpoints/system.js`)

This endpoint saves Document Builder prompts.

- **Action**:
  1.  **Destructure from Request**: Add your new prompt key(s) (in camelCase, e.g., `myNewStageSystemPrompt`, `myNewStageUserPrompt`) to the destructuring assignment from `reqBody(request)`.
      ```javascript
      // Example modification to destructuring
      const {
        // ... existing prompt keys
        myNewStageSystemPrompt,
        myNewStageUserPrompt,
      } = reqBody(request);
      ```
  2.  **Update Settings**: Add your new prompt(s) to the object passed to `SystemSettings._updateSettings()`. Ensure you map the camelCase API key to the snake_case database label.
      ```javascript
      // Example additions to the object for _updateSettings
      await SystemSettings._updateSettings({
        // ... existing prompt settings
        my_new_stage_system_prompt: myNewStageSystemPrompt,
        my_new_stage_user_prompt: myNewStageUserPrompt,
      });
      ```

### 4. Update Documentation Table

Finally, document your new prompt(s) in this file.

- **Location**: `server/docs/document_builder_prompts.md` (this current file)
- **Action**: Add a new row (or rows if you added system/user pairs) to the "Managed Prompt Keys" table. Include the API key (camelCase), the `system_settings` label (snake_case), and the source constant from `legalDrafting.js`.

**Example Table Row:**

| API Key / Response Field | `system_settings` Label      | Default Source (from `legalDrafting.js`) |
| ------------------------ | ---------------------------- | ---------------------------------------- |
| `myNewStageSystemPrompt` | `my_new_stage_system_prompt` | `MY_NEW_STAGE_PROMPT.SYSTEM_PROMPT`      |
| `myNewStageUserPrompt`   | `my_new_stage_user_prompt`   | `MY_NEW_STAGE_PROMPT.USER_PROMPT`        |

### 5. Frontend Integration (Guidance)

While this documentation focuses on backend changes, remember that corresponding frontend updates are necessary:

- The Document Builder settings page in the UI will need new input fields for these prompts.
- The frontend logic must send the new prompt keys (e.g., `myNewStageSystemPrompt`) in the payload to `POST /system/set-document-builder`.
- The frontend must correctly process and display the new prompts (including their defaults like `defaultMyNewStageSystemPrompt`) when fetched from `GET /system/get-document-builder`.
  Coordinate with frontend developers to ensure a seamless integration.

## Testing Changes

After modifying `legalDrafting.js` or related system settings for prompts:

1. Restart the server.
2. Navigate to "Admin Settings" -> "Document Builder".
3. Verify that your new/modified prompt groups and individual prompts appear correctly.
4. Check that the labels and descriptions are displayed as expected.
5. Test editing and saving a prompt to ensure `defaultContent` is populated correctly and changes persist.
6. Run the test suite: `cd server && yarn test:watch legalDrafting.test.js` to ensure all `systemSettingName`s are valid.

## Internationalization (i18n) of Prompts

All UI-facing text related to Document Builder prompts (group titles, group descriptions, individual prompt labels, and individual prompt help text/descriptions) **must be internationalized**.

This is achieved by:

1.  **Defining translation keys in `server/utils/chats/prompts/legalDrafting.js`**:

    - For `GROUP_TITLE` and `GROUP_DESCRIPTION` entries in `exportedLegalPrompts`:
      - The `label` field (e.g., `"document-builder.prompts.group.document_summary.title"`) serves as the translation key.
      - The `defaultContent` field provides the default English text for this key.
    - For `SYSTEM_PROMPT`, `USER_PROMPT`, and `PROMPT_TEMPLATE` entries:
      - The `label` field (e.g., `"document-builder.prompts.document-summary-system-label"`) is the translation key for the prompt's title/label shown in the UI. The `defaultContent` of this entry (which is the actual prompt text) should be used as the English translation for this key if the key itself is too generic.
      - The `description` field (e.g., `"document-builder.prompts.document-summary-system-description"`) is the translation key for the UI help text. A descriptive English sentence should be used as its translation.

2.  **Adding keys and translations to frontend locale files**:
    - All translation keys defined as per point 1 (from `label` and `description` fields in `exportedLegalPrompts`) must be added to _all_ frontend locale files.
    - The primary English locale file is `frontend/src/locales/en/common.js`.
    - Corresponding translations must be added to other language files (e.g., `frontend/src/locales/sv/common.js`, `fr/common.js`, etc.).

**Example Workflow for Adding a New Internationalized Prompt Group:**

1.  In `server/utils/chats/prompts/legalDrafting.js`:
    - Define your new `GROUP_TITLE` entry:
      ```javascript
      {
        promptKey: "NEW_FEATURE_PROMPT",
        promptField: "GROUP_TITLE",
        label: "document-builder.prompts.group.new_feature.title", // Translation Key
        defaultContent: "New Feature Prompts" // English text for the key
      }
      ```
    - Define your `GROUP_DESCRIPTION` entry:
      ```javascript
      {
        promptKey: "NEW_FEATURE_PROMPT",
        promptField: "GROUP_DESCRIPTION",
        label: "document-builder.prompts.group.new_feature.description", // Translation Key
        defaultContent: "Configure prompts for the new amazing feature." // English text for the key
      }
      ```
    - Define your actual prompt (e.g., `SYSTEM_PROMPT`):
      ```javascript
      {
        promptKey: "NEW_FEATURE_PROMPT",
        promptField: "SYSTEM_PROMPT",
        label: "document-builder.prompts.new-feature.system-label", // Translation Key for UI Label
        defaultContent: "System prompt text for the new feature...", // Actual prompt text
        systemSettingName: generateSystemSettingPKeyForLegalDrafting("NEW_FEATURE_PROMPT", "SYSTEM_PROMPT"),
        description: "document-builder.prompts.new-feature.system-description" // Translation Key for UI Description
      }
      ```
2.  In `frontend/src/locales/en/common.js` (and all other locale files):
    ```javascript
    "document-builder": {
      "prompts": {
        "group": {
          // ... other groups
          "new_feature": {
            "title": "New Feature Prompts",
            "description": "Configure prompts for the new amazing feature."
          }
        },
        // ... other prompts
        "new-feature.system-label": "New Feature System Prompt Label", // Or use the defaultContent from legalDrafting.js if more suitable
        "new-feature.system-description": "This system prompt configures the AI for the new amazing feature."
      }
    }
    ```

Refer to the comments at the top of `server/utils/chats/prompts/legalDrafting.js` for more detailed in-code guidance.
