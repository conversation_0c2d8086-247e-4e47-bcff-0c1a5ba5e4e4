import { useTranslation } from "react-i18next";

// Helper function to format content with appropriate tags
export const formatContentWithTags = (content, tFunction) => {
  const tagOpen = tFunction(
    "docx-edit.content-examples-tag-open",
    "<CONTENT_EXAMPLES>"
  );
  const tagClose = tFunction(
    "docx-edit.content-examples-tag-close",
    "</CONTENT_EXAMPLES>"
  );
  const infoText = tFunction(
    "docx-edit.content-examples-info",
    "<INFO>This is an example of the content to be produced, from a similar legal task. Note that this example content can be much shorter or longer than the content that shall now be produced.</INFO>"
  );
  return `\n\n${tagOpen}\n${infoText}\n${content}\n${tagClose}`;
};

// Function to auto-resize textareas, adapted from LegalTaskForm
export const autoResizeTextarea = (event, maxHeight = 200) => {
  const textarea = event.target;
  textarea.style.height = "auto";
  const scrollHeight = textarea.scrollHeight;
  const newHeight = Math.min(Math.max(70, scrollHeight), maxHeight);
  textarea.style.height = `${newHeight}px`;
  // Set data attribute based on whether scrolling is needed
  if (scrollHeight > maxHeight) {
    textarea.dataset.needsScroll = "true";
  } else {
    textarea.dataset.needsScroll = "false";
  }
};
