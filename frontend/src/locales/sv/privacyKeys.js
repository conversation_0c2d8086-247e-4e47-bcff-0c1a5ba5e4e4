export default {
  // =========================
  // PRIVACY & DATA-HANDLING
  // =========================
  privacy: {
    title: "Sekretess & Datahantering",
    description:
      "Detta är din konfiguration för hur anslutna tredjepartsleverantörer och vår plattform hanterar dina data.",
    llm: "LLM-val",
    embedding: "Inbäddningspreferens",
    vector: "Vektordatabas",
    anonymous: "Anonym telemetri aktiverad",
    "desc-event": "Alla händelser registreras utan IP-adress och innehåller",
    "desc-id": "ingen identifierande",
    "desc-cont":
      "information, inställningar, chattar eller annan icke-användarinriktad information. Se listan över händelsetaggar på",
    "desc-git": "Gith<PERSON> här",
    "desc-end":
      "Om du väljer att stänga av telemetri ber vi dig överväga att skicka feedback så att vi kan fortsätta förbättra plattformen.",
  },
  // =========================
  // LLM SELECTION PRIVACY
  // =========================
  "llm-selection-privacy": {
    openai: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Dina prompts och dokumenttext som används för svar är synliga för OpenAI",
      ],
    },
    azure: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Din text och inbäddningstext är inte synliga för OpenAI eller Microsoft",
      ],
    },
    anthropic: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Dina prompts och dokumenttext som används för att generera svar är synliga för Anthropic",
      ],
    },
    gemini: {
      description: [
        "Dina chattar är avpersonifierade och kan användas för träning",
        "Dina prompts och dokumenttext som används för att generera svar är synliga för Google",
      ],
    },
    lmstudio: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på servern som kör LMStudio",
      ],
    },
    localai: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på servern som kör LocalAI",
      ],
    },
    ollama: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på maskinen som kör Ollama",
      ],
    },
    native: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på denna instans",
      ],
    },
    togetherai: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Dina prompts och dokumenttext som används för svar är synliga för TogetherAI",
      ],
    },
    mistral: {
      description: [
        "Dina prompts och dokumenttext som används för svar skickas till Mistral",
      ],
    },
    huggingface: {
      description: [
        "Dina prompts och dokumenttext som används för svar skickas till din HuggingFace-hanterade endpoint",
      ],
    },
    perplexity: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Dina prompts och dokumenttext som används för att generera svar är synliga för Perplexity AI",
      ],
    },
    openrouter: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Dina prompts och dokumenttext som används för att generera svar är synliga för OpenRouter",
      ],
    },
    groq: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Dina prompts och dokumenttext som används för svar är synliga för Groq",
      ],
    },
    koboldcpp: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på servern som kör KoboldCPP",
      ],
    },
    textgenwebui: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på servern som kör Oobabooga Text Generation Web UI",
      ],
    },
    "generic-openai": {
      description: [
        "Data delas enligt de villkor och avtal som gäller hos din generiska endpoint-leverantör.",
      ],
    },
    cohere: {
      description: [
        "Data delas enligt villkoren för cohere.com och lokala integritetslagar.",
      ],
    },
    litellm: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på servern som kör LiteLLM",
      ],
    },
  },

  // =========================
  // VECTOR DATABASE PRIVACY
  // =========================
  "vector-db-privacy": {
    chroma: {
      description: [
        "Dina vektorer och dokumenttext lagras på din Chroma-instans",
        "Åtkomst till din instans hanteras av dig",
      ],
    },
    pinecone: {
      description: [
        "Dina vektorer och dokumenttext lagras på Pinecones servrar",
        "Åtkomsten till dina data hanteras av Pinecone",
      ],
    },
    qdrant: {
      description: [
        "Dina vektorer och dokumenttext lagras på din Qdrant-instans (moln eller egenvärd)",
      ],
    },
    weaviate: {
      description: [
        "Dina vektorer och dokumenttext lagras på din Weaviate-instans (moln eller egenvärd)",
      ],
    },
    milvus: {
      description: [
        "Dina vektorer och dokumenttext lagras på din Milvus-instans (moln eller egenvärd)",
      ],
    },
    zilliz: {
      description: [
        "Dina vektorer och dokumenttext lagras på ditt Zilliz-molnkluster.",
      ],
    },
    astra: {
      description: [
        "Dina vektorer och dokumenttext lagras på din molndatabas i AstraDB.",
      ],
    },
    lancedb: {
      description: [
        "Dina vektorer och dokumenttext lagras privat på denna instans.",
      ],
    },
  },

  // =========================
  // EMBEDDING ENGINE PRIVACY
  // =========================
  "embedding-engine-privacy": {
    native: {
      description: ["Din dokumenttext inbäddas privat på denna instans."],
    },
    openai: {
      description: [
        "Din dokumenttext skickas till OpenAI:s servrar",
        "Dina dokument används inte för OpenAIs träningsändamål",
      ],
    },
    azure: {
      description: [
        "Din dokumenttext skickas till din Microsoft Azure-tjänst",
        "Dina dokument används inte för träningsändamål",
      ],
    },
    localai: {
      description: [
        "Din dokumenttext inbäddas privat på servern som kör LocalAI",
      ],
    },
    ollama: {
      description: [
        "Din dokumenttext inbäddas privat på servern som kör Ollama",
      ],
    },
    lmstudio: {
      description: [
        "Din dokumenttext inbäddas privat på servern som kör LMStudio",
      ],
    },
    cohere: {
      description: [
        "Data delas enligt villkoren för cohere.com samt lokala integritetslagar.",
      ],
    },
    voyageai: {
      description: [
        "Data som skickas till Voyage AI:s servrar delas enligt VoyageAI:s villkor.",
      ],
    },
  },
};
