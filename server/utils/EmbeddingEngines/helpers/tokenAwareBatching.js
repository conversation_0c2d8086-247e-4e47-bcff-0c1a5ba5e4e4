const { TokenManager } = require("../../helpers/tiktoken");

/**
 * Token-Aware Batching Helper for Embedding Engines
 *
 * Handles OpenAI's 300k token per request limit by:
 * - Counting tokens in text chunks before creating batches
 * - Applying safety margins to account for API estimation differences
 * - Creating optimally-sized batches that respect token limits
 * - Providing error recovery with automatic retry logic
 */
class TokenAwareBatcher {
  constructor(options = {}) {
    this.maxTokensPerRequest = parseInt(
      process.env.OPENAI_EMBEDDING_MAX_TOKENS_PER_REQUEST || "300000"
    );
    this.safetyMargin = parseFloat(
      process.env.OPENAI_EMBEDDING_SAFETY_MARGIN || "0.75"
    );
    this.maxRetries = parseInt(process.env.OPENAI_EMBEDDING_MAX_RETRIES || "3");
    this.logTokenUsage =
      process.env.OPENAI_EMBEDDING_LOG_TOKEN_USAGE === "true";
    this.logBatchOptimization =
      process.env.OPENAI_EMBEDDING_LOG_BATCH_OPTIMIZATION === "true";

    // Override with provided options
    Object.assign(this, options);

    // Calculate effective token limit with safety margin (after options are applied)
    this.effectiveTokenLimit = Math.floor(
      this.maxTokensPerRequest * this.safetyMargin
    );

    this.tokenManager = new TokenManager();

    if (this.logBatchOptimization) {
      console.log(
        `[TokenAwareBatcher] Initialized with ${this.effectiveTokenLimit} effective token limit (${this.safetyMargin * 100}% of ${this.maxTokensPerRequest})`
      );
    }
  }

  /**
   * Create token-aware batches from text chunks
   * @param {string[]} textChunks - Array of text chunks to batch
   * @param {number} maxConcurrentChunks - Maximum items per batch (fallback limit)
   * @returns {string[][]} Array of batches, each respecting token limits
   */
  createTokenAwareBatches(textChunks, maxConcurrentChunks = 500) {
    if (!Array.isArray(textChunks) || textChunks.length === 0) {
      return [];
    }

    const batches = [];
    let currentBatch = [];
    let currentBatchTokens = 0;

    // Pre-calculate token counts for all chunks
    const chunksWithTokens = textChunks.map((chunk) => ({
      text: chunk,
      tokens: this.tokenManager.countFromString(chunk),
    }));

    if (this.logTokenUsage) {
      const totalTokens = chunksWithTokens.reduce(
        (sum, item) => sum + item.tokens,
        0
      );
      console.log(
        `[TokenAwareBatcher] Processing ${textChunks.length} chunks with ${totalTokens} total tokens`
      );
    }

    for (const item of chunksWithTokens) {
      // Check if adding this chunk would exceed token limit or item limit
      const wouldExceedTokens =
        currentBatchTokens + item.tokens > this.effectiveTokenLimit;
      const wouldExceedItems = currentBatch.length >= maxConcurrentChunks;

      if ((wouldExceedTokens || wouldExceedItems) && currentBatch.length > 0) {
        // Finalize current batch
        batches.push(currentBatch.map((batchItem) => batchItem.text));

        if (this.logBatchOptimization) {
          console.log(
            `[TokenAwareBatcher] Created batch ${batches.length} with ${currentBatch.length} items and ${currentBatchTokens} tokens`
          );
        }

        // Start new batch
        currentBatch = [];
        currentBatchTokens = 0;
      }

      // Handle single chunks that exceed token limit
      if (item.tokens > this.effectiveTokenLimit) {
        console.warn(
          `[TokenAwareBatcher] Single chunk exceeds token limit (${item.tokens} > ${this.effectiveTokenLimit}). Processing individually.`
        );

        // Process oversized chunk in its own batch
        batches.push([item.text]);

        if (this.logBatchOptimization) {
          console.log(
            `[TokenAwareBatcher] Created oversized batch ${batches.length} with 1 item and ${item.tokens} tokens`
          );
        }
        continue;
      }

      // Add chunk to current batch
      currentBatch.push(item);
      currentBatchTokens += item.tokens;
    }

    // Add final batch if it has content
    if (currentBatch.length > 0) {
      batches.push(currentBatch.map((item) => item.text));

      if (this.logBatchOptimization) {
        console.log(
          `[TokenAwareBatcher] Created final batch ${batches.length} with ${currentBatch.length} items and ${currentBatchTokens} tokens`
        );
      }
    }

    if (this.logBatchOptimization) {
      console.log(
        `[TokenAwareBatcher] Created ${batches.length} total batches from ${textChunks.length} chunks`
      );
    }

    return batches;
  }

  /**
   * Check if an error is a token limit error that should trigger retry
   * @param {Error} error - Error from embedding API
   * @returns {boolean} True if this is a token limit error
   */
  isTokenLimitError(error) {
    if (!error) return false;

    const errorMessage = error.message || "";
    const errorType = error.type || "";

    return (
      errorType === "max_tokens_per_request" ||
      errorMessage.includes("max 300000 tokens per request") ||
      (errorMessage.includes("Requested") &&
        errorMessage.includes("tokens, max") &&
        errorMessage.includes("per request"))
    );
  }

  /**
   * Create a reduced batch for retry after token limit error
   * @param {string[]} originalBatch - The original batch that failed
   * @param {number} retryAttempt - Current retry attempt (1-based)
   * @returns {string[][]} Array of smaller batches for retry
   */
  createRetryBatches(originalBatch, retryAttempt) {
    if (!Array.isArray(originalBatch) || originalBatch.length === 0) {
      return [];
    }

    // Reduce batch size exponentially with each retry
    const reductionFactor = Math.pow(0.5, retryAttempt);
    const newTokenLimit = Math.floor(
      this.effectiveTokenLimit * reductionFactor
    );

    console.warn(
      `[TokenAwareBatcher] Retry attempt ${retryAttempt}: reducing token limit to ${newTokenLimit} (${reductionFactor * 100}% of original)`
    );

    // Temporarily reduce our effective limit for retry
    const originalLimit = this.effectiveTokenLimit;
    this.effectiveTokenLimit = newTokenLimit;

    try {
      // Create new batches with reduced limit
      const retryBatches = this.createTokenAwareBatches(
        originalBatch,
        Math.max(1, Math.floor(originalBatch.length * reductionFactor))
      );
      return retryBatches;
    } finally {
      // Restore original limit
      this.effectiveTokenLimit = originalLimit;
    }
  }

  /**
   * Log token count discrepancy between local count and API count
   * @param {number} localTokenCount - Token count calculated locally
   * @param {number} apiTokenCount - Token count reported by API
   * @param {number} batchSize - Number of items in the batch
   */
  logTokenDiscrepancy(localTokenCount, apiTokenCount, batchSize) {
    if (this.logTokenUsage && apiTokenCount && localTokenCount) {
      const discrepancy = (
        ((apiTokenCount - localTokenCount) / localTokenCount) *
        100
      ).toFixed(1);
      console.log(
        `[TokenAwareBatcher] Token count discrepancy: Local=${localTokenCount}, API=${apiTokenCount}, Diff=${discrepancy}%, BatchSize=${batchSize}`
      );
    }
  }

  /**
   * Get current configuration for logging/debugging
   * @returns {Object} Current configuration object
   */
  getConfig() {
    return {
      maxTokensPerRequest: this.maxTokensPerRequest,
      safetyMargin: this.safetyMargin,
      effectiveTokenLimit: this.effectiveTokenLimit,
      maxRetries: this.maxRetries,
      logTokenUsage: this.logTokenUsage,
      logBatchOptimization: this.logBatchOptimization,
    };
  }
}

module.exports = {
  TokenAwareBatcher,
};
