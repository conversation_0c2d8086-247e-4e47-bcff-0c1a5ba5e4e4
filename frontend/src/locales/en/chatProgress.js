export default {
  // =========================
  // PROGRESS
  // =========================
  chatProgress: {
    title: "Processing",
    processing: "Processing...",
    step: "Step",
    timeLeft: "Time left",
    details: "Details",
    abort: "Abort",
    modalTitle: "Progress Details",
    "close-msg": "Are you sure you want to cancel the process?",
    noThreadSelected: "No thread selected",
    noActiveProgress: "No active progress",
    of: "of",
    started: "Started",
    error: {
      title: "There was an error processing your request.",
      description:
        "Please try again or contact support if the problem persists.",
      retry: "Retry",
      dismiss: "Dismiss",
      showDetails: "Show technical details",
      hideDetails: "Hide technical details",
    },
    cancelled: "Process was cancelled.",
    types: {
      main: {
        step1: {
          label: "Generating section list",
          desc: "Using the main document to create an initial structure.",
        },
        step2: {
          label: "Processing documents",
          desc: "Generating descriptions and checking relevance.",
        },
        step3: {
          label: "Mapping documents to sections",
          desc: "Assigning relevant documents to each section.",
        },
        step4: {
          label: "Identifying legal issues",
          desc: "Extracting key legal issues for each section.",
        },
        step5: {
          label: "Generating legal memos",
          desc: "Creating legal memoranda for the identified issues.",
        },
        step6: {
          label: "Drafting sections",
          desc: "Composing the content for each individual section.",
        },
        step7: {
          label: "Combining & finalizing document",
          desc: "Assembling sections into the final document.",
        },
      },
      noMain: {
        step1: {
          label: "Processing documents",
          desc: "Generating descriptions for all uploaded files.",
        },
        step2: {
          label: "Generating section list",
          desc: "Creating a structured list of sections from document summaries.",
        },
        step3: {
          label: "Finalizing document mapping",
          desc: "Confirming document relevance for each planned section.",
        },
        step4: {
          label: "Identifying legal issues",
          desc: "Extracting key legal issues for each section.",
        },
        step5: {
          label: "Generating legal memos",
          desc: "Creating legal memoranda for the identified issues.",
        },
        step6: {
          label: "Drafting sections",
          desc: "Composing the content for each individual section.",
        },
        step7: {
          label: "Combining & finalizing document",
          desc: "Assembling sections into the final legal document.",
        },
      },
      referenceFiles: {
        step1: {
          label: "Processing reference files",
          desc: "Processing reference files",
        },
        step2: {
          label: "Processing review files",
          desc: "Processing review files",
        },
        step3: {
          label: "Generating section list",
          desc: "Generating section list",
        },
        step4: {
          label: "Drafting sections",
          desc: "Drafting sections",
        },
        step5: {
          label: "Generating report",
          desc: "Generating report",
        },
      },
      documentDrafting: {
        step1: {
          label: "Preparing documents",
          desc: "Gathering and preparing all relevant documents for the drafting process.",
        },
        step2: {
          label: "Analyzing content",
          desc: "Analyzing document content and identifying key legal issues and clauses.",
        },
        step3: {
          label: "Generating draft",
          desc: "Composing the final document draft based on the analysis.",
        },
      },
    },
  },

  // =========================
  // PROGRESS TYPES
  // =========================
  ddProgress: {
    title: "Document Drafting",
  },
  cdbProgress: {
    title: "Complex Document Builder",
    general: {
      placeholderSubTask: "Processing item {{index}}...",
    },
  },
  // =========================
  // STREAMCDB PROGRESS MODAL
  // =========================
  streamcdb_progress_modal: {
    confirm_abort_title: "Confirm Abort",
    confirm_abort_description: "Are you sure you want to cancel the process?",
    keep_running: "Continue Running",
    abort_run: "Abort Process",
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Response Generation Progress",
    description:
      "Displays the real-time progress of tasks for finishing prompt, depending on linking with other workspaces and size of files. The modal will close automatically once all steps are complete.",
    step_fetching_memos: "Fetching legal data on current topics",
    step_processing_chunks: "Processing uploaded documents",
    step_combining_responses: "Finalize response",
    sub_step_chunk_label: "Processing document group {{index}}",
    sub_step_memo_label: "Fetched legal data from {{workspaceSlug}}",
    placeholder_sub_task: "Queued sub-task",
    desc_fetching_memos:
      "Retrieving relevant legal information from linked workspaces",
    desc_processing_chunks:
      "Analyzing and extracting information from document groups",
    desc_combining_responses:
      "Synthesizing information into a comprehensive response",
  },
  // =========================
  // PROGRESS NOTIFICATION
  // =========================
  progress_notification: {
    legal: "Legal task is running in the background.",
    dd: "Document drafting continues in the background.",
    reopen: "Open status window",
  },
};
