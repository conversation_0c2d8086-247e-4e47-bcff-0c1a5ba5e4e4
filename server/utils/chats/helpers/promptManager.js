const defaultPrompts = require("../prompts/legalDrafting");
const { SystemSettings } = require("../../../models/systemSettings");

/**
 * Generates a unique SystemSettings PKey for a given prompt and its field.
 * Example: defaultPromptKey="DEFAULT_DOCUMENT_SUMMARY", fieldKey="SYSTEM_PROMPT"
 * returns "cdb_document_summary_system_prompt"
 * @param {string} defaultPromptKey - The key of the default prompt (e.g., "DEFAULT_DOCUMENT_SUMMARY").
 * @param {string} fieldKey - The field of the prompt (e.g., "SYSTEM_PROMPT", "USER_PROMPT").
 * @returns {string} The constructed PKey for SystemSettings.
 */
function getSystemSettingPKey(defaultPromptKey, fieldKey) {
  // Remove "DEFAULT_" prefix, then convert to lower case for the prompt name part.
  const baseName = defaultPromptKey.replace(/^DEFAULT_/, "").toLowerCase();
  // Convert fieldKey to lower case for the field name part.
  return `cdb_${baseName}_${fieldKey.toLowerCase()}`;
}

/**
 * Fetches all default prompts and overrides them with custom values
 * from SystemSettings if they exist, using a systematic PKey format:
 * cdb_{prompt_name}_{field_name}
 * Exports the resolved prompts with keys prefixed by CURRENT_.
 * @returns {Promise<Object>} An object containing all resolved prompts with CURRENT_ prefix.
 */
async function getResolvedPrompts() {
  // Start with a deep copy of the default prompts
  const resolved = JSON.parse(JSON.stringify(defaultPrompts));

  for (const defaultPromptKey in defaultPrompts) {
    if (
      Object.prototype.hasOwnProperty.call(defaultPrompts, defaultPromptKey)
    ) {
      const defaultPromptObject = defaultPrompts[defaultPromptKey];

      for (const fieldKey in defaultPromptObject) {
        if (
          Object.prototype.hasOwnProperty.call(defaultPromptObject, fieldKey)
        ) {
          const systemSettingPKey = getSystemSettingPKey(
            defaultPromptKey,
            fieldKey
          );

          try {
            const setting = await SystemSettings.get({
              label: systemSettingPKey,
            });

            if (
              setting &&
              typeof setting.value === "string" &&
              setting.value.trim() !== ""
            ) {
              resolved[defaultPromptKey][fieldKey] = setting.value;
              console.log(
                `Info: Applied custom SystemSetting from key '${systemSettingPKey}' for ${defaultPromptKey}.${fieldKey}`
              );
            }
          } catch (error) {
            // Errors might occur if SystemSettings.get throws when a key is not found.
            // Or, it might return null/undefined, which is handled by the 'if (setting && ...)' check.
            // For now, we assume non-existence is not an error to be loudly logged here, as defaults will be used.
            // console.log(`Debug: Setting for '${systemSettingPKey}' not found or error: ${error.message}`);
          }
        }
      }
    }
  }

  const exportablePrompts = {};
  for (const key in resolved) {
    if (Object.prototype.hasOwnProperty.call(resolved, key)) {
      exportablePrompts[`CURRENT_${key}`] = resolved[key];
    }
  }
  return exportablePrompts;
}

// --- Added PROMPT_MAPPINGS generation logic ---
// Dynamically generate a camelCase → systemSettingName mapping for all prompts.
// The heuristic matches the expectations in unit tests (e.g. "summarySystemPrompt").
// 1. Remove the leading "cdb_" prefix
// 2. Split on underscores and identify the main subject (typically the second part)
// 3. Append the field type (System/User) and the word "Prompt".
function toCamelCaseKey(systemSettingName) {
  if (
    typeof systemSettingName !== "string" ||
    !systemSettingName.startsWith("cdb_")
  ) {
    return null;
  }
  const tokens = systemSettingName.replace(/^cdb_/, "").split("_");
  if (tokens.length < 3) return null;

  // Find tokens for subject and field type (system/user)
  const subjectTokenIndex = tokens.findIndex((t) =>
    ["summary", "relevance", "section", "memo", "index"].includes(t)
  );
  const fieldTypeIndex = tokens.findIndex((t) =>
    ["system", "user"].includes(t)
  );

  if (subjectTokenIndex === -1 || fieldTypeIndex === -1) return null;

  const subject = tokens[subjectTokenIndex]; // e.g. "summary"
  const fieldType = tokens[fieldTypeIndex]; // "system" | "user"
  const camel = `${subject}${fieldType.charAt(0).toUpperCase()}${fieldType.slice(1)}Prompt`;
  return camel;
}

let PROMPT_MAPPINGS = undefined;
try {
  // The default export list lives in ../prompts/legalDrafting, but we require lazily to avoid circular deps.
  // eslint-disable-next-line global-require
  const { exportedLegalPrompts } = require("../prompts/legalDrafting");
  PROMPT_MAPPINGS = exportedLegalPrompts.reduce((acc, prompt) => {
    const camelKey = toCamelCaseKey(prompt.systemSettingName);
    if (camelKey) {
      acc[camelKey] = prompt.systemSettingName;
    }
    return acc;
  }, {});
} catch (e) {
  // In test scenarios the dependency might be mocked or unavailable; leave PROMPT_MAPPINGS undefined.
  PROMPT_MAPPINGS = undefined;
}
// --- End PROMPT_MAPPINGS generation logic ---

module.exports = {
  getResolvedPrompts,
  PROMPT_MAPPINGS,
};
