name: Release Management

on:
  push:
    tags:
      - "v*.*.*"
  workflow_dispatch:
    inputs:
      version:
        description: "Release version (e.g., v1.2.0)"
        required: true
        type: string
      release_type:
        description: "Release type"
        required: true
        default: "release"
        type: choice
        options:
          - release
          - prerelease

jobs:
  create-release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: develop
          fetch-depth: 0

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Extract version from tag or input
        id: version
        run: |
          if [[ "${{ github.event_name }}" == "push" ]]; then
            VERSION=${GITHUB_REF#refs/tags/}
          else
            VERSION=${{ inputs.version }}
          fi
          echo "version=${VERSION}" >> $GITHUB_OUTPUT
          echo "version_number=${VERSION#v}" >> $GITHUB_OUTPUT

      - name: Update version.json
        run: |
          cat > server/data/version.json << EOF
          {
            "version": "${{ steps.version.outputs.version_number }}",
            "description": "Release ${{ steps.version.outputs.version }}",
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "commit": "${{ github.sha }}"
          }
          EOF

      - name: Update package.json versions
        run: |
          # Update root package.json
          npm version ${{ steps.version.outputs.version_number }} --no-git-tag-version

          # Update server package.json
          cd server
          npm version ${{ steps.version.outputs.version_number }} --no-git-tag-version
          cd ..

          # Update collector package.json
          cd collector
          npm version ${{ steps.version.outputs.version_number }} --no-git-tag-version
          cd ..

      - name: Generate changelog
        id: changelog
        run: |
          # Generate changelog from git commits since last tag
          LAST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
          if [[ -n "$LAST_TAG" ]]; then
            CHANGELOG=$(git log ${LAST_TAG}..HEAD --pretty=format:"- %s (%h)" --no-merges)
          else
            CHANGELOG=$(git log --pretty=format:"- %s (%h)" --no-merges -10)
          fi

          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Commit version updates
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add server/data/version.json package.json server/package.json collector/package.json
          git commit -m "chore: bump version to ${{ steps.version.outputs.version }}" || exit 0

      - name: Push version updates to develop
        run: |
          git push origin develop

      - name: Create/Update Git tag
        run: |
          git tag -a ${{ steps.version.outputs.version }} -m "Release ${{ steps.version.outputs.version }}"
          git push origin ${{ steps.version.outputs.version }}

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ steps.version.outputs.version }}
          name: Release ${{ steps.version.outputs.version }}
          body: |
            ## Changes in ${{ steps.version.outputs.version }}

            ${{ steps.changelog.outputs.changelog }}

            ## Deployment Images

            - **Production**: `ghcr.io/${{ github.repository }}:${{ steps.version.outputs.version }}`
            - **Latest**: `ghcr.io/${{ github.repository }}:latest`

            ## Installation

            ```bash
            docker pull ghcr.io/${{ github.repository }}:${{ steps.version.outputs.version }}
            ```
          draft: false
          prerelease: ${{ inputs.release_type == 'prerelease' }}
          token: ${{ secrets.GITHUB_TOKEN }}

  build-and-tag-release:
    needs: create-release
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract version
        id: version
        run: |
          if [[ "${{ github.event_name }}" == "push" ]]; then
            VERSION=${GITHUB_REF#refs/tags/}
          else
            VERSION=${{ inputs.version }}
          fi
          echo "version=${VERSION}" >> $GITHUB_OUTPUT

      - name: Build and push release image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/Dockerfile
          push: true
          platforms: linux/amd64
          tags: |
            ghcr.io/${{ github.repository }}:${{ steps.version.outputs.version }}
            ghcr.io/${{ github.repository }}:latest
          labels: |
            org.opencontainers.image.title=IST Legal
            org.opencontainers.image.description=IST Legal Platform
            org.opencontainers.image.version=${{ steps.version.outputs.version }}
            org.opencontainers.image.revision=${{ github.sha }}
