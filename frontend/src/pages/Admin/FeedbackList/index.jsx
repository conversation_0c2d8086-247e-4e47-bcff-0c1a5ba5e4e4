import React, { useState, useEffect } from "react";
import { Trash, ArrowLeft, Eye, X } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { useModal } from "@/hooks/useModal";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import Sidebar from "@/components/SettingsSidebar";
import { Button } from "@/components/Button";
import paths from "@/utils/paths";
import System from "@/models/system";
import showToast from "@/utils/toast";
import { API_BASE } from "@/utils/constants";
import {
  useInvalidateFeedbackCount,
  useMarkFeedbackAsSeen,
} from "@/stores/feedbackStore";

export default function FeedbackTable() {
  const { t } = useTranslation();
  const { isOpen, openModal, closeModal } = useModal();
  const [feedbackList, setFeedbackList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [previewFile, setPreviewFile] = useState(null);
  const invalidateFeedbackCount = useInvalidateFeedbackCount();
  const markFeedbackAsSeen = useMarkFeedbackAsSeen();

  const handlePreview = (filePath) => {
    setPreviewFile(filePath);
    openModal();
  };

  const FilePreview = ({ file }) => {
    const [hasError, setHasError] = useState(false);
    const fileType = file?.toLowerCase().split(".").pop();

    if (!file) return null;

    const fileUrl = `${API_BASE}/uploads/feedback/${file}`;

    if (fileType === "pdf") {
      return (
        <iframe src={fileUrl} className="w-full h-[80vh]" title="PDF Preview" />
      );
    }

    if (["jpg", "jpeg", "png"].includes(fileType)) {
      if (hasError) {
        return (
          <div className="text-center text-foreground text-opacity-60 p-4">
            <p>{t("feedback.imageLoadError")}</p>
            <p className="text-sm mt-2">{fileUrl}</p>
          </div>
        );
      }

      return (
        <img
          src={fileUrl}
          alt={`Preview of ${file}`}
          className="max-w-full max-h-[80vh] object-contain mx-auto"
          onError={() => {
            console.error("Error loading image:", fileUrl);
            setHasError(true);
          }}
        />
      );
    }

    return (
      <div className="text-center text-foreground text-opacity-60 p-4">
        {t("feedback.unsupportedFile")}
      </div>
    );
  };

  const fetchFeedbackList = async () => {
    try {
      const { success, feedback, error } = await System.getFeedback();
      if (success) {
        setFeedbackList(feedback);
      } else {
        showToast(error, "error");
      }
    } catch (e) {
      showToast(e, "error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFeedbackList();
    // Mark feedback as seen when the page loads
    markFeedbackAsSeen();
  }, [t, markFeedbackAsSeen]);

  const handleDelete = async (id) => {
    try {
      const { success, error } = await System.deleteFeedback(id);
      if (success) {
        setFeedbackList((prev) => prev.filter((fb) => fb.id !== id));
        showToast(t("feedback-settings.delete-feedback"), "success");
        // Update feedback count in store
        invalidateFeedbackCount();
      } else {
        showToast(t("feedback-settings.delete-feedback", { error }), "error");
      }
    } catch (error) {
      showToast(t("feedback.deleteError", { error: error.message }), "error");
    }
  };

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="relative rounded-md w-full h-full overflow-y-scroll">
          <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
            <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-3 border-bottom">
              <Link to={paths.home()}>
                <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
              </Link>
              <div className="flex flex-col">
                <div className="items-center flex gap-x-4">
                  <p className="text-lg leading-6 font-bold text-foreground">
                    {t("feedback-settings.header-title")}
                  </p>
                </div>
                <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                  {t("feedback-settings.header-description")}
                </p>
              </div>
            </div>
            {loading ? (
              <div className="text-center py-8 text-foreground text-opacity-60">
                {t("feedback.loading")}
              </div>
            ) : (
              <div className="bg-elevated rounded-lg overflow-hidden shadow">
                <table className="w-full text-sm text-left">
                  <thead className="text-foreground text-opacity-80 text-xs leading-[18px] font-bold uppercase border-white border-b border-opacity-60">
                    <tr>
                      <th className="px-6 py-3 rounded-tl-lg">
                        {t("workspace.name")}
                      </th>
                      <th className="px-6 py-3">
                        {t("document-builder.table.description")}
                      </th>
                      <th className="px-6 py-3">{t("feedback.attachment")}</th>
                      <th className="px-6 py-3 rounded-tr-lg"></th>
                    </tr>
                  </thead>
                  <tbody className="text-foreground">
                    {feedbackList.length === 0 ? (
                      <tr>
                        <td
                          colSpan="4"
                          className="px-6 py-4 text-center text-foreground text-opacity-60"
                        >
                          {t("feedback.noFeedback")}
                        </td>
                      </tr>
                    ) : (
                      feedbackList.map((feedback) => (
                        <tr
                          key={feedback.id}
                          className="border-b border-border hover:bg-secondary-hover"
                        >
                          <td className="px-6 py-4 font-medium">
                            {feedback.fullName}
                          </td>
                          <td className="px-6 py-4">{feedback.message}</td>
                          <td className="px-6 py-4">
                            {feedback.filePath ? (
                              <Button
                                onClick={() => handlePreview(feedback.filePath)}
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-primary hover:text-primary-hover hover:bg-secondary"
                              >
                                <Eye size={16} />
                              </Button>
                            ) : (
                              <span className="text-foreground text-opacity-60">
                                {t("feedback.noFile")}
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-4">
                            <Button
                              onClick={() => handleDelete(feedback.id)}
                              variant="destructive"
                              size="icon"
                              className="h-8 w-8"
                            >
                              <Trash size={16} />
                            </Button>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* File Preview Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-background rounded-lg p-4 max-w-4xl w-full max-h-[90vh] overflow-auto shadow-lg border border-border">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-foreground">
                {t("feedback.filePreview")}
              </h2>
              <Button variant="ghost" size="sm" onClick={closeModal}>
                <X size={20} />
              </Button>
            </div>
            <FilePreview file={previewFile} />
          </div>
        </div>
      )}
    </div>
  );
}
