const { User } = require("../../../models/user");

describe("User Model - custom_system_prompt validation", () => {
  describe("custom_system_prompt validation", () => {
    test("should return null for null input", () => {
      const result = User.validations.custom_system_prompt(null);
      expect(result).toBeNull();
    });

    test("should return null for undefined input", () => {
      const result = User.validations.custom_system_prompt(undefined);
      expect(result).toBeNull();
    });

    test("should return null for empty string", () => {
      const result = User.validations.custom_system_prompt("");
      expect(result).toBeNull();
    });

    test("should return null for whitespace-only string", () => {
      const result = User.validations.custom_system_prompt("   ");
      expect(result).toBeNull();
    });

    test("should return trimmed string for valid input", () => {
      const input = "  This is a valid system prompt  ";
      const result = User.validations.custom_system_prompt(input);
      expect(result).toBe("This is a valid system prompt");
    });

    test("should accept string at character limit", () => {
      const input = "a".repeat(10000);
      const result = User.validations.custom_system_prompt(input);
      expect(result).toBe(input);
    });

    test("should throw error for string exceeding character limit", () => {
      const input = "a".repeat(10001);
      expect(() => {
        User.validations.custom_system_prompt(input);
      }).toThrow("Custom system prompt cannot exceed 10,000 characters");
    });

    test("should handle non-string input by converting to string", () => {
      const result = User.validations.custom_system_prompt(123);
      expect(result).toBe("123");
    });

    test("should handle multiline strings", () => {
      const input = "Line 1\nLine 2\nLine 3";
      const result = User.validations.custom_system_prompt(input);
      expect(result).toBe(input);
    });
  });
});
