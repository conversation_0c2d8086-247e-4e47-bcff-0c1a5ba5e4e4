import { act, renderHook } from "@testing-library/react";
import useThreadProgress, { useIsProcessActive } from "../useThreadProgress";
import useProgressStore from "@/stores/progressStore";

// Mock thread IDs for tests
const MOCK_THREAD_ID = "thread-123";
const MOCK_THREAD_ID_2 = "thread-456";

describe("useThreadProgress", () => {
  beforeEach(() => {
    // Reset the store state completely before each test
    act(() => {
      useProgressStore.setState({ threads: new Map() });
    });

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe("hook initialization", () => {
    it("should return default values for non-existent thread", () => {
      const { result } = renderHook(() => useThreadProgress("non-existent"));

      expect(result.current.isActive).toBe(false);
      expect(result.current.currentStep).toBe(1);
      expect(result.current.totalSteps).toBe(7);
      expect(result.current.startTime).toBeNull();
      expect(result.current.flowType).toBeNull();
      expect(result.current.currentSubStep).toBeNull();
      expect(result.current.stepStatus).toBe("pending");
      expect(result.current.stepMessage).toBeNull();
      expect(result.current.stepDetails).toEqual([]);
      expect(result.current.error).toBeNull();
    });

    it("should return default values for empty thread slug", () => {
      const { result } = renderHook(() => useThreadProgress(""));

      expect(result.current.isActive).toBe(false);
      expect(result.current.currentStep).toBe(1);
    });

    it("should return default values for null thread slug", () => {
      const { result } = renderHook(() => useThreadProgress(null));

      expect(result.current.isActive).toBe(false);
      expect(result.current.currentStep).toBe(1);
    });

    it("should trim whitespace from thread slug", () => {
      // Start a process with a clean thread ID
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      // Use hook with whitespace around the same thread ID
      const { result } = renderHook(() =>
        useThreadProgress(`  ${MOCK_THREAD_ID}  `)
      );

      expect(result.current.isActive).toBe(true);
    });
  });

  describe("state reflection", () => {
    it("should reflect active thread state", () => {
      // Start a process
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID, 5, "cdb");
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.isActive).toBe(true);
      expect(result.current.currentStep).toBe(1);
      expect(result.current.totalSteps).toBe(5);
      expect(result.current.flowType).toBe("cdb");
      expect(result.current.startTime).toBeGreaterThan(0);
    });

    it("should update when progress changes", () => {
      // Start a process
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.currentStep).toBe(1);
      expect(result.current.stepStatus).toBe("pending");

      // Update progress
      act(() => {
        useProgressStore.getState().updateProgress(
          {
            step: 3,
            status: "in_progress",
            message: "Processing step 3",
          },
          MOCK_THREAD_ID
        );
      });

      expect(result.current.currentStep).toBe(3);
      expect(result.current.stepStatus).toBe("in_progress");
      expect(result.current.stepMessage).toBe("Processing step 3");
    });

    it("should reflect error state", () => {
      // Start a process
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.error).toBeNull();
      expect(result.current.isActive).toBe(true);

      // Set error
      act(() => {
        useProgressStore.getState().setError(MOCK_THREAD_ID, "Test error");
      });

      expect(result.current.error).toBe("Test error");
      expect(result.current.isActive).toBe(false);
    });
  });

  describe("action methods", () => {
    it("should start a process", () => {
      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      act(() => {
        result.current.start(5, "cdb");
      });

      const storeState = useProgressStore.getState();
      const threadState = storeState.threads.get(MOCK_THREAD_ID);

      expect(threadState).toBeDefined();
      expect(threadState.isActive).toBe(true);
      expect(threadState.totalSteps).toBe(5);
      expect(threadState.flowType).toBe("cdb");
    });

    it("should not start process with empty thread slug", () => {
      const { result } = renderHook(() => useThreadProgress(""));

      act(() => {
        result.current.start();
      });

      const storeState = useProgressStore.getState();
      expect(storeState.threads.size).toBe(0);
    });

    it("should update progress", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      act(() => {
        result.current.update({
          step: 2,
          status: "in_progress",
          message: "Step 2",
        });
      });

      expect(result.current.currentStep).toBe(2);
      expect(result.current.stepStatus).toBe("in_progress");
      expect(result.current.stepMessage).toBe("Step 2");
    });

    it("should finish a process", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.isActive).toBe(true);

      act(() => {
        result.current.finish();
      });

      const storeState = useProgressStore.getState();
      expect(storeState.threads.has(MOCK_THREAD_ID)).toBe(false);
    });

    it("should cancel a process", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      const controller = useProgressStore
        .getState()
        .threads.get(MOCK_THREAD_ID).abortController;
      const abortSpy = jest.spyOn(controller, "abort");

      act(() => {
        result.current.cancel();
      });

      expect(abortSpy).toHaveBeenCalled();
      expect(result.current.isActive).toBe(false);
      expect(result.current.stepStatus).toBe("error");
    });

    it("should set error", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      act(() => {
        result.current.setError("Test error message");
      });

      expect(result.current.error).toBe("Test error message");
      expect(result.current.isActive).toBe(false);
      expect(result.current.stepStatus).toBe("error");
    });

    it("should clear error", () => {
      // Start a process and set error first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
        useProgressStore.getState().setError(MOCK_THREAD_ID, "Test error");
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.error).toBe("Test error");

      act(() => {
        result.current.clearError();
      });

      const storeState = useProgressStore.getState();
      expect(storeState.threads.has(MOCK_THREAD_ID)).toBe(false);
    });

    it("should force cleanup", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      expect(result.current.isActive).toBe(true);

      act(() => {
        result.current.forceCleanup();
      });

      const storeState = useProgressStore.getState();
      expect(storeState.threads.has(MOCK_THREAD_ID)).toBe(false);
    });
  });

  describe("abort signal handling", () => {
    it("should get abort signal for active process", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      const signal = result.current.getAbortSignal();
      expect(signal).toBeInstanceOf(AbortSignal);
      expect(signal.aborted).toBe(false);
    });

    it("should return null for non-existent thread", () => {
      const { result } = renderHook(() => useThreadProgress("non-existent"));

      const signal = result.current.getAbortSignal();
      expect(signal).toBeNull();
    });

    it("should return null for empty thread slug", () => {
      const { result } = renderHook(() => useThreadProgress(""));

      const signal = result.current.getAbortSignal();
      expect(signal).toBeNull();
    });

    it("should reflect aborted signal after cancellation", () => {
      // Start a process first
      act(() => {
        useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      });

      const { result } = renderHook(() => useThreadProgress(MOCK_THREAD_ID));

      const signalBefore = result.current.getAbortSignal();
      expect(signalBefore.aborted).toBe(false);

      act(() => {
        result.current.cancel();
      });

      const signalAfter = result.current.getAbortSignal();
      expect(signalAfter.aborted).toBe(true);
    });
  });

  describe("multiple threads independence", () => {
    it("should handle multiple independent threads", () => {
      const { result: result1 } = renderHook(() =>
        useThreadProgress(MOCK_THREAD_ID)
      );
      const { result: result2 } = renderHook(() =>
        useThreadProgress(MOCK_THREAD_ID_2)
      );

      // Start different processes
      act(() => {
        result1.current.start(5, "flow1");
        result2.current.start(8, "flow2");
      });

      expect(result1.current.isActive).toBe(true);
      expect(result1.current.totalSteps).toBe(5);
      expect(result1.current.flowType).toBe("flow1");

      expect(result2.current.isActive).toBe(true);
      expect(result2.current.totalSteps).toBe(8);
      expect(result2.current.flowType).toBe("flow2");

      // Update first thread
      act(() => {
        result1.current.update({ step: 3, status: "in_progress" });
      });

      expect(result1.current.currentStep).toBe(3);
      expect(result2.current.currentStep).toBe(1); // Should not affect second thread

      // Finish first thread
      act(() => {
        result1.current.finish();
      });

      expect(result1.current.isActive).toBe(false);
      expect(result2.current.isActive).toBe(true); // Second thread should still be active
    });
  });

  describe("edge cases", () => {
    it("should handle action calls with no valid thread slug", () => {
      const { result } = renderHook(() => useThreadProgress(""));

      // These should not throw errors
      act(() => {
        result.current.start();
        result.current.update({ step: 1 });
        result.current.finish();
        result.current.cancel();
        result.current.setError("error");
        result.current.clearError();
        result.current.forceCleanup();
      });

      const storeState = useProgressStore.getState();
      expect(storeState.threads.size).toBe(0);
    });

    it("should handle action calls on non-existent thread", () => {
      const { result } = renderHook(() => useThreadProgress("non-existent"));

      // These should not throw errors
      act(() => {
        result.current.update({ step: 1 });
        result.current.finish();
        result.current.cancel();
        result.current.setError("error");
        result.current.clearError();
        result.current.forceCleanup();
      });

      // No errors should occur
      expect(result.current.isActive).toBe(false);
    });
  });
});

describe("useIsProcessActive", () => {
  beforeEach(() => {
    // Reset the store state completely before each test
    act(() => {
      useProgressStore.setState({ threads: new Map() });
    });

    // Clear all mocks
    jest.clearAllMocks();
  });

  it("should return false for non-existent thread", () => {
    const { result } = renderHook(() => useIsProcessActive("non-existent"));
    expect(result.current).toBe(false);
  });

  it("should return false for empty thread slug", () => {
    const { result } = renderHook(() => useIsProcessActive(""));
    expect(result.current).toBe(false);
  });

  it("should return false for null thread slug", () => {
    const { result } = renderHook(() => useIsProcessActive(null));
    expect(result.current).toBe(false);
  });

  it("should return true for active process", () => {
    // Start a process
    act(() => {
      useProgressStore.getState().startProcess(MOCK_THREAD_ID);
    });

    const { result } = renderHook(() => useIsProcessActive(MOCK_THREAD_ID));
    expect(result.current).toBe(true);
  });

  it("should return false for inactive process", () => {
    // Start and then cancel a process
    act(() => {
      useProgressStore.getState().startProcess(MOCK_THREAD_ID);
      useProgressStore.getState().cancelProcess(MOCK_THREAD_ID);
    });

    const { result } = renderHook(() => useIsProcessActive(MOCK_THREAD_ID));
    expect(result.current).toBe(false);
  });

  it("should update when process state changes", () => {
    const { result } = renderHook(() => useIsProcessActive(MOCK_THREAD_ID));

    expect(result.current).toBe(false);

    // Start process
    act(() => {
      useProgressStore.getState().startProcess(MOCK_THREAD_ID);
    });

    expect(result.current).toBe(true);

    // Cancel process
    act(() => {
      useProgressStore.getState().cancelProcess(MOCK_THREAD_ID);
    });

    expect(result.current).toBe(false);
  });

  it("should trim whitespace from thread slug", () => {
    // Start a process with a clean thread ID
    act(() => {
      useProgressStore.getState().startProcess(MOCK_THREAD_ID);
    });

    // Use hook with whitespace around the same thread ID
    const { result } = renderHook(() =>
      useIsProcessActive(`  ${MOCK_THREAD_ID}  `)
    );

    expect(result.current).toBe(true);
  });
});
