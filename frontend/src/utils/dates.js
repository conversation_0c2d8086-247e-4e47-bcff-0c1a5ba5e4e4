import { format } from "date-fns";

export const getLocalizedDate = (date, t) => {
  if (!date) return "";
  const day = format(date, "d");
  const monthNumber = date.getMonth() + 1; // getMonth() is 0-indexed
  const monthKey = `month.${monthNumber}`;
  const year = format(date, "yyyy");
  // Fallback to English full month name if translation not found
  const localizedMonth = t(monthKey, { defaultValue: format(date, "MMMM") });
  return `${day} ${localizedMonth} ${year}`;
};
