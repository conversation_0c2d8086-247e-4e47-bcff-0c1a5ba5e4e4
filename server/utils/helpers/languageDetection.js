const { userFromSession } = require("../http");
const { SystemSettings } = require("../../models/systemSettings");

// Supported languages (aligned with frontend and i18n)
const SUPPORTED_LANGUAGES = ["en", "fr", "de", "sv", "rw", "no", "pl"];
const DEFAULT_LANGUAGE = "en";

/**
 * Get the preferred language for a given request, considering:
 * 1. User's custom language preference (from localStorage via frontend)
 * 2. System's default language setting
 * 3. Fallback to English
 *
 * @param {object} request - Express request object
 * @param {object} response - Express response object (optional)
 * @returns {Promise<string>} Language code (e.g., "en", "sv", "fr")
 */
async function getPreferredLanguage(request, response = null) {
  try {
    // Try to get user-specific language preference
    // Note: In the current implementation, user language is stored in localStorage
    // and sent via the frontend. For server-side detection, we use system language.
    // In future, this could be enhanced to store user language preferences in the database.

    // Check if user is authenticated (optional for version endpoint)
    const user = await userFromSession(request, response);

    // TODO: If user language preferences are stored in database in the future,
    // retrieve and return user's preferred language here

    // Try system language first
    const systemLanguage = await getSystemLanguage();
    if (systemLanguage !== DEFAULT_LANGUAGE) {
      return systemLanguage;
    }

    // If no system language is set, try to detect from Accept-Language header
    const browserLanguage = detectBrowserLanguage(request);
    if (browserLanguage && browserLanguage !== DEFAULT_LANGUAGE) {
      return browserLanguage;
    }

    return DEFAULT_LANGUAGE;
  } catch (error) {
    console.warn("Error detecting preferred language:", error.message);
    return DEFAULT_LANGUAGE;
  }
}

/**
 * Get the system's default language setting
 * @returns {Promise<string>} Language code
 */
async function getSystemLanguage() {
  try {
    const setting = await SystemSettings.get({ label: "language" });
    const language = setting?.value;

    if (language && SUPPORTED_LANGUAGES.includes(language)) {
      return language;
    }
  } catch (error) {
    console.warn("Failed to get system language:", error.message);
  }

  return DEFAULT_LANGUAGE;
}

/**
 * Get localized description from version object based on language preference
 * @param {object} versionObj - Version object containing description fields
 * @param {string} language - Preferred language code
 * @returns {string} Localized description
 */
function getLocalizedDescription(versionObj, language = DEFAULT_LANGUAGE) {
  if (!versionObj) {
    return "";
  }

  // Try to get localized description
  const localizedKey = `description-${language}`;
  if (versionObj[localizedKey]) {
    return versionObj[localizedKey];
  }

  // Fall back to default description
  if (versionObj.description) {
    return versionObj.description;
  }

  // Ultimate fallback
  return "Version information available";
}

/**
 * Detect language preference from browser's Accept-Language header
 * @param {object} request - Express request object
 * @returns {string|null} Language code or null if none supported
 */
function detectBrowserLanguage(request) {
  const acceptLanguage = request.headers["accept-language"];

  if (!acceptLanguage) {
    return null;
  }

  // Parse Accept-Language header (e.g., "sv-SE,sv;q=0.9,en-US;q=0.8,en;q=0.7")
  const languages = acceptLanguage
    .split(",")
    .map((lang) => {
      const [code, q] = lang.trim().split(";q=");
      const quality = q ? parseFloat(q) : 1.0;
      // Extract base language code (sv-SE -> sv, en-US -> en)
      const baseCode = code.split("-")[0].toLowerCase();
      return { code: baseCode, quality };
    })
    .sort((a, b) => b.quality - a.quality); // Sort by quality (preference)

  // Find the first supported language
  for (const { code } of languages) {
    if (SUPPORTED_LANGUAGES.includes(code)) {
      return code;
    }
  }

  return null;
}

/**
 * Validate if a language code is supported
 * @param {string} language - Language code to validate
 * @returns {boolean} True if supported
 */
function isLanguageSupported(language) {
  return SUPPORTED_LANGUAGES.includes(language);
}

module.exports = {
  getPreferredLanguage,
  getSystemLanguage,
  getLocalizedDescription,
  detectBrowserLanguage,
  isLanguageSupported,
  SUPPORTED_LANGUAGES,
  DEFAULT_LANGUAGE,
};
