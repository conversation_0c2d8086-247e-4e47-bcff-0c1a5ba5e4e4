/* eslint-env jest */

import React from "react";
import { render, fireEvent, waitFor, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { BrowserRouter } from "react-router-dom";
import ChatUISettings from "../index";
import System from "@/models/system";
import showToast from "@/utils/toast";

// Polyfill for TextEncoder/TextDecoder in test environment
const { TextEncoder, TextDecoder } = require("util");
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock dependencies
jest.mock("@/models/system", () => ({
  keys: jest.fn(),
  updateSystem: jest.fn(),
}));

jest.mock("@/utils/toast");

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key) => key,
  }),
}));

// Mock react-router-dom
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => jest.fn(),
}));

jest.mock("@/components/SettingsSidebar", () => {
  function MockSettingsSidebar() {
    return <div />;
  }
  return MockSettingsSidebar;
});

jest.mock("@/components/HeaderWorkspace", () => {
  function MockHeaderWorkspace() {
    return <div data-testid="header-workspace" />;
  }
  return MockHeaderWorkspace;
});

jest.mock("../../components/AutoSubmit", () => {
  function MockAutoSubmit() {
    return <div />;
  }
  return MockAutoSubmit;
});
jest.mock("../../components/AutoSpeak", () => {
  function MockAutoSpeak() {
    return <div />;
  }
  return MockAutoSpeak;
});

const initialSettings = {
  scroll_bottom_threshold: 10,
  scroll_streaming_disable_threshold: 50,
  scroll_auto_scroll_threshold: 30,
};

// Helper function to render component with Router context
const renderWithRouter = (component) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe("ChatUISettings Page - Integration Test", () => {
  beforeEach(() => {
    System.keys.mockResolvedValue(initialSettings);
    System.updateSystem.mockResolvedValue({ success: true });
    showToast.mockClear();
    System.keys.mockClear();
    System.updateSystem.mockClear();
  });

  test("loads and displays scroll threshold settings correctly", async () => {
    renderWithRouter(<ChatUISettings />);
    await waitFor(() => {
      expect(System.keys).toHaveBeenCalledTimes(1);
    });

    expect(
      screen.getByText("scroll-threshold-settings.bottom-threshold.title")
    ).toBeInTheDocument();
    const bottomSlider = screen.getByDisplayValue("10");
    expect(bottomSlider).toBeInTheDocument();
  });

  test("allows saving scroll threshold settings", async () => {
    renderWithRouter(<ChatUISettings />);
    await waitFor(() => {
      expect(System.keys).toHaveBeenCalledTimes(1);
    });

    // Note: Slider interactions are complex to test directly, so we focus on form submission.
    const saveButton = screen.getByText(
      "scroll-threshold-settings.save-changes"
    );

    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(System.updateSystem).toHaveBeenCalledWith(initialSettings);
      expect(showToast).toHaveBeenCalledWith(
        "scroll-threshold-settings.saved",
        "success"
      );
    });
  });

  test("shows an error toast when saving settings fails", async () => {
    System.updateSystem.mockRejectedValue({
      success: false,
      error: "Failed to save settings.",
    });
    renderWithRouter(<ChatUISettings />);
    await waitFor(() => {
      expect(System.keys).toHaveBeenCalledTimes(1);
    });

    const saveButton = screen.getByText(
      "scroll-threshold-settings.save-changes"
    );
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(System.updateSystem).toHaveBeenCalledWith(initialSettings);
      expect(showToast).toHaveBeenCalledWith(
        "scroll-threshold-settings.error",
        "error"
      );
    });
  });

  test("handles API errors gracefully on load", async () => {
    System.keys.mockRejectedValue(new Error("API Error"));
    renderWithRouter(<ChatUISettings />);

    await waitFor(() => {
      expect(System.keys).toHaveBeenCalledTimes(1);
      expect(showToast).toHaveBeenCalledWith(
        "scroll-threshold-settings.fetch-error",
        "error"
      );
    });
  });

  test("does not render form while fetching settings", async () => {
    // Prevent the promise from resolving immediately
    System.keys.mockImplementation(() => new Promise(() => {}));
    renderWithRouter(<ChatUISettings />);

    await waitFor(() => {
      expect(System.keys).toHaveBeenCalledTimes(1);
    });

    // The form is only rendered when settings are loaded.
    const saveButton = screen.queryByText(
      "scroll-threshold-settings.save-changes"
    );
    expect(saveButton).not.toBeInTheDocument();
  });
});
