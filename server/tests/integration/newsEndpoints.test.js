const request = require("supertest");
const { NewsMessage } = require("../../models/newsMessage");

// Mock the NewsMessage model
jest.mock("../../models/newsMessage");

// Mock middleware with a variable that can be changed - will be initialized in beforeEach
let mockUser;

jest.mock("../../utils/middleware/validatedRequest", () => ({
  validatedRequest: (req, res, next) => {
    res.locals.user = mockUser;
    next();
  },
}));

// Mock admin middleware
jest.mock("../../utils/middleware/multiUserProtected", () => ({
  multiUserProtected: (req, res, next) => {
    res.locals.user = { id: 1, role: "admin" };
    next();
  },
}));

// Create a mock Express app for testing
const express = require("express");
const app = express();
const apiRouter = express.Router();
app.use(express.json());
app.use("/api", apiRouter);

// Import and setup news routes
const { newsEndpoints } = require("../../endpoints/news");
newsEndpoints(apiRouter);

// Helper function to mock admin user
const mockAdminUser = () => {
  mockUser = { id: 1, role: "admin" };
};

// Helper function to mock default user
const mockDefaultUser = () => {
  mockUser = { id: 1, role: "default" };
};

describe("News Endpoints Integration Tests", () => {
  beforeEach(() => {
    // Reset mockUser to default state before each test
    mockUser = { id: 1, role: "default" };
    jest.clearAllMocks();
  });

  describe("GET /api/news/unread", () => {
    test("should get unread news for authenticated user", async () => {
      const mockUnreadNews = [
        {
          id: 1,
          title: "Test News 1",
          content: "Content 1",
          priority: "high",
          createdAt: "2024-01-01T00:00:00Z",
        },
        {
          id: 2,
          title: "Test News 2",
          content: "Content 2",
          priority: "medium",
          createdAt: "2024-01-02T00:00:00Z",
        },
      ];

      NewsMessage.getUnreadForUser.mockResolvedValue(mockUnreadNews);

      const response = await request(app).get("/api/news/unread");

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.news).toEqual(mockUnreadNews);
      expect(NewsMessage.getUnreadForUser).toHaveBeenCalledWith(1);
    });

    test("should handle database errors gracefully", async () => {
      NewsMessage.getUnreadForUser.mockRejectedValue(
        new Error("Database error")
      );

      const response = await request(app).get("/api/news/unread");

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe("Failed to fetch unread news");
    });
  });

  describe("GET /api/news/dismissed-system", () => {
    test("should get dismissed system news IDs", async () => {
      const mockDismissedIds = ["system-welcome-2024", "system-update-2024"];

      NewsMessage.getDismissedSystemNewsIds.mockResolvedValue(mockDismissedIds);

      const response = await request(app).get("/api/news/dismissed-system");

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.dismissedIds).toEqual(mockDismissedIds);
      expect(NewsMessage.getDismissedSystemNewsIds).toHaveBeenCalledWith(1);
    });

    test("should handle database errors", async () => {
      NewsMessage.getDismissedSystemNewsIds.mockRejectedValue(
        new Error("Database error")
      );

      const response = await request(app).get("/api/news/dismissed-system");

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe(
        "Failed to fetch dismissed system news"
      );
    });
  });

  describe("POST /api/news/:id/dismiss", () => {
    test("should dismiss local news successfully", async () => {
      const mockDismissal = {
        dismissal: { user_id: 1, news_id: "local-123" },
        message: null,
      };

      NewsMessage.dismiss.mockResolvedValue(mockDismissal);

      const response = await request(app).post("/api/news/123/dismiss");

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.dismissal).toEqual(mockDismissal.dismissal);
      expect(NewsMessage.dismiss).toHaveBeenCalledWith(1, 123, false);
    });

    test("should handle dismissal errors", async () => {
      const mockError = {
        dismissal: null,
        message: "Database error",
      };

      NewsMessage.dismiss.mockResolvedValue(mockError);

      const response = await request(app).post("/api/news/123/dismiss");

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe("Database error");
    });

    test("should handle invalid news ID", async () => {
      // With our new validation, invalid IDs are caught before reaching the database
      const response = await request(app).post("/api/news/invalid/dismiss");

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe(
        "newsId must be a valid positive integer"
      );
    });
  });

  describe("POST /api/news/system/:id/dismiss", () => {
    test("should dismiss system news successfully", async () => {
      const mockDismissal = {
        dismissal: { user_id: 1, news_id: "system-welcome-2024" },
        message: null,
      };

      NewsMessage.dismiss.mockResolvedValue(mockDismissal);

      const response = await request(app).post(
        "/api/news/system/system-welcome-2024/dismiss"
      );

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe("System news dismissed successfully");
      expect(NewsMessage.dismiss).toHaveBeenCalledWith(
        1,
        "system-welcome-2024",
        true
      );
    });

    test("should handle system news dismissal errors", async () => {
      const mockError = {
        dismissal: null,
        message: "Database error",
      };

      NewsMessage.dismiss.mockResolvedValue(mockError);

      const response = await request(app).post(
        "/api/news/system/system-welcome-2024/dismiss"
      );

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe("Database error");
    });
  });

  describe("POST /api/news/:id/view", () => {
    test("should mark local news as viewed successfully", async () => {
      // Mock the markAsViewed method
      const mockViewResult = {
        view: { user_id: 1, news_id: 123 },
        message: null,
      };

      NewsMessage.markAsViewed.mockResolvedValue(mockViewResult);

      const response = await request(app).post("/api/news/123/view");

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.view).toEqual(mockViewResult.view);
      expect(NewsMessage.markAsViewed).toHaveBeenCalledWith(1, 123);
    });

    test("should handle invalid news ID for viewing", async () => {
      // With our new validation, invalid IDs are caught before reaching the database
      const response = await request(app).post("/api/news/invalid/view");

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe(
        "newsId must be a valid positive integer"
      );
    });
  });

  describe("Admin News Management Endpoints", () => {
    beforeEach(() => {
      // Mock admin user for admin endpoints
      jest.clearAllMocks();
    });

    describe("GET /api/admin/news", () => {
      test("should get all news for admin", async () => {
        mockAdminUser();

        const mockAllNews = [
          {
            id: 1,
            title: "Admin News 1",
            content: "Content 1",
            priority: "high",
            is_active: true,
            target_roles: null,
            expires_at: null,
            created_by: 1,
            createdAt: "2024-01-01T00:00:00Z",
          },
          {
            id: 2,
            title: "Admin News 2",
            content: "Content 2",
            priority: "medium",
            is_active: false,
            target_roles: '["admin"]',
            expires_at: "2024-12-31T23:59:59Z",
            created_by: 1,
            createdAt: "2024-01-02T00:00:00Z",
          },
        ];

        const expectedTransformedNews = [
          {
            id: 1,
            title: "Admin News 1",
            content: "Content 1",
            priority: "high",
            is_active: true,
            target_roles: null,
            expires_at: null,
            created_by: 1,
            createdAt: "2024-01-01T00:00:00Z",
            isActive: true,
            targetRoles: null,
            expiresAt: null,
            createdBy: 1,
          },
          {
            id: 2,
            title: "Admin News 2",
            content: "Content 2",
            priority: "medium",
            is_active: false,
            target_roles: '["admin"]',
            expires_at: "2024-12-31T23:59:59Z",
            created_by: 1,
            createdAt: "2024-01-02T00:00:00Z",
            isActive: false,
            targetRoles: ["admin"],
            expiresAt: "2024-12-31T23:59:59Z",
            createdBy: 1,
          },
        ];

        NewsMessage.getAll.mockResolvedValue(mockAllNews);

        const response = await request(app).get("/api/admin/news");

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.news).toEqual(expectedTransformedNews);
        expect(NewsMessage.getAll).toHaveBeenCalled();
      });
    });

    describe("POST /api/admin/news", () => {
      test("should create news successfully", async () => {
        mockAdminUser();

        const mockCreatedNews = {
          newsMessage: {
            id: 1,
            title: "New Admin News",
            content: "New Content",
            priority: "high",
            is_active: true,
            target_roles: '["admin"]',
            expires_at: "2024-12-31T23:59:59Z",
            created_by: 1,
          },
          message: null,
        };

        const expectedTransformedNewsMessage = {
          id: 1,
          title: "New Admin News",
          content: "New Content",
          priority: "high",
          is_active: true,
          target_roles: '["admin"]',
          expires_at: "2024-12-31T23:59:59Z",
          created_by: 1,
          isActive: true,
          targetRoles: ["admin"],
          expiresAt: "2024-12-31T23:59:59Z",
          createdBy: 1,
        };

        NewsMessage.create.mockResolvedValue(mockCreatedNews);

        const newsData = {
          title: "New Admin News",
          content: "New Content",
          priority: "high",
          targetRoles: ["admin"],
          expiresAt: "2024-12-31T23:59:59Z",
        };

        const response = await request(app)
          .post("/api/admin/news")
          .send(newsData);

        expect(response.status).toBe(201);
        expect(response.body.success).toBe(true);
        expect(response.body.newsMessage).toEqual(
          expectedTransformedNewsMessage
        );
        expect(NewsMessage.create).toHaveBeenCalledWith({
          title: "New Admin News",
          content: "New Content",
          priority: "high",
          targetRoles: ["admin"],
          expiresAt: new Date("2024-12-31T23:59:59Z"),
          createdBy: 1,
        });
      });

      test("should validate required fields", async () => {
        mockAdminUser();

        const response = await request(app).post("/api/admin/news").send({
          content: "Missing title",
        });

        expect(response.status).toBe(400);
        expect(response.body.success).toBe(false);
        expect(response.body.message).toBe("Title and content are required");
      });
    });

    describe("PUT /api/admin/news/:id", () => {
      test("should update news successfully", async () => {
        mockAdminUser();

        const mockUpdatedNews = {
          newsMessage: {
            id: 1,
            title: "Updated News",
            content: "Updated Content",
            priority: "urgent",
            is_active: true,
            target_roles: null,
            expires_at: null,
            created_by: 1,
          },
          message: null,
        };

        const expectedTransformedNewsMessage = {
          id: 1,
          title: "Updated News",
          content: "Updated Content",
          priority: "urgent",
          is_active: true,
          target_roles: null,
          expires_at: null,
          created_by: 1,
          isActive: true,
          targetRoles: null,
          expiresAt: null,
          createdBy: 1,
        };

        NewsMessage.update.mockResolvedValue(mockUpdatedNews);

        const updateData = {
          title: "Updated News",
          content: "Updated Content",
          priority: "urgent",
        };

        const response = await request(app)
          .put("/api/admin/news/1")
          .send(updateData);

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.newsMessage).toEqual(
          expectedTransformedNewsMessage
        );
        expect(NewsMessage.update).toHaveBeenCalledWith(1, updateData);
      });

      test("should handle update errors", async () => {
        mockAdminUser();

        const mockError = {
          newsMessage: null,
          message: "News not found",
        };

        NewsMessage.update.mockResolvedValue(mockError);

        const response = await request(app)
          .put("/api/admin/news/999")
          .send({ title: "Updated Title" });

        expect(response.status).toBe(400);
        expect(response.body.success).toBe(false);
        expect(response.body.message).toBe("News not found");
      });
    });

    describe("DELETE /api/admin/news/:id", () => {
      test("should delete news successfully", async () => {
        mockAdminUser();

        const mockDeleteResult = {
          success: true,
          message: null,
        };

        NewsMessage.delete.mockResolvedValue(mockDeleteResult);

        const response = await request(app).delete("/api/admin/news/1");

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.message).toBe("News message deleted successfully");
        expect(NewsMessage.delete).toHaveBeenCalledWith(1);
      });

      test("should handle deletion errors", async () => {
        mockAdminUser();

        const mockError = {
          success: false,
          message: "News not found",
        };

        NewsMessage.delete.mockResolvedValue(mockError);

        const response = await request(app).delete("/api/admin/news/999");

        expect(response.status).toBe(400);
        expect(response.body.success).toBe(false);
        expect(response.body.message).toBe("News not found");
      });
    });
  });

  describe("News System Edge Cases", () => {
    test("should handle concurrent dismissals correctly", async () => {
      const mockDismissal = {
        dismissal: { user_id: 1, news_id: "local-123" },
        message: null,
      };

      NewsMessage.dismiss.mockResolvedValue(mockDismissal);

      // Simulate concurrent dismissal requests
      const promises = [
        request(app).post("/api/news/123/dismiss"),
        request(app).post("/api/news/123/dismiss"),
        request(app).post("/api/news/123/dismiss"),
      ];

      const responses = await Promise.all(promises);

      // All should succeed (upsert behavior)
      responses.forEach((response) => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });

      expect(NewsMessage.dismiss).toHaveBeenCalledTimes(3);
    });

    test("should handle mixed system and local news operations", async () => {
      const mockLocalDismissal = {
        dismissal: { user_id: 1, news_id: "local-123" },
        message: null,
      };

      const mockSystemDismissal = {
        dismissal: { user_id: 1, news_id: "system-welcome-2024" },
        message: null,
      };

      NewsMessage.dismiss
        .mockResolvedValueOnce(mockLocalDismissal)
        .mockResolvedValueOnce(mockSystemDismissal);

      const localResponse = await request(app).post("/api/news/123/dismiss");
      const systemResponse = await request(app).post(
        "/api/news/system/system-welcome-2024/dismiss"
      );

      expect(localResponse.status).toBe(200);
      expect(systemResponse.status).toBe(200);
      expect(NewsMessage.dismiss).toHaveBeenCalledWith(1, 123, false);
      expect(NewsMessage.dismiss).toHaveBeenCalledWith(
        1,
        "system-welcome-2024",
        true
      );
    });

    test("should handle large news content correctly", async () => {
      mockAdminUser();

      const largeContent = "A".repeat(10000); // 10KB content
      const mockCreatedNews = {
        newsMessage: {
          id: 1,
          title: "Large News",
          content: largeContent,
          priority: "medium",
        },
        message: null,
      };

      NewsMessage.create.mockResolvedValue(mockCreatedNews);

      const response = await request(app).post("/api/admin/news").send({
        title: "Large News",
        content: largeContent,
        priority: "medium",
      });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.newsMessage.content).toBe(largeContent);
    });

    test("should handle special characters in news content", async () => {
      mockAdminUser();

      const specialContent = "News with émojis 🎉 and spëcial chars: <>&\"'";
      const mockCreatedNews = {
        newsMessage: {
          id: 1,
          title: "Special Characters News",
          content: specialContent,
          priority: "medium",
        },
        message: null,
      };

      NewsMessage.create.mockResolvedValue(mockCreatedNews);

      const response = await request(app).post("/api/admin/news").send({
        title: "Special Characters News",
        content: specialContent,
        priority: "medium",
      });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.newsMessage.content).toBe(specialContent);
    });
  });

  describe("News System Performance", () => {
    test("should handle multiple simultaneous requests", async () => {
      const mockUnreadNews = [{ id: 1, title: "News 1", content: "Content 1" }];

      NewsMessage.getUnreadForUser.mockResolvedValue(mockUnreadNews);

      // Simulate 10 concurrent requests
      const promises = Array(10)
        .fill()
        .map(() => request(app).get("/api/news/unread"));

      const responses = await Promise.all(promises);

      responses.forEach((response) => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });

      expect(NewsMessage.getUnreadForUser).toHaveBeenCalledTimes(10);
    });

    test("should handle empty news arrays correctly", async () => {
      NewsMessage.getUnreadForUser.mockResolvedValue([]);
      NewsMessage.getDismissedSystemNewsIds.mockResolvedValue([]);

      const unreadResponse = await request(app).get("/api/news/unread");
      const dismissedResponse = await request(app).get(
        "/api/news/dismissed-system"
      );

      expect(unreadResponse.status).toBe(200);
      expect(unreadResponse.body.news).toEqual([]);

      expect(dismissedResponse.status).toBe(200);
      expect(dismissedResponse.body.dismissedIds).toEqual([]);
    });
  });
});
