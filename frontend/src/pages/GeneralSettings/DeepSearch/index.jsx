import React from "react";
import Sidebar from "@/components/SettingsSidebar";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { useTranslation } from "react-i18next";
import { ArrowLeft } from "@phosphor-icons/react";
import { Link } from "react-router-dom";
import paths from "@/utils/paths";
import DeepSearchPreference from "../DeepSearchPreference";

export default function DeepSearch() {
  const { t } = useTranslation();

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="flex-1 flex flex-col h-full overflow-y-auto bg-background">
          <div className="top-main-block relative rounded-md w-full h-full overflow-y-scroll">
            <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
              {/* Header Section */}
              <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-4">
                <Link to={paths.settings.system()}>
                  <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
                </Link>
                <div className="flex flex-col">
                  <div className="items-center">
                    <p className="text-lg leading-6 font-bold text-foreground">
                      {t("deep_search.title")}
                    </p>
                  </div>
                  <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                    {t("deep_search.description")}
                  </p>
                </div>
              </div>

              {/* DeepSearch Content */}
              <DeepSearchPreference />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
