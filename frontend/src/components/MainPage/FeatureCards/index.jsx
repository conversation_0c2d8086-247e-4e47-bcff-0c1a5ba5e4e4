import { useState } from "react";
import { MdConstruction } from "react-icons/md";
import { BsFillLightningChargeFill } from "react-icons/bs";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import WorkspaceSelectorModal from "@/components/Modals/WorkspaceSelectorModal";
import { useSetSelectedFeatureCard } from "@/stores/userStore";
import ManageWorkspace from "@/components/Modals/ManageWorkspace";
import paths from "@/utils/paths";

const FeatureCards = () => {
  const { t } = useTranslation();
  const [isWorkspaceSelectorModalOpen, setIsWorkspaceSelectorModalOpen] =
    useState(false);
  const setSelectedFeatureCard = useSetSelectedFeatureCard();
  const [isManageWorkspaceModalOpen, setIsManageWorkspaceModalOpen] =
    useState(false);
  const [manageWorkspaceSlug, setManageWorkspaceSlug] = useState(null);

  const FEATURE_CARDS = [
    {
      id: "draft-from-template",
      icon: BsFillLightningChargeFill,
      title: "featureCards.draft-from-template-title",
      description: "featureCards.draft-from-template-description",
    },
    {
      id: "complex-document-builder",
      icon: MdConstruction,
      title: "featureCards.complex-document-builder-title",
      description: "featureCards.complex-document-builder-description",
    },
  ];

  const handleOpenUploadModal = (workspaceSlug, isNewWorkspace) => {
    setIsWorkspaceSelectorModalOpen(false);

    if (isNewWorkspace) {
      setManageWorkspaceSlug(workspaceSlug);
      setIsManageWorkspaceModalOpen(true);
    } else {
      window.location.href = `${paths.workspace.chat(workspaceSlug)}?openModal=performLegalTask`;
    }
  };

  return (
    <>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mt-8">
        {FEATURE_CARDS.map((card) => (
          <Button
            key={card.id}
            variant="outline"
            className="flex flex-col gap-3 items-start h-fit p-6 rounded-xl text-left [&_svg]:size-7"
            onClick={() => {
              setSelectedFeatureCard(card.id);
              setIsWorkspaceSelectorModalOpen(true);
            }}
          >
            <div className="flex gap-2.5">
              <card.icon className="text-primary" />
              <h3 className="text-xl font-medium text-wrap">{t(card.title)}</h3>
            </div>
            <p className="text-wrap text-muted text-lg font-normal">
              {t(card.description)}
            </p>
          </Button>
        ))}
      </div>

      <WorkspaceSelectorModal
        isOpen={isWorkspaceSelectorModalOpen}
        onClose={() => {
          setIsWorkspaceSelectorModalOpen(false);
          setSelectedFeatureCard(null);
        }}
        onOpenUploadModalForWorkspace={handleOpenUploadModal}
      />

      {isManageWorkspaceModalOpen && manageWorkspaceSlug && (
        <ManageWorkspace
          hideModal={(navigateToWorkspace = false) => {
            setIsManageWorkspaceModalOpen(false);

            if (navigateToWorkspace && manageWorkspaceSlug) {
              window.location.href = `${paths.workspace.chat(manageWorkspaceSlug)}?openModal=performLegalTask`;
            }

            setManageWorkspaceSlug(null);
            setSelectedFeatureCard(null);
          }}
          providedSlug={manageWorkspaceSlug}
        />
      )}
    </>
  );
};

export default FeatureCards;
