export default {
  "use-guide": {
    button: "Use Guide",
    title: "How to use this platform",

    // Common labels
    labels: {
      requirements: "Requirements",
      availability: "Availability",
      note: "Note",
      shortcut: "Shortcut",
      usage: "Usage",
      icon: "Icon",
      features: "Features",
    },

    // Main sections
    sections: {
      overview: "Platform Overview",
      modules: "Platform Modules",
      templateGeneration: "Template Generation",
      documentBuilder: "Complex Document Builder",
      workspace: "Workspace Management",
      messageTools: "Message Tools",
      promptInput: "Prompt Input Features",
      newsSystem: "News and Updates",
      userAccount: "User Account & Settings",
      version: "Version Information",
    },

    // Platform Overview
    overview: {
      title: "Platform Overview",
      description:
        "This platform is a comprehensive legal AI solution that helps you with legal research, document analysis, and document drafting. The platform offers different modules depending on your needs and the system configuration.\n\nThe AI engines used in the platform are regularly updated and continuously improved to maximize precision. However, note that so-called hallucinations may occur, meaning inaccuracies in the generated responses.\n\nTo facilitate identification and management of this, the platform has both a function to view the sources that were applied, and a validation button that can be used to get a report reviewing factual claims in a response compared to the sources available in the platform.\n\nThis is a selection of the available features; below is a review of various additional key functions in the platform.",
    },

    // Module Information
    modules: {
      title: "Platform Modules",
      description:
        "The platform offers two main modules, each designed for specific legal workflows:",

      legalQA: {
        title: "Legal Q&A Module",
        description:
          "The base module containing legal data that cannot be changed by users. System administrators update this module with data from the local jurisdiction.",
        features: [
          "Access to pre-loaded legal databases and jurisdictional information",
          "Ask questions about laws, regulations, and legal procedures",
          "Get answers based on authoritative legal sources",
          "Cannot upload or modify the legal database content",
          "Ideal for legal research and quick legal questions",
        ],
        usage:
          "Select 'Legal Q&A' from the module selector to access this feature.",
      },

      documentDrafting: {
        title: "Document Drafting Module",
        description:
          "When enabled, this module allows users to create their own workspaces with stored data. All uploaded files are processed entirely when running prompts.",
        features: [
          "Create and manage your own workspaces",
          "Upload documents, contracts, and legal files",
          "All uploaded files are fully processed for each query",
          "Generate documents based on your uploaded content",
          "Perform complex legal tasks using your documents",
        ],
        localAI: {
          title: "Local AI Mode",
          description:
            "If the platform has Local AI enabled, this module is called 'Local AI' and all requests are processed entirely with platform-internal AI with no external communication.",
          benefits: [
            "Complete data privacy - no external API calls",
            "All processing happens on the platform's servers",
            "Ideal for sensitive legal documents",
            "No data leaves your organization",
          ],
        },
        usage:
          "Select 'Document Drafting' (or 'Local AI' if enabled) from the module selector to access this feature.",
      },
    },

    // Template Generation
    templateGeneration: {
      title: "Template Generation",
      description:
        "The platform provides powerful template generation capabilities for creating standardized legal documents efficiently.",

      overview: {
        title: "Template System Overview",
        description:
          "Templates allow you to create reusable document structures with predefined content, placeholders, and formatting. This ensures consistency across documents and speeds up the drafting process.",
      },

      usingTemplates: {
        title: "How to Use Templates",
        description: "Access and use existing templates in your workflow:",
        steps: [
          "Navigate to the Legal Templates section in the prompt input area",
          "Browse available templates by category (contracts, agreements, legal memos, etc.)",
          "Select the template that matches your document type",
          "Fill in the required information and customize as needed",
          "The AI will generate a complete document based on the template structure",
          "Review and edit the generated document to meet your specific requirements",
        ],
        tips: [
          "Templates include standard legal language and formatting",
          "Placeholders guide you on what information to provide",
          "Generated documents maintain professional legal structure",
          "Templates can be used as starting points for further customization",
        ],
      },

      customTemplates: {
        title: "Adding Custom Templates",
        description:
          "Create your own templates for frequently used document types:",

        creationProcess: {
          title: "Creating Custom Templates",
          steps: [
            "Access the Custom Templates section in your workspace settings",
            "Click 'Create New Template' to start the template creation process",
            "Define the template structure with sections, headings, and content blocks",
            "Add placeholders for variable information (client names, dates, amounts, etc.)",
            "Include standard legal clauses and boilerplate text",
            "Set template metadata (name, category, description, tags)",
            "Test the template with sample data to ensure proper functionality",
            "Save and make the template available for use",
          ],
        },

        management: {
          title: "Managing Custom Templates",
          features: [
            "Edit existing custom templates to improve or update content",
            "Organize templates by categories and tags for easy discovery",
            "Share templates with team members or keep them private",
            "Version control to track changes and maintain template history",
            "Import/export templates for backup or sharing across systems",
            "Set access permissions for different user roles",
          ],
        },

        bestPractices: {
          title: "Template Best Practices",
          guidelines: [
            "Use clear, descriptive names for templates and placeholders",
            "Include comprehensive instructions for template users",
            "Maintain consistent formatting and structure across templates",
            "Regular review and update templates to reflect current legal standards",
            "Test templates thoroughly before making them available to others",
            "Document template purpose and intended use cases",
          ],
        },
      },

      benefits: {
        title: "Benefits of Template Generation",
        advantages: [
          "Ensures consistency across all generated documents",
          "Reduces time spent on repetitive document creation",
          "Minimizes errors through standardized content",
          "Maintains compliance with legal formatting requirements",
          "Enables rapid document production for common legal tasks",
          "Facilitates knowledge sharing within legal teams",
        ],
      },
    },

    // Complex Document Builder
    documentBuilder: {
      title: "Complex Document Builder",
      description:
        "The complex document builder allows creating of any kind of legal document, based on files that have been uploaded. For example, creating a statement of claim or a due diligence report. There is no limitation on the amount of files uploaded. The kind of legal documents for creating is customized by the system administrator. For using this function, initiate from the home page card or the action button in the document drafting module, and ensure that all files have been uploaded in the workspace before initiating the document building task.",

      overview: {
        title: "Document Builder Overview",
        description:
          "The Complex Document Builder goes beyond simple templates to provide a comprehensive document creation environment. It allows for the construction of multi-part documents with conditional logic, cross-references, and advanced formatting.",
      },

      features: {
        title: "Key Features",
        capabilities: [
          "Multi-section document creation with hierarchical structure",
          "Conditional content based on user inputs and document type",
          "Automatic cross-referencing between document sections",
          "Advanced formatting with legal citation styles",
          "Integration with legal databases for clause libraries",
          "Real-time collaboration features for team document creation",
          "Version control and change tracking throughout the drafting process",
          "Export capabilities to multiple formats (PDF, Word, etc.)",
        ],
      },

      usage: {
        title: "How to Use the Document Builder",
        description: "Step-by-step guide to creating complex documents:",

        gettingStarted: {
          title: "Getting Started",
          steps: [
            "Access the Document Builder from the main navigation menu",
            "Choose a document type or start with a blank document",
            "Define the document structure with main sections and subsections",
            "Set up document metadata (title, parties, dates, jurisdiction)",
          ],
        },

        buildingContent: {
          title: "Building Document Content",
          steps: [
            "Add content blocks for different types of information",
            "Insert legal clauses from the integrated clause library",
            "Set up conditional logic for variable content",
            "Configure cross-references between sections",
            "Apply formatting and styling according to legal standards",
            "Review document structure and content flow",
          ],
        },

        collaboration: {
          title: "Collaboration Features",
          capabilities: [
            "Real-time editing with multiple team members",
            "Comment and suggestion system for document review",
            "Role-based permissions for different editing levels",
            "Track changes and maintain revision history",
            "Approval workflows for document finalization",
          ],
        },
      },

      documentTypes: {
        title: "Supported Document Types",
        types: [
          "Complex contracts with multiple parties and conditions",
          "Legal briefs with citations and appendices",
          "Corporate governance documents",
          "Regulatory compliance documentation",
          "Multi-jurisdictional legal agreements",
          "Structured legal opinions and memoranda",
        ],
      },

      advancedFeatures: {
        title: "Advanced Features",
        features: [
          "AI-powered content suggestions based on document context",
          "Automated legal citation formatting and verification",
          "Integration with external legal databases and resources",
          "Custom workflow automation for document approval processes",
          "Advanced search and replace with legal-specific patterns",
          "Document comparison and merger capabilities",
          "Automated table of contents and index generation",
          "Legal compliance checking against jurisdiction requirements",
        ],
      },

      benefits: {
        title: "Benefits of the Complex Document Builder",
        advantages: [
          "Streamlines creation of sophisticated legal documents",
          "Ensures consistency and accuracy in complex document structures",
          "Reduces time spent on formatting and cross-referencing",
          "Facilitates collaboration among legal teams",
          "Maintains compliance with legal document standards",
          "Provides comprehensive audit trail for document creation",
          "Enables reuse of document components across projects",
          "Integrates seamlessly with existing legal workflows",
        ],
      },
    },

    // Workspace Management
    workspace: {
      title: "Workspace Management",
      description:
        "Workspaces help you organize your documents and conversations by case, client, or legal matter.",

      creating: {
        title: "Creating Workspaces",
        steps: [
          "Choose the appropriate module type ({{tabName1}} or {{tabName2}})",
          "Click the '+' button in the sidebar or select 'Create New Workspace' when prompted",
          "Enter a descriptive name for your workspace (e.g., 'Smith vs. Jones Contract Review')",
          "Upload relevant documents if using {{tabName2}} module",
        ],
      },

      managing: {
        title: "Managing Workspaces",
        features: [
          "Switch between workspaces using the sidebar",
          "Upload additional documents to existing workspaces",
          "View and manage workspace settings",
          "Delete workspaces when no longer needed",
        ],
      },

      uploading: {
        title: "Uploading Documents",
        description:
          "In Document Drafting workspaces, you can upload various document types:",
        supportedFormatsTitle: "Supported Formats:",
        supportedFormats: [
          "PDF documents",
          "Word documents (.docx)",
          "Text files (.txt)",
          "Other common document formats",
        ],
        tipsTitle: "Tips:",
        tips: [
          "Upload all relevant documents before starting your legal task",
          "Organize documents by relevance to your case",
          "Ensure document quality for better AI analysis",
        ],
      },
    },

    // Message Tools
    messageTools: {
      title: "Message Tools",
      description:
        "Each message in the chat has various tools available for interaction:",

      editMessage: {
        title: "Edit Message",
        description:
          "Modify your prompts or AI responses after they've been sent",
        icon: "Pencil icon",
        usage: "Click the pencil icon next to any message to edit its content",
        note: "",
        availability: "",
      },

      copyMessage: {
        title: "Copy Message",
        description: "Copy message content to your clipboard",
        icon: "Copy icon",
        usage: "Click the copy icon to copy the message text",
        note: "",
        availability: "",
      },

      regenerateResponse: {
        title: "Regenerate Response",
        description: "Ask the AI to generate a new response to your prompt",
        icon: "Refresh icon",
        usage:
          "Available on the last AI response - click to get an alternative answer",
        note: "Only available for admin and manager users",
        availability: "",
      },

      deleteMessage: {
        title: "Delete Message",
        description: "Remove a message from the conversation",
        icon: "Trash icon",
        usage: "Click the delete icon and confirm to remove the message",
        note: "",
        availability: "",
      },

      exportToWord: {
        title: "Export to Word",
        description: "Export message content as a Word document",
        icon: "Document icon",
        usage:
          "Click the document icon to download the message as a .docx file",
        note: "",
        availability: "",
      },

      textEditing: {
        title: "Text Editing",
        description:
          "Edit message content with rich text formatting using a full-featured editor",
        icon: "Edit icon",
        usage:
          "Click the text editing icon to open the message in a TipTap editor with formatting options like bold, italic, lists, tables, and more. Make your changes and save to update the message",
        note: "",
        availability:
          "Available for all message types in both Legal Q&A and Document Drafting modules",
      },

      textToSpeech: {
        title: "Text-to-Speech",
        description: "Have the AI response read aloud",
        icon: "Speaker icon",
        usage: "Click the speaker icon to hear the message spoken aloud",
        note: "",
        availability: "",
      },

      citations: {
        title: "View Citations",
        description: "See the source documents used for the AI response",
        icon: "Citation icon",
        usage:
          "Click to view which documents were referenced in generating the response",
        note: "",
        availability: "",
      },

      canvasChat: {
        title: "Canvas Chat",
        description: "Edit and refine AI responses interactively",
        icon: "Canvas icon",
        usage:
          "Open an interactive editor to modify and improve the AI response",
        note: "",
        availability: "",
      },

      validateResponse: {
        title: "Validate Response",
        description: "Review and validate the accuracy of AI responses",
        icon: "Checkmark icon",
        usage: "Available for the last AI response to mark it as validated",
        note: "",
        availability: "",
      },

      manualWorkEstimator: {
        title: "Manual Work Estimator",
        description:
          "Estimate the time required to complete the legal work manually",
        icon: "Clock icon",
        usage:
          "Available for Legal Q&A responses - provides time estimates for manual completion",
        note: "",
        availability:
          "Only available for admin and manager users in Legal Q&A module",
      },
    },

    // Prompt Input Features
    promptInput: {
      title: "Prompt Input Features",
      description:
        "The prompt input area contains various tools to enhance your interactions:",

      speechToText: {
        title: "Speech-to-Text",
        description: "Dictate your prompts using voice input",
        icon: "Microphone icon",
        usage: "Click the microphone or press Ctrl+M to start voice dictation",
        shortcut: "Ctrl + M",
        requirements: "",
        availability: "",
      },

      attachFiles: {
        title: "Attach Files",
        description: "Upload documents to include in your prompt",
        icon: "Paperclip icon",
        usage: "Click to select and upload files relevant to your question",
        shortcut: "",
        requirements: "",
        availability: "",
      },

      resetConversation: {
        title: "Reset Conversation",
        description: "Clear the conversation history and start fresh",
        icon: "Restart icon",
        usage: "Click to reset the current conversation thread",
        shortcut: "",
        requirements: "",
        availability: "",
      },

      slashCommands: {
        title: "Slash Commands",
        description: "Access special commands and presets",
        icon: "Slash icon",
        usage:
          "Type '/' in the input field or click the slash icon to see available commands",
        shortcut: "",
        requirements: "",
        availability: "",
      },

      textSize: {
        title: "Text Size",
        description: "Adjust the text size in the chat interface",
        icon: "Text size icon",
        usage: "Click to change the font size for better readability",
        shortcut: "",
        requirements: "",
        availability: "",
      },

      upgradePrompt: {
        title: "Upgrade Prompt",
        description: "Enhance your prompt with AI assistance",
        icon: "Lightbulb icon",
        usage:
          "Available when your prompt could be improved - click to get suggestions",
        shortcut: "",
        requirements: "Requires at least 10 characters and 3 words",
        availability: "",
      },

      customAI: {
        title: "Custom AI Engine",
        description: "Select different AI models for your queries",
        icon: "Settings icon",
        usage:
          "Choose from available AI models for different types of responses",
        shortcut: "",
        requirements: "",
        availability: "",
      },

      legalQuestion: {
        title: "Ask Legal Question",
        description: "Access structured legal task templates",
        icon: "Question icon",
        usage:
          "Available in Document Drafting - select from predefined legal task types",
        shortcut: "",
        requirements: "",
        availability: "",
      },

      importMemo: {
        title: "Import Memo",
        description: "Import existing legal memos into your workspace",
        icon: "Import icon",
        usage: "Available in Document Drafting - upload and import legal memos",
        shortcut: "",
        requirements: "",
        availability: "",
      },

      filesButton: {
        title: "View Attached Files",
        description: "See and manage files attached to your conversation",
        icon: "Files icon",
        usage:
          "Shows the number of attached files - click to view and manage them",
        shortcut: "",
        requirements: "",
        availability: "",
      },

      legalTemplates: {
        title: "Legal Templates",
        description: "Access pre-built legal document templates",
        icon: "Template icon",
        usage: "Click to browse and use legal document templates",
        shortcut: "",
        requirements: "",
        availability: "",
      },

      systemPrompt: {
        title: "System Prompt Settings",
        description: "Customize the AI's behavior and instructions",
        icon: "Settings icon",
        usage:
          "Available in Legal Q&A workspaces - customize how the AI responds",
        shortcut: "",
        requirements: "",
        availability: "Only available in Legal Q&A module",
      },

      deepSearch: {
        title: "Deep Search Toggle",
        description: "Enable enhanced search capabilities",
        icon: "Search icon",
        usage: "Toggle on for more comprehensive document analysis",
        shortcut: "",
        requirements: "",
        availability: "Available in Legal Q&A module",
      },

      submitPrompt: {
        title: "Submit Prompt",
        description: "Send your prompt to the AI",
        icon: "Arrow icon",
        usage: "Click the arrow button or press Enter to submit your prompt",
        shortcut: "Enter (Shift+Enter for new line)",
        requirements: "",
        availability: "",
      },

      stopGeneration: {
        title: "Stop Generation",
        description: "Stop the AI from generating a response",
        icon: "Stop icon",
        usage: "Click to halt response generation while the AI is responding",
        shortcut: "",
        requirements: "",
        availability: "",
      },
    },

    // News and Updates System
    newsSystem: {
      title: "News and Updates",
      description:
        "Stay informed about platform updates and announcements through the news system.",

      overview: {
        title: "How News Works",
        description:
          "News items are published once and appear as notifications. Once you dismiss a news item, it won't appear automatically again, but you can always view it later through the header icon.",
      },

      newsIndicator: {
        title: "News Notifications",
        description: "New announcements appear with a notification badge:",
        features: [
          "News bell icon in the header shows when new items are available",
          "Red badge displays the number of unread news items",
          "High-priority news may appear as automatic popups",
        ],
        usage: "Click the bell icon in the header to view all news items",
      },

      newsHeaderItem: {
        title: "Viewing All News",
        description: "Access all news through the header icon:",
        icon: "Newspaper icon in the header bar",
        usage:
          "Click the newspaper icon to view all news items, including previously dismissed ones",
        features: [
          "View all active and dismissed news items",
          "Filter between active and dismissed news",
          "Click the eye icon to read full news content",
          "Dismiss news items you've read",
        ],
        note: "Dismissed news items remain accessible for reference but won't notify you again",
        availability: "Available to all users in the header bar",
      },
    },

    // User Account & Settings
    userAccount: {
      title: "User Account & Settings",
      description: "Manage your personal account settings and preferences:",

      accountSettings: {
        title: "Account Settings",
        description:
          "Access your account settings through the user menu in the top-right corner",
        features: [
          "Update your email address",
          "Change your password",
          "Set your preferred language",
          "Configure auto-submit preferences",
          "Set auto-speak preferences",
        ],
        access:
          "Click your user icon in the top-right corner and select 'Account'",
      },

      styleAlignment: {
        title: "Personal Style Alignment",
        description:
          "Customize the AI's writing style to match your professional preferences",

        overview:
          "Upload examples of your own documents to generate a personalized style profile. When active, AI outputs will align more closely with your personal professional writing style.",

        features: {
          title: "Style Profile Features",
          list: [
            "Upload document samples for style analysis",
            "Generate personalized style instructions",
            "Create multiple style profiles",
            "Toggle style alignment on/off",
            "Edit and manage existing profiles",
          ],
        },

        howToUse: {
          title: "How to Use Style Alignment",
          steps: [
            "Click your user icon and select 'Personal Style Alignment'",
            "Upload examples of documents you've written (DOCX format recommended)",
            "Click 'Create Style Profile from Uploaded Documents'",
            "Wait for the AI to analyze your writing style",
            "Save the profile with a descriptive name",
            "Toggle 'Enable Personal Style' to activate",
            "Your future AI responses will match your writing style",
          ],
        },

        management: {
          title: "Managing Style Profiles",
          features: [
            "Create multiple profiles for different types of documents",
            "Edit profile names and style instructions",
            "Set one profile as active at a time",
            "View when each profile was created",
            "Delete profiles you no longer need",
          ],
        },

        tips: {
          title: "Style Alignment Tips",
          list: [
            "Upload high-quality, well-written documents for best results",
            "Use documents that represent your preferred writing style",
            "Create different profiles for different document types (contracts, memos, etc.)",
            "Review and edit the generated style instructions if needed",
            "Test the style alignment with different types of prompts",
          ],
        },

        access:
          "Click your user icon in the top-right corner and select 'Personal Style Alignment'",
      },

      languageSettings: {
        title: "Language Settings",
        description: "The platform supports multiple languages:",
        supportedLanguages: [
          "English",
          "French",
          "Swedish",
          "German",
          "Norwegian",
          "Polish",
          "Kinyarwanda",
        ],
        usage: "Change your language preference in Account Settings",
      },
    },

    // Getting Started
    gettingStarted: {
      title: "Getting Started",
      steps: [
        "Choose your module: {{tabName1}} for research or {{tabName2}} for custom work",
        "Create or select a workspace to organize your work",
        "Upload relevant documents ({{tabName2}} module only)",
        "Check for any news updates or announcements in the news system",
        "Use the chat interface to ask questions or request document generation",
        "Utilize message tools to refine and work with AI responses",
        "Configure your personal style alignment for consistent output",
        "Explore advanced features like templates and legal tasks",
      ],
    },

    // Tips and Best Practices
    tips: {
      title: "Tips and Best Practices",
      list: [
        "Be specific and detailed in your prompts for better results",
        "Upload all relevant documents before starting complex legal tasks",
        "Use the appropriate module for your task type",
        "Stay informed by regularly checking the news system for updates",
        "Take advantage of style alignment for consistent professional output",
        "Utilize message tools to refine and improve AI responses",
        "Organize your work using descriptive workspace names",
        "Review and validate AI responses for accuracy",
        "Use templates for common document types",
        "Dismiss news items after reading to keep your feed organized",
        "Check the version number in the user dropdown menu to see current platform version and recent updates",
      ],
    },

    // =========================
    // VERSION DISPLAY
    // =========================
    version: {
      title: "Version Information",
      description:
        "The current platform version is displayed in the user dropdown menu in the header.",
      features: [
        "Shows the current version number (e.g., v1.1.0)",
        "Hover over the version to see a description of recent updates",
        "Helps you stay informed about platform improvements and new features",
        "Easily accessible from the user menu",
      ],
      usage:
        "Click your user icon in the top-right corner to open the dropdown menu. The version number is displayed at the bottom of the menu. Hover over it to see what's new in the current version.",
    },
  },
};
