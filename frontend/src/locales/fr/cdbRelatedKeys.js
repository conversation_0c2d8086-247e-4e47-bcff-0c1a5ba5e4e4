export default {
  // =========================
  // DOCUMENT BUILDER SETTINGS
  // =========================

  // Document Builder Page
  "document-builder": {
    title: "Invites du Générateur de Documents",
    description:
      "Personnalisez les invites par défaut utilisées par la fonctionnalité Générateur de Documents.",
    "override-prompt-placeholder":
      "Entrez une invite pour remplacer l'invite système par défaut",
    saving: "Enregistrement...",
    save: "Enregistrer les Paramètres d'Invite",
    "toast-success":
      "Invites du générateur de documents enregistrées avec succès.",
    "toast-fail":
      "Échec de l'enregistrement des invites du générateur de documents.",

    // =========================
    // LEGAL DRAFTING PROMPTS
    // =========================
    prompts: {
      group: {
        document_summary: {
          title: "Invites pour Résumé de Document",
          description:
            "Configurez les invites système et utilisateur pour le Résumé de Document.",
        },
        document_relevance: {
          title: "Invites pour Pertinence du Document",
          description:
            "Configurez les invites système et utilisateur pour la Pertinence du Document.",
        },
        section_drafting: {
          title: "Invites pour Rédaction de Section",
          description:
            "Configurez les invites système et utilisateur pour la Rédaction de Section.",
        },
        section_legal_issues: {
          title: "Invites pour Problèmes Juridiques de Section",
          description:
            "Configurez les invites système et utilisateur pour les Problèmes Juridiques de Section.",
        },
        memo_creation: {
          title: "Invites pour Création de Mémo",
          description: "Configurez les invites pour la Création de Mémo.",
        },
        section_index: {
          title: "Invites pour Index des Sections",
          description: "Configurez les invites pour l'Index des Sections.",
        },
        select_main_document: {
          title: "Invites pour Sélection du Document Principal",
          description:
            "Configurez les invites système et utilisateur pour la Sélection du Document Principal.",
        },
        section_list_from_main: {
          title:
            "Invites pour Liste des Sections à partir du Document Principal",
          description:
            "Configurez les invites système et utilisateur pour la Liste des Sections à partir du Document Principal.",
        },
        section_list_from_summaries: {
          title: "Invites pour Liste des Sections à partir des Résumés",
          description:
            "Configurez les invites système et utilisateur pour la Liste des Sections à partir des Résumés.",
        },
        reference_files_description: {
          title: "Invites pour Description des Fichiers de Référence",
          description:
            "Configurez les invites système et utilisateur pour la Description des Fichiers de Référence.",
        },
        review_files_description: {
          title: "Invites pour Description des Fichiers de Révision",
          description:
            "Configurez les invites système et utilisateur pour la Description des Fichiers de Révision.",
        },
        reference_review_sections: {
          title: "Invites pour Sections de Référence/Révision",
          description:
            "Configurez les invites système et utilisateur pour les Sections de Référence/Révision.",
        },
      },
      // Document Summary
      "document-summary-system-label": "Résumé de document (Système)",
      "document-summary-system-description":
        "Invite système instruisant l'IA sur la façon de résumer le contenu d'un document et sa pertinence pour une tâche juridique.",
      "document-summary-user-label": "Résumé de document (Utilisateur)",
      "document-summary-user-description":
        "Modèle d'invite utilisateur pour générer un résumé détaillé du contenu du document par rapport à une tâche juridique spécifique.",
      // Reference Files Description Prompts
      "reference-files-description-system-label":
        "Description des Fichiers de Référence (Système)",
      "reference-files-description-system-description":
        "Invite système pour décrire les fichiers de référence.",
      "reference-files-description-user-label":
        "Description des Fichiers de Référence (Utilisateur)",
      "reference-files-description-user-description":
        "Modèle d'invite utilisateur pour décrire les fichiers de référence.",

      // Document Relevance
      "document-relevance-system-label": "Pertinence du document (Système)",
      "document-relevance-system-description":
        "Invite système pour évaluer si un document est pertinent pour une tâche juridique, avec une réponse vrai/faux attendue.",
      "document-relevance-user-label": "Pertinence du document (Utilisateur)",
      "document-relevance-user-description":
        "Modèle d'invite utilisateur pour vérifier si le contenu du document est pertinent pour une tâche juridique donnée.",

      // Section Drafting
      "section-drafting-system-label": "Rédaction de section (Système)",
      "section-drafting-system-description":
        "Invite système pour générer une section de document unique dans un style juridique professionnel en utilisant des documents et un contexte spécifiés.",
      "section-drafting-user-label": "Rédaction de section (Utilisateur)",
      "section-drafting-user-description":
        "Modèle d'invite utilisateur pour générer une section spécifique d'un document juridique, en tenant compte du titre, de la tâche, des documents sources et des sections adjacentes.",

      // Section Legal Issues
      "section-legal-issues-system-label":
        "Identification des problèmes juridiques de section (Système)",
      "section-legal-issues-system-description":
        "Invite système pour identifier des sujets juridiques spécifiques pour lesquels des informations factuelles devraient être récupérées pour soutenir la rédaction d'une section de document.",
      "section-legal-issues-user-label":
        "Identification des problèmes juridiques de section (Utilisateur)",
      "section-legal-issues-user-description":
        "Modèle d'invite utilisateur pour lister les sujets juridiques ou les points de données pour récupérer des informations contextuelles pertinentes pour une section de document spécifique et une tâche juridique.",

      // Memo Creation
      "memo-creation-template-label": "Modèle de création de mémo par défaut",
      "memo-creation-template-description":
        "Modèle d'invite pour créer un mémorandum juridique abordant un problème juridique spécifique, en tenant compte des documents fournis et du contexte de la tâche.",

      // Section Index
      "section-index-system-label": "Index des sections (Système)",
      "section-index-system-description":
        "Invite système pour générer un index structuré des sections pour un document juridique.",

      // Select Main Document
      "select-main-document-system-label":
        "Sélection du document principal (Système)",
      "select-main-document-system-description":
        "Invite système pour identifier le document principal le plus pertinent pour une tâche juridique à partir de plusieurs résumés de documents.",
      "select-main-document-user-label":
        "Sélection du document principal (Utilisateur)",
      "select-main-document-user-description":
        "Modèle d'invite utilisateur pour identifier le document principal pour une tâche juridique basé sur des résumés de plusieurs documents.",

      // Section List from Main Document
      "section-list-from-main-system-label":
        "Liste des sections à partir du document principal (Système)",
      "section-list-from-main-system-description":
        "Invite système pour créer une liste structurée JSON des sections pour un document juridique basée sur le contenu du document principal et la tâche juridique.",
      "section-list-from-main-user-label":
        "Liste des sections à partir du document principal (Utilisateur)",
      "section-list-from-main-user-description":
        "Modèle d'invite utilisateur pour fournir la tâche juridique et le contenu du document principal afin de générer une liste de sections.",

      // Section List from Summaries
      "section-list-from-summaries-system-label":
        "Liste des sections à partir des résumés (Système)",
      "section-list-from-summaries-system-description":
        "Invite système pour créer une liste structurée JSON des sections basée sur des résumés de documents et la tâche juridique, lorsqu'aucun document principal n'existe.",
      "section-list-from-summaries-user-label":
        "Liste des sections à partir des résumés (Utilisateur)",
      "section-list-from-summaries-user-description":
        "Modèle d'invite utilisateur pour fournir la tâche juridique et les résumés de documents afin de générer une liste de sections, lorsqu'aucun document principal n'existe.",
      // Review Files Description Prompts
      "review-files-description-system-label":
        "Description des Fichiers de Révision (Système)",
      "review-files-description-system-description":
        "Invite système pour décrire les fichiers de révision.",
      "review-files-description-user-label":
        "Description des Fichiers de Révision (Utilisateur)",
      "review-files-description-user-description":
        "Modèle d'invite utilisateur pour décrire les fichiers de révision.",

      // Reference/Review Sections Prompts
      "reference-review-sections-system-label":
        "Sections de Référence/Révision (Système)",
      "reference-review-sections-system-description":
        "Invite système pour définir les sections de référence/révision.",
      "reference-review-sections-user-label":
        "Sections de Référence/Révision (Utilisateur)",
      "reference-review-sections-user-description":
        "Modèle d'invite utilisateur pour définir les sections de référence/révision.",
    },

    // =========================
    // ADD/EDIT LEGAL TASK KEYS
    // =========================
    "view-categories": "Voir toutes les catégories",
    "hide-categories": "Masquer la liste",
    "add-task": "Ajouter une tâche juridique",
    loading: "Chargement...",
    table: {
      title: "Tâches Juridiques",
      name: "Nom",
      "sub-category": "Sous-Catégorie",
      description: "Description",
      prompt: "Invite de Tâche Juridique",
      actions: "Actions",
      delete: "Supprimer",
      "delete-confirm": "Êtes-vous sûr de vouloir supprimer cette catégorie ?",
      "delete-success": "Catégorie supprimée",
      "delete-error": "Échec de la suppression de la catégorie",
    },
    "create-task-title": "Créer une tâche juridique",
    "category-name": "Nom de la Catégorie",
    "category-name-desc": "Entrez le nom de la catégorie principale.",
    "category-name-placeholder": "Entrez le nom de la catégorie",
    "subcategory-name": "Nom de la Sous-Catégorie",
    "subcategory-name-desc": "Entrez le nom de la sous-catégorie.",
    "subcategory-name-placeholder": "Entrez le nom de la sous-catégorie",
    "description-desc":
      "Entrez une description de la catégorie et de la sous-catégorie.",
    "description-placeholder": "Entrez une brève description",
    submitting: "Envoi en cours...",
    submit: "Soumettre",
    validation: {
      "category-required": "Le nom de la catégorie est requis.",
      "subcategory-required": "Le nom de la sous-catégorie est requis.",
      "description-required": "La description est requise.",
      "prompt-required": "L'invite de tâche juridique est requise.",
    },
    "create-task": {
      title: "Créer une tâche juridique",
      category: {
        name: "Nom de la Catégorie",
        desc: "Entrez le nom de la catégorie.",
        placeholder: "Entrez le nom de la catégorie",
        type: "Type de Catégorie",
        new: "Créer une nouvelle catégorie",
        existing: "Utiliser une catégorie existante",
        select: "Sélectionner une catégorie",
        "select-placeholder": "Sélectionner une catégorie existante",
      },
      subcategory: {
        name: "Nom de la Sous-Catégorie",
        desc: "Entrez le nom de la sous-catégorie.",
        placeholder: "Entrez le nom de la sous-catégorie",
      },
      description: {
        name: "Description et instructions utilisateur",
        desc: "Informations et instructions que l'utilisateur verra.",
        placeholder:
          "Décrivez le type de documents qui doivent être téléchargés dans l'espace de travail pour obtenir le meilleur résultat possible",
      },
      prompt: {
        name: "Invite de Tâche Juridique",
        desc: "Entrez l'invite qui sera utilisée pour cette tâche juridique. Vous pouvez également télécharger des documents d'exemple avec les boutons pour ajouter des exemples de contenu à votre invite.",
        placeholder:
          "Entrez l'invite de tâche juridique ou téléchargez des documents d'exemple pour améliorer votre invite...",
      },
      submitting: "Envoi en cours...",
      submit: "Soumettre",
      validation: {
        "category-required": "Le nom de la catégorie est requis.",
        "subcategory-required": "Le nom de la sous-catégorie est requis.",
        "description-required": "La description est requise.",
        "prompt-required": "L'invite de tâche juridique est requise.",
        "legal-task-type-required": "Le type de tâche juridique est requis.",
      },
    },
    "edit-task": {
      title: "Modifier la tâche juridique",
      submitting: "Mise à jour...",
      submit: "Mettre à jour la tâche",
      subcategory: {
        name: "Nom de la Sous-Catégorie",
        desc: "Entrez un nouveau nom pour cette tâche juridique",
        placeholder: "Entrez la tâche juridique...",
      },
      description: {
        name: "Description et instructions utilisateur",
        desc: "Entrez une description et des instructions utilisateur pour cette tâche juridique",
        placeholder: "Entrez la description et les instructions utilisateur...",
      },
      prompt: {
        name: "Invite de Tâche Juridique",
        desc: "Entrez l'invite qui sera utilisée pour cette tâche juridique. Vous pouvez également télécharger des documents d'exemple avec les boutons pour ajouter des exemples de contenu à votre invite.",
        placeholder:
          "Entrez l'invite de tâche juridique ou téléchargez des documents d'exemple pour améliorer votre invite...",
      },
      validation: {
        "subcategory-required": "Le nom de la tâche juridique est requis",
        "description-required": "La description est requise",
        "prompt-required": "L'invite de tâche juridique est requise",
        "legal-task-type-required": "Le type de tâche juridique est requis",
      },
    },
    "task-form": {
      "requires-main-doc-label": "Sélection du Document Principal Requise",
      "requires-main-doc-description":
        "Si coché, l'utilisateur doit sélectionner le document principal parmi les fichiers téléchargés lorsque cette tâche est exécutée. Ceci est fortement recommandé pour les tâches juridiques qui impliquent de répondre à une lettre ou à une ordonnance du tribunal ou similaire, car cela structure le résultat basé sur le document auquel on répond.",
      "requires-main-doc-placeholder": "Oui ou Non",
      "requires-main-doc-explanation-default":
        "Une sélection est requise car cela détermine comment le document sera construit.",
      "requires-main-doc-explanation-yes":
        "Si 'Oui', l'utilisateur devra sélectionner un document principal lorsque cette tâche juridique est démarrée. Ce document sera central au flux de travail de la tâche.",
      "requires-main-doc-explanation-no":
        "Si 'Non', la tâche juridique continuera sans nécessiter un document principal par défaut. La tâche créera un résultat plus dynamiquement basé sur tous les documents téléchargés et la tâche juridique.",
      "legal-task-type-label": "Type de Tâche Juridique",
      "legal-task-type-placeholder": "Sélectionnez un type de tâche juridique",
      option: {
        mainDoc: "Flux de Document Principal",
        noMainDoc: "Flux sans Document Principal",
        referenceFiles: "Comparaison de Fichiers de Référence",
      },
      "legal-task-type-explanation":
        "Choisissez comment la tâche juridique doit traiter les documents.",
      "legal-task-type-explanation-mainDoc":
        "Ce flux nécessite de sélectionner un document principal avant de continuer.",
      "legal-task-type-explanation-noMainDoc":
        "Ce flux procède sans document principal.",
      "legal-task-type-explanation-referenceFiles":
        "Ce flux traite des groupes de règles et réglementations contre des documents de révision.",
    },

    // New keys for Review Generator Prompt feature
    reviewGeneratorPromptButton: "Examiner l'Invite du Générateur",
    reviewGeneratorPromptButtonTooltip:
      "Voir le modèle d'invite exact utilisé pour générer la suggestion de tâche juridique. (Admin uniquement)",
    reviewGeneratorPromptTitle: "Examen de l'Invite du Générateur",
    reviewPromptLabel: "L'invite suivante a été utilisée pour la génération :",
    reviewPromptTextareaLabel: "Contenu de l'Invite du Générateur",
  },

  // =========================
  // PERFORM LEGAL TASK
  // =========================
  performLegalTask: {
    title: "Exécuter une Tâche Juridique",
    noTaskfund: "Aucune tâche juridique disponible.",
    noSubtskfund: "Aucune sous-catégorie disponible.",
    "loading-subcategory": "Chargement des sous-catégories...",
    "select-category": "Sélectionner une catégorie",
    "choose-task": "Choisir une tâche juridique à exécuter",
    "duration-info":
      "Le temps pour exécuter une tâche juridique dépend du nombre de documents dans l'espace de travail. Avec de nombreux documents et une tâche complexe, cela peut prendre très longtemps.",
    description:
      "Activer ou désactiver le bouton d'exécution de tâche juridique dans la Rédaction de Documents.",
    successMessage: "L'exécution de tâche juridique a été {{status}}",
    failureUpdateMessage:
      "Échec de la mise à jour du paramètre d'exécution de tâche juridique.",
    errorSubmitting:
      "Erreur lors de la soumission des paramètres d'exécution de tâche juridique.",
    "additional-instructions-label": "Instructions Supplémentaires :",
    "custom-instructions-label": "Instructions Personnalisées",
    "custom-instructions-placeholder":
      "Entrez des instructions supplémentaires pour la tâche juridique (optionnel)...",
    "select-main-document-label": "Sélectionner le Document Principal (Requis)",
    "select-document-placeholder": "-- Sélectionner un document --",
    selectReferenceFilesLabel:
      "Sélectionner les Fichiers de Référence (Règles et Réglementations)",
    "warning-title": "Avertissement",
    "no-files-title": "Aucun Fichier Disponible",
    "no-files-description":
      "Il n'y a aucun fichier dans cet espace de travail. Veuillez télécharger au moins un fichier avant d'exécuter une tâche juridique.",
    "settings-button": "Ajouter ou modifier les tâches juridiques disponibles",
    settings: "Paramètres des Tâches Juridiques",
    subStep: "Étape en cours ou en file d'attente",
  },

  // =========================
  // LEGAL TASK PROMPT GENERATOR
  // =========================
  "legal-task-prompt-generator": {
    title: "Générateur d'invite utilisateur de tâche juridique",
    description:
      "Proposition automatique de l'invite personnalisée pour une tâche juridique",
    "task-description": "Description de la tâche juridique",
    "task-description-placeholder":
      "Décrivez la tâche juridique que vous voulez accomplir...",
    "specific-instructions": "Instructions spécifiques ou savoir-faire",
    "specific-instructions-description":
      "Incluez toutes instructions spéciales ou expertise spécifique à cette tâche juridique",
    "specific-instructions-placeholder":
      "Ajoutez des instructions spécifiques, de l'expertise ou du savoir-faire pour gérer cette tâche juridique...",
    "suggested-prompt": "Invite utilisateur suggérée",
    "generation-prompt": "Invite pour la génération",
    "create-task": "Créer une tâche juridique basée sur cette suggestion",
    generating: "Génération...",
    generate: "Générer une proposition",
    "toast-success": "Invite générée avec succès",
    "toast-fail": "Échec de la génération de l'invite",
    button: "Générer l'Invite",
    success: "Invite générée avec succès",
    error: "Veuillez d'abord entrer un nom ou une sous-catégorie",
    failed: "Échec de la génération de l'invite",
  },
};
