import { useState } from "react";
import { UploadSimple, CircleNotch } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";

export default function AttachmentWrapper({
  children,
  onDraggingChange,
  handleFiles,
  isUploading,
}) {
  const { t } = useTranslation();
  const [dragging, setDragging] = useState(false);

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.dataTransfer.types.includes("Files")) {
      setDragging(true);
      onDraggingChange?.(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.currentTarget.contains(e.relatedTarget)) return;
    setDragging(false);
    onDraggingChange?.(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragging(false);
    onDraggingChange?.(false);
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFiles(files);
    }
  };

  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      handleFiles(files);
    }
    // Reset input so same file can be re-uploaded
    e.target.value = "";
  };

  return (
    <div
      onDragEnter={handleDragEnter}
      onDragOver={(e) => {
        e.preventDefault();
        if (e.dataTransfer.types.includes("Files")) {
          setDragging(true);
          onDraggingChange?.(true);
        }
      }}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      className="h-full w-full relative"
    >
      <input
        type="file"
        id="dnd-chat-file-uploader"
        onChange={handleFileSelect}
        style={{ display: "none" }}
        multiple
      />
      {children}
      <div
        className={`absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center transition-all duration-200 ${
          dragging ? "opacity-100 visible" : "opacity-0 invisible"
        }`}
      >
        <div
          className={`bg-card p-8 rounded-lg shadow-lg text-center transform transition-transform duration-200 ${
            dragging ? "scale-100" : "scale-95"
          }`}
        >
          <UploadSimple className="mx-auto mb-4 text-primary" size={48} />
          <p className="text-foreground">
            {t("modale.document.drag-drop", "Click to upload or drag and drop")}
          </p>
        </div>
      </div>
      {isUploading && (
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-card p-6 rounded-lg shadow-lg flex items-center gap-3">
            <CircleNotch className="animate-spin text-primary" size={24} />
            <p className="text-foreground whitespace-nowrap">
              {t(
                "modale.document.loading-message",
                "This may take a while for large documents"
              )}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
