export default {
  // =========================
  // EMBEDDER (EMBEDDING PROVIDER NAMES)
  // =========================
  embeder: {
    allm: "Use the built-in embedding provider. Zero setup!",
    openai: "The standard option for most non-commercial use.",
    azure: "The enterprise option of OpenAI hosted on Azure services.",
    localai: "Run embedding models locally on your own machine.",
    ollama: "Run embedding models locally on your own machine.",
    lmstudio:
      "Discover, download, and run thousands of cutting edge LLMs in a few clicks.",
    cohere: "Run powerful embedding models from Cohere.",
    voyageai: "Run powerful embedding models from Voyage AI.",
    "generic-openai": "Use a generic OpenAI embedding model.",
    "default.embedder": "Default Embedder",
    jina: "Jina AI's text-embedding models for multilingual and high-performance embeddings.",
    litellm: "Run powerful embedding models from LiteLLM.",
  },
  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Common strings
    "provider-logo": "{{provider}} logo",

    // LMStudio Embedding Options
    lmstudio: {
      "model-label": "LM Studio Embedding Model",
      "max-chunk-length": "Maximum Chunk Length",
      "max-chunk-length-help": "Maximum length of text chunks for embedding.",
      "hide-endpoint": "Hide Manual Endpoint Input",
      "show-endpoint": "Show Manual Endpoint Input",
      "base-url": "LM Studio Base URL",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help": "Enter the URL where LM Studio is running.",
      "auto-detect": "Auto-Detect",
      "loading-models": "--loading available models--",
      "enter-url-first": "Enter LM Studio URL first",
      "model-help":
        "Select the LM Studio model for embeddings. Models will load after entering a valid LM Studio URL.",
      "loaded-models": "Your loaded models",
    },
    // Ollama Embedding Options
    ollama: {
      "model-label": "Ollama Embedding Model",
      "max-chunk-length": "Maximum Chunk Length",
      "max-chunk-length-help": "Maximum length of text chunks for embedding.",
      "hide-endpoint": "Hide Manual Endpoint Input",
      "show-endpoint": "Show Manual Endpoint Input",
      "base-url": "Ollama Base URL",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help": "Enter the URL where Ollama is running.",
      "auto-detect": "Auto-Detect",
      "loading-models": "--loading available models--",
      "enter-url-first": "Enter Ollama URL first",
      "model-help":
        "Select the Ollama model for embeddings. Models will load after entering a valid Ollama URL.",
      "loaded-models": "Your loaded models",
    },
    // LiteLLM Embedding Options
    litellm: {
      "model-label": "Embedding Model Selection",
      "max-chunk-length": "Maximum Chunk Length",
      "max-chunk-length-help": "Maximum length of text chunks for embedding.",
      "api-key": "API Key",
      optional: "optional",
      "api-key-placeholder": "sk-mysecretkey",
      "loading-models": "-- loading available models --",
      "waiting-url": "-- waiting for URL --",
      "loaded-models": "Your loaded models",
      "model-tooltip": "View supported embedding models at",
      "model-tooltip-link": "LiteLLM's documentation",
      "model-tooltip-more": "for more information about available models.",
    },
    // Cohere Embedding Options
    cohere: {
      "api-key": "Cohere API Key",
      "api-key-placeholder": "Enter your Cohere API Key",
      "model-label": "Model Selection",
      "available-models": "Available embedding models",
    },
    // Jina Embedding Options
    jina: {
      "api-key": "Jina API Key",
      "api-key-format": "Jina API Key must start with 'jina_'",
      "api-key-placeholder": "Enter your Jina API Key",
      "api-key-error": "API key must start with 'jina_'",
      "model-label": "Model Selection",
      "available-models": "Available Embedding Models",
      "embedding-type": "Embedding Type",
      "available-types": "Available Embedding Types",
      dimensions: "Dimensions",
      "available-dimensions": "Available Dimensions",
      task: "Task",
      "available-tasks": "Available Tasks",
      "late-chunking": "Late Chunking",
      "late-chunking-help": "Enable late chunking for document processing",
    },
    // LocalAI Embedding Options
    localai: {
      "model-label": "Embedding Model Name",
      "hide-endpoint": "Hide advanced settings",
      "show-endpoint": "Show advanced settings",
      "base-url": "LocalAI Base URL",
      "base-url-placeholder": "http://localhost:8080/v1",
      "base-url-help": "Enter the URL where LocalAI is running.",
      "auto-detect": "Auto-Detect",
      "loading-models": "-- loading available models --",
      "waiting-url": "-- waiting for URL --",
      "loaded-models": "Your loaded models",
    },
    // Generic OpenAI-Compatible Embedding Options
    generic: {
      "base-url": "Base URL",
      "base-url-placeholder": "https://api.openai.com/v1",
      "base-url-help":
        "Enter the base URL for your OpenAI-compatible API endpoint.",
      "model-label": "Embedding Model",
      "model-placeholder": "Enter the model name (e.g. text-embedding-ada-002)",
      "model-help": "Specify the model identifier for generating embeddings.",
      "api-key": "API Key",
      "api-key-placeholder": "sk-mysecretkey",
      "api-key-help": "Enter your API key for authentication.",
    },
    // OpenAI Embedding Options
    openai: {
      "api-key": "OpenAI API Key",
      "api-key-placeholder": "Enter your OpenAI API Key",
      "model-label": "Model Selection",
      "available-models": "Available embedding models",
    },
    // VoyageAI Embedding Options
    voyageai: {
      "api-key": "VoyageAI API Key",
      "api-key-placeholder": "Enter your VoyageAI API Key",
      "model-label": "Model Selection",
      "available-models": "Available embedding models",
    },
    // Azure OpenAI Embedding Options
    azureai: {
      "service-endpoint": "Azure OpenAI Service Endpoint",
      "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
      "service-endpoint-help": "Enter your Azure OpenAI service endpoint URL",
      "api-key": "Azure OpenAI API Key",
      "api-key-placeholder": "Enter your Azure OpenAI API Key",
      "api-key-help": "Enter your Azure OpenAI API key for authentication",
      "deployment-name": "Embedding Model Deployment Name",
      "deployment-name-placeholder":
        "Enter your Azure OpenAI embedding model deployment name",
      "deployment-name-help":
        "The deployment name for your Azure OpenAI embedding model",
    },
    // Native Embedding Options
    native: {
      description: "Using native embedding provider for text processing",
    },
  },
  // =========================
  // JINA EMBEDDING
  // =========================
  jina: {
    "api-key": "Jina API Key",
    "api-key-placeholder": "Enter your Jina API Key",
    "api-key-format": "Jina API key must start with 'jina_'",
    "model-preference": "Model Preference",
  },

  // =========================
  // OLLAMA EMBEDDING
  // =========================
  ollama: { "max-embedding-chunk-length": "Max Embedding Chunk Length" },

  // =========================
  // VOYAGEAI EMBEDDING
  // =========================
  voyageai: {
    "api-key": "VoyageAI API Key",
    "api-key-placeholder": "Enter your VoyageAI API Key",
    "model-preference": "Model Preference",
  },
};
