/**
 * System News Items Index
 *
 * This file fetches system news items from the server API.
 * The server is the single source of truth for all system news data.
 *
 * To add new system news:
 * 1. Add the news item to server/data/systemNewsItems.js
 * 2. Add translations to ALL language files in frontend/src/locales/[lang]/newsSystemItems.js
 *
 * IMPORTANT - Async Initialization Pattern:
 * This module uses an async initialization pattern to prevent race conditions.
 *
 * - systemNewsItems: A synchronous export that starts empty and gets populated asynchronously
 * - systemNewsInitialized: A function that returns a promise that resolves when systemNewsItems is fully loaded
 *
 * ENHANCED CACHING SYSTEM:
 * - Time-based cache expiration with configurable TTL (default: 5 minutes)
 * - Cache validation and integrity checking
 * - Graceful fallback to stale data during network errors
 * - Manual cache management functions for advanced use cases
 *
 * Usage Patterns:
 *
 * 1. PREFERRED - Use async functions (no race conditions):
 *    const news = await getActiveSystemNews();
 *    const roleNews = await getSystemNewsForRoles(['admin']);
 *
 * 2. If you need the synchronous systemNewsItems array:
 *    await systemNewsInitialized(); // Wait for initialization
 *    console.log(systemNewsItems); // Now safe to use
 *
 * 3. Cache management:
 *    const status = getSystemNewsCacheStatus(); // Monitor cache
 *    await refreshSystemNewsCache(); // Force refresh
 *    setSystemNewsCacheTTL(10 * 60 * 1000); // Configure TTL
 *
 * 4. AVOID - Direct synchronous access (may be empty):
 *    console.log(systemNewsItems); // May be [] if accessed too early
 *
 * The async functions (getActiveSystemNews, getSystemNewsForRoles, etc.) are always safe
 * to use as they fetch fresh data from the server and handle caching internally.
 * Cache automatically expires after the configured TTL and provides fallback to stale
 * data during network errors for improved reliability.
 */

// Cache for the fetched news data
let systemNewsCache = null;
let cacheTimestamp = null;

// Cache configuration
const DEFAULT_CACHE_TTL = 5 * 60 * 1000; // 5 minutes default cache TTL
let CACHE_TTL = DEFAULT_CACHE_TTL;

/**
 * Fetch system news from server API
 * @returns {Promise<Array>} Array of system news items
 */
const fetchSystemNewsFromServer = async () => {
  const now = Date.now();

  // Validate cache integrity before checking expiration
  const validation = validateSystemNewsCache(true);
  if (!validation.valid && validation.fixed) {
    console.warn(
      "Cache integrity issues detected and fixed:",
      validation.issues
    );
  }

  // Check if cache is expired or doesn't exist
  if (!systemNewsCache || !cacheTimestamp || now - cacheTimestamp > CACHE_TTL) {
    try {
      const response = await fetch("/api/news/system");
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      if (data.success) {
        const newsData = data.systemNews || [];
        // Validate that we received an array
        if (!Array.isArray(newsData)) {
          throw new Error("Server returned invalid news data format");
        }
        systemNewsCache = newsData;
        cacheTimestamp = now;
      } else {
        throw new Error(data.message || "Failed to fetch system news");
      }
    } catch (error) {
      console.error("Failed to fetch system news from server:", error);

      // If we have existing cache data and it's not too old (within 2x TTL), use it
      const maxStaleAge = CACHE_TTL * 2;
      if (
        systemNewsCache &&
        cacheTimestamp &&
        now - cacheTimestamp <= maxStaleAge
      ) {
        console.warn("Using stale cache data due to fetch error");
        return systemNewsCache;
      }

      // Otherwise, fallback to empty array
      systemNewsCache = [];
      cacheTimestamp = now; // Still set timestamp to prevent rapid retries
    }
  }
  return systemNewsCache || [];
};

/**
 * Get system news items (async)
 * @returns {Promise<Array<Object>>} Array of system news items
 */
export const getSystemNewsItems = async () => {
  return await fetchSystemNewsFromServer();
};

/**
 * Get active system news with translations (async)
 * @param {Function} t - Translation function (optional)
 * @returns {Promise<Array<Object>>} Array of active system news items
 */
export const getActiveSystemNews = async (t = null) => {
  const systemNews = await fetchSystemNewsFromServer();
  const now = new Date();

  const activeNews = systemNews.filter((item) => {
    // Check if item is active
    if (!item.isActive) return false;

    // Check if item has expired
    if (item.expiresAt && new Date(item.expiresAt) < now) return false;

    return true;
  });

  // Apply translations if provided
  if (t) {
    return activeNews.map((item) => resolveSystemNewsTranslations(item, t));
  }

  return activeNews;
};

/**
 * Get system news for specific user roles with translations (async)
 * @param {Array<string>} userRoles - Array of user roles
 * @param {Function} t - Translation function (optional)
 * @returns {Promise<Array<Object>>} Array of system news items for the specified roles
 */
export const getSystemNewsForRoles = async (userRoles = [], t = null) => {
  const activeNews = await getActiveSystemNews();

  const filteredNews = activeNews.filter((item) => {
    // If no target roles specified, show to all users
    if (!item.targetRoles || item.targetRoles.length === 0) return true;

    // Check if user has any of the target roles
    return userRoles.some((role) => item.targetRoles.includes(role));
  });

  // Apply translations if provided
  if (t) {
    return filteredNews.map((item) => resolveSystemNewsTranslations(item, t));
  }

  return filteredNews;
};

/**
 * Get system news by ID with translations (async)
 * @param {string} id - News item ID
 * @param {Function} t - Translation function (optional)
 * @returns {Promise<Object|null>} System news item or null if not found
 */
export const getSystemNewsById = async (id, t = null) => {
  const systemNews = await fetchSystemNewsFromServer();
  const newsItem = systemNews.find((item) => item.id === id) || null;

  // Apply translations if provided and item exists
  if (newsItem && t) {
    return resolveSystemNewsTranslations(newsItem, t);
  }

  return newsItem;
};

/**
 * Get system news by priority with translations (async)
 * @param {string} priority - Priority level
 * @param {Function} t - Translation function (optional)
 * @returns {Promise<Array<Object>>} Array of system news items with the specified priority
 */
export const getSystemNewsByPriority = async (priority, t = null) => {
  const activeNews = await getActiveSystemNews();
  const filteredNews = activeNews.filter((item) => item.priority === priority);

  // Apply translations if provided
  if (t) {
    return filteredNews.map((item) => resolveSystemNewsTranslations(item, t));
  }

  return filteredNews;
};

/**
 * Resolve translation keys for a system news item
 * @param {Object} newsItem - News item with translation keys
 * @param {Function} t - Translation function
 * @returns {Object} News item with resolved translations
 */
export const resolveSystemNewsTranslations = (newsItem, t) => {
  return {
    ...newsItem,
    title: newsItem.titleKey
      ? t(newsItem.titleKey, newsItem.title)
      : newsItem.title,
    content: newsItem.contentKey
      ? t(newsItem.contentKey, newsItem.content)
      : newsItem.content,
  };
};

/**
 * Clear the system news cache
 * This can be called when news data needs to be refreshed
 */
export const clearSystemNewsCache = () => {
  systemNewsCache = null;
  cacheTimestamp = null;
};

/**
 * Force refresh the system news cache
 * This clears the cache and immediately fetches fresh data
 * @returns {Promise<Array>} Fresh system news data
 */
export const refreshSystemNewsCache = async () => {
  clearSystemNewsCache();
  return await fetchSystemNewsFromServer();
};

/**
 * Set the cache TTL (Time To Live) duration
 * @param {number} ttlMs - Cache TTL in milliseconds
 */
export const setSystemNewsCacheTTL = (ttlMs) => {
  if (typeof ttlMs !== "number" || ttlMs < 0) {
    throw new Error("Cache TTL must be a non-negative number");
  }
  CACHE_TTL = ttlMs;
};

/**
 * Get the current cache TTL setting
 * @returns {number} Current cache TTL in milliseconds
 */
export const getSystemNewsCacheTTL = () => {
  return CACHE_TTL;
};

/**
 * Reset cache TTL to default value
 */
export const resetSystemNewsCacheTTL = () => {
  CACHE_TTL = DEFAULT_CACHE_TTL;
};

/**
 * Validate cache integrity and optionally fix issues
 * @param {boolean} autoFix - Whether to automatically fix detected issues
 * @returns {Object} Validation result with any issues found
 */
export const validateSystemNewsCache = (autoFix = false) => {
  const issues = [];
  let fixed = false;

  // Check if cache and timestamp are in sync
  if ((systemNewsCache === null) !== (cacheTimestamp === null)) {
    issues.push("Cache and timestamp are out of sync");
    if (autoFix) {
      systemNewsCache = null;
      cacheTimestamp = null;
      fixed = true;
    }
  }

  // Check if cache is an array
  if (systemNewsCache !== null && !Array.isArray(systemNewsCache)) {
    issues.push("Cache is not an array");
    if (autoFix) {
      systemNewsCache = [];
      fixed = true;
    }
  }

  // Check if timestamp is valid
  if (
    cacheTimestamp !== null &&
    (typeof cacheTimestamp !== "number" || cacheTimestamp > Date.now())
  ) {
    issues.push("Invalid cache timestamp");
    if (autoFix) {
      cacheTimestamp = null;
      systemNewsCache = null;
      fixed = true;
    }
  }

  return {
    valid: issues.length === 0,
    issues,
    fixed,
    status: getSystemNewsCacheStatus(),
  };
};

/**
 * Check if the system news cache is expired
 * @returns {boolean} True if cache is expired or doesn't exist
 */
export const isSystemNewsCacheExpired = () => {
  if (!systemNewsCache || !cacheTimestamp) {
    return true;
  }
  const now = Date.now();
  return now - cacheTimestamp > CACHE_TTL;
};

/**
 * Get cache status information
 * @returns {Object} Cache status with expiration info
 */
export const getSystemNewsCacheStatus = () => {
  if (!systemNewsCache || !cacheTimestamp) {
    return {
      cached: false,
      expired: true,
      age: null,
      remainingTtl: null,
      ttl: CACHE_TTL,
      itemCount: 0,
      lastFetch: null,
    };
  }

  const now = Date.now();
  const age = now - cacheTimestamp;
  const expired = age > CACHE_TTL;
  const remainingTtl = expired ? 0 : CACHE_TTL - age;

  return {
    cached: true,
    expired,
    age,
    remainingTtl,
    ttl: CACHE_TTL,
    itemCount: Array.isArray(systemNewsCache) ? systemNewsCache.length : 0,
    lastFetch: new Date(cacheTimestamp).toISOString(),
  };
};

/**
 * Reinitialize system news items
 * This clears the cache and reinitializes the systemNewsItems array
 * @returns {Promise<void>} Promise that resolves when reinitialization is complete
 */
export const reinitializeSystemNews = async () => {
  systemNewsCache = null;
  cacheTimestamp = null;
  _initializationPromise = null; // Reset the initialization promise
  try {
    // Fetch fresh news items
    const freshNews = await getSystemNewsItems();
    // Atomically replace the array reference to avoid race conditions
    systemNewsItems = [...freshNews];
  } catch (error) {
    console.error("Failed to reinitialize system news items:", error);
    // Atomically replace with empty array on error
    systemNewsItems = [];
  }
};

// For backward compatibility, export a synchronous version
// Note: This will be empty until properly initialized
export let systemNewsItems = [];

// Promise that resolves when initialization is complete
let _initializationPromise = null;

export const systemNewsInitialized = () => {
  if (!_initializationPromise) {
    _initializationPromise = (async () => {
      try {
        const freshNews = await getSystemNewsItems();
        // Atomically replace the array reference to avoid race conditions
        systemNewsItems = [...freshNews];
      } catch (error) {
        console.error("Failed to load system news items:", error);
        // Atomically replace with empty array on error
        systemNewsItems = [];
      }
    })();
  }
  return _initializationPromise;
};

// For backward compatibility, automatically initialize if accessed
export const getSystemNewsInitialized = systemNewsInitialized;

export default systemNewsItems;
