export default {
  // =========================
  // DATA CONNECTORS
  // =========================
  dataConnectors: {
    github: {
      name: "GitHub-Repo",
      description:
        "Importiere ein gesamtes öffentliches oder privates GitHub-Repository mit einem Klick.",
      url: "GitHub-Repo URL",
      "collect-url": "URL des GitHub-Repos, das du abrufen möchtest.",
      "access-token": "GitHub Access Token",
      optional: "optional",
      "rate-limiting": "Access Token, um Ratenbegrenzungen zu vermeiden.",
      "desc-picker":
        "Nach Abschluss stehen alle Dateien im Dokumenten-Picker zur Einbettung in Arbeitsbereiche zur Verfügung.",
      branch: "Branch",
      "branch-desc": "Der Branch, aus dem du Dateien abrufen möchtest.",
      "branch-loading": "-- lade verfügbare Branches --",
      "desc-start": "Ohne Angabe des",
      "desc-token": "GitHub Access Tokens",
      "desc-connector":
        "kann dieser Connector aufgrund der Ratenbegrenzung der GitHub-API nur die",
      "desc-level": "obersten",
      "desc-end": "Dateien des Repos abrufen.",
      "personal-token":
        "Hole dir ein kostenloses Personal Access Token mit einem GitHub-Konto hier.",
      without: "Ohne ein",
      "personal-token-access": "Personal Access Token",
      "desc-api":
        ", kann die GitHub API die Anzahl der abrufbaren Dateien aufgrund von Ratenbegrenzungen einschränken. Du kannst",
      "temp-token": "ein temporäres Access Token erstellen",
      "avoid-issue": "um dieses Problem zu vermeiden.",
      submit: "Absenden",
      "collecting-files": "Dateien werden abgerufen...",
    },
    "youtube-transcript": {
      name: "YouTube Transkript",
      description:
        "Importiere das Transkript eines gesamten YouTube-Videos über einen Link.",
      url: "YouTube Video URL",
      "url-video": "URL des YouTube-Videos, das du transkribieren möchtest.",
      collect: "Transkript abrufen",
      collecting: "Transkript wird abgerufen...",
      "desc-end":
        "Nach Abschluss steht das Transkript im Dokumenten-Picker zur Einbettung in Arbeitsbereiche bereit.",
    },
    "website-depth": {
      name: "Bulk-Link-Scraper",
      description:
        "Durchsuche eine Website und ihre Unterseiten bis zu einer bestimmten Tiefe.",
      url: "Website URL",
      "url-scrape": "URL der zu durchsuchenden Website.",
      depth: "Tiefe",
      "child-links":
        "Anzahl der Unterlinks, denen der Worker von der Ausgangs-URL folgen soll.",
      "max-links": "Maximale Links",
      "links-scrape":
        "Maximale Anzahl von Links, die durchsucht werden sollen.",
      scraping: "Website wird durchsucht...",
      submit: "Absenden",
      "desc-scrap":
        "Nach Abschluss stehen alle durchsuchten Seiten im Dokumenten-Picker zur Einbettung in Arbeitsbereiche bereit.",
    },
    confluence: {
      name: "Confluence",
      description: "Importiere eine gesamte Confluence-Seite mit einem Klick.",
      url: "Confluence Seiten URL",
      "url-page": "URL einer Seite im Confluence-Space.",
      username: "Confluence-Benutzername",
      "own-username": "Dein Confluence-Benutzername.",
      token: "Confluence Access Token",
      "desc-start":
        "Du musst einen Access Token für die Authentifizierung angeben. Du kannst einen Access Token",
      here: "hier",
      access: "Access Token für die Authentifizierung.",
      collecting: "Seiten werden abgerufen...",
      submit: "Absenden",
      "desc-end":
        "Nach Abschluss stehen alle Seiten im Arbeitsbereich zur Einbettung bereit.",
    },
  },
  // =========================
  // CONFLUENCE CONNECTOR COMPONENT
  // =========================
  confluence: {
    "space-key": "Confluence Space Key",
    "space-key-desc":
      "Dies ist der Space-Key deiner Confluence-Instanz, der verwendet wird. Beginnt üblicherweise mit ~",
    "space-key-placeholder": "z.B.: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "z.B.: https://example.atlassian.net, http://localhost:8211, etc...",
    "token-tooltip": "Du kannst ein API-Token erstellen",
    "token-tooltip-here": "hier",
  },
};
