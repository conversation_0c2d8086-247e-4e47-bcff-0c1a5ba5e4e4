---
description: Guidelines for backend development
globs: server/**/*.js,collector/**/*.js
alwaysApply: false
---
# Backend Development Guidelines

## Server Structure
- The server is built with Express.js
- Endpoints are organized in the `server/endpoints` directory
- Models for database access are in the `server/models` directory
- Utilities and helpers are in the `server/utils` directory

## API Design
- Follow RESTful principles for API endpoints
- Use appropriate HTTP methods (GET, POST, PUT, DELETE)
- Return consistent response formats
- Document APIs using Swagger/OpenAPI

## Database
- Use Prisma for database access
- Define database schema in `server/prisma/schema.prisma`
- Create migrations for schema changes
- Use transactions for operations that modify multiple records

## Error Handling
- Use try/catch blocks for async operations
- Return appropriate HTTP status codes
- Provide meaningful error messages
- Log errors with sufficient context

## Authentication & Authorization
- Validate user permissions for protected routes
- Use middleware for authentication checks
- Implement proper token validation
- Follow security best practices

## Internationalization (i18n)
- Server and collector files have specific existing files for i18n keys
- Add new translation keys to the appropriate locale files
- Ensure error messages and system messages are internationalized

## Performance
- Optimize database queries
- Use caching where appropriate
- Implement pagination for large result sets
- Monitor and optimize resource usage

## RAG Implementation
- Follow the established patterns for Retrieval-Augmented Generation
- Properly handle document ingestion and processing
- Implement vector search with appropriate similarity thresholds
- Manage context windows for LLM prompts

## Development Environment

### Auto-Reloading Servers

**Important**: In development, all servers are configured to automatically restart when code changes are detected. You do **NOT** need to manually restart servers after making changes.

- **Backend Server**: Automatically restarts on file changes in `server/` directory
- **Frontend Development Server**: Automatically reloads on file changes in `frontend/` directory
- **Collector Service**: Automatically restarts on file changes in `collector/` directory

**What this means for development:**
- Make your code changes and save files
- Servers will automatically detect changes and restart
- No need to run terminal commands to restart servers
- Changes will be reflected immediately after the auto-restart completes
- Focus on coding rather than server management

**Exception**: Only manual restart required if you modify environment variables or configuration files that require a full application restart.

## Core Principles

1. **Input Validation**: Always validate user inputs before database operations
2. **Error Handling**: Implement comprehensive error handling with proper HTTP status codes
3. **Security**: Validate authentication and authorization at every endpoint
4. **Performance**: Use efficient database queries and proper indexing
5. **Maintainability**: Write clear, documented code with consistent patterns

## User ID Validation

### Critical Requirements

All user-related endpoints must implement robust ID validation to prevent Prisma errors and security issues.

**Always validate user IDs before database operations:**

```javascript
// In models/user.js
static async get(clause = {}) {
  // Validate ID if present
  if (clause.id !== undefined) {
    const id = clause.id;
    if (id === null || id === undefined || (typeof id === 'number' && isNaN(id))) {
      console.log(`User.get: Invalid ID provided: ${id} (type: ${typeof id})`);
      return null;
    }

    // Convert string IDs to numbers if valid
    if (typeof id === 'string') {
      const parsedId = parseInt(id);
      if (isNaN(parsedId)) {
        console.log(`User.get: Invalid string ID: ${id}`);
        return null;
      }
      clause.id = parsedId;
    }
  }

  const user = await prisma.users.findFirst({ where: clause });
  return user || null;
}
```

**JWT Token Validation:**

```javascript
// In utils/http/index.js
function userFromSession(request, response = null) {
  const token = request.cookies?.token || request.headers?.authorization?.split(' ')[1];
  if (!token) return null;

  try {
    const valid = jwt.verify(token, process.env.JWT_SECRET);
    if (!valid?.id) return null;

    // Validate and parse user ID
    const userId = parseInt(valid.id);
    if (isNaN(userId)) {
      console.log(`userFromSession: Invalid user ID in token: ${valid.id}`);
      return null;
    }

    return User.get({ id: userId });
  } catch (error) {
    return null;
  }
}
```

### Common Validation Patterns

1. **Check for null/undefined/NaN:**
   ```javascript
   if (value === null || value === undefined || (typeof value === 'number' && isNaN(value))) {
     return null;
   }
   ```

2. **Safe parseInt conversion:**
   ```javascript
   const id = parseInt(stringId);
   if (isNaN(id)) {
     console.log(`Invalid ID conversion: ${stringId} -> ${id}`);
     return null;
   }
   ```

3. **Middleware validation:**
   ```javascript
   function validateMultiUserRequest(request, response, next) {
     const user = userFromSession(request, response);
     if (!user || !user.id) {
       return response.status(401).json({ success: false, error: "Unauthorized" });
     }

     const userId = parseInt(user.id);
     if (isNaN(userId)) {
       return response.status(401).json({ success: false, error: "Invalid user ID" });
     }

     response.locals.user = user;
     next();
   }
   ```

## Route Registration Order

### Critical Rule: Specific Before General

Route registration order in `server/index.js` is critical. More specific routes must be registered before more general ones to prevent route conflicts.

**Correct Order:**
```javascript
// server/index.js
// Register specific routes first
app.use("/api", userCustomSystemPromptEndpoints);
app.use("/api", specificFeatureEndpoints);

// Register general routes last
app.use("/api", developerEndpoints); // Contains catch-all routes like /user/:id
app.use("/api", generalEndpoints);
```

**Why This Matters:**
- Routes like `/user/custom-system-prompt` must be registered before `/user/:id`
- Express matches routes in registration order
- General patterns can "catch" requests meant for specific endpoints

### Route Conflict Prevention

1. **Document route registration order:**
   ```javascript
   // Register user-specific endpoints before general user endpoints
   app.use("/api", userCustomSystemPromptEndpoints); // /user/custom-system-prompt
   app.use("/api", developerEndpoints); // /user/:id (catch-all)
   ```

2. **Use specific route patterns:**
   ```javascript
   // Good: specific pattern
   router.get("/user/custom-system-prompt", handler);

   // Problematic: too general, can conflict
   router.get("/user/:action", handler);
   ```

3. **Test route conflicts:**
   - Verify specific routes work correctly
   - Ensure general routes don't interfere
   - Test route parameter parsing

## Database Operations

### Prisma Best Practices

1. **Always validate inputs before Prisma calls:**
   ```javascript
   // Bad: can cause Prisma validation errors
   const user = await prisma.users.findFirst({ where: { id: null } });

   // Good: validate first
   if (!id || isNaN(parseInt(id))) {
     return null;
   }
   const user = await prisma.users.findFirst({ where: { id: parseInt(id) } });
   ```

2. **Handle Prisma errors gracefully:**
   ```javascript
   try {
     const user = await prisma.users.findFirst({ where: clause });
     return user || null;
   } catch (error) {
     console.error('Database error:', error);
     return null;
   }
   ```

3. **Keep schema synchronized:**
   ```bash
   # Development
   npx prisma db push

   # Production
   npx prisma migrate deploy
   ```

## Environment Variables

### Required Validation

Always validate required environment variables on server startup:

```javascript
// server/index.js
require('dotenv').config();

const requiredEnvVars = ['JWT_SECRET', 'DATABASE_URL'];
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    console.error(`${envVar} environment variable is required`);
    process.exit(1);
  }
}
```

### Security Considerations

1. **Never log sensitive environment variables**
2. **Use strong, unique JWT secrets**
3. **Validate environment variable formats when applicable**

## Error Handling

### HTTP Status Codes

Use appropriate HTTP status codes:

```javascript
// 400 - Bad Request (invalid input)
return response.status(400).json({ success: false, error: "Invalid input" });

// 401 - Unauthorized (authentication failed)
return response.status(401).json({ success: false, error: "Unauthorized" });

// 403 - Forbidden (authorization failed)
return response.status(403).json({ success: false, error: "Forbidden" });

// 404 - Not Found (resource doesn't exist)
return response.status(404).json({ success: false, error: "Not found" });

// 500 - Internal Server Error (unexpected errors)
return response.status(500).json({ success: false, error: "Internal server error" });
```

### Error Logging

1. **Log errors with context:**
   ```javascript
   console.error('Error in endpoint:', {
     endpoint: '/api/user/custom-system-prompt',
     userId: user?.id,
     error: error.message,
     stack: error.stack
   });
   ```

2. **Don't expose internal errors to clients:**
   ```javascript
   // Bad: exposes internal details
   return response.status(500).json({ error: error.message });

   // Good: generic error message
   return response.status(500).json({ error: "Internal server error" });
   ```

## Testing

### Required Test Cases

1. **Input validation tests:**
   - Valid inputs
   - null/undefined inputs
   - Invalid type inputs
   - Edge cases (empty strings, NaN, etc.)

2. **Authentication tests:**
   - Valid JWT tokens
   - Invalid JWT tokens
   - Missing tokens
   - Expired tokens

3. **Route conflict tests:**
   - Specific routes work correctly
   - General routes don't interfere
   - Parameter parsing works as expected

### Debugging Techniques

1. **Add comprehensive logging:**
   ```javascript
   console.log(`Function called with:`, JSON.stringify(params));
   console.log(`Processing ID: ${id} type: ${typeof id}`);
   console.log(`Final result:`, result);
   ```

2. **Use descriptive error messages:**
   ```javascript
   console.log(`User.get: Invalid ID provided: ${id} (type: ${typeof id})`);
   ```

3. **Test with various input types:**
   - Valid integers
   - String representations of numbers
   - null and undefined values
   - NaN values
   - Invalid strings

## Common Anti-Patterns

### Avoid These Mistakes

1. **Skipping input validation:**
   ```javascript
   // Bad: no validation
   const user = await User.get({ id: req.params.id });

   // Good: validate first
   const id = parseInt(req.params.id);
   if (isNaN(id)) {
     return res.status(400).json({ error: "Invalid ID" });
   }
   const user = await User.get({ id });
   ```

2. **Incorrect route registration order:**
   ```javascript
   // Bad: general route registered first
   app.use("/api", generalEndpoints); // /user/:id
   app.use("/api", specificEndpoints); // /user/custom-system-prompt (never reached)

   // Good: specific routes first
   app.use("/api", specificEndpoints);
   app.use("/api", generalEndpoints);
   ```

3. **Missing error handling:**
   ```javascript
   // Bad: no error handling
   const user = await prisma.users.findFirst({ where: { id } });

   // Good: handle errors
   try {
     const user = await prisma.users.findFirst({ where: { id } });
     return user || null;
   } catch (error) {
     console.error('Database error:', error);
     return null;
   }
   ```

## Related Documentation

- [Troubleshooting User Endpoints](mdc:../server/docs/troubleshooting_user_endpoints.md)
- [Database Schema Guidelines](mdc:database-schema.mdc)
- [Security Guidelines](mdc:security-guidelines.mdc)
