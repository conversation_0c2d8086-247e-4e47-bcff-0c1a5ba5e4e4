export default {
  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Registrer Rexor-prosjekt",
    "project-id": "Prosjekt-ID",
    "resource-id": "Ressurs-ID",
    "activity-id": "Aktivitets-ID",
    register: "Registrer prosjekt",
    "invoice-text": "Foynet antall oppslag",
    registering: "registrerer...",
    "not-active": "Denne saken er ikke aktiv for registrering",
    account: {
      title: "Logg inn på Rexor",
      username: "Brukernavn",
      password: "<PERSON>ord",
      "no-token": "Ingen token mottatt i handleLoginSuccess",
      logout: "Logg ut",
      "no-user": "Vennligst logg inn først",
      connected: "Koblet til Rexor",
      "not-connected": "Ikke tilko<PERSON>t",
      "change-account": "Bytt konto",
      "session-expired": "Økt utløpt. Vennligst logg inn igjen.",
    },
    "hide-article-transaction": "Skjul tidstransaksjons-skjema",
    "show-article-transaction": "Vis tidstransaksjons-skjema",
    "article-transaction-title": "Legg til tidstransaksjon",
    "registration-date": "Registreringsdato",
    description: "Beskrivelse",
    "description-internal": "Intern beskrivelse",
    "hours-worked": "Timer arbeidet",
    "invoiced-hours": "Fakturerte timer",
    invoiceable: "Fakturerbar",
    "sending-article-transaction": "Sender tidstransaksjon...",
    "save-article-transaction": "Lagre tidstransaksjon",
    "project-not-register": "Prosjektet må registreres først.",
    "article-transaction-error": "Kunne ikke skrive tidstransaksjon",
    "not-exist": "Denne saken kunne ikke finnes",
    "missing-economy-id-admin":
      "Vennligst legg til en økonomi-ID til brukerprofilen din før du registrerer et prosjekt.",
    "missing-economy-id-user":
      "Vennligst be systemadministratoren om å legge til en økonomi-ID til brukerprofilen din for å kunne registrere et prosjekt.",
    "api-settings": {
      title: "Rexor API-konfigurasjon",
      description:
        "Konfigurer tilpassede Rexor API-endepunkter og legitimasjon. La stå tomt for å bruke standardverdier.",
      "loading-message": "Laster Rexor API-innstillinger...",
      "api-base-url": "API-basis-URL",
      "api-base-url-placeholder": "https://api.rexor.se/v231/Api",
      "auth-url": "Autentiserings-URL",
      "auth-url-placeholder": "https://auth.rexor.se/v231/Token",
      "client-id-dev": "Utviklings-klient-ID",
      "client-id-dev-placeholder": "testfoyen",
      "client-id-prod": "Produksjons-klient-ID",
      "client-id-prod-placeholder": "foyen",
      "api-host": "API-vert",
      "api-host-placeholder": "api.rexor.se",
      "save-button": "Lagre innstillinger",
      "reset-button": "Tilbakestill til standard",
      "success-message": "Rexor API-innstillinger lagret vellykket",
      "error-message": "Kunne ikke lagre Rexor API-innstillinger",
    },
  },
};
