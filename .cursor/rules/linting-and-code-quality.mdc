---
description:
globs: *.js,*.jsx
alwaysApply: true
---
# Linting and Code Quality Guidelines

## ESLint Configuration

The project uses ESLint for code quality enforcement with configuration in `eslint.config.js`. Common rules include:

- No unused variables
- No prototype builtins (use `Object.prototype.hasOwnProperty.call()` instead of direct method calls)
- Proper React hooks usage
- Consistent formatting (via <PERSON><PERSON><PERSON>)

## Running Linting Checks

### Frontend

```bash
cd frontend
npm run lint
# Or for a specific file:
npx eslint src/path/to/file.js
```

### Server

```bash
cd server
npm run lint
# Or for a specific file:
npx eslint path/to/file.js
```

## Common ESLint Issues and How to Fix Them

### 1. Unused Variables and Imports

**Issue:**
```
'useState' is defined but never used. eslint(no-unused-vars)
```

**Fix:**
- Remove unused imports and variables
- If you need to keep a parameter but don't use it, prefix it with underscore: `_unusedParam`

### 2. Object Prototype Methods

**Issue:**
```
Do not access Object.prototype method 'hasOwnProperty' from target object. eslint(no-prototype-builtins)
```

**Fix:**
Replace:
```javascript
if (obj.hasOwnProperty("property")) {
```

With:
```javascript
if (Object.prototype.hasOwnProperty.call(obj, "property")) {
```

### 3. Unused React Hooks

**Issue:**
```
'useEffect' is defined but never used. eslint(no-unused-vars)
```

**Fix:**
- Remove unused hook imports
- If you're defining a component that doesn't use hooks it imported, remove the imports

### 4. React Hook Dependencies

**Issue:**
```
React Hook useEffect has a missing dependency: 'value'. eslint(react-hooks/exhaustive-deps)
```

**Fix:**
- Add the missing dependency to the dependency array
- If you intentionally want to exclude it, consider using a ref or other pattern

### 5. Event Handlers in JSX

**Issue:**
```
JSX props should not use arrow functions. eslint(react/jsx-no-bind)
```

**Fix:**
- Define the handler outside the JSX and reference it:
```javascript
const handleClick = () => {
  // handler code
};

return <button onClick={handleClick}>Click me</button>;
```

## Automatic Fixing

### ESLint Auto-fixing

Many ESLint issues can be automatically fixed using:

```bash
# Frontend
cd frontend
npx eslint src/path/to/file.js --fix

# Server
cd server
npx eslint path/to/file.js --fix
```

### Prettier Formatting

Always run Prettier as a final step before committing code to ensure consistent formatting:

```bash
# Format a specific file
npx prettier --write path/to/file.js

# Format all files in frontend
cd frontend
npx prettier --ignore-path ../.prettierignore --write ./src

# Format all files in server
cd server
npx prettier --ignore-path ../.prettierignore --write ./endpoints ./models ./utils index.js
```

## Best Practices

1. **Fix issues as you go**: Don't accumulate linting errors
2. **Understand the rules**: Don't just silence warnings without understanding why
3. **Be consistent**: Follow the established patterns in the codebase
4. **Run Prettier as the final step**: Always format your code with Prettier as a last step
5. **Update cursor rules**: When fixing common issues, update cursor rules to help others avoid the same mistakes

## Common Coding Mistakes to Avoid

### 1. ES Modules vs CommonJS in Test Files

The project uses ES modules (`import`/`export`) but test files sometimes incorrectly use CommonJS (`require`).

**Issue:**
```javascript
const { useShowMetrics } = require('./settingsStore.test.js');
```

**Fix:**
```javascript
// For synchronous imports at the top of the file
import { useShowMetrics } from './settingsStore.test.js';

// For dynamic imports inside functions
async function someFunction() {
  const { useShowMetrics } = await import('./settingsStore.test.js');
}
```

### 2. Async/Await Usage

When using `await`, ensure the containing function is marked as `async`.

**Issue:**
```javascript
function runTests() {
  const result = await someAsyncFunction();
}
```

**Fix:**
```javascript
async function runTests() {
  const result = await someAsyncFunction();
}
```

### 3. Trailing Whitespace

Avoid trailing whitespace in code, especially after statements.

**Issue:**
```javascript
const value = 42;
```

**Fix:**
```javascript
const value = 42;
```

## Updating Cursor Rules

When you encounter and fix common coding issues, update the cursor rules to help others avoid the same mistakes:

1. Add the issue and its solution to this file (`.cursor/rules/linting-and-code-quality.mdc`)
2. If it's a significant pattern, consider adding it to the project's documentation
3. For team-wide issues, suggest adding automated checks or pre-commit hooks

## API Endpoint Testing Tips

### Mocking Middleware
- When testing API endpoints that use authentication or other complex middleware (e.g., `validatedRequest` in this project), ensure the middleware is properly mocked in your Jest tests.
- Failure to mock middleware can lead to unexpected errors (like 401s or 404s if the middleware interacts with databases or environment variables not set up for the test environment) that mask the actual behavior of the endpoint logic.
- A common way to mock is using `jest.mock` at the top of your test file:
  ```javascript
  jest.mock("../../path/to/middleware.js", () => ({
    middlewareName: (req, res, next) => {
      // Optionally set res.locals if your endpoint relies on it
      // res.locals.user = { id: 1, role: 'admin' };
      next();
    },
  }));
  ```

### Jest "did not exit" Warning
- If you encounter the warning: "Jest did not exit one second after the test run has completed. This usually means that there are asynchronous operations that weren't stopped in your tests."
- This often points to open handles such as database connections (e.g., Prisma client), timers (`setTimeout`, `setInterval`), or active network sockets that were not properly closed at the end of your test suites (`afterAll`).
- Use Jest's `--detectOpenHandles` flag to help identify the source of the open handles.
- Ensure database connections are closed (e.g., `await prisma.$disconnect()` in an `afterAll` block if `prisma` is used directly in tests or by the tested code without proper teardown within the app itself for test environments).
