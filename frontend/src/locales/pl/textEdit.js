const TRANSLATIONS = {
  "text-edit": {
    toolbar: {
      bold: "Pogrub tekst",
      italic: "Pochyl tekst",
      underline: "Podkreśl tekst",
      strikethrough: "Przekreśl tekst",
      code: "Formatuj jako kod",
      "heading-1": "Formatuj jako duży nagłówek",
      "heading-2": "Formatuj jako średni nagłówek",
      "heading-3": "Formatuj jako mały nagłówek",
      "bullet-list": "Utwórz listę punktowaną",
      "numbered-list": "Utwórz listę numerowaną",
      blockquote: "Formatuj jako cytat blokowy",
      "add-link": "Dodaj link",
      "remove-link": "Usuń link",
      "insert-table": "Wstaw tabelę",
      "text-color": "Zmień kolor tekstu",
      highlight: "Podświetl tekst",
      undo: "Cof<PERSON>j ostat<PERSON> czynno<PERSON><PERSON>",
      redo: "Ponów ostatni<PERSON> czynno<PERSON>",
    },
    modals: {
      link: {
        title: "Dodaj link",
        "url-label": "URL",
        "url-placeholder": "https://example.com",
        cancel: "Anuluj",
        add: "Dodaj link",
      },
      "text-color": {
        title: "Ustaw kolor tekstu",
        "color-label": "Wybierz kolor",
        "custom-color": "Niestandardowy kolor",
        "color-placeholder": "#ff0000, czerwony, rgb(255,0,0)",
        "color-help":
          "Wprowadź kolor w formacie hex (#ff0000), RGB (rgb(255,0,0)) lub nazwę (czerwony)",
        cancel: "Anuluj",
        apply: "Zastosuj kolor",
      },
      highlight: {
        title: "Ustaw kolor podświetlenia",
        "color-label": "Wybierz kolor podświetlenia",
        "custom-color": "Niestandardowy kolor podświetlenia",
        "color-placeholder": "#ffff00, żółty, rgb(255,255,0)",
        "color-help":
          "Wprowadź kolor w formacie hex (#ffff00), RGB (rgb(255,255,0)) lub nazwę (żółty)",
        cancel: "Anuluj",
        apply: "Zastosuj podświetlenie",
      },
    },
    errors: {
      "invalid-url":
        "Nieprawidłowy URL. Proszę wprowadzić prawidłowy adres internetowy.",
      "invalid-color":
        "Nieprawidłowy kolor. Proszę wprowadzić prawidłowy kolor CSS.",
      "dangerous-url": "Ten URL nie jest dozwolony ze względów bezpieczeństwa.",
      "dangerous-color": "Ten kolor zawiera potencjalnie szkodliwą zawartość.",
    },
    colors: {
      select: "Wybierz",
      highlight: "podświetlenie",
      "no-highlight": "Brak podświetlenia",
      black: "Czarny",
      "dark-gray": "Ciemnoszary",
      gray: "Szary",
      "light-gray": "Jasnoszary",
      red: "Czerwony",
      orange: "Pomarańczowy",
      amber: "Bursztynowy",
      yellow: "Żółty",
      lime: "Limonkowy",
      green: "Zielony",
      emerald: "Szmaragdowy",
      teal: "Morski",
      cyan: "Cyjan",
      sky: "Niebieski",
      blue: "Niebieski",
      indigo: "Indygo",
      violet: "Fioletowy",
      purple: "Purpurowy",
      fuchsia: "Fuksja",
      pink: "Różowy",
      rose: "Różany",
    },
  },
};

export default TRANSLATIONS;
